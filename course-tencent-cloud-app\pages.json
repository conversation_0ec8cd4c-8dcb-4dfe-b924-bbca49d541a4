{"easycom": {"^u-(.*)": "@/uview-ui/components/u-$1/u-$1.vue"}, "pages": [{"path": "pages/index/index", "style": {"navigationBarTitleText": "酷瓜云课堂（开源版）"}}, {"path": "pages/search/index", "style": {"navigationBarTitleText": "内容搜索"}}, {"path": "pages/discovery/index", "style": {"navigationBarTitleText": "精彩发现"}}, {"path": "pages/course/category", "style": {"navigationBarTitleText": "课程分类"}}, {"path": "pages/course/list", "style": {"navigationBarTitleText": "课程列表"}}, {"path": "pages/course/info", "style": {"navigationBarTitleText": "课程详情"}}, {"path": "pages/chapter/vod", "style": {"navigationBarTitleText": "课时详情"}}, {"path": "pages/chapter/live", "style": {"navigationBarTitleText": "课时详情"}}, {"path": "pages/chapter/read", "style": {"navigationBarTitleText": "课时详情"}}, {"path": "pages/teacher/list", "style": {"navigationBarTitleText": "教师列表"}}, {"path": "pages/teacher/index", "style": {"navigationBarTitleText": "教师主页"}}, {"path": "pages/review/info", "style": {"navigationBarTitleText": "评价详情"}}, {"path": "pages/review/add", "style": {"navigationBarTitleText": "发布评价"}}, {"path": "pages/review/edit", "style": {"navigationBarTitleText": "修改评价"}}, {"path": "pages/consult/info", "style": {"navigationBarTitleText": "咨询详情"}}, {"path": "pages/consult/add", "style": {"navigationBarTitleText": "提交咨询"}}, {"path": "pages/consult/edit", "style": {"navigationBarTitleText": "修改咨询"}}, {"path": "pages/live/list", "style": {"navigationBarTitleText": "直播列表"}}, {"path": "pages/help/index", "style": {"navigationBarTitleText": "帮助中心"}}, {"path": "pages/help/info", "style": {"navigationBarTitleText": "帮助详情"}}, {"path": "pages/page/info", "style": {"navigationBarTitleText": "单页详情"}}, {"path": "pages/page/web-view", "style": {"navigationBarTitleText": "页面详情"}}, {"path": "pages/user/index", "style": {"navigationBarTitleText": "用户主页"}}, {"path": "pages/me/index", "style": {"navigationBarTitleText": "我的主页"}}, {"path": "pages/me/courses", "style": {"navigationBarTitleText": "我的课程"}}, {"path": "pages/me/favorites", "style": {"navigationBarTitleText": "我的收藏"}}, {"path": "pages/me/consults", "style": {"navigationBarTitleText": "我的咨询"}}, {"path": "pages/me/reviews", "style": {"navigationBarTitleText": "我的评价"}}, {"path": "pages/me/orders", "style": {"navigationBarTitleText": "我的订单"}}, {"path": "pages/me/refunds", "style": {"navigationBarTitleText": "我的退款"}}, {"path": "pages/me/profile", "style": {"navigationBarTitleText": "资料信息"}}, {"path": "pages/me/account", "style": {"navigationBarTitleText": "账户信息"}}, {"path": "pages/account/login", "style": {"navigationBarTitleText": "用户登录"}}, {"path": "pages/account/register", "style": {"navigationBarTitleText": "用户注册"}}, {"path": "pages/account/forget", "style": {"navigationBarTitleText": "忘记密码"}}, {"path": "pages/vip/index", "style": {"navigationBarTitleText": "会员中心"}}, {"path": "pages/order/confirm", "style": {"navigationBarTitleText": "确认订单"}}, {"path": "pages/order/pay", "style": {"navigationBarTitleText": "支付订单"}}, {"path": "pages/order/info", "style": {"navigationBarTitleText": "订单详情"}}, {"path": "pages/trade/h5pay", "style": {"navigationBarTitleText": "H5支付"}}, {"path": "pages/refund/confirm", "style": {"navigationBarTitleText": "确认退款"}}, {"path": "pages/refund/info", "style": {"navigationBarTitleText": "退款详情"}}], "tabBar": {"list": [{"pagePath": "pages/index/index", "iconPath": "static/img/home.png", "selectedIconPath": "static/img/home_fill.png", "text": "首页"}, {"pagePath": "pages/course/category", "iconPath": "static/img/category.png", "selectedIconPath": "static/img/category_fill.png", "text": "分类"}, {"pagePath": "pages/discovery/index", "iconPath": "static/img/discover.png", "selectedIconPath": "static/img/discover_fill.png", "text": "发现"}, {"pagePath": "pages/me/index", "iconPath": "static/img/people.png", "selectedIconPath": "static/img/people_fill.png", "text": "我的"}], "backgroundColor": "#ffffff", "color": "#888888", "selectedColor": "#1aad19"}, "globalStyle": {"navigationBarTextStyle": "black", "navigationBarTitleText": "uni-app", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8"}}