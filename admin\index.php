<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

// 确保必要的数据表存在
function ensureTablesExist($conn) {
    // 创建courses表
    $create_courses = "
    CREATE TABLE IF NOT EXISTS `courses` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `title` varchar(255) NOT NULL COMMENT '课程标题',
        `description` text COMMENT '课程描述',
        `video_url` varchar(500) NOT NULL COMMENT '视频链接',
        `thumbnail` varchar(500) DEFAULT NULL COMMENT '课程缩略图',
        `cover_image` varchar(500) DEFAULT NULL COMMENT '课程封面图片',
        `duration` int(11) DEFAULT NULL COMMENT '视频时长（秒）',
        `price` decimal(10,2) DEFAULT 0.00 COMMENT '课程价格',
        `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
        `is_free` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否免费：0否 1是',
        `status` enum('published','draft','inactive') NOT NULL DEFAULT 'draft' COMMENT '状态：published已发布 draft草稿 inactive禁用',
        `view_count` int(11) NOT NULL DEFAULT 0 COMMENT '观看次数',
        `student_count` int(11) NOT NULL DEFAULT 0 COMMENT '学生数量',
        `rating` decimal(3,2) DEFAULT 0.00 COMMENT '课程评分（0-5）',
        `teacher_name` varchar(100) DEFAULT NULL COMMENT '讲师姓名',
        `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
        `created_by` int(11) NOT NULL COMMENT '创建者ID',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`id`),
        KEY `idx_status` (`status`),
        KEY `idx_sort` (`sort_order`),
        KEY `idx_created_at` (`created_at`),
        KEY `idx_view_count` (`view_count`),
        KEY `idx_student_count` (`student_count`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程表'";

    // 创建orders表
    $create_orders = "
    CREATE TABLE IF NOT EXISTS `orders` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `order_no` varchar(50) NOT NULL COMMENT '订单号',
        `user_id` int(11) NOT NULL COMMENT '用户ID',
        `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
        `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
        `actual_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实付金额',
        `order_status` enum('pending','completed','cancelled','refunded','expired') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
        `payment_status` enum('unpaid','paid','refunding','refunded','failed') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态',
        `payment_method` enum('wechat','alipay') DEFAULT NULL COMMENT '支付方式',
        `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
        `expire_time` datetime NOT NULL COMMENT '过期时间',
        `remark` text COMMENT '订单备注',
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `order_no` (`order_no`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_order_status` (`order_status`),
        KEY `idx_payment_status` (`payment_status`),
        KEY `idx_created_at` (`created_at`),
        KEY `idx_payment_time` (`payment_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表'";

    // 创建order_items表
    $create_order_items = "
    CREATE TABLE IF NOT EXISTS `order_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `order_id` int(11) NOT NULL COMMENT '订单ID',
        `course_id` int(11) NOT NULL COMMENT '课程ID',
        `course_title` varchar(255) NOT NULL COMMENT '课程标题',
        `course_price` decimal(10,2) NOT NULL COMMENT '课程价格',
        `original_price` decimal(10,2) NOT NULL COMMENT '课程原价',
        `quantity` int(11) NOT NULL DEFAULT '1' COMMENT '数量',
        `subtotal` decimal(10,2) NOT NULL COMMENT '小计',
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_order_id` (`order_id`),
        KEY `idx_course_id` (`course_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单商品表'";

    $conn->query($create_courses);
    $conn->query($create_orders);
    $conn->query($create_order_items);
}

// 确保表存在
ensureTablesExist($conn);

// 初始化示例数据（仅在表为空时）
function initSampleData($conn) {
    // 检查courses表是否为空
    $result = $conn->query("SELECT COUNT(*) as count FROM courses");
    $course_count = $result->fetch_assoc()['count'];

    if ($course_count == 0) {
        // 插入示例课程数据
        $sample_courses = [
            ['系统使用入门教程', '本课程将详细介绍系统的基本功能和使用方法', 'published', 99.00, 199.00, 150, 1200],
            ['高级功能使用指南', '深入讲解系统的高级功能和最佳实践', 'published', 199.00, 299.00, 89, 800],
            ['常见问题解答', '汇总了用户在使用过程中遇到的常见问题及解决方案', 'published', 49.00, 99.00, 200, 1500],
            ['新功能预览', '介绍即将上线的新功能特性', 'draft', 0.00, 0.00, 0, 0],
            ['系统维护指南', '系统管理员必备的维护知识', 'draft', 0.00, 0.00, 0, 0]
        ];

        $stmt = $conn->prepare("INSERT INTO courses (title, description, status, price, original_price, student_count, view_count, video_url, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, 'https://example.com/video.mp4', 1)");

        foreach ($sample_courses as $course) {
            $stmt->bind_param("sssddii", $course[0], $course[1], $course[2], $course[3], $course[4], $course[5], $course[6]);
            $stmt->execute();
        }
    }
}

// 初始化示例数据
initSampleData($conn);

// 获取用户统计数据
function getUserStats($conn) {
    $stats = [];

    // 用户总数
    $result = $conn->query("SELECT COUNT(*) as count FROM users");
    $stats['total_users'] = $result->fetch_assoc()['count'];

    // 今日新增用户
    $result = $conn->query("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()");
    $stats['today_new_users'] = $result->fetch_assoc()['count'];

    // 本周新增用户
    $result = $conn->query("SELECT COUNT(*) as count FROM users WHERE YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)");
    $stats['week_new_users'] = $result->fetch_assoc()['count'];

    // 本月新增用户
    $result = $conn->query("SELECT COUNT(*) as count FROM users WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())");
    $stats['month_new_users'] = $result->fetch_assoc()['count'];

    // 活跃用户（最近7天有登录记录）
    // 首先检查user_login_logs表是否存在
    $table_exists = $conn->query("SHOW TABLES LIKE 'user_login_logs'");
    if ($table_exists && $table_exists->num_rows > 0) {
        $result = $conn->query("SELECT COUNT(DISTINCT user_id) as count FROM user_login_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $active_users = $result->fetch_assoc();
        $stats['active_users'] = $active_users ? $active_users['count'] : 0;
    } else {
        // 如果没有登录日志表，使用last_login_time字段（如果存在）
        $column_exists = $conn->query("SHOW COLUMNS FROM users LIKE 'last_login_time'");
        if ($column_exists && $column_exists->num_rows > 0) {
            $result = $conn->query("SELECT COUNT(*) as count FROM users WHERE last_login_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
            $active_users = $result->fetch_assoc();
            $stats['active_users'] = $active_users ? $active_users['count'] : 0;
        } else {
            // 如果都没有，则使用最近7天注册的用户作为活跃用户的近似值
            $result = $conn->query("SELECT COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
            $active_users = $result->fetch_assoc();
            $stats['active_users'] = $active_users ? $active_users['count'] : 0;
        }
    }

    return $stats;
}

// 获取课程统计数据
function getCourseStats($conn) {
    $stats = [];

    // 课程总数
    $result = $conn->query("SELECT COUNT(*) as count FROM courses");
    $stats['total_courses'] = $result->fetch_assoc()['count'];

    // 已发布课程数
    $result = $conn->query("SELECT COUNT(*) as count FROM courses WHERE status = 'published'");
    $stats['published_courses'] = $result->fetch_assoc()['count'];

    // 草稿课程数
    $result = $conn->query("SELECT COUNT(*) as count FROM courses WHERE status = 'draft'");
    $stats['draft_courses'] = $result->fetch_assoc()['count'];

    // 最受欢迎的课程（按学生数量排序，取前5名）
    $result = $conn->query("SELECT id, title, student_count, view_count FROM courses WHERE status = 'published' ORDER BY student_count DESC, view_count DESC LIMIT 5");
    $stats['popular_courses'] = [];
    while ($row = $result->fetch_assoc()) {
        $stats['popular_courses'][] = $row;
    }

    return $stats;
}

// 获取销售统计数据
function getSalesStats($conn) {
    $stats = [];

    // 总销售额
    $result = $conn->query("SELECT COALESCE(SUM(actual_amount), 0) as total FROM orders WHERE payment_status = 'paid'");
    $stats['total_sales'] = $result->fetch_assoc()['total'];

    // 今日销售额
    $result = $conn->query("SELECT COALESCE(SUM(actual_amount), 0) as total FROM orders WHERE payment_status = 'paid' AND DATE(payment_time) = CURDATE()");
    $stats['today_sales'] = $result->fetch_assoc()['total'];

    // 本月销售额
    $result = $conn->query("SELECT COALESCE(SUM(actual_amount), 0) as total FROM orders WHERE payment_status = 'paid' AND YEAR(payment_time) = YEAR(CURDATE()) AND MONTH(payment_time) = MONTH(CURDATE())");
    $stats['month_sales'] = $result->fetch_assoc()['total'];

    // 订单统计
    $result = $conn->query("SELECT COUNT(*) as count FROM orders");
    $stats['total_orders'] = $result->fetch_assoc()['count'];

    $result = $conn->query("SELECT COUNT(*) as count FROM orders WHERE order_status = 'pending'");
    $stats['pending_orders'] = $result->fetch_assoc()['count'];

    $result = $conn->query("SELECT COUNT(*) as count FROM orders WHERE order_status = 'completed'");
    $stats['completed_orders'] = $result->fetch_assoc()['count'];

    // 最近7天销售趋势
    $result = $conn->query("
        SELECT
            DATE(payment_time) as date,
            COALESCE(SUM(actual_amount), 0) as daily_sales,
            COUNT(*) as daily_orders
        FROM orders
        WHERE payment_status = 'paid'
        AND payment_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DATE(payment_time)
        ORDER BY date DESC
    ");
    $stats['sales_trend'] = [];
    while ($row = $result->fetch_assoc()) {
        $stats['sales_trend'][] = $row;
    }

    return $stats;
}

// 获取所有统计数据
$user_stats = getUserStats($conn);
$course_stats = getCourseStats($conn);
$sales_stats = getSalesStats($conn);

// 渲染页面头部
render_admin_header('仪表盘', 'dashboard');
?>

<!-- 欢迎信息 -->
<?php render_card_start('欢迎回来'); ?>
    <div style="display: flex; align-items: center; gap: 15px;">
        <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
            <i class="fas fa-user-circle" style="font-size: 30px; color: white;"></i>
        </div>
        <div>
            <h3 style="margin: 0; color: #2c3e50;">欢迎, <?php echo htmlspecialchars($_SESSION['username']); ?>!</h3>
            <p style="margin: 5px 0 0 0; color: #666;">今天是 <?php echo date('Y年m月d日'); ?></p>
        </div>
    </div>
<?php render_card_end(); ?>

<!-- 用户统计模块 -->
<?php render_card_start('用户统计'); ?>
<div class="admin-stats-grid">
    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-primary">
            <i class="fas fa-users"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($user_stats['total_users']); ?></div>
            <div class="admin-stat-label">用户总数</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-success">
            <i class="fas fa-user-plus"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($user_stats['today_new_users']); ?></div>
            <div class="admin-stat-label">今日新增</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-info">
            <i class="fas fa-calendar-week"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($user_stats['week_new_users']); ?></div>
            <div class="admin-stat-label">本周新增</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-warning">
            <i class="fas fa-calendar-alt"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($user_stats['month_new_users']); ?></div>
            <div class="admin-stat-label">本月新增</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-success">
            <i class="fas fa-user-check"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($user_stats['active_users']); ?></div>
            <div class="admin-stat-label">活跃用户（7天）</div>
        </div>
    </div>
</div>
<?php render_card_end(); ?>

<!-- 课程统计模块 -->
<?php render_card_start('课程统计'); ?>
<div class="admin-stats-grid">
    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-primary">
            <i class="fas fa-graduation-cap"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($course_stats['total_courses']); ?></div>
            <div class="admin-stat-label">课程总数</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-success">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($course_stats['published_courses']); ?></div>
            <div class="admin-stat-label">已发布课程</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-warning">
            <i class="fas fa-edit"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($course_stats['draft_courses']); ?></div>
            <div class="admin-stat-label">草稿课程</div>
        </div>
    </div>
</div>

<?php if (!empty($course_stats['popular_courses'])): ?>
<div style="margin-top: 20px;">
    <h4 style="margin-bottom: 15px; color: #2c3e50;">最受欢迎的课程</h4>
    <div class="admin-table-responsive">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>排名</th>
                    <th>课程名称</th>
                    <th>学生数量</th>
                    <th>观看次数</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($course_stats['popular_courses'] as $index => $course): ?>
                <tr>
                    <td>
                        <span class="badge badge-<?php echo $index < 3 ? 'warning' : 'secondary'; ?>">
                            #<?php echo $index + 1; ?>
                        </span>
                    </td>
                    <td><?php echo htmlspecialchars($course['title']); ?></td>
                    <td><?php echo number_format($course['student_count']); ?></td>
                    <td><?php echo number_format($course['view_count']); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
<?php endif; ?>
<?php render_card_end(); ?>

<!-- 销售统计模块 -->
<?php render_card_start('销售统计'); ?>
<div class="admin-stats-grid">
    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-success">
            <i class="fas fa-dollar-sign"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number">¥<?php echo number_format($sales_stats['total_sales'], 2); ?></div>
            <div class="admin-stat-label">总销售额</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-info">
            <i class="fas fa-calendar-day"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number">¥<?php echo number_format($sales_stats['today_sales'], 2); ?></div>
            <div class="admin-stat-label">今日销售额</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-warning">
            <i class="fas fa-calendar-alt"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number">¥<?php echo number_format($sales_stats['month_sales'], 2); ?></div>
            <div class="admin-stat-label">本月销售额</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-primary">
            <i class="fas fa-shopping-cart"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($sales_stats['total_orders']); ?></div>
            <div class="admin-stat-label">总订单数</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-warning">
            <i class="fas fa-clock"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($sales_stats['pending_orders']); ?></div>
            <div class="admin-stat-label">待处理订单</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-success">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($sales_stats['completed_orders']); ?></div>
            <div class="admin-stat-label">已完成订单</div>
        </div>
    </div>
</div>

<?php if (!empty($sales_stats['sales_trend'])): ?>
<div style="margin-top: 20px;">
    <h4 style="margin-bottom: 15px; color: #2c3e50;">最近7天销售趋势</h4>
    <div class="admin-table-responsive">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>日期</th>
                    <th>销售额</th>
                    <th>订单数</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($sales_stats['sales_trend'] as $trend): ?>
                <tr>
                    <td><?php echo date('m月d日', strtotime($trend['date'])); ?></td>
                    <td>¥<?php echo number_format($trend['daily_sales'], 2); ?></td>
                    <td><?php echo number_format($trend['daily_orders']); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
<?php else: ?>
<div style="margin-top: 20px; text-align: center; color: #666;">
    <i class="fas fa-chart-line" style="font-size: 48px; margin-bottom: 10px;"></i>
    <p>暂无销售数据</p>
</div>
<?php endif; ?>
<?php render_card_end(); ?>

<!-- 快速操作 -->
<?php render_card_start('快速操作'); ?>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
        <?php render_link_button('用户管理', 'users.php', 'admin-btn-primary'); ?>
        <?php render_link_button('课程管理', 'courses.php', 'admin-btn-success'); ?>
        <?php render_link_button('订单管理', 'orders.php', 'admin-btn-warning'); ?>
        <?php render_link_button('系统设置', 'settings.php', 'admin-btn-secondary'); ?>
    </div>
<?php render_card_end(); ?>

<!-- 系统信息 -->
<?php render_card_start('系统信息'); ?>
<div class="admin-stats-grid">
    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-success">
            <i class="fas fa-server"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number">正常</div>
            <div class="admin-stat-label">系统状态</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-info">
            <i class="fab fa-php"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo PHP_VERSION; ?></div>
            <div class="admin-stat-label">PHP版本</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-primary">
            <i class="fas fa-database"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number">MySQL</div>
            <div class="admin-stat-label">数据库</div>
        </div>
    </div>
</div>

<div style="color: #666; line-height: 1.8; margin-top: 20px;">
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
        <div>
            <p><strong>服务器时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            <p><strong>系统启动时间:</strong> <?php echo date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']); ?></p>
        </div>
        <div>
            <p><strong>内存使用:</strong> <?php echo round(memory_get_usage() / 1024 / 1024, 2); ?> MB</p>
            <p><strong>峰值内存:</strong> <?php echo round(memory_get_peak_usage() / 1024 / 1024, 2); ?> MB</p>
        </div>
    </div>
</div>
<?php render_card_end(); ?>

<style>
/* 统计卡片样式 */
.admin-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.admin-stat-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.admin-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.admin-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #fff;
    background: #6c757d;
}

.admin-stat-icon.admin-stat-success {
    background: #28a745;
}

.admin-stat-icon.admin-stat-warning {
    background: #ffc107;
}

.admin-stat-icon.admin-stat-danger {
    background: #dc3545;
}

.admin-stat-icon.admin-stat-info {
    background: #17a2b8;
}

.admin-stat-icon.admin-stat-primary {
    background: #007bff;
}

.admin-stat-content {
    flex: 1;
}

.admin-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.admin-stat-label {
    font-size: 14px;
    color: #6c757d;
}

/* 表格样式 */
.admin-table-responsive {
    overflow-x: auto;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.admin-table th,
.admin-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.admin-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.admin-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 徽章样式 */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-warning {
    color: #212529;
    background-color: #ffc107;
}

.badge-secondary {
    color: #fff;
    background-color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-stats-grid {
        grid-template-columns: 1fr;
    }

    .admin-stat-card {
        padding: 15px;
    }

    .admin-stat-number {
        font-size: 20px;
    }
}
</style>

<?php render_admin_footer(); ?>
