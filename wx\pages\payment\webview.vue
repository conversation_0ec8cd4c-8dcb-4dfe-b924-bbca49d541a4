<template>
	<view class="payment-webview">
		<web-view 
			:src="paymentUrl" 
			@message="handleMessage"
			@load="handleLoad"
			@error="handleError">
		</web-view>
		
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<uni-load-more
				status="loading"
				:content-text="{
					contentdown: '正在加载支付页面...',
					contentrefresh: '正在加载支付页面...',
					contentnomore: '加载完成'
				}"
			></uni-load-more>
		</view>
		
		<!-- 错误状态 -->
		<view v-if="error" class="error-container">
			<uni-icons type="info" size="40" color="#ff6b6b" class="error-icon"></uni-icons>
			<text class="error-text">{{ error }}</text>
			<button class="retry-btn" @click="retryLoad">重试</button>
		</view>
	</view>
</template>

<script>
	import { showError, showSuccess } from '../../utils/storage.js';
	
	export default {
		data() {
			return {
				paymentUrl: '',
				loading: true,
				error: '',
				orderId: '',
				paymentType: ''
			};
		},
		
		onLoad(options) {
			console.log('支付页面参数:', options);
			
			// 获取支付参数
			this.orderId = options.order_id || '';
			this.paymentType = options.type || '';
			this.paymentUrl = decodeURIComponent(options.payment_url || '');
			
			if (!this.paymentUrl) {
				this.error = '支付链接无效';
				this.loading = false;
				return;
			}
			
			console.log('支付页面参数:', {
				order_id: this.orderId,
				payment_url: this.paymentUrl
			});
		},
		
		onShow() {
			// 页面显示时检查支付状态
			if (this.orderId) {
				this.checkPaymentStatus();
			}
		},
		
		methods: {
			/**
			 * 处理webview消息
			 */
			handleMessage(event) {
				console.log('收到webview消息:', event);
				
				const detail = event.detail;
				if (detail && detail.data && detail.data.length > 0) {
					const message = detail.data[0];
					
					if (message.type === 'payment_success') {
						this.handlePaymentSuccess(message.data);
					} else if (message.type === 'payment_failed') {
						this.handlePaymentFailed(message.data);
					}
				}
			},
			
			/**
			 * 处理页面加载完成
			 */
			handleLoad(event) {
				console.log('webview加载完成:', event);
				this.loading = false;
				this.error = '';
			},
			
			/**
			 * 处理页面加载错误
			 */
			handleError(event) {
				console.error('webview加载错误:', event);
				this.loading = false;
				this.error = '支付页面加载失败';
			},
			
			/**
			 * 重试加载
			 */
			retryLoad() {
				this.loading = true;
				this.error = '';
				// 强制重新加载webview
				this.$forceUpdate();
			},
			
			/**
			 * 处理支付成功
			 */
			handlePaymentSuccess(data) {
				console.log('支付成功:', data);
				showSuccess('支付成功');
				
				// 返回上一页并刷新
				setTimeout(() => {
					uni.navigateBack({
						delta: 1
					});
				}, 1500);
			},
			
			/**
			 * 处理支付失败
			 */
			handlePaymentFailed(data) {
				console.log('支付失败:', data);
				showError(data.message || '支付失败');
			},
			
			/**
			 * 检查支付状态
			 */
			async checkPaymentStatus() {
				if (!this.orderId) return;
				
				try {
					const response = await uni.request({
						url: `${this.$config.API_BASE_URL}/payment-status.php`,
						method: 'GET',
						data: {
							order_id: this.orderId
						},
						header: {
							'Authorization': `Bearer ${uni.getStorageSync('access_token')}`
						}
					});
					
					console.log('支付状态检查:', response);
					
					if (response.statusCode === 200 && response.data) {
						const result = response.data;
						
						if (result.code === 200) {
							const status = result.data.status;
							
							if (status === 'paid') {
								this.handlePaymentSuccess({ order_id: this.orderId });
							}
						}
					}
				} catch (error) {
					console.error('检查支付状态失败:', error);
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
	.payment-webview {
		width: 100%;
		height: 100vh;
		position: relative;
	}
	
	.loading-container {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 10;
	}
	
	.error-container {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		text-align: center;
		z-index: 10;
		padding: 20px;
	}
	
	.error-icon {
		margin-bottom: 20px;
	}
	
	.error-text {
		display: block;
		color: #666;
		margin-bottom: 20px;
		font-size: 16px;
	}
	
	.retry-btn {
		background: #007bff;
		color: white;
		border: none;
		border-radius: 4px;
		padding: 10px 20px;
		font-size: 16px;
	}
</style>
