# 后台管理系统 UI 设计规范

## 概述

本文档定义了后台管理系统的UI设计标准，确保所有管理功能页面保持一致的视觉风格和用户体验。

## 设计原则

### 1. 一致性
- 所有页面使用统一的布局结构
- 统一的色彩方案和视觉元素
- 一致的交互模式和反馈机制

### 2. 简洁性
- 清晰的信息层次结构
- 简洁明了的界面元素
- 减少不必要的视觉干扰

### 3. 易用性
- 直观的导航结构
- 清晰的操作反馈
- 响应式设计适配不同设备

## 布局结构

### 主布局 (.admin-layout)
```
┌─────────────────────────────────────────┐
│ 侧边栏导航     │ 主内容区域              │
│ (.admin-sidebar) │ (.admin-main)         │
│                │ ┌─────────────────────┐ │
│                │ │ 顶部导航栏          │ │
│                │ │ (.admin-header)     │ │
│                │ ├─────────────────────┤ │
│                │ │ 内容区域            │ │
│                │ │ (.admin-content)    │ │
│                │ │                     │ │
│                │ └─────────────────────┘ │
└─────────────────────────────────────────┘
```

### 侧边栏规范
- 固定宽度：250px
- 背景：渐变色 (#667eea 到 #764ba2)
- 包含：标题区域 + 导航菜单
- 导航项目：图标 + 文字标签
- 当前页面高亮显示

### 主内容区规范
- 左边距：250px（为侧边栏留空间）
- 包含：顶部导航栏 + 内容区域
- 背景色：#f5f7fa

## 色彩方案

### 主色调
- 主色：#667eea (蓝紫色)
- 辅助色：#764ba2 (深紫色)
- 背景色：#f5f7fa (浅灰蓝)

### 功能色彩
- 成功：#27ae60 (绿色)
- 警告：#f39c12 (橙色)
- 危险：#e74c3c (红色)
- 信息：#3498db (蓝色)
- 次要：#95a5a6 (灰色)

### 文字色彩
- 主文字：#333333
- 次要文字：#666666
- 辅助文字：#999999
- 白色文字：#ffffff

## 组件规范

### 1. 卡片容器 (.admin-card)
```html
<div class="admin-card">
    <div class="admin-card-header">
        <h3 class="admin-card-title">卡片标题</h3>
    </div>
    <div class="admin-card-body">
        <!-- 卡片内容 -->
    </div>
</div>
```

### 2. 表单组件
```html
<form class="admin-form">
    <div class="admin-form-group">
        <label class="admin-form-label">标签</label>
        <input class="admin-form-input" type="text">
    </div>
</form>
```

### 3. 按钮组件
```html
<!-- 主要按钮 -->
<button class="admin-btn admin-btn-primary">主要按钮</button>

<!-- 成功按钮 -->
<button class="admin-btn admin-btn-success">成功按钮</button>

<!-- 危险按钮 -->
<button class="admin-btn admin-btn-danger">危险按钮</button>

<!-- 次要按钮 -->
<button class="admin-btn admin-btn-secondary">次要按钮</button>

<!-- 小尺寸按钮 -->
<button class="admin-btn admin-btn-primary admin-btn-sm">小按钮</button>
```

### 4. 表格组件
```html
<table class="admin-table">
    <thead>
        <tr>
            <th>表头1</th>
            <th>表头2</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>数据1</td>
            <td>数据2</td>
        </tr>
    </tbody>
</table>
```

### 5. 状态标签
```html
<span class="admin-badge admin-badge-success">成功</span>
<span class="admin-badge admin-badge-danger">危险</span>
<span class="admin-badge admin-badge-warning">警告</span>
<span class="admin-badge admin-badge-info">信息</span>
```

## 导航菜单规范

### 菜单项结构
```html
<a href="页面链接" class="admin-nav-item [active]">
    <i class="图标类名"></i>
    菜单文字
</a>
```

### 标准菜单项
- 仪表盘：`fas fa-tachometer-alt`
- 用户管理：`fas fa-users`
- 管理员管理：`fas fa-user-shield`
- 系统设置：`fas fa-cog`

## 模板函数使用

### 页面结构函数
```php
// 渲染页面头部
render_admin_header('页面标题', '当前页面标识');

// 渲染页面尾部
render_admin_footer();
```

### 组件渲染函数
```php
// 卡片容器
render_card_start('卡片标题');
// 内容
render_card_end();

// 表单
render_form_start();
render_form_input('标签', '字段名', '类型', '默认值', 是否必填, '占位符');
render_button('按钮文字', '类型', '样式类');
render_form_end();

// 表格
render_table_start(['表头1', '表头2']);
// 表格行内容
render_table_end();
```

### 消息显示函数
```php
show_success_message('成功消息');
show_error_message('错误消息');
```

## 响应式设计

### 断点设置
- 移动端：< 768px
- 平板端：768px - 1024px
- 桌面端：> 1024px

### 移动端适配
- 侧边栏隐藏，通过按钮切换显示
- 表格横向滚动
- 表单字段垂直排列
- 按钮全宽显示

## 开发规范

### 1. 新页面开发流程
1. 引入模板文件：`require_once 'includes/admin_template.php';`
2. 使用 `render_admin_header()` 渲染页面头部
3. 使用标准组件函数构建页面内容
4. 使用 `render_admin_footer()` 渲染页面尾部

### 2. CSS类命名规范
- 使用 `admin-` 前缀
- 采用BEM命名方式
- 组件类：`.admin-card`
- 修饰类：`.admin-btn-primary`
- 状态类：`.active`

### 3. JavaScript规范
- 使用原生JavaScript
- 函数命名采用驼峰式
- 添加必要的表单验证
- 确认对话框使用统一样式

## 图标使用

### 图标库
使用 Font Awesome 6.0.0 图标库

### 常用图标
- 用户：`fas fa-user`
- 用户组：`fas fa-users`
- 设置：`fas fa-cog`
- 编辑：`fas fa-edit`
- 删除：`fas fa-trash`
- 添加：`fas fa-plus`
- 保存：`fas fa-save`
- 搜索：`fas fa-search`

## 注意事项

1. **一致性**：所有新功能必须遵循此设计规范
2. **可维护性**：使用模板函数而非直接编写HTML
3. **可扩展性**：新增组件应添加到模板文件中
4. **兼容性**：确保在主流浏览器中正常显示
5. **性能**：优化CSS和JavaScript文件大小

## 更新记录

- 2024-07-12：初始版本创建
- 包含完整的UI设计规范和组件库
- 建立标准化的开发流程
