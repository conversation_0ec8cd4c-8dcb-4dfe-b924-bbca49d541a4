!function(e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).QVBP2P=e()}(function(){var q;return function i(n,a,o){function s(t,e){if(!a[t]){if(!n[t]){var r="function"==typeof require&&require;if(!e&&r)return r(t,!0);if(c)return c(t,!0);throw(e=new Error("Cannot find module '"+t+"'")).code="MODULE_NOT_FOUND",e}r=a[t]={exports:{}},n[t][0].call(r.exports,function(e){return s(n[t][1][e]||e)},r,r.exports,i,n,a,o)}return a[t].exports}for(var c="function"==typeof require&&require,e=0;e<o.length;e++)s(o[e]);return s}({1:[function(e,t,r){},{}],2:[function(e,t,r){var i,n;i=this,n=function(e){for(var t=e,r=t.lib.BlockCipher,i=t.algo,l=[],n=[],a=[],o=[],s=[],c=[],u=[],p=[],d=[],h=[],f=[],_=0;_<256;_++)f[_]=_<128?_<<1:_<<1^283;for(var y=0,v=0,_=0;_<256;_++){var m=v^v<<1^v<<2^v<<3^v<<4,g=f[n[l[y]=m=m>>>8^255&m^99]=y],S=f[g],b=f[S],R=257*f[m]^16843008*m;a[y]=R<<24|R>>>8,o[y]=R<<16|R>>>16,s[y]=R<<8|R>>>24,c[y]=R,u[m]=(R=16843009*b^65537*S^257*g^16843008*y)<<24|R>>>8,p[m]=R<<16|R>>>16,d[m]=R<<8|R>>>24,h[m]=R,y?(y=g^f[f[f[b^g]]],v^=f[f[v]]):y=v=1}var T=[0,1,2,4,8,16,32,64,128,27,54],i=i.AES=r.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4,i=4*(1+(this._nRounds=6+r)),n=this._keySchedule=[],a=0;a<i;a++)a<r?n[a]=t[a]:(c=n[a-1],a%r?6<r&&a%r==4&&(c=l[c>>>24]<<24|l[c>>>16&255]<<16|l[c>>>8&255]<<8|l[255&c]):(c=l[(c=c<<8|c>>>24)>>>24]<<24|l[c>>>16&255]<<16|l[c>>>8&255]<<8|l[255&c],c^=T[a/r|0]<<24),n[a]=n[a-r]^c);for(var o=this._invKeySchedule=[],s=0;s<i;s++){var c,a=i-s;c=s%4?n[a]:n[a-4],o[s]=s<4||a<=4?c:u[l[c>>>24]]^p[l[c>>>16&255]]^d[l[c>>>8&255]]^h[l[255&c]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,a,o,s,c,l)},decryptBlock:function(e,t){var r=e[t+1],r=(e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,u,p,d,h,n),e[t+1]);e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,i,n,a,o,s){for(var c=this._nRounds,l=e[t]^r[0],u=e[t+1]^r[1],p=e[t+2]^r[2],d=e[t+3]^r[3],h=4,f=1;f<c;f++)var _=i[l>>>24]^n[u>>>16&255]^a[p>>>8&255]^o[255&d]^r[h++],y=i[u>>>24]^n[p>>>16&255]^a[d>>>8&255]^o[255&l]^r[h++],v=i[p>>>24]^n[d>>>16&255]^a[l>>>8&255]^o[255&u]^r[h++],m=i[d>>>24]^n[l>>>16&255]^a[u>>>8&255]^o[255&p]^r[h++],l=_,u=y,p=v,d=m;_=(s[l>>>24]<<24|s[u>>>16&255]<<16|s[p>>>8&255]<<8|s[255&d])^r[h++],y=(s[u>>>24]<<24|s[p>>>16&255]<<16|s[d>>>8&255]<<8|s[255&l])^r[h++],v=(s[p>>>24]<<24|s[d>>>16&255]<<16|s[l>>>8&255]<<8|s[255&u])^r[h++],m=(s[d>>>24]<<24|s[l>>>16&255]<<16|s[u>>>8&255]<<8|s[255&p])^r[h++];e[t]=_,e[t+1]=y,e[t+2]=v,e[t+3]=m},keySize:8});return t.AES=r._createHelper(i),e.AES},"object"==typeof r?t.exports=r=n(e(4),e(5),e(15),e(9),e(3)):n(i.CryptoJS)},{15:15,3:3,4:4,5:5,9:9}],3:[function(e,t,r){var i,n;i=this,n=function(e){function n(e){return"string"==typeof e?f:h}function a(e,t,r){var i,n=this._iv;n?(i=n,this._iv=void 0):i=this._prevBlock;for(var a=0;a<r;a++)e[t+a]^=i[a]}var t,r,o,i,s,c,l,u,p,d,h,f;e.lib.Cipher||(t=(e=e).lib,r=t.Base,o=t.WordArray,i=t.BufferedBlockAlgorithm,(s=e.enc).Utf8,c=s.Base64,l=e.algo.EvpKDF,u=t.Cipher=i.extend({cfg:r.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){i.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(i){return{encrypt:function(e,t,r){return n(t).encrypt(i,e,t,r)},decrypt:function(e,t,r){return n(t).decrypt(i,e,t,r)}}}}),t.StreamCipher=u.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),s=e.mode={},p=t.BlockCipherMode=r.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),p=s.CBC=((s=p.extend()).Encryptor=s.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize;a.call(this,e,t,i),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+i)}}),s.Decryptor=s.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,n=e.slice(t,t+i);r.decryptBlock(e,t),a.call(this,e,t,i),this._prevBlock=n}}),s),s=(e.pad={}).Pkcs7={pad:function(e,t){for(var t=4*t,r=t-e.sigBytes%t,i=r<<24|r<<16|r<<8|r,n=[],a=0;a<r;a+=4)n.push(i);t=o.create(n,r);e.concat(t)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},t.BlockCipher=u.extend({cfg:u.cfg.extend({mode:p,padding:s}),reset:function(){u.reset.call(this);var e,t=this.cfg,r=t.iv,t=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=t.createEncryptor:(e=t.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,r&&r.words):(this._mode=e.call(t,this,r&&r.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),d=t.CipherParams=r.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),p=(e.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,e=e.salt,e=e?o.create([1398893684,1701076831]).concat(e).concat(t):t;return e.toString(c)},parse:function(e){var t,e=c.parse(e),r=e.words;return 1398893684==r[0]&&1701076831==r[1]&&(t=o.create(r.slice(2,4)),r.splice(0,4),e.sigBytes-=16),d.create({ciphertext:e,salt:t})}},h=t.SerializableCipher=r.extend({cfg:r.extend({format:p}),encrypt:function(e,t,r,i){i=this.cfg.extend(i);var n=e.createEncryptor(r,i),t=n.finalize(t),n=n.cfg;return d.create({ciphertext:t,key:r,iv:n.iv,algorithm:e,mode:n.mode,padding:n.padding,blockSize:e.blockSize,formatter:i.format})},decrypt:function(e,t,r,i){return i=this.cfg.extend(i),t=this._parse(t,i.format),e.createDecryptor(r,i).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),s=(e.kdf={}).OpenSSL={execute:function(e,t,r,i){i=i||o.random(8);e=l.create({keySize:t+r}).compute(e,i),r=o.create(e.words.slice(t),4*r);return e.sigBytes=4*t,d.create({key:e,iv:r,salt:i})}},f=t.PasswordBasedCipher=h.extend({cfg:h.cfg.extend({kdf:s}),encrypt:function(e,t,r,i){r=(i=this.cfg.extend(i)).kdf.execute(r,e.keySize,e.ivSize),i.iv=r.iv,e=h.encrypt.call(this,e,t,r.key,i);return e.mixIn(r),e},decrypt:function(e,t,r,i){i=this.cfg.extend(i),t=this._parse(t,i.format);r=i.kdf.execute(r,e.keySize,e.ivSize,t.salt);return i.iv=r.iv,h.decrypt.call(this,e,t,r.key,i)}}))},"object"==typeof r?t.exports=r=n(e(4),e(9)):n(i.CryptoJS)},{4:4,9:9}],4:[function(_,r,i){!function(f){var e,t;e=this,t=function(){var i,l=Math;if("undefined"!=typeof window&&window.crypto&&(i=window.crypto),"undefined"!=typeof self&&self.crypto&&(i=self.crypto),!(i=!(i=!(i="undefined"!=typeof globalThis&&globalThis.crypto?globalThis.crypto:i)&&"undefined"!=typeof window&&window.msCrypto?window.msCrypto:i)&&void 0!==f&&f.crypto?f.crypto:i)&&"function"==typeof _)try{i=_(1)}catch(e){}var r=Object.create||function(e){return t.prototype=e,e=new t,t.prototype=null,e};function t(){}var e={},n=e.lib={},a=n.Base={extend:function(e){var t=r(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),(t.init.prototype=t).$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},u=n.WordArray=a.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||s).stringify(this)},concat:function(e){var t=this.words,r=e.words,i=this.sigBytes,n=e.sigBytes;if(this.clamp(),i%4)for(var a=0;a<n;a++){var o=r[a>>>2]>>>24-a%4*8&255;t[i+a>>>2]|=o<<24-(i+a)%4*8}else for(var s=0;s<n;s+=4)t[i+s>>>2]=r[s>>>2];return this.sigBytes+=n,this},clamp:function(){var e=this.words,t=this.sigBytes;e[t>>>2]&=4294967295<<32-t%4*8,e.length=l.ceil(t/4)},clone:function(){var e=a.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(function(){if(i){if("function"==typeof i.getRandomValues)try{return i.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof i.randomBytes)try{return i.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")}());return new u.init(t,e)}}),o=e.enc={},s=o.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n++){var a=t[n>>>2]>>>24-n%4*8&255;i.push((a>>>4).toString(16)),i.push((15&a).toString(16))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i+=2)r[i>>>3]|=parseInt(e.substr(i,2),16)<<24-i%8*4;return new u.init(r,t/2)}},c=o.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n++){var a=t[n>>>2]>>>24-n%4*8&255;i.push(String.fromCharCode(a))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i++)r[i>>>2]|=(255&e.charCodeAt(i))<<24-i%4*8;return new u.init(r,t)}},p=o.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},d=n.BufferedBlockAlgorithm=a.extend({reset:function(){this._data=new u.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=p.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(e){var t,r=this._data,i=r.words,n=r.sigBytes,a=this.blockSize,o=n/(4*a),s=(o=e?l.ceil(o):l.max((0|o)-this._minBufferSize,0))*a,e=l.min(4*s,n);if(s){for(var c=0;c<s;c+=a)this._doProcessBlock(i,c);t=i.splice(0,s),r.sigBytes-=e}return new u.init(t,e)},clone:function(){var e=a.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),h=(n.Hasher=d.extend({cfg:a.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(r){return function(e,t){return new r.init(t).finalize(e)}},_createHmacHelper:function(r){return function(e,t){return new h.HMAC.init(r,t).finalize(e)}}}),e.algo={});return e},"object"==typeof i?r.exports=i=t():e.CryptoJS=t()}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{1:1}],5:[function(e,t,r){var i,n;i=this,n=function(e){var f;return f=e.lib.WordArray,e.enc.Base64={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=this._map,n=(e.clamp(),[]),a=0;a<r;a+=3)for(var o=(t[a>>>2]>>>24-a%4*8&255)<<16|(t[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|t[a+2>>>2]>>>24-(a+2)%4*8&255,s=0;s<4&&a+.75*s<r;s++)n.push(i.charAt(o>>>6*(3-s)&63));var c=i.charAt(64);if(c)for(;n.length%4;)n.push(c);return n.join("")},parse:function(e){var t=e.length,r=this._map;if(!(i=this._reverseMap))for(var i=this._reverseMap=[],n=0;n<r.length;n++)i[r.charCodeAt(n)]=n;for(var a,o,s=r.charAt(64),c=(s&&-1!==(s=e.indexOf(s))&&(t=s),e),l=t,u=i,p=[],d=0,h=0;h<l;h++)h%4&&(a=u[c.charCodeAt(h-1)]<<h%4*2,o=u[c.charCodeAt(h)]>>>6-h%4*2,p[d>>>2]|=(a|o)<<24-d%4*8,d++);return f.create(p,d)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},e.enc.Base64},"object"==typeof r?t.exports=r=n(e(4)):n(i.CryptoJS)},{4:4}],6:[function(e,t,r){var i,n;i=this,n=function(e){var f;return f=e.lib.WordArray,e.enc.Base64url={stringify:function(e,t=!0){for(var r=e.words,i=e.sigBytes,n=t?this._safe_map:this._map,a=(e.clamp(),[]),o=0;o<i;o+=3)for(var s=(r[o>>>2]>>>24-o%4*8&255)<<16|(r[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|r[o+2>>>2]>>>24-(o+2)%4*8&255,c=0;c<4&&o+.75*c<i;c++)a.push(n.charAt(s>>>6*(3-c)&63));var l=n.charAt(64);if(l)for(;a.length%4;)a.push(l);return a.join("")},parse:function(e,t=!0){var r=e.length,i=t?this._safe_map:this._map;if(!(n=this._reverseMap))for(var n=this._reverseMap=[],a=0;a<i.length;a++)n[i.charCodeAt(a)]=a;for(var o,s,t=i.charAt(64),c=(t&&-1!==(t=e.indexOf(t))&&(r=t),e),l=r,u=n,p=[],d=0,h=0;h<l;h++)h%4&&(o=u[c.charCodeAt(h-1)]<<h%4*2,s=u[c.charCodeAt(h)]>>>6-h%4*2,p[d>>>2]|=(o|s)<<24-d%4*8,d++);return f.create(p,d)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"},e.enc.Base64url},"object"==typeof r?t.exports=r=n(e(4)):n(i.CryptoJS)},{4:4}],7:[function(e,t,r){var i,n;i=this,n=function(e){return e.enc.Hex},"object"==typeof r?t.exports=r=n(e(4)):n(i.CryptoJS)},{4:4}],8:[function(e,t,r){var i,n;i=this,n=function(e){var n=e.lib.WordArray,t=e.enc;function o(e){return e<<8&4278255360|e>>>8&16711935}return t.Utf16=t.Utf16BE={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n+=2){var a=t[n>>>2]>>>16-n%4*8&65535;i.push(String.fromCharCode(a))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i++)r[i>>>1]|=e.charCodeAt(i)<<16-i%2*16;return n.create(r,2*t)}},t.Utf16LE={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n+=2){var a=o(t[n>>>2]>>>16-n%4*8&65535);i.push(String.fromCharCode(a))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i++)r[i>>>1]|=o(e.charCodeAt(i)<<16-i%2*16);return n.create(r,2*t)}},e.enc.Utf16},"object"==typeof r?t.exports=r=n(e(4)):n(i.CryptoJS)},{4:4}],9:[function(e,t,r){var i,n;i=this,n=function(e){var t,r,i,u,n,a;return r=(t=e).lib,i=r.Base,u=r.WordArray,r=t.algo,n=r.MD5,a=r.EvpKDF=i.extend({cfg:i.extend({keySize:4,hasher:n,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r,i=this.cfg,n=i.hasher.create(),a=u.create(),o=a.words,s=i.keySize,c=i.iterations;o.length<s;){r&&n.update(r),r=n.update(e).finalize(t),n.reset();for(var l=1;l<c;l++)r=n.finalize(r),n.reset();a.concat(r)}return a.sigBytes=4*s,a}}),t.EvpKDF=function(e,t,r){return a.create(r).compute(e,t)},e.EvpKDF},"object"==typeof r?t.exports=r=n(e(4),e(31),e(12)):n(i.CryptoJS)},{12:12,31:31,4:4}],10:[function(e,t,r){var i,n;i=this,n=function(e){var t,r;return t=e.lib.CipherParams,r=e.enc.Hex,e.format.Hex={stringify:function(e){return e.ciphertext.toString(r)},parse:function(e){e=r.parse(e);return t.create({ciphertext:e})}},e.format.Hex},"object"==typeof r?t.exports=r=n(e(4),e(3)):n(i.CryptoJS)},{3:3,4:4}],11:[function(e,t,r){var i,n;i=this,n=function(e){return e.HmacMD5},"object"==typeof r?t.exports=r=n(e(4),e(15),e(12)):n(i.CryptoJS)},{12:12,15:15,4:4}],12:[function(e,t,r){var i,n;i=this,n=function(e){var t,s;t=e.lib.Base,s=e.enc.Utf8,e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=s.parse(t));for(var r=e.blockSize,i=4*r,e=((t=t.sigBytes>i?e.finalize(t):t).clamp(),this._oKey=t.clone()),t=this._iKey=t.clone(),n=e.words,a=t.words,o=0;o<r;o++)n[o]^=1549556828,a[o]^=909522486;e.sigBytes=t.sigBytes=i,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,e=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(e))}})},"object"==typeof r?t.exports=r=n(e(4)):n(i.CryptoJS)},{4:4}],13:[function(e,t,r){var i,n;i=this,n=function(e){return e},"object"==typeof r?t.exports=r=n(e(4),e(38),e(14),e(8),e(5),e(6),e(15),e(31),e(33),e(32),e(36),e(35),e(34),e(30),e(12),e(26),e(9),e(3),e(16),e(18),e(17),e(20),e(19),e(21),e(22),e(23),e(25),e(24),e(10),e(2),e(37),e(29),e(28),e(27)):i.CryptoJS=i.CryptoJS},{10:10,12:12,14:14,15:15,16:16,17:17,18:18,19:19,2:2,20:20,21:21,22:22,23:23,24:24,25:25,26:26,27:27,28:28,29:29,3:3,30:30,31:31,32:32,33:33,34:34,35:35,36:36,37:37,38:38,4:4,5:5,6:6,8:8,9:9}],14:[function(e,t,r){var i,n;i=this,n=function(e){var t,n;return"function"==typeof ArrayBuffer&&(t=e.lib.WordArray,n=t.init,(t.init=function(e){if((e=(e=e instanceof ArrayBuffer?new Uint8Array(e):e)instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array?new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e)instanceof Uint8Array){for(var t=e.byteLength,r=[],i=0;i<t;i++)r[i>>>2]|=e[i]<<24-i%4*8;n.call(this,r,t)}else n.apply(this,arguments)}).prototype=t),e.lib.WordArray},"object"==typeof r?t.exports=r=n(e(4)):n(i.CryptoJS)},{4:4}],15:[function(e,t,r){var i,n;i=this,n=function(e){for(var c=Math,t=e,r=(n=t.lib).WordArray,i=n.Hasher,n=t.algo,E=[],a=0;a<64;a++)E[a]=4294967296*c.abs(c.sin(a+1))|0;function I(e,t,r,i,n,a,o){e=e+(t&r|~t&i)+n+o;return(e<<a|e>>>32-a)+t}function w(e,t,r,i,n,a,o){e=e+(t&i|r&~i)+n+o;return(e<<a|e>>>32-a)+t}function P(e,t,r,i,n,a,o){e=e+(t^r^i)+n+o;return(e<<a|e>>>32-a)+t}function O(e,t,r,i,n,a,o){e=e+(r^(t|~i))+n+o;return(e<<a|e>>>32-a)+t}return n=n.MD5=i.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var i=t+r,n=e[i];e[i]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var a=this._hash.words,o=e[t+0],s=e[t+1],c=e[t+2],l=e[t+3],u=e[t+4],p=e[t+5],d=e[t+6],h=e[t+7],f=e[t+8],_=e[t+9],y=e[t+10],v=e[t+11],m=e[t+12],g=e[t+13],S=e[t+14],b=e[t+15],R=I(a[0],k=a[1],C=a[2],T=a[3],o,7,E[0]),T=I(T,R,k,C,s,12,E[1]),C=I(C,T,R,k,c,17,E[2]),k=I(k,C,T,R,l,22,E[3]);R=I(R,k,C,T,u,7,E[4]),T=I(T,R,k,C,p,12,E[5]),C=I(C,T,R,k,d,17,E[6]),k=I(k,C,T,R,h,22,E[7]),R=I(R,k,C,T,f,7,E[8]),T=I(T,R,k,C,_,12,E[9]),C=I(C,T,R,k,y,17,E[10]),k=I(k,C,T,R,v,22,E[11]),R=I(R,k,C,T,m,7,E[12]),T=I(T,R,k,C,g,12,E[13]),C=I(C,T,R,k,S,17,E[14]),R=w(R,k=I(k,C,T,R,b,22,E[15]),C,T,s,5,E[16]),T=w(T,R,k,C,d,9,E[17]),C=w(C,T,R,k,v,14,E[18]),k=w(k,C,T,R,o,20,E[19]),R=w(R,k,C,T,p,5,E[20]),T=w(T,R,k,C,y,9,E[21]),C=w(C,T,R,k,b,14,E[22]),k=w(k,C,T,R,u,20,E[23]),R=w(R,k,C,T,_,5,E[24]),T=w(T,R,k,C,S,9,E[25]),C=w(C,T,R,k,l,14,E[26]),k=w(k,C,T,R,f,20,E[27]),R=w(R,k,C,T,g,5,E[28]),T=w(T,R,k,C,c,9,E[29]),C=w(C,T,R,k,h,14,E[30]),R=P(R,k=w(k,C,T,R,m,20,E[31]),C,T,p,4,E[32]),T=P(T,R,k,C,f,11,E[33]),C=P(C,T,R,k,v,16,E[34]),k=P(k,C,T,R,S,23,E[35]),R=P(R,k,C,T,s,4,E[36]),T=P(T,R,k,C,u,11,E[37]),C=P(C,T,R,k,h,16,E[38]),k=P(k,C,T,R,y,23,E[39]),R=P(R,k,C,T,g,4,E[40]),T=P(T,R,k,C,o,11,E[41]),C=P(C,T,R,k,l,16,E[42]),k=P(k,C,T,R,d,23,E[43]),R=P(R,k,C,T,_,4,E[44]),T=P(T,R,k,C,m,11,E[45]),C=P(C,T,R,k,b,16,E[46]),R=O(R,k=P(k,C,T,R,c,23,E[47]),C,T,o,6,E[48]),T=O(T,R,k,C,h,10,E[49]),C=O(C,T,R,k,S,15,E[50]),k=O(k,C,T,R,p,21,E[51]),R=O(R,k,C,T,m,6,E[52]),T=O(T,R,k,C,l,10,E[53]),C=O(C,T,R,k,y,15,E[54]),k=O(k,C,T,R,s,21,E[55]),R=O(R,k,C,T,f,6,E[56]),T=O(T,R,k,C,b,10,E[57]),C=O(C,T,R,k,d,15,E[58]),k=O(k,C,T,R,g,21,E[59]),R=O(R,k,C,T,u,6,E[60]),T=O(T,R,k,C,v,10,E[61]),C=O(C,T,R,k,c,15,E[62]),k=O(k,C,T,R,_,21,E[63]),a[0]=a[0]+R|0,a[1]=a[1]+k|0,a[2]=a[2]+C|0,a[3]=a[3]+T|0},_doFinalize:function(){for(var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes,n=(t[i>>>5]|=128<<24-i%32,c.floor(r/4294967296)),n=(t[15+(64+i>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),t[14+(64+i>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),e.sigBytes=4*(t.length+1),this._process(),this._hash),a=n.words,o=0;o<4;o++){var s=a[o];a[o]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}return n},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}}),t.MD5=i._createHelper(n),t.HmacMD5=i._createHmacHelper(n),e.MD5},"object"==typeof r?t.exports=r=n(e(4)):n(i.CryptoJS)},{4:4}],16:[function(e,t,r){var i,n;i=this,n=function(e){function a(e,t,r,i){var n,a=this._iv;a?(n=a.slice(0),this._iv=void 0):n=this._prevBlock,i.encryptBlock(n,0);for(var o=0;o<r;o++)e[t+o]^=n[o]}var t;return e.mode.CFB=((t=e.lib.BlockCipherMode.extend()).Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize;a.call(this,e,t,i,r),this._prevBlock=e.slice(t,t+i)}}),t.Decryptor=t.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,n=e.slice(t,t+i);a.call(this,e,t,i,r),this._prevBlock=n}}),t),e.mode.CFB},"object"==typeof r?t.exports=r=n(e(4),e(3)):n(i.CryptoJS)},{3:3,4:4}],17:[function(e,t,r){var i,n;i=this,n=function(e){function c(e){var t,r,i;return 255==(e>>24&255)?(r=e>>8&255,i=255&e,255===(t=e>>16&255)?(t=0,255===r?(r=0,255===i?i=0:++i):++r):++t,e=0,e=(e+=t<<16)+(r<<8)+i):e+=1<<24,e}var t,r;return e.mode.CTRGladman=(t=e.lib.BlockCipherMode.extend(),r=t.Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,n=this._iv,a=this._counter,o=(n&&(a=this._counter=n.slice(0),this._iv=void 0),0===((n=a)[0]=c(n[0]))&&(n[1]=c(n[1])),a.slice(0));r.encryptBlock(o,0);for(var s=0;s<i;s++)e[t+s]^=o[s]}}),t.Decryptor=r,t),e.mode.CTRGladman},"object"==typeof r?t.exports=r=n(e(4),e(3)):n(i.CryptoJS)},{3:3,4:4}],18:[function(e,t,r){var i,n;i=this,n=function(e){var t,r;return e.mode.CTR=(t=e.lib.BlockCipherMode.extend(),r=t.Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,n=this._iv,a=this._counter,o=(n&&(a=this._counter=n.slice(0),this._iv=void 0),a.slice(0));r.encryptBlock(o,0),a[i-1]=a[i-1]+1|0;for(var s=0;s<i;s++)e[t+s]^=o[s]}}),t.Decryptor=r,t),e.mode.CTR},"object"==typeof r?t.exports=r=n(e(4),e(3)):n(i.CryptoJS)},{3:3,4:4}],19:[function(e,t,r){var i,n;i=this,n=function(e){var t;return e.mode.ECB=((t=e.lib.BlockCipherMode.extend()).Encryptor=t.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),t.Decryptor=t.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),t),e.mode.ECB},"object"==typeof r?t.exports=r=n(e(4),e(3)):n(i.CryptoJS)},{3:3,4:4}],20:[function(e,t,r){var i,n;i=this,n=function(e){var t,r;return e.mode.OFB=(t=e.lib.BlockCipherMode.extend(),r=t.Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,n=this._iv,a=this._keystream;n&&(a=this._keystream=n.slice(0),this._iv=void 0),r.encryptBlock(a,0);for(var o=0;o<i;o++)e[t+o]^=a[o]}}),t.Decryptor=r,t),e.mode.OFB},"object"==typeof r?t.exports=r=n(e(4),e(3)):n(i.CryptoJS)},{3:3,4:4}],21:[function(e,t,r){var i,n;i=this,n=function(e){return e.pad.AnsiX923={pad:function(e,t){var r=e.sigBytes,t=4*t,t=t-r%t,r=r+t-1;e.clamp(),e.words[r>>>2]|=t<<24-r%4*8,e.sigBytes+=t},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Ansix923},"object"==typeof r?t.exports=r=n(e(4),e(3)):n(i.CryptoJS)},{3:3,4:4}],22:[function(e,t,r){var i,n;i=this,n=function(r){return r.pad.Iso10126={pad:function(e,t){t*=4,t-=e.sigBytes%t;e.concat(r.lib.WordArray.random(t-1)).concat(r.lib.WordArray.create([t<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},r.pad.Iso10126},"object"==typeof r?t.exports=r=n(e(4),e(3)):n(i.CryptoJS)},{3:3,4:4}],23:[function(e,t,r){var i,n;i=this,n=function(r){return r.pad.Iso97971={pad:function(e,t){e.concat(r.lib.WordArray.create([2147483648],1)),r.pad.ZeroPadding.pad(e,t)},unpad:function(e){r.pad.ZeroPadding.unpad(e),e.sigBytes--}},r.pad.Iso97971},"object"==typeof r?t.exports=r=n(e(4),e(3)):n(i.CryptoJS)},{3:3,4:4}],24:[function(e,t,r){var i,n;i=this,n=function(e){return e.pad.NoPadding={pad:function(){},unpad:function(){}},e.pad.NoPadding},"object"==typeof r?t.exports=r=n(e(4),e(3)):n(i.CryptoJS)},{3:3,4:4}],25:[function(e,t,r){var i,n;i=this,n=function(e){return e.pad.ZeroPadding={pad:function(e,t){t*=4;e.clamp(),e.sigBytes+=t-(e.sigBytes%t||t)},unpad:function(e){for(var t=e.words,r=e.sigBytes-1,r=e.sigBytes-1;0<=r;r--)if(t[r>>>2]>>>24-r%4*8&255){e.sigBytes=r+1;break}}},e.pad.ZeroPadding},"object"==typeof r?t.exports=r=n(e(4),e(3)):n(i.CryptoJS)},{3:3,4:4}],26:[function(e,t,r){var i,n;i=this,n=function(e){var t,r,i,v,n,m,a;return r=(t=e).lib,i=r.Base,v=r.WordArray,r=t.algo,n=r.SHA1,m=r.HMAC,a=r.PBKDF2=i.extend({cfg:i.extend({keySize:4,hasher:n,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r=this.cfg,i=m.create(r.hasher,e),n=v.create(),a=v.create([1]),o=n.words,s=a.words,c=r.keySize,l=r.iterations;o.length<c;){for(var u=i.update(t).finalize(a),p=(i.reset(),u.words),d=p.length,h=u,f=1;f<l;f++){h=i.finalize(h),i.reset();for(var _=h.words,y=0;y<d;y++)p[y]^=_[y]}n.concat(u),s[0]++}return n.sigBytes=4*c,n}}),t.PBKDF2=function(e,t,r){return a.create(r).compute(e,t)},e.PBKDF2},"object"==typeof r?t.exports=r=n(e(4),e(31),e(12)):n(i.CryptoJS)},{12:12,31:31,4:4}],27:[function(e,t,r){var i,n;i=this,n=function(e){function s(){for(var e=this._X,t=this._C,r=0;r<8;r++)o[r]=t[r];t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0;for(r=0;r<8;r++){var i=e[r]+t[r],n=65535&i,a=i>>>16;c[r]=((n*n>>>17)+n*a>>>15)+a*a^((4294901760&i)*i|0)+((65535&i)*i|0)}e[0]=c[0]+(c[7]<<16|c[7]>>>16)+(c[6]<<16|c[6]>>>16)|0,e[1]=c[1]+(c[0]<<8|c[0]>>>24)+c[7]|0,e[2]=c[2]+(c[1]<<16|c[1]>>>16)+(c[0]<<16|c[0]>>>16)|0,e[3]=c[3]+(c[2]<<8|c[2]>>>24)+c[1]|0,e[4]=c[4]+(c[3]<<16|c[3]>>>16)+(c[2]<<16|c[2]>>>16)|0,e[5]=c[5]+(c[4]<<8|c[4]>>>24)+c[3]|0,e[6]=c[6]+(c[5]<<16|c[5]>>>16)+(c[4]<<16|c[4]>>>16)|0,e[7]=c[7]+(c[6]<<8|c[6]>>>24)+c[5]|0}var t,r,i,n,o,c;return r=(t=e).lib.StreamCipher,i=t.algo,n=[],o=[],c=[],i=i.RabbitLegacy=r.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],i=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]],n=this._b=0;n<4;n++)s.call(this);for(n=0;n<8;n++)i[n]^=r[n+4&7];if(t){var e=t.words,t=e[0],e=e[1],t=16711935&(t<<8|t>>>24)|4278255360&(t<<24|t>>>8),e=16711935&(e<<8|e>>>24)|4278255360&(e<<24|e>>>8),a=t>>>16|4294901760&e,o=e<<16|65535&t;i[0]^=t,i[1]^=a,i[2]^=e,i[3]^=o,i[4]^=t,i[5]^=a,i[6]^=e,i[7]^=o;for(n=0;n<4;n++)s.call(this)}},_doProcessBlock:function(e,t){var r=this._X;s.call(this),n[0]=r[0]^r[5]>>>16^r[3]<<16,n[1]=r[2]^r[7]>>>16^r[5]<<16,n[2]=r[4]^r[1]>>>16^r[7]<<16,n[3]=r[6]^r[3]>>>16^r[1]<<16;for(var i=0;i<4;i++)n[i]=16711935&(n[i]<<8|n[i]>>>24)|4278255360&(n[i]<<24|n[i]>>>8),e[t+i]^=n[i]},blockSize:4,ivSize:2}),t.RabbitLegacy=r._createHelper(i),e.RabbitLegacy},"object"==typeof r?t.exports=r=n(e(4),e(5),e(15),e(9),e(3)):n(i.CryptoJS)},{15:15,3:3,4:4,5:5,9:9}],28:[function(e,t,r){var i,n;i=this,n=function(e){function c(){for(var e=this._X,t=this._C,r=0;r<8;r++)o[r]=t[r];t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0;for(r=0;r<8;r++){var i=e[r]+t[r],n=65535&i,a=i>>>16;s[r]=((n*n>>>17)+n*a>>>15)+a*a^((4294901760&i)*i|0)+((65535&i)*i|0)}e[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,e[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,e[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,e[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,e[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,e[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,e[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,e[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}var t,r,i,n,o,s;return r=(t=e).lib.StreamCipher,i=t.algo,n=[],o=[],s=[],i=i.Rabbit=r.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,r=0;r<4;r++)e[r]=16711935&(e[r]<<8|e[r]>>>24)|4278255360&(e[r]<<24|e[r]>>>8);for(var i=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],n=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]],r=this._b=0;r<4;r++)c.call(this);for(r=0;r<8;r++)n[r]^=i[r+4&7];if(t){var t=t.words,a=t[0],t=t[1],a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t=16711935&(t<<8|t>>>24)|4278255360&(t<<24|t>>>8),o=a>>>16|4294901760&t,s=t<<16|65535&a;n[0]^=a,n[1]^=o,n[2]^=t,n[3]^=s,n[4]^=a,n[5]^=o,n[6]^=t,n[7]^=s;for(r=0;r<4;r++)c.call(this)}},_doProcessBlock:function(e,t){var r=this._X;c.call(this),n[0]=r[0]^r[5]>>>16^r[3]<<16,n[1]=r[2]^r[7]>>>16^r[5]<<16,n[2]=r[4]^r[1]>>>16^r[7]<<16,n[3]=r[6]^r[3]>>>16^r[1]<<16;for(var i=0;i<4;i++)n[i]=16711935&(n[i]<<8|n[i]>>>24)|4278255360&(n[i]<<24|n[i]>>>8),e[t+i]^=n[i]},blockSize:4,ivSize:2}),t.Rabbit=r._createHelper(i),e.Rabbit},"object"==typeof r?t.exports=r=n(e(4),e(5),e(15),e(9),e(3)):n(i.CryptoJS)},{15:15,3:3,4:4,5:5,9:9}],29:[function(e,t,r){var i,n;i=this,n=function(e){var t=e,r=t.lib.StreamCipher,i=t.algo,n=i.RC4=r.extend({_doReset:function(){for(var e=this._key,t=e.words,r=e.sigBytes,i=this._S=[],n=0;n<256;n++)i[n]=n;for(var n=0,a=0;n<256;n++){var o=n%r,o=t[o>>>2]>>>24-o%4*8&255,a=(a+i[n]+o)%256,o=i[n];i[n]=i[a],i[a]=o}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=a.call(this)},keySize:8,ivSize:0});function a(){for(var e=this._S,t=this._i,r=this._j,i=0,n=0;n<4;n++){var r=(r+e[t=(t+1)%256])%256,a=e[t];e[t]=e[r],e[r]=a,i|=e[(e[t]+e[r])%256]<<24-8*n}return this._i=t,this._j=r,i}return t.RC4=r._createHelper(n),i=i.RC4Drop=n.extend({cfg:n.cfg.extend({drop:192}),_doReset:function(){n._doReset.call(this);for(var e=this.cfg.drop;0<e;e--)a.call(this)}}),t.RC4Drop=r._createHelper(i),e.RC4},"object"==typeof r?t.exports=r=n(e(4),e(5),e(15),e(9),e(3)):n(i.CryptoJS)},{15:15,3:3,4:4,5:5,9:9}],30:[function(e,t,r){var i,n;i=this,n=function(e){function T(e,t,r){return e&t|~e&r}function C(e,t,r){return e&r|t&~r}function k(e,t){return e<<t|e>>>32-t}var t,r,i,n,E,I,w,P,O,D;return Math,r=(t=e).lib,i=r.WordArray,n=r.Hasher,r=t.algo,E=i.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),I=i.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),w=i.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),P=i.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),O=i.create([0,1518500249,1859775393,2400959708,2840853838]),D=i.create([1352829926,1548603684,1836072691,2053994217,0]),r=r.RIPEMD160=n.extend({_doReset:function(){this._hash=i.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var i=t+r,n=e[i];e[i]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}for(var a,o,s,c,l,u,p=this._hash.words,d=O.words,h=D.words,f=E.words,_=I.words,y=w.words,v=P.words,m=a=p[0],g=o=p[1],S=s=p[2],b=c=p[3],R=l=p[4],r=0;r<80;r+=1)u=(u=k(u=(u=a+e[t+f[r]]|0)+(r<16?(o^s^c)+d[0]:r<32?T(o,s,c)+d[1]:r<48?((o|~s)^c)+d[2]:r<64?C(o,s,c)+d[3]:(o^(s|~c))+d[4])|0,y[r]))+l|0,a=l,l=c,c=k(s,10),s=o,o=u,u=(u=k(u=(u=m+e[t+_[r]]|0)+(r<16?(g^(S|~b))+h[0]:r<32?C(g,S,b)+h[1]:r<48?((g|~S)^b)+h[2]:r<64?T(g,S,b)+h[3]:(g^S^b)+h[4])|0,v[r]))+R|0,m=R,R=b,b=k(S,10),S=g,g=u;u=p[1]+s+b|0,p[1]=p[2]+c+R|0,p[2]=p[3]+l+m|0,p[3]=p[4]+a+g|0,p[4]=p[0]+o+S|0,p[0]=u},_doFinalize:function(){for(var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes,i=(t[i>>>5]|=128<<24-i%32,t[14+(64+i>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),e.sigBytes=4*(t.length+1),this._process(),this._hash),n=i.words,a=0;a<5;a++){var o=n[a];n[a]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}return i},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}}),t.RIPEMD160=n._createHelper(r),t.HmacRIPEMD160=n._createHmacHelper(r),e.RIPEMD160},"object"==typeof r?t.exports=r=n(e(4)):n(i.CryptoJS)},{4:4}],31:[function(e,t,r){var i,n;i=this,n=function(e){var t,r,i,n,u;return r=(t=e).lib,i=r.WordArray,n=r.Hasher,r=t.algo,u=[],r=r.SHA1=n.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,i=r[0],n=r[1],a=r[2],o=r[3],s=r[4],c=0;c<80;c++){u[c]=c<16?0|e[t+c]:(l=u[c-3]^u[c-8]^u[c-14]^u[c-16])<<1|l>>>31;var l=(i<<5|i>>>27)+s+u[c];l+=c<20?1518500249+(n&a|~n&o):c<40?1859775393+(n^a^o):c<60?(n&a|n&o|a&o)-1894007588:(n^a^o)-899497514,s=o,o=a,a=n<<30|n>>>2,n=i,i=l}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+a|0,r[3]=r[3]+o|0,r[4]=r[4]+s|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[14+(64+i>>>9<<4)]=Math.floor(r/4294967296),t[15+(64+i>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}}),t.SHA1=n._createHelper(r),t.HmacSHA1=n._createHmacHelper(r),e.SHA1},"object"==typeof r?t.exports=r=n(e(4)):n(i.CryptoJS)},{4:4}],32:[function(e,t,r){var i,n;i=this,n=function(e){var t,r,i,n;return r=(t=e).lib.WordArray,i=t.algo,n=i.SHA256,i=i.SHA224=n.extend({_doReset:function(){this._hash=new r.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=n._doFinalize.call(this);return e.sigBytes-=4,e}}),t.SHA224=n._createHelper(i),t.HmacSHA224=n._createHmacHelper(i),e.SHA224},"object"==typeof r?t.exports=r=n(e(4),e(33)):n(i.CryptoJS)},{33:33,4:4}],33:[function(e,t,r){var i,n;i=this,n=function(e){var n=Math,t=e,r=(a=t.lib).WordArray,i=a.Hasher,a=t.algo,o=[],f=[];function s(e){return 4294967296*(e-(0|e))|0}for(var c=2,l=0;l<64;)!function(e){for(var t=n.sqrt(e),r=2;r<=t;r++)if(!(e%r))return;return 1}(c)||(l<8&&(o[l]=s(n.pow(c,.5))),f[l]=s(n.pow(c,1/3)),l++),c++;var _=[],a=a.SHA256=i.extend({_doReset:function(){this._hash=new r.init(o.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,i=r[0],n=r[1],a=r[2],o=r[3],s=r[4],c=r[5],l=r[6],u=r[7],p=0;p<64;p++){_[p]=p<16?0|e[t+p]:(((d=_[p-15])<<25|d>>>7)^(d<<14|d>>>18)^d>>>3)+_[p-7]+(((d=_[p-2])<<15|d>>>17)^(d<<13|d>>>19)^d>>>10)+_[p-16];var d=i&n^i&a^n&a,h=u+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&c^~s&l)+f[p]+_[p],u=l,l=c,c=s,s=o+h|0,o=a,a=n,n=i,i=h+(((i<<30|i>>>2)^(i<<19|i>>>13)^(i<<10|i>>>22))+d)|0}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+a|0,r[3]=r[3]+o|0,r[4]=r[4]+s|0,r[5]=r[5]+c|0,r[6]=r[6]+l|0,r[7]=r[7]+u|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[14+(64+i>>>9<<4)]=n.floor(r/4294967296),t[15+(64+i>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});return t.SHA256=i._createHelper(a),t.HmacSHA256=i._createHmacHelper(a),e.SHA256},"object"==typeof r?t.exports=r=n(e(4)):n(i.CryptoJS)},{4:4}],34:[function(e,t,r){var i,n;i=this,n=function(e){for(var u=Math,t=e,p=(n=t.lib).WordArray,i=n.Hasher,r=t.x64.Word,n=t.algo,E=[],I=[],w=[],a=1,o=0,s=0;s<24;s++){E[a+5*o]=(s+1)*(s+2)/2%64;var c=(2*a+3*o)%5;a=o%5,o=c}for(a=0;a<5;a++)for(o=0;o<5;o++)I[a+5*o]=o+(2*a+3*o)%5*5;for(var l=1,d=0;d<24;d++){for(var h,f=0,_=0,y=0;y<7;y++)1&l&&((h=(1<<y)-1)<32?_^=1<<h:f^=1<<h-32),128&l?l=l<<1^113:l<<=1;w[d]=r.create(f,_)}for(var P=[],v=0;v<25;v++)P[v]=r.create();return n=n.SHA3=i.extend({cfg:i.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new r.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var r=this._state,i=this.blockSize/2,n=0;n<i;n++){var a=e[t+2*n],o=e[t+2*n+1],a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8);(R=r[n]).high^=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),R.low^=a}for(var s=0;s<24;s++){for(var c=0;c<5;c++){for(var l=0,u=0,p=0;p<5;p++)l^=(R=r[c+5*p]).high,u^=R.low;var d=P[c];d.high=l,d.low=u}for(c=0;c<5;c++)for(var h=P[(c+4)%5],f=P[(c+1)%5],_=f.high,f=f.low,l=h.high^(_<<1|f>>>31),u=h.low^(f<<1|_>>>31),p=0;p<5;p++)(R=r[c+5*p]).high^=l,R.low^=u;for(var y=1;y<25;y++){var v=(R=r[y]).high,m=R.low,g=E[y],v=(u=g<32?(l=v<<g|m>>>32-g,m<<g|v>>>32-g):(l=m<<g-32|v>>>64-g,v<<g-32|m>>>64-g),P[I[y]]);v.high=l,v.low=u}var S=P[0],b=r[0];S.high=b.high,S.low=b.low;for(c=0;c<5;c++)for(p=0;p<5;p++){var R=r[y=c+5*p],T=P[y],C=P[(c+1)%5+5*p],k=P[(c+2)%5+5*p];R.high=T.high^~C.high&k.high,R.low=T.low^~C.low&k.low}R=r[0],S=w[s];R.high^=S.high,R.low^=S.low}},_doFinalize:function(){for(var e=this._data,t=e.words,r=(this._nDataBytes,8*e.sigBytes),i=32*this.blockSize,n=(t[r>>>5]|=1<<24-r%32,t[(u.ceil((1+r)/i)*i>>>5)-1]|=128,e.sigBytes=4*t.length,this._process(),this._state),r=this.cfg.outputLength/8,a=r/8,o=[],s=0;s<a;s++){var c=n[s],l=c.high,c=c.low,l=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8);o.push(16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8)),o.push(l)}return new p.init(o,r)},clone:function(){for(var e=i.clone.call(this),t=e._state=this._state.slice(0),r=0;r<25;r++)t[r]=t[r].clone();return e}}),t.SHA3=i._createHelper(n),t.HmacSHA3=i._createHmacHelper(n),e.SHA3},"object"==typeof r?t.exports=r=n(e(4),e(38)):n(i.CryptoJS)},{38:38,4:4}],35:[function(e,t,r){var i,n;i=this,n=function(e){var t,r,i,n,a;return r=(t=e).x64,i=r.Word,n=r.WordArray,r=t.algo,a=r.SHA512,r=r.SHA384=a.extend({_doReset:function(){this._hash=new n.init([new i.init(3418070365,3238371032),new i.init(1654270250,914150663),new i.init(2438529370,812702999),new i.init(355462360,4144912697),new i.init(1731405415,4290775857),new i.init(2394180231,1750603025),new i.init(3675008525,1694076839),new i.init(1203062813,3204075428)])},_doFinalize:function(){var e=a._doFinalize.call(this);return e.sigBytes-=16,e}}),t.SHA384=a._createHelper(r),t.HmacSHA384=a._createHmacHelper(r),e.SHA384},"object"==typeof r?t.exports=r=n(e(4),e(38),e(36)):n(i.CryptoJS)},{36:36,38:38,4:4}],36:[function(e,t,r){var i,n;i=this,n=function(e){var t=e,r=t.lib.Hasher,i=(a=t.x64).Word,n=a.WordArray,a=t.algo;function o(){return i.create.apply(i,arguments)}for(var te=[o(1116352408,3609767458),o(1899447441,602891725),o(3049323471,3964484399),o(3921009573,2173295548),o(961987163,4081628472),o(1508970993,3053834265),o(2453635748,2937671579),o(2870763221,3664609560),o(3624381080,2734883394),o(310598401,1164996542),o(607225278,1323610764),o(1426881987,3590304994),o(1925078388,4068182383),o(2162078206,991336113),o(2614888103,633803317),o(3248222580,3479774868),o(3835390401,2666613458),o(4022224774,944711139),o(264347078,2341262773),o(604807628,2007800933),o(770255983,1495990901),o(1249150122,1856431235),o(1555081692,3175218132),o(1996064986,2198950837),o(2554220882,3999719339),o(2821834349,766784016),o(2952996808,2566594879),o(3210313671,3203337956),o(3336571891,1034457026),o(3584528711,2466948901),o(113926993,3758326383),o(338241895,168717936),o(666307205,1188179964),o(773529912,1546045734),o(1294757372,1522805485),o(1396182291,2643833823),o(1695183700,2343527390),o(1986661051,1014477480),o(2177026350,1206759142),o(2456956037,344077627),o(2730485921,1290863460),o(2820302411,3158454273),o(3259730800,3505952657),o(3345764771,106217008),o(3516065817,3606008344),o(3600352804,1432725776),o(4094571909,1467031594),o(275423344,851169720),o(430227734,3100823752),o(506948616,1363258195),o(659060556,3750685593),o(883997877,3785050280),o(958139571,3318307427),o(1322822218,3812723403),o(1537002063,2003034995),o(1747873779,3602036899),o(1955562222,1575990012),o(2024104815,1125592928),o(2227730452,2716904306),o(2361852424,442776044),o(2428436474,593698344),o(2756734187,3733110249),o(3204031479,2999351573),o(3329325298,3815920427),o(3391569614,3928383900),o(3515267271,566280711),o(3940187606,3454069534),o(4118630271,4000239992),o(116418474,1914138554),o(174292421,2731055270),o(289380356,3203993006),o(460393269,320620315),o(685471733,587496836),o(852142971,1086792851),o(1017036298,365543100),o(1126000580,2618297676),o(1288033470,3409855158),o(1501505948,4234509866),o(1607167915,987167468),o(1816402316,1246189591)],re=[],s=0;s<80;s++)re[s]=o();return a=a.SHA512=r.extend({_doReset:function(){this._hash=new n.init([new i.init(1779033703,4089235720),new i.init(3144134277,2227873595),new i.init(1013904242,4271175723),new i.init(2773480762,1595750129),new i.init(1359893119,2917565137),new i.init(2600822924,725511199),new i.init(528734635,4215389547),new i.init(1541459225,327033209)])},_doProcessBlock:function(U,N){for(var e=this._hash.words,t=e[0],r=e[1],i=e[2],n=e[3],a=e[4],o=e[5],s=e[6],e=e[7],x=t.high,c=t.low,B=r.high,l=r.low,j=i.high,u=i.low,K=n.high,p=n.low,H=a.high,d=a.low,G=o.high,h=o.low,W=s.high,f=s.low,z=e.high,_=e.low,y=x,v=c,m=B,g=l,S=j,b=u,q=K,R=p,T=H,C=d,V=G,k=h,Y=W,J=f,X=z,Q=_,E=0;E<80;E++)var I,w,P=re[E],O=(E<16?(w=P.high=0|U[N+2*E],I=P.low=0|U[N+2*E+1]):(M=(A=re[E-15]).high,A=A.low,F=(L=re[E-2]).high,L=L.low,D=(O=re[E-7]).high,O=O.low,$=(Z=re[E-16]).high,w=(w=((M>>>1|A<<31)^(M>>>8|A<<24)^M>>>7)+D+((I=(D=(A>>>1|M<<31)^(A>>>8|M<<24)^(A>>>7|M<<25))+O)>>>0<D>>>0?1:0))+((F>>>19|L<<13)^(F<<3|L>>>29)^F>>>6)+((I+=A=(L>>>19|F<<13)^(L<<3|F>>>29)^(L>>>6|F<<26))>>>0<A>>>0?1:0),I+=M=Z.low,P.high=w=w+$+(I>>>0<M>>>0?1:0),P.low=I),T&V^~T&Y),D=C&k^~C&J,L=y&m^y&S^m&S,F=(v>>>28|y<<4)^(v<<30|y>>>2)^(v<<25|y>>>7),A=te[E],Z=A.high,$=A.low,M=Q+((C>>>14|T<<18)^(C>>>18|T<<14)^(C<<23|T>>>9)),P=X+((T>>>14|C<<18)^(T>>>18|C<<14)^(T<<23|C>>>9))+(M>>>0<Q>>>0?1:0),ee=F+(v&g^v&b^g&b),X=Y,Q=J,Y=V,J=k,V=T,k=C,T=q+(P=P+O+((M=M+D)>>>0<D>>>0?1:0)+Z+((M=M+$)>>>0<$>>>0?1:0)+w+((M=M+I)>>>0<I>>>0?1:0))+((C=R+M|0)>>>0<R>>>0?1:0)|0,q=S,R=b,S=m,b=g,m=y,g=v,y=P+(((y>>>28|v<<4)^(y<<30|v>>>2)^(y<<25|v>>>7))+L+(ee>>>0<F>>>0?1:0))+((v=M+ee|0)>>>0<M>>>0?1:0)|0;c=t.low=c+v,t.high=x+y+(c>>>0<v>>>0?1:0),l=r.low=l+g,r.high=B+m+(l>>>0<g>>>0?1:0),u=i.low=u+b,i.high=j+S+(u>>>0<b>>>0?1:0),p=n.low=p+R,n.high=K+q+(p>>>0<R>>>0?1:0),d=a.low=d+C,a.high=H+T+(d>>>0<C>>>0?1:0),h=o.low=h+k,o.high=G+V+(h>>>0<k>>>0?1:0),f=s.low=f+J,s.high=W+Y+(f>>>0<J>>>0?1:0),_=e.low=_+Q,e.high=z+X+(_>>>0<Q>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[30+(128+i>>>10<<5)]=Math.floor(r/4294967296),t[31+(128+i>>>10<<5)]=r,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32}),t.SHA512=r._createHelper(a),t.HmacSHA512=r._createHmacHelper(a),e.SHA512},"object"==typeof r?t.exports=r=n(e(4),e(38)):n(i.CryptoJS)},{38:38,4:4}],37:[function(e,t,r){var i,n;i=this,n=function(e){var t=e,i=(r=t.lib).WordArray,r=r.BlockCipher,n=t.algo,l=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],u=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],p=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],d=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],h=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],a=n.DES=r.extend({_doReset:function(){for(var e=this._key.words,t=[],r=0;r<56;r++){var i=l[r]-1;t[r]=e[i>>>5]>>>31-i%32&1}for(var n=this._subKeys=[],a=0;a<16;a++){for(var o=n[a]=[],s=p[a],r=0;r<24;r++)o[r/6|0]|=t[(u[r]-1+s)%28]<<31-r%6,o[4+(r/6|0)]|=t[28+(u[r+24]-1+s)%28]<<31-r%6;o[0]=o[0]<<1|o[0]>>>31;for(r=1;r<7;r++)o[r]=o[r]>>>4*(r-1)+3;o[7]=o[7]<<5|o[7]>>>27}for(var c=this._invSubKeys=[],r=0;r<16;r++)c[r]=n[15-r]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,r){this._lBlock=e[t],this._rBlock=e[t+1],f.call(this,4,252645135),f.call(this,16,65535),_.call(this,2,858993459),_.call(this,8,16711935),f.call(this,1,1431655765);for(var i=0;i<16;i++){for(var n=r[i],a=this._lBlock,o=this._rBlock,s=0,c=0;c<8;c++)s|=d[c][((o^n[c])&h[c])>>>0];this._lBlock=o,this._rBlock=a^s}var l=this._lBlock;this._lBlock=this._rBlock,this._rBlock=l,f.call(this,1,1431655765),_.call(this,8,16711935),_.call(this,2,858993459),f.call(this,16,65535),f.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function f(e,t){t=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=t,this._lBlock^=t<<e}function _(e,t){t=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=t,this._rBlock^=t<<e}return t.DES=r._createHelper(a),n=n.TripleDES=r.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),r=e.length<4?e.slice(0,2):e.slice(2,4),e=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=a.createEncryptor(i.create(t)),this._des2=a.createEncryptor(i.create(r)),this._des3=a.createEncryptor(i.create(e))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2}),t.TripleDES=r._createHelper(n),e.TripleDES},"object"==typeof r?t.exports=r=n(e(4),e(5),e(15),e(9),e(3)):n(i.CryptoJS)},{15:15,3:3,4:4,5:5,9:9}],38:[function(e,t,r){var i,n;i=this,n=function(e){var t,n,a;return t=e.lib,n=t.Base,a=t.WordArray,(t=e.x64={}).Word=n.extend({init:function(e,t){this.high=e,this.low=t}}),t.WordArray=n.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:8*e.length},toX32:function(){for(var e=this.words,t=e.length,r=[],i=0;i<t;i++){var n=e[i];r.push(n.high),r.push(n.low)}return a.create(r,this.sigBytes)},clone:function(){for(var e=n.clone.call(this),t=e.words=this.words.slice(0),r=t.length,i=0;i<r;i++)t[i]=t[i].clone();return e}}),e},"object"==typeof r?t.exports=r=n(e(4)):n(i.CryptoJS)},{4:4}],39:[function(e,t,r){function i(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function c(e){return"function"==typeof e}function l(e){return"object"==typeof e&&null!==e}function u(e){return void 0===e}((t.exports=i).EventEmitter=i).prototype._events=void 0,i.prototype._maxListeners=void 0,i.defaultMaxListeners=10,i.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||isNaN(e))throw TypeError("n must be a positive number");return this._maxListeners=e,this},i.prototype.emit=function(e){var t,r,i,n,a,o,s;if((this._events||(this._events={}),"error"===e)&&(!this._events.error||l(this._events.error)&&!this._events.error.length))throw(s=arguments[1])instanceof Error?s:((o=new Error('Uncaught, unspecified "error" event. ('+s+")")).context=s,o);if(u(t=this._events[e]))return!1;if(c(t))switch(arguments.length){case 1:t.call(this);break;case 2:t.call(this,arguments[1]);break;case 3:t.call(this,arguments[1],arguments[2]);break;default:i=Array.prototype.slice.call(arguments,1),t.apply(this,i)}else if(l(t))for(i=Array.prototype.slice.call(arguments,1),r=(a=t.slice()).length,n=0;n<r;n++)a[n].apply(this,i);return!0},i.prototype.on=i.prototype.addListener=function(e,t){if(c(t))return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",e,c(t.listener)?t.listener:t),this._events[e]?l(this._events[e])?this._events[e].push(t):this._events[e]=[this._events[e],t]:this._events[e]=t,l(this._events[e])&&!this._events[e].warned&&(t=u(this._maxListeners)?i.defaultMaxListeners:this._maxListeners)&&0<t&&this._events[e].length>t&&(this._events[e].warned=!0,console.trace),this;throw TypeError("listener must be a function")},i.prototype.once=function(e,t){var r;if(c(t))return r=!1,i.listener=t,this.on(e,i),this;throw TypeError("listener must be a function");function i(){this.removeListener(e,i),r||(r=!0,t.apply(this,arguments))}},i.prototype.removeListener=function(e,t){var r,i,n,a;if(!c(t))throw TypeError("listener must be a function");if(this._events&&this._events[e])if(n=(r=this._events[e]).length,i=-1,r===t||c(r.listener)&&r.listener===t)delete this._events[e],this._events.removeListener&&this.emit("removeListener",e,t);else if(l(r)){for(a=n;0<a--;)if(r[a]===t||r[a].listener&&r[a].listener===t){i=a;break}if(i<0)return this;1===r.length?(r.length=0,delete this._events[e]):r.splice(i,1),this._events.removeListener&&this.emit("removeListener",e,t)}return this},i.prototype.removeAllListeners=function(e){var t,r;if(this._events)if(this._events.removeListener)if(0===arguments.length){for(t in this._events)"removeListener"!==t&&this.removeAllListeners(t);this.removeAllListeners("removeListener"),this._events={}}else{if(c(r=this._events[e]))this.removeListener(e,r);else if(r)for(;r.length;)this.removeListener(e,r[r.length-1]);delete this._events[e]}else 0===arguments.length?this._events={}:this._events[e]&&delete this._events[e];return this},i.prototype.listeners=function(e){e=this._events&&this._events[e]?c(this._events[e])?[this._events[e]]:this._events[e].slice():[];return e},i.prototype.listenerCount=function(e){if(this._events){e=this._events[e];if(c(e))return 1;if(e)return e.length}return 0},i.listenerCount=function(e,t){return e.listenerCount(t)}},{}],40:[function(e,t,r){"use strict";var D=e(41);function o(e,t,r,i,n){t=D.writeRtpDescription(e.kind,t);return t=(t=(t+=D.writeIceParameters(e.iceGatherer.getLocalParameters()))+D.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===r?"actpass":n||"active"))+("a=mid:"+e.mid+"\r\n"),e.rtpSender&&e.rtpReceiver?t+="a=sendrecv\r\n":e.rtpSender?t+="a=sendonly\r\n":e.rtpReceiver?t+="a=recvonly\r\n":t+="a=inactive\r\n",e.rtpSender&&(r=e.rtpSender._initialTrackId||e.rtpSender.track.id,e.rtpSender._initialTrackId=r,t=t+"a="+(n="msid:"+(i?i.id:"-")+" "+r+"\r\n")+"a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+n,e.sendEncodingParameters[0].rtx)&&(t=(t+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+n)+"a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n"),t+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+D.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(t+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+D.localCName+"\r\n"),t}function L(i,n){function a(e,t){e=parseInt(e,10);for(var r=0;r<t.length;r++)if(t[r].payloadType===e||t[r].preferredPayloadType===e)return t[r]}var o={codecs:[],headerExtensions:[],fecMechanisms:[]};return i.codecs.forEach(function(r){for(var e=0;e<n.codecs.length;e++){var t=n.codecs[e];if(r.name.toLowerCase()===t.name.toLowerCase()&&r.clockRate===t.clockRate&&("rtx"!==r.name.toLowerCase()||!r.parameters||!t.parameters.apt||function(e,t,r,i){e=a(e.parameters.apt,r),r=a(t.parameters.apt,i);return e&&r&&e.name.toLowerCase()===r.name.toLowerCase()}(r,t,i.codecs,n.codecs))){(t=JSON.parse(JSON.stringify(t))).numChannels=Math.min(r.numChannels,t.numChannels),o.codecs.push(t),t.rtcpFeedback=t.rtcpFeedback.filter(function(e){for(var t=0;t<r.rtcpFeedback.length;t++)if(r.rtcpFeedback[t].type===e.type&&r.rtcpFeedback[t].parameter===e.parameter)return!0;return!1});break}}}),i.headerExtensions.forEach(function(e){for(var t=0;t<n.headerExtensions.length;t++){var r=n.headerExtensions[t];if(e.uri===r.uri){o.headerExtensions.push(r);break}}}),o}function a(e,t,r){return-1!=={offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[t][e].indexOf(r)}function F(e,t){var r=e.getRemoteCandidates().find(function(e){return t.foundation===e.foundation&&t.ip===e.ip&&t.port===e.port&&t.priority===e.priority&&t.protocol===e.protocol&&t.type===e.type});return r||e.addRemoteCandidate(t),!r}function d(e,t){t=new Error(t);return t.name=e,t.code={NotSupportedError:9,InvalidStateError:11,InvalidAccessError:15,TypeError:void 0,OperationError:void 0}[e],t}t.exports=function(w,P){function O(e,t){t.addTrack(e),t.dispatchEvent(new w.MediaStreamTrackEvent("addtrack",{track:e}))}function n(e,t,r,i){var n=new Event("track");n.track=t,n.receiver=r,n.transceiver={receiver:r},n.streams=i,w.setTimeout(function(){e._dispatchEvent("track",n)})}function i(e){var t,i,n,r=this,a=document.createDocumentFragment();if(["addEventListener","removeEventListener","dispatchEvent"].forEach(function(e){r[e]=a[e].bind(a)}),this.canTrickleIceCandidates=null,this.needNegotiation=!1,this.localStreams=[],this.remoteStreams=[],this._localDescription=null,this._remoteDescription=null,this.signalingState="stable",this.iceConnectionState="new",this.connectionState="new",this.iceGatheringState="new",e=JSON.parse(JSON.stringify(e||{})),this.usingBundle="max-bundle"===e.bundlePolicy,"negotiate"===e.rtcpMuxPolicy)throw d("NotSupportedError","rtcpMuxPolicy 'negotiate' is not supported");switch(e.rtcpMuxPolicy||(e.rtcpMuxPolicy="require"),e.iceTransportPolicy){case"all":case"relay":break;default:e.iceTransportPolicy="all"}switch(e.bundlePolicy){case"balanced":case"max-compat":case"max-bundle":break;default:e.bundlePolicy="balanced"}if(e.iceServers=(t=e.iceServers||[],i=P,n=!1,(t=JSON.parse(JSON.stringify(t))).filter(function(e){var t,r;if(e&&(e.urls||e.url))return r=e.urls||e.url,e.url&&e.urls,r=(r=(t="string"==typeof r)?[r]:r).filter(function(e){return 0!==e.indexOf("turn:")||-1===e.indexOf("transport=udp")||-1!==e.indexOf("turn:[")||n?0===e.indexOf("stun:")&&14393<=i&&-1===e.indexOf("?transport=udp"):n=!0}),delete e.url,e.urls=t?r[0]:r,!!r.length})),this._iceGatherers=[],e.iceCandidatePoolSize)for(var o=e.iceCandidatePoolSize;0<o;o--)this._iceGatherers.push(new w.RTCIceGatherer({iceServers:e.iceServers,gatherPolicy:e.iceTransportPolicy}));else e.iceCandidatePoolSize=0;this._config=e,this.transceivers=[],this._sdpSessionId=D.generateSessionId(),this._sdpSessionVersion=0,this._dtlsRole=void 0,this._isClosed=!1}Object.defineProperty(i.prototype,"localDescription",{configurable:!0,get:function(){return this._localDescription}}),Object.defineProperty(i.prototype,"remoteDescription",{configurable:!0,get:function(){return this._remoteDescription}}),i.prototype.onicecandidate=null,i.prototype.onaddstream=null,i.prototype.ontrack=null,i.prototype.onremovestream=null,i.prototype.onsignalingstatechange=null,i.prototype.oniceconnectionstatechange=null,i.prototype.onconnectionstatechange=null,i.prototype.onicegatheringstatechange=null,i.prototype.onnegotiationneeded=null,i.prototype.ondatachannel=null,i.prototype._dispatchEvent=function(e,t){this._isClosed||(this.dispatchEvent(t),"function"==typeof this["on"+e]&&this["on"+e](t))},i.prototype._emitGatheringStateChange=function(){var e=new Event("icegatheringstatechange");this._dispatchEvent("icegatheringstatechange",e)},i.prototype.getConfiguration=function(){return this._config},i.prototype.getLocalStreams=function(){return this.localStreams},i.prototype.getRemoteStreams=function(){return this.remoteStreams},i.prototype._createTransceiver=function(e,t){var r=0<this.transceivers.length,e={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:e,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,associatedRemoteMediaStreams:[],wantReceive:!0};return this.usingBundle&&r?(e.iceTransport=this.transceivers[0].iceTransport,e.dtlsTransport=this.transceivers[0].dtlsTransport):(r=this._createIceAndDtlsTransports(),e.iceTransport=r.iceTransport,e.dtlsTransport=r.dtlsTransport),t||this.transceivers.push(e),e},i.prototype.addTrack=function(t,e){if(this._isClosed)throw d("InvalidStateError","Attempted to call addTrack on a closed peerconnection.");var r;if(this.transceivers.find(function(e){return e.track===t}))throw d("InvalidAccessError","Track already exists.");for(var i=0;i<this.transceivers.length;i++)this.transceivers[i].track||this.transceivers[i].kind!==t.kind||(r=this.transceivers[i]);return r=r||this._createTransceiver(t.kind),this._maybeFireNegotiationNeeded(),-1===this.localStreams.indexOf(e)&&this.localStreams.push(e),r.track=t,r.stream=e,r.rtpSender=new w.RTCRtpSender(t,r.dtlsTransport),r.rtpSender},i.prototype.addStream=function(t){var i,r=this;15025<=P?t.getTracks().forEach(function(e){r.addTrack(e,t)}):(i=t.clone(),t.getTracks().forEach(function(e,t){var r=i.getTracks()[t];e.addEventListener("enabled",function(e){r.enabled=e.enabled})}),i.getTracks().forEach(function(e){r.addTrack(e,i)}))},i.prototype.removeTrack=function(t){if(this._isClosed)throw d("InvalidStateError","Attempted to call removeTrack on a closed peerconnection.");if(!(t instanceof w.RTCRtpSender))throw new TypeError("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.");var e=this.transceivers.find(function(e){return e.rtpSender===t});if(!e)throw d("InvalidAccessError","Sender was not created by this connection.");var r=e.stream;e.rtpSender.stop(),e.rtpSender=null,e.track=null,e.stream=null,-1===this.transceivers.map(function(e){return e.stream}).indexOf(r)&&-1<this.localStreams.indexOf(r)&&this.localStreams.splice(this.localStreams.indexOf(r),1),this._maybeFireNegotiationNeeded()},i.prototype.removeStream=function(e){var r=this;e.getTracks().forEach(function(t){var e=r.getSenders().find(function(e){return e.track===t});e&&r.removeTrack(e)})},i.prototype.getSenders=function(){return this.transceivers.filter(function(e){return!!e.rtpSender}).map(function(e){return e.rtpSender})},i.prototype.getReceivers=function(){return this.transceivers.filter(function(e){return!!e.rtpReceiver}).map(function(e){return e.rtpReceiver})},i.prototype._createIceGatherer=function(r,e){var i,n=this;return e&&0<r?this.transceivers[0].iceGatherer:this._iceGatherers.length?this._iceGatherers.shift():(i=new w.RTCIceGatherer({iceServers:this._config.iceServers,gatherPolicy:this._config.iceTransportPolicy}),Object.defineProperty(i,"state",{value:"new",writable:!0}),this.transceivers[r].bufferedCandidateEvents=[],this.transceivers[r].bufferCandidates=function(e){var t=!e.candidate||0===Object.keys(e.candidate).length;i.state=t?"completed":"gathering",null!==n.transceivers[r].bufferedCandidateEvents&&n.transceivers[r].bufferedCandidateEvents.push(e)},i.addEventListener("localcandidate",this.transceivers[r].bufferCandidates),i)},i.prototype._gather=function(i,n){var e,a=this,o=this.transceivers[n].iceGatherer;o.onlocalcandidate||(e=this.transceivers[n].bufferedCandidateEvents,this.transceivers[n].bufferedCandidateEvents=null,o.removeEventListener("localcandidate",this.transceivers[n].bufferCandidates),o.onlocalcandidate=function(e){var t,r;a.usingBundle&&0<n||((t=new Event("icecandidate")).candidate={sdpMid:i,sdpMLineIndex:n},(r=!(e=e.candidate)||0===Object.keys(e).length)?"new"!==o.state&&"gathering"!==o.state||(o.state="completed"):("new"===o.state&&(o.state="gathering"),e.component=1,e.ufrag=o.getLocalParameters().usernameFragment,e=D.writeCandidate(e),t.candidate=Object.assign(t.candidate,D.parseCandidate(e)),t.candidate.candidate=e,t.candidate.toJSON=function(){return{candidate:t.candidate.candidate,sdpMid:t.candidate.sdpMid,sdpMLineIndex:t.candidate.sdpMLineIndex,usernameFragment:t.candidate.usernameFragment}}),(e=D.getMediaSections(a._localDescription.sdp))[t.candidate.sdpMLineIndex]+=r?"a=end-of-candidates\r\n":"a="+t.candidate.candidate+"\r\n",a._localDescription.sdp=D.getDescription(a._localDescription.sdp)+e.join(""),e=a.transceivers.every(function(e){return e.iceGatherer&&"completed"===e.iceGatherer.state}),"gathering"!==a.iceGatheringState&&(a.iceGatheringState="gathering",a._emitGatheringStateChange()),r||a._dispatchEvent("icecandidate",t),e&&(a._dispatchEvent("icecandidate",new Event("icecandidate")),a.iceGatheringState="complete",a._emitGatheringStateChange()))},w.setTimeout(function(){e.forEach(function(e){o.onlocalcandidate(e)})},0))},i.prototype._createIceAndDtlsTransports=function(){var e=this,t=new w.RTCIceTransport(null),r=(t.onicestatechange=function(){e._updateIceConnectionState(),e._updateConnectionState()},new w.RTCDtlsTransport(t));return r.ondtlsstatechange=function(){e._updateConnectionState()},r.onerror=function(){Object.defineProperty(r,"state",{value:"failed",writable:!0}),e._updateConnectionState()},{iceTransport:t,dtlsTransport:r}},i.prototype._disposeIceAndDtlsTransports=function(e){var t=this.transceivers[e].iceGatherer,t=(t&&(delete t.onlocalcandidate,delete this.transceivers[e].iceGatherer),this.transceivers[e].iceTransport),t=(t&&(delete t.onicestatechange,delete this.transceivers[e].iceTransport),this.transceivers[e].dtlsTransport);t&&(delete t.ondtlsstatechange,delete t.onerror,delete this.transceivers[e].dtlsTransport)},i.prototype._transceive=function(e,t,r){var i=L(e.localCapabilities,e.remoteCapabilities);t&&e.rtpSender&&(i.encodings=e.sendEncodingParameters,i.rtcp={cname:D.localCName,compound:e.rtcpParameters.compound},e.recvEncodingParameters.length&&(i.rtcp.ssrc=e.recvEncodingParameters[0].ssrc),e.rtpSender.send(i)),r&&e.rtpReceiver&&0<i.codecs.length&&("video"===e.kind&&e.recvEncodingParameters&&P<15019&&e.recvEncodingParameters.forEach(function(e){delete e.rtx}),e.recvEncodingParameters.length?i.encodings=e.recvEncodingParameters:i.encodings=[{}],i.rtcp={compound:e.rtcpParameters.compound},e.rtcpParameters.cname&&(i.rtcp.cname=e.rtcpParameters.cname),e.sendEncodingParameters.length&&(i.rtcp.ssrc=e.sendEncodingParameters[0].ssrc),e.rtpReceiver.receive(i))},i.prototype.setLocalDescription=function(e){var t,l,u,p=this;return-1===["offer","answer"].indexOf(e.type)?Promise.reject(d("TypeError",'Unsupported type "'+e.type+'"')):!a("setLocalDescription",e.type,p.signalingState)||p._isClosed?Promise.reject(d("InvalidStateError","Can not set local "+e.type+" in state "+p.signalingState)):("offer"===e.type?(t=D.splitSections(e.sdp),l=t.shift(),t.forEach(function(e,t){e=D.parseRtpParameters(e);p.transceivers[t].localCapabilities=e}),p.transceivers.forEach(function(e,t){p._gather(e.mid,t)})):"answer"===e.type&&(l=(t=D.splitSections(p._remoteDescription.sdp)).shift(),u=0<D.matchPrefix(l,"a=ice-lite").length,t.forEach(function(e,t){var r,i=p.transceivers[t],n=i.iceGatherer,a=i.iceTransport,o=i.dtlsTransport,s=i.localCapabilities,c=i.remoteCapabilities;D.isRejected(e)&&0===D.matchPrefix(e,"a=bundle-only").length||i.rejected||(r=D.getIceParameters(e,l),e=D.getDtlsParameters(e,l),u&&(e.role="server"),p.usingBundle&&0!==t||(p._gather(i.mid,t),"new"===a.state&&a.start(n,r,u?"controlling":"controlled"),"new"===o.state&&o.start(e)),t=L(s,c),p._transceive(i,0<t.codecs.length,!1))})),p._localDescription={type:e.type,sdp:e.sdp},"offer"===e.type?p._updateSignalingState("have-local-offer"):p._updateSignalingState("stable"),Promise.resolve())},i.prototype.setRemoteDescription=function(b){var R,T,e,C,k,E,t,I=this;return-1===["offer","answer"].indexOf(b.type)?Promise.reject(d("TypeError",'Unsupported type "'+b.type+'"')):!a("setRemoteDescription",b.type,I.signalingState)||I._isClosed?Promise.reject(d("InvalidStateError","Can not set remote "+b.type+" in state "+I.signalingState)):(R={},I.remoteStreams.forEach(function(e){R[e.id]=e}),T=[],e=D.splitSections(b.sdp),C=e.shift(),k=0<D.matchPrefix(C,"a=ice-lite").length,E=0<D.matchPrefix(C,"a=group:BUNDLE ").length,I.usingBundle=E,t=D.matchPrefix(C,"a=ice-options:")[0],I.canTrickleIceCandidates=!!t&&0<=t.substr(14).split(" ").indexOf("trickle"),e.forEach(function(e,t){var r,i,n,a,o,s,c,l,u,p,d,h,f,_=D.splitLines(e),y=D.getKind(e),v=D.isRejected(e)&&0===D.matchPrefix(e,"a=bundle-only").length,_=_[0].substr(2).split(" ")[2],m=D.getDirection(e,C),g=D.parseMsid(e),S=D.getMid(e)||D.generateIdentifier();v||"application"===y&&("DTLS/SCTP"===_||"UDP/DTLS/SCTP"===_)?I.transceivers[t]={mid:S,kind:y,protocol:_,rejected:!0}:(!v&&I.transceivers[t]&&I.transceivers[t].rejected&&(I.transceivers[t]=I._createTransceiver(y,!0)),_=D.parseRtpParameters(e),v||(a=D.getIceParameters(e,C),(o=D.getDtlsParameters(e,C)).role="client"),i=D.parseRtpEncodingParameters(e),s=D.parseRtcpParameters(e),c=0<D.matchPrefix(e,"a=end-of-candidates",C).length,e=D.matchPrefix(e,"a=candidate:").map(function(e){return D.parseCandidate(e)}).filter(function(e){return 1===e.component}),("offer"===b.type||"answer"===b.type)&&!v&&E&&0<t&&I.transceivers[t]&&(I._disposeIceAndDtlsTransports(t),I.transceivers[t].iceGatherer=I.transceivers[0].iceGatherer,I.transceivers[t].iceTransport=I.transceivers[0].iceTransport,I.transceivers[t].dtlsTransport=I.transceivers[0].dtlsTransport,I.transceivers[t].rtpSender&&I.transceivers[t].rtpSender.setTransport(I.transceivers[0].dtlsTransport),I.transceivers[t].rtpReceiver)&&I.transceivers[t].rtpReceiver.setTransport(I.transceivers[0].dtlsTransport),"offer"!==b.type||v?"answer"!==b.type||v||(v=(r=I.transceivers[t]).iceGatherer,h=r.iceTransport,l=r.dtlsTransport,f=r.rtpReceiver,u=r.sendEncodingParameters,n=r.localCapabilities,I.transceivers[t].recvEncodingParameters=i,I.transceivers[t].remoteCapabilities=_,I.transceivers[t].rtcpParameters=s,e.length&&"new"===h.state&&(!k&&!c||E&&0!==t?e.forEach(function(e){F(r.iceTransport,e)}):h.setRemoteCandidates(e)),E&&0!==t||("new"===h.state&&h.start(v,a,"controlling"),"new"===l.state&&l.start(o)),!L(r.localCapabilities,r.remoteCapabilities).codecs.filter(function(e){return"rtx"===e.name.toLowerCase()}).length&&r.sendEncodingParameters[0].rtx&&delete r.sendEncodingParameters[0].rtx,I._transceive(r,"sendrecv"===m||"recvonly"===m,"sendrecv"===m||"sendonly"===m),!f||"sendrecv"!==m&&"sendonly"!==m?delete r.rtpReceiver:(d=f.track,g?(R[g.stream]||(R[g.stream]=new w.MediaStream),O(d,R[g.stream]),T.push([d,f,R[g.stream]])):(R.default||(R.default=new w.MediaStream),O(d,R.default),T.push([d,f,R.default])))):((r=I.transceivers[t]||I._createTransceiver(y)).mid=S,r.iceGatherer||(r.iceGatherer=I._createIceGatherer(t,E)),e.length&&"new"===r.iceTransport.state&&(!c||E&&0!==t?e.forEach(function(e){F(r.iceTransport,e)}):r.iceTransport.setRemoteCandidates(e)),n=w.RTCRtpReceiver.getCapabilities(y),P<15019&&(n.codecs=n.codecs.filter(function(e){return"rtx"!==e.name})),u=r.sendEncodingParameters||[{ssrc:1001*(2*t+2)}],h=!1,"sendrecv"===m||"sendonly"===m?(h=!r.rtpReceiver,f=r.rtpReceiver||new w.RTCRtpReceiver(r.dtlsTransport,y),h&&(d=f.track,(p=g&&"-"===g.stream?p:g?(R[g.stream]||(R[g.stream]=new w.MediaStream,Object.defineProperty(R[g.stream],"id",{get:function(){return g.stream}})),Object.defineProperty(d,"id",{get:function(){return g.track}}),R[g.stream]):(R.default||(R.default=new w.MediaStream),R.default))&&(O(d,p),r.associatedRemoteMediaStreams.push(p)),T.push([d,f,p]))):r.rtpReceiver&&r.rtpReceiver.track&&(r.associatedRemoteMediaStreams.forEach(function(e){var t=e.getTracks().find(function(e){return e.id===r.rtpReceiver.track.id});t&&(t=t,(e=e).removeTrack(t),e.dispatchEvent(new w.MediaStreamTrackEvent("removetrack",{track:t})))}),r.associatedRemoteMediaStreams=[]),r.localCapabilities=n,r.remoteCapabilities=_,r.rtpReceiver=f,r.rtcpParameters=s,r.sendEncodingParameters=u,r.recvEncodingParameters=i,I._transceive(I.transceivers[t],!1,h)))}),void 0===I._dtlsRole&&(I._dtlsRole="offer"===b.type?"active":"passive"),I._remoteDescription={type:b.type,sdp:b.sdp},"offer"===b.type?I._updateSignalingState("have-remote-offer"):I._updateSignalingState("stable"),Object.keys(R).forEach(function(e){var t,i=R[e];i.getTracks().length&&(-1===I.remoteStreams.indexOf(i)&&(I.remoteStreams.push(i),(t=new Event("addstream")).stream=i,w.setTimeout(function(){I._dispatchEvent("addstream",t)})),T.forEach(function(e){var t=e[0],r=e[1];i.id===e[2].id&&n(I,t,r,[i])}))}),T.forEach(function(e){e[2]||n(I,e[0],e[1],[])}),w.setTimeout(function(){I&&I.transceivers&&I.transceivers.forEach(function(e){e.iceTransport&&"new"===e.iceTransport.state&&0<e.iceTransport.getRemoteCandidates().length&&e.iceTransport.addRemoteCandidate({})})},4e3),Promise.resolve())},i.prototype.close=function(){this.transceivers.forEach(function(e){e.iceTransport&&e.iceTransport.stop(),e.dtlsTransport&&e.dtlsTransport.stop(),e.rtpSender&&e.rtpSender.stop(),e.rtpReceiver&&e.rtpReceiver.stop()}),this._isClosed=!0,this._updateSignalingState("closed")},i.prototype._updateSignalingState=function(e){this.signalingState=e;e=new Event("signalingstatechange");this._dispatchEvent("signalingstatechange",e)},i.prototype._maybeFireNegotiationNeeded=function(){var t=this;"stable"===this.signalingState&&!0!==this.needNegotiation&&(this.needNegotiation=!0,w.setTimeout(function(){var e;t.needNegotiation&&(t.needNegotiation=!1,e=new Event("negotiationneeded"),t._dispatchEvent("negotiationneeded",e))},0))},i.prototype._updateIceConnectionState=function(){var e,t={new:0,closed:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};this.transceivers.forEach(function(e){e.iceTransport&&!e.rejected&&t[e.iceTransport.state]++}),e="new",0<t.failed?e="failed":0<t.checking?e="checking":0<t.disconnected?e="disconnected":0<t.new?e="new":0<t.connected?e="connected":0<t.completed&&(e="completed"),e!==this.iceConnectionState&&(this.iceConnectionState=e,e=new Event("iceconnectionstatechange"),this._dispatchEvent("iceconnectionstatechange",e))},i.prototype._updateConnectionState=function(){var e,t={new:0,closed:0,connecting:0,connected:0,completed:0,disconnected:0,failed:0};this.transceivers.forEach(function(e){e.iceTransport&&e.dtlsTransport&&!e.rejected&&(t[e.iceTransport.state]++,t[e.dtlsTransport.state]++)}),t.connected+=t.completed,e="new",0<t.failed?e="failed":0<t.connecting?e="connecting":0<t.disconnected?e="disconnected":0<t.new?e="new":0<t.connected&&(e="connected"),e!==this.connectionState&&(this.connectionState=e,e=new Event("connectionstatechange"),this._dispatchEvent("connectionstatechange",e))},i.prototype.createOffer=function(){var a=this;if(a._isClosed)return Promise.reject(d("InvalidStateError","Can not call createOffer after close"));var t=a.transceivers.filter(function(e){return"audio"===e.kind}).length,r=a.transceivers.filter(function(e){return"video"===e.kind}).length,e=arguments[0];if(e){if(e.mandatory||e.optional)throw new TypeError("Legacy mandatory/optional constraints not supported.");void 0!==e.offerToReceiveAudio&&(t=!0===e.offerToReceiveAudio?1:!1===e.offerToReceiveAudio?0:e.offerToReceiveAudio),void 0!==e.offerToReceiveVideo&&(r=!0===e.offerToReceiveVideo?1:!1===e.offerToReceiveVideo?0:e.offerToReceiveVideo)}for(a.transceivers.forEach(function(e){"audio"===e.kind?--t<0&&(e.wantReceive=!1):"video"===e.kind&&--r<0&&(e.wantReceive=!1)});0<t||0<r;)0<t&&(a._createTransceiver("audio"),t--),0<r&&(a._createTransceiver("video"),r--);var i=D.writeSessionBoilerplate(a._sdpSessionId,a._sdpSessionVersion++),e=(a.transceivers.forEach(function(e,t){var r=e.track,i=e.kind,n=e.mid||D.generateIdentifier(),n=(e.mid=n,e.iceGatherer||(e.iceGatherer=a._createIceGatherer(t,a.usingBundle)),w.RTCRtpSender.getCapabilities(i)),t=(P<15019&&(n.codecs=n.codecs.filter(function(e){return"rtx"!==e.name})),n.codecs.forEach(function(t){"H264"===t.name&&void 0===t.parameters["level-asymmetry-allowed"]&&(t.parameters["level-asymmetry-allowed"]="1"),e.remoteCapabilities&&e.remoteCapabilities.codecs&&e.remoteCapabilities.codecs.forEach(function(e){t.name.toLowerCase()===e.name.toLowerCase()&&t.clockRate===e.clockRate&&(t.preferredPayloadType=e.payloadType)})}),n.headerExtensions.forEach(function(t){(e.remoteCapabilities&&e.remoteCapabilities.headerExtensions||[]).forEach(function(e){t.uri===e.uri&&(t.id=e.id)})}),e.sendEncodingParameters||[{ssrc:1001*(2*t+1)}]);r&&15019<=P&&"video"===i&&!t[0].rtx&&(t[0].rtx={ssrc:t[0].ssrc+1}),e.wantReceive&&(e.rtpReceiver=new w.RTCRtpReceiver(e.dtlsTransport,i)),e.localCapabilities=n,e.sendEncodingParameters=t}),"max-compat"!==a._config.bundlePolicy&&(i+="a=group:BUNDLE "+a.transceivers.map(function(e){return e.mid}).join(" ")+"\r\n"),i+="a=ice-options:trickle\r\n",a.transceivers.forEach(function(e,t){i=i+o(e,e.localCapabilities,"offer",e.stream,a._dtlsRole)+"a=rtcp-rsize\r\n",!e.iceGatherer||"new"===a.iceGatheringState||0!==t&&a.usingBundle||(e.iceGatherer.getLocalCandidates().forEach(function(e){e.component=1,i+="a="+D.writeCandidate(e)+"\r\n"}),"completed"===e.iceGatherer.state&&(i+="a=end-of-candidates\r\n"))}),new w.RTCSessionDescription({type:"offer",sdp:i}));return Promise.resolve(e)},i.prototype.createAnswer=function(){var i,n,e,a=this;return a._isClosed?Promise.reject(d("InvalidStateError","Can not call createAnswer after close")):"have-remote-offer"!==a.signalingState&&"have-local-pranswer"!==a.signalingState?Promise.reject(d("InvalidStateError","Can not call createAnswer in signalingState "+a.signalingState)):(i=D.writeSessionBoilerplate(a._sdpSessionId,a._sdpSessionVersion++),a.usingBundle&&(i+="a=group:BUNDLE "+a.transceivers.map(function(e){return e.mid}).join(" ")+"\r\n"),i+="a=ice-options:trickle\r\n",n=D.getMediaSections(a._remoteDescription.sdp).length,a.transceivers.forEach(function(e,t){var r;n<t+1||(e.rejected?("application"===e.kind?"DTLS/SCTP"===e.protocol?i+="m=application 0 DTLS/SCTP 5000\r\n":i+="m=application 0 "+e.protocol+" webrtc-datachannel\r\n":"audio"===e.kind?i+="m=audio 0 UDP/TLS/RTP/SAVPF 0\r\na=rtpmap:0 PCMU/8000\r\n":"video"===e.kind&&(i+="m=video 0 UDP/TLS/RTP/SAVPF 120\r\na=rtpmap:120 VP8/90000\r\n"),i+="c=IN IP4 0.0.0.0\r\na=inactive\r\na=mid:"+e.mid+"\r\n"):(e.stream&&("audio"===e.kind?r=e.stream.getAudioTracks()[0]:"video"===e.kind&&(r=e.stream.getVideoTracks()[0]),r)&&15019<=P&&"video"===e.kind&&!e.sendEncodingParameters[0].rtx&&(e.sendEncodingParameters[0].rtx={ssrc:e.sendEncodingParameters[0].ssrc+1}),!(t=L(e.localCapabilities,e.remoteCapabilities)).codecs.filter(function(e){return"rtx"===e.name.toLowerCase()}).length&&e.sendEncodingParameters[0].rtx&&delete e.sendEncodingParameters[0].rtx,i+=o(e,t,"answer",e.stream,a._dtlsRole),e.rtcpParameters&&e.rtcpParameters.reducedSize&&(i+="a=rtcp-rsize\r\n")))}),e=new w.RTCSessionDescription({type:"answer",sdp:i}),Promise.resolve(e))},i.prototype.addIceCandidate=function(s){var c,l=this;return s&&void 0===s.sdpMLineIndex&&!s.sdpMid?Promise.reject(new TypeError("sdpMLineIndex or sdpMid required")):new Promise(function(e,t){if(!l._remoteDescription)return t(d("InvalidStateError","Can not add ICE candidate without a remote description"));if(s&&""!==s.candidate){var r=s.sdpMLineIndex;if(s.sdpMid)for(var i=0;i<l.transceivers.length;i++)if(l.transceivers[i].mid===s.sdpMid){r=i;break}var n=l.transceivers[r];if(!n)return t(d("OperationError","Can not add ICE candidate"));if(n.rejected)return e();var a=0<Object.keys(s.candidate).length?D.parseCandidate(s.candidate):{};if("tcp"===a.protocol&&(0===a.port||9===a.port))return e();if(a.component&&1!==a.component)return e();if((0===r||0<r&&n.iceTransport!==l.transceivers[0].iceTransport)&&!F(n.iceTransport,a))return t(d("OperationError","Can not add ICE candidate"));n=s.candidate.trim();0===n.indexOf("a=")&&(n=n.substr(2)),(c=D.getMediaSections(l._remoteDescription.sdp))[r]+="a="+(a.type?n:"end-of-candidates")+"\r\n",l._remoteDescription.sdp=D.getDescription(l._remoteDescription.sdp)+c.join("")}else for(var o=0;o<l.transceivers.length&&(l.transceivers[o].rejected||(l.transceivers[o].iceTransport.addRemoteCandidate({}),(c=D.getMediaSections(l._remoteDescription.sdp))[o]+="a=end-of-candidates\r\n",l._remoteDescription.sdp=D.getDescription(l._remoteDescription.sdp)+c.join(""),!l.usingBundle));o++);e()})},i.prototype.getStats=function(t){if(t&&t instanceof w.MediaStreamTrack){var r=null;if(this.transceivers.forEach(function(e){e.rtpSender&&e.rtpSender.track===t?r=e.rtpSender:e.rtpReceiver&&e.rtpReceiver.track===t&&(r=e.rtpReceiver)}),r)return r.getStats();throw d("InvalidAccessError","Invalid selector.")}var i=[];return this.transceivers.forEach(function(t){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach(function(e){t[e]&&i.push(t[e].getStats())})}),Promise.all(i).then(function(e){var t=new Map;return e.forEach(function(e){e.forEach(function(e){t.set(e.id,e)})}),t})};["RTCRtpSender","RTCRtpReceiver","RTCIceGatherer","RTCIceTransport","RTCDtlsTransport"].forEach(function(e){var t,e=w[e];e&&e.prototype&&e.prototype.getStats&&(t=e.prototype.getStats,e.prototype.getStats=function(){return t.apply(this).then(function(r){var i=new Map;return Object.keys(r).forEach(function(e){var t;r[e].type={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[(t=r[e]).type]||t.type,i.set(e,r[e])}),i})})});var e=["createOffer","createAnswer"];return e.forEach(function(e){var r=i.prototype[e];i.prototype[e]=function(){var t=arguments;return"function"==typeof t[0]||"function"==typeof t[1]?r.apply(this,[arguments[2]]).then(function(e){"function"==typeof t[0]&&t[0].apply(null,[e])},function(e){"function"==typeof t[1]&&t[1].apply(null,[e])}):r.apply(this,arguments)}}),(e=["setLocalDescription","setRemoteDescription","addIceCandidate"]).forEach(function(e){var r=i.prototype[e];i.prototype[e]=function(){var t=arguments;return"function"==typeof t[1]||"function"==typeof t[2]?r.apply(this,arguments).then(function(){"function"==typeof t[1]&&t[1].apply(null)},function(e){"function"==typeof t[2]&&t[2].apply(null,[e])}):r.apply(this,arguments)}}),["getStats"].forEach(function(e){var t=i.prototype[e];i.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]?t.apply(this,arguments).then(function(){"function"==typeof e[1]&&e[1].apply(null)}):t.apply(this,arguments)}}),i}},{41:41}],41:[function(e,t,r){"use strict";var l={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};l.localCName=l.generateIdentifier(),l.splitLines=function(e){return e.trim().split("\n").map(function(e){return e.trim()})},l.splitSections=function(e){return e.split("\nm=").map(function(e,t){return(0<t?"m="+e:e).trim()+"\r\n"})},l.getDescription=function(e){e=l.splitSections(e);return e&&e[0]},l.getMediaSections=function(e){e=l.splitSections(e);return e.shift(),e},l.matchPrefix=function(e,t){return l.splitLines(e).filter(function(e){return 0===e.indexOf(t)})},l.parseCandidate=function(e){for(var t=(0===e.indexOf("a=candidate:")?e.substring(12):e.substring(10)).split(" "),r={foundation:t[0],component:parseInt(t[1],10),protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]},i=8;i<t.length;i+=2)switch(t[i]){case"raddr":r.relatedAddress=t[i+1];break;case"rport":r.relatedPort=parseInt(t[i+1],10);break;case"tcptype":r.tcpType=t[i+1];break;case"ufrag":r.ufrag=t[i+1],r.usernameFragment=t[i+1];break;default:r[t[i]]=t[i+1]}return r},l.writeCandidate=function(e){var t=[],r=(t.push(e.foundation),t.push(e.component),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port),e.type);return t.push("typ"),t.push(r),"host"!==r&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},l.parseIceOptions=function(e){return e.substr(14).split(" ")},l.parseRtpMap=function(e){var e=e.substr(9).split(" "),t={payloadType:parseInt(e.shift(),10)},e=e[0].split("/");return t.name=e[0],t.clockRate=parseInt(e[1],10),t.channels=3===e.length?parseInt(e[2],10):1,t.numChannels=t.channels,t},l.writeRtpMap=function(e){var t=e.payloadType,r=(void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType),e.channels||e.numChannels||1);return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==r?"/"+r:"")+"\r\n"},l.parseExtmap=function(e){e=e.substr(9).split(" ");return{id:parseInt(e[0],10),direction:0<e[0].indexOf("/")?e[0].split("/")[1]:"sendrecv",uri:e[1]}},l.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},l.parseFmtp=function(e){for(var t,r={},i=e.substr(e.indexOf(" ")+1).split(";"),n=0;n<i.length;n++)r[(t=i[n].trim().split("="))[0].trim()]=t[1];return r},l.writeFmtp=function(t){var r,e="",i=t.payloadType;return void 0!==t.preferredPayloadType&&(i=t.preferredPayloadType),t.parameters&&Object.keys(t.parameters).length&&(r=[],Object.keys(t.parameters).forEach(function(e){t.parameters[e]?r.push(e+"="+t.parameters[e]):r.push(e)}),e+="a=fmtp:"+i+" "+r.join(";")+"\r\n"),e},l.parseRtcpFb=function(e){e=e.substr(e.indexOf(" ")+1).split(" ");return{type:e.shift(),parameter:e.join(" ")}},l.writeRtcpFb=function(e){var t="",r=e.payloadType;return void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach(function(e){t+="a=rtcp-fb:"+r+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"}),t},l.parseSsrcMedia=function(e){var t=e.indexOf(" "),r={ssrc:parseInt(e.substr(7,t-7),10)},i=e.indexOf(":",t);return-1<i?(r.attribute=e.substr(t+1,i-t-1),r.value=e.substr(i+1)):r.attribute=e.substr(t+1),r},l.parseSsrcGroup=function(e){e=e.substr(13).split(" ");return{semantics:e.shift(),ssrcs:e.map(function(e){return parseInt(e,10)})}},l.getMid=function(e){e=l.matchPrefix(e,"a=mid:")[0];if(e)return e.substr(6)},l.parseFingerprint=function(e){e=e.substr(14).split(" ");return{algorithm:e[0].toLowerCase(),value:e[1]}},l.getDtlsParameters=function(e,t){return{role:"auto",fingerprints:l.matchPrefix(e+t,"a=fingerprint:").map(l.parseFingerprint)}},l.writeDtlsParameters=function(e,t){var r="a=setup:"+t+"\r\n";return e.fingerprints.forEach(function(e){r+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"}),r},l.parseCryptoLine=function(e){e=e.substr(9).split(" ");return{tag:parseInt(e[0],10),cryptoSuite:e[1],keyParams:e[2],sessionParams:e.slice(3)}},l.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?l.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},l.parseCryptoKeyParams=function(e){return 0!==e.indexOf("inline:")?null:{keyMethod:"inline",keySalt:(e=e.substr(7).split("|"))[0],lifeTime:e[1],mkiValue:e[2]?e[2].split(":")[0]:void 0,mkiLength:e[2]?e[2].split(":")[1]:void 0}},l.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},l.getCryptoParameters=function(e,t){return l.matchPrefix(e+t,"a=crypto:").map(l.parseCryptoLine)},l.getIceParameters=function(e,t){var r=l.matchPrefix(e+t,"a=ice-ufrag:")[0],e=l.matchPrefix(e+t,"a=ice-pwd:")[0];return r&&e?{usernameFragment:r.substr(12),password:e.substr(10)}:null},l.writeIceParameters=function(e){return"a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n"},l.parseRtpParameters=function(e){for(var t={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},r=l.splitLines(e)[0].split(" "),i=3;i<r.length;i++){var n=r[i],a=l.matchPrefix(e,"a=rtpmap:"+n+" ")[0];if(a){var o=l.parseRtpMap(a),a=l.matchPrefix(e,"a=fmtp:"+n+" ");switch(o.parameters=a.length?l.parseFmtp(a[0]):{},o.rtcpFeedback=l.matchPrefix(e,"a=rtcp-fb:"+n+" ").map(l.parseRtcpFb),t.codecs.push(o),o.name.toUpperCase()){case"RED":case"ULPFEC":t.fecMechanisms.push(o.name.toUpperCase())}}}return l.matchPrefix(e,"a=extmap:").forEach(function(e){t.headerExtensions.push(l.parseExtmap(e))}),t},l.writeRtpDescription=function(e,t){var r="",i=(r=(r=(r=(r+="m="+e+" ")+(0<t.codecs.length?"9":"0")+" UDP/TLS/RTP/SAVPF ")+(t.codecs.map(function(e){return void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType}).join(" ")+"\r\n"))+"c=IN IP4 0.0.0.0\r\n"+"a=rtcp:9 IN IP4 0.0.0.0\r\n",t.codecs.forEach(function(e){r=(r=(r+=l.writeRtpMap(e))+l.writeFmtp(e))+l.writeRtcpFb(e)}),0);return t.codecs.forEach(function(e){e.maxptime>i&&(i=e.maxptime)}),0<i&&(r+="a=maxptime:"+i+"\r\n"),r+="a=rtcp-mux\r\n",t.headerExtensions&&t.headerExtensions.forEach(function(e){r+=l.writeExtmap(e)}),r},l.parseRtpEncodingParameters=function(e){var t,r=[],i=l.parseRtpParameters(e),n=-1!==i.fecMechanisms.indexOf("RED"),a=-1!==i.fecMechanisms.indexOf("ULPFEC"),o=l.matchPrefix(e,"a=ssrc:").map(function(e){return l.parseSsrcMedia(e)}).filter(function(e){return"cname"===e.attribute}),s=0<o.length&&o[0].ssrc,o=l.matchPrefix(e,"a=ssrc-group:FID").map(function(e){return e.substr(17).split(" ").map(function(e){return parseInt(e,10)})}),c=(0<o.length&&1<o[0].length&&o[0][0]===s&&(t=o[0][1]),i.codecs.forEach(function(e){"RTX"===e.name.toUpperCase()&&e.parameters.apt&&(e={ssrc:s,codecPayloadType:parseInt(e.parameters.apt,10)},s&&t&&(e.rtx={ssrc:t}),r.push(e),n)&&((e=JSON.parse(JSON.stringify(e))).fec={ssrc:s,mechanism:a?"red+ulpfec":"red"},r.push(e))}),0===r.length&&s&&r.push({ssrc:s}),l.matchPrefix(e,"b="));return c.length&&(c=0===c[0].indexOf("b=TIAS:")?parseInt(c[0].substr(7),10):0===c[0].indexOf("b=AS:")?1e3*parseInt(c[0].substr(5),10)*.95-16e3:void 0,r.forEach(function(e){e.maxBitrate=c})),r},l.parseRtcpParameters=function(e){var t={},r=l.matchPrefix(e,"a=ssrc:").map(function(e){return l.parseSsrcMedia(e)}).filter(function(e){return"cname"===e.attribute})[0],r=(r&&(t.cname=r.value,t.ssrc=r.ssrc),l.matchPrefix(e,"a=rtcp-rsize")),r=(t.reducedSize=0<r.length,t.compound=0===r.length,l.matchPrefix(e,"a=rtcp-mux"));return t.mux=0<r.length,t},l.parseMsid=function(e){var t,r=l.matchPrefix(e,"a=msid:");return 1===r.length?{stream:(t=r[0].substr(7).split(" "))[0],track:t[1]}:0<(r=l.matchPrefix(e,"a=ssrc:").map(function(e){return l.parseSsrcMedia(e)}).filter(function(e){return"msid"===e.attribute})).length?{stream:(t=r[0].value.split(" "))[0],track:t[1]}:void 0},l.parseSctpDescription=function(e){var t,r=l.parseMLine(e),i=l.matchPrefix(e,"a=max-message-size:"),i=(0<i.length&&(t=parseInt(i[0].substr(19),10)),isNaN(t)&&(t=65536),l.matchPrefix(e,"a=sctp-port:"));return 0<i.length?{port:parseInt(i[0].substr(12),10),protocol:r.fmt,maxMessageSize:t}:0<l.matchPrefix(e,"a=sctpmap:").length?(i=l.matchPrefix(e,"a=sctpmap:")[0].substr(10).split(" "),{port:parseInt(i[0],10),protocol:i[1],maxMessageSize:t}):void 0},l.writeSctpDescription=function(e,t){var r=[],r="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"];return void 0!==t.maxMessageSize&&r.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),r.join("")},l.generateSessionId=function(){return Math.random().toString().substr(2,21)},l.writeSessionBoilerplate=function(e,t,r){t=void 0!==t?t:2,e=e||l.generateSessionId();return"v=0\r\no="+(r||"thisisadapterortc")+" "+e+" "+t+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},l.writeMediaSection=function(e,t,r,i){t=l.writeRtpDescription(e.kind,t);return t=(t=(t+=l.writeIceParameters(e.iceGatherer.getLocalParameters()))+l.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===r?"actpass":"active"))+("a=mid:"+e.mid+"\r\n"),e.direction?t+="a="+e.direction+"\r\n":e.rtpSender&&e.rtpReceiver?t+="a=sendrecv\r\n":e.rtpSender?t+="a=sendonly\r\n":e.rtpReceiver?t+="a=recvonly\r\n":t+="a=inactive\r\n",e.rtpSender&&(t=t+"a="+(r="msid:"+i.id+" "+e.rtpSender.track.id+"\r\n")+"a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+r,e.sendEncodingParameters[0].rtx)&&(t=(t+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+r)+"a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n"),t+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+l.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(t+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+l.localCName+"\r\n"),t},l.getDirection=function(e,t){for(var r=l.splitLines(e),i=0;i<r.length;i++)switch(r[i]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return r[i].substr(2)}return t?l.getDirection(t):"sendrecv"},l.getKind=function(e){return l.splitLines(e)[0].split(" ")[0].substr(2)},l.isRejected=function(e){return"0"===e.split(" ",2)[1]},l.parseMLine=function(e){e=l.splitLines(e)[0].substr(2).split(" ");return{kind:e[0],port:parseInt(e[1],10),protocol:e[2],fmt:e.slice(3).join(" ")}},l.parseOLine=function(e){e=l.matchPrefix(e,"o=")[0].substr(2).split(" ");return{username:e[0],sessionId:e[1],sessionVersion:parseInt(e[2],10),netType:e[3],addressType:e[4],address:e[5]}},l.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;for(var t=l.splitLines(e),r=0;r<t.length;r++)if(t[r].length<2||"="!==t[r].charAt(1))return!1;return!0},"object"==typeof t&&(t.exports=l)},{}],42:[function(e,W,z){!function(n,p){"use strict";function e(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t}function U(e,t){return typeof e===u&&-1!==A(t).indexOf(A(e))}function a(e,t){if(typeof e===u)return e=e.replace(/^\s\s*/,c).replace(/\s\s*$/,c),typeof t==l?e:e.substring(0,255)}function o(e,t){for(var r,i,n,a,o,s=0;s<t.length&&!a;){for(var c=t[s],l=t[s+1],u=r=0;u<c.length&&!a;)if(a=c[u++].exec(e))for(i=0;i<l.length;i++)o=a[++r],typeof(n=l[i])===h&&0<n.length?2===n.length?typeof n[1]==d?this[n[0]]=n[1].call(this,o):this[n[0]]=n[1]:3===n.length?typeof n[1]!==d||n[1].exec&&n[1].test?this[n[0]]=o?o.replace(n[1],n[2]):p:this[n[0]]=o?n[1].call(this,o,n[2]):p:4===n.length&&(this[n[0]]=o?n[3].call(this,o.replace(n[1],n[2])):p):this[n]=o||p;s+=2}}function t(e,t){for(var r in t)if(typeof t[r]===h&&0<t[r].length){for(var i=0;i<t[r].length;i++)if(U(t[r][i],e))return"?"===r?p:r}else if(U(t[r],e))return"?"===r?p:r;return e}function s(e,t){var r,i;return typeof e===h&&(t=e,e=p),this instanceof s?(r=e||(typeof n!=l&&n.navigator&&n.navigator.userAgent?n.navigator.userAgent:c),i=t?function(e,t){var r,i={};for(r in e)t[r]&&t[r].length%2==0?i[r]=t[r].concat(e[r]):i[r]=e[r];return i}(G,t):G,this.getBrowser=function(){var e,t={};return t[_]=p,t[m]=p,o.call(t,r,i.browser),t.major=typeof(e=t.version)===u?e.replace(/[^\d\.]/g,c).split(".")[0]:p,t},this.getCPU=function(){var e={};return e[g]=p,o.call(e,r,i.cpu),e},this.getDevice=function(){var e={};return e[v]=p,e[f]=p,e[y]=p,o.call(e,r,i.device),e},this.getEngine=function(){var e={};return e[_]=p,e[m]=p,o.call(e,r,i.engine),e},this.getOS=function(){var e={};return e[_]=p,e[m]=p,o.call(e,r,i.os),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(e){return r=typeof e===u&&255<e.length?a(e,255):e,this},this.setUA(r),this):new s(e,t).getResult()}var i,c="",d="function",l="undefined",h="object",u="string",f="model",_="name",y="type",v="vendor",m="version",g="architecture",r="console",S="mobile",b="tablet",R="smarttv",T="wearable",N="embedded",C="Amazon",k="Apple",x="BlackBerry",E="Browser",I="Chrome",w="Firefox",P="Google",O="Microsoft",B="Motorola",D="Opera",L="Samsung",F="Sony",j="Zebra",K="Facebook",A=function(e){return e.toLowerCase()},H={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},G={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[m,[_,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[m,[_,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[_,m],[/opios[\/ ]+([\w\.]+)/i],[m,[_,D+" Mini"]],[/\bopr\/([\w\.]+)/i],[m,[_,D]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale|qqbrowserlite|qq)\/([-\w\.]+)/i,/(weibo)__([\d\.]+)/i],[_,m],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[m,[_,"UC"+E]],[/\bqbcore\/([\w\.]+)/i],[m,[_,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[m,[_,"WeChat"]],[/konqueror\/([\w\.]+)/i],[m,[_,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[m,[_,"IE"]],[/yabrowser\/([\w\.]+)/i],[m,[_,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[_,/(.+)/,"$1 Secure "+E],m],[/\bfocus\/([\w\.]+)/i],[m,[_,w+" Focus"]],[/\bopt\/([\w\.]+)/i],[m,[_,D+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[m,[_,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[m,[_,"Dolphin"]],[/coast\/([\w\.]+)/i],[m,[_,D+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[m,[_,"MIUI "+E]],[/fxios\/([-\w\.]+)/i],[m,[_,w]],[/\bqihu|(qi?ho?o?|360)browser/i],[[_,"360 "+E]],[/(oculus|samsung|sailfish)browser\/([\w\.]+)/i],[[_,/(.+)/,"$1 "+E],m],[/(comodo_dragon)\/([\w\.]+)/i],[[_,/_/g," "],m],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[_,m],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i],[_],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[_,K],m],[/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[_,m],[/\bgsa\/([\w\.]+) .*safari\//i],[m,[_,"GSA"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[m,[_,I+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[_,I+" WebView"],m],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[m,[_,"Android "+E]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[_,m],[/version\/([\w\.]+) .*mobile\/\w+ (safari)/i],[m,[_,"Mobile Safari"]],[/version\/([\w\.]+) .*(mobile ?safari|safari)/i],[m,_],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[_,[m,t,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[_,m],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[_,"Netscape"],m],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[m,[_,w+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[_,m]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,A]],[/((?:i[346]|x)86)[;\)]/i],[[g,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[g,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[g,/ower/,c,A]],[/(sun4\w)[;\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[g,A]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[pt]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[v,L],[y,b]],[/\b((?:s[cgp]h|gt|sm)-\w+|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[v,L],[y,S]],[/\((ip(?:hone|od)[\w ]*);/i],[f,[v,k],[y,S]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[v,k],[y,b]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[v,"Huawei"],[y,b]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}-[atu]?[ln][01259x][012359][an]?)\b(?!.+d\/s)/i],[f,[v,"Huawei"],[y,S]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[v,"Xiaomi"],[y,S]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[v,"Xiaomi"],[y,b]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[v,"OPPO"],[y,S]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[v,"Vivo"],[y,S]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[f,[v,"Realme"],[y,S]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[v,B],[y,S]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[v,B],[y,b]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[v,"LG"],[y,b]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[v,"LG"],[y,S]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[v,"Lenovo"],[y,b]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[v,"Nokia"],[y,S]],[/(pixel c)\b/i],[f,[v,P],[y,b]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[v,P],[y,S]],[/droid.+ ([c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[v,F],[y,S]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[v,F],[y,b]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[v,"OnePlus"],[y,S]],[/(alexa)webm/i,/(kf[a-z]{2}wi)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[v,C],[y,b]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[v,C],[y,S]],[/(playbook);[-\w\),; ]+(rim)/i],[f,v,[y,b]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[v,x],[y,S]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[v,"ASUS"],[y,b]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[v,"ASUS"],[y,S]],[/(nexus 9)/i],[f,[v,"HTC"],[y,b]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic|sony)[-_ ]?([-\w]*)/i],[v,[f,/_/g," "],[y,S]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[v,"Acer"],[y,b]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[v,"Meizu"],[y,S]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[v,"Sharp"],[y,S]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[v,f,[y,S]],[/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[v,f,[y,b]],[/(surface duo)/i],[f,[v,O],[y,b]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[v,"Fairphone"],[y,S]],[/(u304aa)/i],[f,[v,"AT&T"],[y,S]],[/\bsie-(\w*)/i],[f,[v,"Siemens"],[y,S]],[/\b(rct\w+) b/i],[f,[v,"RCA"],[y,b]],[/\b(venue[\d ]{2,7}) b/i],[f,[v,"Dell"],[y,b]],[/\b(q(?:mv|ta)\w+) b/i],[f,[v,"Verizon"],[y,b]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[v,"Barnes & Noble"],[y,b]],[/\b(tm\d{3}\w+) b/i],[f,[v,"NuVision"],[y,b]],[/\b(k88) b/i],[f,[v,"ZTE"],[y,b]],[/\b(nx\d{3}j) b/i],[f,[v,"ZTE"],[y,S]],[/\b(gen\d{3}) b.+49h/i],[f,[v,"Swiss"],[y,S]],[/\b(zur\d{3}) b/i],[f,[v,"Swiss"],[y,b]],[/\b((zeki)?tb.*\b) b/i],[f,[v,"Zeki"],[y,b]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[v,"Dragon Touch"],f,[y,b]],[/\b(ns-?\w{0,9}) b/i],[f,[v,"Insignia"],[y,b]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[v,"NextBook"],[y,b]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[v,"Voice"],f,[y,S]],[/\b(lvtel\-)?(v1[12]) b/i],[[v,"LvTel"],f,[y,S]],[/\b(ph-1) /i],[f,[v,"Essential"],[y,S]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[v,"Envizen"],[y,b]],[/\b(trio[-\w\. ]+) b/i],[f,[v,"MachSpeed"],[y,b]],[/\btu_(1491) b/i],[f,[v,"Rotor"],[y,b]],[/(shield[\w ]+) b/i],[f,[v,"Nvidia"],[y,b]],[/(sprint) (\w+)/i],[v,f,[y,S]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[v,O],[y,S]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[v,j],[y,b]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[v,j],[y,S]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[v,f,[y,r]],[/droid.+; (shield) bui/i],[f,[v,"Nvidia"],[y,r]],[/(playstation [345portablevi]+)/i],[f,[v,F],[y,r]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[v,O],[y,r]],[/smart-tv.+(samsung)/i],[v,[y,R]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[v,L],[y,R]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[v,"LG"],[y,R]],[/(apple) ?tv/i],[v,[f,k+" TV"],[y,R]],[/crkey/i],[[f,I+"cast"],[v,P],[y,R]],[/droid.+aft(\w)( bui|\))/i],[f,[v,C],[y,R]],[/\(dtv[\);].+(aquos)/i],[f,[v,"Sharp"],[y,R]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w ]*; *(\w[^;]*);([^;]*)/i],[[v,a],[f,a],[y,R]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[y,R]],[/((pebble))app/i],[v,f,[y,T]],[/droid.+; (glass) \d/i],[f,[v,P],[y,T]],[/droid.+; (wt63?0{2,3})\)/i],[f,[v,j],[y,T]],[/(quest( 2)?)/i],[f,[v,K],[y,T]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[v,[y,N]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[f,[y,S]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[y,b]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[y,b]],[/(phone|mobile(?:[;\/]| safari)|pda(?=.+windows ce))/i],[[y,S]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[v,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[m,[_,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[m,[_,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i],[_,m],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[m,_]],os:[[/microsoft (windows) (vista|xp)/i],[_,m],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[_,[m,t,H]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[_,"Windows"],[m,t,H]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/cfnetwork\/.+darwin/i],[[m,/_/g,"."],[_,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[_,"Mac OS"],[m,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86)/i],[m,_],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[_,m],[/\(bb(10);/i],[m,[_,x]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[m,[_,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[m,[_,w+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[m,[_,"webOS"]],[/crkey\/([\d\.]+)/i],[m,[_,I+"cast"]],[/(cros) [\w]+ ([\w\.]+\w)/i],[[_,"Chromium OS"],m],[/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[_,m],[/(sunos) ?([\w\.\d]*)/i],[[_,"Solaris"],m],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux)/i,/(unix) ?([\w\.]*)/i],[_,m]]},M=(s.VERSION="1.0.2",s.BROWSER=e([_,m,"major"]),s.CPU=e([g]),s.DEVICE=e([f,v,y,r,S,R,b,T,N]),s.ENGINE=s.OS=e([_,m]),typeof z!=l?(z=typeof W!=l&&W.exports?W.exports=s:z).UAParser=s:typeof q===d&&q.amd?q(function(){return s}):typeof n!=l&&(n.UAParser=s),typeof n!=l&&(n.jQuery||n.Zepto));M&&!M.ua&&(i=new s,M.ua=i.getResult(),M.ua.get=function(){return i.getUA()},M.ua.set=function(e){i.setUA(e);var t,r=i.getResult();for(t in r)M.ua[t]=r[t]})}("object"==typeof window?window:this)},{}],43:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});e=(0,e(44).adapterFactory)({window:"undefined"==typeof window?void 0:window});r.default=e},{44:44}],44:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.adapterFactory=function(){var e=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).window,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{shimChrome:!0,shimFirefox:!0,shimEdge:!0,shimSafari:!0},r=a.log,i=a.detectBrowser(e),n={browserDetails:i,commonShim:u,extractVersion:a.extractVersion,disableLog:a.disableLog,disableWarnings:a.disableWarnings};switch(i.browser){case"chrome":if(!o||!o.shimPeerConnection||!t.shimChrome)return r("Chrome shim is not included in this adapter release."),n;if(null===i.version)return r("Chrome shim can not determine version, not shimming."),n;r("adapter.js shimming chrome."),(n.browserShim=o).shimGetUserMedia(e),o.shimMediaStream(e),o.shimPeerConnection(e),o.shimOnTrack(e),o.shimAddTrackRemoveTrack(e),o.shimGetSendersWithDtmf(e),o.shimGetStats(e),o.shimSenderReceiverGetStats(e),o.fixNegotiationNeeded(e),u.shimRTCIceCandidate(e),u.shimConnectionState(e),u.shimMaxMessageSize(e),u.shimSendThrowTypeError(e),u.removeAllowExtmapMixed(e);break;case"firefox":if(!c||!c.shimPeerConnection||!t.shimFirefox)return r("Firefox shim is not included in this adapter release."),n;r("adapter.js shimming firefox."),(n.browserShim=c).shimGetUserMedia(e),c.shimPeerConnection(e),c.shimOnTrack(e),c.shimRemoveStream(e),c.shimSenderGetStats(e),c.shimReceiverGetStats(e),c.shimRTCDataChannel(e),c.shimAddTransceiver(e),c.shimGetParameters(e),c.shimCreateOffer(e),c.shimCreateAnswer(e),u.shimRTCIceCandidate(e),u.shimConnectionState(e),u.shimMaxMessageSize(e),u.shimSendThrowTypeError(e);break;case"edge":if(!s||!s.shimPeerConnection||!t.shimEdge)return r("MS edge shim is not included in this adapter release."),n;r("adapter.js shimming edge."),(n.browserShim=s).shimGetUserMedia(e),s.shimGetDisplayMedia(e),s.shimPeerConnection(e),s.shimReplaceTrack(e),u.shimMaxMessageSize(e),u.shimSendThrowTypeError(e);break;case"safari":if(!l||!t.shimSafari)return r("Safari shim is not included in this adapter release."),n;r("adapter.js shimming safari."),(n.browserShim=l).shimRTCIceServerUrls(e),l.shimCreateOfferLegacy(e),l.shimCallbacksAPI(e),l.shimLocalStreamsAPI(e),l.shimRemoteStreamsAPI(e),l.shimTrackEventTransceiver(e),l.shimGetUserMedia(e),l.shimAudioContext(e),u.shimRTCIceCandidate(e),u.shimMaxMessageSize(e),u.shimSendThrowTypeError(e),u.removeAllowExtmapMixed(e);break;default:r("Unsupported browser!")}return n};var a=i(e(57)),o=i(e(45)),s=i(e(49)),c=i(e(53)),l=i(e(56)),u=i(e(48));function i(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}},{45:45,48:48,49:49,53:53,56:56,57:57}],45:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=r.shimGetUserMedia=void 0;var s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=e(47),n=(Object.defineProperty(r,"shimGetUserMedia",{enumerable:!0,get:function(){return i.shimGetUserMedia}}),e(46));Object.defineProperty(r,"shimGetDisplayMedia",{enumerable:!0,get:function(){return n.shimGetDisplayMedia}}),r.shimMediaStream=function(e){e.MediaStream=e.MediaStream||e.webkitMediaStream},r.shimOnTrack=function(a){{var e;"object"!==(void 0===a?"undefined":s(a))||!a.RTCPeerConnection||"ontrack"in a.RTCPeerConnection.prototype?c.wrapPeerConnectionEvent(a,"track",function(e){return e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e}):(Object.defineProperty(a.RTCPeerConnection.prototype,"ontrack",{get:function(){return this._ontrack},set:function(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0}),e=a.RTCPeerConnection.prototype.setRemoteDescription,a.RTCPeerConnection.prototype.setRemoteDescription=function(){var n=this;return this._ontrackpoly||(this._ontrackpoly=function(i){i.stream.addEventListener("addtrack",function(t){var e=void 0,e=a.RTCPeerConnection.prototype.getReceivers?n.getReceivers().find(function(e){return e.track&&e.track.id===t.track.id}):{track:t.track},r=new Event("track");r.track=t.track,r.receiver=e,r.transceiver={receiver:e},r.streams=[i.stream],n.dispatchEvent(r)}),i.stream.getTracks().forEach(function(t){var e=void 0,e=a.RTCPeerConnection.prototype.getReceivers?n.getReceivers().find(function(e){return e.track&&e.track.id===t.id}):{track:t},r=new Event("track");r.track=t,r.receiver=e,r.transceiver={receiver:e},r.streams=[i.stream],n.dispatchEvent(r)})},this.addEventListener("addstream",this._ontrackpoly)),e.apply(this,arguments)})}},r.shimGetSendersWithDtmf=function(e){{var i,n,t,r,a,o;"object"===(void 0===e?"undefined":s(e))&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype?(i=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}},e.RTCPeerConnection.prototype.getSenders||(e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()},n=e.RTCPeerConnection.prototype.addTrack,e.RTCPeerConnection.prototype.addTrack=function(e,t){var r=n.apply(this,arguments);return r||(r=i(this,e),this._senders.push(r)),r},t=e.RTCPeerConnection.prototype.removeTrack,e.RTCPeerConnection.prototype.removeTrack=function(e){t.apply(this,arguments);e=this._senders.indexOf(e);-1!==e&&this._senders.splice(e,1)}),r=e.RTCPeerConnection.prototype.addStream,e.RTCPeerConnection.prototype.addStream=function(e){var t=this;this._senders=this._senders||[],r.apply(this,[e]),e.getTracks().forEach(function(e){t._senders.push(i(t,e))})},a=e.RTCPeerConnection.prototype.removeStream,e.RTCPeerConnection.prototype.removeStream=function(e){var r=this;this._senders=this._senders||[],a.apply(this,[e]),e.getTracks().forEach(function(t){var e=r._senders.find(function(e){return e.track===t});e&&r._senders.splice(r._senders.indexOf(e),1)})}):"object"===(void 0===e?"undefined":s(e))&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)&&(o=e.RTCPeerConnection.prototype.getSenders,e.RTCPeerConnection.prototype.getSenders=function(){var t=this,e=o.apply(this,[]);return e.forEach(function(e){return e._pc=t}),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get:function(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}}))}},r.shimGetStats=function(e){var o;e.RTCPeerConnection&&(o=e.RTCPeerConnection.prototype.getStats,e.RTCPeerConnection.prototype.getStats=function(){var r,i,n=this,e=Array.prototype.slice.call(arguments),t=e[0],a=e[1],e=e[2];return 0<arguments.length&&"function"==typeof t?o.apply(this,arguments):0!==o.length||0!==arguments.length&&"function"==typeof t?(r=function(e){var i={};return e.result().forEach(function(t){var r={id:t.id,timestamp:t.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[t.type]||t.type};t.names().forEach(function(e){r[e]=t.stat(e)}),i[r.id]=r}),i},i=function(t){return new Map(Object.keys(t).map(function(e){return[e,t[e]]}))},2<=arguments.length?o.apply(this,[function(e){a(i(r(e)))},t]):new Promise(function(t,e){o.apply(n,[function(e){t(i(r(e)))},e])}).then(a,e)):o.apply(this,[])})},r.shimSenderReceiverGetStats=function(e){var r,t,i,a;"object"===(void 0===e?"undefined":s(e))&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver&&("getStats"in e.RTCRtpSender.prototype||((r=e.RTCPeerConnection.prototype.getSenders)&&(e.RTCPeerConnection.prototype.getSenders=function(){var t=this,e=r.apply(this,[]);return e.forEach(function(e){return e._pc=t}),e}),(t=e.RTCPeerConnection.prototype.addTrack)&&(e.RTCPeerConnection.prototype.addTrack=function(){var e=t.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){var t=this;return this._pc.getStats().then(function(e){return c.filterStats(e,t.track,!0)})}),"getStats"in e.RTCRtpReceiver.prototype||((i=e.RTCPeerConnection.prototype.getReceivers)&&(e.RTCPeerConnection.prototype.getReceivers=function(){var t=this,e=i.apply(this,[]);return e.forEach(function(e){return e._pc=t}),e}),c.wrapPeerConnectionEvent(e,"track",function(e){return e.receiver._pc=e.srcElement,e}),e.RTCRtpReceiver.prototype.getStats=function(){var t=this;return this._pc.getStats().then(function(e){return c.filterStats(e,t.track,!1)})}),"getStats"in e.RTCRtpSender.prototype)&&"getStats"in e.RTCRtpReceiver.prototype&&(a=e.RTCPeerConnection.prototype.getStats,e.RTCPeerConnection.prototype.getStats=function(){var t,r,i,n;return 0<arguments.length&&arguments[0]instanceof e.MediaStreamTrack?(t=arguments[0],n=i=r=void 0,this.getSenders().forEach(function(e){e.track===t&&(r?n=!0:r=e)}),this.getReceivers().forEach(function(e){return e.track===t&&(i?n=!0:i=e),e.track===t}),n||r&&i?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):r?r.getStats():i?i.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))):a.apply(this,arguments)})},r.shimAddTrackRemoveTrackWithNative=u,r.shimAddTrackRemoveTrack=function(n){if(n.RTCPeerConnection){var e=c.detectBrowser(n);if(n.RTCPeerConnection.prototype.addTrack&&65<=e.version)return u(n);var r=n.RTCPeerConnection.prototype.getLocalStreams,i=(n.RTCPeerConnection.prototype.getLocalStreams=function(){var t=this,e=r.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map(function(e){return t._reverseStreams[e.id]})},n.RTCPeerConnection.prototype.addStream),t=(n.RTCPeerConnection.prototype.addStream=function(e){var t,r=this;this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},e.getTracks().forEach(function(t){if(r.getSenders().find(function(e){return e.track===t}))throw new DOMException("Track already exists.","InvalidAccessError")}),this._reverseStreams[e.id]||(t=new n.MediaStream(e.getTracks()),this._streams[e.id]=t,this._reverseStreams[t.id]=e,e=t),i.apply(this,[e])},n.RTCPeerConnection.prototype.removeStream),a=(n.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[(this._streams[e.id]||e).id],delete this._streams[e.id]},n.RTCPeerConnection.prototype.addTrack=function(t,e){var r=this;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");var i=[].slice.call(arguments,1);if(1!==i.length||!i[0].getTracks().find(function(e){return e===t}))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(function(e){return e.track===t}))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};var i=this._streams[e.id];return i?(i.addTrack(t),Promise.resolve().then(function(){r.dispatchEvent(new Event("negotiationneeded"))})):(i=new n.MediaStream([t]),this._streams[e.id]=i,this._reverseStreams[i.id]=e,this.addStream(i)),this.getSenders().find(function(e){return e.track===t})},["createOffer","createAnswer"].forEach(function(e){var i=n.RTCPeerConnection.prototype[e],t=l({},e,function(){var t=this,r=arguments;return arguments.length&&"function"==typeof arguments[0]?i.apply(this,[function(e){e=s(t,e);r[0].apply(null,[e])},function(e){r[1]&&r[1].apply(null,e)},arguments[2]]):i.apply(this,arguments).then(function(e){return s(t,e)})});n.RTCPeerConnection.prototype[e]=t[e]}),n.RTCPeerConnection.prototype.setLocalDescription),o=(n.RTCPeerConnection.prototype.setLocalDescription=function(){var r,e,i;return arguments.length&&arguments[0].type&&(arguments[0]=(r=this,i=(e=arguments[0]).sdp,Object.keys(r._reverseStreams||[]).forEach(function(e){var e=r._reverseStreams[e],t=r._streams[e.id];i=i.replace(new RegExp(e.id,"g"),t.id)}),new RTCSessionDescription({type:e.type,sdp:i}))),a.apply(this,arguments)},Object.getOwnPropertyDescriptor(n.RTCPeerConnection.prototype,"localDescription"));Object.defineProperty(n.RTCPeerConnection.prototype,"localDescription",{get:function(){var e=o.get.apply(this);return""===e.type?e:s(this,e)}}),n.RTCPeerConnection.prototype.removeTrack=function(t){var r=this;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!t._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(t._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};var i=void 0;Object.keys(this._streams).forEach(function(e){r._streams[e].getTracks().find(function(e){return t.track===e})&&(i=r._streams[e])}),i&&(1===i.getTracks().length?this.removeStream(this._reverseStreams[i.id]):i.removeTrack(t.track),this.dispatchEvent(new Event("negotiationneeded")))}}function s(r,e){var i=e.sdp;return Object.keys(r._reverseStreams||[]).forEach(function(e){var e=r._reverseStreams[e],t=r._streams[e.id];i=i.replace(new RegExp(t.id,"g"),e.id)}),new RTCSessionDescription({type:e.type,sdp:i})}},r.shimPeerConnection=function(i){var e,t,r=c.detectBrowser(i);!i.RTCPeerConnection&&i.webkitRTCPeerConnection&&(i.RTCPeerConnection=i.webkitRTCPeerConnection);i.RTCPeerConnection&&(e=0===i.RTCPeerConnection.prototype.addIceCandidate.length,r.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(e){var t=i.RTCPeerConnection.prototype[e],r=l({},e,function(){return arguments[0]=new("addIceCandidate"===e?i.RTCIceCandidate:i.RTCSessionDescription)(arguments[0]),t.apply(this,arguments)});i.RTCPeerConnection.prototype[e]=r[e]}),t=i.RTCPeerConnection.prototype.addIceCandidate,i.RTCPeerConnection.prototype.addIceCandidate=function(){return e||arguments[0]?r.version<78&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():t.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})},r.fixNegotiationNeeded=function(e){var r=c.detectBrowser(e);c.wrapPeerConnectionEvent(e,"negotiationneeded",function(e){var t=e.target;if(!(r.version<72||t.getConfiguration&&"plan-b"===t.getConfiguration().sdpSemantics)||"stable"===t.signalingState)return e})};var c=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e(57));function l(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){var t=this;return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(function(e){return t._shimmedLocalStreams[e][0]})};var i=e.RTCPeerConnection.prototype.addTrack,n=(e.RTCPeerConnection.prototype.addTrack=function(e,t){if(!t)return i.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};var r=i.apply(this,arguments);return this._shimmedLocalStreams[t.id]?-1===this._shimmedLocalStreams[t.id].indexOf(r)&&this._shimmedLocalStreams[t.id].push(r):this._shimmedLocalStreams[t.id]=[t,r],r},e.RTCPeerConnection.prototype.addStream),t=(e.RTCPeerConnection.prototype.addStream=function(e){var r=this,t=(this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach(function(t){if(r.getSenders().find(function(e){return e.track===t}))throw new DOMException("Track already exists.","InvalidAccessError")}),this.getSenders()),i=(n.apply(this,arguments),this.getSenders().filter(function(e){return-1===t.indexOf(e)}));this._shimmedLocalStreams[e.id]=[e].concat(i)},e.RTCPeerConnection.prototype.removeStream),a=(e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],t.apply(this,arguments)},e.RTCPeerConnection.prototype.removeTrack);e.RTCPeerConnection.prototype.removeTrack=function(r){var i=this;return this._shimmedLocalStreams=this._shimmedLocalStreams||{},r&&Object.keys(this._shimmedLocalStreams).forEach(function(e){var t=i._shimmedLocalStreams[e].indexOf(r);-1!==t&&i._shimmedLocalStreams[e].splice(t,1),1===i._shimmedLocalStreams[e].length&&delete i._shimmedLocalStreams[e]}),a.apply(this,arguments)}}},{46:46,47:47,57:57}],46:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=function(a,e){a.navigator.mediaDevices&&"getDisplayMedia"in a.navigator.mediaDevices||a.navigator.mediaDevices&&"function"==typeof e&&(a.navigator.mediaDevices.getDisplayMedia=function(n){return e(n).then(function(e){var t=n.video&&n.video.width,r=n.video&&n.video.height,i=n.video&&n.video.frameRate;return n.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:e,maxFrameRate:i||3}},t&&(n.video.mandatory.maxWidth=t),r&&(n.video.mandatory.maxHeight=r),a.navigator.mediaDevices.getUserMedia(n)})})}},{}],47:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r.shimGetUserMedia=function(e){var t,o,i,n,r,s=e&&e.navigator;s.mediaDevices&&(t=a.detectBrowser(e),o=function(n){var a;return"object"!==(void 0===n?"undefined":c(n))||n.mandatory||n.optional?n:(a={},Object.keys(n).forEach(function(t){var r,i,e;"require"!==t&&"advanced"!==t&&"mediaSource"!==t&&(void 0!==(r="object"===c(n[t])?n[t]:{ideal:n[t]}).exact&&"number"==typeof r.exact&&(r.min=r.max=r.exact),i=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t},void 0!==r.ideal&&(a.optional=a.optional||[],e={},"number"==typeof r.ideal?(e[i("min",t)]=r.ideal,a.optional.push(e),(e={})[i("max",t)]=r.ideal):e[i("",t)]=r.ideal,a.optional.push(e)),void 0!==r.exact&&"number"!=typeof r.exact?(a.mandatory=a.mandatory||{},a.mandatory[i("",t)]=r.exact):["min","max"].forEach(function(e){void 0!==r[e]&&(a.mandatory=a.mandatory||{},a.mandatory[i(e,t)]=r[e])}))}),n.advanced&&(a.optional=(a.optional||[]).concat(n.advanced)),a)},i=function(r,i){if(!(61<=t.version)){if((r=JSON.parse(JSON.stringify(r)))&&"object"===c(r.audio)&&((e=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])})((r=JSON.parse(JSON.stringify(r))).audio,"autoGainControl","googAutoGainControl"),e(r.audio,"noiseSuppression","googNoiseSuppression"),r.audio=o(r.audio)),r&&"object"===c(r.video)){var n=(n=r.video.facingMode)&&("object"===(void 0===n?"undefined":c(n))?n:{ideal:n}),e=t.version<66;if(n&&("user"===n.exact||"environment"===n.exact||"user"===n.ideal||"environment"===n.ideal)&&(!s.mediaDevices.getSupportedConstraints||!s.mediaDevices.getSupportedConstraints().facingMode||e)){delete r.video.facingMode;var a=void 0;if("environment"===n.exact||"environment"===n.ideal?a=["back","rear"]:"user"!==n.exact&&"user"!==n.ideal||(a=["front"]),a)return s.mediaDevices.enumerateDevices().then(function(e){var t=(e=e.filter(function(e){return"videoinput"===e.kind})).find(function(t){return a.some(function(e){return t.label.toLowerCase().includes(e)})});return(t=!t&&e.length&&a.includes("back")?e[e.length-1]:t)&&(r.video.deviceId=n.exact?{exact:t.deviceId}:{ideal:t.deviceId}),r.video=o(r.video),l("chrome: "+JSON.stringify(r)),i(r)})}r.video=o(r.video)}l("chrome: "+JSON.stringify(r))}return i(r)},n=function(e){return 64<=t.version?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString:function(){return this.name+(this.message&&": ")+this.message}}},s.getUserMedia=function(e,t,r){i(e,function(e){s.webkitGetUserMedia(e,t,function(e){r&&r(n(e))})})}.bind(s),s.mediaDevices.getUserMedia)&&(r=s.mediaDevices.getUserMedia.bind(s.mediaDevices),s.mediaDevices.getUserMedia=function(e){return i(e,function(t){return r(t).then(function(e){if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach(function(e){e.stop()}),new DOMException("","NotFoundError");return e},function(e){return Promise.reject(n(e))})})})};var a=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e(57));var l=a.log},{57:57}],48:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r=(r.shimRTCIceCandidate=function(t){var n;!t.RTCIceCandidate||t.RTCIceCandidate&&"foundation"in t.RTCIceCandidate.prototype||(n=t.RTCIceCandidate,t.RTCIceCandidate=function(e){var t,r,i;return"object"===(void 0===e?"undefined":a(e))&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substr(2)),e.candidate&&e.candidate.length?(t=new n(e),r=s.default.parseCandidate(e.candidate),(i=Object.assign(t,r)).toJSON=function(){return{candidate:i.candidate,sdpMid:i.sdpMid,sdpMLineIndex:i.sdpMLineIndex,usernameFragment:i.usernameFragment}},i):new n(e)},t.RTCIceCandidate.prototype=n.prototype,i.wrapPeerConnectionEvent(t,"icecandidate",function(e){return e.candidate&&Object.defineProperty(e,"candidate",{value:new t.RTCIceCandidate(e.candidate),writable:"false"}),e}))},r.shimMaxMessageSize=function(e){var a,o;e.RTCPeerConnection&&(a=i.detectBrowser(e),"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get:function(){return void 0===this._sctp?null:this._sctp}}),o=e.RTCPeerConnection.prototype.setRemoteDescription,e.RTCPeerConnection.prototype.setRemoteDescription=function(){var e,t,r,i,n;return this._sctp=null,"chrome"===a.browser&&76<=a.version&&"plan-b"===this.getConfiguration().sdpSemantics&&Object.defineProperty(this,"sctp",{get:function(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0}),(n=arguments[0])&&n.sdp&&((n=s.default.splitSections(n.sdp)).shift(),n.some(function(e){e=s.default.parseMLine(e);return e&&"application"===e.kind&&-1!==e.protocol.indexOf("SCTP")}))&&(n=null===(n=(n=arguments[0]).sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/))||n.length<2||(n=parseInt(n[1],10))!=n?-1:n,r=n,i=65536,r=i="firefox"===a.browser?a.version<57?-1===r?16384:2147483637:a.version<60?57===a.version?65535:65536:2147483637:i,i=arguments[0],n=n,t=65536,"firefox"===a.browser&&57===a.version&&(t=65535),0<(i=s.default.matchPrefix(i.sdp,"a=max-message-size:")).length?t=parseInt(i[0].substr(19),10):"firefox"===a.browser&&-1!==n&&(t=2147483637),i=t,e=void 0,e=0===r&&0===i?Number.POSITIVE_INFINITY:0===r||0===i?Math.max(r,i):Math.min(r,i),n={},Object.defineProperty(n,"maxMessageSize",{get:function(){return e}}),this._sctp=n),o.apply(this,arguments)})},r.shimSendThrowTypeError=function(e){var t;function r(t,r){var i=t.send;t.send=function(){var e=arguments[0],e=e.length||e.size||e.byteLength;if("open"===t.readyState&&r.sctp&&e>r.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+r.sctp.maxMessageSize+" bytes)");return i.apply(t,arguments)}}e.RTCPeerConnection&&"createDataChannel"in e.RTCPeerConnection.prototype&&(t=e.RTCPeerConnection.prototype.createDataChannel,e.RTCPeerConnection.prototype.createDataChannel=function(){var e=t.apply(this,arguments);return r(e,this),e},i.wrapPeerConnectionEvent(e,"datachannel",function(e){return r(e.channel,e.target),e}))},r.shimConnectionState=function(e){var r;!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype||(r=e.RTCPeerConnection.prototype,Object.defineProperty(r,"connectionState",{get:function(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(r,"onconnectionstatechange",{get:function(){return this._onconnectionstatechange||null},set:function(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(function(e){var t=r[e];r[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=function(e){var t,r=e.target;return r._lastConnectionState!==r.connectionState&&(r._lastConnectionState=r.connectionState,t=new Event("connectionstatechange",e),r.dispatchEvent(t)),e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),t.apply(this,arguments)}}))},r.removeAllowExtmapMixed=function(e){var t,r;!e.RTCPeerConnection||"chrome"===(t=i.detectBrowser(e)).browser&&71<=t.version||"safari"===t.browser&&605<=t.version||(r=e.RTCPeerConnection.prototype.setRemoteDescription,e.RTCPeerConnection.prototype.setRemoteDescription=function(e){return e&&e.sdp&&-1!==e.sdp.indexOf("\na=extmap-allow-mixed")&&(e.sdp=e.sdp.split("\n").filter(function(e){return"a=extmap-allow-mixed"!==e.trim()}).join("\n")),r.apply(this,arguments)})},e(41)),s=(r=r)&&r.__esModule?r:{default:r},i=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e(57))},{41:41,57:57}],49:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=r.shimGetUserMedia=void 0;var i=e(52),n=(Object.defineProperty(r,"shimGetUserMedia",{enumerable:!0,get:function(){return i.shimGetUserMedia}}),e(51));Object.defineProperty(r,"shimGetDisplayMedia",{enumerable:!0,get:function(){return n.shimGetDisplayMedia}}),r.shimPeerConnection=function(e){var t=a.detectBrowser(e);{var r;e.RTCIceGatherer&&(e.RTCIceCandidate||(e.RTCIceCandidate=function(e){return e}),e.RTCSessionDescription||(e.RTCSessionDescription=function(e){return e}),t.version<15025)&&(r=Object.getOwnPropertyDescriptor(e.MediaStreamTrack.prototype,"enabled"),Object.defineProperty(e.MediaStreamTrack.prototype,"enabled",{set:function(e){r.set.call(this,e);var t=new Event("enabled");t.enabled=e,this.dispatchEvent(t)}}))}!e.RTCRtpSender||"dtmf"in e.RTCRtpSender.prototype||Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get:function(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=new e.RTCDtmfSender(this):"video"===this.track.kind&&(this._dtmf=null)),this._dtmf}});e.RTCDtmfSender&&!e.RTCDTMFSender&&(e.RTCDTMFSender=e.RTCDtmfSender);var i=(0,s.default)(e,t.version);e.RTCPeerConnection=function(e){return e&&e.iceServers&&(e.iceServers=(0,o.filterIceServers)(e.iceServers,t.version),a.log("ICE servers after filtering:",e.iceServers)),new i(e)},e.RTCPeerConnection.prototype=i.prototype},r.shimReplaceTrack=function(e){!e.RTCRtpSender||"replaceTrack"in e.RTCRtpSender.prototype||(e.RTCRtpSender.prototype.replaceTrack=e.RTCRtpSender.prototype.setTrack)};var a=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e(57)),o=e(50),r=e(40),s=(e=r)&&e.__esModule?e:{default:e}},{40:40,50:50,51:51,52:52,57:57}],50:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.filterIceServers=function(e,t){var i=!1;return(e=JSON.parse(JSON.stringify(e))).filter(function(e){var t,r;if(e&&(e.urls||e.url))return r=e.urls||e.url,e.url&&!e.urls&&n.deprecated("RTCIceServer.url","RTCIceServer.urls"),r=(r=(t="string"==typeof r)?[r]:r).filter(function(e){return 0!==e.indexOf("stun:")&&e.startsWith("turn")&&!e.startsWith("turn:[")&&e.includes("transport=udp")&&!i&&(i=!0)}),delete e.url,e.urls=t?r[0]:r,!!r.length})};var n=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e(57))},{57:57}],51:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=function(e){"getDisplayMedia"in e.navigator&&(!e.navigator.mediaDevices||e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||(e.navigator.mediaDevices.getDisplayMedia=e.navigator.getDisplayMedia.bind(e.navigator)))}},{}],52:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetUserMedia=function(e){var e=e&&e.navigator,t=e.mediaDevices.getUserMedia.bind(e.mediaDevices);e.mediaDevices.getUserMedia=function(e){return t(e).catch(function(e){return Promise.reject({name:{PermissionDeniedError:"NotAllowedError"}[(e=e).name]||e.name,message:e.message,constraint:e.constraint,toString:function(){return this.name}})})}}},{}],53:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=r.shimGetUserMedia=void 0;var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=e(55),n=(Object.defineProperty(r,"shimGetUserMedia",{enumerable:!0,get:function(){return i.shimGetUserMedia}}),e(54));Object.defineProperty(r,"shimGetDisplayMedia",{enumerable:!0,get:function(){return n.shimGetDisplayMedia}}),r.shimOnTrack=function(e){"object"===(void 0===e?"undefined":o(e))&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})},r.shimPeerConnection=function(i){var e,n,r,a=s.detectBrowser(i);"object"===(void 0===i?"undefined":o(i))&&(i.RTCPeerConnection||i.mozRTCPeerConnection)&&(!i.RTCPeerConnection&&i.mozRTCPeerConnection&&(i.RTCPeerConnection=i.mozRTCPeerConnection),a.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(e){var t=i.RTCPeerConnection.prototype[e],r=function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r;return e}({},e,function(){return arguments[0]=new("addIceCandidate"===e?i.RTCIceCandidate:i.RTCSessionDescription)(arguments[0]),t.apply(this,arguments)});i.RTCPeerConnection.prototype[e]=r[e]}),a.version<68&&(e=i.RTCPeerConnection.prototype.addIceCandidate,i.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?arguments[0]&&""===arguments[0].candidate?Promise.resolve():e.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())}),n={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},r=i.RTCPeerConnection.prototype.getStats,i.RTCPeerConnection.prototype.getStats=function(){var e=Array.prototype.slice.call(arguments),t=e[0],i=e[1],e=e[2];return r.apply(this,[t||null]).then(function(r){if(a.version<53&&!i)try{r.forEach(function(e){e.type=n[e.type]||e.type})}catch(e){if("TypeError"!==e.name)throw e;r.forEach(function(e,t){r.set(t,Object.assign({},e,{type:n[e.type]||e.type}))})}return r}).then(i,e)})},r.shimSenderGetStats=function(e){var r,t;"object"===(void 0===e?"undefined":o(e))&&e.RTCPeerConnection&&e.RTCRtpSender&&(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype||((r=e.RTCPeerConnection.prototype.getSenders)&&(e.RTCPeerConnection.prototype.getSenders=function(){var t=this,e=r.apply(this,[]);return e.forEach(function(e){return e._pc=t}),e}),(t=e.RTCPeerConnection.prototype.addTrack)&&(e.RTCPeerConnection.prototype.addTrack=function(){var e=t.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}))},r.shimReceiverGetStats=function(e){var r;"object"===(void 0===e?"undefined":o(e))&&e.RTCPeerConnection&&e.RTCRtpSender&&(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype||((r=e.RTCPeerConnection.prototype.getReceivers)&&(e.RTCPeerConnection.prototype.getReceivers=function(){var t=this,e=r.apply(this,[]);return e.forEach(function(e){return e._pc=t}),e}),s.wrapPeerConnectionEvent(e,"track",function(e){return e.receiver._pc=e.srcElement,e}),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}))},r.shimRemoveStream=function(e){!e.RTCPeerConnection||"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(t){var r=this;s.deprecated("removeStream","removeTrack"),this.getSenders().forEach(function(e){e.track&&t.getTracks().includes(e.track)&&r.removeTrack(e)})})},r.shimRTCDataChannel=function(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)},r.shimAddTransceiver=function(e){var n;"object"===(void 0===e?"undefined":o(e))&&e.RTCPeerConnection&&(n=e.RTCPeerConnection.prototype.addTransceiver)&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];var e,t=arguments[1],r=t&&"sendEncodings"in t,i=(r&&t.sendEncodings.forEach(function(e){if("rid"in e)if(!/^[a-z0-9]{0,16}$/i.test(e.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in e&&!(1<=parseFloat(e.scaleResolutionDownBy)))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(0<=parseFloat(e.maxFramerate)))throw new RangeError("max_framerate must be >= 0.0")}),n.apply(this,arguments));return!r||"encodings"in(r=(e=i.sender).getParameters())&&(1!==r.encodings.length||0!==Object.keys(r.encodings[0]).length)||(r.encodings=t.sendEncodings,e.sendEncodings=t.sendEncodings,this.setParametersPromises.push(e.setParameters(r).then(function(){delete e.sendEncodings}).catch(function(){delete e.sendEncodings}))),i})},r.shimGetParameters=function(e){var t;"object"===(void 0===e?"undefined":o(e))&&e.RTCRtpSender&&(t=e.RTCRtpSender.prototype.getParameters)&&(e.RTCRtpSender.prototype.getParameters=function(){var e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})},r.shimCreateOffer=function(e){var r;"object"===(void 0===e?"undefined":o(e))&&e.RTCPeerConnection&&(r=e.RTCPeerConnection.prototype.createOffer,e.RTCPeerConnection.prototype.createOffer=function(){var e=this,t=arguments;return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(function(){return r.apply(e,t)}).finally(function(){e.setParametersPromises=[]}):r.apply(this,arguments)})},r.shimCreateAnswer=function(e){var r;"object"===(void 0===e?"undefined":o(e))&&e.RTCPeerConnection&&(r=e.RTCPeerConnection.prototype.createAnswer,e.RTCPeerConnection.prototype.createAnswer=function(){var e=this,t=arguments;return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(function(){return r.apply(e,t)}).finally(function(){e.setParametersPromises=[]}):r.apply(this,arguments)})};var s=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e(57))},{54:54,55:55,57:57}],54:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=function(t,r){t.navigator.mediaDevices&&"getDisplayMedia"in t.navigator.mediaDevices||t.navigator.mediaDevices&&(t.navigator.mediaDevices.getDisplayMedia=function(e){return e&&e.video?(!0===e.video?e.video={mediaSource:r}:e.video.mediaSource=r,t.navigator.mediaDevices.getUserMedia(e)):((e=new DOMException("getDisplayMedia without video constraints is undefined")).name="NotFoundError",e.code=8,Promise.reject(e))})}},{}],55:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r.shimGetUserMedia=function(e){var t=c.detectBrowser(e),i=e&&e.navigator,e=e&&e.MediaStreamTrack;{var r,n,a,o;i.getUserMedia=function(e,t,r){c.deprecated("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),i.mediaDevices.getUserMedia(e).then(t,r)},55<t.version&&"autoGainControl"in i.mediaDevices.getSupportedConstraints()||(r=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])},n=i.mediaDevices.getUserMedia.bind(i.mediaDevices),i.mediaDevices.getUserMedia=function(e){return"object"===(void 0===e?"undefined":s(e))&&"object"===s(e.audio)&&(e=JSON.parse(JSON.stringify(e)),r(e.audio,"autoGainControl","mozAutoGainControl"),r(e.audio,"noiseSuppression","mozNoiseSuppression")),n(e)},e&&e.prototype.getSettings&&(a=e.prototype.getSettings,e.prototype.getSettings=function(){var e=a.apply(this,arguments);return r(e,"mozAutoGainControl","autoGainControl"),r(e,"mozNoiseSuppression","noiseSuppression"),e}),e&&e.prototype.applyConstraints&&(o=e.prototype.applyConstraints,e.prototype.applyConstraints=function(e){return"audio"===this.kind&&"object"===(void 0===e?"undefined":s(e))&&(e=JSON.parse(JSON.stringify(e)),r(e,"autoGainControl","mozAutoGainControl"),r(e,"noiseSuppression","mozNoiseSuppression")),o.apply(this,[e])}))}};var c=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e(57))},{57:57}],56:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r.shimLocalStreamsAPI=function(e){var a;"object"===(void 0===e?"undefined":c(e))&&e.RTCPeerConnection&&("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),"addStream"in e.RTCPeerConnection.prototype||(a=e.RTCPeerConnection.prototype.addTrack,e.RTCPeerConnection.prototype.addStream=function(t){var r=this;this._localStreams||(this._localStreams=[]),this._localStreams.includes(t)||this._localStreams.push(t),t.getAudioTracks().forEach(function(e){return a.call(r,e,t)}),t.getVideoTracks().forEach(function(e){return a.call(r,e,t)})},e.RTCPeerConnection.prototype.addTrack=function(e){for(var t=this,r=arguments.length,i=Array(1<r?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];return i&&i.forEach(function(e){t._localStreams?t._localStreams.includes(e)||t._localStreams.push(e):t._localStreams=[e]}),a.apply(this,arguments)}),"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){var t,r=this,i=(this._localStreams||(this._localStreams=[]),this._localStreams.indexOf(e));-1!==i&&(this._localStreams.splice(i,1),t=e.getTracks(),this.getSenders().forEach(function(e){t.includes(e.track)&&r.removeTrack(e)}))}))},r.shimRemoteStreamsAPI=function(e){var t;"object"===(void 0===e?"undefined":c(e))&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams||[]}),"onaddstream"in e.RTCPeerConnection.prototype||(Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get:function(){return this._onaddstream},set:function(e){var r=this;this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=function(e){e.streams.forEach(function(e){var t;r._remoteStreams||(r._remoteStreams=[]),r._remoteStreams.includes(e)||(r._remoteStreams.push(e),(t=new Event("addstream")).stream=e,r.dispatchEvent(t))})})}}),t=e.RTCPeerConnection.prototype.setRemoteDescription,e.RTCPeerConnection.prototype.setRemoteDescription=function(){var r=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(e){e.streams.forEach(function(e){var t;r._remoteStreams||(r._remoteStreams=[]),0<=r._remoteStreams.indexOf(e)||(r._remoteStreams.push(e),(t=new Event("addstream")).stream=e,r.dispatchEvent(t))})}),t.apply(r,arguments)}))},r.shimCallbacksAPI=function(e){var i,n,a,o,s,t;"object"===(void 0===e?"undefined":c(e))&&e.RTCPeerConnection&&(e=e.RTCPeerConnection.prototype,i=e.createOffer,n=e.createAnswer,a=e.setLocalDescription,o=e.setRemoteDescription,s=e.addIceCandidate,e.createOffer=function(e,t){var r=i.apply(this,[2<=arguments.length?arguments[2]:e]);return t?(r.then(e,t),Promise.resolve()):r},e.createAnswer=function(e,t){var r=n.apply(this,[2<=arguments.length?arguments[2]:e]);return t?(r.then(e,t),Promise.resolve()):r},t=function(e,t,r){e=a.apply(this,[e]);return r?(e.then(t,r),Promise.resolve()):e},e.setLocalDescription=t,t=function(e,t,r){e=o.apply(this,[e]);return r?(e.then(t,r),Promise.resolve()):e},e.setRemoteDescription=t,t=function(e,t,r){e=s.apply(this,[e]);return r?(e.then(t,r),Promise.resolve()):e},e.addIceCandidate=t)},r.shimGetUserMedia=function(e){var i=e&&e.navigator;{var t;i.mediaDevices&&i.mediaDevices.getUserMedia&&(e=i.mediaDevices,t=e.getUserMedia.bind(e),i.mediaDevices.getUserMedia=function(e){return t(n(e))})}!i.getUserMedia&&i.mediaDevices&&i.mediaDevices.getUserMedia&&(i.getUserMedia=function(e,t,r){i.mediaDevices.getUserMedia(e).then(t,r)}.bind(i))},r.shimConstraints=n,r.shimRTCIceServerUrls=function(e){var a;e.RTCPeerConnection&&(a=e.RTCPeerConnection,e.RTCPeerConnection=function(e,t){if(e&&e.iceServers){for(var r=[],i=0;i<e.iceServers.length;i++){var n=e.iceServers[i];!n.hasOwnProperty("urls")&&n.hasOwnProperty("url")?(o.deprecated("RTCIceServer.url","RTCIceServer.urls"),(n=JSON.parse(JSON.stringify(n))).urls=n.url,delete n.url,r.push(n)):r.push(e.iceServers[i])}e.iceServers=r}return new a(e,t)},e.RTCPeerConnection.prototype=a.prototype,"generateCertificate"in a)&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:function(){return a.generateCertificate}})},r.shimTrackEventTransceiver=function(e){"object"===(void 0===e?"undefined":c(e))&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})},r.shimCreateOfferLegacy=function(e){var r=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){var t;return e&&(void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio),t=this.getTransceivers().find(function(e){return"audio"===e.receiver.track.kind}),!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio"),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo),t=this.getTransceivers().find(function(e){return"video"===e.receiver.track.kind}),!1===e.offerToReceiveVideo&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveVideo||t||this.addTransceiver("video")),r.apply(this,arguments)}},r.shimAudioContext=function(e){"object"!==(void 0===e?"undefined":c(e))||e.AudioContext||(e.AudioContext=e.webkitAudioContext)};var o=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e(57));function n(e){return e&&void 0!==e.video?Object.assign({},e,{video:o.compactObject(e.video)}):e}},{57:57}],57:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r.extractVersion=n,r.wrapPeerConnectionEvent=function(e,i,n){var a,o;e.RTCPeerConnection&&(e=e.RTCPeerConnection.prototype,a=e.addEventListener,e.addEventListener=function(e,t){var r;return e!==i?a.apply(this,arguments):(r=function(e){e=n(e);e&&(t.handleEvent?t.handleEvent(e):t(e))},this._eventMap=this._eventMap||{},this._eventMap[i]||(this._eventMap[i]=new Map),this._eventMap[i].set(t,r),a.apply(this,[e,r]))},o=e.removeEventListener,e.removeEventListener=function(e,t){var r;return e===i&&this._eventMap&&this._eventMap[i]&&this._eventMap[i].has(t)?(r=this._eventMap[i].get(t),this._eventMap[i].delete(t),0===this._eventMap[i].size&&delete this._eventMap[i],0===Object.keys(this._eventMap).length&&delete this._eventMap,o.apply(this,[e,r])):o.apply(this,arguments)},Object.defineProperty(e,"on"+i,{get:function(){return this["_on"+i]},set:function(e){this["_on"+i]&&(this.removeEventListener(i,this["_on"+i]),delete this["_on"+i]),e&&this.addEventListener(i,this["_on"+i]=e)},enumerable:!0,configurable:!0}))},r.disableLog=function(e){return"boolean"==typeof e?e?"adapter.js logging disabled":"adapter.js logging enabled":new Error("Argument type: "+(void 0===e?"undefined":i(e))+". Please use a boolean.")},r.disableWarnings=function(e){return"boolean"==typeof e?"adapter.js deprecation warnings "+(e?"disabled":"enabled"):new Error("Argument type: "+(void 0===e?"undefined":i(e))+". Please use a boolean.")},r.log=function(){"undefined"==typeof window||i(window)},r.deprecated=function(e,t){},r.detectBrowser=function(e){var t,r={browser:null,version:null};return void 0!==e&&e.navigator?(t=e.navigator).mozGetUserMedia?(r.browser="firefox",r.version=n(t.userAgent,/Firefox\/(\d+)\./,1)):t.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection&&!e.RTCIceGatherer?(r.browser="chrome",r.version=n(t.userAgent,/Chrom(e|ium)\/(\d+)\./,2)):t.mediaDevices&&t.userAgent.match(/Edge\/(\d+).(\d+)$/)?(r.browser="edge",r.version=n(t.userAgent,/Edge\/(\d+).(\d+)$/,2)):e.RTCPeerConnection&&t.userAgent.match(/AppleWebKit\/(\d+)\./)?(r.browser="safari",r.version=n(t.userAgent,/AppleWebKit\/(\d+)\./,1),r.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype):r.browser="Not a supported browser.":r.browser="Not a browser.",r},r.compactObject=function n(a){if(!s(a))return a;return Object.keys(a).reduce(function(e,t){var r=s(a[t]),i=r?n(a[t]):a[t],r=r&&!Object.keys(i).length;return void 0===i||r?e:Object.assign(e,o({},t,i))},{})},r.walkStats=c,r.filterStats=function(r,t,e){var i,n=e?"outbound-rtp":"inbound-rtp",a=new Map;return null!==t&&(i=[],r.forEach(function(e){"track"===e.type&&e.trackIdentifier===t.id&&i.push(e)}),i.forEach(function(t){r.forEach(function(e){e.type===n&&e.trackId===t.id&&c(r,e,a)})})),a};function n(e,t,r){e=e.match(t);return e&&e.length>=r&&parseInt(e[r],10)}function s(e){return"[object Object]"===Object.prototype.toString.call(e)}function c(t,r,i){r&&!i.has(r.id)&&(i.set(r.id,r),Object.keys(r).forEach(function(e){e.endsWith("Id")?c(t,t.get(r[e]),i):e.endsWith("Ids")&&r[e].forEach(function(e){c(t,t.get(e),i)})}))}},{}],58:[function(e,t,r){var d=arguments[3],h=arguments[4],f=arguments[5],_=JSON.stringify;t.exports=function(e,t){for(var r=Object.keys(f),i=0,n=r.length;i<n;i++){var a=r[i],o=f[a].exports;if(o===e||o&&o.default===e){s=a;break}}if(!s){for(var s=Math.floor(Math.pow(16,8)*Math.random()).toString(16),c={},i=0,n=r.length;i<n;i++)c[a=r[i]]=a;h[s]=["function(require,module,exports){"+e+"(self); }",c]}var l=Math.floor(Math.pow(16,8)*Math.random()).toString(16),u={},p=(u[s]=s,h[l]=["function(require,module,exports){var f = require("+_(s)+");(f.default ? f.default : f)(self);}",u],{});!function e(t){p[t]=!0;for(var r in h[t][1]){r=h[t][1][r];p[r]||e(r)}}(l);var u="("+d+")({"+Object.keys(p).map(function(e){return _(e)+":["+h[e][0]+","+_(h[e][1])+"]"}).join(",")+"},{},["+_(l)+"])",l=window.URL||window.webkitURL||window.mozURL||window.msURL,u=new Blob([u],{type:"text/javascript"});return t&&t.bare?u:(t=l.createObjectURL(u),(l=new Worker(t)).objectURL=t,l)}},{}],59:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function n(){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(r,"__esModule",{value:!0}),function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)}(n,[{key:"destroy",value:function(){throw"InitiatorBase:destroy 此处需要实现"}},{key:"start",value:function(){}},{key:"stop",value:function(){}},{key:"startLazyComponents",value:function(){}},{key:"stopLazyComponents",value:function(){}},{key:"load",value:function(){throw"InitiatorBase:load 此处需要实现"}},{key:"setMediaElement",value:function(){throw"InitiatorBase:setMediaElement 此处需要实现"}},{key:"localPeerId",get:function(){}},{key:"xStreamId",get:function(){}}]),r.default=n},{}],60:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});r.ComCode={ROLLBACK:-2,HTTP_STATUS_CODE_INVALID:-1,RECEIVE_BUFFER:1,BUFFER_EOF:2,END_OF_STREAM:3,STREAM_NOT_FOUND:4},r.ComEvent={STATE_CHANGE:"tp2pStateChange",RECEIVE_STATE:"tp2pReceiveState"},r.RollbackReasons={STREAM_NOT_FOUND:"stream_not_found",PLAY_TIMEOUT:"play_timeout",SPLIT_STARTING:"split_starting",OTHER_REASON:"other_reason"}},{}],61:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});r.tp2pDefaultConfig={debug:!1,randomPlayId:"",startTime:Date.now(),loadTime:0,roomId:"",partnerId:"",sourceUrl:"",channelId:"",playTimes:0,confBaseUrl:"https://conf.qvb.qcloud.com/api/v1/live/h5/",stunServer:"",stunDomain:"",signalServer:"signal.qvb.qcloud.com",reportServer:"https://log.qvb.qcloud.com/reporter/vlive",trackerUrl:"",trackerVersion:"v1",trackerCustomVersion:"p6",liveDelayPreset:0,reporteInterval:6e4,useP2p:!0,initRollback:!1,p2p:1,monitorStart:5,bFix:1,bpFix:1,confVersion:0,mainMaxRetryCount:3,retryMainDelay:1500,confPCDNBaseUrl:"https://conf.qvb.qcloud.com/api/v3/live/h5/",trackerParameter:"",redirectedURL:void 0,uselessSliceCount:300,pcdnStreams:4,subStreamLoadInterval:3e3,p2pWindow:5,rollback404:!0,eos404Limit:2,enableRBAACSafari:!0,enableWriteAll:!1,switchSubMinBufLen:1,swithSubStreamDelay:1e4,bigHole:1e4,init404Rooms:[],enable302Rooms:["all"],enableStartWorker:!1,enablePeak:!1,peakPeriod:[684e5,828e5],playStartTimeout:4e3,reacquireDelay:3500,forwardParams:[],enableAppName:!0,enableRetry403:!1,enablePrevent:!1,enableSpeedUp:!1,accelerationRate:1.05,delayCheckInterval:15e3,enableBlockSpeedUp:!1,pcdnLiveDelay:7,assignDelayRooms:[],assignDelay:6e3,enableSlowDown:!1,slowDownRate:.9,enableSuperLatencyRooms:[],enableStartSeek:!1,startSeekDelay:0,startSeekStableRate:.5,startSeekSampleIntl:100,startSeekSampleCnt:3,enableRollbackOther:!1,rollbackWhileList:[],initStuckThreshold:1e3,mainStuckThreshold:1e4,disableTrigger:!1,restartP2PDelay:3e4,maxSkipRetryTimes:5,enableStuckReload:!0,stuckCauseReload:12e3,enableStuckWaitBuffer:!1,resumePlayDelay:9,resumePlayBuffer:3,enableReportGap:!1,enableStuckSeek:!1,enablePatchP2PFill:!0,patchSkipThreshold:20,patchSubStreamDiff:1.5,patchAccurateFill:!1,enablePatchCDNFill:!0,fillTimeout:2e3,fillMinBuf:2,fillRatio:1,parseHeader:!0,fillMaxTaskSize:2,enableAbortController:!0,enableSubFill:!1,judgeAliveDelay:3e3,judgeAliveCount:5,p2pUsefulRatio:.5,sampleDuration:6,PCKeepTimeout:2e3,sampleUsefulJudge:!1,clearPeerOnPauseP2P:!0,enableShareFrag:!1,pcdnConfTimeout:5e3,pcdnConfMaxRetry:5,pcdnConfRetryDelay:2e3,pcdnConfMaxRetryDelay:4e3,maxSubscribePCUsefulCount:2,maxConnectRequestEachPeriod:3,maxConnecting:8,maxConnectedPC:7,maxSubscribedPC:6,PCHeartTimeout:1e4,bufferedAmountLimit:5242880,DCChunkSize:64512,enableDCClosing:!1,enableDCOrdered:!0,checkPeerInterval:2e4,initEstimate:2,peerCacheSize:100,peerCacheDuration:6e4,enableStaticRTC:!1,rtcReportDeduplication:!0,localStreamShare:!0,maxSubLevel:3,ssmResGap2PR:300,ssmCheckInterval:300,ssmContinuousCount:3,ssmEmitInterval:3e4,stuckCauseRollback:1e4,stuckCauseSkip:5e3,stuckThreshold:1,confTimeout:3e3,confMaxRetry:0,confRetryDelay:1e3,confMaxRetryDelay:3e3,trackerInterval:16e3,trackerTimeout:5e3,trackerMaxRetry:2,trackerRetryDelay:2e3,trackerMaxRetryDelay:1e4,reportLoadingTimeout:5e3,reportLoadingMaxRetry:1,reportLoadingRetryDelay:500,reportLoadingMaxRetryDelay:1e3,plrEnable:0,plrThreshold:.02,plrCacheLength:2048,plrCheckDelay:2,confirmLossEnable:!0,startPlayWaitSliceLen:0,recordRecentStuckInterval:60,hundredSecStuckCnt:0,hundredSecStuckDur:0,sdkInitTime:0,service:1,platform:31,moduleId:1080,command:81e4,accessKey:"",xp2pAppKey:"",localSecKey:"",authMode:"none"}},{}],62:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var o=u(e(71)),a=u(e(70)),s=e(60),c=e(144),l=e(132);function u(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(e){if(this instanceof r)return(e=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,e,o.default.ERROR))).TAG="ErrorController",e;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"destroy",value:function(){a.default.prototype.destroy.call(this),this.tp2p=null}},{key:"onError",value:function(e){if(e.fatal){var t=e.extendReportData;switch(this._report(e.rollbackCode,t),this.tp2p.initiator.stopLazyComponents(),e.rollbackCode){case l.ReportCode.ROLLBACK:this._triggerRollback(t);break;case l.ReportCode.END_OF_STREAM:this._triggerEndOfStream();break;case l.ReportCode.STREAM_NOT_FOUND:this._triggerStreamNotFound();break;default:this._triggerRollback()}}}},{key:"_triggerEndOfStream",value:function(){c.logger.warn("触发流结束. 触发loader中onStateChange"),this.tp2p.status.closeReason="eos",this.tp2p.trigger(o.default.STATE_CHANGE,{code:s.ComCode.END_OF_STREAM})}},{key:"_triggerStreamNotFound",value:function(){c.logger.warn("触发404. 触发loader中onStateChange"),this.tp2p.status.closeReason="404",this.tp2p.trigger(o.default.STATE_CHANGE,{code:s.ComCode.STREAM_NOT_FOUND})}},{key:"_triggerRollback",value:function(e){var t=this.tp2p,e=(t.status.closeReason="rollback",this._rollbackReason(e));c.logger.warn("触发回退. 触发loader中onStateChange"),t.trigger(o.default.STATE_CHANGE,{code:s.ComCode.ROLLBACK,rollbackReason:e})}},{key:"_rollbackReason",value:function(e){if(!e)return s.RollbackReasons.OTHER_REASON;switch(e[l.ReportField.ROLLBACK_REASON]){case l.ReportField.ROLLBACK_STUCK:case l.ReportField.ROLLBACK_EMPTY_BUFFER:return s.RollbackReasons.PLAY_TIMEOUT;case l.ReportField.ROLLBACK_CDN_ERR:case l.ReportField.ROLLBACK_NONEXISTENT:return s.RollbackReasons.STREAM_NOT_FOUND;case l.ReportField.ROLLBACK_SPLIT_STARTING:case l.ReportField.ROLLBACK_NOT_ENOUGH_SPLIT:return s.RollbackReasons.SPLIT_STARTING;case l.ReportField.ROLLBACK_CONF_ERROR:case l.ReportField.ROLLBACK_APPEND_EXCEPTION:case l.ReportField.ROLLBACK_CONF_CONFIG:case l.ReportField.ROLLBACK_CONF_TIMEOUT:default:return s.RollbackReasons.OTHER_REASON}}},{key:"_report",value:function(e,t){var r,i,n,a={};a[l.ReportField.EVENT_CODE]=e,a[l.ReportField.CODE]=l.ReportCode.PHASE_DATA,t&&((e=t[l.ReportField.ROLLBACK_REASON])&&(this.tp2p.status.rollbackReason=e,c.logger.warn("回退原因: "+e),t.i=(r={},i=l.ReportField.PLAY_ORIGIN,n=1,i in r?Object.defineProperty(r,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[i]=n,r),-1<[l.ReportField.ROLLBACK_CONF_CONFIG,l.ReportField.ROLLBACK_NONEXISTENT,l.ReportField.ROLLBACK_STREAM_404].indexOf(e)&&(t.i[l.ReportField.ABNORMAL_REQUEST]=1),Object.assign(a,t)),this.tp2p.trigger(o.default.REPORT_ONCE,a))}}]),r}(a.default);r.default=e},{132:132,144:144,60:60,70:70,71:71}],63:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=d(e(71)),o=d(e(70)),s=d(e(147)),c=d(e(134)),l=d(e(142)),u=e(132),p=d(e(139));function d(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(e){if(this instanceof r)return(e=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,e,a.default.REPORT_ONCE))).TAG="ReporterController",e._callbacks=[],e.onCollectAndReport=e.collectAndReport.bind(e),e.onBeforeUnload=e.beforeUnload.bind(e),e.playStarted=0,window.addEventListener("beforeunload",e.onBeforeUnload),e;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"onStarting",value:function(){this._timer||(this._timer=setInterval(this.onCollectAndReport,this.tp2p.config.reporteInterval))}},{key:"onStopping",value:function(){this._timer&&(clearInterval(this._timer),this.timer=null)}},{key:"beforeUnload",value:function(){this._gatherExceptClose()||this.collectAndReport("close")}},{key:"_gatherExceptClose",value:function(){return this._closeCounter?(this._closeExcept||(this._closeExcept={close_cnt:0}),this._closeExcept.close_cnt=this._closeCounter,this._closeCounter+=1,!0):(window.removeEventListener("beforeunload",this.onBeforeUnload),!(this._closeCounter=1))}},{key:"destroy",value:function(){window.removeEventListener("beforeunload",this.onBeforeUnload),this.collectAndReport("destroy"),this.onStopping(),this.onCollectAndReport=null,o.default.prototype.destroy.call(this),this.playStarted=0,this.tp2p=null}},{key:"registerModule",value:function(e,t){this._callbacks.push(t)}},{key:"collectAndReport",value:function(){var t=this,e=0<arguments.length&&void 0!==arguments[0]&&arguments[0],r=this.template,i=this.tp2p.config,n=this.tp2p.status,a=(this._callbacks.forEach(function(e){e=e();t._combineData(r.i,e)}),this.closeExcept);a&&this._combineData(r.i,a),e&&(r.i[u.ReportField.IS_FIRST_PIECE_DOWNLOAD]=n.isFirstPieceDownloaded,r.i[u.ReportField.EXIT_REASON]=n.closeReason||e,r.i[u.ReportField.PLAY_TIME]=s.default.timestamp()-i.loadTime,n.rollbackReason)&&(r.i[u.ReportField.ROLLBACK_REASON]=n.rollbackReason),this.report(r,e)}},{key:"onReportOnce",value:function(t){t.data_type=1;var r=this.template,e=(Object.keys(t).forEach(function(e){r[e]=t[e]}),this.closeExcept);e&&t&&t.i&&this._combineData(r.i,e),this.report(r)}},{key:"report",value:function(e){this.tp2p.trigger(a.default.REPORTING_DATA,{data:e,lastReport:1<arguments.length&&void 0!==arguments[1]&&arguments[1]})}},{key:"_combineData",value:function(t,r){Object.keys(r).forEach(function(e){t[e]=r[e]})}},{key:"closeExcept",get:function(){var e=this._closeExcept;return this._closeExcept=null,e}},{key:"template",get:function(){var e=this.tp2p.config,t=this.tp2p.status,r=Math.floor((s.default.timestamp()-e.loadTime)/1e3),i=r-this.playStarted;return this.playStarted=r,{service:e.service,platform:e.platform,appid:e.appId,bizid:e.bizId,stream_id:e.channelId,module_id:e.moduleId,command:e.command,data_time:Math.round(s.default.timestamp()/1e3),version:c.default.version,video_type:"live",data_type:2,channel:e.channelId,partner:e.partner,uuid:l.default.myUUID().UUID,code:"000",str_video_type:"live",src_type:"pcdn"===e.arch?"xstream":"auto",host:window.location.host,life:Math.round(s.default.timestamp()-e.startTime),cur_play_duration:Math.round(s.default.timestamp()-e.loadTime),url:e.originalUrl,referer:window.location.href,pro:"h5",browser_name:e.browser.name,browser_major:e.browser.major,browser_version:e.browser.version,user_agent:window.navigator.userAgent,roomid:e.roomId,str_user_id:l.default.myUUID().UUID,str_play_id:e.randomPlayId,page_visibility:p.default.pageVisibility().pageVisible,memory_usage:p.default.memory(),full_screen:p.default.isFullScreen(),open_visible:t.openVisible,arch:e.arch,log_version:"pcdn"===e.arch?1:0,conf_version:e.confVersion,cur_time:Date.now(),i:{play_started:r,session_time:i}}}}]),r}(o.default);r.default=e},{132:132,134:134,139:139,142:142,147:147,70:70,71:71}],64:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=u(e(71)),o=u(e(70)),s=u(e(150)),c=u(e(147)),l=u(e(151));function u(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(e){if(this instanceof r)return(e=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,e,a.default.REPORTING_DATA))).TAG="ReporterLoader",e;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"destroy",value:function(){o.default.prototype.destroy.call(this),this.tp2p=null}},{key:"onReportingData",value:function(e){var t=this.tp2p.config,r=new l.default,i=s.default.encrypt(JSON.stringify(e.data)),e="close"===e.lastReport,n={tag:c.default.timestamp(),url:t.reportServer,method:"POST",headers:[{header:"Content-Type",value:"application/octet-stream"}],data:i},a={timeout:t.reportLoadingTimeout,maxRetry:t.reportLoadingMaxRetry,retryDelay:t.reportLoadingRetryDelay,maxRetryDelay:t.reportLoadingMaxRetryDelay,sync:e};e?navigator&&navigator.sendBeacon&&navigator.sendBeacon(t.reportServer,i):r.load(n,a,{onSuccess:function(){},onError:function(){},onTimeout:function(){}})}}]),r}(o.default);r.default=e},{147:147,150:150,151:151,70:70,71:71}],65:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),e};function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=l(e(71)),o=l(e(70)),s=l(e(73)),c=e(132);function l(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=i;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function i(e){var t,r;if(this instanceof i)return(t=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(i.__proto__||Object.getPrototypeOf(i)).call(this,e,a.default.TRACKER_LOADED,a.default.TRACKER_STARTING,a.default.TRACKER_STOPPING))).TAG="TrackerController",t.onRequestTracker=t._requestTracker.bind(t),r=(e=e.config).peerCacheSize,e=e.peerCacheDuration,t.peerCache=new s.default({maxCacheSize:r,cacheDuration:e}),t._initReporterData(),t.tp2p.registerReportCallback(t.TAG,t.reportCallback.bind(t)),t;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),n(i,[{key:"destroy",value:function(){this._clearTimer(),o.default.prototype.destroy.call(this),this.tp2p=null}},{key:"onTrackerLoaded",value:function(e){var t=this;try{var r,i=JSON.parse(e.response);0!==i.ret&&"0"!==i.ret||((r=i.peers).forEach(function(e){t.peerCache.add(e.pid,{streamId:e.stream})}),this._reportData[c.ReportField.T_PEER]+=r.length)}catch(e){}}},{key:"onTrackerStarting",value:function(){var e=this.tp2p.config;this._timer||(this._timer=setInterval(this.onRequestTracker,e.trackerInterval),this._requestTracker())}},{key:"onTrackerStopping",value:function(){this.stop()}},{key:"stop",value:function(){this.peerCache.clear(),this._clearTimer()}},{key:"nextStreamPeer",value:function(e){return this.peerCache.nextStreamPeer(e)}},{key:"_requestTracker",value:function(){var e,t,r=this.tp2p.config,i=this.tp2p.initiator,n=i.xStreamId,i=i.localPeerId;i&&r.useP2p&&(t="_",r.stunServer&&(e=r.stunServer.split(":"),t+=this._ipToNumber(e[0])),(e=r.trackerParameter)&&(e+="_"),r.stunDomain&&(t=""),t=r.trackerServer+"/htbt?channel=h5"+t+"_"+r.pcdnStreams+"_"+e+r.channelId+"_"+r.trackerCustomVersion+"-"+r.trackerVersion+"&resolution=UHD&pid="+i+"&streamId="+n+(r.localStreamShare?"&mode=bat":""),this.tp2p.trigger(a.default.TRACKER_LOADING,{url:t}))}},{key:"_ipToNumber",value:function(e){var t=0;return""===e||4!==(e=e.split(".")).length?0:(t=(t=(t+=parseInt(e[0],10)<<24)+(parseInt(e[1],10)<<16))+(parseInt(e[2],10)<<8))+(parseInt(e[3],10)<<0)>>>0}},{key:"_clearTimer",value:function(){this._timer&&(clearInterval(this._timer),this._timer=null)}},{key:"_initReporterData",value:function(){this._reportData={},this._reportData[c.ReportField.T_PEER]=0}},{key:"reportCallback",value:function(){var e=this._reportData;return this._initReporterData(),e}},{key:"nextPeer",get:function(){return this.peerCache.nextPeer}}]),i}(o.default);r.default=e},{132:132,70:70,71:71,73:73}],66:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=l(e(71)),o=e(69),s=l(e(70)),c=l(e(151));function l(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(e){if(this instanceof r)return(e=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,e,a.default.TRACKER_LOADING))).TAG="TrackerLoader",e;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"destroy",value:function(){s.default.prototype.destroy.call(this),this._loader&&(this._loader.destroy(),this._loader=null),this.tp2p=null}},{key:"onTrackerLoading",value:function(e){this._loader&&this._loader.destroy();var t=this.tp2p.config,e=(this._loader=new c.default,{tag:"tracker_"+Date.now(),url:e.url,method:"GET",responseType:"text"}),t={timeout:t.trackerTimeout,maxRetry:t.trackerMaxRetry,retryDelay:t.trackerRetryDelay,maxRetryDelay:t.trackerMaxRetryDelay},r={onSuccess:this.loadSuccess.bind(this),onError:this.loadError.bind(this),onTimeout:this.loadTimeout.bind(this)};this._loader.load(e,t,r)}},{key:"loadSuccess",value:function(e,t,r){e=e.data;this.tp2p.trigger(a.default.TRACKER_LOADED,{response:e})}},{key:"loadError",value:function(e,t,r){this.tp2p.trigger(a.default.ERROR,{type:o.ErrorTypes.NETWORK_ERROR,details:o.ErrorDetails.REPORTER_LOAD_ERROR,fatal:!1,context:r})}},{key:"loadTimeout",value:function(e,t){this.tp2p.trigger(a.default.ERROR,{type:o.ErrorTypes.NETWORK_ERROR,details:o.ErrorDetails.REPORTER_LOAD_TIMEOUT,fatal:!1,context:t})}}]),r}(s.default);r.default=e},{151:151,69:69,70:70,71:71}],67:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=["domain","appId","bizId","partner","pcdnMixed"];r.decodeAccessKey=function(e){try{var t=JSON.parse(window.atob(e)),r=!0;return i.forEach(function(e){t[e]||(r=!1)}),!r||t}catch(e){return null}},r.createAccessKey=function(e){return window.btoa(JSON.stringify(e))}},{}],68:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var e=e(148),a=(e=e)&&e.__esModule?e:{default:e};i(o,null,[{key:"Url",value:function(e,t){var r=this.BasicHandleUrl(e);switch(t){case"h5.huya.com":var i,n=e.match(/&ratio=([^&]+)/);n&&(i=r.channelId,a=r.sourceUrl,o=r.originalUrl,r.channelId+="_"+n[1],r.sourceUrl=a.replace(i,r.channelId),r.originalUrl=o.replace(i,r.channelId),r.trackerParameter=r.appName);break;case"h5.huomao.tv":r.enablePrevent=!0;break;case"h5.bilibili.com":var n=e.split("/");r.appName=n[3]+"/"+n[4],r.enablePrevent=!0;break;case"h5.douyutv.com":r.init404Rooms=["all"],r.forwardParams=["origin"],r.enablePrevent=!0,r.rollbackWhileList=["tc-tct.douyucdn2.cn","9699.liveplay.myqcloud.com"],r.enableStartWorker=!0;var a=r.liveUrlHost,o=function(e){if(e){var e=e.split(".")[0];if(/.+(3a|3|1|1a|sa)$/.test(e))return"sa"===(e=/.+(3a|3|1|1a|sa)$/.exec(e)[1])?"1":e}return""};r.appName=(i=a,-1===(n=r.appName).indexOf("dyliveflv")&&(i=o(i))?"dyliveflv"+i:n)}return r}},{key:"BasicHandleUrl",value:function(e){var t=this.createUrlObj(),r=(t.sourceUrl=a.default.GetSourceUrl(e),a.default.GetLiveInfoFromUrl(e));return t.liveUrlHost=r.liveUrlHost,t.channelId=r.channelId,t.appName=r.appName,t.originalUrl=e,t.roomId=a.default.GetRoomIdFromUrl(e),t.params=a.default.ParseUrlParams(e),t}},{key:"createUrlObj",value:function(){return{sourceUrl:"",originalUrl:"",liveUrlHost:"",channelId:"",appName:"",roomId:"",params:void 0}}},{key:"checkConfig",value:function(e,t){var r="",i="",n="";return t||(r="请传入p2pConfig"),t.videoId||(i='p2pConfig中参数中未检测到 video id,请传入videoId.\n 或者自行传入 meida element.\n let media = document.getElementById("your video id");\n qvbp2p.setMediaElement(media)\n'),t.src||(r="请在p2pConfig中填写src"),e._rollback||(n+="* 没有检测到回退函数 rollback, 如果不绑定rollback, 请确保在loader的onStateChange中处理了回退. \n"),{error:r,warn:i,log:n}}}]);e=o;function o(){if(!(this instanceof o))throw new TypeError("Cannot call a class as a function")}r.default=e},{148:148}],69:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});r.ErrorTypes={NETWORK_ERROR:"networkError",MEDIA_ERROR:"mediaError",MUX_ERROR:"muxError",OTHER_ERROR:"otherError"},r.ErrorDetails={CDN_LOAD_ERROR:"cdnLoadError",CDN_LOAD_TIMEOUT:"cdnLoadTimeout",CONF_LOAD_ERROR:"confLoadError",CONF_LOAD_TIMEOUT:"confLoadTimeout",CONF_DECRYPT_ERROR:"confDecryptError",REPORTER_LOAD_TIMEOUT:"reporterLoadTimeout",REPORTER_LOAD_ERROR:"reporterLoadError",PIECE_DEMUX_ERROR:"pieceDemuxError",STREAM_STOP:"streamStop",STUCK_ROLLBACK:"stuckRollback"}},{}],70:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=function(e,t,r){return t&&a(e.prototype,t),r&&a(e,r),e};function a(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var o=e(144);function s(e){var t=this,r=s;if(!(t instanceof r))throw new TypeError("Cannot call a class as a function");this.tp2p=e,this.onEvent=this.onEvent.bind(this);for(var i=arguments.length,n=Array(1<i?i-1:0),a=1;a<i;a++)n[a-1]=arguments[a];this.handledEvents=n,this.useGenericHandler=!0,this.registerListeners(),this.TAG="EventHandler"}n(s,[{key:"destroy",value:function(){this.unregisterListeners()}},{key:"isEventHandler",value:function(){return"object"===i(this.handledEvents)&&this.handledEvents.length&&"function"==typeof this.onEvent}},{key:"registerListeners",value:function(){var t=this;this.isEventHandler()&&this.handledEvents.forEach(function(e){if("tp2pEventGeneric"===e)throw new Error("Forbidden event name: "+e);t.tp2p.on(e,t.onEvent)})}},{key:"unregisterListeners",value:function(){var t=this;this.isEventHandler()&&this.handledEvents.forEach(function(e){t.tp2p.off(e,t.onEvent)})}},{key:"onEvent",value:function(e,t){this.onEventGeneric(e,t)}},{key:"onEventGeneric",value:function(t,e){try{!function(e,t){var r="on"+e.replace("tp2p","");if("function"!=typeof this[r])throw new Error("Event "+e+" has no generic handler in this "+this.constructor.name+" class (tried "+r+")");return this[r].bind(this,t)}.call(this,t,e).call()}catch(e){o.logger.error("error happened while processing "+t+":"+e.message)}}},{key:"_logTitle",value:function(){return this.TAG+" ### "}}]),r.default=s},{144:144}],71:[function(e,t,r){"use strict";t.exports={CDN_DOWNLOAD_STARTING:"tp2pCdnDownloadStarting",CDN_DOWNLOAD_STOP:"tp2pCdnDownloadStop",CDN_LOADING:"tp2pCdnLoading",CDN_LOADED:"tp2pCdnLoaded",P2P_LOADED:"tp2pP2pLoaded",FRAG_LOADED:"tp2pFragLoaded",CDN_DOWNLOAD_RESET:"tp2pCdnDownloadReset",CDN_DOWNLOAD_PAUSE:"tp2pCdnDownloadPause",BUFFER_STASHING:"tp2pBufferStashing",BUFFER_REQUESTING:"tp2pBufferRequesting",CONF_LOADING:"tp2pConfLoading",CONF_LOADED:"tp2pConfLoaded",CONF_PARSING:"tp2pConfParsing",CREAT_PLAY:"tp2pCreatPlay",STATE_CHANGE:"tp2pStateChange",RECEIVE_STATE:"tp2pReceiveState",ERROR:"tp2pError",SYNC_STEP_BACK:"tp2pSyncStepBack",REPORT_ONCE:"tp2pReportOnce",REPORTING_DATA:"tp2pReportingData",REPORTER_STARTING:"tp2pReporterStarting",REPORTER_STOPPING:"tp2pReporterStopping",TRACKER_LOADING:"tp2pTrackerLoading",TRACKER_LOADED:"tp2pTrackerLoaded",TRACKER_STARTING:"tp2pTrackerStarting",TRACKER_STOPPING:"tp2pTrackerStopping",SIGNAL_SENDING:"tp2pSignalSending",SIGNAL_RECEIVING:"tp2pSignalReceiving",FAST_FORWARD:"tp2pFastForward",MEDIA_CREATED:"tp2pMediaCreated",WRITER_TICK:"tp2pWriterTick",FLV_HEAD_LOADED:"tp2pFlvHeadLoaded",LOADED_DATA:"tp2pLoadedData",FIRST_CONF_LOADED:"tp2pFirstConfLoaded",WRITE_SKIP:"tp2pWriteSkip",STOP_WRITE_SKIP:"tp2pStopWriteSkip",FILL_FRAGMENT_SUCCESS:"tp2pFillFragmentSuccess",FILL_FRAGMENT_FAIL:"tp2pFillFragmentFail",P2P_FILL_FRAGMENT_SUCCESS:"tp2pP2pFillFragmentSuccess",P2P_FILL_FRAGMENT_FAIL:"tp2pP2pFillFragmentFail",FOUND_SLICE:"tp2pFoundSlice",P2P_FILL_SUCCESS:"tp2pP2pFillSuccess",P2P_FILL_REQ:"tp2pP2pFillReq",P2P_FILL_RES:"tp2pP2pFillRes",TRY_P2P_FILL:"tp2pTryP2pFill",SUBSCRIBE_FILL:"tp2pSubscribeFill",PULL_SUB_STREAM:"tp2pPullSubStream",P2P_STARTING:"tp2pP2pStarting",P2P_STOPPING:"tp2pP2pStopping",STUCK_RELOAD:"tp2pStuckReload",CHANGE_PEER:"tp2pChangePeer",CONF_FAIL:"tp2pConfFail",SHUT_DOWN_P2P:"tp2pShutDownP2p",TURN_ON_P2P:"tp2pTurnOnP2p"}},{}],72:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var e=e(147),a=(e=e)&&e.__esModule?e:{default:e};function o(){if(!(this instanceof o))throw new TypeError("Cannot call a class as a function");this.TAG="DataSet",this._set=new Set,this._keeper={}}i(o,[{key:"destroy",value:function(){this.clear(),this._set=null,this._keeper=null}},{key:"add",value:function(e,t){if(this.has(e))return!1;this._set.add(e),this._keeper[e]={loader:t.loader,addTime:a.default.timestamp()}}},{key:"has",value:function(e){return this._set.has(e)}},{key:"get",value:function(e){return!!this.has(e)&&this._keeper[e]}},{key:"clear",value:function(){var t=this;this._set.forEach(function(e){t._deleteFromKeeper(e)}),this._set.clear(),this._keeper={}}},{key:"deletes",value:function(e){return!!this.has(e)&&(this._set.delete(e),this._deleteFromKeeper(e),!0)}},{key:"keys",value:function(){var e=this._set.keys();return[].concat(function(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)}(e))}},{key:"_deleteFromKeeper",value:function(e){try{this._keeper[e].loader.destroy(),delete this._keeper[e].loader,delete this._keeper[e]}catch(e){}}},{key:"_logTitle",value:function(){return this.TAG+" ### "}},{key:"size",get:function(){return this._set.size}}]),r.default=o},{147:147}],73:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=o(e(72)),c=o(e(147));function o(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(e){if(!(this instanceof r))throw new TypeError("Cannot call a class as a function");var t=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this));if(t.TAG="PeerCache",e.maxCacheSize)return t._maxCacheSize=e.maxCacheSize,t._liveDuration=e.cacheDuration||0,t;throw Error('peer cache 初始化必填 "maxCacheSize"')}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"add",value:function(e,t){this.has(e)&&this.deletes(e),this._set.add(e),this._keeper[e]={streamId:t.streamId,addTime:c.default.timestamp()},this._keepSize(),this._removeOutDate()}},{key:"_keepSize",value:function(){var e=this.size-this._maxCacheSize;if(0<e)for(var t=this.keys(),r=void 0,r=0;r<e;r++){var i=t.shift();this.deletes(i)}}},{key:"_removeOutDate",value:function(){if(this._liveDuration){var e=this._set.values(),t=!0,r=!1,i=void 0;try{for(var n,a=e[Symbol.iterator]();!(t=(n=a.next()).done);t=!0){var o=n.value,s=this._keeper[o];c.default.timestamp()-s.addTime>this._liveDuration&&this.deletes(o)}}catch(e){r=!0,i=e}finally{try{!t&&a.return&&a.return()}finally{if(r)throw i}}}}},{key:"nextStreamPeer",value:function(e){var t=this.keys();if(0<t.length){for(var r=null,i=0;i<t.length;i++){var n=t[i],a=this._keeper[n].streamId;if(a===e){r={peerId:n,streamId:a};break}}return r&&this.deletes(r.peerId),r}return null}},{key:"_deleteFromKeeper",value:function(e){try{delete this._keeper[e]}catch(e){}}},{key:"nextPeer",get:function(){var e,t=this.keys();return 0<t.length?(t=t.pop(),e=this._keeper[t].streamId,this.deletes(t),{peerId:t,streamId:e}):null}}]),r}(a.default);r.default=e},{147:147,72:72}],74:[function(e,t,r){"use strict";e=e(134).default;window.QVBP2P=e,t.exports=e},{134:134}],75:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=c(e(76)),o=c(e(71)),s=c(e(70));function c(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(e){var t;if(this instanceof r)return(t=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,e,o.default.SIGNAL_SENDING))).TAG="SignalClient",e=e.config,t._socketIOConnection=new a.default({signalServer:e.signalServer},t.onEvents.bind(t)),t._socketIOConnection.on("message",function(e){t.tp2p.trigger(o.default.SIGNAL_RECEIVING,e)}),t;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"onSignalSending",value:function(e){this.send(e)}},{key:"destroy",value:function(){this._socketIOConnection.destroy(),s.default.prototype.destroy.call(this),this.tp2p=null}},{key:"onEvents",value:function(e){"connect"===e.event&&(this.tp2p.initiator.localPeerId=this._socketIOConnection.socketId)}},{key:"send",value:function(e){this._socketIOConnection.emit("message",e)}}]),r}(s.default);r.default=e},{70:70,71:71,76:76}],76:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var e=e(77),a=(e=e)&&e.__esModule?e:{default:e};function o(e,t){if(!(this instanceof o))throw new TypeError("Cannot call a class as a function");this._socket=new a.default(e.signalServer,{reconnection:!0,reconnectionAttempts:10,reconnectionDelay:2e3,randomizationFactor:.5,timeout:2e4,autoConnect:!0}),this.callback=t,this._initListening()}i(o,[{key:"destroy",value:function(){this._disconnect(),this._socket=null,this.callback=null}},{key:"on",value:function(e,t){this._socket.on(e,t)}},{key:"emit",value:function(e,t){this._socket.emit(e,t)}},{key:"_disconnect",value:function(){return this._socket.disconnect()}},{key:"_initListening",value:function(){var e=this,t=this._socket;t.on("connect",function(){e.callback({event:"connect"})}),t.on("connect_error",function(e){}),t.on("connect_timeout",function(e){}),t.on("error",function(e){}),t.on("disconnect",function(e){}),t.on("reconnect",function(e){}),t.on("reconnect_attempt",function(e){}),t.on("reconnecting",function(e){}),t.on("reconnect_error",function(e){}),t.on("reconnect_failed",function(){}),t.on("ping",function(){}),t.on("pong",function(e){})}},{key:"socketId",get:function(){return this._socket.id}},{key:"connection",get:function(){return this._socket},set:function(e){this._socket=e}},{key:"logTime",get:function(){var e=new Date;return e.getHours()+":"+e.getMinutes()+":"+e.getSeconds()+"."+e.getMilliseconds()}}]),r.default=o},{77:77}],77:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function n(e,t){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function");this.id=null,this.events={},this.uri="wss://"+e,this.options=t,this.reconnect=0,this.timer=null,this.initWebSocket()}Object.defineProperty(r,"__esModule",{value:!0}),function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)}(n,[{key:"initWebSocket",value:function(){WebSocket&&(this.reconnect+=1,this._ws=new WebSocket(this.uri),this._ws.onopen=this.onopen.bind(this),this._ws.onmessage=this.onmessage.bind(this),this._ws.onclose=this.onclose.bind(this),this._ws.onerror=this.onerror.bind(this))}},{key:"on",value:function(e,t){"connect"!==e&&"message"!==e||(this.events[e]=t)}},{key:"emit",value:function(e,t){"message"===e&&this._ws.send("S "+t.to+" "+JSON.stringify(t))}},{key:"disconnect",value:function(){this.timer&&(clearTimeout(this.timer),this.timer=null),this.options.reconnection=!1,this._ws.close()}},{key:"onopen",value:function(e){this.reconnect=0}},{key:"onmessage",value:function(e){var t=e.data.substring(0,1);"C"===t?(this.id=e.data.substring(2),this.events.connect()):"S"===t&&(t=JSON.parse(e.data.substring(29)),this.events.message(t))}},{key:"onerror",value:function(e){}},{key:"onclose",value:function(e){var t=this;this.options.reconnection&&this.options.reconnectionAttempts>this.reconnect&&(this.timer=setTimeout(function(){t.initWebSocket()},this.options.reconnectionDelay+1e3*this.reconnect))}},{key:"logTime",get:function(){var e=new Date;return e.getHours()+":"+e.getMinutes()+":"+e.getSeconds()+"."+e.getMilliseconds()}}]),r.default=n},{}],78:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});r.SignalType={OFFER:"offer",ANSWER:"answer",ICE_CANDIDATE:"iceCandidate",JUST_START:"justStart"},r.DataChannelState={OPEN:"open",CLOSE:"close",ERROR:"error",BUFFER_AMOUNT_LOW:"bufferAmountLow",REPORT:"report"}},{}],79:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=e(78);function o(e,t,r){if(!(this instanceof o))throw new TypeError("Cannot call a class as a function");this.TAG="DataChannel",this.enableDCClosing=t.enableDCClosing,this.callbacks=r;r={negotiated:!0,id:t.id,ordered:t.enableDCOrdered,reliable:!0,bufferedAmountLowThreshold:0};this.dataChannel=e.createDataChannel(t.label,r),this.dataChannel.binaryType="arraybuffer",this._bindHandler(),this._bufferedAmountLimit=t.bufferedAmountLimit}i(o,[{key:"destroy",value:function(){this.dataChannel.close(),this._removeHandler(),this.dataChannel=null,this.callbacks=null}},{key:"_bindHandler",value:function(){var e=this.dataChannel;e.onopen=this.onOpen.bind(this),e.onerror=this.onError.bind(this),e.onclose=this.onClose.bind(this),e.onmessage=this.onMessage.bind(this),e.onbufferedamountlow&&(e.onbufferedamountlow=this.onBufferedAmountLow.bind(this))}},{key:"_removeHandler",value:function(){var e=this.dataChannel;e.onopen=null,e.onerror=null,e.onclose=null,e.onmessage=null,e.onbufferedamountlow=null}},{key:"send",value:function(e){var t=this.dataChannel;if(t&&"open"===t.readyState){if(t.bufferedAmount<=this._bufferedAmountLimit)try{e instanceof Array?e.forEach(function(e){t.send(e)}):t.send(e)}catch(e){}}else this.enableDCClosing&&t&&"closing"===this.readyState&&this.onClose()}},{key:"onMessage",value:function(e){this.callbacks&&this.callbacks.onDataChannelMessage(e.data)}},{key:"onOpen",value:function(){this.callbacks&&this.callbacks.onDataChannelStateChange({state:a.DataChannelState.OPEN})}},{key:"onClose",value:function(){this.callbacks&&this.callbacks.onDataChannelStateChange({state:a.DataChannelState.CLOSE})}},{key:"onError",value:function(e){this.callbacks&&this.callbacks.onDataChannelStateChange({state:a.DataChannelState.ERROR})}},{key:"onBufferedAmountLow",value:function(){this.callbacks.onDataChannelStateChange({state:a.DataChannelState.BUFFER_AMOUNT_LOW})}},{key:"readyState",get:function(){return this.dataChannel.readyState}},{key:"bufferedAmount",get:function(){return this.dataChannel.bufferedAmount}},{key:"label",get:function(){return this.dataChannel.label}},{key:"id",get:function(){return this.dataChannel.id}},{key:"bufferedAmountLowThreshold",get:function(){return this.dataChannel.bufferedAmountLowThreshold}}]),r.default=o},{78:78}],80:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=e(79),o=(a=a)&&a.__esModule?a:{default:a},s=e(78);i(c,[{key:"destroy",value:function(){this._dataChannel.destroy(),this._dataChannel=null,this._rtcPeerConnection.close(),this.callbacks=null}},{key:"send",value:function(e){this._dataChannel.send(e)}},{key:"_bindHandler",value:function(){this._rtcPeerConnection.onicecandidate=this.onIceCandidate.bind(this),this._rtcPeerConnection.oniceconnectionstatechange=this.onIceConnectionStateChange.bind(this),this._rtcPeerConnection.onsignalingstatechange=this.onSignalingStateChange.bind(this)}},{key:"receiveSignal",value:function(e){switch(e.type){case s.SignalType.JUST_START:this._createOffer();break;case s.SignalType.OFFER:this._receiveOffer(e.msg);break;case s.SignalType.ANSWER:this._receiveAnswer(e.msg);break;case s.SignalType.ICE_CANDIDATE:this._receiveCandidate(e.msg)}}},{key:"onIceCandidate",value:function(e){this.callbacks&&e&&e.candidate&&this.callbacks.onSendBySignalChannel({type:s.SignalType.ICE_CANDIDATE,msg:e.candidate})}},{key:"onIceConnectionStateChange",value:function(e){this.callbacks&&this.callbacks.onStateChange({context:"peerConnection",type:"IceConnectionState",state:e.target.iceConnectionState})}},{key:"onSignalingStateChange",value:function(){this.callbacks&&this.callbacks.onStateChange({context:"peerConnection",type:"SignalingState",state:this._rtcPeerConnection.signalingState})}},{key:"onDataChannelMessage",value:function(e){this.callbacks&&this.callbacks.onMessage(e)}},{key:"onDataChannelStateChange",value:function(e){this.callbacks&&this.callbacks.onStateChange({context:"dataChannel",type:"State",state:e.state,originData:e})}},{key:"_receiveOffer",value:function(e){var t=this;this._receiveDescription(e,function(){t._createAnswer()})}},{key:"_receiveAnswer",value:function(e){this._receiveDescription(e)}},{key:"_receiveCandidate",value:function(e){var t=void 0;try{t=new RTCIceCandidate(e)}catch(e){return}this._rtcPeerConnection.addIceCandidate(t).then(function(){}).catch(function(e){})}},{key:"_createAnswer",value:function(){var t=this;this._rtcPeerConnection.createAnswer().then(function(e){return t._rtcPeerConnection.setLocalDescription(e)}).then(function(){t.callbacks&&t.callbacks.onSendBySignalChannel({type:s.SignalType.ANSWER,msg:t._rtcPeerConnection.localDescription})}).catch(function(e){})}},{key:"_receiveDescription",value:function(e,t){this._rtcPeerConnection.setRemoteDescription(new RTCSessionDescription(e)).then(function(e){t&&t()}).catch(function(e){})}},{key:"_createOffer",value:function(){var t=this;this._rtcPeerConnection.createOffer({OfferToReceiveAudio:!1,OfferToReceiveVideo:!1}).then(function(e){return t._rtcPeerConnection.setLocalDescription(e)}).then(function(){t.callbacks&&t.callbacks.onSendBySignalChannel({type:s.SignalType.OFFER,msg:t._rtcPeerConnection.localDescription,streamId:t._dataChannel.id})}).catch(function(e){})}},{key:"iceGatheringState",get:function(){return this._rtcPeerConnection.iceGatheringState}}]);a=c;function c(e,t){if(!(this instanceof c))throw new TypeError("Cannot call a class as a function");this.TAG="WebRTCPeerConnection",this.localId=e.localId,this.remoteId=e.remoteId,this.callbacks=t,this._rtcPeerConnection=new RTCPeerConnection(e.ice),this._bindHandler();t={label:this.remoteId,id:0,bufferedAmountLimit:e.bufferedAmountLimit,enableDCClosing:e.enableDCClosing,enableDCOrdered:e.enableDCOrdered},e={onDataChannelMessage:this.onDataChannelMessage.bind(this),onDataChannelStateChange:this.onDataChannelStateChange.bind(this)};this._dataChannel=new o.default(this._rtcPeerConnection,t,e)}r.default=a},{78:78,79:79}],81:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var c=o(e(71)),a=o(e(70)),l=o(e(134)),u=e(132);function o(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(e){if(this instanceof r)return(e=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,e,c.default.CONF_PARSING))).TAG="ConfController",e._pconfField=[],e.init(),e;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"destroy",value:function(){this.onStopping(),a.default.prototype.destroy.call(this),this.tp2p=null}},{key:"init",value:function(){this._confRequestCounter=0,this._initConfFieldMap(),this._getConfFromLocalStorage()}},{key:"_initConfFieldMap",value:function(){this._pconfField=[["monitorStart","monitor_start"],["PCHeartTimeout","pc_heart_timeout"],["syncInterval","sync_interval"],["peerCacheSize","peer_cache_size"],["trackerInterval","tracker_interval"],["stuckCauseRollback","stuck_cause_rollback"],["stuckCauseSkip","stuck_cause_skip"],["localStreamShare","local_stream_share"],["maxSubLevel","max_sub_level"],["maxSubscribePCUsefulCount","max_subscribe_pc_useful_count"],["maxConnectRequestEachPeriod","max_connect_request_each_period"],["maxConnectedPC","max_connected_pc"],["maxSubscribedPC","max_subscribed_pc"],["PCKeepTimeout","pc_keep_timeout"],["bufferedAmountLimit","buffered_amount_limit"],["DCChunkSize","dc_chunk_size"],["stunServer","h5_stun_server"],["signalServer","h5_signal_server"],["initEstimate","init_estimate"],["initRollback","init_rollback"],["checkPeerInterval","check_peer_interval"],["stuckThreshold","stuck_threshold"],["maxConnecting","max_connecting"],["enableSlowDown","enable_slow_down"],["slowDownRate","slow_down_rate"],["bFix","b_fix"],["bpFix","bp_fix"],["trackerCustomVersion","tracker_custom_version"],["p2p","p2p"],["fillTimeout","fill_timeout"],["p2pWindow","p2p_w"],["judgeAliveDelay","judge_alive_delay"],["judgeAliveCount","judge_alive_cnt"],["p2pUsefulRatio","p2p_useful_ratio"],["sampleDuration","useful_sample_dura"],["fillMinBuf","fill_min_buf"],["enableSpeedUp","enable_speed_up"],["accelerationRate","acc_rate"],["checkInterval","check_interval"],["enablePatchP2PFill","en_patch_p2p_fill"],["enablePatchCDNFill","en_patch_cdn_fill"],["patchSkipThreshold","patch_skip_thr"],["patchAccurateFill","patch_acc_fill"],["patchSubStreamDiff","patch_sub_stream_diff"],["ssmCheckInterval","ssm_check_interval"],["ssmResGap2PR","ssm_res_gap_pr"],["ssmContinuousCount","ssm_continuous_count"],["ssmEmitInterval","ssm_emit_interval"],["pcdnLiveDelay","pcdn_live_delay"],["uselessSliceCount","useless_slice_cnt"],["confVersion","conf_ver"],["enableDCClosing","en_dc_closing"],["enableDCOrdered","en_dc_ordered"],["mainMaxRetryCount","main_max_retry_cnt"],["enableSubFill","en_sub_fill"],["subStreamLoadInterval","sub_load_interval"],["sampleUsefulJudge","sample_useful_judge"],["disableTrigger","dis_trigger"],["plrEnable","plr_enable"],["plrThreshold","plr_threshold"],["plrCacheLength","plr_cache_length"],["plrCheckDelay","plr_check_delay"],["retryMainDelay","retry_main_delay"],["enableWriteAll","en_write_all"],["confirmLossEnable","confirm_loss_enable"],["enableStuckReload","en_stuck_reload"],["restartP2PDelay","restart_p2p_delay"],["fillRatio","fill_ratio"],["maxSkipRetryTimes","max_skip_retry"],["enableRollbackOther","en_rb_other"],["rollbackWhileList","rb_white_list"],["enableStuckWaitBuffer","en_stuck_wait"],["resumePlayDelay","re_play_delay"],["resumePlayBuffer","re_play_buf"],["parseHeader","parse_header"],["rollback404","rb_404"],["eos404Limit","eos_404_limit"],["startPlayWaitSliceLen","start_play_wait","spwsl"],["playStartTimeout","play_start_timeout","pst"],["assignDelayRooms","assign_delay_rooms","adr"],["assignDelay","assign_delay","ady"],["fillMaxTaskSize","fill_max_size"],["stuckCauseReload","stuck_cause_reload"],["enableAbortController","en_abort"],["enableBlockSpeedUp","en_block_speedup"],["switchSubMinBufLen","switch_sub_min_buf"],["recordRecentStuckInterval","record_recent_stuck_interval"],["hundredSecStuckCnt","hundred_sec_stuck_cnt"],["hundredSecStuckDur","hundred_sec_stuck_dur"],["bigHole","big_hole"],["init404Rooms","init_404_rooms","i4r"],["enable302Rooms","en_302_rooms","e3r"],["enableStartWorker","en_start_worker","ensw"],["enablePeak","en_peak","ep"],["peakPeriod","peak_period","pp"],["enableShareFrag","en_share_frag"],["peerCacheDuration","peer_cache_dura"],["enableSuperLatencyRooms","en_super_rooms","eslr"],["enableAppName","an_app_name","ean"],["enableStaticRTC","en_static_rtc"],["rtcReportDeduplication","rtc_report_sort"],["enableRetry403","en_retry_403"],["enableStuckSeek","en_stuck_seek"],["enableReportGap","en_report_gap"],["enableRBAACSafari","en_rb_safari_aac"],["enableStartSeek","en_start_seek"],["startSeekDelay","start_seek_delay"],["startSeekStableRate","start_seek_stable_rate"],["startSeekSampleIntl","start_seek_sample_intl"],["startSeekSampleCnt","start_seek_sample_cnt"],["stunDomain","stun_domain"],["swithSubStreamDelay","switch_sub_stream_delay"]]}},{key:"_getConfFromLocalStorage",value:function(){var r,i;window.localStorage&&(r=this.tp2p.config,i=JSON.parse(localStorage.getItem("TC_CONF"))||{},this._pconfField.forEach(function(e){var t=i[e[2]];void 0!==t&&void 0!==r[e[0]]&&(r[e[0]]=t)}))}},{key:"onStopping",value:function(){}},{key:"starting",value:function(){}},{key:"onConfParsing",value:function(e){var t,r,e=e.conf,i=this.tp2p.config,n=e.pconf,e=e.cconf,a=this._pconfField,o={},s=(a.forEach(function(e){void 0!==n[e[1]]&&(i[e[0]]=n[e[1]],e[2])&&(o[e[2]]=n[e[1]])}),window.localStorage&&localStorage.setItem("TC_CONF",JSON.stringify(o)),n.arch_pcdn);s&&"pcdn"===i.arch&&a.forEach(function(e){void 0!==s[e[1]]&&(i[e[0]]=s[e[1]])}),i.initRollback||!i.p2p?this.tp2p.trigger(c.default.ERROR,{fatal:!0,rollbackCode:u.ReportCode.ROLLBACK,extendReportData:(a={},t=u.ReportField.ROLLBACK_REASON,r=u.ReportField.ROLLBACK_CONF_CONFIG,t in a?Object.defineProperty(a,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):a[t]=r,a)}):(n.tracker&&(i.trackerServer=n.tracker),n.reportserver&&(i.reportServer=n.reportserver),n.report_interval&&6e4<n.report_interval&&(i.reporteInterval=n.report_interval),void 0!==n.h5_p2p&&(i.useP2p=n.h5_p2p&&l.default.supportP2P()),void 0!==e.appid&&(i.partnerId=e.appid),void 0!==e.channelId&&(i.channelId=e.channelId),i.timeOffset=e.verifyTime-Date.now(),i.streams=i.pcdnStreams,this._confRequestCounter+=1,1===this._confRequestCounter?this.tp2p.trigger(c.default.FIRST_CONF_LOADED):this.tp2p.trigger(c.default.CONF_LOADED))}}]),r}(a.default);r.default=e},{132:132,134:134,70:70,71:71}],82:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=d(e(71)),o=d(e(70)),s=d(e(150)),c=d(e(11)),l=d(e(7)),u=e(132),p=d(e(151));function d(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(e){if(this instanceof r)return(e=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,e,a.default.CONF_LOADING))).TAG="ConfLoader",e.config=e.tp2p.config,e._isFirstDownload=!0,e.confFail=0,e._initReporterData(),e.tp2p.registerReportCallback(e.TAG,e.reportCallback.bind(e)),e;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"destroy",value:function(){this.loader&&this.loader.destroy(),o.default.prototype.destroy.call(this),this.tp2p=null,this.config=null,this._isFirstDownload=null}},{key:"onConfLoading",value:function(){var e,t,r;this.loader&&!this.loader._abort||(t=this.config,this.loader=new p.default,e={url:""+t.confPCDNBaseUrl+t.channelId+"?domain="+t.domain+"&s="+window.btoa(t.sourceUrl)+"&d="+t.liveDelayPreset+"&timestamp="+parseInt(Date.now(),10),method:"GET",responseType:"text"},t={timeout:t.pcdnConfTimeout,maxRetry:t.pcdnConfMaxRetry,retryDelay:t.pcdnConfRetryDelay,maxRetryDelay:t.pcdnConfMaxRetryDelay},r={onSuccess:this.loadSuccess.bind(this),onError:this.loadError.bind(this),onTimeout:this.loadTimeout.bind(this)},this.loader.load(e,t,r))}},{key:"loadSuccess",value:function(e,t,r){var i,e=e.data,e=s.default.decrypt(e),n=this.tp2p.config;this._isFirstDownload&&(this._isFirstDownload=!1,(i={i:{}})[u.ReportField.CODE]=u.ReportCode.PHASE_DATA,i[u.ReportField.EVENT_CODE]=u.ReportCode.CONF_OK,i.i[u.ReportField.CONF_DURATION]=t.tload-n.startTime,i.i[u.ReportField.CONF_OK_TIME]=t.tload-t.trequest,this.tp2p.trigger(a.default.REPORT_ONCE,i)),this.loader=null,this.confFail=0,e.cconf.verifyTime=1e3*e.cconf.verifyTime+Math.round((t.tfirst-t.tsend)/2),this.tp2p.trigger(a.default.CONF_PARSING,{conf:e})}},{key:"loadError",value:function(e,t,r){this._reportData[u.ReportField.CONF_ERROR]+=1,this._confFail()}},{key:"loadTimeout",value:function(e,t){this._reportData[u.ReportField.CONF_TIMEOUT]+=1,this._confFail()}},{key:"_confFail",value:function(){this.confFail=1,this.tp2p.trigger(a.default.CONF_FAIL)}},{key:"reportCallback",value:function(){var t=this._reportData;return Object.keys(t).forEach(function(e){t[e]||delete t[e]}),t[u.ReportField.CONF_FAIL]=this.confFail,this._initReporterData(),t}},{key:"_initReporterData",value:function(){this._reportData={};var e=this._reportData;e[u.ReportField.CONF_ERROR]=0,e[u.ReportField.CONF_TIMEOUT]=0,e[u.ReportField.CONF_FAIL]=0}}],[{key:"hmacMD5",value:function(e,t){return window.btoa(new c.default(t,e).toString(l.default))}}]),r}(o.default);r.default=e},{11:11,132:132,150:150,151:151,7:7,70:70,71:71}],83:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});r.RequestType={MAIN_STREAM:1,SUB_STREAM:2,FRAGMENT:3},r.HttpReportType={CODE_404:404,CODE_403:403,CODE_200:200,UNKNOWN_ERROR:-3,DONE:0,TIMEOUT:-1,NETWORK_ERROR:-2,READ_ERROR:-4,REPORT_RES_HEADER:11,REPORT_STATS:12},r.SubLoadingState={LOADING_MAIN:2,LOADING:1,IDLE:0}},{}],84:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=e(71),o=(a=a)&&a.__esModule?a:{default:a},s=e(83),c=e(132);function l(e,t,r){if(!(this instanceof l))throw new TypeError("Cannot call a class as a function");this.TAG="MainStreamController",this.initiator=e,this.tp2p=t,this.switchController=r,this.stateMgr=e.stateMgr,this._initReporterData(),this.tp2p.registerReportCallback(this.TAG,this.reportCallback.bind(this))}i(l,[{key:"destroy",value:function(){this._clearDelayTimer(),this._clearStartTimer()}},{key:"shutDownP2P",value:function(){this.tp2p.config.useP2p=!1,this.tp2p.trigger(o.default.P2P_STOPPING),this.switchController.switchLoading(s.RequestType.MAIN_STREAM),this._reportData[c.ReportField.SHUT_DOWN_P2P]+=1}},{key:"turnOnP2P",value:function(){var e=this;this.tp2p.config.useP2p=!0,this._restartP2P(function(){e._reportData[c.ReportField.TURN_ON_P2P]+=1})}},{key:"onStuckReload",value:function(){var e,t=this;this.stateMgr.reloadingMain||(this.stateMgr.reloadingMain=!0,this._reportData[c.ReportField.STUCK_RELOAD_COUNT]+=1,this.tp2p.trigger(o.default.P2P_STOPPING),this.tp2p.trigger(o.default.STOP_WRITE_SKIP),this.switchController.switchLoading(s.RequestType.MAIN_STREAM),(e=this.tp2p.config.restartP2PDelay)&&!this._delayStartTimer&&(this._delayStartTimer=setTimeout(this._restartP2P.bind(this),e,function(){t._reportData[c.ReportField.STUCK_RELOAD_RECOVER]+=1})))}},{key:"_restartP2P",value:function(e){var t=this;this._clearDelayTimer(),this._restartTimer||(this._restartTimer=setInterval(function(){t._canStartP2P()&&(t._clearStartTimer(),t.switchController.switchLoading(s.RequestType.SUB_STREAM),t.tp2p.trigger(o.default.P2P_STARTING),e)&&e()},1e3))}},{key:"_canStartP2P",value:function(){var e=this.tp2p.config,t=Math.max(e.pcdnLiveDelay-e.p2pWindow-2,2),r=e.pcdnLiveDelay;return e.useP2p&&this.tp2p.bufferLength>t&&this.stateMgr.realDelay>r}},{key:"_clearDelayTimer",value:function(){this._delayStartTimer&&(clearTimeout(this._delayStartTimer),this._delayStartTimer=null)}},{key:"_clearStartTimer",value:function(){this._restartTimer&&(clearInterval(this._restartTimer),this._restartTimer=null)}},{key:"onLoadError",value:function(){}},{key:"_initReporterData",value:function(){this._reportData={};var e=this._reportData;e[c.ReportField.STUCK_RELOAD_COUNT]=0,e[c.ReportField.STUCK_RELOAD_RECOVER]=0,e[c.ReportField.SHUT_DOWN_P2P]=0,e[c.ReportField.TURN_ON_P2P]=0}},{key:"reportCallback",value:function(){var t=this._reportData;return Object.keys(t).forEach(function(e){t[e]||delete t[e]}),this._initReporterData(),t}}]),r.default=l},{132:132,71:71,83:83}],85:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=y(e(97)),_=e(132),o=y(e(71)),s=y(e(70)),c=y(e(139)),l=e(83),u=e(146),p=y(e(15)),d=y(e(140)),h=y(e(135)),f=e(136);function y(e){return e&&e.__esModule?e:{default:e}}function v(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}e=function(e){var t=n;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function n(e,t,r){var i;if(this instanceof n)return(i=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,t,o.default.FIRST_CONF_LOADED))).TAG="PCDNController",i.storage=e.storage,i.stateMgr=e.stateMgr,i.patchMgr=e.patchMgr,i.storageMgr=e.storageMgr,i.streamController=r,i.baseSub=e.pcdnStreams,i.xStreamId=e.xStreamId,i.tp2p=t,i._loadQueue=[],i._downloadServers=new Map,i._idCount=0,i._nonSliceVideoTagCnt=0,i._mediaRecoder={aac:null,avc:null},i._initReporterData(),i.tp2p.registerReportCallback(i.TAG,i.reportCallback.bind(i)),i;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(n,[{key:"destroy",value:function(){this._downloadServers.forEach(function(e){e.destroy()}),this._downloadServers=null,this._collectTimeout&&clearInterval(this._collectTimeout),this._reacquireTimer&&(clearTimeout(this._reacquireTimer),this._reacquireTimer=null)}},{key:"onFirstConfLoaded",value:function(){}},{key:"setStreamId",value:function(e){this.streamId=e,this.domain={main:this.tp2p.config.pcdnMixed}}},{key:"load",value:function(){var e,t,r,i,n=this,a=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};if(!this.streamId)throw Error("no streamId");if(a&&~[l.RequestType.MAIN_STREAM,l.RequestType.SUB_STREAM,l.RequestType.FRAGMENT].indexOf(a.type))return e=this.tp2p.config,!(t="")&&"local"!==e.authMode||(a.query=Object.assign(a.query||{},(t=e.localSecKey||(0,u.genKey)(t),n=n.streamId,r=Math.round((Date.now()+6e5)/1e3).toString(16),v(i={},(0,u.genKey)([116,120,83,101,99,114,101,116]),(0,p.default)(""+t+n+r).toString()),v(i,(0,u.genKey)([116,120,84,105,109,101]),r),i))),a.query=Object.assign(a.query||{},this.appendParam(e.forwardParams)),this._idCount+=1,this._loadQueue.push({id:this._idCount,config:a}),this._load(),this._idCount}},{key:"appendParam",value:function(e){var r={},i=this.tp2p.config.params;return e.forEach(function(e){var t=i.get(e);!t&&""!==t||(r[e]=t)}),r}},{key:"abort",value:function(e){this._downloadServers.has(e)&&(this._downloadServers.get(e).destroy(),this._downloadServers.delete(e))}},{key:"_genStreamDomain",value:function(){return this.tp2p.config.redirectedURL?this.tp2p.config.redirectedURL+"/"+this.domain.main:""+this.domain.main}},{key:"_genDownloadUrl",value:function(t){var e=this._genStreamDomain(t),r=this.tp2p.config.appName,i=this.tp2p.config.enableAppName,e="https://"+(e+"/")+(r&&i?r+"/":"")+this.streamId+".xs",n=new h.default(this.tp2p.config.params.toString()),i=(Object.keys(t.query||{}).forEach(function(e){n.set(e,t.query[e])}),n.toString()||""),r=e+(i?"?"+i:"");return r}},{key:"_statisticPlayStart",value:function(){var e=this.tp2p.config;e.realLoadStart||(e.realLoadStart=Date.now())}},{key:"_load",value:function(){var s=this,i=this.tp2p.config;this._loadQueue.forEach(function(e){var t=e.id,e=e.config,r=s._genDownloadUrl(e),r=(e.type===l.RequestType.MAIN_STREAM&&s._statisticPlayStart(),"remote"===i.authMode&&(e.headers=(0,f.createAuthHeader)(i.partner,i.xp2pAppKey,i.domain,r)),new a.default(r,t,e.type,e));r.on("found_slice",function(e){var t=e.sliceId,r=e.payload,i=e.tagTime,e=e.requestType;t>=s.stateMgr.nextWriteId&&!s.storage.has(t)&&(s._reportData[_.ReportField.CDN_DOWNLOAD_USEFUL_BYTES]+=r.length),s._storage(t,r,i),t%s.baseSub!==s.xStreamId||e===l.RequestType.FRAGMENT&&!s.tp2p.config.enableShareFrag||s.tp2p.trigger(o.default.CDN_LOADED,{sliceId:t,payload:r,tagTime:i}),e===l.RequestType.FRAGMENT?s._processFragSlice(t):e===l.RequestType.MAIN_STREAM?s._processMainSlice(t):e===l.RequestType.SUB_STREAM&&s._processSubSlice(t),s._setStatus(e,t),s._detectBigHole(t)}),r.on("found_flv_head",function(e){var t=e.startSliceId,e=e.payload;s.tp2p.trigger(o.default.FLV_HEAD_LOADED,{startSliceId:t,payload:e}),s.stateMgr.playStart||(t=c.default.pageVisibility(),s.tp2p.trigger(o.default.LOADED_DATA),s._reportData[_.ReportField.FIRST_DOWNLOAD_TIME]=Date.now()-i.loadTime,s._reportData[_.ReportField.REAL_LOAD_DURATION]=Date.now()-i.realLoadStart,s._reportData[_.ReportField.LOADING_PAGE_VISIBILITY]=t.pageVisible)}),r.on("frag_loaded",function(e,t){t===l.RequestType.FRAGMENT&&(s._reportData[_.ReportField.FILL_BYTES]+=e),s._reportData[_.ReportField.CDN_DOWNLOAD_BYTES]+=e,s.tp2p.flow.CBS+=e*(i.bFix||1),s.tp2p.flow.CBT+=e*(i.bFix||1),s.tp2p.flow.CBU+=e*(i.bFix||1)}),r.on("http_status",function(e){var t=e.serverId,r=e.requestType,i=e.requestConfig,n=e.httpReportType,a=e.data,o=e.redirectedURL;switch(n){case l.HttpReportType.CODE_200:s._setRedirectedURL(o),s.streamController.onResOk(t,r,i);break;case l.HttpReportType.DONE:s.streamController.onFinished(t,r,i);break;case l.HttpReportType.NETWORK_ERROR:s.streamController.onNetworkError(t,r,i);break;case l.HttpReportType.TIMEOUT:s.streamController.onTimeout(t,r,i);break;case l.HttpReportType.UNKNOWN_ERROR:s.streamController.onUnknownError(t,r,i);break;case l.HttpReportType.CODE_403:s.streamController.onForbidden(t,r,i);break;case l.HttpReportType.CODE_404:s.streamController.onNotFound(t,r,i);break;case l.HttpReportType.READ_ERROR:s.streamController.onReadError(t,r,i);break;case l.HttpReportType.REPORT_RES_HEADER:s.streamController.onResHeader(t,r,i,a)}}),r.on("slice_error",function(e){var t=e.sliceId,e=e.diff;s._reportData[_.ReportField.ERR_SLICE_FRAME_COUNT]+=t+"_"+e+","}),r.on("sequence_header",function(e){var t=e.type,e=e.tag,r=s.tp2p.config;"Safari"===r.browser.name&&r.enableRBAACSafari&&(null===s._mediaRecoder[t]?s._mediaRecoder[t]=e:d.default.CompareTagData(s._mediaRecoder[t],e)||s.streamController.onSafariSequenceChanged(t))}),r.on("video_tag",function(){s.stateMgr.playStart||(0<=s._nonSliceVideoTagCnt&&(s._nonSliceVideoTagCnt+=1),5<s._nonSliceVideoTagCnt&&(s.streamController.onSliceNotFound(),s._nonSliceVideoTagCnt=-1))}),s._downloadServers.set(t,r)}),this._loadQueue=[]}},{key:"_processMainSlice",value:function(e){this.stateMgr.firstSubSliceId&&e>this.stateMgr.firstSubSliceId+3&&this.streamController.stopMainStream();var t=!1;this.stateMgr.lastMainSliceId&&this.stateMgr.lastMainSliceId!==e-1&&(this._reportData[_.ReportField.ERR_MAIN_NOT_CON]+=1,t=!0),this.stateMgr.loadingMain&&this.tp2p.trigger(o.default.FOUND_SLICE,{sliceId:t?e:0}),this.stateMgr.lastMainSliceId=e}},{key:"_processSubSlice",value:function(e){this.stateMgr.firstSubSliceId||(this.stateMgr.firstSubSliceId=e)}},{key:"_setRedirectedURL",value:function(e){e&&!this.tp2p.config.redirectedURL&&(e=e.slice(e.indexOf("://")+3),this.tp2p.config.redirectedURL=e.slice(0,e.indexOf("/")))}},{key:"_processFragSlice",value:function(e){this.tp2p.trigger(o.default.FILL_FRAGMENT_SUCCESS,{sliceArray:[e]})}},{key:"_setStatus",value:function(e,t){this.stateMgr.setSubStreamId(t),this.patchMgr.sliceArrive(t,"cdn"),e!==l.RequestType.MAIN_STREAM&&e!==l.RequestType.SUB_STREAM||(this.stateMgr.notFoundCounter=0)}},{key:"_storage",value:function(e,t,r){this.storage.set(e,{payload:t,tagTime:r}),this.storageMgr.cleanStorage()}},{key:"_detectBigHole",value:function(e){var t,r;void 0!==this._lastSliceId&&(t=Math.abs(e-this._lastSliceId),r=this._reportData,t>this.tp2p.config.bigHole&&(r[_.ReportField.BIG_HOLE]+=1),5e4<t?r[_.ReportField.HOLE_SIZE_3]+=1:5e3<t?r[_.ReportField.HOLE_SIZE_2]+=1:500<t&&(r[_.ReportField.HOLE_SIZE_1]+=1)),this._lastSliceId=e}},{key:"_collectTimelineInfo",value:function(){var h=this,f=this.tp2p.config.timeOffset;void 0!==f&&(this._collectTimeout&&clearInterval(this._collectTimeout),this._collectTimeout=setInterval(function(){var e=h.stateMgr;if(0==Math.round((Date.now()+f)/1e3)%60){for(var t=Math.floor(e.playId),r=e.maxSliceId,i=e.baseId+Math.floor((Date.now()-e.baseTime)/1e3)*e.frameRate,n={},a={},o={},s={},c=0;c<h.baseSub;c++){var l=e.getMaxSubStreamId(c),u="s"+c;n[u]=l-t,a[u]=e.getLastSubStreamId(c)-t,o[u]=l-i,s[u]=r-l}var p=r-t,d=r-i;h._reportData[_.ReportField.DIFF_SUB_MAX_TO_PLAY]=n,h._reportData[_.ReportField.DIFF_SUB_CON_TO_PLAY]=a,h._reportData[_.ReportField.DIFF_SUB_MAX_TO_IDLE]=o,h._reportData[_.ReportField.DIFF_MAX_TO_SUB_MAX]=s,h._reportData[_.ReportField.DIFF_MAX_TO_PLAY]=p,h._reportData[_.ReportField.DIFF_MAX_TO_IDLE]=d}},1e3))}},{key:"_initReporterData",value:function(){this._reportData={};var e=this._reportData;e[_.ReportField.CDN_DOWNLOAD_BYTES]=0,e[_.ReportField.CDN_BYTES]=0,e[_.ReportField.CDN_DOWNLOAD_USEFUL_BYTES]=0,e[_.ReportField.FILL_BYTES]=0,e[_.ReportField.FIRST_DOWNLOAD_TIME]=0,e[_.ReportField.ERR_SLICE_FRAME_COUNT]="",e[_.ReportField.LOADING_PAGE_VISIBILITY]="",e[_.ReportField.ERR_MAIN_NOT_CON]=0,e[_.ReportField.CUR_WRITE_ID]=0,e[_.ReportField.DIFF_SUB_MAX_TO_PLAY]={},e[_.ReportField.DIFF_SUB_CON_TO_PLAY]={},e[_.ReportField.DIFF_SUB_MAX_TO_IDLE]={},e[_.ReportField.DIFF_MAX_TO_SUB_MAX]={},e[_.ReportField.DIFF_MAX_TO_PLAY]=0,e[_.ReportField.DIFF_MAX_TO_IDLE]=0,e[_.ReportField.BIG_HOLE]=0,e[_.ReportField.HOLE_SIZE_3]=0,e[_.ReportField.HOLE_SIZE_2]=0,e[_.ReportField.HOLE_SIZE_1]=0,e[_.ReportField.REAL_LOAD_DURATION]=0,e[_.ReportField.APP_NAME]="",e[_.ReportField.EN_APP_NAME]=!1,e[_.ReportField.REDIRECT_OC]="",e[_.ReportField.FRAME_RATE]=0}},{key:"reportCallback",value:function(){var t=this._reportData,e=(t[_.ReportField.FRAME_RATE]=this.stateMgr.frameRate,t[_.ReportField.CDN_BYTES]=t[_.ReportField.CDN_DOWNLOAD_BYTES],t[_.ReportField.CUR_WRITE_ID]=this.stateMgr.nextWriteId-1,this.tp2p.config),r=(t[_.ReportField.APP_NAME]=e.appName,t[_.ReportField.EN_APP_NAME]=e.enableAppName,t[_.ReportField.REDIRECT_OC]=e.redirectedURL||"",[_.ReportField.DIFF_SUB_MAX_TO_PLAY,_.ReportField.DIFF_SUB_CON_TO_PLAY,_.ReportField.DIFF_SUB_MAX_TO_IDLE,_.ReportField.DIFF_MAX_TO_SUB_MAX].forEach(function(e){Object.keys(t[e]).length||delete t[e]}),[_.ReportField.CDN_DOWNLOAD_BYTES,_.ReportField.CDN_BYTES,_.ReportField.CDN_DOWNLOAD_USEFUL_BYTES,_.ReportField.CUR_WRITE_ID]);return Object.keys(t).forEach(function(e){-1!==r.indexOf(e)||t[e]||delete t[e]}),this._initReporterData(),t}}]),n}(s.default);r.default=e},{132:132,135:135,136:136,139:139,140:140,146:146,15:15,70:70,71:71,83:83,97:97}],86:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var o=_(e(85)),l=e(132),s=e(69),u=_(e(71)),a=_(e(70)),c=_(e(142)),p=e(83),d=_(e(84)),h=_(e(88)),f=_(e(143));function _(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=a;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function a(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},i=this,n=a;if(i instanceof n)return(i=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,t,u.default.PULL_SUB_STREAM,u.default.STUCK_RELOAD,u.default.TURN_ON_P2P,u.default.SHUT_DOWN_P2P))).TAG="StreamSwitchController",i.config=r,i.stateMgr=e.stateMgr,i.storage=e.storage,i._downloadTask=new Map,i._fragmentReqLen=0,i._loadRecord=new Map,i._retrySub=new Map,i.tp2p=t,i._fragmentCounter=0,i._rollback=!1,i._lastTryTime=Date.now(),i._testResHeader={fullQUIC:!1,fullTLS13:!1,subQUIC:!1,subTLS13:!1},i.pcdnController=new o.default(e,t,i),i.mainStreamController=new d.default(e,t,i),i._initReporterData(),i.tp2p.registerReportCallback(i.TAG,i.reportCallback.bind(i)),i;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(a,[{key:"onShutDownP2p",value:function(){this.mainStreamController.shutDownP2P()}},{key:"onTurnOnP2p",value:function(){this.mainStreamController.turnOnP2P()}},{key:"onPullSubStream",value:function(e){e=e.subStreamId;this.loadSubStream(e,!0)}},{key:"onStuckReload",value:function(){this.mainStreamController.onStuckReload()}},{key:"destroy",value:function(){this._clearTimer(),this.mainStreamController.destroy(),this.pcdnController.destroy(),this._downloadTask.clear()}},{key:"_clearTimer",value:function(){this._retryMainTimer&&clearTimeout(this._retryMainTimer),clearTimeout(this._timeout),clearTimeout(this._killTimer)}},{key:"startLoad",value:function(e){this.pcdnController.setStreamId(e),this.initLoad()}},{key:"initLoad",value:function(){this.switchLoading(p.RequestType.MAIN_STREAM),this._trySub()}},{key:"_trySub",value:function(){var e=this;this._timeout=setTimeout(function(){!(Date.now()-e._lastTryTime<e.tp2p.config.swithSubStreamDelay)&&(e._lastTryTime=Date.now(),e._allowSwitching())?(e._reportData[l.ReportField.SWITCH_MOMENT]=Date.now()-e.tp2p.config.loadTime,e.switchLoading(p.RequestType.SUB_STREAM),e.tp2p.trigger(u.default.TRACKER_STARTING)):e._trySub()},1e3)}},{key:"_allowSwitching",value:function(){var e=!0;return e=!this.stateMgr.playStart||this.tp2p.bufferLength<this.tp2p.config.switchSubMinBufLen?!1:e}},{key:"switchLoading",value:function(e){var t=this;switch(e){case p.RequestType.MAIN_STREAM:this.stateMgr.loadingMain=!0,this._abortAllRequest(),this.loadMainStream();break;case p.RequestType.SUB_STREAM:this.stateMgr.loadingMain=!1,this._killTimer=setTimeout(function(){t.stopMainStream()},4e3);for(var r=this.config.baseSub,i=0;i<r;i++)this.loadSubStream(i)}}},{key:"_abortAllRequest",value:function(){var r=this;this._downloadTask.forEach(function(e,t){r.abort({},t)})}},{key:"loadMainStream",value:function(){this.stateMgr.loadMainReset(),this.stateMgr.stopAutoFetchSubStreamOnce();var e=this._getMainConfig();return this._load(e)}},{key:"stopMainStream",value:function(){var e=this._getMainConfig();this.abort(e)}},{key:"loadSubStream",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],r=this._getSubConfig(e);return t&&(t=this._getSubConfig(e),t=this.getRequestKey(t),t=this._loadRecord.get(t))&&Date.now()-t>this.tp2p.config.subStreamLoadInterval&&this.stopSubStream(e),this.stateMgr.setSubStreamState(e,p.SubLoadingState.LOADING),this._load(r)}},{key:"stopSubStream",value:function(e){e=this._getSubConfig(e);this.abort(e)}},{key:"loadFragments",value:function(e){var t=this.tp2p.config.fillMaxTaskSize;if(this._fragmentReqLen>=t)return this._reportData[l.ReportField.FILL_FULL_QUE]+=1,this.tp2p.trigger(u.default.FILL_FRAGMENT_FAIL,{sliceArray:e}),-1;this._fragmentCounter+=1;t=this._getFragmentConfig(e);return this._load(t)}},{key:"_load",value:function(e){var t=this.getRequestKey(e);if(!this._downloadTask.has(t)){switch(e.type){case p.RequestType.SUB_STREAM:this._reportData[l.ReportField.LOAD_SUB_COUNT]+=1;break;case p.RequestType.FRAGMENT:this._fragmentReqLen+=1}this._loadRecord.set(t,Date.now());e=this.pcdnController.load(e);return this._downloadTask.set(t,e),e}}},{key:"abort",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",r=(e&&e.type===p.RequestType.SUB_STREAM&&this.stateMgr.setSubStreamState(e.query.substream,p.SubLoadingState.IDLE),t||this.getRequestKey(e));this._downloadTask.has(r)&&((-1<t.indexOf("fragment")||e.type===p.RequestType.FRAGMENT)&&--this._fragmentReqLen,this.pcdnController.abort(this._downloadTask.get(r)),this._downloadTask.delete(r))}},{key:"_getMainConfig",value:function(){var e={type:p.RequestType.MAIN_STREAM,enableAbortController:this.tp2p.config.enableAbortController,query:{playid:this.tp2p.config.randomPlayId,uuid:c.default.myUUID().UUID||"error"}},t=this._specifyStreamDelay();return t&&(e.query.delay=t),e}},{key:"_specifyStreamDelay",value:function(){var e;return!this.stateMgr.playStart&&(e=this.tp2p.config,h.default.SpecifyDelayRoom(this.pcdnController.streamId,e.assignDelayRooms))?e.assignDelay:0}},{key:"_getSubConfig",value:function(e){return{type:p.RequestType.SUB_STREAM,enableAbortController:this.tp2p.config.enableAbortController,query:{startid:this.stateMgr.getLastSubStreamId(e)+this.config.baseSub,substream:e,basesub:this.config.baseSub,playid:this.tp2p.config.randomPlayId,uuid:c.default.myUUID().UUID||"error"}}}},{key:"_getFragmentConfig",value:function(e){var t=this.tp2p.config;return{type:p.RequestType.FRAGMENT,timeout:t.fillTimeout||2e3,enableAbortController:t.enableAbortController,query:{slicenum:e.join(","),playid:t.randomPlayId,uuid:c.default.myUUID().UUID||"error",reqid:this._fragmentCounter},context:{start:Date.now()}}}},{key:"getRequestKey",value:function(e){switch(e.type){case p.RequestType.MAIN_STREAM:return"main";case p.RequestType.SUB_STREAM:return"sub"+e.query.substream;case p.RequestType.FRAGMENT:return"fragment"+this._fragmentCounter}}},{key:"findKeyByServerId",value:function(e){var t=!0,r=!1,i=void 0;try{for(var n,a=this._downloadTask[Symbol.iterator]();!(t=(n=a.next()).done);t=!0){var o=n.value;if(o[1]===e)return o[0]}}catch(e){r=!0,i=e}finally{try{!t&&a.return&&a.return()}finally{if(r)throw i}}}},{key:"onResOk",value:function(e,t,r){t===p.RequestType.SUB_STREAM?this._retrySub.delete(e):t===p.RequestType.FRAGMENT?(this._reportData[l.ReportField.FILL_REQ_OK]+=1,r.ok=!0):t===p.RequestType.MAIN_STREAM&&(this._retrySub.delete(e),this.stateMgr.playStart||(r=this.tp2p.config.realLoadStart,this._reportData[l.ReportField.RES_OK]=Date.now()-r))}},{key:"onNotFound",value:function(e,t,r){switch(t){case p.RequestType.SUB_STREAM:this._notFoundDecision(),this._processFail(e,t,r,l.ReportField.ROLLBACK_STREAM_404);break;case p.RequestType.MAIN_STREAM:this._notFoundDecision()||this._processFail(e,t,r,l.ReportField.ROLLBACK_STREAM_404);break;case p.RequestType.FRAGMENT:this._processFail(e,t,r,l.ReportField.ROLLBACK_STREAM_404)}}},{key:"_notFoundDecision",value:function(){return this.stateMgr.playStart?!!this._monitorNotFound(this.tp2p.config.rollback404)&&(this.tp2p.config.rollback404?this._reportAndRollback(l.ReportField.ROLLBACK_STREAM_404):this._reportStreamException(l.ReportCode.END_OF_STREAM),!0):(h.default.InitStreamNotFound(this.pcdnController.streamId,this.tp2p.config.init404Rooms)?(this._reportData[l.ReportField.ABNORMAL_REQUEST]=1,this._reportStreamException(l.ReportCode.STREAM_NOT_FOUND)):this._reportAndRollback(l.ReportField.ROLLBACK_STREAM_404),!0)}},{key:"_monitorNotFound",value:function(e){return!!e||(this.stateMgr.notFoundCounter+=1,this.stateMgr.notFoundCounter>=this.tp2p.config.eos404Limit)}},{key:"onForbidden",value:function(e,t,r){this._processFail(e,t,r,l.ReportField.ROLLBACK_STREAM_403)}},{key:"onNetworkError",value:function(e,t,r){this._processFail(e,t,r,l.ReportField.ROLLBACK_NETWORK_ERR)}},{key:"onFinished",value:function(e,t,r){this._processFail(e,t,r,l.ReportField.ROLLBACK_STREAM_END)}},{key:"onUnknownError",value:function(e,t,r){this._processFail(e,t,r,l.ReportField.ROLLBACK_STREAM_UNKNOWN_ERR)}},{key:"onTimeout",value:function(e,t,r){this._processFail(e,t,r,l.ReportField.ROLLBACK_STREAM_TIMEOUT)}},{key:"onReadError",value:function(e,t,r){this._processFail(e,t,r,l.ReportField.ROLLBACK_READ_ERR)}},{key:"onResHeader",value:function(e,t,r,i){var n=this._testResHeader,a=this._parseStreamHeader(i),o=a.findQUIC,s=a.findTLS13;switch(t){case p.RequestType.MAIN_STREAM:n.fullQUIC||this.stateMgr.playStart||(n.fullQUIC=o)&&f.default.StorageVar("full_quic",!0),n.fullTLS13||this.stateMgr.playStart||(n.fullTLS13=s);break;case p.RequestType.SUB_STREAM:(n.subQUIC=o)&&f.default.StorageVar("sub_quic",!0),n.subTLS13=s;break;case p.RequestType.FRAGMENT:this.tp2p.config.parseHeader&&this._parseHeader(i,r)}}},{key:"_parseStreamHeader",value:function(e){e=e.header;return{findQUIC:!!e["quic-info"],findTLS13:"TLSv1.3"===(e["X-SSL-PROTOCOL"]||e["x-ssl-protocol"])}}},{key:"_parseHeader",value:function(e,t){var r,i,e=e.header,e=e["x-tlive-detailerrmsg"]||e["X-Tlive-DetailErrMsg"];-1!==["null","NULL"].indexOf(e)?t.noErrDetailHeader=!0:(e=(t=this._parseErrMsg(e)).sliceRange,r=t.earlySlice,i=t.missSlice,t=t.expireSlice,e.length&&(this.stateMgr.fillSliceRange=e),(e=i.concat(t)).length&&this.tp2p.trigger(u.default.FILL_FRAGMENT_FAIL,{sliceArray:e,failReason:l.ReportField.ROLLBACK_STREAM_404}),(e=this._reportData)[l.ReportField.EARLY_COUNT]+=r.length,e[l.ReportField.EXPIRE_COUNT]+=t.length,e[l.ReportField.MISS_COUNT]+=i.length)}},{key:"_parseErrMsg",value:function(e){var t,r,i=[],n=[],a=[],o=[];return e&&(t=(e=e.split("/"))[1],e[0]&&(i=e[0].split("-").map(function(e){return+e||0})),t)&&(t=(e=t.split(";"))[0],r=e[1],e=e[2],t&&(n=t.split(",").map(function(e){return+e})),r&&(a=r.split(",").map(function(e){return+e})),e)&&(o=e.split(",").map(function(e){return+e})),{sliceRange:i,earlySlice:n,missSlice:a,expireSlice:o}}},{key:"onSliceNotFound",value:function(){this._reportAndRollback(l.ReportField.ROLLBACK_SLICE_NOT_FOUND)}},{key:"onSafariSequenceChanged",value:function(e){e="aac"===e?l.ReportField.ROLLBACK_SAFARI_AAC:l.ReportField.ROLLBACK_SAFARI_AVC;this._reportAndRollback(e)}},{key:"_processFail",value:function(e,t,r,i){var n,a,o,s,c=this;t===p.RequestType.MAIN_STREAM?(this.stateMgr.playStart||this._reportFullError(i),n=this.tp2p.config.mainMaxRetryCount-1,a=this._retrySub.get(e)||0,i===l.ReportField.ROLLBACK_STREAM_403&&!this.tp2p.config.enableRetry403||n<a&&i!==l.ReportField.ROLLBACK_STREAM_404?this._reportAndRollback(i):(this._retrySub.delete(e),this.abort(r),this._retryMainTimer=setTimeout(function(){var e=c.loadMainStream();c._retrySub.set(e,a+1)},this.tp2p.config.retryMainDelay))):t===p.RequestType.SUB_STREAM?(this._reportLoadSubError(i),this.abort(r)):t===p.RequestType.FRAGMENT&&(o=[],r.query.slicenum.split(",").forEach(function(e){o.push(+e)}),i===l.ReportField.ROLLBACK_STREAM_END?Date.now()-r.context.start<=1e3?this._reportData[l.ReportField.FILL_DURATION].ms1000+=1:this._reportData[l.ReportField.FILL_DURATION].ms2000+=1:i===l.ReportField.ROLLBACK_STREAM_404?(this._reportData[l.ReportField.FILL_REQ_404]+=1,this._reportData[l.ReportField.FILL_REQ_404]+this._reportData[l.ReportField.FILL_BAK_404]<8&&(this._reportData[l.ReportField.FILL_404_ID]+=o[0]+"-"+o[o.length-1]+"-"+this.stateMgr.nextWriteId+"-"+this.stateMgr.maxSliceId+"-"+(r.bakOC?"bak":"sug")+",")):i===l.ReportField.ROLLBACK_STREAM_TIMEOUT&&(this._reportData[l.ReportField.FILL_REQ_TIMEOUT]+=1,t.ok&&(this._reportData[l.ReportField.FILL_TIMEOUT_OK]+=1),this._reportData[l.ReportField.FILL_TIMEOUT_ID]+=r.query.reqid+"-"+!!r.ok+"-"+this.tp2p.bufferLength+"'}, "),this.abort("",this.findKeyByServerId(e)),this.tp2p.config.parseHeader?i===l.ReportField.ROLLBACK_STREAM_404&&r.noErrDetailHeader&&0<o.length&&this.tp2p.trigger(u.default.FILL_FRAGMENT_FAIL,{sliceArray:o,failReason:i}):(s=[],o.forEach(function(e){c.storage.has(e)||s.push(e)}),0<s.length&&this.tp2p.trigger(u.default.FILL_FRAGMENT_FAIL,{sliceArray:s,failReason:i})))}},{key:"_reportFullError",value:function(e){var t=void 0;switch(e){case l.ReportField.ROLLBACK_STREAM_404:t=l.ReportField.FULL_404;break;case l.ReportField.ROLLBACK_STREAM_403:t=l.ReportField.FULL_403;break;case l.ReportField.ROLLBACK_NETWORK_ERR:t=l.ReportField.FULL_NET_ERR;break;case l.ReportField.ROLLBACK_STREAM_UNKNOWN_ERR:t=l.ReportField.FULL_UN_ERR;break;case l.ReportField.ROLLBACK_STREAM_TIMEOUT:t=l.ReportField.FULL_TIMEOUT;break;case l.ReportField.ROLLBACK_READ_ERR:t=l.ReportField.FULL_READ_ERR}t&&(this._reportData[t]+=1)}},{key:"_reportLoadSubError",value:function(e){var t=void 0;switch(e){case l.ReportField.ROLLBACK_STREAM_404:t=l.ReportField.NOT_FOUND_COUNT;break;case l.ReportField.ROLLBACK_STREAM_403:t=l.ReportField.FORBIDDEN_COUNT;break;case l.ReportField.ROLLBACK_NETWORK_ERR:t=l.ReportField.NETWORK_ERROR_COUNT;break;case l.ReportField.ROLLBACK_STREAM_END:t=l.ReportField.FINISHED_COUNT;break;case l.ReportField.ROLLBACK_STREAM_UNKNOWN_ERR:t=l.ReportField.UNKNOWN_ERROR_COUNT;break;case l.ReportField.ROLLBACK_STREAM_TIMEOUT:t=l.ReportField.TIMEOUT_COUNT;break;case l.ReportField.ROLLBACK_READ_ERR:t=l.ReportField.READ_ERROR_COUNT}t&&(this._reportData[t]+=1)}},{key:"_reportAndRollback",value:function(e){var t,r;this._rollback||(this._rollback=!0,this.tp2p.trigger(u.default.ERROR,{type:s.ErrorTypes.NETWORK_ERROR,fatal:!0,rollbackCode:l.ReportCode.ROLLBACK,extendReportData:(t={},r=l.ReportField.ROLLBACK_REASON,e=e,r in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t)}))}},{key:"_reportStreamException",value:function(e){this._rollback||(this._rollback=!0,this.tp2p.trigger(u.default.ERROR,{type:s.ErrorTypes.NETWORK_ERROR,fatal:!0,rollbackCode:e}))}},{key:"_initReporterData",value:function(){this._reportData={};var e=this._reportData;e[l.ReportField.FILL_REQ_OK]=0,e[l.ReportField.FILL_SLICE_OK]=0,e[l.ReportField.RES_OK]=0,e[l.ReportField.FILL_REQ_404]=0,e[l.ReportField.FILL_REQ_TIMEOUT]=0,e[l.ReportField.FILL_TIMEOUT_OK]=0,e[l.ReportField.FILL_FULL_QUE]=0,e[l.ReportField.FILL_404_ID]="",e[l.ReportField.FILL_TIMEOUT_ID]="",e[l.ReportField.FILL_DURATION]={ms1000:0,ms2000:0},e[l.ReportField.SUB_MAX_ID]="",e[l.ReportField.SUB_CONTINUOUS_ID]="",e[l.ReportField.FILL_REQ_OK]=0,e[l.ReportField.FILL_BAK_404]=0,e[l.ReportField.FILL_BAK_TIMEOUT]=0,e[l.ReportField.STREAM_STATE]="",e[l.ReportField.LOAD_SUB_COUNT]=0,e[l.ReportField.NOT_FOUND_COUNT]=0,e[l.ReportField.FORBIDDEN_COUNT]=0,e[l.ReportField.NETWORK_ERROR_COUNT]=0,e[l.ReportField.FINISHED_COUNT]=0,e[l.ReportField.UNKNOWN_ERROR_COUNT]=0,e[l.ReportField.TIMEOUT_COUNT]=0,e[l.ReportField.READ_ERROR_COUNT]=0,e[l.ReportField.EARLY_COUNT]=0,e[l.ReportField.MISS_COUNT]=0,e[l.ReportField.EXPIRE_COUNT]=0,e[l.ReportField.SWITCH_MOMENT]=0,e[l.ReportField.SUB_QUIC]=!1,e[l.ReportField.SUB_TLS13]=!1,e[l.ReportField.FULL_QUIC]=!1,e[l.ReportField.FULL_TLS13]=!1,e[l.ReportField.FULL_404]=0,e[l.ReportField.FULL_403]=0,e[l.ReportField.FULL_NET_ERR]=0,e[l.ReportField.FULL_READ_ERR]=0,e[l.ReportField.FULL_UN_ERR]=0,e[l.ReportField.FULL_TIMEOUT]=0,e[l.ReportField.FULL_QUIC_USER]=!1,e[l.ReportField.SUB_QUIC_USER]=!1,e[l.ReportField.ENABLE_302]=!1}},{key:"reportCallback",value:function(){for(var e=this._testResHeader,t=(this._reportData[l.ReportField.FULL_TLS13]=e.fullTLS13,this._reportData[l.ReportField.FULL_QUIC]=e.fullQUIC,this._reportData[l.ReportField.SUB_TLS13]=e.subTLS13,this._reportData[l.ReportField.SUB_QUIC]=e.subQUIC,this._reportData[l.ReportField.FULL_QUIC_USER]=f.default.ReadVar("full_quic"),this._reportData[l.ReportField.SUB_QUIC_USER]=f.default.ReadVar("sub_quic"),this._reportData[l.ReportField.STREAM_STATE]=this.stateMgr.getAllSubStreamState(),0===this._reportData[l.ReportField.FILL_REQ_OK]&&delete this._reportData[l.ReportField.FILL_DURATION],this.stateMgr),r=[],i=[],n=0;n<this.config.baseSub;n++)r.push(t.getMaxSubStreamId(n)),i.push(t.getLastSubStreamId(n));this._reportData[l.ReportField.SUB_MAX_ID]=r.join("-"),this._reportData[l.ReportField.SUB_CONTINUOUS_ID]=i.join("-");var a=this._reportData;return Object.keys(a).forEach(function(e){a[e]||delete a[e]}),this._initReporterData(),a}}]),a}(a.default);r.default=e},{132:132,142:142,143:143,69:69,70:70,71:71,83:83,84:84,85:85,88:88}],87:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.SubStreamStatus=void 0;var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var c=e(132),e=e(71),l=(e=e)&&e.__esModule?e:{default:e};var u=r.SubStreamStatus={OUT_GAP:0,IN_GAP:1},e=(i(a,[{key:"destroy",value:function(){this._streamState=[],clearInterval(this._checkTimeout)}},{key:"_getSubStreamStatus",value:function(e){var t=this.stateMgr,r=t.p2pBoundId,i=t.frameRate,n=this.tp2p.config.ssmResGap2PR;if(!n)return u.OUT_GAP;for(var a=t.getLastSubStreamId(e),t=t.getMaxSubStreamId(e),o=a+this.baseSub,s=t;o<=s;o+=this.baseSub)this.storage.has(o)&&(a+=this.baseSub);return r+Math.round(n/1e3*i)<=a?u.OUT_GAP:u.IN_GAP}},{key:"_checkSubStream",value:function(){if(this._streamState.length){for(var e=this.stateMgr.loadingMain,t=null,r=0;r<this.baseSub;r++)var i=this.stateMgr.getLastSubStreamId(r),t=Math.min(t||i,i);if(!e&&null!==t){var e=t%this.baseSub,n=this._getSubStreamStatus(e),a=this._streamState[e].cdn;if(n===u.IN_GAP){var n=Date.now(),o=this.tp2p.config,s=o.ssmContinuousCount,o=o.ssmEmitInterval;if(n-a.ts>=o)return a.continuous+=1,void(a.continuous>=s&&(a.continuous=0,a.ts=n,this.tp2p.trigger(l.default.PULL_SUB_STREAM,{subStreamId:e}),this._reportData[c.ReportField.SSM_PULL_STREAM]+=1))}a.continuous=0}}}},{key:"_initReporterData",value:function(){this._reportData={},this._reportData[c.ReportField.SSM_PULL_STREAM]=0}},{key:"reportCallback",value:function(){var e=this._reportData;return this._initReporterData(),e}}]),a);function a(e,t){var r=this;if(!(this instanceof a))throw new TypeError("Cannot call a class as a function");this.TAG="SubStreamMonitor",this.tp2p=t,this.stateMgr=e.stateMgr,this.storage=e.storage,this.switchController=e.switchController,this.baseSub=t.config.pcdnStreams,this._streamState=[],this._checkTimeout=null;for(var i=0;i<this.baseSub;i++)this._streamState.push({cdn:{ts:0,continuous:0}});0<t.config.ssmCheckInterval&&(this._checkTimeout=setInterval(function(){r._checkSubStream()},t.config.ssmCheckInterval)),this._initReporterData(),this.tp2p.registerReportCallback(this.TAG,this.reportCallback.bind(this))}r.default=e},{132:132,71:71}],88:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function n(){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(r,"__esModule",{value:!0}),function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)}(n,null,[{key:"DomainStuckRollback",value:function(e,t){for(var r=0;r<t.length;r++)if(-1!==e.indexOf(t[r]))return!1;return!0}},{key:"SpecifyDelayRoom",value:function(e,t){if(t&&0!==t.length)for(var r=0;r<t.length;r++)if(-1!==e.indexOf(t[r]))return!0;return!1}},{key:"SuperLowLatency",value:function(e,t){if(t&&0!==t.length)for(var r=0;r<t.length;r++)if(-1!==e.indexOf(t[r]))return!0;return!1}},{key:"SuperLowLatencyConfig",value:function(){return{enableSlowDown:!0,delayCheckInterval:3e3,assignDelay:1e3}}},{key:"InitStreamNotFound",value:function(e,t){if(t&&0!==t.length){if("all"===t[0])return!0;for(var r=0;r<t.length;r++)if(-1!==e.indexOf(t[r]))return!0}return!1}}]),r.default=n},{}],89:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),e};function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=e(132),o=s(e(71));function s(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=i;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function i(e,t){var r;if(this instanceof i)return(r=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(i.__proto__||Object.getPrototypeOf(i)).call(this,t,o.default.FILL_FRAGMENT_SUCCESS,o.default.FILL_FRAGMENT_FAIL))).TAG="CDNFillManager",r.initiator=e,r.stateMgr=e.stateMgr,r.storage=e.storage,r.storageMgr=e.storageMgr,r.tp2p=t,r.missingFragment=new Set,r.fail404=r.stateMgr.confirmLoss,r._initReporterData(),r.tp2p.registerReportCallback(r.TAG,r.reportCallback.bind(r)),r;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),n(i,[{key:"destroy",value:function(){this.missingFragment=null}},{key:"cdnFill",value:function(e){var t=this,r=this.stateMgr.nextWriteId,i=[];return e.length&&(this._reportData[a.ReportField.FILL_REQ]+=1,e.forEach(function(e){t.storage.has(e)||t.missingFragment.has(e)||t.fail404.has(e)||(t.missingFragment.add(e),i.push(e)),e>t.stateMgr.getMaxSubStreamId(e%4)&&(t._reportData[a.ReportField.FILL_GT_SUB]+=1)}),0<i.length)&&(this.initiator.switchController.loadFragments(i),this._reportData[a.ReportField.FILL_SLICE]+=i.length),this._removeOutDateFrag(r),this._removeOutDate404(r),i.length}},{key:"onFillFragmentSuccess",value:function(e){e=e.sliceArray;this._reportData[a.ReportField.FILL_SLICE_OK]+=e.length,this._deleteFragCache(e)}},{key:"onFillFragmentFail",value:function(e){var t=e.sliceArray,e=e.failReason;this._deleteFragCache(t),e!==a.ReportField.ROLLBACK_STREAM_404&&e!==a.ReportField.ROLLBACK_STREAM_END||this._record404(t)}},{key:"_deleteFragCache",value:function(e){var t=this;e.forEach(function(e){t.missingFragment.delete(e)})}},{key:"_removeOutDateFrag",value:function(i){this.missingFragment.forEach(function(e,t,r){e<i&&r.delete(t)})}},{key:"_record404",value:function(e){var t=this;e.forEach(function(e){t.fail404.add(e)})}},{key:"_removeOutDate404",value:function(i){this.fail404.forEach(function(e,t,r){e<i&&r.delete(t)})}},{key:"_initReporterData",value:function(){this._reportData={};var e=this._reportData;e[a.ReportField.FILL_REQ]=0,e[a.ReportField.FILL_SLICE]=0,e[a.ReportField.FILL_SLICE_OK]=0,e[a.ReportField.FILL_GT_SUB]=0}},{key:"reportCallback",value:function(){var e=this._reportData;return this._initReporterData(),e}}]),i}(s(e(70)).default);r.default=e},{132:132,70:70,71:71}],90:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=e(71),o=(a=a)&&a.__esModule?a:{default:a},s=e(132);i(c,[{key:"init",value:function(){this._maxSliceId=0,this._maxContinuousSliceId=0,this._minSliceId=0,this._fillArray=[],this._p2pFillArray=[],this._cdnFillArray=[],this._subStreams=[];for(var e=0;e<this.baseSub;e++)this._subStreams[e]={maxSliceId:0,maxContinuousSliceId:0,missingSliceIds:[],from:"normal"};this._timer||(this._timer=setInterval(this._removeOutDateSlice.bind(this),1e3)),this._initReporterData(),this.tp2p.registerReportCallback(this.TAG,this.reportCallback.bind(this))}},{key:"onStarting",value:function(){this._cdnFillTimer||(this._cdnFillTimer=setInterval(this._PatchFill.bind(this),1e3))}},{key:"sliceArrive",value:function(e,t){var r=e%this.baseSub,r=this.updateSubStreams(r,e,t);this.sliceIncontinuity(r)}},{key:"updateSubStreams",value:function(e,t,r){var e=this._subStreams[e],i=[],n=60,a=(this.initiator.stateMgr.frameRate&&(n=this.initiator.stateMgr.frameRate),this.globalConfig),o=Math.abs(t-e.maxSliceId);if(0!==e.maxSliceId&&(5*n<o&&(this._reportData[s.ReportField.MISS_OVER_5S]+=1),10*n<o&&(this._reportData[s.ReportField.MISS_OVER_10S]+=1),20*n<o&&(this._reportData[s.ReportField.MISS_OVER_20S]+=1),30*n<o)&&(this._reportData[s.ReportField.MISS_OVER_30S]+=1),!(0!==e.maxSliceId&&o>n*a.patchSkipThreshold))return 0!==e.maxSliceId&&e.maxSliceId+this.baseSub<t&&(i=this._converter(e.maxSliceId,t),e.missingSliceIds=e.missingSliceIds.concat(i)),e.maxSliceId<t?e.maxSliceId=t:-1!==(o=e.missingSliceIds.indexOf(t))&&e.missingSliceIds.splice(o,1),0===e.maxContinuousSliceId||0===e.missingSliceIds.length?e.maxContinuousSliceId=t:e.maxContinuousSliceId=e.missingSliceIds[0]-this.baseSub,e.from=r,e.missingSliceIds.length,i;e.maxSliceId=t,e.from=r}},{key:"_removeOutDateSlice",value:function(){for(var e=this.stateMgr.nextWriteId,t=this.baseSub-1;0<=t;t--)for(var r=this._subStreams[t].missingSliceIds,i=r.length-1;0<=i;i--)r[i]<e&&r.splice(i,1)}},{key:"sliceIncontinuity",value:function(e){e&&0!==e.length&&(this._fillArray=this._fillArray.concat(e))}},{key:"_getSliceFromArray",value:function(e,t){for(var r=[],i=this.stateMgr.nextWriteId;r.length<=t&&0<e.length;){var n=e.shift();n&&i<=n&&!this.storage.has(n)&&r.push(n)}return r}},{key:"tryP2PFill",value:function(){var e=this.globalConfig,t=this.initiator.stateMgr.frameRate;e.enablePatchP2PFill&&(t=this._getSliceFromArray(this._fillArray,2*t),this.tp2p.trigger(o.default.TRY_P2P_FILL,{missing:t,from:"P2PFill"}),e.enablePatchCDNFill)&&(this._p2pFillArray=this._p2pFillArray.concat(t))}},{key:"_subStreamContrast",value:function(){var r=this,i=this.initiator.stateMgr.frameRate,n=this.globalConfig;this._subStreams.forEach(function(e){var t=r.maxSliceId-e.maxSliceId;t<i*n.patchSkipThreshold&&t>i*n.patchSubStreamDiff&&r.sliceIncontinuity(r._converter(e.maxSliceId,r.maxSliceId))})}},{key:"tryCDNFill",value:function(){var e=this.globalConfig,t=this.initiator.stateMgr.frameRate;e.enablePatchP2PFill?this._cdnFillArray=this._getSliceFromArray(this._p2pFillArray,2*t):this._cdnFillArray=this._getSliceFromArray(this._fillArray,2*t),0<this._cdnFillArray.length&&e.enablePatchCDNFill&&(t=this.cdnFillManager.cdnFill(this._cdnFillArray),this._reportData[s.ReportField.PATCH_CDN_FILL_REQ]+=1,this._reportData[s.ReportField.PATCH_CDN_FILL_SLICE]+=t,this._cdnFillArray=[])}},{key:"_PatchFill",value:function(){this._subStreamContrast(),this.tryCDNFill(),this.tryP2PFill()}},{key:"_converter",value:function(e,t){for(var r=[],i=e+this.baseSub;i<t;i+=this.baseSub)r.push(i);return r}},{key:"_initReporterData",value:function(){this._reportData={};var e=this._reportData;e[s.ReportField.MISS_OVER_10S]=0,e[s.ReportField.MISS_OVER_5S]=0,e[s.ReportField.MISS_OVER_20S]=0,e[s.ReportField.MISS_OVER_30S]=0,e[s.ReportField.PATCH_CDN_FILL_REQ]=0,e[s.ReportField.PATCH_CDN_FILL_SLICE]=0}},{key:"reportCallback",value:function(){var t=this._reportData;return Object.keys(t).forEach(function(e){t[e]||delete t[e]}),this._initReporterData(),t}},{key:"destroy",value:function(){this._subStreams=[],this.onStopping()}},{key:"onStopping",value:function(){this._timer&&(clearInterval(this._timer),this._timer=null),this._cdnFillTimer&&(clearInterval(this._cdnFillTimer),this._cdnFillTimer=null)}},{key:"maxSliceId",get:function(){var t=this;return this._subStreams.forEach(function(e){e.maxSliceId>t._maxSliceId&&(t._maxSliceId=e.maxSliceId)}),this._maxSliceId}},{key:"maxContinuousSliceId",get:function(){return this._maxContinuousSliceId}},{key:"minSliceId",get:function(){return this._minSliceId}}]);a=c;function c(e,t){if(!(this instanceof c))throw new TypeError("Cannot call a class as a function");this.TAG="PatchManager",this.baseSub=e.pcdnStreams,this.xStreamId=e.xStreamId,this.globalConfig=t.config,this.initiator=e,this.tp2p=t,this.storage=e.storage,this.stateMgr=e.stateMgr,this.init(),this.cdnFillManager=e.cdnFillManager}r.default=a},{132:132,71:71}],91:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=e(83);i(o,[{key:"reset",value:function(){this.frameRate=30,this.playSkipSucc=!1,this.playStart=!1,this.nextWriteId=0,this.confirmLoss=new Set,this.confirmLossSkipSliceId=0,this.firstSubSliceId=0,this.lastMainSliceId=0,this.reloadingMain=!1,this.loadingMain=!1,this.baseTime=Date.now(),this.baseId=0,this.stuck=!1,this.notFoundCounter=0,this.subStreamState={},this._fillSliceRange=[0,0,Date.now()],this._maxSliceId=0,this._subStreamMaxContinousId=[],this._subStreamMaxId=[];for(var e=0;e<this.baseSub;e++)this._subStreamMaxContinousId.push(0),this._subStreamMaxId.push(0),this.subStreamState[e]=a.SubLoadingState.LOADING_MAIN}},{key:"setSubStreamId",value:function(e){this.setMaxSliceId(e);var t,r=e%this.baseSub,i=this._subStreamMaxContinousId[r];if(0===i)this._subStreamMaxContinousId[r]=e;else for(this.nextWriteId-i>this.baseSub&&(t=r-this.nextWriteId%this.baseSub,i=this.nextWriteId+(t<=0?t:t-this.baseSub),this._subStreamMaxContinousId[r]=i);this.storage.has(i)||this.confirmLoss.has(i);)this._subStreamMaxContinousId[r]=i,i+=this.baseSub;e>this._subStreamMaxId[r]&&(this._subStreamMaxId[r]=e)}},{key:"resetSubMaxCon",value:function(){this._subStreamMaxContinousId=[];for(var e=0;e<this.baseSub;e++)this._subStreamMaxContinousId.push(0)}},{key:"getLastSubStreamId",value:function(e){return this._subStreamMaxContinousId[e]}},{key:"getMaxSubStreamId",value:function(e){return this._subStreamMaxId[e]}},{key:"setMaxSliceId",value:function(e){e>this._maxSliceId&&(this._maxSliceId=e)}},{key:"stopAutoFetchSubStreamOnce",value:function(){for(var e=0;e<this.baseSub;e++)this.subStreamState[e]=a.SubLoadingState.LOADING_MAIN}},{key:"getAllSubStreamState",value:function(){for(var e="",t=["i","s","m"],r=0;r<this.baseSub;r++)e+=t[this.getSubStreamState(r)]+"-";return e}},{key:"getSubStreamState",value:function(e){return this.subStreamState[e]}},{key:"setSubStreamState",value:function(e,t){this.subStreamState[e]=t}},{key:"onCdnLoaded",value:function(e){var t=this.initiator.p2pController;t&&t.onCdnLoaded(e)}},{key:"onP2PLoaded",value:function(e){var t=this.initiator.p2pController;t&&t.onP2pLoaded(e)}},{key:"loadMainReset",value:function(){this.firstSubSliceId=0,this.lastMainSliceId=0}},{key:"maxXSubStreamId",get:function(){return this._subStreamMaxId[this.xStreamId]}},{key:"maxSliceId",get:function(){return this._maxSliceId}},{key:"p2pBoundId",get:function(){return this.maxSliceId-this.frameRate*this.globalConfig.p2pWindow}},{key:"lowLatency",get:function(){return this.tp2p.bufferLength<this.globalConfig.fillMinBuf&&this.p2pBoundId<this.nextWriteId}},{key:"maxContinuousId",get:function(){return this.initiator.storageMgr.maxContinuousId}},{key:"minSliceId",get:function(){return this.initiator.storageMgr.minSliceId}},{key:"playId",get:function(){return this.nextWriteId-this.tp2p.bufferLength*this.frameRate}},{key:"realDelay",get:function(){return(this.maxSliceId-this.playId)/this.frameRate}},{key:"fillSliceRange",get:function(){var e=this._fillSliceRange.slice(0,2);return 0<e[1]&&(e[1]+=Math.floor((Date.now()-this._fillSliceRange[2])/1e3*this.frameRate)),e},set:function(e){e instanceof Array&&2===e.length&&(this._fillSliceRange=e.concat(Date.now()))}}]);e=o;function o(e,t){if(!(this instanceof o))throw new TypeError("Cannot call a class as a function");this.baseSub=e.pcdnStreams,this.xStreamId=e.xStreamId,this.globalConfig=t.config,this.initiator=e,this.tp2p=t,this.storage=e.storage,this.reset()}r.default=e},{83:83}],92:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});r.FlvConst={FLV_HEADER_LEN:9,TAG_HEADER_LEN:11,PREV_TAG_SIZE_LEN:4,AUDIO_TAG:8,VIDEO_TAG:9,SCRIPT_TAG:18,I_FRAME:1,NAL_VPS:32,NAL_SPS:33,NAL_PPS:34},r.SoundFormat={AAC:10},r.AACPacketType={AACSequenceHeader:0,AACRaw:1},r.CodecID={AVC:7},r.AVCPacketType={AVCSequenceHeader:0,AVCNALU:1}},{}],93:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=s(e(95)),l=e(92),u=s(e(94)),o=s(e(140));function s(e){return e&&e.__esModule?e:{default:e}}i(c,[{key:"destroy",value:function(){this.tagCache.clear()}},{key:"_fragLoaded",value:function(){if(this._parseFlvHead)this.loaderBuffer.length<l.FlvConst.FLV_HEADER_LEN+l.FlvConst.PREV_TAG_SIZE_LEN||(this._readFlvHeader(),this._fragLoaded());else for(var e;null!==(e=this._readTag());)this._parseTag(e)}},{key:"_readFlvHeader",value:function(){this._parseFlvHead=!1;var e=this.loaderBuffer.shift(13);this._writeFlvHead&&this.tagCache.push(e)}},{key:"_readTag",value:function(){var e;return this.loaderBuffer.length<l.FlvConst.TAG_HEADER_LEN||(e=this.loaderBuffer.toInt(1,3),e=l.FlvConst.TAG_HEADER_LEN+e+l.FlvConst.PREV_TAG_SIZE_LEN,this.loaderBuffer.length<e)?null:this.loaderBuffer.shift(e)}},{key:"_updateRecoder",value:function(){var e=this.sequenceHeadertagRecoder;e.aac.tag&&(e.aac.afterCnt+=1),e.avc.tag&&(e.avc.afterCnt+=1)}},{key:"_parseTag",value:function(e){this.tagCache.push(e);var t=31&e[0],r=(e[4]<<16)+(e[5]<<8)+e[6]+(e[7]<<24);switch(t){case l.FlvConst.SCRIPT_TAG:this._throwSequenceHeaderTag&&this.tagCache.pop();break;case l.FlvConst.AUDIO_TAG:if(0===r&&o.default.AACSequenceHeaderTag(e)){if(this.observer.emit("sequence_header",{type:"aac",tag:e}),!this._firstSliceFound&&this._throwFirstSeqHeader){this.tagCache.pop();break}if(!this._writeFlvHead){var i=this.sequenceHeadertagRecoder;i.aac.tag?o.default.CompareTagData(i.aac.tag,e)||(i.aac.tag=e,i.aac.afterCnt=0,i.aac.updateCnt+=1):i.aac.tag=e,this.tagCache.pop();break}}this._writeFlvHead||this._updateRecoder();break;case l.FlvConst.VIDEO_TAG:if(0===r&&o.default.AVCSequenceHeaderTag(e)){if(this.observer.emit("sequence_header",{type:"avc",tag:e}),!this._firstSliceFound&&this._throwFirstSeqHeader){this.tagCache.pop();break}if(!this._writeFlvHead){this.sequenceHeadertagRecoder.avc.tag=e,this.sequenceHeadertagRecoder.avc.afterCnt=0,this.tagCache.pop();break}}this._writeFlvHead||this._updateRecoder(),this._parseVideoTag(e),this._writeFlvHead&&!this._firstSliceFound&&this.observer.emit("video_tag")}}},{key:"_parseVideoTag",value:function(e){var t=15&e[l.FlvConst.TAG_HEADER_LEN];7==t&&this._parseAVCVideoPacket(e)}},{key:"_parseAVCVideoPacket",value:function(e){var t,r=1,r=(e[r++]<<16)+(e[+r]<<8)+e[3];r<4||1===e[t=l.FlvConst.TAG_HEADER_LEN+1]&&this._parseAVCVideoData(e,r,t+4)}},{key:"_parseAVCVideoData",value:function(e,t,r){for(var i=t-5,n=this._naluLengthSize,a=0;a<i&&!(i<=a+4);){var o=r+a,o=(e[o]<<24)+(e[o+1]<<16)+(e[o+2]<<8)+e[o+3];if(3===n&&(o>>>=8),i-n<o)return;var s=r+1+a+n;if(6===e[s-1]){s=e.slice(s,s+o-1),s=u.default.parseSEI(s);if(s){var c=(e[4]<<16)+(e[5]<<8)+e[6]+(e[7]<<24);s.tagTimestamp=c,s.keyFrame=(240&e[l.FlvConst.TAG_HEADER_LEN])>>4,this._deleteSEINalu(e,r+a,o+n),this._findSliceVideoTag(s);break}}a+=4+o}}},{key:"_deleteSEINalu",value:function(e,t,r){var i=e.length,i=new ArrayBuffer(i-r),n=new Uint8Array(i),a=n.length,e=(n.set(e.slice(0,t),0),n.set(e.slice(t+r),t),new DataView(i)),t=16777215&e.getUint32(0,!1),i=e.getUint8(0),t=(e.setUint32(0,t-r),e.setUint8(0,i),e.getUint32(a-4));e.setUint32(a-4,t-r),this.tagCache.pop(),this.tagCache.push(n)}},{key:"_findSliceVideoTag",value:function(e){var t=e.idx,r=e.frames,i=e.tagTimestamp,e=e.keyFrame,n=this.tagCache.size,a=(this._firstSliceFound&&n!==r&&(this.observer.emit("slice_error",{sliceId:t,diff:r+"_"+n+"_"+this._requestType}),r=n),void 0);if(this._writeFlvHead)a=this._firstSliceFound?this.tagCache.getSlice(0,!0):this.tagCache.getSlice(n-1);else{var o=this.sequenceHeadertagRecoder;if(1===e){n=this.tagCache.pop();o.aac.tag&&this.tagCache.push(o.aac.tag),o.avc.tag&&this.tagCache.push(o.avc.tag),this.tagCache.push(n)}else if(o.aac.tag&&(0<o.aac.updateCnt||!o.avc.tag)){for(var s=[],c=0;c<o.aac.afterCnt;c++){var l=this.tagCache.pop();l&&s.push(l)}this.tagCache.push(o.aac.tag);for(var u=s.length,p=0;p<u;p++)this.tagCache.push(s.pop())}a=this.tagCache.getSlice(r)}this.observer.emit("found_slice",{sliceId:t,payload:a,tagTime:i,requestType:this._requestType}),this._firstSliceFound||(this._firstSliceFound=!0,this._writeFlvHead&&(e=this.tagCache.getSlice(0,!0),this.observer.emit("found_flv_head",{startSliceId:t,payload:e}))),this.sequenceHeadertagRecoder.reset(),this.tagCache.clear()}}]);e=c;function c(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=this,r=c;if(!(t instanceof r))throw new TypeError("Cannot call a class as a function");this.TAG="FlvDemux, requestType: "+e.requestType+" subStreamIndex: "+e.subStreamIndex,this.observer=e.observer,this.loaderBuffer=e.loaderBuffer,this._parseFlvHead=e.parseFlvHead,this._writeFlvHead=e.writeFlvHead,this._throwFirstSeqHeader=this._parseFlvHead&&!this._writeFlvHead,this._throwScriptTag=this._parseFlvHead&&!this._writeFlvHead,this._requestType=e.requestType,this._firstSliceFound=!1,this._naluLengthSize=4,this.tagCache=new a.default,this.observer.on("frag_loaded",this._fragLoaded.bind(this)),this.sequenceHeadertagRecoder={aac:{updateCnt:0,tag:null,afterCnt:0},avc:{tag:null,afterCnt:0},reset:function(){this.aac={updateCnt:0,tag:null,afterCnt:0},this.avc={tag:null,afterCnt:0}}}}r.default=e},{140:140,92:92,94:94,95:95}],94:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});var o=5,n=(function(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),e}(s,null,[{key:"_ebsp2rbsp",value:function(e){for(var t=e,r=t.byteLength,i=new Uint8Array(r),n=0,a=0;a<r;a++)2<=a&&3===t[a]&&0===t[a-1]&&0===t[a-2]||(i[n]=t[a],n+=1);return new Uint8Array(i.buffer,0,n)}},{key:"parseSEI",value:function(e){for(var e=s._ebsp2rbsp(e),t=new DataView(e.buffer),r=0,i=0,n=0,e=void 0,a=void 0;a=t.getUint8(n),n+=1,r+=a,255===a;);for(;a=t.getUint8(n),n+=1,i+=a,255===a;);return e=r===o?s.decodeUnregisteredUserData(t,n,i):e}},{key:"decodeUnregisteredUserData",value:function(e,t,r){if(r<16||30<r)return null;for(var i=[84,101,110,99,101,110,116,83,116,114,101,97,109,83,69,73],n=0;n<16;n++)if(e.getUint8(t+n)!==i[n])return null;t+=16;r={uuid:"",idx:"",frames:"",timestamp:""};return r.uuid="tencentStreamSEI",r.idx=e.getUint32(t),r.frames=e.getUint16(t+=4),t+=2,r.timestamp=Math.pow(2,32)*e.getUint32(t)+e.getUint32(t+4),r}}]),s);function s(){if(!(this instanceof s))throw new TypeError("Cannot call a class as a function")}r.default=n},{}],95:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(a,[{key:"push",value:function(e){this.cache.push(e)}},{key:"pop",value:function(){return this.cache.pop()}},{key:"getSlice",value:function(e){var t=this.cache.length,r=void 0,i=(t===e||1<arguments.length&&void 0!==arguments[1]&&arguments[1]?(r=this.cache,this.cache=[]):r=this.cache.splice(t-e),0),n=(r.forEach(function(e){i+=e.length}),new Uint8Array(i)),a=0;return r.forEach(function(e){n.set(e,a),a+=e.length}),n}},{key:"clear",value:function(){this.cache=[]}},{key:"size",get:function(){return this.cache.length}}]);var n=a;function a(){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function");this.cache=[]}r.default=n},{}],96:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=C(e(59)),o=C(e(128)),s=C(e(131)),c=C(e(91)),l=C(e(90)),u=C(e(86)),p=C(e(107)),d=C(e(130)),h=C(e(106)),f=C(e(82)),_=C(e(81)),y=C(e(71)),v=e(132),m=C(e(112)),g=C(e(89)),S=C(e(139)),b=C(e(87)),R=e(61),T=C(e(88));function C(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=n;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function n(e,t,r){var i;if(this instanceof n)return(i=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(n.__proto__||Object.getPrototypeOf(n)).call(this))).TAG="PCDNInitiator",i.tp2p=e,i.startedHook=t,i.stoppedHook=r,i.pcdnStreams=e.config.pcdnStreams,i._xStreamId=void 0!==e.config.xStreamId?e.config.xStreamId:Math.floor(i.pcdnStreams*Math.random()),i.confLoader=new f.default(e),i.confController=new _.default(e),i.storage=new o.default,i.stateMgr=new c.default(i,e),i.cdnFillManager=new g.default(i,e),i.patchMgr=new l.default(i,e),i.storageMgr=new d.default(i,e),i.subStreamMonitor=new b.default(i,e),i.switchController=new u.default(i,e,{baseSub:i.pcdnStreams}),i.writer=new s.default(i,e),i.videoProxy=new p.default,i.videoManager=new h.default(i,e),i.coreComponents=[i.patchMgr,i.cdnFillManager,i.storage,i.subStreamMonitor,i.switchController,i.writer,i.videoProxy,i.storageMgr,i.videoManager,i.confLoader,i.confController],i.lazyStartComponents=[i.patchMgr,i.writer,i.videoManager],e.on(y.default.LOADED_DATA,function(){var e,t;i.stateMgr.playStart||((e={i:{}})[v.ReportField.CODE]=v.ReportCode.PHASE_DATA,e.i[v.ReportField.LOAD_OK_TIME]=Date.now()-i.tp2p.config.loadTime,t=S.default.pageVisibility(),e.i[v.ReportField.PLAY_VISIBLE]=t.pageVisible,i.tp2p.trigger(y.default.REPORT_ONCE,e),i.stateMgr.playStart=!0,i.tp2p.status.isFirstPieceDownloaded=!0,i.tp2p.trigger(y.default.CONF_LOADING))}),e.on(y.default.FIRST_CONF_LOADED,function(){i.startLazyComponents(),e.config.useP2p&&i._lazyStartP2P()}),e.on(y.default.CONF_FAIL,function(){i.startLazyComponents()}),i;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(n,[{key:"destroy",value:function(){this.stopLazyComponents(),this.coreComponents.forEach(function(e){e.destroy()})}},{key:"startLazyComponents",value:function(){this.lazyComStart||(this.lazyComStart=!0,this.lazyStartComponents.forEach(function(e){e&&e.onStarting()}),this.startedHook&&this.startedHook())}},{key:"stopLazyComponents",value:function(){this.lazyComStop||(this.stoppedHook&&this.stoppedHook(),this.lazyComStop=!0,this.lazyStartComponents.forEach(function(e){e&&e.onStopping()}))}},{key:"_lazyStartP2P",value:function(){this.p2pController||(this.p2pController=new m.default(this,this.tp2p),this.coreComponents.push(this.p2pController),this.lazyStartComponents.push(this.p2pController))}},{key:"load",value:function(){var e,t=this.tp2p.config.channelId,r=this.tp2p.config;T.default.SuperLowLatency(r.channelId,r.enableSuperLatencyRooms)&&(e=T.default.SuperLowLatencyConfig(),Object.assign(r,e)),this._reportPlayStart(),this.switchController.startLoad(t)}},{key:"_reportPlayStart",value:function(){var e=this.tp2p,t=e.config,r={i:{}},i=(r[v.ReportField.CODE]=v.ReportCode.PLAY_START,r.i[v.ReportField.REQUEST]=1,r.i[v.ReportField.URL]=t.originalUrl,r.i[v.ReportField.LOAD_VISIBLE]=S.default.pageVisibility().pageVisible,R.tp2pDefaultConfig.playTimes);1===i&&(r.i[v.ReportField.BACKGROUND_TIME]=t.loadTime-t.startTime),r.i[v.ReportField.SDK_PLAY_TIMES]=i,e.trigger(y.default.REPORT_ONCE,r)}},{key:"setMediaElement",value:function(e){this.videoProxy.setVideoByElement(e)}},{key:"localPeerId",get:function(){return this._localPeerId},set:function(e){this._localPeerId=e}},{key:"xStreamId",get:function(){return this._xStreamId},set:function(e){this._xStreamId=e}}]),n}(a.default);r.default=e},{106:106,107:107,112:112,128:128,130:130,131:131,132:132,139:139,59:59,61:61,71:71,81:81,82:82,86:86,87:87,88:88,89:89,90:90,91:91}],97:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=o(e(39)),s=o(e(98)),c=o(e(129)),l=o(e(93)),u=e(83);function o(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=o;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function o(e,t,r,i){var n,a;if(this instanceof o)return(n=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(o.__proto__||Object.getPrototypeOf(o)).call(this))).TAG="DownloadServer",n.id=t,n.isMainStream=r===u.RequestType.MAIN_STREAM,n.isSubStream=r===u.RequestType.SUB_STREAM,n.requestType=r,n.config=i,n.loaderBuffer=new c.default,n.flvDemux=new l.default({parseFlvHead:n.isMainStream||n.isSubStream,writeFlvHead:n.isMainStream,loaderBuffer:n.loaderBuffer,observer:n,requestType:r,subStreamIndex:void 0===i.query.substream?"fullStream":i.query.substream}),a=function(e,t){e={serverId:n.id,requestType:n.requestType,requestConfig:n.config,httpReportType:e,data:t,redirectedURL:n.streamLoader.redirectedURL};n.emit("http_status",e)},r===u.RequestType.FRAGMENT&&i.timeout&&(n.timeoutTimeout=setTimeout(function(){a(u.HttpReportType.TIMEOUT),n.timeoutTimeout=null},i.timeout)),n.streamLoader=new s.default({url:e,timeout:r===u.RequestType.FRAGMENT?2*i.timeout:i.timeout,headers:i.headers,streamingDownload:r!==u.RequestType.FRAGMENT,enableAbortController:i.enableAbortController,onLoadBuffer:function(e){n.loaderBuffer.push(e),n.emit("frag_loaded",e.byteLength,r)},onHttpReport:a}),n;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(o,[{key:"destroy",value:function(){this.timeoutTimeout&&clearTimeout(this.timeoutTimeout),this.streamLoader.destroy(),this.loaderBuffer.clear(),this.streamLoader=null,this.loaderBuffer=null,this.flvDemux=null}}]),o}(a.default);r.default=e},{129:129,39:39,83:83,93:93,98:98}],98:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=e(83);i(o,[{key:"abort",value:function(){this._abortController&&this._enableAbortController&&this._abortController.abort(),this._abort=!0,this._readerInstance&&(this._readerInstance.cancel().catch(function(e){}),this._readerInstance=null)}},{key:"destroy",value:function(){this.abort(),this.onHttpReport=function(){},this.onLoadBuffer=function(){}}},{key:"_open",value:function(e){var t,r=this;this._readerInstance||(t={method:"GET",mode:"cors",referrerPolicy:"no-referrer-when-downgrade",headers:this._headers||{}},window.AbortController&&(this._abortController=new window.AbortController,t.signal=this._abortController.signal),e=window.fetch(e,t).then(function(e){if(!r._abort)return Promise.resolve(e);r._readerInstance||(r._readerInstance=e.body.getReader(),r.abort())}).catch(function(e){if(!r._abort)return Promise.reject(e)}),t=new Promise(function(e,t){setTimeout(function(){t("timeout")},r._timeout)}),Promise.race([e,t]).then(function(e){var t;r._abort||(t=e.headers,t=r._getHeader(t),r.onHttpReport(a.HttpReportType.REPORT_RES_HEADER,{header:t}),e.ok&&200<=e.status&&e.status<=209?(e.redirected&&(r._redirectedURL=e.url),r.onHttpReport(a.HttpReportType.CODE_200),r._readerInstance=e.body.getReader(),r._pump(r._readerInstance)):404===e.status?r.onHttpReport(a.HttpReportType.CODE_404):403===e.status?r.onHttpReport(a.HttpReportType.CODE_403):r.onHttpReport(a.HttpReportType.UNKNOWN_ERROR))}).catch(function(e){r._abort||("timeout"===e?r.onHttpReport(a.HttpReportType.TIMEOUT):r.onHttpReport(a.HttpReportType.NETWORK_ERROR))}))}},{key:"_pump",value:function(r){var i=this;this._abort||r.read().then(function(e){var t=e.done,e=e.value;t?i._readerInstance&&(i._readerInstance=null,i.onHttpReport(a.HttpReportType.DONE)):(i.onLoadBuffer&&i.onLoadBuffer(e),i._pump(r))}).catch(function(e){i._abort||i.onHttpReport(a.HttpReportType.READ_ERROR)})}},{key:"_getHeader",value:function(e){var t={},r=!0,i=!1,n=void 0;try{for(var a,o=e[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){var s=a.value;t[s[0]]=s[1]}}catch(e){i=!0,n=e}finally{try{!r&&o.return&&o.return()}finally{if(i)throw n}}return t}},{key:"redirectedURL",get:function(){if(this._redirectedURL)return this._redirectedURL}}],[{key:"support",value:function(){return!!(window.fetch&&window.ReadableStream&&Uint8Array)}}]);e=o;function o(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=this,r=o;if(!(t instanceof r))throw new TypeError("Cannot call a class as a function");this.TAG="FetchStreamLoader",this.onLoadBuffer=e.onLoadBuffer,this.onHttpReport=e.onHttpReport,this._abort=!1,this._timeout=e.timeout||4e3,this._headers=e.headers,this._enableAbortController=e.enableAbortController,this._abortController=null,this._open(e.url)}r.default=e},{83:83}],99:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=s(e(101)),o=s(e(102));function s(e){return e&&e.__esModule?e:{default:e}}function c(e,t,r){var i=this;if(!(this instanceof c))throw new TypeError("Cannot call a class as a function");this.TAG="DelayManager",this.initiator=e,this.tp2p=t,this.videoProxy=r;e=this.tp2p.config;this.playpackRateController=new a.default(this.initiator,this.videoProxy,e),this.seekController=new o.default(this.videoProxy,e),this.seekController.on("seeked",function(){i._startPlaybackRateCtrl()}),this.seekController.on("timeout",function(){i._startPlaybackRateCtrl()})}i(c,[{key:"destroy",value:function(){this.initiator=null,this.tp2p=null,this.videoProxy=null,this.seekController=null,this.playpackRateController=null}},{key:"onStarting",value:function(){this.tp2p.config.enableStartSeek?this.seekController.onStarting():this._startPlaybackRateCtrl()}},{key:"onStopping",value:function(){this.seekController.onStopping(),this.playpackRateController.onStopping()}},{key:"_startPlaybackRateCtrl",value:function(){"Safari"!==this.tp2p.config.browser.name&&this.playpackRateController.onStarting()}}]),r.default=c},{101:101,102:102}],100:[function(r,e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=r(144),o=l(r(71)),s=r(132),c=l(r(58));function l(e){return e&&e.__esModule?e:{default:e}}i(u,[{key:"destroy",value:function(){this._clearWorker(),this._clearTimer()}},{key:"setMonitor",value:function(){var e=this.tp2p.config;e.enableStartWorker&&"undefined"!=typeof Worker?this._initWorker():this._initMonitorTimer(),this._reportWorker(e.enableStartWorker)}},{key:"_initMonitorTimer",value:function(){var e=this.tp2p.config;this._monitorStartTimer||(this._monitorStartTimer=setTimeout(this._playStartMonitor.bind(this),e.playStartTimeout))}},{key:"_reportWorker",value:function(e){var t=0,t=e&&"undefined"!=typeof Worker?2:"undefined"==typeof Worker?1:3;this._reportData[s.ReportField.USE_WORKER]=t}},{key:"_initWorker",value:function(){var t=this,e=this.tp2p.config;try{this.work=(0,c.default)(r(105)),this.work.addEventListener("message",function(e){e.data&&"timeout"===e.data.cmd&&(e.data.outWorkerTimeout=Date.now(),t._clearWorker(),e.data.clearWorkerTime=Date.now(),t._dealTiming(e.data),t._playStartMonitor())}),this.work.onerror=function(e){t._reportData[s.ReportField.WORKER_ONERR]+=1},this.work.postMessage({cmd:"init",data:{playStartTimeout:e.playStartTimeout,startTime:Date.now()}})}catch(e){this._reportData[s.ReportField.WORKER_EXCEPT]+=1}}},{key:"_dealTiming",value:function(e){this._reportData[s.ReportField.BEFORE_INIT_WORKER]=e.outWorkerInit-this.tp2p.config.sdkInitTime,this._reportData[s.ReportField.INIT_TRANSMISSION]=e.inWorkerInit-e.outWorkerInit,this._reportData[s.ReportField.TIMER_COST]=e.inWorkerTimeout-e.inWorkerInit,this._reportData[s.ReportField.TIMEOUT_TRANSMISSION]=e.outWorkerTimeout-e.inWorkerTimeout,this._reportData[s.ReportField.CLEAR_WORKER_COST]=e.clearWorkerTime-e.outWorkerTimeout}},{key:"_clearWorker",value:function(){this.work&&(window.URL.revokeObjectURL(this.work.objectURL),this.work.terminate(),this.work=null)}},{key:"_playStartMonitor",value:function(){var e,t,r;this.stateMgr.playStart||(this.tp2p.media||a.logger.warn("没有检测到 media"),this.stateMgr.playStart||a.logger.warn("media没有足够数据"),this.tp2p.trigger(o.default.ERROR,{fatal:!0,rollbackCode:s.ReportCode.ROLLBACK,extendReportData:(e={},t=s.ReportField.ROLLBACK_REASON,r=s.ReportField.ROLLBACK_EMPTY_BUFFER,t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e)}))}},{key:"_clearTimer",value:function(){this._monitorStartTimer&&(clearTimeout(this._monitorStartTimer),this._monitorStartTimer=void 0)}},{key:"onLoadedData",value:function(){this._reportData[s.ReportField.LOADEDDATA_TIME]=Date.now()-this.tp2p.config.loadTime}},{key:"onLoadedMetadata",value:function(){this._reportData[s.ReportField.METADATA_TIME]=Date.now()-this.tp2p.config.loadTime}},{key:"reportCallback",value:function(){var t=this._reportData;return t[s.ReportField.PLAY_DELAY]=this.stateMgr.realDelay,t[s.ReportField.BUFFER_LENGTH]=parseInt(this.tp2p.bufferLength,10),Object.keys(t).forEach(function(e){-1!==[s.ReportField.BUFFER_LENGTH,s.ReportField.PLAY_DELAY].indexOf(e)||t[e]||delete t[e]}),this._initReporterData(),t}},{key:"_initReporterData",value:function(){this._reportData={};var e=this._reportData;e[s.ReportField.BUFFER_LENGTH]=0,e[s.ReportField.METADATA_TIME]=0,e[s.ReportField.LOADEDDATA_TIME]=0,e[s.ReportField.PLAY_DELAY]=0,e[s.ReportField.USE_WORKER]=0,e[s.ReportField.WORKER_EXCEPT]=0,e[s.ReportField.WORKER_ONERR]=0,e[s.ReportField.BEFORE_INIT_WORKER]=0,e[s.ReportField.INIT_TRANSMISSION]=0,e[s.ReportField.TIMER_COST]=0,e[s.ReportField.TIMEOUT_TRANSMISSION]=0}}]);i=u;function u(e,t){if(!(this instanceof u))throw new TypeError("Cannot call a class as a function");this.TAG="PlaybackController",this.stateMgr=e.stateMgr,this.tp2p=t,this._initReporterData(),this.tp2p.registerReportCallback(this.TAG,this.reportCallback.bind(this)),this.setMonitor()}t.default=i},{105:105,132:132,144:144,58:58,71:71}],101:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});var n="idle",a="accelerating",o="internalRateChange",s="external_fast_forward",c="external_slow_down",l="external_normal",u=(function(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),e}(p,[{key:"destroy",value:function(){this.onStopping()}},{key:"onStarting",value:function(){var e=this._config.delayCheckInterval;this.onStopping(),this._loopTimer=setInterval(this.loop.bind(this),e),this.loop()}},{key:"onStopping",value:function(){this._loopTimer&&(clearInterval(this._loopTimer),this._loopTimer=null),this._clearTimer&&(clearTimeout(this._clearTimer),this._clearTimer=null)}},{key:"loop",value:function(){var e=this.check(),t=e.duration,e=e.speedRate;if(this._config.enableSpeedUp)if(t)switch(this._accelerationRate=e,this._state){case n:this.speedUp(t,this._accelerationRate);break;case a:this.clear(),this.speedUp(t,this._accelerationRate)}else this.clear()}},{key:"check",value:function(){var e={duration:0,speedRate:1},t=this._config,r=t.pcdnLiveDelay,i=t.p2pWindow,n=t.accelerationRate,a=t.enableSlowDown,t=t.slowDownRate,o=this.stateMgr.realDelay-r;return 0<o&&this.videoProxy.bufferLength>r-i?(r=1e3*o)<1e3||0<r&&1!==n&&(e.duration=Math.floor(r/(n-1)),e.speedRate=n):o<0&&a&&(-500<(i=1e3*o)||(e.duration=Math.floor(i/(t-1)),e.speedRate=t)),e}},{key:"speedUp",value:function(e){var t=this,r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:1.05;this._state=a,this.setPlaybackRate(r),this._clearTimer=setTimeout(function(){t.clear()},e)}},{key:"clear",value:function(){this._state=n,this.setPlaybackRate(1),clearTimeout(this._clearTimer)}},{key:"setPlaybackRate",value:function(e){this.videoProxy.playbackRate!==e&&(this.videoProxy.playbackRate=e)}},{key:"onRateChange",value:function(){var e=this._rateChangeReason();switch(e){case o:case c:break;case l:case s:this._recoverRate()}}},{key:"_rateChangeReason",value:function(){var e=this.videoProxy.playbackRate;return 1<e&&this._state!==a?s:1===e&&this._state!==n?l:e<1?c:o}},{key:"_recoverRate",value:function(){switch(this._state){case n:this.setPlaybackRate(1);break;case a:this.setPlaybackRate(this._accelerationRate)}}}]),p);function p(e,t,r){if(!(this instanceof p))throw new TypeError("Cannot call a class as a function");this.TAG="PlaybackRateController",this.stateMgr=e.stateMgr,this.videoProxy=t,this._config=r,this._accelerationRate=1,this._state=n,this._clearTimer=null,this._loopTimer=null}r.default=u},{}],102:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),e};function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var e=e(39),e=(e=e)&&e.__esModule?e:{default:e};e=function(e){var t=i;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function i(e,t){var r;if(this instanceof i)return(r=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(i.__proto__||Object.getPrototypeOf(i)).call(this))).TAG="SeekController",r._config=t,r._videoProxy=e,r.reset(),r;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),n(i,[{key:"destroy",value:function(){this.removeAllListeners(),this.reset(),this._config=null,this._videoProxy=null}},{key:"reset",value:function(){this._lastBufferLen=0,this._lastSampleTime=0,this._recentRateSamples=[],this._clearTimer()}},{key:"onStarting",value:function(){this._config.enableStartSeek&&this.monitorRate()}},{key:"onStopping",value:function(){this.reset()}},{key:"monitorRate",value:function(){var t=this;this._loopTimer=setInterval(function(){var e;t._lastBufferLen&&t._lastSampleTime&&(e=t._bufferIncreasingRate(),t._reachStableState(e))&&t._seek()&&(t.reset(),t.emit("seeked")),t._lastBufferLen=t._videoProxy.bufferLength,t._lastSampleTime=performance.now()},this._config.startSeekSampleIntl),this._timeout=setTimeout(function(){t.reset(),t.emit("timeout")},1e4)}},{key:"_bufferIncreasingRate",value:function(){var e=this._videoProxy.bufferLength,e=1e3*(e-this._lastBufferLen),t=performance.now()-this._lastSampleTime;return e/t}},{key:"_reachStableState",value:function(e){var t=this,r=this._config,i=r.startSeekSampleCnt,n=r.startSeekStableRate;return t._recentRateSamples.push(e),t._recentRateSamples.length>i&&t._recentRateSamples.shift(),t._recentRateSamples.length===i&&this._recentRateSamples.every(function(e){return e<n})}},{key:"_seek",value:function(){var e,t,r=this._videoProxy.bufferLength;return!(this._videoProxy.readyState<=2&&r<1||(e=(t=this._config).startSeekDelay,t=t.pcdnLiveDelay,(r=r-(e||t))<0||(this._videoProxy.currentTime+=r),0))}},{key:"_clearTimer",value:function(){this._loopTimer&&(clearInterval(this._loopTimer),this._loopTimer=null),this._timeout&&(clearTimeout(this._timeout),this._timeout=null)}}]),i}(e.default);r.default=e},{39:39}],103:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(a,null,[{key:"VideoInfo",value:function(e){for(var t=e.buffered,r=e.currentTime,i=!0,n=[],a=0;a<t.length;a++){var o=t.start(a),s=t.end(a);o<=r&&r<=s&&(i=!1),n.push({start:o,end:s})}return{current_time:r,ready_state:e.readyState,fall_behind:!!t.length&&r<t.start(0),ahead:!!t.length&&r>t.end(t.length-1),in_gap:i,buffered_num:t.length,buffered_range:n}}},{key:"MonitorPlayHeadMoving",value:function(a){return new Promise(function(e){var t=a.currentTime,r=0,i=!0,n=setInterval(function(){a.currentTime===t&&(i=!1),t=a.currentTime,2===(r+=1)&&(clearInterval(n),e({moving:i}))},60)})}},{key:"SeekALittle",value:function(t){var r=t.currentTime;return t&&(t.currentTime+=.3),this.MonitorPlayHeadMoving(t).then(function(e){return{moving:e.moving,time_shift:t.currentTime-r,ready_state:t.readyState}})}}]);var n=a;function a(){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function")}r.default=n},{}],104:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var s=e(132),a=u(e(88)),o=u(e(71)),c=e(69),l=u(e(103));function u(e){return e&&e.__esModule?e:{default:e}}function p(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}var d=1,h=2,e=(i(f,[{key:"destroy",value:function(){this._cleanTimer(),this._sliceMovingChecker&&(clearInterval(this._sliceMovingChecker),this._sliceMovingChecker=void 0)}},{key:"onWaiting",value:function(){this.videoProxy.ended||this.videoProxy.seeking||(this.stateMgr.stuck=!0,this._processRecentStuck(),this._newStatistics(),this._report(),this._stuckChecker.start())}},{key:"_checkSliceIdIsMoving",value:function(){var t=this;return this.stateMgr.nextWriteId===this._lastCheckWriteId?(l.default.MonitorPlayHeadMoving(this.videoProxy).then(function(e){e=e.moving;e||t.onWaiting()}),!1):(this.onCanplay(),this._lastCheckWriteId=this.stateMgr.nextWriteId,!0)}},{key:"_report",value:function(){this._reportStall(),this._reportGap()}},{key:"_reportStall",value:function(){var e=this.tp2p.bufferLength,t=this.tp2p.config,r=(e>t.stuckThreshold&&(this._reportData[s.ReportField.PLAY_STUCK_TIRED]+=1),Date.now()-t.loadTime),i=r.toFixed(2);if(this._reportData[s.ReportField.PLAY_STUCK_SHORT]+=1,r<t.initStuckThreshold&&(this._reportData[s.ReportField.PLAY_STUCK_INIT]+=1,this._stuckInitTime.push(i)),r<t.mainStuckThreshold&&(this._reportData[s.ReportField.PLAY_STUCK_MAIN]+=1,this._stuckMainTime.push(i)),this._reportData[s.ReportField.PLAY_STUCK_SHORT]<5){for(var n="",a="",o=0;o<this.initiator.pcdnStreams;o++)n+="-"+this.stateMgr.getMaxSubStreamId(o),a+=this.stateMgr.getSubStreamState(o);this._reportData[s.ReportField.STUCK_ID_PCDN]+="next:"+this.stateMgr.nextWriteId+"_buflen:"+e.toFixed(2)+"_submaxid:"+n+"_substate:"+a+", "}}},{key:"_reportGap",value:function(){var e=l.default.VideoInfo(this.videoProxy);this.tp2p.config.enableReportGap&&this._reportData[s.ReportField.VIDEO_BUFFERED].push(e)}},{key:"_processRecentStuck",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"waiting",t=Date.now(),r=this._recentStuck,i=this._stuckInterval,n=r&&r[r.length-1];for(n&&!n[1]&&(n[1]=t),"waiting"===e&&r.push([t,0]);;){var a=r&&r[0]&&r[0][0];if(isNaN(a)||!(1e3*i<=t-a))break;r.shift()}this._processHundredStuck()}},{key:"_processHundredStuck",value:function(){var t=Date.now(),e=this._recentStuck,r=this._stuckInterval,i=0,n=0,e=(e.forEach(function(e){i+=1,n+=(e[1]||t)-e[0]}),i=Math.round(i/r*100),n=Math.round(n/(1e3*r)*100),this._reportData[s.ReportField.HUNDRED_STUCK_CNT]=i,this._reportData[s.ReportField.HUNDRED_STUCK_DUR]=n,this.tp2p.config),r=e.hundredSecStuckCnt,e=e.hundredSecStuckDur;r&&e&&(r<=i||e<=n)&&this._rollback()}},{key:"_newStatistics",value:function(){var e=this.stateMgr,t=e.loadingMain,r=e.playId,i=e.confirmLossSkipSliceId,e=e.frameRate,t=(this._reportData[s.ReportField.STUCK_TOTAL]+=1,this._reportData[s.ReportField["STUCK_FROM_"+(t?"MAIN":"SUB")]]+=1,Math.round(1e3*this.tp2p.bufferLength)/1e3);this._reportData[s.ReportField.STUCK_FIRST_BUFFER_LEN].length||(this._reportData[s.ReportField.STUCK_FIRST_BUFFER_LEN]=t),this._reportData[s.ReportField.STUCK_ALL_BUFFER_LEN]+="-"+t,Math.abs(r-i)<=e&&(this._reportData[s.ReportField.STUCK_FROM_CL]+=1)}},{key:"_processStuck",value:function(){var e,t,r,i=void 0;void 0!==this._stallTime&&(i=Date.now()-this._stallTime,t=(e=this.tp2p.config).stuckCauseSkip,r=e.stuckCauseRollback,e.stuckCauseReload<=i?this._reload():r<=i?this._rollback():!this._skipping&&t<=i&&(this._skipping=!0,this._reportData[s.ReportField.PLAY_SKIP]+=1,this.tp2p.trigger(o.default.WRITE_SKIP,{retry:!0})))}},{key:"_reload",value:function(){this._cleanTimer(),this._reportData[s.ReportField.STUCK_TRIGGER_RELOAD_COUNT]+=1,this.tp2p.trigger(o.default.STUCK_RELOAD)}},{key:"_rollback",value:function(){var e,t,r,i,n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"";n||(i=(r=this.tp2p.config).enableStuckReload,e=r.originalUrl,t=r.enableRollbackOther,r=r.rollbackWhileList,i?t&&a.default.DomainStuckRollback(e,r)&&(n=h):n=d),n&&(this._cleanTimer(),this._reportData[s.ReportField.ROLLBACK_STUCK_CODE]=n,this.tp2p.trigger(o.default.ERROR,{fatal:!0,type:c.ErrorTypes.NETWORK_ERROR,details:c.ErrorDetails.STUCK_ROLLBACK,rollbackCode:s.ReportCode.ROLLBACK,extendReportData:(p(i={},s.ReportField.ROLLBACK_REASON,s.ReportField.ROLLBACK_STUCK),p(i,s.ReportField.ROLLBACK_STUCK_CODE,n),i)}))}},{key:"onCanplay",value:function(){var e;!this._stallTime||this.videoProxy.ended||this.videoProxy.seeking||(this._processRecentStuck(),this.stateMgr.stuck=!1,this._skipping=!1,this.stateMgr.reloadingMain=!1,e=Date.now()-this._stallTime,this._cleanTimer(),e<60&&(--this._reportData[s.ReportField.PLAY_STUCK_SHORT],this._reportData[s.ReportField.IGNORE_STUCK]+=1),this.stateMgr.playSkipSucc&&(this._reportData[s.ReportField.PLAY_SKIP_SUCC]+=1,this.stateMgr.playSkipSucc=!1),f.IsNumeric(e)&&(this._reportData[s.ReportField.STUCK_DURATION]+=e,this._reportData[s.ReportField.STUCK_DURAS].push(e.toFixed(2))))}},{key:"_cleanTimer",value:function(){this._stuckChecker.stop()}},{key:"reportCallback",value:function(){var t=this._reportData;return t[s.ReportField.STUCK_COUNT]=t[s.ReportField.PLAY_STUCK_SHORT],t[s.ReportField.STUCK_ALL_BUFFER_LEN]&&(t[s.ReportField.STUCK_ALL_BUFFER_LEN]=t[s.ReportField.STUCK_ALL_BUFFER_LEN].slice(1)),t[s.ReportField.INIT_STUCK_TIME]=this._stuckInitTime.join("-"),t[s.ReportField.MAIN_STUCK_TIME]=this._stuckMainTime.join("-"),Object.keys(t).forEach(function(e){t[e]&&0!==t[e].length||delete t[e]}),this._initReporterData(),t}},{key:"_initReporterData",value:function(){this._stuckMainTime=[],this._stuckInitTime=[],this._reportData={};var e=this._reportData;e[s.ReportField.PLAY_STUCK_SHORT]=0,e[s.ReportField.STUCK_COUNT]=0,e[s.ReportField.STUCK_DURATION]=0,e[s.ReportField.PLAY_STUCK_TIRED]=0,e[s.ReportField.PLAY_SKIP]=0,e[s.ReportField.PLAY_SKIP_SUCC]=0,e[s.ReportField.STUCK_ID_PCDN]="",e[s.ReportField.PLAY_STUCK_INIT]=0,e[s.ReportField.PLAY_STUCK_MAIN]=0,e[s.ReportField.STUCK_TRIGGER_RELOAD_COUNT]=0,e[s.ReportField.STUCK_TOTAL]=0,e[s.ReportField.STUCK_FROM_MAIN]=0,e[s.ReportField.STUCK_FROM_SUB]=0,e[s.ReportField.STUCK_FROM_CL]=0,e[s.ReportField.STUCK_FIRST_BUFFER_LEN]=0,e[s.ReportField.STUCK_ALL_BUFFER_LEN]="",e[s.ReportField.INIT_STUCK_TIME]="",e[s.ReportField.MAIN_STUCK_TIME]="",e[s.ReportField.HUNDRED_STUCK_CNT]=0,e[s.ReportField.HUNDRED_STUCK_DUR]=0,e[s.ReportField.VIDEO_BUFFERED]=[],e[s.ReportField.FAKE_STUCK]=0,e[s.ReportField.IGNORE_STUCK]=0,e[s.ReportField.STUCK_SEEK]=[],e[s.ReportField.STUCK_RECOVER_BY_SEEK]=0,e[s.ReportField.STUCK_DURAS]=[]}}],[{key:"IsNumeric",value:function(e){return!isNaN(parseFloat(e))&&isFinite(e)}}]),f);function f(e,t){var r=this;if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");this.TAG="StuckController",this.initiator=e,this.stateMgr=e.stateMgr,this.videoProxy=e.videoProxy,this.tp2p=t,this._lastCheckWriteId=0,this._initReporterData(),this.tp2p.registerReportCallback(this.TAG,this.reportCallback.bind(this)),this._stuckInterval=this.tp2p.config.recordRecentStuckInterval,this._recentStuck=[],this._skipping=!1,"Firefox"===this.tp2p.config.browser.name&&(this._sliceMovingChecker=setInterval(this._checkSliceIdIsMoving.bind(this),1e3)),this._stuckChecker={start:function(){r._stuckCheckTimer||(r._stallTime=Date.now(),r._stuckCheckTimer=setInterval(r._processStuck.bind(r),1e3),r.tp2p.config.enableStuckSeek&&l.default.MonitorPlayHeadMoving(r.videoProxy).then(function(e){var e=e.moving,t=r._reportData;e?(r._stuckChecker.stop(),t[s.ReportField.FAKE_STUCK]+=1):1<r.videoProxy.bufferLength&&l.default.SeekALittle(r.videoProxy).then(function(e){t[s.ReportField.STUCK_SEEK].push(e),t[s.ReportField.STUCK_RECOVER_BY_SEEK]+=1})}))},stop:function(){r._stuckCheckTimer&&(clearInterval(r._stuckCheckTimer),r._stuckCheckTimer=void 0,r._stallTime=void 0)}}}r.default=e},{103:103,132:132,69:69,71:71,88:88}],105:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(a,[{key:"onMessage",value:function(e){var e=e.data,t=e.cmd,e=e.data;"init"===t&&this.init(e)}},{key:"init",value:function(e){var t=this,r=e.playStartTimeout,e=e.startTime,r=r-(Date.now()-e);this.timing.outWorkerInit=e,this.timing.inWorkerInit=Date.now(),0<r?setTimeout(function(){t.timing.inWorkerTimeout=Date.now(),t.timing.cmd="timeout",t.postMessage(t.timing)},r):(this.timing.inWorkerTimeout=Date.now(),this.timing.cmd="timeout",this.postMessage(this.timing))}},{key:"postMessage",value:function(e){this.self.postMessage(e)}}]);var n=a;function a(e){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function");(this.self=e).addEventListener("message",this.onMessage.bind(this)),this.timing={}}r.default=function(e){new n(e)}},{}],106:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=c(e(104)),o=c(e(100)),s=c(e(99));function c(e){return e&&e.__esModule?e:{default:e}}function l(e,t){if(!(this instanceof l))throw new TypeError("Cannot call a class as a function");this.TAG="VideoManager",this.initiator=e,this.tp2p=t,this.videoProxy=e.videoProxy,this.stateMgr=e.stateMgr,this.stuckController=new a.default(e,t),this.playbackController=new o.default(e,t),this.delayManager=new s.default(e,t,this.videoProxy),this.timeupdateHandler=this.onTimeupate.bind(this),this.canplayHandler=this.stuckController.onCanplay.bind(this.stuckController),this.waitingHandler=this.stuckController.onWaiting.bind(this.stuckController),this.videoProxy.on("timeupdate",this.timeupdateHandler),"Firefox"!==this.tp2p.config.browser.name&&this.videoProxy.on("canplay",this.canplayHandler),this.videoProxy.on("loadeddata",this.playbackController.onLoadedData.bind(this.playbackController)),this.videoProxy.on("loadedmetadata",this.playbackController.onLoadedMetadata.bind(this.playbackController))}i(l,[{key:"destroy",value:function(){this.delayManager.destroy(),this.stuckController.destroy(),this.playbackController.destroy()}},{key:"onTimeupate",value:function(){this.videoProxy.removeListener("timeupdate",this.timeupdateHandler),"Firefox"!==this.tp2p.config.browser.name&&this.videoProxy.on("waiting",this.waitingHandler)}},{key:"onStarting",value:function(){this.delayManager.onStarting()}},{key:"onStopping",value:function(){this.delayManager.onStopping()}}]),r.default=l},{100:100,104:104,99:99}],107:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=s(e(39)),o=s(e(137));function s(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(){var e;if(this instanceof r)return(e=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this))).TAG="VideoProxy",e._videoElement=document.createElement("video"),e._listenEvent=["waiting","canplay","loadedmetadata","loadeddata","ratechange","timeupdate"],e._handler=new Map,e._genHandler(),e;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"destroy",value:function(){this._videoElement&&(this.releaseVideoElement(),this._videoElement=null)}},{key:"setVideoByElement",value:function(e){this._videoElement&&this.releaseVideoElement(),this._videoElement=e,this.attachVideoElement()}},{key:"setVideoById",value:function(e){e=document.getElementById(e);this.setVideoByElement(e)}},{key:"attachVideoElement",value:function(){var r=this;this._handler.forEach(function(e,t){r._videoElement.addEventListener(t,e)})}},{key:"releaseVideoElement",value:function(){var r=this;this._handler.forEach(function(e,t){r._videoElement.removeEventListener(t,e)})}},{key:"onWaiting",value:function(){this.emit("waiting")}},{key:"onCanplay",value:function(){this.emit("canplay")}},{key:"onLoadedmetadata",value:function(){this.emit("loadedmetadata")}},{key:"onLoadeddata",value:function(){this.emit("loadeddata")}},{key:"onRatechange",value:function(){this.emit("ratechange")}},{key:"onTimeupdate",value:function(){this.emit("timeupdate")}},{key:"_transEventToFunc",value:function(e){if(!e)throw new TypeError("Video-Proxy: param event must be a non-empty string");if(e.length<4)throw new Error("Video-Proxy: no such event");return"on"+e[0].toUpperCase()+e.slice(1)}},{key:"_genHandler",value:function(){var r=this;this._listenEvent.forEach(function(e){var t=r._transEventToFunc(e);if(!r[t])throw new Error("Video-Proxy: no such function");r._handler.set(e,r[t].bind(r))})}},{key:"currentTime",get:function(){return this._videoElement.currentTime},set:function(e){this._videoElement.currentTime=e}},{key:"seeking",get:function(){return this._videoElement.seeking}},{key:"ended",get:function(){return this._videoElement.ended}},{key:"video",get:function(){return this._videoElement}},{key:"buffered",get:function(){return this._videoElement.buffered}},{key:"playbackRate",set:function(e){this._videoElement.playbackRate=e},get:function(){return this._videoElement.playbackRate}},{key:"readyState",get:function(){return this._videoElement.readyState}},{key:"bufferLength",get:function(){var e,t=this.video;return t?(e=t.currentTime,o.default.bufferInfo(t,e,0).len):0}}]),r}(a.default);r.default=e},{137:137,39:39}],108:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});r.Uri={PING:1,PONG:2,AV:3,SUB:4,TREE:5,P2P_FILL_REQ:6,P2P_FILL_RES:7},r.PeerConnectionState={FAIL:"fail",OPEN:"open",ICE:"ice",ERROR:"error",REPORT:"report"},r.SubscribeEvent={SUBSCRIBE:1,SUBSCRIBE_SUCC:2,SUBSCRIBE_FAIL:3,UNSUBSCRIBE:4,SUBSCRIBE_TREE:5},r.PieceUsage={NORMAL:0,FILL:1,PUSH:2}},{}],109:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(a,[{key:"destroy",value:function(){this.subs=null}},{key:"addPeer",value:function(e,t){this.allPeers.set(e,t)}},{key:"deletePeer",value:function(e){this.allPeers.delete(e)}},{key:"sendToPeer",value:function(e,t,r){var i=this,n=this.allPeers.get(e);n&&setTimeout(function(){r&&(t=i._updateSeq(n,t)),n.send(t)},0)}},{key:"addSub",value:function(e,t){this.subs.set(e,t)}},{key:"deleteSub",value:function(e){this.subs.delete(e)}},{key:"sendToAllSub",value:function(t){var r=this;this.subs.forEach(function(e){setTimeout(function(){t=r._updateSeq(e,t),e.send(t)})})}},{key:"_updateSeq",value:function(e,t){var r;return e&&(r=e.seq||0,t instanceof Array)&&(t.forEach(function(e){new DataView(e).setUint32(1,r),r+=1}),e.seq=r),t}},{key:"peerSize",get:function(){return this.allPeers.size}},{key:"subsSize",get:function(){return this.subs.size}}]);var n=a;function a(){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function");this.TAG="DatachannelManager",this.subs=new Map,this.allPeers=new Map}r.default=n},{}],110:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var o=a(e(123)),s=a(e(124)),c=a(e(71)),l=a(e(70)),u=e(132),p=e(108);function a(e){return e&&e.__esModule?e:{default:e}}function v(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)}e=function(e){var t=a;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function a(e,t,r,i,n){if(this instanceof a)return(t=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,t,c.default.TRY_P2P_FILL,c.default.P2P_FILL_RES,c.default.P2P_FILL_REQ,c.default.P2P_FILL_SUCCESS,c.default.SUBSCRIBE_FILL))).TAG="FillManager",t.initiator=e,t.storage=e.storage,t.stateMgr=e.stateMgr,t.dataChannelManager=r,t.sliceManager=i,t.peerManager=n,t.baseSub=e.pcdnStreams,t._initReporterData(),t.tp2p.registerReportCallback(t.TAG,t.reportCallback.bind(t)),t;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(a,[{key:"destroy",value:function(){this._pushSubTimer&&clearInterval(this._pushSubTimer),l.default.prototype.destroy.call(this),this.tp2p=null}},{key:"onTryP2pFill",value:function(e){var t,r,e=e.missing,i=(this._reportData[u.ReportField.FILL_TRY_P]+=e.length,void 0),n=void 0;this.dataChannelManager.peerSize?(r=this.groupPeers(),t=void 0,i=(t=this.tp2p.config.patchAccurateFill?this.assignPeer(e,r):this.pickPeer(e,r)).p2pMissing,n=t.fillMapping,r=this.generateReq(n),this._reportData[u.ReportField.FILL_REQ_P]+=r.length,this._reportData[u.ReportField.FILL_MISS_P]+=i.length,this.send(r)):(this._reportData[u.ReportField.FILL_NO_PEER_MISS]+=(i=e).length,this.tp2p.trigger(c.default.P2P_FILL_FRAGMENT_FAIL,{failReason:"missing",sliceArray:i}))}},{key:"onSubscribeFill",value:function(e){var i=this,t=e.from,e=e.startSliceId;if(this.tp2p.config.enableSubFill&&(this._pushQueue||(this._pushQueue=[]),e)){for(var r=this.stateMgr.maxXSubStreamId,n=e;n<=r;n+=this.baseSub)this.storage.has(n)?this._pushQueue.push({peerId:t,sliceId:n}):this._reportData[u.ReportField.PUSH_MISS]+=1;this._pushSubTimer||(this._pushSubTimer=setInterval(function(){var e=Math.min(i._pushQueue.length,5);if(e)for(var t=0;t<e;t++){i._reportData[u.ReportField.PUSH_REQ]+=1;var r=i._pushQueue.shift();i.sendRes(r.peerId,r.sliceId,p.PieceUsage.PUSH)}},100))}}},{key:"groupPeers",value:function(){for(var r=[],e=(this.peerManager.forEach(function(e,t){e&&((e=e.getPeerInfo()).sort(function(e,t){return e.rtt-t.rtt}),r[t]=e)}),r.length),t=0;t<e;t++)r[t]||(r[t]=[]);return r}},{key:"pickPeer",value:function(e,i){var n=this,a=[];return e.forEach(function(e){var t=e%n.baseSub,r=i[t];n.storage.has(e)||(t=n.peerManager[t])&&(0<=(t=r.indexOf(t.sub))&&r.splice(t,1),0!==(t=(t=n._findInfo(r,e))||n._findInfo((r=[]).concat.apply(r,v(i)),e)))&&a.push({peerId:t,sliceId:e})}),{p2pMissing:[],fillMapping:a}}},{key:"_findInfo",value:function(e,t){var r=0,i=0;if(e&&e.length){var n=!0,a=!1,o=void 0;try{for(var s,c=e[Symbol.iterator]();!(n=(s=c.next()).done);n=!0){var l=s.value;(0===r||l.remoteMaxSubId>r&&t>l.remoteMinSliceId)&&(r=l.remoteMaxSubId,i=l.peerId)}}catch(e){a=!0,o=e}finally{try{!n&&c.return&&c.return()}finally{if(a)throw o}}}return i}},{key:"assignPeer",value:function(e,c){var l=this,u=[],p=[],t=[],d=!1;if(e.forEach(function(e){var t=e%l.baseSub,t=c[t];if(d=!1,t&&t.length){var r=!0,i=!1,n=void 0;try{for(var a,o=t[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){var s=a.value;if(e>s.remoteMinSliceId&&e<s.remoteMaxSubId){u.push({peerId:s.peerId,sliceId:e}),d=!0;break}}}catch(e){i=!0,n=e}finally{try{!r&&o.return&&o.return()}finally{if(i)throw n}}}d||p.push(e)}),p.length)for(var r=(e=[]).concat.apply(e,v(c)),i=(r.sort(function(e,t){return e.score-t.score}),p.length),n=0;n<i;n++){var a=p[n],d=!1,o=!0,s=!1,h=void 0;try{for(var f,_=r[Symbol.iterator]();!(o=(f=_.next()).done);o=!0){var y=f.value;if(a>y.remoteMinSliceId&&a<y.remoteMaxContinuousId){u.push({peerId:y.peerId,sliceId:a}),d=!0;break}}}catch(e){s=!0,h=e}finally{try{!o&&_.return&&_.return()}finally{if(s)throw h}}d||t.push(a)}return{p2pMissing:t,fillMapping:u}}},{key:"generateReq",value:function(e){var r=[];return e.forEach(function(e){var t=new o.default;t.fillSliceId=e.sliceId,r.push({peerId:e.peerId,data:t.encode()})}),r}},{key:"onP2pFillReq",value:function(e){var t=e.remotePeerId,e=e.pkt,r=new o.default,e=(this._reportData[u.ReportField.ON_FILL_REQ_P]+=1,r.decode(e),r.fillSliceId);this.sendRes(t,e,p.PieceUsage.FILL)}},{key:"sendRes",value:function(e,t,r){var i;this.storage.has(t)?(i=this.storage.get(t),r===p.PieceUsage.FILL?(this._reportData[u.ReportField.FILL_P_S_BYTES]+=i.payload.length,this._reportData[u.ReportField.HAS_FILL_REQ_P]+=1):this._reportData[u.ReportField.PUSH_S_BYTES]+=i.payload.length,this.sliceManager.receiveFillSlice(e,t,i.payload,i.tagTime,r)):r===p.PieceUsage.PUSH?this._reportData[u.ReportField.PUSH_MISS]+=1:(i=this.generateEmptyRes(t),this.dataChannelManager.sendToPeer(e,i,!1))}},{key:"generateEmptyRes",value:function(e){var t=new s.default;return t.fillSliceId=e,t.findSlice=0,t.encode()}},{key:"onP2pFillRes",value:function(e){e.remotePeerId;var e=e.pkt,t=new s.default;t.decode(e),t.findSlice||(this._reportData[u.ReportField.FILL_EMPTY_P]+=1,this.tp2p.trigger(c.default.P2P_FILL_FRAGMENT_FAIL,{failReason:"notFound",sliceArray:[t.fillSliceId]}))}},{key:"onP2pFillSuccess",value:function(e){var t=e.sliceId,r=e.length,e=e.usage;e===p.PieceUsage.FILL?(this._reportData[u.ReportField.FILL_OK_P]+=1,this._reportData[u.ReportField.FILL_P_U_BYTES]+=r,this.tp2p.trigger(c.default.P2P_FILL_FRAGMENT_SUCCESS,{sliceArray:[t]})):e===p.PieceUsage.PUSH&&(this._reportData[u.ReportField.PUSH_OK]+=1,this._reportData[u.ReportField.PUSH_U_BYTES]+=r)}},{key:"send",value:function(e){var t=this;e.forEach(function(e){t.dataChannelManager.sendToPeer(e.peerId,e.data,!1)})}},{key:"_initReporterData",value:function(){this._reportData={};var e=this._reportData;e[u.ReportField.FILL_TRY_P]=0,e[u.ReportField.FILL_NO_PEER_MISS]=0,e[u.ReportField.FILL_REQ_P]=0,e[u.ReportField.ON_FILL_REQ_P]=0,e[u.ReportField.FILL_OK_P]=0,e[u.ReportField.FILL_MISS_P]=0,e[u.ReportField.FILL_EMPTY_P]=0,e[u.ReportField.FILL_P_S_BYTES]=0,e[u.ReportField.HAS_FILL_REQ_P]=0,e[u.ReportField.FILL_P_U_BYTES]=0,e[u.ReportField.PUSH_REQ]=0,e[u.ReportField.PUSH_OK]=0,e[u.ReportField.PUSH_MISS]=0,e[u.ReportField.PUSH_S_BYTES]=0,e[u.ReportField.PUSH_U_BYTES]=0}},{key:"reportCallback",value:function(){var t=this._reportData;return Object.keys(t).forEach(function(e){t[e]||delete t[e]}),this._initReporterData(),t}}]),a}(l.default);r.default=e},{108:108,123:123,124:124,132:132,70:70,71:71}],111:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=e(108),o=c(e(120)),s=c(e(121));function c(e){return e&&e.__esModule?e:{default:e}}i(l,[{key:"destroy",value:function(){this._clearTimer()}},{key:"_clearTimer",value:function(){this._onCheckTimer&&(clearInterval(this._onCheckTimer),this._onCheckTimer=null)}},{key:"start",value:function(){this.dataChannelOpen=!0}},{key:"onCheck",value:function(){var e=Date.now()-this._lastSyncTime,t="initHeartTimeout";("initHeartTimeout"===(t=this.dataChannelOpen?"heartTimeout":t)?this._heartTimeout:this._keepTimeout)<e?this.pc.callbacks.onPeerConnectionState({remoteId:this.pc.remoteId,type:a.PeerConnectionState.FAIL,reason:t}):this.dataChannelOpen&&this.sendPing()}},{key:"sendPing",value:function(){var e=new o.default;e.lastSliceId=this.stateMgr.maxXSubStreamId,e.minSliceId=this.stateMgr.minSliceId,e.maxContinuousId=this.stateMgr.maxContinuousId,e.sendTime=Date.now()-this._timeBase,this.pc.send(e.encode())}},{key:"receivePing",value:function(e){this._lastSyncTime=Date.now();var t=new o.default;t.decode(e),this.peerInfo.sampleRemoteId({lastSliceId:t.lastSliceId,minSliceId:t.minSliceId,maxContinuousId:t.maxContinuousId}),this.sendPong(t.sendTime)}},{key:"sendPong",value:function(e){var t=new s.default;t.lastSliceId=this.stateMgr.maxXSubStreamId,t.minSliceId=this.stateMgr.minSliceId,t.maxContinuousId=this.stateMgr.maxContinuousId,t.lastPingTime=e,this.pc.send(t.encode())}},{key:"receivePong",value:function(e){this._lastSyncTime=Date.now();var t=new s.default,e=(t.decode(e),Date.now()-this._timeBase-t.lastPingTime);this.peerInfo.sampleRtt(e),this.peerInfo.sampleRemoteId({lastSliceId:t.lastSliceId,minSliceId:t.minSliceId,maxContinuousId:t.maxContinuousId})}}]);e=l;function l(e,t,r,i){if(!(this instanceof l))throw new TypeError("Cannot call a class as a function");this.TAG="Heartbeat",this.pc=t,this.peerInfo=r,this.stateMgr=i,this.dataChannelOpen=!1,this._lastSyncTime=Date.now(),this._heartTimeout=e.PCHeartTimeout,this._keepTimeout=e.PCKeepTimeout,this._timeBase=Date.now(),this._onCheckTimer=setInterval(this.onCheck.bind(this),1e3)}r.default=e},{108:108,120:120,121:121}],112:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=v(e(109)),o=v(e(126)),s=v(e(116)),c=v(e(75)),l=v(e(71)),u=v(e(70)),p=v(e(66)),d=v(e(65)),h=v(e(113)),f=v(e(110)),_=e(132),y=v(e(127));function v(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=n;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function n(e,t){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function");var r=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,t,l.default.SIGNAL_RECEIVING,l.default.CDN_LOADED,l.default.P2P_LOADED,l.default.P2P_STARTING,l.default.P2P_STOPPING,l.default.CHANGE_PEER));r.TAG="P2PController",r._xStreamId=e.xStreamId,r.streams=e.pcdnStreams,r._localStreamShare=t.config.localStreamShare,r._maxSubLevel=t.config.maxSubLevel,r.trackerLoader=new p.default(t),r.trackerController=new d.default(t),r.signalClient=new c.default(t),r.dataChannelManager=new a.default,r.p2pMonitor=new h.default(e,t),r.sliceManager=new o.default(e,t,r.dataChannelManager,r.p2pMonitor),r.webrtcStatistic=new y.default({enableSort:t.config.rtcReportDeduplication}),r.peerManager=[];for(var i=0;i<r.streams;i++)!r._localStreamShare&&i===r._xStreamId||(r.peerManager[i]=new s.default(e,t,{dataChannelManager:r.dataChannelManager,sliceManager:r.sliceManager,p2pMonitor:r.p2pMonitor,webrtcStatistic:r.webrtcStatistic},{managerStreamId:i}));return r.fillManager=new f.default(e,t,r.dataChannelManager,r.sliceManager,r.peerManager),r.tp2p.registerReportCallback(r.TAG,r.reportCallback.bind(r)),r._initReporterData(),r}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(n,[{key:"onSignalReceiving",value:function(e){var t=e.streamId;this.peerManager[t]&&this.peerManager[t].receiveSignal(e)}},{key:"onCdnLoaded",value:function(e){this.sliceManager.receiveSlice(e.sliceId,e.payload,e.tagTime)}},{key:"onP2pLoaded",value:function(e){var t=e.sliceId,r=e.payload,e=e.tagTime;this._localStreamShare&&t%this.streams===this._xStreamId&&(this.sliceManager.receiveSlice(t,r,e),this._reportData[_.ReportField.P2P_FROM_SHARE_BYTES]+=r.byteLength)}},{key:"onChangePeer",value:function(e){e=e.subStreamId,e=this.peerManager[e];e&&e.changePeer()}},{key:"onP2pStarting",value:function(){this._reportData[_.ReportField.START_P2P]+=1,this.tp2p.trigger(l.default.TRACKER_STARTING),this.peerManager.forEach(function(e){e&&e.onStarting()})}},{key:"onP2pStopping",value:function(){this._reportData[_.ReportField.STOPPING_P2P]+=1,this.tp2p.trigger(l.default.TRACKER_STOPPING),this.peerManager.forEach(function(e){e&&e.onStopping()})}},{key:"destroy",value:function(){this.fillManager.destroy(),this.sliceManager.destroy(),this.p2pMonitor.destroy(),this.trackerLoader.destroy(),this.trackerController.destroy(),this.signalClient.destroy(),this.peerManager.forEach(function(e){e.destroy()}),this.dataChannelManager.destroy(),this.webrtcStatistic.reset()}},{key:"onStopping",value:function(){this.p2pMonitor.onStopping(),this.trackerController.onTrackerStopping(),this.peerManager.forEach(function(e){e&&e.onStopping()})}},{key:"onStarting",value:function(){}},{key:"_initReporterData",value:function(){this._reportData={};var e=this._reportData;e[_.ReportField.MY_STREAM_ID]=this.tp2p.initiator.xStreamId,e[_.ReportField.P2P_FROM_SHARE_BYTES]=0,e[_.ReportField.LOCAL_STREAM_SHARE]=this._localStreamShare?1:0,e[_.ReportField.SUB_TREE_LEVEL]=this._maxSubLevel,e[_.ReportField.STUN_IP]=this.tp2p.config.stunServer,e[_.ReportField.STOPPING_P2P]=0,e[_.ReportField.START_P2P]=0}},{key:"reportCallback",value:function(){this.collectReportData();var e=this._reportData;return this._initReporterData(),e}},{key:"collectReportData",value:function(){var e=this.webrtcStatistic.output();e&&(this._reportData[_.ReportField.PEER_CONN]=JSON.stringify(e));for(var t,r=0;r<this.streams;r++)!this._localStreamShare&&r===this._xStreamId||(t=this.peerManager[r].reportData,this._combineData(t,r))}},{key:"_combineData",value:function(t,r){var i=this;Object.keys(t).forEach(function(e){e===_.ReportField.PEER_NUM?(i._addReport(_.ReportField.CONNECTED,t[e]),i._addObjectReport(_.ReportField.PEER_NUM,r,t[e])):i._addReport(e,t[e])})}},{key:"_addReport",value:function(e,t){var r,i="[object Object]"==={}.toString.call(t);this._reportData[e]||(this._reportData[e]=i?{}:0),i?(r=this._reportData[e],Object.keys(t).forEach(function(e){r[e]=(r[e]||0)+t[e]})):this._reportData[e]+=t}},{key:"_addObjectReport",value:function(e,t,r){this._reportData[e]||(this._reportData[e]={}),this._reportData[e]["stream"+t]=r}}]),n}(u.default);r.default=e},{109:109,110:110,113:113,116:116,126:126,127:127,132:132,65:65,66:66,70:70,71:71,75:75}],113:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=e(132),o=u(e(125)),s=e(83),c=u(e(145)),l=u(e(71));function u(e){return e&&e.__esModule?e:{default:e}}i(p,[{key:"_init",value:function(){for(var e,t=0;t<this.baseSub;t++)(this.localStreamShare||t!==this.xStreamId)&&(this.subUpdateTime[t]=Date.now()+1e4,this.subSampleCount[t]=0,e={sampleDuration:this.config.sampleDuration,frameRate:this.stateMgr.frameRate,pcdnStreams:this.config.pcdnStreams},this.ratioCalcu[t]=new o.default(e),this.config.plrEnable)&&(this._plr[t]=new c.default({threshold:e.plrThreshold,cacheLength:e.plrCacheLength,checkDelay:e.plrCheckDelay}))}},{key:"resetStatistics",value:function(e){this._resetP2PAvailableRatio(e),this._plr[e]&&this._plr[e].reset()}},{key:"onStarting",value:function(){var e=this;this._init(),this._sampleTimer=setInterval(function(){e.openSample=!0,e._judgeTimer=setTimeout(e.start.bind(e),2e3)},3e3)}},{key:"onStopping",value:function(){this._judgeTimer&&(clearTimeout(this._judgeTimer),this._judgeTimer=null),this._sampleTimer&&(clearInterval(this._sampleTimer),this._sampleTimer=null)}},{key:"destroy",value:function(){this.onStopping(),this.openSample=!1,this.initiator=null,this.tp2p=null}},{key:"sample",value:function(e,t){e%=this.baseSub;this.ratioCalcu[e].sample(t),!this.openSample||this.tp2p.config.sampleUsefulJudge&&!t||(this.subSampleCount[e]+=1,this.subUpdateTime[e]=Date.now())}},{key:"plrSample",value:function(e,t){var e=e%this.baseSub,r=this._plr[e];r&&r.calculate(t)>=this.config.plrThreshold&&(this._reportData[a.ReportField.SUB_TRY_PLR]+=1,this.tp2p.trigger(l.default.CHANGE_PEER,{subStreamId:e}))}},{key:"start",value:function(){this.openSample=!1;for(var e=0;e<this.baseSub;e++)!this.localStreamShare&&e===this.xStreamId||this.judge(e)}},{key:"judge",value:function(e){var t=Date.now()-this.subUpdateTime[e]<this.config.judgeAliveDelay&&this.subSampleCount[e]>this.config.judgeAliveCount,r=this.stateMgr.getSubStreamState(e);r!==s.SubLoadingState.LOADING_MAIN&&(t&&r===s.SubLoadingState.LOADING?(this.switchController.stopSubStream(e),this._reportData[a.ReportField.P2P_DIS_SUB]+=1):t||r!==s.SubLoadingState.IDLE||(this.switchController.loadSubStream(e),this._reportData[a.ReportField.P2P_EN_SUB]+=1),this.clear(e))}},{key:"clear",value:function(e){this.subSampleCount[e]=0}},{key:"subUseful",value:function(e){return this.ratioCalcu[e].ratio>this.config.p2pUsefulRatio}},{key:"_resetP2PAvailableRatio",value:function(e){var t={sampleDuration:this.config.sampleDuration,frameRate:this.stateMgr.frameRate,pcdnStreams:this.config.pcdnStreams};this.ratioCalcu[e]=new o.default(t)}},{key:"_initReporterData",value:function(){this._reportData={};var e=this._reportData;e[a.ReportField.P2P_DIS_SUB]=0,e[a.ReportField.P2P_EN_SUB]=0,e[a.ReportField.SUB_TRY_PLR]=0}},{key:"reportCallback",value:function(){var e=this._reportData;return this._initReporterData(),e}}]);e=p;function p(e,t){if(!(this instanceof p))throw new TypeError("Cannot call a class as a function");this.TAG="P2PMonitor",this.initiator=e,this.tp2p=t,this.config=t.config,this.stateMgr=e.stateMgr,this.switchController=e.switchController,this.localStreamShare=t.config.localStreamShare,this.xStreamId=e.xStreamId,this.baseSub=e.pcdnStreams,this.subUpdateTime={},this.subSampleCount={},this.openSample=!1,this.ratioCalcu={},this._plr=[],this._initReporterData(),this.tp2p.registerReportCallback(this.TAG,this.reportCallback.bind(this)),this.onStarting()}r.default=e},{125:125,132:132,145:145,71:71,83:83}],114:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=p(e(80)),o=e(108),s=e(78),c=p(e(111)),l=p(e(115)),u=p(e(71));function p(e){return e&&e.__esModule?e:{default:e}}i(d,[{key:"destroy",value:function(){this.webrtcPeerConnection.destroy(),this.peerInfo.destroy(),this.heartbeat.destroy(),this.webrtcPeerConnection=null}},{key:"onStateChange",value:function(e){var t=e.context;if("peerConnection"===t){var r=e.type;"IceConnectionState"===r&&this.callbacks.onPeerConnectionState({type:o.PeerConnectionState.ICE,passive:this.passive,state:e.state})}else if("dataChannel"===t)switch(e.state){case s.DataChannelState.REPORT:this.callbacks.onPeerConnectionState({remoteId:this.remoteId,type:o.PeerConnectionState.REPORT,passive:this.passive,reason:"report",originData:e.originData});break;case s.DataChannelState.CLOSE:this.callbacks.onPeerConnectionState({remoteId:this.remoteId,type:o.PeerConnectionState.FAIL,passive:this.passive,reason:"dataChannelClose"});break;case s.DataChannelState.ERROR:this.callbacks.onPeerConnectionState({remoteId:this.remoteId,type:o.PeerConnectionState.FAIL,passive:this.passive,reason:"dataChannelError"});break;case s.DataChannelState.OPEN:this.callbacks.onPeerConnectionState({remoteId:this.remoteId,type:o.PeerConnectionState.OPEN,passive:this.passive,reason:"dataChannelOpen"}),this.heartbeat.start()}}},{key:"receiveSignal",value:function(e){switch(e.type){case s.SignalType.ANSWER:this.webrtcPeerConnection.receiveSignal({type:s.SignalType.ANSWER,msg:e.payload});break;case s.SignalType.OFFER:this.webrtcPeerConnection.receiveSignal({type:s.SignalType.OFFER,msg:e.payload});break;case s.SignalType.ICE_CANDIDATE:this.webrtcPeerConnection.receiveSignal({type:s.SignalType.ICE_CANDIDATE,msg:e.payload});break;default:this.webrtcPeerConnection.receiveSignal({type:s.SignalType.JUST_START})}}},{key:"onSendBySignalChannel",value:function(e){var t=e.type,r=void 0;switch(t){case s.SignalType.OFFER:r={from:this.localId,to:this.remoteId,streamId:this.streamId,type:s.SignalType.OFFER,payload:e.msg},this._sendBySignaling(r);break;case s.SignalType.ICE_CANDIDATE:r={from:this.localId,to:this.remoteId,streamId:this.streamId,type:s.SignalType.ICE_CANDIDATE,payload:e.msg},this._sendBySignaling(r);break;case s.SignalType.ANSWER:r={from:this.localId,to:this.remoteId,streamId:this.streamId,type:s.SignalType.ANSWER,payload:e.msg},this._sendBySignaling(r)}}},{key:"_sendBySignaling",value:function(e){this.tp2p.trigger(u.default.SIGNAL_SENDING,e)}},{key:"onMessage",value:function(e){switch(new DataView(e).getUint8(0)){case o.Uri.AV:this.sliceManager.receivePiece(e);break;case o.Uri.PING:this.heartbeat.receivePing(e);break;case o.Uri.PONG:this.heartbeat.receivePong(e);break;case o.Uri.SUB:this.callbacks.onSubMessage(e);break;case o.Uri.P2P_FILL_REQ:this.tp2p.trigger(u.default.P2P_FILL_REQ,{remotePeerId:this.remoteId,pkt:e});break;case o.Uri.P2P_FILL_RES:this.tp2p.trigger(u.default.P2P_FILL_RES,{remotePeerId:this.remoteId,pkt:e})}}},{key:"send",value:function(e){this.webrtcPeerConnection._dataChannel.send(e)}},{key:"usableDelay",get:function(){return this.peerInfo.usableDelay}},{key:"rtt",get:function(){return this.peerInfo.rtt}},{key:"score",get:function(){return this.peerInfo.score}},{key:"info",get:function(){return this.peerInfo}}]);e=d;function d(e,t,r,i,n){if(!(this instanceof d))throw new TypeError("Cannot call a class as a function");this.TAG="PeerConnection",this.localId=i.localId,this.remoteId=i.remoteId,this.passive=!!i.passive,this.callbacks=n,this.streamId=e.xStreamId,this.sliceManager=r;i=(this.tp2p=t).config,n={localId:this.localId,remoteId:this.remoteId,ice:{iceServers:[{urls:"stun:"+(i.stunDomain||i.stunServer)}]},bufferedAmountLimit:i.bufferedAmountLimit,enableDCClosing:i.enableDCClosing,enableDCOrdered:i.enableDCOrdered},r={onMessage:this.onMessage.bind(this),onStateChange:this.onStateChange.bind(this),onSendBySignalChannel:this.onSendBySignalChannel.bind(this)};this.webrtcPeerConnection=new a.default(n,r),this.peerInfo=new l.default(e.stateMgr),this.heartbeat=new c.default({PCHeartTimeout:i.PCHeartTimeout,PCKeepTimeout:i.PCKeepTimeout},this,this.peerInfo,e.stateMgr)}r.default=e},{108:108,111:111,115:115,71:71,78:78,80:80}],115:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var e=e(149),a=(e=e)&&e.__esModule?e:{default:e};function o(e){if(!(this instanceof o))throw new TypeError("Cannot call a class as a function");this.stateMgr=e,this.rttWa=new a.default(.5,100),this.remoteMaxId=0,this.remoteMinSliceId=0,this.remoteMaxContinuousId=0}i(o,[{key:"destroy",value:function(){}},{key:"sampleRtt",value:function(e){this.rttWa.sample(e)}},{key:"sampleRemoteId",value:function(e){var t=e.lastSliceId,r=e.minSliceId,e=e.maxContinuousId;this.remoteMaxId=t,this.remoteMinSliceId=r,this.remoteMaxContinuousId=e}},{key:"usableDelay",get:function(){var e=this.stateMgr,t=e.frameRate,r=e.p2pBoundId+t,i=e.nextWriteId,r=this.remoteMaxId>r;return r=e.lowLatency?this.remoteMaxId>i+2*t:r}},{key:"maxSliceId",get:function(){return this.remoteMaxId}},{key:"rtt",get:function(){return this.rttWa.getEstimate()}},{key:"score",get:function(){var e=this.rtt||50;return 100/Math.max(e,1)}}]),r.default=o},{149:149}],116:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=d(e(117)),o=e(132),s=e(78),c=d(e(114)),l=e(108),u=d(e(122)),p=d(e(71));function d(e){return e&&e.__esModule?e:{default:e}}i(h,[{key:"_init",value:function(){this.TAG="PeerManager",this._subed=new Set,this._sub=null,this._docheckPeer=this._checkPeer.bind(this),this._connected=new a.default,this._connecting=new a.default,this._connTryTotal=0,this._connSuccTotal=0,this._peerChangeTs=0,this._dcSendDuration=[0,5,10,20,50,100,1/0],this._dcSendLevelCount=this._dcSendDuration.length-1,this._stoppingP2P=!1,this.onStarting(),this._resetSubTree(),this._initReporterData()}},{key:"onStarting",value:function(){this._stoppingP2P=!1;var e=this.tp2p.config;this._sendSubTreeTimer=setInterval(this._sendSubTree.bind(this),1e3),this._checkPeerTimer=setInterval(this._docheckPeer,e.checkPeerInterval)}},{key:"onStopping",value:function(){var r=this;this._stoppingP2P=!0,this.tp2p.config.clearPeerOnPauseP2P&&(this._connected.forEach(function(e,t){r._deletePC(t)}),this._connecting.forEach(function(e,t){r._deletePC(t)})),this._clearTimer()}},{key:"_clearTimer",value:function(){this._checkPeerTimer&&(clearInterval(this._checkPeerTimer),this._checkPeerTimer=null),this._sendSubTreeTimer&&(clearTimeout(this._sendSubTreeTimer),this._sendSubTreeTimer=null)}},{key:"changePeer",value:function(){var e=Date.now();e-this._peerChangeTs>=this.tp2p.config.checkPeerInterval&&(this._peerChangeTs=e,this._pickUsefulSubPC(!0))}},{key:"destroy",value:function(){this._reportData=null,this._subed=null,this._connected.destroy(),this._connecting.destroy(),this._clearTimer(),this.tp2p=null,this._subTreeInfo=null}},{key:"getPeerInfo",value:function(){var r=this,i=[];return this._connected.forEach(function(e,t){i.push({peerId:t,rtt:e.rtt,parent:t===r._sub,remoteMaxSubId:e.info.remoteMaxId,remoteMinSliceId:e.info.remoteMinSliceId,remoteMaxContinuousId:e.info.remoteMaxContinuousId})}),i}},{key:"_markPCConnected",value:function(e){var t=this._connecting.remove(e);this._connected.add(e,t),this.dataChannelManager.addPeer(e,t.webrtcPeerConnection._dataChannel)}},{key:"_deletePC",value:function(e){this._connected.deletes(e),this._connecting.deletes(e),this._sub===e&&(this._sub=null),this._subed.delete(e),this.dataChannelManager.deleteSub(e),this.dataChannelManager.deletePeer(e),this._subTreeInfo.tree[e]&&delete this._subTreeInfo.tree[e]}},{key:"_checkPCExist",value:function(e){return this._connecting.has(e)||this._connected.has(e)}},{key:"receiveSignal",value:function(e){if(this._stoppingP2P)this._reportData[o.ReportField.STOP_OFFER]+=1;else{var t=e.type,r=e.from,i=e.to;if(t===s.SignalType.OFFER&&!this._checkPCExist(r)){if(!this._canConnect())return void(this.reportData[o.ReportField.REFUSE_OFFER]+=1);this.createPeerConnection({localId:i,remoteId:r,passive:!0})}t=this._connecting.get(r);t&&t.receiveSignal(e)}}},{key:"createPeerConnection",value:function(e){var t=e.localId,r=e.remoteId,e=e.passive,i={onSubMessage:this.onSubMessage.bind(this),onPeerConnectionState:this.onPeerConnectionState.bind(this)},n=void 0;this._reportData[o.ReportField.CONN_TRY]+=1;try{n=new c.default(this.initiator,this.tp2p,this.sliceManager,{localId:t,remoteId:r,passive:e},i)}catch(e){this._reportData[o.ReportField.CREATE_PC_ERROR]+=1}return n&&this._connecting.add(r,n),n}},{key:"_checkPeer",value:function(){this._keepUsefulPCNumber(),this._pickUsefulSubPC(),this._kickUselessPeer(),this._clearUselessSubTree()}},{key:"_resetSubTree",value:function(){this._subTreeInfo={tree:{}}}},{key:"_sendSubTree",value:function(){var r=this,i=this._connected,e=this._subed,t=this.initiator,n=t.localPeerId,t=t.xStreamId;if(this._managerStreamId!==t&&this._sendSubTreeTimer)return clearTimeout(this._sendSubTreeTimer),!1;if(!n)return!1;this._subTreeInfo.tree[n]={parent:this._sub||"",children:[],timestamp:Date.now()};function a(t){var r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,i={},e=s[t];return e&&(i[t]=e).children.forEach(function(e){e!==r&&Object.assign(i,a(e,t))}),i||{}}var o=this._subTreeInfo.tree[n],s=(e.size&&this._subed.forEach(function(e){o.children.push(e)}),this._subTreeInfo.tree),c={childrenTree:function e(t){var r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,i={},n=s[t];return n&&(i[t]=n).parent&&n.parent!==r&&Object.assign(i,e(n.parent,t)),i||{}}(n),parentTree:a(n)};["parent","children"].forEach(function(e){var t=c[e+"Tree"],e=o[e];(e="string"==typeof e?[e]:e).forEach(function(e){e=i.get(e);e&&r.sendMessage(e,l.SubscribeEvent.SUBSCRIBE_TREE,t)})})}},{key:"_clearUselessSubTree",value:function(){var t,r,i,e=this.initiator,n=e.localPeerId,e=e.xStreamId;this._managerStreamId===e&&(t=Date.now(),r=this._subTreeInfo.tree,i=this.tp2p.config.checkPeerInterval,Object.keys(r).forEach(function(e){n!==e&&r[e].timestamp-t>=i&&delete r[e]}))}},{key:"_pickUsefulSubPC",value:function(){var e=0<arguments.length&&void 0!==arguments[0]&&arguments[0],t=this._connected,r=this.initiator,i=r.localPeerId,r=r.xStreamId;if(this._managerStreamId===r){r=this._subTreeInfo.tree[i];if(!r||r.parent||r.children.length)return!1}i=t.get(this._sub);if(!e&&i&&this.p2pMonitor.subUseful(this._managerStreamId))return!1;var n=i;t.forEach(function(e){e.usableDelay&&(!n||e.score>n.score)&&(n=e)}),n&&n!==i&&(this.sendMessage(n,l.SubscribeEvent.SUBSCRIBE),i&&(this._reportData[o.ReportField.CHANGE_SUB]+=1),this._reportData[o.ReportField.SUB_TRY]+=1)}},{key:"_keepUsefulPCNumber",value:function(){var e=this.initiator.localPeerId,t=this.initiator.p2pController.trackerController,r=this.tp2p.config;if(!this._canConnect())return!1;for(var i=0;i++<r.maxConnectRequestEachPeriod;){var n=t.nextStreamPeer(this._managerStreamId);if(!n){this._reportData[o.ReportField.LACK_OF_PEER]+=1;break}n.peerId===e||this._checkPCExist(n.peerId)||(n=this.createPeerConnection({localId:e,remoteId:n.peerId}))&&n.receiveSignal({type:"init"})}}},{key:"_kickUselessPeer",value:function(){var r,i,n=this,e=this._connected,a=this._subed,t=e.size-this.tp2p.config.maxConnectedPC;0<t&&(r=[],i=[],e.forEach(function(e,t){if(!a.has(t)&&t!==n._sub){if(!e.usableDelay)return i.push(e),!1;r.push(e)}}),i.length<t&&r.length&&(r.sort(function(e,t){return e.score-t.score}),i.concat(r.slice(i.length-t))),i.forEach(function(e){n._kickPeer(e.remoteId)&&(n._reportData[o.ReportField.PEER_KICK_WORST]+=1)}))}},{key:"_kickPeer",value:function(e){return!(!this._connected.has(e)||this._subed.has(e)||(this._deletePC(e),0))}},{key:"_canConnect",value:function(){var e=this.tp2p.config,t=e.maxConnectedPC,r=e.maxConnecting,e=e.maxSubscribePCUsefulCount,i=this._connected.filter("usableDelay").length;return!(this._connected.size>=t&&e<=i||this._connecting.size>=r)}},{key:"_canbeSubscribed",value:function(e){var e=e.from,t=this._subed,r=this.initiator,i=r.localPeerId,r=r.xStreamId,n=this.tp2p.config,a=n.maxSubscribedPC,n=n.maxSubLevel,o=!1;return!(t.has(e)||t.size<a&&(o=!0,this._managerStreamId===r)&&n<=this._getCurSubLevel(i))&&o}},{key:"_getCurSubLevel",value:function(e){var t=this._subTreeInfo.tree;return t[e]?t[e].parent?1+this._getCurSubLevel(t[e].parent):1:0}},{key:"sendMessage",value:function(e,t){var r,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:[];e&&((r=new u.default).event=t,r.from=e.localId,r.tree=i,r.startId=this.stateMgr.getLastSubStreamId(this._managerStreamId),e.send(r.encode()))}},{key:"onSubscribe",value:function(e){var t=e.from,r=this._connected.get(t);this._canbeSubscribed(e)?(this.sendMessage(r,l.SubscribeEvent.SUBSCRIBE_SUCC),this._subed.add(t),this.dataChannelManager.addSub(t,r.webrtcPeerConnection._dataChannel),this.tp2p.trigger(p.default.SUBSCRIBE_FILL,{from:t,startSliceId:e.startId})):(this.sendMessage(r,l.SubscribeEvent.SUBSCRIBE_FAIL),this._reportData[o.ReportField.PEER_REFUSE]+=1)}},{key:"onUnSubscribe",value:function(e){var e=e.from,t=this._subed;t.has(e)&&(t.delete(e),this.dataChannelManager.deleteSub(e))}},{key:"onSubscribeSucc",value:function(e){var e=e.from,t=this._connected.get(this._sub);t&&this.sendMessage(t,l.SubscribeEvent.UNSUBSCRIBE),this._reportData[o.ReportField.SUB_SUCC]+=1,this._sub=e,this.p2pMonitor.resetStatistics(this._managerStreamId)}},{key:"onSubscribeFail",value:function(e){e=e.from;this._kickPeer(e)&&(this._reportData[o.ReportField.PEER_KICK_SUB_FAIL]+=1)}},{key:"onSubscribeTree",value:function(e){var r=this,e=e.tree,i=void 0===e?{}:e;Object.keys(i).forEach(function(e){var t;r.initiator.localPeerId!==e&&((t=i[e]).timestamp=Date.now(),r._subTreeInfo.tree[e]=t)})}},{key:"onSubMessage",value:function(e){var t=new u.default;switch(t.decode(e),t.event){case l.SubscribeEvent.SUBSCRIBE:this.onSubscribe(t);break;case l.SubscribeEvent.UNSUBSCRIBE:this.onUnSubscribe(t);break;case l.SubscribeEvent.SUBSCRIBE_FAIL:this.onSubscribeFail(t);break;case l.SubscribeEvent.SUBSCRIBE_SUCC:this.onSubscribeSucc(t);break;case l.SubscribeEvent.SUBSCRIBE_TREE:this.onSubscribeTree(t)}}},{key:"onPeerConnectionState",value:function(e){var t=this.initiator.localPeerId;switch(e.type){case l.PeerConnectionState.REPORT:var r=e.originData.duration;o.Level.DivideLevels(r,this._dcSendDuration,this._reportData[o.ReportField.DC_SEND_DURATION]);break;case l.PeerConnectionState.ERROR:break;case l.PeerConnectionState.FAIL:r=this._sub;this._deletePC(e.remoteId),"initHeartTimeout"===e.reason?(this.gConfig.enableStaticRTC&&!e.passive&&this.webrtcStatistic.record(t,e.remoteId,!1),this._reportData[o.ReportField.INIT_PC_TIMEOUT]+=1):"heartTimeout"===e.reason?this._reportData[o.ReportField.KEEP_PC_TIMEOUT]+=1:"dataChannelError"===e.reason?this._reportData[o.ReportField.DC_ERR]+=1:"dataChannelClose"===e.reason&&(this._reportData[o.ReportField.DC_CLOSE]+=1),this._sub||e.remoteId!==r||this._pickUsefulSubPC();break;case l.PeerConnectionState.OPEN:this.gConfig.enableStaticRTC&&!e.passive&&this.webrtcStatistic.record(t,e.remoteId,!0),this._reportData[o.ReportField.CONN_SUCC]+=1,this._markPCConnected(e.remoteId);break;case l.PeerConnectionState.ICE:}}},{key:"_initReporterData",value:function(){this._reportData={};var e=this._reportData;e[o.ReportField.SUB_TRY]=0,e[o.ReportField.SUB_SUCC]=0,e[o.ReportField.PEER_REFUSE]=0,e[o.ReportField.PEER_KICK_WORST]=0,e[o.ReportField.PEER_KICK_SUB_FAIL]=0,e[o.ReportField.LACK_OF_PEER]=0,e[o.ReportField.P2P_OUT_STREAMS]=0,e[o.ReportField.USEFUL_STREAMS]=0,e[o.ReportField.AVAILABLE_STREAMS]=0,e[o.ReportField.PEER_NUM]=0,e[o.ReportField.CONN_TRY]=0,e[o.ReportField.CONN_SUCC]=0,e[o.ReportField.DC_SEND_DURATION]=o.Level.GenLevels(this._dcSendLevelCount),e[o.ReportField.CREATE_PC_ERROR]=0,e[o.ReportField.CONN_TRY_TOTAL]=this._connTryTotal,e[o.ReportField.CONN_SUCC_TOTAL]=this._connSuccTotal,e[o.ReportField.INIT_PC_TIMEOUT]=0,e[o.ReportField.KEEP_PC_TIMEOUT]=0,e[o.ReportField.REFUSE_OFFER]=0,e[o.ReportField.DC_ERR]=0,e[o.ReportField.DC_CLOSE]=0,e[o.ReportField.STOP_OFFER]=0}},{key:"sub",get:function(){return this._sub}},{key:"reportData",get:function(){this._reportData[o.ReportField.CONN_SUCC_TOTAL]+=this._reportData[o.ReportField.CONN_SUCC],this._connSuccTotal=this._reportData[o.ReportField.CONN_SUCC_TOTAL],this._reportData[o.ReportField.CONN_TRY_TOTAL]+=this._reportData[o.ReportField.CONN_TRY],this._connTryTotal=this._reportData[o.ReportField.CONN_TRY_TOTAL],this._reportData[o.ReportField.AVAILABLE_STREAMS]=0<this._connected.size?1:0,this._reportData[o.ReportField.USEFUL_STREAMS]=this._sub?1:0,this._reportData[o.ReportField.P2P_OUT_STREAMS]=this._subed.size,this._reportData[o.ReportField.PEER_NUM]=this._connected.size,this._reportData[o.ReportField.CHANGE_SUB]=0,delete this._reportData[o.ReportField.DC_SEND_DURATION];var e=this._reportData;return this._initReporterData(),e}}]);e=h;function h(e,t,r,i){var n=r.dataChannelManager,a=r.sliceManager,o=r.p2pMonitor,r=r.webrtcStatistic;if(!(this instanceof h))throw new TypeError("Cannot call a class as a function");this.initiator=e,this.stateMgr=e.stateMgr,this.tp2p=t,this.gConfig=t.config,this.dataChannelManager=n,this.sliceManager=a,this.p2pMonitor=o,this.webrtcStatistic=r,this._managerStreamId=i.managerStreamId,this._init()}r.default=e},{108:108,114:114,117:117,122:122,132:132,71:71,78:78}],117:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function n(){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function");this.map=new Map}Object.defineProperty(r,"__esModule",{value:!0}),function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)}(n,[{key:"destroy",value:function(){this.clear()}},{key:"clear",value:function(){this.map.forEach(function(e){e.destroy()}),this.map.clear()}},{key:"add",value:function(e,t){this.map.set(e,t)}},{key:"has",value:function(e){return this.map.has(e)}},{key:"get",value:function(e){return this.map.get(e)}},{key:"deletes",value:function(e){var t=this.map.get(e);return!!t&&(t.destroy(),this.map.delete(e),!0)}},{key:"remove",value:function(e){var t=this.map.get(e);return this.map.delete(e),t}},{key:"filter",value:function(t){var r=[];return this.map.forEach(function(e){e[t]&&r.push(e)}),r}},{key:"getOne",value:function(){return this.map.values().next().value}},{key:"forEach",value:function(e){this.map.forEach(e)}},{key:"size",get:function(){return this.map.size}}]),r.default=n},{}],118:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var s=r.DataType={UINT8:1,UINT16:2,UINT32:4,CUSTOM:5},i=(i(a,[{key:"push",value:function(e,t,r){this.content.push([e,t,r]),this.len+=r,e===s.CUSTOM&&(this.len+=4)}},{key:"encode",value:function(){var t,r,i,n=0,e=new ArrayBuffer(this.len),a=new DataView(e),o=new Uint8Array(e);return this.content.forEach(function(e){switch(t=e[0],r=e[1],i=e[2],t){case s.UINT8:a.setUint8(n,r),n+=1;break;case s.UINT16:a.setUint16(n,r),n+=2;break;case s.UINT32:a.setUint32(n,r),n+=4;break;case s.CUSTOM:a.setUint32(n,i),n+=4,o.set(r,n),n+=i}}),this.content=[],this.len=0,e}},{key:"pushDecodeType",value:function(e){this.decodeType.push(e)}},{key:"decode",value:function(e){var t,r=void 0,i=0,n=[],a=new DataView(e),o=new Uint8Array(e);return this.decodeType.forEach(function(e){switch(e){case s.UINT8:r=a.getUint8(i),i+=1;break;case s.UINT16:r=a.getUint16(i),i+=2;break;case s.UINT32:r=a.getUint32(i),i+=4;break;case s.CUSTOM:t=a.getUint32(i),i+=4,r=o.slice(i,t+i),i+=t}n.push(r)}),this.decodeType=[],n}}]),a);function a(){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function");this.len=0,this.content=[],this.decodeType=[],this.uri=0}r.default=i},{}],119:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function a(e,t,r){null===e&&(e=Function.prototype);var i=Object.getOwnPropertyDescriptor(e,t);return void 0!==i?"value"in i?i.value:void 0!==(i=i.get)?i.call(r):void 0:null!==(i=Object.getPrototypeOf(e))?a(i,t,r):void 0}var o=e(118),s=(s=o)&&s.__esModule?s:{default:s},c=e(108);e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(){var e;if(this instanceof r)return(e=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this))).uri=c.Uri.AV,e.seq=0,e.sliceId=0,e.tagTime=0,e.index=0,e.totalCount=0,e.usage=c.PieceUsage.NORMAL,e.data=void 0,e;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"encode",value:function(){return this.push(o.DataType.UINT8,this.uri,1),this.push(o.DataType.UINT32,this.seq,4),this.push(o.DataType.UINT32,this.sliceId,4),this.push(o.DataType.UINT32,this.tagTime,4),this.push(o.DataType.UINT8,this.index,1),this.push(o.DataType.UINT8,this.totalCount,1),this.push(o.DataType.UINT8,this.usage,1),this.push(o.DataType.CUSTOM,this.data,this.data.length),a(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"encode",this).call(this)}},{key:"decode",value:function(e){this.pushDecodeType(o.DataType.UINT8),this.pushDecodeType(o.DataType.UINT32),this.pushDecodeType(o.DataType.UINT32),this.pushDecodeType(o.DataType.UINT32),this.pushDecodeType(o.DataType.UINT8),this.pushDecodeType(o.DataType.UINT8),this.pushDecodeType(o.DataType.UINT8),this.pushDecodeType(o.DataType.CUSTOM);e=a(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"decode",this).call(this,e);this.uri=e[0],this.seq=e[1],this.sliceId=e[2],this.tagTime=e[3],this.index=e[4],this.totalCount=e[5],this.usage=e[6],this.data=e[7]}}]),r}(s.default);r.default=e},{108:108,118:118}],120:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function a(e,t,r){null===e&&(e=Function.prototype);var i=Object.getOwnPropertyDescriptor(e,t);return void 0!==i?"value"in i?i.value:void 0!==(i=i.get)?i.call(r):void 0:null!==(i=Object.getPrototypeOf(e))?a(i,t,r):void 0}var o=e(118),s=(s=o)&&s.__esModule?s:{default:s},c=e(108);e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(){var e;if(this instanceof r)return(e=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this))).uri=c.Uri.PING,e.receiveCnt=0,e.sendCnt=0,e.lastSliceId=0,e.minSliceId=0,e.maxContinuousId=0,e.sendTime=0,e;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"encode",value:function(){return this._cal(),this.push(o.DataType.UINT8,this.uri,1),this.push(o.DataType.UINT8,this.receiveCnt,1),this.push(o.DataType.UINT8,this.sendCnt,1),this.push(o.DataType.UINT16,this.lastSliceId,2),this.push(o.DataType.UINT32,this.minSliceId,4),this.push(o.DataType.UINT16,this.maxContinuousId,2),this.push(o.DataType.UINT32,this.sendTime,4),a(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"encode",this).call(this)}},{key:"decode",value:function(e){this.pushDecodeType(o.DataType.UINT8),this.pushDecodeType(o.DataType.UINT8),this.pushDecodeType(o.DataType.UINT8),this.pushDecodeType(o.DataType.UINT16),this.pushDecodeType(o.DataType.UINT32),this.pushDecodeType(o.DataType.UINT16),this.pushDecodeType(o.DataType.UINT32);e=a(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"decode",this).call(this,e);this.uri=e[0],this.receiveCnt=e[1],this.sendCnt=e[2],this.lastSliceId=e[3],this.minSliceId=e[4],this.maxContinuousId=e[5],this.sendTime=e[6],this._reCal()}},{key:"_cal",value:function(){this.lastSliceId=this.lastSliceId-this.minSliceId,this.maxContinuousId=this.maxContinuousId-this.minSliceId}},{key:"_reCal",value:function(){this.lastSliceId=this.lastSliceId+this.minSliceId,this.maxContinuousId=this.maxContinuousId+this.minSliceId}}]),r}(s.default);r.default=e},{108:108,118:118}],121:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function a(e,t,r){null===e&&(e=Function.prototype);var i=Object.getOwnPropertyDescriptor(e,t);return void 0!==i?"value"in i?i.value:void 0!==(i=i.get)?i.call(r):void 0:null!==(i=Object.getPrototypeOf(e))?a(i,t,r):void 0}var o=e(118),s=(s=o)&&s.__esModule?s:{default:s},c=e(108);e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(){var e;if(this instanceof r)return(e=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this))).uri=c.Uri.PONG,e.lastSliceId=0,e.minSliceId=0,e.maxContinuousId=0,e.lastPingTime=0,e;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"encode",value:function(){return this._cal(),this.push(o.DataType.UINT8,this.uri,1),this.push(o.DataType.UINT16,this.lastSliceId,2),this.push(o.DataType.UINT32,this.minSliceId,4),this.push(o.DataType.UINT16,this.maxContinuousId,2),this.push(o.DataType.UINT32,this.lastPingTime,4),a(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"encode",this).call(this)}},{key:"decode",value:function(e){this.pushDecodeType(o.DataType.UINT8),this.pushDecodeType(o.DataType.UINT16),this.pushDecodeType(o.DataType.UINT32),this.pushDecodeType(o.DataType.UINT16),this.pushDecodeType(o.DataType.UINT32);e=a(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"decode",this).call(this,e);this.uri=e[0],this.lastSliceId=e[1],this.minSliceId=e[2],this.maxContinuousId=e[3],this.lastPingTime=e[4],this._reCal()}},{key:"_cal",value:function(){this.lastSliceId=this.lastSliceId-this.minSliceId,this.maxContinuousId=this.maxContinuousId-this.minSliceId}},{key:"_reCal",value:function(){this.lastSliceId=this.lastSliceId+this.minSliceId,this.maxContinuousId=this.maxContinuousId+this.minSliceId}}]),r}(s.default);r.default=e},{108:108,118:118}],122:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function a(e,t,r){null===e&&(e=Function.prototype);var i=Object.getOwnPropertyDescriptor(e,t);return void 0!==i?"value"in i?i.value:void 0!==(i=i.get)?i.call(r):void 0:null!==(i=Object.getPrototypeOf(e))?a(i,t,r):void 0}var o=e(118),s=u(o),c=e(108),l=u(e(138));function u(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(){var e;if(this instanceof r)return(e=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this))).uri=c.Uri.SUB,e.event=0,e.startId=0,e.from="",e.tree=[],e;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"encode",value:function(){this.push(o.DataType.UINT8,this.uri,1),this.push(o.DataType.UINT8,this.event,1),this.push(o.DataType.UINT32,this.startId,4);var e=l.default.str2ab(this.from),e=new Uint8Array(e),e=(this.push(o.DataType.CUSTOM,e,e.length),l.default.str2ab(JSON.stringify(this.tree))),e=new Uint8Array(e);return this.push(o.DataType.CUSTOM,e,e.length),a(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"encode",this).call(this)}},{key:"decode",value:function(e){this.pushDecodeType(o.DataType.UINT8),this.pushDecodeType(o.DataType.UINT8),this.pushDecodeType(o.DataType.UINT32),this.pushDecodeType(o.DataType.CUSTOM),this.pushDecodeType(o.DataType.CUSTOM);var e=a(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"decode",this).call(this,e),t=(this.uri=e[0],this.event=e[1],this.startId=e[2],e[3]),t=(this.from=l.default.ab2str(t.buffer),e[4]);this.tree=JSON.parse(l.default.ab2str(t.buffer))}}]),r}(s.default);r.default=e},{108:108,118:118,138:138}],123:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function a(e,t,r){null===e&&(e=Function.prototype);var i=Object.getOwnPropertyDescriptor(e,t);return void 0!==i?"value"in i?i.value:void 0!==(i=i.get)?i.call(r):void 0:null!==(i=Object.getPrototypeOf(e))?a(i,t,r):void 0}var o=e(118),s=(s=o)&&s.__esModule?s:{default:s},c=e(108);e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(){var e;if(this instanceof r)return(e=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this))).uri=c.Uri.P2P_FILL_REQ,e.fillSliceId=0,e;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"encode",value:function(){return this.push(o.DataType.UINT8,this.uri,1),this.push(o.DataType.UINT32,this.fillSliceId,4),a(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"encode",this).call(this)}},{key:"decode",value:function(e){this.pushDecodeType(o.DataType.UINT8),this.pushDecodeType(o.DataType.UINT32);e=a(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"decode",this).call(this,e);this.uri=e[0],this.fillSliceId=e[1]}}]),r}(s.default);r.default=e},{108:108,118:118}],124:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function a(e,t,r){null===e&&(e=Function.prototype);var i=Object.getOwnPropertyDescriptor(e,t);return void 0!==i?"value"in i?i.value:void 0!==(i=i.get)?i.call(r):void 0:null!==(i=Object.getPrototypeOf(e))?a(i,t,r):void 0}var o=e(118),s=(s=o)&&s.__esModule?s:{default:s},c=e(108);e=function(e){var t=r;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function r(){var e;if(this instanceof r)return(e=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this))).uri=c.Uri.P2P_FILL_RES,e.fillSliceId=0,e.findSlice=0,e;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),i(r,[{key:"encode",value:function(){return this.push(o.DataType.UINT8,this.uri,1),this.push(o.DataType.UINT32,this.fillSliceId,4),this.push(o.DataType.UINT8,this.findSlice,1),a(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"encode",this).call(this)}},{key:"decode",value:function(e){this.pushDecodeType(o.DataType.UINT8),this.pushDecodeType(o.DataType.UINT32),this.pushDecodeType(o.DataType.UINT8);e=a(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"decode",this).call(this,e);this.uri=e[0],this.fillSliceId=e[1],this.findSlice=e[2]}}]),r}(s.default);r.default=e},{108:108,118:118}],125:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(a,[{key:"init",value:function(){for(var e=0;e<this.sampleDuration;e++)this.samples[e]=0}},{key:"sample",value:function(e){var t=Math.floor(Date.now()/1e3);e&&(this.samples.has(t)?(e=this.samples.get(t)+1,this.samples.set(t,e)):this.samples.set(t,1))}},{key:"totalUseful",get:function(){var t=0;return this.samples.forEach(function(e){t+=e}),t}},{key:"ratio",get:function(){var r,i=this;return Date.now()-this.startTime<1e3*this.sampleDuration?1:(r=Math.floor(Date.now()/1e3)-this.sampleDuration,this.samples.forEach(function(e,t){t<=r&&i.samples.delete(t)}),this.totalUseful/(this.sampleDuration*this.frameRate/this.pcdnStreams))}}]);var n=a;function a(e){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function");this.TAG="RatioCalcu",this.frameRate=e.frameRate,this.sampleDuration=e.sampleDuration,this.pcdnStreams=e.pcdnStreams,this.samples=new Map,this.startTime=Math.floor(Date.now()/1e3),this.init()}r.default=n},{}],126:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var c=s(e(119)),a=s(e(71)),o=e(132),l=e(108);function s(e){return e&&e.__esModule?e:{default:e}}i(u,[{key:"_init",value:function(){this.cache=new Map,this._clearCacheTimer=setInterval(this.clearCache.bind(this),1e4)}},{key:"destroy",value:function(){this.cache.clear(),this._clearCacheTimer&&(clearInterval(this._clearCacheTimer),this._clearCacheTimer=null)}},{key:"receiveSlice",value:function(e,t,r){var i,n=this.dataChannelManager.subsSize;n&&(i=this.fragmentedData(t,this._chunkSize),e=this.packetPiece(e,r,i),this._reportData[o.ReportField.P2P_SEND_BYTES]+=t.length*n,this.sendToPeer(e))}},{key:"receiveFillSlice",value:function(e,t,r,i,n){var a=this.fragmentedData(r,this._chunkSize),t=this.packetPiece(t,i,a,n);this._reportData[o.ReportField.P2P_SEND_BYTES]+=r.length,this.dataChannelManager.sendToPeer(e,t)}},{key:"fragmentedData",value:function(e,t){for(var r=e.length,i=Math.ceil(r/t),n=[],a=0;a<i;a++){var o=a*t,s=Math.min(o+t,r),o=e.slice(o,s);n.push(o)}return n}},{key:"packetPiece",value:function(i,n,e){var a=3<arguments.length&&void 0!==arguments[3]?arguments[3]:l.PieceUsage.NORMAL,o=[],s=e.length;return e.forEach(function(e,t){var r=new c.default;r.sliceId=i,r.tagTime=n,r.index=t,r.totalCount=s,r.usage=a,r.data=e,o.push(r.encode())}),o}},{key:"sendToPeer",value:function(e){this.dataChannelManager.sendToAllSub(e)}},{key:"receivePiece",value:function(e){var t=new c.default,e=(t.decode(e),t.sliceId),r=t.seq,i=t.tagTime,n=t.usage;this.addPiece(t)&&(t=this.mergePiece(t),n===l.PieceUsage.NORMAL?this.writeNormalSlice(e,t,i):n!==l.PieceUsage.FILL&&n!==l.PieceUsage.PUSH||this.writeFillSlice(e,t,i,n)),this.p2pMonitor.plrSample(e,r)}},{key:"addPiece",value:function(e){var t=e.sliceId,r=e.index,i=e.totalCount,e=e.data,n=(this._reportData[o.ReportField.P2P_DOWNLOAD_BYTES]+=e.length,this.cache.get(t));return n?n[r]||(n[r]=e,n[i]+=1,n[i+1]+=e.length):((n=[]).length=i+2,n[i]=1,n[i+1]=e.length,n[r]=e,this.cache.set(t,n)),n[i]===i}},{key:"mergePiece",value:function(e){for(var t=e.sliceId,r=e.totalCount,i=this.cache.get(t),n=new Uint8Array(i[r+1]),a=0,o=0;o<r;o++)n.set(i[o],a),a+=i[o].length;return this.cache.delete(t),n}},{key:"storeSlice",value:function(e,t,r){this.tp2p.flow.PBT+=t.length*(this.tp2p.config.bpFix||1);var i=!this.storage.has(e)&&e>=this.stateMgr.nextWriteId;return i&&(this.stateMgr.setSubStreamId(e),this.patchMgr.sliceArrive(e,"p2p"),this.storageMgr.cleanStorage(),this.storage.set(e,{payload:t,tagTime:r}),this._reportData[o.ReportField.P2P_DOWNLOAD_USEFUL_BYTES]+=t.length,this.tp2p.flow.PRB+=t.length*(this.tp2p.config.bpFix||1),this.tp2p.flow.PBU+=t.length*(this.tp2p.config.bpFix||1)),i}},{key:"writeNormalSlice",value:function(e,t,r){var i=this.storeSlice(e,t,r);this.p2pMonitor.sample(e,i),i&&(this.tp2p.config.enableWriteAll&&this.tp2p&&this.tp2p.initiator&&this.tp2p.initiator.writer&&this.tp2p.initiator.writer.onFoundSlice(),this._disableTrigger?this.stateMgr.onP2PLoaded({sliceId:e,payload:t,tagTime:r}):this.tp2p.trigger(a.default.P2P_LOADED,{sliceId:e,payload:t,tagTime:r}))}},{key:"writeFillSlice",value:function(e,t,r,i){this.storeSlice(e,t,r)?this.tp2p.trigger(a.default.P2P_FILL_SUCCESS,{sliceId:e,length:t.length,usage:i}):this._reportData[o.ReportField.FILL_OK_P_USELESS]+=1}},{key:"clearCache",value:function(){if(this.cache.size){var e=this.stateMgr.nextWriteId-1,t=this.cache.keys(),r=!0,i=!1,n=void 0;try{for(var a,o=t[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){var s=a.value;s<e&&this.cache.delete(s)}}catch(e){i=!0,n=e}finally{try{!r&&o.return&&o.return()}finally{if(i)throw n}}}}},{key:"_initReporterData",value:function(){this._reportData={};var e=this._reportData;e[o.ReportField.P2P_SEND_BYTES]=0,e[o.ReportField.P2P_DOWNLOAD_BYTES]=0,e[o.ReportField.P2P_BYTES]=0,e[o.ReportField.P2P_DOWNLOAD_USEFUL_BYTES]=0,e[o.ReportField.FILL_OK_P_USELESS]=0}},{key:"reportCallback",value:function(){this._reportData[o.ReportField.P2P_BYTES]=this._reportData[o.ReportField.P2P_DOWNLOAD_USEFUL_BYTES];var e=this._reportData;return this._initReporterData(),e}}]);e=u;function u(e,t,r,i){if(!(this instanceof u))throw new TypeError("Cannot call a class as a function");this.TAG="SliceManager";t=(this.tp2p=t).config;this.patchMgr=e.patchMgr,this.storage=e.storage,this.storageMgr=e.storageMgr,this.stateMgr=e.stateMgr,this.p2pMonitor=i,this.dataChannelManager=r,this._chunkSize=t.DCChunkSize,this._disableTrigger=t.disableTrigger,this._init(),this._initReporterData(),this.tp2p.registerReportCallback(this.TAG,this.reportCallback.bind(this))}r.default=e},{108:108,119:119,132:132,71:71}],127:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var e=e(15),a=(e=e)&&e.__esModule?e:{default:e};function o(e){if(!(this instanceof o))throw new TypeError("Cannot call a class as a function");this.config=e,this._succSet=new Set,this._failSet=new Set}i(o,[{key:"reset",value:function(){this._succSet.clear(),this._failSet.clear()}},{key:"record",value:function(e,t,r){r=r?this._succSet:this._failSet,e=[e,t];this.config.enableSort&&e.sort(),r.add((0,a.default)(e.join("")).toString())}},{key:"output",value:function(){var e;return this._failSet.size||this._failSet.size?(e={succ:Array.from(this._succSet),fail:Array.from(this._failSet)},this.reset(),e):null}}]),r.default=o},{15:15}],128:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function n(){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function");this.map=new Map}Object.defineProperty(r,"__esModule",{value:!0}),function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)}(n,[{key:"destroy",value:function(){this.map=null}},{key:"has",value:function(e){return this.map.has(e)}},{key:"get",value:function(e){return this.map.get(e)}},{key:"set",value:function(e,t){this.map.set(e,t)}},{key:"delete",value:function(e){if(1<arguments.length&&void 0!==arguments[1]&&arguments[1]){var t=this.map.keys(),r=!0,i=!1,n=void 0;try{for(var a,o=t[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){var s=a.value;s<=e&&this.map.delete(s)}}catch(e){i=!0,n=e}finally{try{!r&&o.return&&o.return()}finally{if(i)throw n}}}else this.map.delete(e)}},{key:"clear",value:function(){this.map.clear()}},{key:"size",get:function(){return this.map.size}}]),r.default=n},{}],129:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(a,[{key:"push",value:function(e){this.array.push(e),this.length+=e.length}},{key:"shift",value:function(e){if(this.array.length<1)return new Uint8Array(0);var t;if(this.offset+e===this.array[0].length)return t=this.array[0].slice(this.offset,this.offset+e),this.offset=0,this.array.shift(),this.length-=e,t;if(this.offset+e<this.array[0].length)return t=this.array[0].slice(this.offset,this.offset+e),this.offset+=e,this.length-=e,t;for(var r=new Uint8Array(e),i=0;0<this.array.length&&0<e;){if(this.offset+e<this.array[0].length){var n=this.array[0].slice(this.offset,this.offset+e);r.set(n,i),this.offset+=e,this.length-=e,e=0;break}n=this.array[0].length-this.offset;r.set(this.array[0].slice(this.offset,this.array[0].length),i),this.array.shift(),this.offset=0,i+=n,this.length-=n,e-=n}return r}},{key:"clear",value:function(){this.array=[],this.length=0,this.offset=0}},{key:"shiftBuffer",value:function(){0<this.array.length&&(this.length-=this.array[0].length,this.array.shift(),this.offset=0)}},{key:"toInt",value:function(e,t){for(var r=0,i=this.offset+e;i<this.offset+t+e;)i<this.array[0].length?r=256*r+this.array[0][i]:this.array[1]&&(r=256*r+this.array[1][i-this.array[0].length]),i+=1;return r}}]);var n=a;function a(e){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function");this.length=e||0,this.array=[],this.offset=0}r.default=n},{}],130:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(a,[{key:"destroy",value:function(){this.tp2p=null,this.stateMgr=null,this.storage=null}},{key:"cleanStorage",value:function(){var e=this.tp2p.config.uselessSliceCount,e=this.stateMgr.nextWriteId-1-e;0<e&&(this.storage.delete(e,!0),this._minId=1+e)}},{key:"missingSliceNum",value:function(e,t){for(var r=0,i=e;i<t;i++)this.storage.has(i)||(r+=1);return r}},{key:"minSliceId",get:function(){for(var e=this.stateMgr.nextWriteId;this._minId<e;){if(this.storage.has(this._minId))return this._minId;this._minId+=1}return this._minId}},{key:"maxContinuousId",get:function(){for(var e=this.stateMgr.nextWriteId-1,t=e;this.storage.has(t);)t+=1;return e===t?e:t-1}}]);var n=a;function a(e,t){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function");this.tp2p=t,this.storage=e.storage,this.stateMgr=e.stateMgr,this._minId=0}r.default=n},{}],131:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),e};function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=p(e(71)),o=p(e(70)),s=e(60),c=p(e(141)),l=p(e(140)),u=e(132);function p(e){return e&&e.__esModule?e:{default:e}}e=function(e){var t=i;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);function i(e,t){var r;if(this instanceof i)return(r=function(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(i.__proto__||Object.getPrototypeOf(i)).call(this,t,a.default.FLV_HEAD_LOADED,a.default.WRITE_SKIP,a.default.FOUND_SLICE,a.default.STOP_WRITE_SKIP))).TAG="PCDNWriter",r.tp2p=t,r._writerHeader=!1,r._nextWriteId=0,r.storage=e.storage,r.stateMgr=e.stateMgr,r.frameRater=new c.default,r.flvParser=new l.default,r.sampleFrameRate=!0,r._skipRetryTimes=0,r._confirmLossSkipTs=0,r._confirmLossCheckDelay=0,r._confirmLossSkipId=-1,r._beginId=0,r._lastWriteId=0,r._writeHandler=r.writeSlice.bind(r),r._initReporterData(),r.tp2p.registerReportCallback(r.TAG,r.reportCallback.bind(r)),r;throw new TypeError("Cannot call a class as a function")}return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e),n(i,[{key:"destroy",value:function(){this.onStopping(),this.storage=null}},{key:"clearTimer",value:function(){this._writerTimer&&(clearInterval(this._writerTimer),this._writerTimer=null),this._sampleTimer&&(clearInterval(this._sampleTimer),this._sampleTimer=null),this._checkSkipTimer&&(clearTimeout(this._checkSkipTimer),this._checkSkipTimer=null)}},{key:"startTimer",value:function(){var e=this;this._writerTimer||(this._writerTimer=setInterval(this._writeHandler,200)),this._sampleTimer||(this._sampleTimer=setTimeout(function(){e.sampleFrameRate=!0},100))}},{key:"onStarting",value:function(){}},{key:"onStopping",value:function(){this.clearTimer()}},{key:"onFlvHeadLoaded",value:function(e){var t=e.startSliceId,e=e.payload;this._writerHeader||(this.writeToPlayer(e.buffer),this._writerHeader=!0,this.startTimer(),this._beginId=t),this.nextWriteId<t&&(this.nextWriteId=t),this.stateMgr.baseTime=Date.now(),this.stateMgr.baseId=this.nextWriteId,this.writeSlice()}},{key:"onFoundSlice",value:function(e){e=e.sliceId;this.stateMgr.loadingMain&&e&&this.nextWriteId<e&&(this.nextWriteId=e),this.writeSlice()}},{key:"writeSlice",value:function(){this._writeControl()&&this._writeSlice()}},{key:"_writeControl",value:function(){return this._startControl()&&this._stuckControl()}},{key:"_startControl",value:function(){if(this.stateMgr.loadingMain){var e=this.tp2p.config.startPlayWaitSliceLen;if(0<e){var t=this.stateMgr,r=t.baseId,i=t.maxSliceId;if(this.nextWriteId>=r+e)return!0;if(e<=i-r+1)for(var n=0,a=r;a<=i;a++)if(this.storage.has(a)&&e<=(n+=1))return!0;return!1}}return!0}},{key:"_stuckControl",value:function(){var e;return!this.tp2p.config.enableStuckWaitBuffer||(e=!this.stateMgr.stuck||this._delayCheck()&&this._bufferCheck(),this.stateMgr.stuck&&e&&(this._reportData[u.ReportField.RESUME_PLAY_SUCC]+=1),e)}},{key:"_delayCheck",value:function(){return this.stateMgr.realDelay>=this.tp2p.config.resumePlayDelay}},{key:"_bufferCheck",value:function(){var e=this.nextWriteId;if(!e)return!0;var t=this.storage.get(e);if(t)for(var r=t.tagTime,i=e,n=this.tp2p.config.resumePlayBuffer;this.storage.has(i);){if(this.storage.get(i).tagTime-r>=1e3*n||i-e>=n*this.stateMgr.frameRate)return!0;i+=1}return!1}},{key:"_writeSlice",value:function(){var e=this;if(this._writerHeader)for(var t=this.storage,r=void 0;r=t.get((i=void 0,e.stateMgr.loadingMain||!1!==(i=e._isConfirmLoss(e.nextWriteId))&&(e.nextWriteId=i),e.nextWriteId));){this.nextWriteId===this._confirmLossSkipId&&(this._reportData[u.ReportField.CL_SKIP_SUC]+=1,this._confirmLossSkipId=-1),this._lastWriteId&&1<this.nextWriteId-this._lastWriteId&&(this._reportData[u.ReportField.LOSS_COUNT]+=this.nextWriteId-this._lastWriteId),this._lastWriteId&&this._lastWriteId>=this.nextWriteId&&(this._reportData[u.ReportField.REWRITE_COUNT]+=1),this._lastWriteId=this.nextWriteId;var i=r.payload.buffer.slice(0);this.writeToPlayer(i),this.sampleFrameRate&&10<(i=this.frameRater.sample(r.tagTime))&&i<70&&(this.stateMgr.frameRate=i,this.sampleFrameRate=!1),this.nextWriteId+=1}}},{key:"writeToPlayer",value:function(e){this.tp2p.trigger(a.default.STATE_CHANGE,{code:s.ComCode.RECEIVE_BUFFER,payload:e})}},{key:"onWriteSkip",value:function(e){var t,e=e.retry;this.stateMgr.playStart&&(t=this.nextWriteId,!1!==(t=this._findKeyFrame(t,t+30*this.stateMgr.frameRate))?(e&&(this.stateMgr.playSkipSucc=!0,this._skipRetryTimes=0),this.nextWriteId=t):e&&this._retrySkip())}},{key:"_findKeyFrame",value:function(e,t){for(var r=this.storage,i=this.flvParser;e<=t;){if(r.has(e)){var n=r.get(e);if(i.containKeyFrame(n.payload.buffer))return e}e+=1}return!1}},{key:"_retrySkip",value:function(){var e=this;this._checkSkipTimer||this._skipRetryTimes!==this.tp2p.config.maxSkipRetryTimes&&(this._checkSkipTimer=setTimeout(function(){e._checkSkipTimer=null,e._skipRetryTimes+=1,e._reportData[u.ReportField.SKIP_RETRY_TIMES]+=1,e.onWriteSkip({retry:!0})},2e3))}},{key:"onStopWriteSkip",value:function(){this._checkSkipTimer&&(clearTimeout(this._checkSkipTimer),this._checkSkipTimer=null)}},{key:"_isConfirmLoss",value:function(e){var t,r=this.stateMgr,i=this.tp2p.config.confirmLossEnable,n=!1;return!r.loadingMain&&i&&r.confirmLoss.has(e)&&(i=e,(t=Date.now())-this._confirmLossSkipTs>=this._confirmLossCheckDelay&&!1!==(n=this._findKeyFrame(e,Math.min(e+30*r.frameRate,r.maxSliceId)))&&(r.confirmLossSkipSliceId=e,this._confirmLossSkipId=n,this._confirmLossSkipTs=t,this._confirmLossCheckDelay=(n-e)*r.frameRate*1e3,i="("+i+", "+n+")",this._reportData[u.ReportField.CL_SKIP_COUNT]+=1,this._reportData[u.ReportField.CL_SKIP_BUFFERS]=this.tp2p.bufferLength),~this._reportData[u.ReportField.CL_SLICE_IDS].indexOf(i)||this._reportData[u.ReportField.CL_SLICE_IDS].push(i)),n}},{key:"reportCallback",value:function(){for(var e=this._reportData,t=this.stateMgr.maxSliceId,r=(e[u.ReportField.DIFF_MAX_WRITE]=t-this.nextWriteId,this.stateMgr.baseId+Math.floor((Date.now()-this.stateMgr.baseTime)/1e3)*this.stateMgr.frameRate),i=(e[u.ReportField.DIFF_MAX_BASE]=t-r,[]),n=[],a=0;a<this.stateMgr.baseSub;a++){var o=t-this.stateMgr.getMaxSubStreamId(a),o=(i.push(o),e[u.ReportField.DIFF_MAX_SUB]["s"+a]=o,this.stateMgr.getMaxSubStreamId(a)-this.stateMgr.getLastSubStreamId(a));n.push(o),e[u.ReportField.DIFF_SUB_CON]["s"+a]=o}e[u.ReportField.DIFF_MAX_SUB_AVG]=i.reduce(function(e,t){return e+t})/this.stateMgr.baseSub,e[u.ReportField.DIFF_SUB_CON_AVG]=n.reduce(function(e,t){return e+t})/this.stateMgr.baseSub;r=e[u.ReportField.CL_SLICE_IDS];return delete e[u.ReportField.CL_SLICE_IDS],r.length&&(e[u.ReportField.CL_SLICE_IDS]=r.join("-")),e[u.ReportField.WRITE_COUNT]=this.nextWriteId-this._beginId,e[u.ReportField.RESUME_PLAY_SUCC]||delete e[u.ReportField.RESUME_PLAY_SUCC],e[u.ReportField.REWRITE_COUNT]||delete e[u.ReportField.REWRITE_COUNT],this._initReporterData(),e}},{key:"_initReporterData",value:function(){this._beginId=this.nextWriteId,this._reportData={};var e=this._reportData;e[u.ReportField.SKIP_RETRY_TIMES]=0,e[u.ReportField.DIFF_MAX_SUB]={},e[u.ReportField.DIFF_SUB_CON]={},e[u.ReportField.DIFF_MAX_SUB_AVG]=0,e[u.ReportField.DIFF_SUB_CON_AVG]=0,e[u.ReportField.CL_SLICE_IDS]=[],e[u.ReportField.CL_SKIP_COUNT]=0,e[u.ReportField.CL_SKIP_SUC]=0,e[u.ReportField.CL_SKIP_BUFFERS]=0,e[u.ReportField.WRITE_COUNT]=0,e[u.ReportField.LOSS_COUNT]=0,e[u.ReportField.RESUME_PLAY_SUCC]=0,e[u.ReportField.REWRITE_COUNT]=0}},{key:"nextWriteId",set:function(e){1<e-this._nextWriteId&&this.stateMgr.resetSubMaxCon(),this._nextWriteId=e,this.stateMgr.nextWriteId=this._nextWriteId},get:function(){return this._nextWriteId}}]),i}(o.default);r.default=e},{132:132,140:140,141:141,60:60,70:70,71:71}],132:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}r.ReportField={STREAM_ID:"stream_id",STR_APPID:"str_appid",REQUEST:"request",STUCK_COUNT:"stuck_count",CDN_BYTES:"cdn_bytes",P2P_BYTES:"p2p_bytes",STR_USER_ID:"str_user_id",STR_PLAY_ID:"str_play_id",ROOMID:"roomid",USER_AGENT:"user_agent",REFERER:"referer",LOG_VERSION:"log_version",CONF_VERSION:"conf_version",SERVICE:"service",APPID:"appid",BIZID:"bizid",MODULE_ID:"module_id",COMMAND:"command",DATA_TIME:"data_time",DATA_TYPE:"data_type",PLATFORM:"platform",VERSION:"version",VIDEO_TYPE:"video_type",CHANNEL:"channel",PARTNER:"partner",PAGE_VISIBILITY:"page_visibility",ARCH:"arch",LOAD_VISIBLE:"page_visible",PLAY_VISIBLE:"play_visible",CURRENT_TIME:"cur_time",TOTAL_PLAY_TIME:"totalplay_t",PLAY_TIME:"play_time",PLAY_STARTED:"play_started",SESSION_TIME:"session_time",CURRENT_PLAYED_DURATION:"cur_play_duration",UUID:"uuid",CODE:"code",EVENT_CODE:"event_code",SRC_TYPE:"src_type",LIFE:"life",HOST:"host",STR_VIDEO_TYPE:"str_video_type",URL:"url",PLAY_START:"play_start",SDK_PLAY_TIMES:"sdk_play_times",CONF_OK_TIME:"confok_t",CONF_DURATION:"confduration",CONF_ERROR:"conf_error",CONF_TIMEOUT:"conf_timeout",CONF_FAIL:"conf_fail",LOAD_OK_TIME:"loadok_t",METADATA_TIME:"metadata_t",LOADEDDATA_TIME:"loadeddata_t",FIRST_DOWNLOAD_TIME:"first_dw_t",BACKGROUND_TIME:"background_t",RES_OK:"res_ok_t",PLAY_STUCK_SHORT:"play_stuck_short",PLAY_STUCK_HIGH_BUFFER:"play_stuck_high_buffer",PLAY_STUCK_TIRED:"play_stuck_tired",PLAY_STUCK_INIT:"play_stuck_init",INIT_STUCK_TIME:"init_stuck_time",PLAY_STUCK_MAIN:"play_stuck_main",MAIN_STUCK_TIME:"main_stuck_time",IGNORE_STUCK:"ignore_stuck",FAKE_STUCK:"fake_stuck",STUCK_SEEK:"stuck_seek",STUCK_RECOVER_BY_SEEK:"stuck_seek_recover",BUFFERED_NUM:"buffered_num",BUFFERED_RANGE:"buffered_range",VIDEO_BUFFERED:"video_buffered",PLAY_SKIP:"play_skip",PLAY_DELAY:"play_delay",STUCK_ID:"stuck_pieceid",WAIT_PLAY:"wait_play",WAIT_BUFFER_LEN:"wait_buffer_len",CDN_ERROR_ID:"cdn_errorid",IS_FIRST_PIECE_DOWNLOAD:"has_first_piece",STUCK_DURATION:"stuck_duration",STUCK_DURAS:"stuck_duras",FASTFORWARD_COUNT:"fastforward_count",SLOWDOWN_COUNT:"slowdown_count",REAL_FASTFORWARD:"real_fastforward",REAL_SLOWDOWN:"real_slowdown",CLEAR_FASTFORWARD:"clear_fastforward",CDN_DOWNLOAD_BYTES:"cdn_download_bytes",CDN_DOWNLOAD_USEFUL_BYTES:"cdn_download_useful_bytes",P2P_DOWNLOAD_BYTES:"p2p_download_bytes",P2P_DOWNLOAD_USEFUL_BYTES:"p2p_download_useful_bytes",P2P_FROM_SHARE_BYTES:"p2p_from_share_bytes",P2P_SEND_BYTES:"p2p_send_bytes",DATA_DELAY:"data_delay",BUFFER_LENGTH:"buffer_length",SYNC:"sync",SYNC_BACK:"sync_back",CDN_PIECE_ID:"cdn_piece_id",PLAY_ID:"play_id",LATEST_PIECE_ID:"latest_piece_id",APPEND_PIECE_ID:"append_id",CUR_CDN_ID:"cur_cdn_id",USE_WORKER:"use_worker",WORKER_EXCEPT:"worker_except",WORKER_ONERR:"worker_onerr",CDN_REQUEST:"cdn_request",CDN_SUCCESS:"cdn_success",CDN_RETRY:"cdn_retry",CDN_FAIL:"cdn_fail",CDN_404:"cdn_404",CDN_TIMEOUT:"cdn_timeout",CDN_DURATION:"cdn_duration",CDN_BACKUP_REQUEST:"cdn_backup_request",CDN_BACKUP_REQUEST_SUCC:"cdn_backup_request_succ",REFUSE_XPIECE:"refuse_xpiece",REFUSE_BACKUP:"refuse_backup",CDN_ABORT_MAIN:"cdn_abort_main",CDN_ABORT_BAK:"cdn_abort_bak",SUB_TRY:"sub_try",SUB_TRY_PLR:"sub_try_plr",SUB_SUCC:"sub_succ",SUB_TREE_LEVEL:"sub_tree_level",UNSUB:"unsub",PEER_REFUSE:"peer_refuse",PEER_KICK_WORST:"peer_kick_worst",PEER_KICK_SUB_FAIL:"peer_kick_sub_fail",WEBRTC_ICE_CONNECT_STATE:"webrtc_ice_connect_state",LACK_OF_PEER:"lack_of_peer",USEFUL_STREAMS:"useful_streams",AVAILABLE_STREAMS:"available_streams",P2P_OUT_STREAMS:"p2p_out_streams",PEER_NUM:"peer_num",CONNECTED:"connected",DC_SEND_DURATION:"dc_send_duration",MY_STREAM_ID:"stream_id",INIT_PC_TIMEOUT:"init_pc_timeout",KEEP_PC_TIMEOUT:"keep_pc_timeout",DC_ERR:"dc_err",DC_CLOSE:"dc_close",TYPE_ERROR_BLOB:"type_error_blob",CONN_TRY:"conn_try",CONN_TRY_TOTAL:"conn_try_total",CONN_SUCC:"conn_succ",CONN_SUCC_TOTAL:"conn_succ_total",T_PEER:"t_peers",CREATE_PC_ERROR:"create_pc_error",REFUSE_OFFER:"refuse_offer",STOP_OFFER:"stop_offer",LOCAL_STREAM_SHARE:"local_stream_share",STUN_IP:"stun",CHANGE_SUB:"change_sub",STOPPING_P2P:"stop_p2p",START_P2P:"start_p2p",PEER_CONN:"peer_conn",PLAY_ORIGIN:"play_origin",ABNORMAL_REQUEST:"abnormal_request",EXIT_REASON:"exit_reason",EXIT_DESTROY:"destroy",EXIT_CLOSE_TAB:"close",EXIT_ROLLBACK:"rollback",ROLLBACK_REASON:"rollback_reason",ROLLBACK_STUCK:"rb_stuck",ROLLBACK_CDN_ERR:"rb_cdn_err",ROLLBACK_SPLIT_STARTING:"rb_conf_ret8",ROLLBACK_NONEXISTENT:"rb_conf_403",ROLLBACK_CONF_ERROR:"rb_conf_error",ROLLBACK_CONF_TIMEOUT:"rb_conf_timeout",ROLLBACK_CONF_CONFIG:"rb_conf_config",ROLLBACK_EMPTY_BUFFER:"rb_empty_buf",ROLLBACK_NOT_ENOUGH_SPLIT:"rb_lack_m4s",ROLLBACK_APPEND_EXCEPTION:"rb_append_except",ROLLBACK_STUCK_CODE:"rb_stuck_code",BROWSER_NAME:"browser_name",BROWSER_MAJOR:"browser_major",BROWSER_VERSION:"browser_version",MEMORY_USAGE:"memory_usage",FULL_SCREEN:"full_screen",OPEN_VISIBLE:"open_visible",METADATA:"metadata",VIDEO_DATA_RATE:"videodatarate",VIDEO_CURRENT_TIME:"v_current_time",ROLLBACK_STREAM_END:"rb_stream_end",ROLLBACK_STREAM_404:"rb_stream_404",ROLLBACK_STREAM_403:"rb_stream_403",ROLLBACK_NETWORK_ERR:"rb_net_err",ROLLBACK_STREAM_UNKNOWN_ERR:"rb_stream_un_err",ROLLBACK_STREAM_TIMEOUT:"rb_stream_timeout",ROLLBACK_SLICE_NOT_FOUND:"rb_no_slice",ROLLBACK_READ_ERR:"rb_read_err",ROLLBACK_SAFARI_AAC:"rb_safari_aac",ROLLBACK_SAFARI_AVC:"rb_safari_avc",FILL_REQ:"fill_req",FILL_SLICE:"fill_slice",FILL_FULL_QUE:"fill_full_que",FILL_REQ_404:"fill_req_404",FILL_REQ_TIMEOUT:"fill_req_timeout",FILL_TIMEOUT_OK:"fill_timeout_ok",FILL_REQ_OK:"fill_req_ok",FILL_SLICE_OK:"fill_slice_ok",FILL_404_ID:"fill_404_id",FILL_TIMEOUT_ID:"fill_timeout_id",FILL_GT_SUB:"fill_gt_sub",FILL_DURATION:"fill_dura",FILL_BYTES:"fill_bytes",FILL_BAK_OK:"fill_bak_ok",FILL_BAK_404:"fill_bak_404",FILL_BAK_TIMEOUT:"fill_bak_timeout",EARLY_COUNT:"early_cnt",MISS_COUNT:"miss_cnt",EXPIRE_COUNT:"expire_cnt",FILL_TRY_P:"fill_try_p",PATCH_CDN_FILL_REQ:"patch_cdn_fill_req",PATCH_CDN_FILL_SLICE:"patch_cdn_fill_slice",MISS_OVER_5S:"miss_over_5s",MISS_OVER_10S:"miss_over_10s",MISS_OVER_20S:"miss_over_20s",MISS_OVER_30S:"miss_over_30s",FILL_REQ_P:"fill_req_p",ON_FILL_REQ_P:"on_fill_req_p",HAS_FILL_REQ_P:"has_fill_req_p",FILL_OK_P:"fill_ok_p",FILL_OK_P_USELESS:"fill_ok_p_useless",FILL_NO_PEER_MISS:"fill_no_peer_miss",FILL_MISS_P:"fill_miss_p",FILL_EMPTY_P:"fill_empty_p",FILL_TIMEOUT_P:"fill_timeout_p",FILL_P_S_BYTES:"fill_p_s_bytes",FILL_P_R_BYTES:"fill_p_r_bytes",FILL_P_U_BYTES:"fill_p_u_bytes",PUSH_REQ:"push_req",PUSH_OK:"push_ok",PUSH_MISS:"push_miss",PUSH_S_BYTES:"push_s_bytes",PUSH_U_BYTES:"push_u_bytes",PLAY_SKIP_SUCC:"play_skip_succ",P2P_EN_SUB:"p2p_en_sub",P2P_DIS_SUB:"p2p_dis_sub",SKIP_RETRY_TIMES:"skip_retry_times",FRAME_RATE:"frame_rate",FULL_404:"full_404",FULL_403:"full_403",FULL_NET_ERR:"full_net_err",FULL_READ_ERR:"full_read_err",FULL_UN_ERR:"full_un_err",FULL_TIMEOUT:"full_timeout",NOT_FOUND_COUNT:"cnt_404",FORBIDDEN_COUNT:"cnt_403",NETWORK_ERROR_COUNT:"cnt_net_err",FINISHED_COUNT:"cnt_done",UNKNOWN_ERROR_COUNT:"cnt_unknown",TIMEOUT_COUNT:"cnt_timeout",READ_ERROR_COUNT:"cnt_read_err",STREAM_STATE:"stream_state",LOAD_SUB_COUNT:"load_sub_cnt",ERR_SLICE_FRAME_COUNT:"err_slice_cnt",ERR_MAIN_NOT_CON:"err_main_not_con",SWITCH_MOMENT:"switch_moment",BIG_HOLE:"big_hole",HOLE_SIZE_1:"hole_size_1",HOLE_SIZE_2:"hole_size_2",HOLE_SIZE_3:"hole_size_3",FULL_TLS13:"full_tls13",SUB_TLS13:"sub_tls13",FULL_QUIC:"full_quic",SUB_QUIC:"sub_quic",FULL_QUIC_USER:"full_quic_user",SUB_QUIC_USER:"sub_quic_user",REAL_LOAD_DURATION:"real_load_dura",ENABLE_302:"en_302",APP_NAME:"app_name",EN_APP_NAME:"en_app_name",REDIRECT_OC:"redirect_oc",STUCK_ID_PCDN:"stuck_id_pcdn",SUB_MAX_ID:"sub_max_id",DIFF_MAX_BASE:"diff_max_base",DIFF_MAX_WRITE:"diff_max_write",DIFF_MAX_SUB:"diff_max_sub",DIFF_SUB_CON:"diff_sub_con",DIFF_SUB_CON_AVG:"diff_sub_con_avg",DIFF_MAX_SUB_AVG:"diff_max_sub_avg",SUB_CONTINUOUS_ID:"sub_continuous_id",STUCK_TRIGGER_RELOAD_COUNT:"trigger_reload_cnt",STUCK_RELOAD_COUNT:"reload_cnt",STUCK_RELOAD_RECOVER:"stuck_reload_recover",CUR_WRITE_ID:"cur_write_id",WRITE_COUNT:"write_cnt",LOSS_COUNT:"loss_cnt",REWRITE_COUNT:"rewrite_cnt",RESUME_PLAY_SUCC:"resume_play_succ",LOADING_PAGE_VISIBILITY:"page_visible_load",SSM_PULL_STREAM:"ssm_pull_stream",STUCK_TOTAL:"stuck_total",STUCK_FROM_MAIN:"stuck_from_main",STUCK_FROM_SUB:"stuck_from_sub",STUCK_FROM_CL:"stuck_from_cl",STUCK_FIRST_BUFFER_LEN:"stuck_first_buffer_len",STUCK_ALL_BUFFER_LEN:"stuck_all_buffer_len",HUNDRED_STUCK_CNT:"hundred_stuck_cnt",HUNDRED_STUCK_DUR:"hundred_stuck_dur",CL_SLICE_IDS:"cl_slice_ids",CL_SKIP_COUNT:"cl_skip_count",CL_SKIP_SUC:"cl_skip_suc",CL_SKIP_BUFFERS:"cl_skip_buffers",DIFF_SUB_MAX_TO_PLAY:"diff_sub_max_to_play",DIFF_SUB_CON_TO_PLAY:"diff_sub_con_to_play",DIFF_SUB_MAX_TO_IDLE:"diff_sub_max_to_idle",DIFF_MAX_TO_SUB_MAX:"diff_max_to_sub_max",DIFF_MAX_TO_PLAY:"diff_max_to_play",DIFF_MAX_TO_IDLE:"diff_max_to_idle",BEFORE_INIT_WORKER:"before_init_worker",INIT_TRANSMISSION:"init_transmission",TIMER_COST:"timer_cost",TIMEOUT_TRANSMISSION:"timeout_transmission",CLEAR_WORKER_COST:"clear_worker_cost",SHUT_DOWN_P2P:"shut_down_p2p",TURN_ON_P2P:"turn_on_p2p"},r.ReportCode={PHASE_DATA:"000",PLAY_START:"101",LOAD_OK:"102",ROLLBACK:"103",CONF_OK:"104",END_OF_STREAM:"105",STREAM_NOT_FOUND:"106"},r.Level=(i(a,null,[{key:"GenLevels",value:function(e){for(var t={},r=0;r<e;r++)t["level"+r]=0;return t["level-1"]=0,t}},{key:"DivideLevels",value:function(e,t,r){var i=t.length;if("number"!=typeof e)throw Error("level分级传入的数值需为 number 类型");if(t[i-1]!==1/0)throw Error("level分级传入的levelInterval最后一个值必须为Infinity!");for(var n=void 0,a=0;a<i-1;a++)if(e>=t[a]&&e<t[a+1]){n="level"+a;break}r[n=n||"level-1"]+=1}}]),a);function a(){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function")}},{}],133:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function n(){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function");this._cdnBytesTotal=0,this._cdnBytesUseful=0,this._p2pBytesTotal=0,this._p2pBytesUseful=0,this.reset()}Object.defineProperty(r,"__esModule",{value:!0}),function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)}(n,[{key:"reset",value:function(){this._cdnBytes=0,this._p2pReceiveBytes=0,this._p2pSendBytes=0}},{key:"CBS",get:function(){return Math.floor(this._cdnBytes)},set:function(e){this._cdnBytes=e}},{key:"CBT",get:function(){return Math.floor(this._cdnBytesTotal)},set:function(e){this._cdnBytesTotal=e}},{key:"CBU",get:function(){return Math.floor(this._cdnBytesUseful)},set:function(e){this._cdnBytesUseful=e}},{key:"PBT",get:function(){return Math.floor(this._p2pBytesTotal)},set:function(e){this._p2pBytesTotal=e}},{key:"PBU",get:function(){return Math.floor(this._p2pBytesUseful)},set:function(e){this._p2pBytesUseful=e}},{key:"PRB",get:function(){return Math.floor(this._p2pReceiveBytes)},set:function(e){this._p2pReceiveBytes=e}},{key:"PSB",get:function(){return this._p2pSendBytes},set:function(e){this._p2pSendBytes=e}}]),r.default=n},{}],134:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}e(43);var a=e(69),o=e(61),s=b(e(39)),c=b(e(71)),l=e(60),u=e(144),p=b(e(64)),d=b(e(62)),h=b(e(63)),f=b(e(96)),_=b(e(133)),y=b(e(137)),v=b(e(42)),m=b(e(139)),g=b(e(68)),S=e(67);function b(e){return e&&e.__esModule?e:{default:e}}i(R,null,[{key:"create",value:function(e){return new R({appId:e.tencentCloudAppId,bizId:e.bizId,partner:e.xp2pAppId,domain:e.xp2pPackage,pcdnMixed:e.xp2pPlayDomain,xp2pAppKey:e.xp2pAppKey,localSecKey:e.localSecKey,authMode:e.authMode,accessKey:e.accessKey,debug:e.debug})}},{key:"isSupported",value:function(){return m.default.supportMSEH264Playback()&&(m.default.supportFetch()&&m.default.supportReadableStream()||m.default.supportMozXhr())&&R.supportP2P()&&R.FilterBrowser()}},{key:"FilterBrowser",value:function(){try{var e=new v.default(window.navigator.userAgent).getBrowser(),t=parseInt(e.major,10);if("number"==typeof t&&0!==t){if("Chrome"===e.name)return 55<=t;if("Firefox"===e.name)return 65<=t;if("Safari"===e.name)return 11<=t;var r=/.+\sChrome\/([0-9]{2,3})/.exec(navigator.userAgent);if(r&&r[1]){var i=parseInt(r[1],10);if("number"==typeof i&&0!==i&&i<55)return!1}}return!0}catch(e){return!0}}},{key:"supportP2P",value:function(){return m.default.supportWebSocket()&&m.default.supportDataType()&&m.default.supportObjectURL()&&m.default.supportRTCPeerConnection()&&m.default.supportDataChannel()}},{key:"version",get:function(){return"1.8.29"}},{key:"Events",get:function(){return c.default}},{key:"ComEvents",get:function(){return l.ComEvent}},{key:"ComCodes",get:function(){return l.ComCode}},{key:"RollbackReason",get:function(){return l.RollbackReasons}},{key:"ErrorTypes",get:function(){return a.ErrorTypes}},{key:"ErrorDetails",get:function(){return a.ErrorDetails}},{key:"DefaultConfig",get:function(){return R.defaultConfig||o.tp2pDefaultConfig},set:function(e){R.defaultConfig=e}}]),i(R,[{key:"destroy",value:function(){u.logger.log("destroy"),this.coreComponents.forEach(function(e){e.destroy()}),this.initiator.destroy(),this.lazyStartComponents=null,this.observer.removeAllListeners(),this.media=null,this._player=null}},{key:"loadSource",value:function(e){this.config.loadTime=Date.now(),this._registerConfig(e),this._initPlay()}},{key:"shutDownP2P",value:function(){this.trigger(c.default.SHUT_DOWN_P2P)}},{key:"turnOnP2P",value:function(){this.trigger(c.default.TURN_ON_P2P)}},{key:"_registerConfig",value:function(e){var t=this.config,r=g.default.checkConfig(this,e);if(r.error)throw Error(r.error);r.warn&&u.logger.warn(r.warn),r.log&&u.logger.log(r.log);r=g.default.Url(e.src,t.domain);Object.assign(t,r),this._setMediaElementById(e.videoId)}},{key:"_initPlay",value:function(){this.initiator.load()}},{key:"_startLazyComponents",value:function(){this.lazyStartComponents.forEach(function(e){e.onStarting()})}},{key:"_stopLazyComponents",value:function(){this.lazyStartComponents.forEach(function(e){e.onStopping()})}},{key:"registerReportCallback",value:function(e,t){this.reporterController.registerModule(e,t)}},{key:"setMediaElement",value:function(e){e instanceof HTMLVideoElement?(u.logger.log("接口直接设置video element, id: "+e.id),this._registerMediaElement(e)):u.logger.error("参数需传入 HTMLVideoElement")}},{key:"_setMediaElementById",value:function(e){e&&(u.logger.log("传入videoId "+e),e=document.getElementById(e),this._registerMediaElement(e))}},{key:"_registerMediaElement",value:function(e){if(!e)throw new Error("Needs video element here");this.media?u.logger.warn("video element将会被重置. from id: "+this.media.id+" to id: "+e.id):u.logger.log("首次设置video element, id: "+e.id),this.media=e,this.initiator.setMediaElement(this.media)}},{key:"getStatistics",value:function(){var e={cdnBytes:this.flow.CBS,p2pReceiveBytes:this.flow.PRB,p2pSendBytes:this.flow.PSB};return this.flow.reset(),"h5.huya.com"!==this.config.domain&&delete e.p2pSendBytes,e}},{key:"huyaStatistics",value:function(){return{cdnBytesTotal:this.flow.CBT,cdnBytesUseful:this.flow.CBU,p2pBytesTotal:this.flow.PBT,p2pBytesUseful:this.flow.PBU}}},{key:"bufferLength",get:function(){var e,t=this.media;return t?(e=t.currentTime,y.default.bufferInfo(t,e,0).len):0}}]);e=R;function R(){var n=this,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=this,r=R;if(!(t instanceof r))throw new TypeError("Cannot call a class as a function");this.TAG="TP2P",this.config={};t=e,Object.assign(n.config,JSON.parse(JSON.stringify(t))),(r=e.accessKey)&&((r=(0,S.decodeAccessKey)(r))?Object.assign(n.config,r):window.error("[QVBP2P] accessKey解析失败")),i=R.DefaultConfig,Object.keys(i).forEach(function(e){e in n.config||(n.config[e]=i[e])});R.DefaultConfig.playTimes+=1,n.config.sdkInitTime=Date.now(),t=new v.default(window.navigator.userAgent),n.config.browser=t.getBrowser(),n.config.randomPlayId=Date.now()+"-"+Math.floor(Math.random()*(Math.floor(999999999)-Math.ceil(1)))+Math.ceil(1),n.config.loadTime=Date.now(),(0,u.enableLogs)(e.debug),u.logger.log("初始化p2p"),this.status={rollbackReason:"",openVisible:"unset",closeReason:"",isFirstPieceDownloaded:!1},this.observer=new s.default;var i,a=this.observer,r=(a.trigger=function(e){for(var t=arguments.length,r=Array(1<t?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];a.emit.apply(a,[e,e].concat(r))},a.off=function(e){for(var t=arguments.length,r=Array(1<t?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];a.removeListener.apply(a,[e].concat(r))},this.on=a.on.bind(a),this.off=a.off.bind(a),this.trigger=a.trigger.bind(a),this.listen=function(e,t){var r=Object.keys(l.ComEvent),i=[];r.forEach(function(e){i.push(l.ComEvent[e])}),-1<i.indexOf(e)&&n.on(e,t)},this.reporterLoader=new p.default(this),this.reporterController=new h.default(this),this.errorController=new d.default(this),this.lazyStartComponents=[this.reporterController],this.coreComponents=[this.reporterController,this.reporterLoader,this.errorController],this.config.arch="pcdn",f.default);this.initiator=new r(this,this._startLazyComponents.bind(this),this._stopLazyComponents.bind(this)),this.flow=new _.default}r.default=e},{133:133,137:137,139:139,144:144,39:39,42:42,43:43,60:60,61:61,62:62,63:63,64:64,67:67,68:68,69:69,71:71,96:96}],135:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var l=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e)){var r=t,i=[],n=!0,t=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(i.push(o.value),!r||i.length!==r);n=!0);}catch(e){t=!0,a=e}finally{try{!n&&s.return&&s.return()}finally{if(t)throw a}}return i}throw new TypeError("Invalid attempt to destructure non-iterable instance")};function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function n(e){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function");this._paramList=new Map,this._parse(e)}(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(n,[{key:"_parse",value:function(e){var r=this;e&&e.split("&").forEach(function(e){var e=e.split("="),e=l(e,2),t=e[0],e=e[1];r._paramList.set(t,e)})}},{key:"set",value:function(e,t){this._paramList.set(e,t)}},{key:"get",value:function(e){return this._paramList.get(e)}},{key:"delete",value:function(e){this._paramList.delete(e)}},{key:"size",value:function(){return this._paramList.size}},{key:"toString",value:function(){var e=[],t=!0,r=!1,i=void 0;try{for(var n,a=this._paramList[Symbol.iterator]();!(t=(n=a.next()).done);t=!0){var o=l(n.value,2),s=o[0],c=o[1];e.push(s+"="+c)}}catch(e){r=!0,i=e}finally{try{!t&&a.return&&a.return()}finally{if(r)throw i}}return e.join("&")}}]),r.default=n},{}],136:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.createAuthHeader=function(e,t,r,i,n,a){i=s(i),i=new URL(i),n=n||Date.now(),a=a||Math.floor(1e9*Math.random()),i="GET "+i.pathname+i.search+" HTTP/1.1\n"+n+"\n"+i.host+"\n"+r+"\n",i=(0,o.HmacSHA256)(i,t),t=o.enc.Base64.stringify(i);return{Authorization:"MAC kid="+e+" ts="+n+" seq-nr="+a+" mac="+t+" h=host:x-package","x-package":r}},r.getOrigionalUrl=s;var o=e(13);function s(e){var t=e,e=new URL(e);return t=e.host.includes("smtcdns.net")?e.protocol+"/"+e.pathname+e.search:t}},{13:13}],137:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default={isBuffered:function(e,t){try{if(e)for(var r=e.buffered,i=0;i<r.length;i++)if(t>=r.start(i)&&t<=r.end(i))return!0}catch(e){}return!1},bufferInfo:function(e,t,r){try{if(e){for(var i=e.buffered,n=[],a=void 0,a=0;a<i.length;a++)n.push({start:i.start(a),end:i.end(a)});return this.bufferedInfo(n,t,r)}}catch(e){}return{len:0,start:t,end:t,nextStart:void 0}},bufferedInfo:function(e,t,r){var i=[],n=void 0,a=void 0,o=void 0,s=void 0,c=void 0;for(e.sort(function(e,t){var r=e.start-t.start;return r||t.end-e.end}),c=0;c<e.length;c++){var l,u=i.length;u&&(l=i[u-1].end,e[c].start-l<r)?e[c].end>l&&(i[u-1].end=e[c].end):i.push(e[c])}for(n=c=0,o=a=t;c<i.length;c++){var p=i[c].start,d=i[c].end;if(p<=t+r&&t<d)a=p,n=(o=d)-t;else if(t+r<p){s=p;break}}return{len:n,start:a,end:o,nextStart:s}}}},{}],138:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(a,null,[{key:"ab2str",value:function(e){return String.fromCharCode.apply(null,new Uint8Array(e))}},{key:"str2ab",value:function(e){for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),i=0,n=e.length;i<n;i++)r[i]=e.charCodeAt(i);return t}}]);var n=a;function a(){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function")}r.default=n},{}],139:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(a,null,[{key:"supportMSEH264Playback",value:function(){return window.MediaSource&&window.MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"')}},{key:"supportDataType",value:function(){return"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array&&"undefined"!=typeof DataView}},{key:"supportWebSocket",value:function(){return"WebSocket"in window||"MozWebSocket"in window}},{key:"supportRTCPeerConnection",value:function(){return!!(window.RTCPeerConnection||window.webkitRTCPeerConnection||window.mozRTCPeerConnection||window.msRTCPeerConnection||window.oRTCPeerConnection)}},{key:"supportDataChannel",value:function(){var e=window.RTCPeerConnection||window.msRTCPeerConnection||window.mozRTCPeerConnection||window.webkitRTCPeerConnection;var t=!1;try{var r=new e(null),t="createDataChannel"in r;r.close(),r=null}catch(e){}return t}},{key:"supportObjectURL",value:function(){return"URL"in window&&"createObjectURL"in URL}},{key:"supportFetch",value:function(){return"fetch"in window}},{key:"supportReadableStream",value:function(){return"ReadableStream"in window}},{key:"supportMozXhr",value:function(){try{var e=new XMLHttpRequest;return e.open("GET","https://example.com",!0),(e.responseType="moz-chunked-arraybuffer")===e.responseType}catch(e){return!1}}},{key:"pageVisibility",value:function(){var e={hidden:"",visibilityChange:"",pageVisible:"",support:!0};void 0!==document.hidden?(e.hidden="hidden",e.visibilityChange="visibilitychange"):void 0!==document.msHidden?(e.hidden="msHidden",e.visibilityChange="msvisibilitychange"):void 0!==document.webkitHidden?(e.hidden="webkitHidden",e.visibilityChange="webkitvisibilitychange"):e.support=!1;return e.pageVisible=e.support?window.document[e.hidden]?"hidden":"visible":"notSupport",e}},{key:"memory",value:function(){var e,t="";return t=performance&&performance.memory?(e=performance.memory).jsHeapSizeLimit+" | "+e.totalJSHeapSize+" | "+e.usedJSHeapSize:t}},{key:"isFullScreen",value:function(){return!!(document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement)}}]);var n=a;function a(){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function")}r.default=n},{}],140:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=e(92);function o(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)}i(s,[{key:"_parseTag",value:function(e){for(var t=0,r=e.byteLength;t<r;){var i=new DataView(e,t);if(r<t+11+4)break;var n=i.getUint8(0),a=16777215&i.getUint32(0);if(t+11+a+4>e.byteLength)break;if(8!==n&&9!==n&&18!==n);else if(!this._containKeyFrame&&9===n&&(this._containKeyFrame=1==(i.getUint8(11)>>>4&15),this._containKeyFrame))return;t+=11+a+4}}},{key:"parseTag",value:function(e){for(var t=0,r=e.byteLength;t<r;){var i=new DataView(e,t);if(r<t+11+4)break;var n,a,o=i.getUint8(0),s=16777215&i.getUint32(0);if(t+11+s+4>e.byteLength)break;8!==o&&9!==o&&18!==o?t+=11+s+4:(n=i.getUint8(4),a=i.getUint8(5),i.getUint8(6),i.getUint8(7),t+=11+s+4)}}},{key:"containKeyFrame",value:function(e){this._containKeyFrame=!1,this._parseTag(e);e=this._containKeyFrame;return this._containKeyFrame=!1,e}}],[{key:"AACSequenceHeaderTag",value:function(e){var t=(240&e[11])>>4;if(t===a.SoundFormat.AAC&&e[12]===a.AACPacketType.AACSequenceHeader)return!0;return!1}},{key:"AVCSequenceHeaderTag",value:function(e){var t=15&e[11];if(t===a.CodecID.AVC&&e[12]===a.AVCPacketType.AVCSequenceHeader)return!0;return!1}},{key:"CompareTagData",value:function(e,t){return e.byteLength===t.byteLength&&(e=e.slice(11),t=t.slice(11),JSON.stringify([].concat(o(e)))===JSON.stringify([].concat(o(t))))}}]);e=s;function s(){if(!(this instanceof s))throw new TypeError("Cannot call a class as a function");this.TAG="FlvParse"}r.default=e},{92:92}],141:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function n(){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function");this.startTime=0,this.counter=0}Object.defineProperty(r,"__esModule",{value:!0}),function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)}(n,[{key:"sample",value:function(e){if(0===this.startTime)this.startTime=e;else{if(3e3<e-this.startTime)return parseInt(this.counter/3,10);this.counter+=1}return-1}}]),r.default=n},{}],142:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(a,null,[{key:"UUID",value:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}},{key:"myUUID",value:function(){var e,t,r;return window.localStorage?(e=void 0,t=parseInt(localStorage.getItem("loadTimes")||0,10),(r=localStorage.getItem("myUUID"))?e=r:(e=a.UUID(),localStorage.setItem("myUUID",e)),36!==e.length&&(e=a.UUID(),localStorage.setItem("myUUID",e)),localStorage.setItem("loadTimes",t+1),{UUID:e,loadTimes:t+1}):{UUID:a.UUID(),loadTimes:-1}}}]);var n=a;function a(){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function")}r.default=n},{}],143:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function n(){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(r,"__esModule",{value:!0}),function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)}(n,null,[{key:"StorageVar",value:function(e,t){n.Storage("TC_VAR",e,t)}},{key:"ReadVar",value:function(e){return n.Read("TC_VAR",e)}},{key:"Read",value:function(e,t){try{return JSON.parse(localStorage.getItem(e))[t]}catch(e){}}},{key:"Storage",value:function(e,t,r){try{var i=JSON.parse(localStorage.getItem(e))||{};i[t]=r,localStorage.setItem(e,JSON.stringify(i))}catch(e){}}}]),r.default=n},{}],144:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function n(){}var a={trace:n,debug:n,log:n,warn:n,info:n,error:n},o=a;function s(a){var o=self.console[a];return o?function(){for(var e,t,r=arguments.length,i=Array(r),n=0;n<r;n++)i[n]=arguments[n];i[0]&&(i[0]=(e=a,t=i[0],t="[ QVBP2P "+e+"] > "+t)),o.apply(self.console,i)}:n}r.enableLogs=function(e){if(!0===e||"object"===(void 0===e?"undefined":i(e))){!function(t){for(var e=arguments.length,r=Array(1<e?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];r.forEach(function(e){o[e]=t[e]?t[e].bind(t):s(e)})}(e,"debug","log","info","warn","error");try{o.log()}catch(e){o=a}}else o=a},r.logger=o},{}],145:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(a,[{key:"reset",value:function(){this._seqList=[],this._checkPoint=0,this._lastLossPoint=0,this._lossInterval=[]}},{key:"calculate",value:function(e){if(isNaN(e))return 0;for(var t,r=this._seqList,i=this._lossInterval,n=(r.length>=this._allowLength&&r.shift(),r.push(e),this._checkPoint),a=Math.max(0,e-this._delay);n<a;n++)~r.indexOf(n)||(t=n-this._lastLossPoint-1,this._lastLossPoint=n,0<t&&(9<=i.length&&i.shift(),i.push(t)));if(this._checkPoint=n,!i.length)return 0;for(var o=[1,1,1,1,.8,.6,.4,.2],s=0,c=0,l=0,n=0,a=i.length;n<a;n++)n<a-1&&(s+=i[n]*o[n],l+=o[n]),1<=n&&(c+=i[n]*o[n-1]);return l/Math.max(s,c)}}]);var n=a;function a(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=this,r=a;if(!(t instanceof r))throw new TypeError("Cannot call a class as a function");this._delay=e.checkDelay,this._allowLength=e.cacheLength,this.reset()}r.default=n},{}],146:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});r.genKey=function(e){return e.map(function(e){return String.fromCodePoint(parseInt(e,10))}).join("")}},{}],147:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function n(){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(r,"__esModule",{value:!0}),function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)}(n,null,[{key:"getLogTime",value:function(){var e=new Date;return e.getHours()+":"+e.getMinutes()+":"+e.getSeconds()+"."+e.getMilliseconds()}},{key:"timestamp",value:function(){return Date.now()}}]),r.default=n},{}],148:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e};function n(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var e=e(135),a=(e=e)&&e.__esModule?e:{default:e};i(o,null,[{key:"TransformProtocol",value:function(e){e=e.split("//");return document.location.protocol+"//"+e[1]}},{key:"GetRoomIdFromUrl",value:function(e){e=e.match(/\/(\d+)(?=r)/);return e?e[1]:""}},{key:"GetSourceUrl",value:function(e){return/rtmp:|http:|https:/.test(e)?e.split("?")[0]:null}},{key:"GetLiveInfoFromUrl",value:function(e){var t,r=o.GetSourceUrl(e),i=void 0,n=void 0,a=void 0;return r?(i=0<r.lastIndexOf(".flv")?r.substring(r.lastIndexOf("/")+1,r.lastIndexOf(".flv")):r.substring(r.lastIndexOf("/")+1,r.length),n=1<(t=r.split("/")).length?t[t.length-2]:"",a=/^(?:rtmp:\/\/|http:\/\/|https:\/\/)([^/]+)\//.exec(r)[1]):(a=n="",i=e),{channelId:i,appName:n,liveUrlHost:a}}},{key:"GetSearch",value:function(e){e=e.split("?");return 1<e.length?e[1]:""}},{key:"ParseUrlParams",value:function(e){e=o.GetSearch(e);return new a.default(e)}}]);e=o;function o(){if(!(this instanceof o))throw new TypeError("Cannot call a class as a function")}r.default=e},{135:135}],149:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function n(e,t){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function");this.alpha_=e,this.estimate_=t}Object.defineProperty(r,"__esModule",{value:!0}),function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)}(n,[{key:"sample",value:function(e){this.estimate_?this.estimate_=e*(1-this.alpha_)+this.alpha_*this.estimate_:this.estimate_=e}},{key:"getEstimate",value:function(){return this.estimate_}}]),r.default=n},{}],150:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(a,null,[{key:"encrypt",value:function(e){for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),i=[99,117,105],n=0;n<e.length;n++)r[n]=e.codePointAt(n)^i[n%i.length];return r}},{key:"decrypt",value:function(e){for(var t="",r=[99,117,105],i=0;i<e.length;i++){var n=e[i].codePointAt(0)^r[i%r.length];t+=String.fromCodePoint(n)}return JSON.parse(t)}}]);var n=a;function a(){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function")}r.default=n},{}],151:[function(e,t,r){"use strict";function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(r,"__esModule",{value:!0});(function(e,t,r){t&&i(e.prototype,t),r&&i(e,r)})(a,[{key:"destroy",value:function(){var e=!(0<arguments.length&&void 0!==arguments[0])||arguments[0];this._abort=!0,this._outerAbort=e,this.abort(),this.loader=null}},{key:"abort",value:function(){var e=this.loader;e&&e.abort(),window.clearTimeout(this.requestTimeout),this.requestTimeout=null,window.clearTimeout(this.retryTimeout),this.retryTimeout=null}},{key:"load",value:function(e,t,r){this.context=e,this.config=t,this.callbacks=r,this.stats={trequest:Date.now(),retry:0},this.retryDelay=t.retryDelay,this.loadInternal()}},{key:"loadInternal",value:function(){var t=this.context,r=this.config,i=(this.loader=new XMLHttpRequest,this.loader),n=this.stats,a=(n.tfirst=0,n.loaded=0,this.xhrSetup),o=(o=t.method)||"GET";try{if(a)try{a(i,t.url)}catch(e){i.open(o,t.url,!r.sync),a(i,t.url)}i.readyState||i.open(o,t.url,!r.sync)}catch(e){return void this.callbacks.onError({code:i.status,text:e.message},n,t,i)}t.rangeEnd&&i.setRequestHeader("Range","bytes="+t.rangeStart+"-"+(t.rangeEnd-1)),t.headers&&t.headers.forEach(function(e){i.setRequestHeader(e.header,e.value)}),i.onreadystatechange=this.readystatechange.bind(this),i.onprogress=this.loadprogress.bind(this),i.onerror=this.onerror.bind(this),t.responseType&&(i.responseType=t.responseType),this.requestTimeout=window.setTimeout(this.loadtimeout.bind(this),this.config.timeout,i),n.tsend=Date.now(),"POST"===o?i.send(t.data):i.send()}},{key:"readystatechange",value:function(e){var t,r,e=e.currentTarget,i=e.readyState,n=this.stats,a=this.context,o=this.config,s=e.status;this._outerAbort||2<=i&&(0===n.tfirst&&(n.tfirst=Math.max(Date.now(),n.trequest)),4===i?(this.requestTimeout&&(window.clearTimeout(this.requestTimeout),this.requestTimeout=null),200<=s&&s<300?(n.tload=Math.max(n.tfirst,Date.now()),t=r=void 0,t="arraybuffer"===a.responseType?(r=e.response).byteLength:(r=e.responseText).length,n.total=t,n.loaded=t,n.loadeds=parseInt(t*(this.context.bFix||1),10),t={url:e.responseURL,data:r},this.callbacks.onSuccess(t,n,a,e)):(this.callbacks.onLoadMonitor&&(r={code:s,retry:n.retry},this.callbacks.onLoadMonitor(r,a)),n.retry>=o.maxRetry?this.callbacks.onError({code:s,text:e.statusText},n,a,e):(this.destroy(!1),this.retryTimeout=window.setTimeout(this.loadInternal.bind(this),this.retryDelay),this.retryDelay=Math.min(2*this.retryDelay,o.maxRetryDelay),n.retry+=1))):this.config.stream&&(this.requestTimeout&&(window.clearTimeout(this.requestTimeout),this.requestTimeout=null),200===s)&&2===i&&this.callbacks.onHeadLoaded&&this.callbacks.onHeadLoaded())}},{key:"loadtimeout",value:function(e){var t=this.stats,r=this.context,i=this.config,i=t.retry>=i.maxRetry;t.reachMaxRetry=i,this.callbacks.onTimeout(t,r,{readyState:e.readyState})||i||(this.destroy(!1),this.loadInternal(),t.retry+=1)}},{key:"loadprogress",value:function(e){var t;!this._outerAbort&&(e=e.target.response,t=this.callbacks.onProgress)&&t(e)}},{key:"onerror",value:function(){this.callbacks.onLoadError&&this.callbacks.onLoadError()}}]);var n=a;function a(e){if(!(this instanceof a))throw new TypeError("Cannot call a class as a function");this.TAG="XhrLoader"+.5*Date.now(),e&&e.xhrSetup&&(this.xhrSetup=e.xhrSetup)}r.default=n},{}]},{},[74])(74)});
