<?php
/**
 * 添加测试课程数据
 * 用于修复课程显示问题
 */

require_once 'auth.php';

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

try {
    $auth = new AuthAPI();
    $conn = $auth->getConn();
    
    echo "<h2>添加测试课程数据</h2>";
    
    // 首先检查是否已有管理员
    $admin_check = $conn->query("SELECT id FROM admins LIMIT 1");
    $admin_id = 1;
    
    if (!$admin_check || $admin_check->num_rows == 0) {
        // 创建默认管理员
        $password_hash = password_hash('password', PASSWORD_DEFAULT);
        $admin_sql = "INSERT INTO admins (id, username, password, email, name, status) VALUES (1, 'admin', ?, '<EMAIL>', '系统管理员', 'active')";
        $admin_stmt = $conn->prepare($admin_sql);
        $admin_stmt->bind_param("s", $password_hash);
        if ($admin_stmt->execute()) {
            echo "✓ 创建默认管理员成功<br>";
        } else {
            echo "✗ 创建默认管理员失败: " . $conn->error . "<br>";
        }
    } else {
        $admin_row = $admin_check->fetch_assoc();
        $admin_id = $admin_row['id'];
        echo "✓ 使用现有管理员 ID: $admin_id<br>";
    }
    
    // 检查courses表是否存在必要字段
    $fields_to_add = [
        'cover_image' => "VARCHAR(500) DEFAULT NULL COMMENT '课程封面图片'",
        'is_recommended' => "TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否推荐：0否 1是'",
        'is_recommend' => "TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否推荐（兼容字段）：0否 1是'",
        'view_count' => "INT(11) NOT NULL DEFAULT 0 COMMENT '观看次数'",
        'price' => "DECIMAL(10,2) DEFAULT 0.00 COMMENT '课程价格'",
        'original_price' => "DECIMAL(10,2) DEFAULT NULL COMMENT '原价'",
        'is_free' => "TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否免费：0否 1是'",
        'teacher_name' => "VARCHAR(100) DEFAULT NULL COMMENT '讲师姓名'",
        'subtitle' => "VARCHAR(500) DEFAULT NULL COMMENT '课程副标题'",
        'rating' => "DECIMAL(3,2) DEFAULT 0.00 COMMENT '课程评分（0-5）'",
        'student_count' => "INT(11) NOT NULL DEFAULT 0 COMMENT '学生数量'"
    ];
    
    // 检查现有字段
    $existing_fields = [];
    $structure = $conn->query("DESCRIBE courses");
    if ($structure) {
        while ($field = $structure->fetch_assoc()) {
            $existing_fields[] = $field['Field'];
        }
    }
    
    // 添加缺失字段
    echo "<h3>字段检查与添加:</h3>";
    foreach ($fields_to_add as $field_name => $field_definition) {
        if (!in_array($field_name, $existing_fields)) {
            $sql = "ALTER TABLE courses ADD COLUMN $field_name $field_definition";
            if ($conn->query($sql)) {
                echo "✓ 添加字段 $field_name 成功<br>";
            } else {
                echo "✗ 添加字段 $field_name 失败: " . $conn->error . "<br>";
            }
        } else {
            echo "- 字段 $field_name 已存在<br>";
        }
    }
    
    // 清理现有测试数据
    $conn->query("DELETE FROM courses WHERE id BETWEEN 1 AND 20");
    echo "<br>✓ 清理现有测试数据<br>";
    
    // 添加测试课程数据
    echo "<h3>添加测试课程:</h3>";
    
    $test_courses = [
        [
            'id' => 1,
            'title' => 'Web前端开发基础',
            'description' => '从零开始学习HTML、CSS、JavaScript，掌握现代Web前端开发技术。适合初学者入门学习。',
            'video_url' => 'https://sample-videos.com/zip/10/mp4/480/mp4-sample-1.mp4',
            'thumbnail' => 'https://picsum.photos/400/300?random=1',
            'cover_image' => 'https://picsum.photos/800/450?random=1',
            'duration' => 3600,
            'is_recommended' => 1,
            'is_recommend' => 1,
            'view_count' => 1250,
            'price' => 199.00,
            'original_price' => 299.00,
            'is_free' => 0,
            'teacher_name' => '张老师',
            'subtitle' => '零基础入门前端开发',
            'rating' => 4.8,
            'student_count' => 320,
            'sort_order' => 1
        ],
        [
            'id' => 2,
            'title' => 'Python编程入门',
            'description' => 'Python语言基础语法、数据结构、面向对象编程等核心概念详解。',
            'video_url' => 'https://sample-videos.com/zip/10/mp4/480/mp4-sample-2.mp4',
            'thumbnail' => 'https://picsum.photos/400/300?random=2',
            'cover_image' => 'https://picsum.photos/800/450?random=2',
            'duration' => 4200,
            'is_recommended' => 1,
            'is_recommend' => 1,
            'view_count' => 980,
            'price' => 0.00,
            'original_price' => null,
            'is_free' => 1,
            'teacher_name' => '李老师',
            'subtitle' => '从入门到精通',
            'rating' => 4.6,
            'student_count' => 450,
            'sort_order' => 2
        ],
        [
            'id' => 3,
            'title' => 'React框架实战',
            'description' => '深入学习React框架，包括组件开发、状态管理、路由配置等高级特性。',
            'video_url' => 'https://sample-videos.com/zip/10/mp4/480/mp4-sample-3.mp4',
            'thumbnail' => 'https://picsum.photos/400/300?random=3',
            'cover_image' => 'https://picsum.photos/800/450?random=3',
            'duration' => 5400,
            'is_recommended' => 0,
            'is_recommend' => 0,
            'view_count' => 756,
            'price' => 299.00,
            'original_price' => 399.00,
            'is_free' => 0,
            'teacher_name' => '王老师',
            'subtitle' => '现代前端框架实战',
            'rating' => 4.9,
            'student_count' => 180,
            'sort_order' => 3
        ],
        [
            'id' => 4,
            'title' => 'Node.js后端开发',
            'description' => '使用Node.js构建高性能后端应用，包括Express框架、数据库操作、API设计等。',
            'video_url' => 'https://sample-videos.com/zip/10/mp4/480/mp4-sample-4.mp4',
            'thumbnail' => 'https://picsum.photos/400/300?random=4',
            'cover_image' => 'https://picsum.photos/800/450?random=4',
            'duration' => 4800,
            'is_recommended' => 1,
            'is_recommend' => 1,
            'view_count' => 623,
            'price' => 249.00,
            'original_price' => null,
            'is_free' => 0,
            'teacher_name' => '赵老师',
            'subtitle' => '全栈开发必备技能',
            'rating' => 4.7,
            'student_count' => 210,
            'sort_order' => 4
        ],
        [
            'id' => 5,
            'title' => 'MySQL数据库设计',
            'description' => '数据库设计原理、SQL语句优化、索引设计、事务处理等数据库核心技术。',
            'video_url' => 'https://sample-videos.com/zip/10/mp4/480/mp4-sample-5.mp4',
            'thumbnail' => 'https://picsum.photos/400/300?random=5',
            'cover_image' => 'https://picsum.photos/800/450?random=5',
            'duration' => 3900,
            'is_recommended' => 0,
            'is_recommend' => 0,
            'view_count' => 445,
            'price' => 0.00,
            'original_price' => null,
            'is_free' => 1,
            'teacher_name' => '刘老师',
            'subtitle' => '数据库设计与优化',
            'rating' => 4.5,
            'student_count' => 380,
            'sort_order' => 5
        ],
        [
            'id' => 6,
            'title' => 'Vue.js前端框架',
            'description' => 'Vue.js框架从基础到进阶，包括组件化开发、Vuex状态管理、Vue Router等。',
            'video_url' => 'https://sample-videos.com/zip/10/mp4/480/mp4-sample-6.mp4',
            'thumbnail' => 'https://picsum.photos/400/300?random=6',
            'cover_image' => 'https://picsum.photos/800/450?random=6',
            'duration' => 4500,
            'is_recommended' => 1,
            'is_recommend' => 1,
            'view_count' => 892,
            'price' => 199.00,
            'original_price' => 299.00,
            'is_free' => 0,
            'teacher_name' => '陈老师',
            'subtitle' => '渐进式前端框架',
            'rating' => 4.8,
            'student_count' => 290,
            'sort_order' => 6
        ]
    ];
    
    // 插入课程数据
    $insert_sql = "INSERT INTO courses (id, title, description, video_url, thumbnail, cover_image, duration,
                   is_recommended, is_recommend, view_count, price, original_price, is_free,
                   teacher_name, subtitle, rating, student_count, sort_order, status, created_by)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?)";

    $stmt = $conn->prepare($insert_sql);

    foreach ($test_courses as $course) {
        // 参数类型: i=integer, s=string, d=double
        $stmt->bind_param("isssssiiiddissdiiii",
            $course['id'],                  // i - integer
            $course['title'],               // s - string
            $course['description'],         // s - string
            $course['video_url'],           // s - string
            $course['thumbnail'],           // s - string
            $course['cover_image'],         // s - string
            $course['duration'],            // i - integer
            $course['is_recommended'],      // i - integer
            $course['is_recommend'],        // i - integer
            $course['view_count'],          // i - integer
            $course['price'],               // d - double
            $course['original_price'],      // d - double (可能为null)
            $course['is_free'],             // i - integer
            $course['teacher_name'],        // s - string
            $course['subtitle'],            // s - string
            $course['rating'],              // d - double
            $course['student_count'],       // i - integer
            $course['sort_order'],          // i - integer
            $admin_id                       // i - integer
        );
        
        if ($stmt->execute()) {
            echo "✓ 添加课程: {$course['title']}<br>";
        } else {
            echo "✗ 添加课程失败: {$course['title']} - " . $conn->error . "<br>";
        }
    }
    
    echo "<br><h3>完成!</h3>";
    echo "已成功添加 " . count($test_courses) . " 个测试课程。<br>";
    echo "<a href='../wx/pages/index/index.html' target='_blank'>测试微信小程序首页</a><br>";
    echo "<a href='courseList.php' target='_blank'>测试课程列表API</a><br>";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage();
}
?>
