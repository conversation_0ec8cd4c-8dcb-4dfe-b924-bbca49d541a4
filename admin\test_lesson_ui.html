<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课时管理UI测试</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8f9fa;
        }

        .lesson-container {
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }

        .lesson-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .lesson-title h2 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }

        .lesson-subtitle {
            color: #666;
            margin: 5px 0 0 0;
            font-size: 14px;
        }

        .lesson-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .lesson-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .stats-row {
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stats-left {
            display: flex;
            gap: 10px;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .btn-outline {
            background: white;
            border: 1px solid #007bff;
            color: #007bff;
        }

        .btn-outline:hover {
            background: #007bff;
            color: white;
        }

        .table-container {
            overflow-x: auto;
        }

        .lesson-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .lesson-table th,
        .lesson-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .lesson-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .lesson-table tbody tr:hover {
            background: #f8f9fa;
        }

        .lesson-title-cell strong {
            color: #333;
            font-size: 14px;
        }

        .lesson-description {
            color: #6c757d;
            font-size: 12px;
            margin-top: 4px;
        }

        .status-badge {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .free-badge {
            background: #d4edda;
            color: #155724;
        }

        .paid-badge {
            background: #fff3cd;
            color: #856404;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #138496;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .sort-handle {
            cursor: move;
            color: #6c757d;
        }

        .sort-order {
            font-weight: 500;
            color: #495057;
        }

        .test-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="lesson-container">
        <div class="test-info">
            <h4><i class="fas fa-info-circle"></i> 课时管理UI测试页面</h4>
            <p>这是课时管理页面的UI预览，展示了完整的界面设计和交互效果。</p>
        </div>

        <div class="lesson-header">
            <div class="lesson-title">
                <h2>课时管理</h2>
                <p class="lesson-subtitle">课程：Web前端开发基础</p>
            </div>
            <div class="lesson-actions">
                <a href="#" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回课程列表
                </a>
                <button type="button" class="btn btn-primary" onclick="alert('添加课时功能')">
                    <i class="fas fa-plus"></i> 添加课时
                </button>
            </div>
        </div>

        <div class="lesson-content">
            <div class="stats-row">
                <div class="stats-left">
                    <span class="badge badge-info">总课时: 5</span>
                    <span class="badge badge-success">总时长: 02:30:00</span>
                </div>
                <div class="stats-right">
                    <button type="button" class="btn btn-outline" onclick="alert('排序模式')">
                        <i class="fas fa-sort"></i> 排序模式
                    </button>
                </div>
            </div>

            <div class="table-container">
                <table class="lesson-table">
                    <thead>
                        <tr>
                            <th width="60">排序</th>
                            <th>课时标题</th>
                            <th width="100">时长</th>
                            <th width="80">状态</th>
                            <th width="80">免费</th>
                            <th width="120">创建时间</th>
                            <th width="150">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <span class="sort-handle" style="display: none;">
                                    <i class="fas fa-grip-vertical"></i>
                                </span>
                                <span class="sort-order">1</span>
                            </td>
                            <td>
                                <div class="lesson-title-cell">
                                    <strong>第1课：HTML基础入门</strong>
                                    <div class="lesson-description">学习HTML的基本语法和常用标签，为网页开发打下基础。</div>
                                </div>
                            </td>
                            <td>00:30:00</td>
                            <td>
                                <span class="status-badge status-active">启用</span>
                            </td>
                            <td>
                                <span class="status-badge free-badge">免费</span>
                            </td>
                            <td>07-16 14:30</td>
                            <td>
                                <button type="button" class="btn btn-info btn-sm" onclick="alert('编辑课时')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" onclick="alert('删除课时')">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span class="sort-handle" style="display: none;">
                                    <i class="fas fa-grip-vertical"></i>
                                </span>
                                <span class="sort-order">2</span>
                            </td>
                            <td>
                                <div class="lesson-title-cell">
                                    <strong>第2课：CSS样式设计</strong>
                                    <div class="lesson-description">掌握CSS选择器、属性和布局技巧，美化网页外观。</div>
                                </div>
                            </td>
                            <td>00:40:00</td>
                            <td>
                                <span class="status-badge status-active">启用</span>
                            </td>
                            <td>
                                <span class="status-badge free-badge">免费</span>
                            </td>
                            <td>07-16 14:35</td>
                            <td>
                                <button type="button" class="btn btn-info btn-sm" onclick="alert('编辑课时')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" onclick="alert('删除课时')">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span class="sort-handle" style="display: none;">
                                    <i class="fas fa-grip-vertical"></i>
                                </span>
                                <span class="sort-order">3</span>
                            </td>
                            <td>
                                <div class="lesson-title-cell">
                                    <strong>第3课：JavaScript基础</strong>
                                    <div class="lesson-description">学习JavaScript语法、变量、函数等基础概念。</div>
                                </div>
                            </td>
                            <td>00:50:00</td>
                            <td>
                                <span class="status-badge status-active">启用</span>
                            </td>
                            <td>
                                <span class="status-badge paid-badge">付费</span>
                            </td>
                            <td>07-16 14:40</td>
                            <td>
                                <button type="button" class="btn btn-info btn-sm" onclick="alert('编辑课时')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" onclick="alert('删除课时')">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        console.log('课时管理UI测试页面加载完成');
        console.log('所有样式和交互效果已应用');
    </script>
</body>
</html>
