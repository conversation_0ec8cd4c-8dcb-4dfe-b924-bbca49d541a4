<template>
	<view class="lesson-player-container">
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<uni-load-more status="loading"></uni-load-more>
		</view>
		
		<!-- 课时播放器 -->
		<view class="player-content" v-else-if="lessonInfo">
			<!-- 课时信息头部 -->
			<view class="lesson-header">
				<view class="lesson-title">{{ lessonInfo.title }}</view>
				<view class="lesson-meta">
					<text class="lesson-duration">{{ formatDuration(lessonInfo.duration) }}</text>
					<uni-tag
						v-if="lessonInfo.is_free"
						text="免费"
						type="success"
						size="mini"
					></uni-tag>
					<uni-tag
						v-else
						text="付费"
						type="warning"
						size="mini"
					></uni-tag>
				</view>
			</view>
			
			<!-- 视频播放器 -->
			<view class="video-container">
				<!-- 腾讯云点播播放器 -->
				<view v-if="lessonInfo.video_type === 'vod' && lessonInfo.vod_file_id" class="vod-player">
					<!-- VOD视频有播放URL时显示播放器 -->
					<video
						v-if="lessonInfo.vod_video_url"
						id="vod-player"
						class="video-player"
						:src="lessonInfo.vod_video_url"
						:poster="validPoster"
						controls
						:show-fullscreen-btn="true"
						:show-play-btn="true"
						:show-center-play-btn="true"
						:enable-progress-gesture="true"
						:page-gesture="false"
						@timeupdate="handleTimeUpdate"
						@ended="handleVideoEnded"
						@play="handleVideoPlay"
						@pause="handleVideoPause"
						@error="handleVideoError"
						@fullscreenchange="handleFullscreenChange"
					></video>

					<!-- VOD视频正在获取播放URL时显示加载状态 -->
					<view v-else class="vod-loading">
						<uni-load-more status="loading" content-text="{ contentText: { contentdown: '正在获取播放地址...', contentrefresh: '正在获取播放地址...', contentnomore: '获取播放地址失败' } }"></uni-load-more>
						<text class="loading-text">正在获取播放地址，请稍候...</text>
						<button class="retry-btn" @click="retryGetVodUrl" v-if="vodRetryCount >= maxVodRetries">
							手动重试
						</button>
					</view>
				</view>

				<!-- 普通视频播放器 -->
				<view v-else-if="lessonInfo.video_url" class="normal-player">
					<video
						id="normal-player"
						class="video-player"
						:src="lessonInfo.video_url"
						:poster="validPoster"
						controls
						:show-fullscreen-btn="true"
						:show-play-btn="true"
						:show-center-play-btn="true"
						:enable-progress-gesture="true"
						:page-gesture="false"
						@timeupdate="handleTimeUpdate"
						@ended="handleVideoEnded"
						@play="handleVideoPlay"
						@pause="handleVideoPause"
						@error="handleVideoError"
						@fullscreenchange="handleFullscreenChange"
					></video>
				</view>

				<!-- 无视频提示 -->
				<view v-else class="no-video">
					<uni-icons type="videocam" size="60" color="#ccc"></uni-icons>
					<text class="no-video-text">暂无视频内容</text>
				</view>
			</view>
			
			<!-- 播放控制栏 -->
			<view class="player-controls" v-if="hasVideo">
				<view class="progress-section">
					<text class="time-text">{{ formatTime(currentTime) }}</text>
					<view class="progress-bar" @click="seekTo">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
							<view class="progress-thumb" :style="{ left: progressPercent + '%' }"></view>
						</view>
					</view>
					<text class="time-text">{{ formatTime(lessonInfo.duration) }}</text>
				</view>
				
				<view class="control-buttons">
					<button class="control-btn" @click="togglePlay">
						<uni-icons :type="isPlaying ? 'pause' : 'play'" size="20"></uni-icons>
					</button>
					<button class="control-btn" @click="toggleFullscreen">
						<uni-icons type="fullscreen" size="20"></uni-icons>
					</button>
				</view>
			</view>
			
			<!-- 课时描述 -->
			<view class="lesson-description" v-if="lessonInfo.description">
				<view class="section-title">课时介绍</view>
				<text class="description-text">{{ lessonInfo.description }}</text>
			</view>
			
			<!-- 课程信息 -->
			<view class="course-info" v-if="lessonInfo.course">
				<view class="section-title">所属课程</view>
				<view class="course-card" @click="goToCourse">
					<text class="course-title">{{ lessonInfo.course.title }}</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
			
			<!-- 课时导航 -->
			<view class="lesson-navigation" v-if="lessonInfo.navigation">
				<view class="section-title">课时导航</view>
				<view class="nav-buttons">
					<button 
						class="nav-btn prev-btn" 
						:disabled="!lessonInfo.navigation.prev_lesson"
						@click="goToPrevLesson"
					>
						<uni-icons type="left" size="16"></uni-icons>
						<text>上一课时</text>
					</button>
					<button 
						class="nav-btn next-btn" 
						:disabled="!lessonInfo.navigation.next_lesson"
						@click="goToNextLesson"
					>
						<text>下一课时</text>
						<uni-icons type="right" size="16"></uni-icons>
					</button>
				</view>
				<view class="lesson-progress-text">
					第{{ lessonInfo.navigation.current_index }}课时 / 共{{ lessonInfo.navigation.total_lessons }}课时
				</view>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view class="error-container" v-else-if="error">
			<uni-icons type="info" size="60" color="#f56c6c"></uni-icons>
			<text class="error-text">{{ error }}</text>
			<button class="retry-btn" @click="loadLessonInfo">重试</button>
		</view>
	</view>
</template>

<script>
import { lessonApi } from '@/api/course.js'

export default {
	name: 'LessonPlayer',
	data() {
		return {
			loading: true,
			error: '',
			lessonInfo: null,
			lessonId: 0,
			courseId: 0,

			// 播放状态
			isPlaying: false,
			currentTime: 0,
			duration: 0,
			isFullscreen: false,

			// VOD相关状态
			vodUrlFetching: false,
			vodRetryCount: 0,
			maxVodRetries: 3,

			// 播放进度记录
			watchStartTime: 0,
			lastProgressTime: 0,
			progressSaveInterval: null
		}
	},
	
	computed: {
		hasVideo() {
			if (!this.lessonInfo) return false

			// VOD类型视频
			if (this.lessonInfo.video_type === 'vod') {
				return this.lessonInfo.vod_file_id || this.lessonInfo.vod_video_url
			}

			// URL类型视频
			if (this.lessonInfo.video_type === 'url') {
				return this.lessonInfo.video_url && this.isValidVideoUrl(this.lessonInfo.video_url)
			}

			return false
		},

		validPoster() {
			// 验证poster是否为有效的网络地址
			if (!this.lessonInfo || !this.lessonInfo.thumbnail) {
				console.log('validPoster: 没有缩略图数据')
				return ''
			}

			const thumbnail = this.lessonInfo.thumbnail
			console.log('validPoster: 原始缩略图URL:', thumbnail)

			// 检查是否为有效的网络地址（以http://或https://开头）
			if (thumbnail.startsWith('http://') || thumbnail.startsWith('https://')) {
				console.log('validPoster: 有效的网络地址，直接使用:', thumbnail)
				return thumbnail
			}

			// 如果不是有效的网络地址，返回空字符串
			console.log('validPoster: 无效的网络地址，返回空字符串')
			return ''
		},

		progressPercent() {
			if (!this.duration || this.duration === 0) return 0
			return Math.min((this.currentTime / this.duration) * 100, 100)
		}
	},
	
	onLoad(options) {
		this.lessonId = parseInt(options.lesson_id) || 0
		this.courseId = parseInt(options.course_id) || 0
		
		if (this.lessonId <= 0) {
			this.error = '课时ID无效'
			this.loading = false
			return
		}
		
		this.loadLessonInfo()
	},
	
	onUnload() {
		this.saveWatchProgress()
		this.clearProgressInterval()
	},
	
	onHide() {
		this.saveWatchProgress()
	},
	
	methods: {
		async loadLessonInfo() {
			try {
				this.loading = true
				this.error = ''
				
				const response = await lessonApi.getLessonDetail({
					lesson_id: this.lessonId,
					course_id: this.courseId
				})
				
				if (response.success) {
					this.lessonInfo = response.data
					this.duration = this.lessonInfo.duration || 0
					
					// 设置页面标题
					uni.setNavigationBarTitle({
						title: this.lessonInfo.title
					})
					
					// 开始记录观看时间
					this.startWatchTracking()
				} else {
					this.error = response.message || '加载课时信息失败'
				}
			} catch (error) {
				console.error('加载课时信息失败:', error)
				this.error = '网络错误，请重试'
			} finally {
				this.loading = false
			}
		},
		
		getVodPlayUrl() {
			// 如果有腾讯云点播URL，直接使用
			if (this.lessonInfo.vod_video_url) {
				console.log('使用已保存的VOD播放URL:', this.lessonInfo.vod_video_url)
				return this.lessonInfo.vod_video_url
			}

			// 如果有fileId但没有播放URL，尝试通过API获取
			if (this.lessonInfo.vod_file_id) {
				console.log('检测到VOD fileId但无播放URL，尝试获取:', this.lessonInfo.vod_file_id)
				this.fetchVodPlayUrl(this.lessonInfo.vod_file_id)
				// 显示加载提示
				if (!this.vodUrlFetching) {
					this.vodUrlFetching = true
					uni.showToast({
						title: '正在获取播放地址...',
						icon: 'loading',
						duration: 2000
					})
				}
				// 返回空字符串，等待API返回结果
				return ''
			}

			console.log('没有可用的VOD播放URL')
			return ''
		},

		// 通过API获取VOD播放URL
		async fetchVodPlayUrl(fileId) {
			try {
				console.log('正在获取VOD播放URL:', fileId, '重试次数:', this.vodRetryCount)

				const response = await this.$http.get('/api/vod-status.php', {
					params: {
						file_id: fileId
					}
				})

				if (response.data.success && response.data.data) {
					const videoData = response.data.data
					let playUrl = ''

					// 优先使用转码后的URL
					if (videoData.transcoding && videoData.transcoding.streams && videoData.transcoding.streams.length > 0) {
						// 选择合适清晰度的转码流
						const streams = videoData.transcoding.streams
						// 优先选择720p或480p，如果没有则选择第一个
						const preferredStream = streams.find(s => s.height === 720) ||
												streams.find(s => s.height === 480) ||
												streams[0]
						playUrl = preferredStream.url
					} else if (videoData.playback_urls && videoData.playback_urls.length > 0) {
						// 使用自适应流URL
						playUrl = videoData.playback_urls[0].url
					}

					if (playUrl) {
						console.log('获取到VOD播放URL:', playUrl)
						// 更新课时信息中的播放URL
						this.lessonInfo.vod_video_url = playUrl
						this.vodUrlFetching = false
						this.vodRetryCount = 0
						// 隐藏加载提示
						uni.hideToast()
						// 触发重新渲染
						this.$forceUpdate()
						// 显示成功提示
						uni.showToast({
							title: '播放地址获取成功',
							icon: 'success',
							duration: 1500
						})
					} else {
						console.log('转码可能还在进行中，暂无可用播放URL')
						this.handleVodUrlFetchFailed('视频转码中，请稍后重试')
					}
				} else {
					console.error('获取VOD播放URL失败:', response.data.message)
					this.handleVodUrlFetchFailed(response.data.message || '获取播放地址失败')
				}
			} catch (error) {
				console.error('获取VOD播放URL请求失败:', error)
				this.handleVodUrlFetchFailed('网络请求失败，请检查网络连接')
			}
		},

		// 处理VOD URL获取失败
		handleVodUrlFetchFailed(message) {
			this.vodUrlFetching = false

			if (this.vodRetryCount < this.maxVodRetries) {
				this.vodRetryCount++
				console.log(`VOD URL获取失败，${3}秒后重试 (${this.vodRetryCount}/${this.maxVodRetries})`)

				uni.showToast({
					title: `${message}，${3}秒后重试`,
					icon: 'none',
					duration: 2000
				})

				setTimeout(() => {
					if (this.lessonInfo && this.lessonInfo.vod_file_id && !this.lessonInfo.vod_video_url) {
						this.fetchVodPlayUrl(this.lessonInfo.vod_file_id)
					}
				}, 3000)
			} else {
				console.log('VOD URL获取重试次数已达上限')
				uni.showModal({
					title: '播放失败',
					content: `${message}，已重试${this.maxVodRetries}次。请稍后手动刷新页面重试。`,
					showCancel: true,
					cancelText: '稍后重试',
					confirmText: '刷新页面',
					success: (res) => {
						if (res.confirm) {
							// 重新加载页面
							this.vodRetryCount = 0
							this.loadLessonInfo()
						}
					}
				})
			}
		},

		// 手动重试获取VOD播放URL
		retryGetVodUrl() {
			if (this.lessonInfo && this.lessonInfo.vod_file_id) {
				this.vodRetryCount = 0
				this.vodUrlFetching = false
				this.fetchVodPlayUrl(this.lessonInfo.vod_file_id)
			}
		},

		handleTimeUpdate(e) {
			this.currentTime = e.detail.currentTime || 0
			
			// 每5秒保存一次进度
			if (this.currentTime - this.lastProgressTime >= 5) {
				this.saveWatchProgress()
				this.lastProgressTime = this.currentTime
			}
		},
		
		handleVideoEnded() {
			this.isPlaying = false
			this.saveWatchProgress(true) // 标记为完成
			
			// 自动播放下一课时
			if (this.lessonInfo.navigation && this.lessonInfo.navigation.next_lesson) {
				uni.showModal({
					title: '课时完成',
					content: '是否播放下一课时？',
					success: (res) => {
						if (res.confirm) {
							this.goToNextLesson()
						}
					}
				})
			}
		},
		
		handleVideoPlay() {
			this.isPlaying = true
			this.startProgressInterval()
		},
		
		handleVideoPause() {
			this.isPlaying = false
			this.clearProgressInterval()
			this.saveWatchProgress()
		},
		
		handleVideoError(e) {
			console.error('视频播放错误:', e)

			// 详细的错误信息
			let errorMessage = '视频播放失败'
			let shouldRetry = false

			if (e && e.detail) {
				const errorCode = e.detail.errCode || e.detail.errorCode
				const errorMsg = e.detail.errMsg || e.detail.errorMessage

				console.error('视频错误详情:', {
					errorCode,
					errorMsg,
					detail: e.detail,
					lessonInfo: this.lessonInfo
				})

				// 根据错误码提供更具体的错误信息
				switch (errorCode) {
					case -1:
						errorMessage = '网络连接失败，请检查网络连接'
						shouldRetry = true
						break
					case -2:
						errorMessage = '视频格式不支持，请联系管理员'
						break
					case -3:
						errorMessage = '视频地址无效或已失效'
						shouldRetry = true
						break
					case 1001:
						errorMessage = '网络错误，请重试'
						shouldRetry = true
						break
					case 1002:
						errorMessage = '视频解码失败，可能是格式问题'
						break
					case 1003:
						errorMessage = '视频文件损坏或不完整'
						break
					case 1004:
						errorMessage = '视频加载超时，请检查网络'
						shouldRetry = true
						break
					default:
						if (errorMsg) {
							errorMessage = errorMsg
							shouldRetry = true
						}
				}
			}

			// 特殊处理：如果是VOD视频且没有播放URL，尝试重新获取
			if (this.lessonInfo && this.lessonInfo.video_type === 'vod' &&
				this.lessonInfo.vod_file_id && !this.lessonInfo.vod_video_url) {
				console.log('VOD视频播放失败，尝试重新获取播放URL')
				this.fetchVodPlayUrl(this.lessonInfo.vod_file_id)
				return
			}

			// 特殊处理：如果是普通URL视频，检查URL有效性
			if (this.lessonInfo && this.lessonInfo.video_type === 'url' && this.lessonInfo.video_url) {
				// 检查是否是已知的无效测试URL
				if (this.lessonInfo.video_url.includes('sample-videos.com')) {
					errorMessage = '测试视频链接已失效，请联系管理员更新视频'
					shouldRetry = false
				}
			}

			// 显示错误对话框
			const modalOptions = {
				title: '播放错误',
				content: errorMessage + '\n\n错误详情：' + (e.detail?.errMsg || '未知错误'),
				showCancel: shouldRetry,
				cancelText: '取消',
				confirmText: shouldRetry ? '重试' : '确定',
				success: (res) => {
					if (res.confirm && shouldRetry) {
						// 重新加载课时信息
						this.loadLessonInfo()
					}
				}
			}

			uni.showModal(modalOptions)
		},
		
		handleFullscreenChange(e) {
			this.isFullscreen = e.detail.fullScreen
		},
		
		togglePlay() {
			const videoContext = uni.createVideoContext('vod-player') || uni.createVideoContext('normal-player')
			if (this.isPlaying) {
				videoContext.pause()
			} else {
				videoContext.play()
			}
		},
		
		toggleFullscreen() {
			const videoContext = uni.createVideoContext('vod-player') || uni.createVideoContext('normal-player')
			if (this.isFullscreen) {
				videoContext.exitFullScreen()
			} else {
				videoContext.requestFullScreen()
			}
		},
		
		seekTo(e) {
			if (!this.duration) return
			
			const rect = e.currentTarget.getBoundingClientRect()
			const x = e.detail.x - rect.left
			const percent = x / rect.width
			const seekTime = percent * this.duration
			
			const videoContext = uni.createVideoContext('vod-player') || uni.createVideoContext('normal-player')
			videoContext.seek(seekTime)
		},
		
		startWatchTracking() {
			this.watchStartTime = Date.now()
		},
		
		startProgressInterval() {
			this.clearProgressInterval()
			this.progressSaveInterval = setInterval(() => {
				this.saveWatchProgress()
			}, 10000) // 每10秒保存一次
		},
		
		clearProgressInterval() {
			if (this.progressSaveInterval) {
				clearInterval(this.progressSaveInterval)
				this.progressSaveInterval = null
			}
		},
		
		async saveWatchProgress(isCompleted = false) {
			if (!this.lessonInfo || this.currentTime <= 0) return
			
			try {
				const watchTime = Math.floor((Date.now() - this.watchStartTime) / 1000)
				const progressPosition = Math.floor(this.currentTime)
				const completionRate = this.duration > 0 ? (progressPosition / this.duration) * 100 : 0
				
				await lessonApi.saveWatchProgress({
					lesson_id: this.lessonId,
					course_id: this.courseId,
					watch_time: watchTime,
					progress_position: progressPosition,
					completion_rate: Math.min(completionRate, 100),
					is_completed: isCompleted || completionRate >= 90
				})
			} catch (error) {
				console.error('保存观看进度失败:', error)
			}
		},
		
		goToCourse() {
			uni.navigateTo({
				url: `/pages/courses/detail?id=${this.courseId}`
			})
		},
		
		goToPrevLesson() {
			if (this.lessonInfo.navigation && this.lessonInfo.navigation.prev_lesson) {
				const prevLesson = this.lessonInfo.navigation.prev_lesson
				uni.redirectTo({
					url: `/pages/lessons/player?lesson_id=${prevLesson.id}&course_id=${this.courseId}`
				})
			}
		},
		
		goToNextLesson() {
			if (this.lessonInfo.navigation && this.lessonInfo.navigation.next_lesson) {
				const nextLesson = this.lessonInfo.navigation.next_lesson
				uni.redirectTo({
					url: `/pages/lessons/player?lesson_id=${nextLesson.id}&course_id=${this.courseId}`
				})
			}
		},
		
		formatDuration(seconds) {
			if (!seconds) return '00:00'
			const hours = Math.floor(seconds / 3600)
			const minutes = Math.floor((seconds % 3600) / 60)
			const secs = seconds % 60
			
			if (hours > 0) {
				return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
			} else {
				return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
			}
		},
		
		formatTime(seconds) {
			return this.formatDuration(Math.floor(seconds))
		},

		/**
		 * 验证视频URL是否有效
		 */
		isValidVideoUrl(url) {
			if (!url || typeof url !== 'string') return false

			// 检查是否是有效的URL格式
			try {
				new URL(url)
			} catch (e) {
				return false
			}

			// 检查协议（小程序要求HTTPS）
			if (!url.startsWith('https://')) {
				console.warn('视频URL必须使用HTTPS协议:', url)
				return false
			}

			// 检查是否是已知的无效测试URL
			const invalidDomains = [
				'sample-videos.com',
				'example.com',
				'test.com'
			]

			for (const domain of invalidDomains) {
				if (url.includes(domain)) {
					console.warn('检测到无效的测试URL:', url)
					return false
				}
			}

			// 检查文件扩展名（可选）
			const supportedExtensions = ['.mp4', '.m3u8', '.mov', '.avi']
			const hasValidExtension = supportedExtensions.some(ext =>
				url.toLowerCase().includes(ext)
			)

			if (!hasValidExtension) {
				console.warn('视频URL可能不包含支持的文件格式:', url)
				// 不直接返回false，因为有些URL可能没有明显的扩展名
			}

			return true
		},

		/**
		 * 获取视频播放的最佳URL
		 */
		getBestVideoUrl() {
			if (!this.lessonInfo) return ''

			// VOD类型优先使用vod_video_url
			if (this.lessonInfo.video_type === 'vod') {
				if (this.lessonInfo.vod_video_url) {
					return this.lessonInfo.vod_video_url
				}
				// 如果有file_id但没有播放URL，触发获取
				if (this.lessonInfo.vod_file_id) {
					this.fetchVodPlayUrl(this.lessonInfo.vod_file_id)
					return ''
				}
			}

			// URL类型使用video_url
			if (this.lessonInfo.video_type === 'url' && this.lessonInfo.video_url) {
				if (this.isValidVideoUrl(this.lessonInfo.video_url)) {
					return this.lessonInfo.video_url
				} else {
					console.error('无效的视频URL:', this.lessonInfo.video_url)
					return ''
				}
			}

			return ''
		}
	}
}
</script>

<style lang="scss" scoped>
.lesson-player-container {
	min-height: 100vh;
	background-color: #f8f9fa;
}

.loading-container, .error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 60vh;
	padding: 40rpx;
}

.error-text {
	margin: 20rpx 0;
	color: #666;
	text-align: center;
}

.retry-btn {
	margin-top: 20rpx;
	padding: 16rpx 32rpx;
	background-color: #007aff;
	color: white;
	border-radius: 8rpx;
	border: none;
	font-size: 28rpx;
}

.player-content {
	background-color: white;
}

.lesson-header {
	padding: 30rpx;
	border-bottom: 1px solid #eee;

	.lesson-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 16rpx;
		line-height: 1.4;
	}

	.lesson-meta {
		display: flex;
		align-items: center;
		gap: 16rpx;

		.lesson-duration {
			color: #666;
			font-size: 26rpx;
		}
	}
}

.video-container {
	position: relative;
	width: 100%;
	background-color: #000;

	.video-player {
		width: 100%;
		height: 420rpx;
	}

	.no-video, .vod-loading {
		height: 420rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #f5f5f5;

		.no-video-text, .loading-text {
			margin-top: 20rpx;
			color: #999;
			font-size: 28rpx;
			text-align: center;
		}

		.retry-btn {
			margin-top: 30rpx;
			padding: 16rpx 32rpx;
			background-color: #007aff;
			color: white;
			border-radius: 8rpx;
			border: none;
			font-size: 28rpx;
		}
	}
}

.player-controls {
	padding: 20rpx 30rpx;
	background-color: white;
	border-bottom: 1px solid #eee;

	.progress-section {
		display: flex;
		align-items: center;
		gap: 20rpx;
		margin-bottom: 20rpx;

		.time-text {
			font-size: 24rpx;
			color: #666;
			min-width: 80rpx;
		}

		.progress-bar {
			flex: 1;
			height: 40rpx;
			display: flex;
			align-items: center;

			.progress-bg {
				position: relative;
				width: 100%;
				height: 6rpx;
				background-color: #e5e5e5;
				border-radius: 3rpx;

				.progress-fill {
					height: 100%;
					background-color: #007aff;
					border-radius: 3rpx;
					transition: width 0.1s;
				}

				.progress-thumb {
					position: absolute;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 20rpx;
					height: 20rpx;
					background-color: #007aff;
					border-radius: 50%;
					box-shadow: 0 2rpx 6rpx rgba(0, 122, 255, 0.3);
				}
			}
		}
	}

	.control-buttons {
		display: flex;
		justify-content: center;
		gap: 40rpx;

		.control-btn {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			background-color: #f8f9fa;
			border: 1px solid #e5e5e5;
			display: flex;
			align-items: center;
			justify-content: center;

			&:active {
				background-color: #e5e5e5;
			}
		}
	}
}

.lesson-description, .course-info, .lesson-navigation {
	margin: 20rpx 0;
	background-color: white;

	.section-title {
		padding: 30rpx 30rpx 20rpx;
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		border-bottom: 1px solid #f0f0f0;
	}
}

.lesson-description {
	.description-text {
		padding: 20rpx 30rpx 30rpx;
		color: #666;
		line-height: 1.6;
		font-size: 28rpx;
	}
}

.course-info {
	.course-card {
		padding: 20rpx 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.course-title {
			color: #333;
			font-size: 30rpx;
		}

		&:active {
			background-color: #f8f9fa;
		}
	}
}

.lesson-navigation {
	.nav-buttons {
		padding: 20rpx 30rpx;
		display: flex;
		gap: 20rpx;

		.nav-btn {
			flex: 1;
			height: 80rpx;
			border-radius: 8rpx;
			border: 1px solid #e5e5e5;
			background-color: white;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 10rpx;
			font-size: 28rpx;
			color: #333;

			&:not(:disabled):active {
				background-color: #f8f9fa;
			}

			&:disabled {
				color: #ccc;
				border-color: #f0f0f0;
			}
		}

		.prev-btn {
			border-color: #007aff;
			color: #007aff;
		}

		.next-btn {
			background-color: #007aff;
			color: white;
			border-color: #007aff;
		}
	}

	.lesson-progress-text {
		padding: 10rpx 30rpx 30rpx;
		text-align: center;
		color: #999;
		font-size: 26rpx;
	}
}
</style>
