<?php
/**
 * 快速修复支付问题
 * 提供快速解决方案来处理微信支付配置问题
 */

header('Content-Type: text/html; charset=utf-8');
require_once '../includes/db.php';

$action = $_GET['action'] ?? '';
$message = '';

if ($action) {
    try {
        switch ($action) {
            case 'disable_payment':
                // 禁用微信支付
                $stmt = $conn->prepare("
                    INSERT INTO settings (setting_key, setting_value, setting_type, description) 
                    VALUES ('wechat_pay_enabled', '0', 'boolean', '是否启用微信支付')
                    ON DUPLICATE KEY UPDATE setting_value = '0'
                ");
                if ($stmt->execute()) {
                    $message = "✅ 已禁用微信支付，现在支付功能将显示维护提示";
                } else {
                    $message = "❌ 操作失败";
                }
                break;
                
            case 'enable_test_config':
                // 设置测试配置（注意：这不是真实的支付配置）
                $test_configs = [
                    'wechat_pay_enabled' => '0', // 仍然禁用，避免真实调用
                    'wechat_pay_app_id' => 'wxa936b5c9fa9b1893',
                    'wechat_pay_mch_id' => '1234567890', // 测试商户号
                    'wechat_pay_api_key' => 'test_api_key_32_characters_long', // 测试密钥
                    'wechat_pay_notify_url' => 'https://wx.yx420.cn/api/payment-wechat-notify.php'
                ];
                
                foreach ($test_configs as $key => $value) {
                    $stmt = $conn->prepare("
                        INSERT INTO settings (setting_key, setting_value, setting_type, description) 
                        VALUES (?, ?, 'string', ?)
                        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                    ");
                    $description = '微信支付配置: ' . $key;
                    $stmt->bind_param("sss", $key, $value, $description);
                    $stmt->execute();
                }
                
                $message = "✅ 已设置测试配置（支付仍然禁用以避免错误）";
                break;
                
            case 'clear_config':
                // 清除所有微信支付配置
                $stmt = $conn->prepare("DELETE FROM settings WHERE setting_key LIKE 'wechat_pay_%'");
                if ($stmt->execute()) {
                    $message = "✅ 已清除所有微信支付配置";
                } else {
                    $message = "❌ 清除失败";
                }
                break;
        }
    } catch (Exception $e) {
        $message = "❌ 操作失败: " . $e->getMessage();
    }
}

// 获取当前配置状态
$current_config = [];
try {
    $stmt = $conn->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'wechat_pay_%'");
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $current_config[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    $message = "❌ 读取配置失败: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速修复支付问题</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .message { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }
        .btn-danger { background: #dc3545; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-info { background: #17a2b8; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>快速修复微信支付问题</h1>
    
    <?php if ($message): ?>
        <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>
    
    <div class="info message">
        <h3>当前问题</h3>
        <p>微信小程序购买课程时出现 "appid和mch_id不匹配" 错误，这是因为微信支付配置不完整或不正确。</p>
    </div>
    
    <h2>快速解决方案</h2>
    
    <div class="warning message">
        <h3>推荐方案：禁用微信支付</h3>
        <p>如果您暂时没有微信支付商户号，建议先禁用微信支付功能，这样用户在尝试支付时会看到友好的提示信息。</p>
        <a href="?action=disable_payment" class="btn btn-danger">禁用微信支付</a>
    </div>
    
    <h2>其他操作</h2>
    <p>
        <a href="?action=enable_test_config" class="btn btn-warning">设置测试配置</a>
        <a href="?action=clear_config" class="btn btn-info">清除所有配置</a>
        <a href="setup_wechat_payment.php" class="btn btn-success">完整配置页面</a>
    </p>
    
    <h2>当前配置状态</h2>
    <?php if (!empty($current_config)): ?>
        <table>
            <tr>
                <th>配置项</th>
                <th>当前值</th>
                <th>状态</th>
            </tr>
            <?php
            $config_names = [
                'wechat_pay_enabled' => '是否启用',
                'wechat_pay_app_id' => 'AppID',
                'wechat_pay_mch_id' => '商户号',
                'wechat_pay_api_key' => 'API密钥',
                'wechat_pay_notify_url' => '回调URL'
            ];
            
            foreach ($config_names as $key => $name) {
                $value = $current_config[$key] ?? '';
                $status = empty($value) ? '❌ 未设置' : '✅ 已设置';
                
                if ($key === 'wechat_pay_api_key' && !empty($value)) {
                    $value = str_repeat('*', min(strlen($value), 20));
                }
                
                echo "<tr>";
                echo "<td>$name</td>";
                echo "<td>" . htmlspecialchars($value) . "</td>";
                echo "<td>$status</td>";
                echo "</tr>";
            }
            ?>
        </table>
    <?php else: ?>
        <p>没有找到微信支付配置。</p>
    <?php endif; ?>
    
    <h2>测试步骤</h2>
    <div class="info message">
        <ol>
            <li><strong>禁用微信支付</strong>：点击上面的"禁用微信支付"按钮</li>
            <li><strong>重新测试</strong>：在微信开发者工具中重新测试购买流程</li>
            <li><strong>查看效果</strong>：现在应该显示友好的错误提示而不是技术错误</li>
            <li><strong>后续配置</strong>：获得真实微信支付商户号后，使用"完整配置页面"进行设置</li>
        </ol>
    </div>
    
    <h2>长期解决方案</h2>
    <div class="warning message">
        <p>要完全启用微信支付功能，您需要：</p>
        <ul>
            <li>申请微信支付商户号（在微信支付商户平台）</li>
            <li>获取真实的API密钥</li>
            <li>确保AppID与商户号正确绑定</li>
            <li>配置正确的回调URL</li>
        </ul>
        <p>完成后，使用 <a href="setup_wechat_payment.php">完整配置页面</a> 进行设置。</p>
    </div>
</body>
</html>
