# 课程视频系统 API 接口文档

## 基础信息

**Base URL**: `https://wx.yx420.cn/api/`
**Content-Type**: `application/json; charset=utf-8`
**CORS**: 支持跨域请求

## 接口列表

### 1. 获取课程列表

**接口地址**: `courseList.php`
**请求方式**: `GET`

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码 |
| limit | int | 否 | 10 | 每页数量（最大50） |
| user_id | int | 否 | 0 | 用户ID，提供时只返回该用户有权限的课程 |
| search | string | 否 | - | 搜索关键词（标题或描述） |

#### 请求示例

```javascript
// 获取所有启用的课程（不包含视频链接）
fetch('https://wx.yx420.cn/api/courseList.php?page=1&limit=10')

// 获取用户有权限的课程（包含视频链接和用户信息）
fetch('https://wx.yx420.cn/api/courseList.php?user_id=123&page=1&limit=10')

// 搜索课程
fetch('https://wx.yx420.cn/api/courseList.php?search=入门教程')
```

#### 响应格式

```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "title": "入门教程：系统使用指南",
                "description": "本课程将详细介绍系统的基本功能和使用方法",
                "video_url": "https://example.com/videos/tutorial-basic.mp4",
                "thumbnail": "https://example.com/thumbnails/tutorial-basic.jpg",
                "duration": 1800,
                "duration_formatted": "00:30:00",
                "sort_order": 1,
                "created_at": "2025-07-14 10:00:00",
                "user_info": {
                    "assigned_at": "2025-07-14 09:00:00",
                    "expires_at": null,
                    "last_watched_at": "2025-07-14 11:30:00",
                    "watch_progress": 45.5,
                    "watch_count": 3
                }
            }
        ],
        "pagination": {
            "page": 1,
            "limit": 10,
            "total": 2,
            "pages": 1
        }
    },
    "timestamp": 1720819200
}
```

### 2. 获取课程详情

**接口地址**: `courseDetail.php`
**请求方式**: `GET`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 课程ID |
| user_id | int | 否 | 用户ID，提供时验证权限并返回用户信息 |

#### 请求示例

```javascript
// 获取课程基本信息（不包含视频链接）
fetch('https://wx.yx420.cn/api/courseDetail.php?id=1')

// 获取课程详情（验证用户权限）
fetch('https://wx.yx420.cn/api/courseDetail.php?id=1&user_id=123')
```

#### 响应格式

```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "title": "入门教程：系统使用指南",
        "description": "本课程将详细介绍系统的基本功能和使用方法，适合新用户学习。",
        "video_url": "https://example.com/videos/tutorial-basic.mp4",
        "thumbnail": "https://example.com/thumbnails/tutorial-basic.jpg",
        "duration": 1800,
        "duration_formatted": "00:30:00",
        "sort_order": 1,
        "created_at": "2025-07-14 10:00:00",
        "user_info": {
            "assigned_at": "2025-07-14 09:00:00",
            "expires_at": null,
            "last_watched_at": "2025-07-14 11:30:00",
            "watch_progress": 45.5,
            "watch_count": 3,
            "has_access": true
        }
    },
    "timestamp": 1720819200
}
```

### 3. 更新观看进度

**接口地址**: `courseDetail.php`
**请求方式**: `POST`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID |
| course_id | int | 是 | 课程ID |
| watch_time | int | 否 | 本次观看时长（秒） |
| progress_position | int | 否 | 观看进度位置（秒） |
| watch_progress | float | 否 | 观看进度百分比（0-100） |

#### 请求示例

```javascript
fetch('https://wx.yx420.cn/api/courseDetail.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        user_id: 123,
        course_id: 1,
        watch_time: 300,
        progress_position: 900,
        watch_progress: 50.0
    })
})
```

#### 响应格式

```json
{
    "code": 200,
    "message": "观看进度更新成功",
    "data": {
        "watch_progress": 50.0,
        "watch_count": 4,
        "last_watched_at": "2025-07-14 12:00:00"
    },
    "timestamp": 1720819200
}
```

### 4. 获取用户课程列表

**接口地址**: `userCourses.php`
**请求方式**: `GET`

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| user_id | int | 是 | - | 用户ID |
| page | int | 否 | 1 | 页码 |
| limit | int | 否 | 10 | 每页数量（最大50） |
| status | string | 否 | active | 状态筛选：active/expired/revoked/all |
| search | string | 否 | - | 搜索关键词 |

#### 请求示例

```javascript
// 获取用户的有效课程
fetch('https://wx.yx420.cn/api/userCourses.php?user_id=123&status=active')

// 获取用户的过期课程
fetch('https://wx.yx420.cn/api/userCourses.php?user_id=123&status=expired')

// 获取用户的所有课程
fetch('https://wx.yx420.cn/api/userCourses.php?user_id=123&status=all')
```

#### 响应格式

```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "title": "入门教程：系统使用指南",
                "description": "本课程将详细介绍系统的基本功能和使用方法",
                "video_url": "https://example.com/videos/tutorial-basic.mp4",
                "thumbnail": "https://example.com/thumbnails/tutorial-basic.jpg",
                "duration": 1800,
                "duration_formatted": "00:30:00",
                "sort_order": 1,
                "created_at": "2025-07-14 10:00:00",
                "user_info": {
                    "assigned_at": "2025-07-14 09:00:00",
                    "expires_at": null,
                    "last_watched_at": "2025-07-14 11:30:00",
                    "watch_progress": 45.5,
                    "watch_count": 3,
                    "status": "active",
                    "is_expired": false,
                    "assigned_by": "admin",
                    "has_access": true
                }
            }
        ],
        "pagination": {
            "page": 1,
            "limit": 10,
            "total": 2,
            "pages": 1
        },
        "statistics": {
            "total_courses": 5,
            "active_courses": 3,
            "expired_courses": 1,
            "revoked_courses": 1,
            "average_progress": 62.5,
            "total_watch_count": 15
        }
    },
    "timestamp": 1720819200
}
```

## 前端使用示例

### HTML 页面示例

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程视频系统</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .course-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }
        .course-card { border: 1px solid #ddd; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .course-thumbnail { width: 100%; height: 180px; object-fit: cover; }
        .course-content { padding: 15px; }
        .course-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
        .course-description { color: #666; font-size: 14px; margin-bottom: 10px; }
        .course-meta { display: flex; justify-content: space-between; align-items: center; font-size: 12px; color: #999; }
        .progress-bar { width: 100%; height: 6px; background: #e1e8ed; border-radius: 3px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: #28a745; transition: width 0.3s ease; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .video-player { width: 100%; max-width: 800px; margin: 20px auto; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; }
        .modal-content { background: white; margin: 50px auto; padding: 20px; width: 90%; max-width: 900px; border-radius: 8px; }
    </style>
</head>
<body>
    <h1>我的课程</h1>
    
    <!-- 用户统计 -->
    <div id="userStats" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;"></div>
    
    <!-- 筛选器 -->
    <div style="margin-bottom: 20px;">
        <select id="statusFilter">
            <option value="active">有效课程</option>
            <option value="expired">过期课程</option>
            <option value="revoked">撤销课程</option>
            <option value="all">全部课程</option>
        </select>
        <input type="text" id="searchInput" placeholder="搜索课程..." style="margin-left: 10px; padding: 8px;">
        <button onclick="loadUserCourses()" class="btn btn-secondary">刷新</button>
    </div>
    
    <!-- 课程列表 -->
    <div id="courseGrid" class="course-grid"></div>
    
    <!-- 分页 -->
    <div id="pagination" style="text-align: center; margin-top: 20px;"></div>
    
    <!-- 视频播放模态框 -->
    <div id="videoModal" class="modal">
        <div class="modal-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2 id="videoTitle"></h2>
                <button onclick="closeVideo()" class="btn btn-secondary">关闭</button>
            </div>
            <div class="video-player">
                <video id="videoPlayer" controls width="100%" style="border-radius: 8px;">
                    您的浏览器不支持视频播放。
                </video>
            </div>
            <div id="videoDescription" style="margin-top: 15px; color: #666;"></div>
        </div>
    </div>

    <script>
        const userId = 123; // 示例用户ID
        let currentPage = 1;
        let currentStatus = 'active';
        let currentSearch = '';
        
        // 加载用户课程
        async function loadUserCourses(page = 1) {
            try {
                currentPage = page;
                currentStatus = document.getElementById('statusFilter').value;
                currentSearch = document.getElementById('searchInput').value;
                
                let url = `https://wx.yx420.cn/api/userCourses.php?user_id=${userId}&page=${page}&limit=12&status=${currentStatus}`;
                if (currentSearch) url += `&search=${encodeURIComponent(currentSearch)}`;
                
                const response = await fetch(url);
                const result = await response.json();
                
                if (result.code === 200) {
                    displayUserStats(result.data.statistics);
                    displayCourses(result.data.list);
                    displayPagination(result.data.pagination);
                } else {
                    console.error('获取课程失败:', result.message);
                }
            } catch (error) {
                console.error('网络错误:', error);
            }
        }
        
        // 显示用户统计
        function displayUserStats(stats) {
            const container = document.getElementById('userStats');
            container.innerHTML = `
                <h3>学习统计</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 10px;">
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #007bff;">${stats.total_courses}</div>
                        <div style="font-size: 12px; color: #666;">总课程数</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #28a745;">${stats.active_courses}</div>
                        <div style="font-size: 12px; color: #666;">有效课程</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #ffc107;">${stats.expired_courses}</div>
                        <div style="font-size: 12px; color: #666;">过期课程</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #dc3545;">${stats.revoked_courses}</div>
                        <div style="font-size: 12px; color: #666;">撤销课程</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #6f42c1;">${stats.average_progress}%</div>
                        <div style="font-size: 12px; color: #666;">平均进度</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #17a2b8;">${stats.total_watch_count}</div>
                        <div style="font-size: 12px; color: #666;">总观看次数</div>
                    </div>
                </div>
            `;
        }
        
        // 显示课程列表
        function displayCourses(courses) {
            const container = document.getElementById('courseGrid');
            container.innerHTML = '';
            
            if (courses.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; grid-column: 1 / -1;">暂无课程</p>';
                return;
            }
            
            courses.forEach(course => {
                const card = document.createElement('div');
                card.className = 'course-card';
                
                const statusColor = getStatusColor(course.user_info.status, course.user_info.is_expired);
                const statusText = getStatusText(course.user_info.status, course.user_info.is_expired);
                
                card.innerHTML = `
                    ${course.thumbnail ? `<img src="${course.thumbnail}" alt="${course.title}" class="course-thumbnail">` : '<div class="course-thumbnail" style="background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #999;">暂无封面</div>'}
                    <div class="course-content">
                        <div class="course-title">${course.title}</div>
                        <div class="course-description">${course.description || '暂无描述'}</div>
                        
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${course.user_info.watch_progress}%"></div>
                        </div>
                        <div style="font-size: 12px; color: #666;">
                            进度: ${course.user_info.watch_progress.toFixed(1)}% | 观看 ${course.user_info.watch_count} 次
                        </div>
                        
                        <div class="course-meta">
                            <span>时长: ${course.duration_formatted || '未知'}</span>
                            <span style="color: ${statusColor};">${statusText}</span>
                        </div>
                        
                        <div style="margin-top: 10px;">
                            ${course.user_info.has_access ? 
                                `<button onclick="playVideo(${course.id}, '${course.title}', '${course.video_url}', '${course.description || ''}')" class="btn btn-primary">观看课程</button>` :
                                `<button class="btn btn-secondary" disabled>无法观看</button>`
                            }
                        </div>
                        
                        <div style="font-size: 11px; color: #999; margin-top: 5px;">
                            分配时间: ${new Date(course.user_info.assigned_at).toLocaleString()}
                            ${course.user_info.expires_at ? `<br>过期时间: ${new Date(course.user_info.expires_at).toLocaleString()}` : ''}
                        </div>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }
        
        // 显示分页
        function displayPagination(pagination) {
            const container = document.getElementById('pagination');
            container.innerHTML = '';
            
            if (pagination.pages <= 1) return;
            
            for (let i = 1; i <= pagination.pages; i++) {
                const button = document.createElement('button');
                button.textContent = i;
                button.className = i === pagination.page ? 'btn btn-primary' : 'btn btn-secondary';
                button.style.margin = '0 2px';
                button.onclick = () => loadUserCourses(i);
                container.appendChild(button);
            }
        }
        
        // 播放视频
        function playVideo(courseId, title, videoUrl, description) {
            document.getElementById('videoTitle').textContent = title;
            document.getElementById('videoDescription').textContent = description;
            
            const player = document.getElementById('videoPlayer');
            player.src = videoUrl;
            
            document.getElementById('videoModal').style.display = 'block';
            
            // 监听播放进度
            player.addEventListener('timeupdate', () => updateProgress(courseId, player));
        }
        
        // 关闭视频
        function closeVideo() {
            const player = document.getElementById('videoPlayer');
            player.pause();
            player.src = '';
            document.getElementById('videoModal').style.display = 'none';
        }
        
        // 更新观看进度
        async function updateProgress(courseId, player) {
            if (player.duration && player.currentTime) {
                const progress = (player.currentTime / player.duration) * 100;
                
                try {
                    await fetch('https://wx.yx420.cn/api/courseDetail.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            user_id: userId,
                            course_id: courseId,
                            watch_time: Math.floor(player.currentTime),
                            progress_position: Math.floor(player.currentTime),
                            watch_progress: progress
                        })
                    });
                } catch (error) {
                    console.error('更新进度失败:', error);
                }
            }
        }
        
        // 获取状态颜色
        function getStatusColor(status, isExpired) {
            if (status === 'active' && !isExpired) return '#28a745';
            if (status === 'active' && isExpired) return '#ffc107';
            if (status === 'expired') return '#ffc107';
            if (status === 'revoked') return '#dc3545';
            return '#6c757d';
        }
        
        // 获取状态文本
        function getStatusText(status, isExpired) {
            if (status === 'active' && !isExpired) return '有效';
            if (status === 'active' && isExpired) return '已过期';
            if (status === 'expired') return '过期';
            if (status === 'revoked') return '撤销';
            return '未知';
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadUserCourses();
        });
        
        // 筛选变化时重新加载
        document.getElementById('statusFilter').addEventListener('change', () => loadUserCourses(1));
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') loadUserCourses(1);
        });
        
        // 点击模态框外部关闭
        document.getElementById('videoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeVideo();
            }
        });
    </script>
</body>
</html>
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 405 | 请求方法不支持 |
| 500 | 服务器内部错误 |

## 数据字段说明

### 课程字段 (courses)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 课程ID |
| title | string | 课程标题 |
| description | text | 课程描述 |
| video_url | string | 视频链接 |
| thumbnail | string | 缩略图链接 |
| duration | int | 视频时长（秒） |
| duration_formatted | string | 格式化时长（HH:mm:ss） |
| status | enum | 状态：active(启用)/inactive(禁用) |
| sort_order | int | 排序 |
| created_by | int | 创建者ID |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

### 用户课程字段 (user_courses)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 记录ID |
| user_id | int | 用户ID |
| course_id | int | 课程ID |
| assigned_by | int | 分配者ID |
| assigned_at | timestamp | 分配时间 |
| expires_at | datetime | 过期时间 |
| status | enum | 状态：active(有效)/expired(过期)/revoked(撤销) |
| last_watched_at | timestamp | 最后观看时间 |
| watch_progress | decimal | 观看进度百分比（0-100） |
| watch_count | int | 观看次数 |
