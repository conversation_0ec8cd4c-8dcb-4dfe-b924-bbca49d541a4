<?php
/**
 * 服务器状态检查脚本
 * 用于检查API服务器的运行状态和数据库连接
 */

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

$status = [];
$overall_status = 'success';

// 检查PHP基本信息
$status['php'] = [
    'version' => PHP_VERSION,
    'status' => 'running',
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time')
];

// 检查数据库连接
$db_status = [
    'status' => 'unknown',
    'message' => '',
    'connection_time' => 0
];

$start_time = microtime(true);
try {
    // 包含数据库配置文件
    $db_config_file = dirname(__FILE__) . '/../includes/db.php';
    if (file_exists($db_config_file)) {
        include $db_config_file;
        
        // 检查数据库连接
        if (isset($conn) && $conn instanceof mysqli) {
            if ($conn->connect_error) {
                $db_status['status'] = 'error';
                $db_status['message'] = '数据库连接失败: ' . $conn->connect_error;
                $overall_status = 'error';
            } else {
                $db_status['status'] = 'connected';
                $db_status['message'] = '数据库连接正常';
                $db_status['connection_time'] = round((microtime(true) - $start_time) * 1000, 2);
                
                // 测试简单查询
                $result = $conn->query("SELECT 1");
                if ($result) {
                    $db_status['query_test'] = 'success';
                } else {
                    $db_status['query_test'] = 'failed';
                    $db_status['message'] .= ' (查询测试失败)';
                }
            }
        } else {
            $db_status['status'] = 'error';
            $db_status['message'] = '数据库连接对象未正确初始化';
            $overall_status = 'error';
        }
    } else {
        $db_status['status'] = 'error';
        $db_status['message'] = '数据库配置文件不存在: ' . $db_config_file;
        $overall_status = 'error';
    }
} catch (Exception $e) {
    $db_status['status'] = 'error';
    $db_status['message'] = '数据库检查异常: ' . $e->getMessage();
    $overall_status = 'error';
}

$status['database'] = $db_status;

// 检查重要目录和文件
$files_status = [];
$important_files = [
    'auth.php' => dirname(__FILE__) . '/auth.php',
    'courseList.php' => dirname(__FILE__) . '/courseList.php',
    'db.php' => dirname(__FILE__) . '/../includes/db.php'
];

foreach ($important_files as $name => $path) {
    $files_status[$name] = [
        'exists' => file_exists($path),
        'readable' => is_readable($path),
        'path' => $path
    ];
    
    if (!$files_status[$name]['exists'] || !$files_status[$name]['readable']) {
        $overall_status = 'warning';
    }
}

$status['files'] = $files_status;

// 检查服务器环境
$status['server'] = [
    'software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'php_sapi' => php_sapi_name(),
    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
    'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
    'timestamp' => date('Y-m-d H:i:s')
];

// 返回结果
$response = [
    'code' => $overall_status === 'success' ? 200 : ($overall_status === 'warning' ? 206 : 500),
    'message' => $overall_status === 'success' ? '服务器状态正常' : ($overall_status === 'warning' ? '服务器状态警告' : '服务器状态异常'),
    'data' => [
        'overall_status' => $overall_status,
        'status' => $status,
        'timestamp' => date('Y-m-d H:i:s'),
        'response_time' => round((microtime(true) - $start_time) * 1000, 2) . 'ms'
    ]
];

echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>