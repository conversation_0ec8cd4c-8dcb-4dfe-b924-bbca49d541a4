<?php
/**
 * 获取用户openid接口
 * 用于支付时获取当前用户的openid
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db.php';
require_once '../includes/auth.php';

// 验证用户身份
$user = authenticate_user();
if (!$user) {
    http_response_code(401);
    echo json_encode([
        'code' => 401,
        'message' => '未授权访问',
        'data' => null
    ]);
    exit;
}

try {
    $user_id = $user['id'];
    
    // 查询用户的openid
    $stmt = $conn->prepare("
        SELECT openid, unionid, session_key 
        FROM wechat_users 
        WHERE user_id = ?
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $wechat_user = $stmt->get_result()->fetch_assoc();
    
    if (!$wechat_user || empty($wechat_user['openid'])) {
        echo json_encode([
            'code' => 404,
            'message' => '未找到微信用户信息，请重新登录',
            'data' => null,
            'user_message' => '获取用户信息失败，请退出重新登录后再试'
        ]);
        exit;
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '获取openid成功',
        'data' => [
            'openid' => $wechat_user['openid'],
            'has_unionid' => !empty($wechat_user['unionid'])
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '服务器错误',
        'data' => null
    ]);
}
?>