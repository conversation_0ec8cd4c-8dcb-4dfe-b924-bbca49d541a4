<?php
/**
 * 微信支付配置设置脚本
 * 用于设置微信支付的基本配置
 */

header('Content-Type: text/html; charset=utf-8');
require_once '../includes/db.php';

echo "<h1>微信支付配置设置</h1>";

try {
    // 检查settings表是否存在
    $result = $conn->query("SHOW TABLES LIKE 'settings'");
    if ($result->num_rows == 0) {
        echo "<p>❌ settings表不存在，请先运行数据库初始化脚本</p>";
        exit;
    }
    
    // 微信支付配置项
    $wechat_pay_settings = [
        'wechat_pay_enabled' => [
            'value' => '0', // 默认禁用，需要手动启用
            'type' => 'boolean',
            'description' => '是否启用微信支付'
        ],
        'wechat_pay_app_id' => [
            'value' => 'wxa936b5c9fa9b1893', // 从config.js中获取的appid
            'type' => 'string',
            'description' => '微信小程序AppID'
        ],
        'wechat_pay_mch_id' => [
            'value' => '', // 需要填写真实的商户号
            'type' => 'string',
            'description' => '微信支付商户号'
        ],
        'wechat_pay_api_key' => [
            'value' => '', // 需要填写真实的API密钥
            'type' => 'string',
            'description' => '微信支付API密钥'
        ],
        'wechat_pay_notify_url' => [
            'value' => 'https://wx.yx420.cn/api/payment-wechat-notify.php',
            'type' => 'string',
            'description' => '微信支付回调通知URL'
        ],
        'wechat_pay_cert_path' => [
            'value' => '',
            'type' => 'string',
            'description' => '微信支付证书路径'
        ],
        'wechat_pay_key_path' => [
            'value' => '',
            'type' => 'string',
            'description' => '微信支付私钥路径'
        ]
    ];
    
    echo "<h2>设置微信支付配置</h2>";
    
    foreach ($wechat_pay_settings as $key => $config) {
        // 检查配置是否已存在
        $check_stmt = $conn->prepare("SELECT id, setting_value FROM settings WHERE setting_key = ?");
        $check_stmt->bind_param("s", $key);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        
        if ($result->num_rows > 0) {
            $existing = $result->fetch_assoc();
            echo "<p>✅ 配置已存在: $key = " . $existing['setting_value'] . "</p>";
        } else {
            // 插入新配置
            $insert_stmt = $conn->prepare("
                INSERT INTO settings (setting_key, setting_value, setting_type, description) 
                VALUES (?, ?, ?, ?)
            ");
            $insert_stmt->bind_param("ssss", $key, $config['value'], $config['type'], $config['description']);
            
            if ($insert_stmt->execute()) {
                echo "<p>✅ 添加配置: $key = " . $config['value'] . "</p>";
            } else {
                echo "<p>❌ 添加配置失败: $key - " . $conn->error . "</p>";
            }
        }
    }
    
    echo "<h2>当前微信支付配置</h2>";
    
    $stmt = $conn->prepare("
        SELECT setting_key, setting_value, description 
        FROM settings 
        WHERE setting_key LIKE 'wechat_pay_%' 
        ORDER BY setting_key
    ");
    $stmt->execute();
    $settings = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    if (!empty($settings)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>配置项</th><th>当前值</th><th>说明</th></tr>";
        foreach ($settings as $setting) {
            $value = $setting['setting_value'];
            if (empty($value)) {
                $value = "<span style='color: red;'>未设置</span>";
            } elseif (strpos($setting['setting_key'], 'api_key') !== false) {
                $value = str_repeat('*', strlen($value)); // 隐藏API密钥
            }
            
            echo "<tr>";
            echo "<td>{$setting['setting_key']}</td>";
            echo "<td>$value</td>";
            echo "<td>{$setting['description']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>配置说明</h2>";
    echo "<div style='background: #f5f5f5; padding: 15px; margin: 10px 0;'>";
    echo "<h3>⚠️ 重要提示</h3>";
    echo "<p><strong>当前微信支付配置不完整，这就是为什么出现 'appid和mch_id不匹配' 错误的原因。</strong></p>";
    echo "<p>要启用微信支付功能，您需要：</p>";
    echo "<ol>";
    echo "<li><strong>获取微信支付商户号</strong>：在微信支付商户平台申请</li>";
    echo "<li><strong>获取API密钥</strong>：在微信支付商户平台设置</li>";
    echo "<li><strong>配置回调URL</strong>：确保服务器可以接收微信支付回调</li>";
    echo "<li><strong>更新数据库配置</strong>：将真实的商户号和API密钥填入settings表</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>临时解决方案</h2>";
    echo "<div style='background: #e8f5e8; padding: 15px; margin: 10px 0;'>";
    echo "<p>如果您暂时不需要真实的微信支付功能，可以：</p>";
    echo "<ol>";
    echo "<li><strong>禁用微信支付</strong>：将 wechat_pay_enabled 设置为 0</li>";
    echo "<li><strong>使用模拟支付</strong>：修改支付逻辑直接标记订单为已支付</li>";
    echo "<li><strong>测试其他功能</strong>：先测试订单创建等其他功能</li>";
    echo "</ol>";
    echo "</div>";
    
    // 提供快速禁用微信支付的选项
    if (isset($_GET['disable_wechat_pay'])) {
        $update_stmt = $conn->prepare("UPDATE settings SET setting_value = '0' WHERE setting_key = 'wechat_pay_enabled'");
        if ($update_stmt->execute()) {
            echo "<p style='color: green;'>✅ 已禁用微信支付</p>";
        } else {
            echo "<p style='color: red;'>❌ 禁用微信支付失败</p>";
        }
    }
    
    echo "<h2>快速操作</h2>";
    echo "<p><a href='?disable_wechat_pay=1' style='background: #ff6b6b; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>禁用微信支付</a></p>";
    echo "<p><small>点击上面的按钮将禁用微信支付功能，这样可以避免支付配置错误</small></p>";
    
    echo "<h2>数据库配置更新SQL</h2>";
    echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0;'>";
    echo "<p>如果您有真实的微信支付配置，可以使用以下SQL更新：</p>";
    echo "<pre>";
    echo "-- 更新微信支付商户号\n";
    echo "UPDATE settings SET setting_value = '您的商户号' WHERE setting_key = 'wechat_pay_mch_id';\n\n";
    echo "-- 更新微信支付API密钥\n";
    echo "UPDATE settings SET setting_value = '您的API密钥' WHERE setting_key = 'wechat_pay_api_key';\n\n";
    echo "-- 启用微信支付\n";
    echo "UPDATE settings SET setting_value = '1' WHERE setting_key = 'wechat_pay_enabled';\n";
    echo "</pre>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p>❌ 配置过程中出现错误: " . $e->getMessage() . "</p>";
}
?>
