<?php
/**
 * 腾讯云点播签名生成类
 * 用于生成上传视频所需的签名
 */

require_once 'vod_config.php';

class VodSignature {
    
    /**
     * 生成客户端上传签名
     * 按照腾讯云VOD官方文档标准实现
     * @param array $params 签名参数
     * @return array 签名结果
     */
    public static function generateUploadSignature($params = []) {
        try {
            // 验证配置
            $configValidation = VodConfig::validateConfig();
            if (!$configValidation['valid']) {
                return [
                    'success' => false,
                    'message' => '配置错误: ' . implode(', ', $configValidation['errors'])
                ];
            }

            $config = VodConfig::getConfig();

            // 当前时间戳
            $currentTimeStamp = time();
            $expireTime = $currentTimeStamp + $config['signature_expire_time'];
            $random = rand(100000, 999999);

            // 构建签名参数 - 按照腾讯云VOD官方示例
            $arg_list = [
                'secretId' => $config['secret_id'],  // 注意：这里是小写的secretId
                'currentTimeStamp' => $currentTimeStamp,
                'expireTime' => $expireTime,
                'random' => $random
            ];

            // 添加可选参数
            if (!empty($params['procedure'])) {
                $arg_list['procedure'] = $params['procedure'];
            } elseif (!empty($config['procedure'])) {
                $arg_list['procedure'] = $config['procedure'];
            }

            if (isset($params['classId'])) {
                $arg_list['classId'] = intval($params['classId']);
            } elseif ($config['class_id'] > 0) {
                $arg_list['classId'] = $config['class_id'];
            }

            if (!empty($params['sourceContext'])) {
                $arg_list['sourceContext'] = $params['sourceContext'];
            }

            if ($config['sub_app_id'] > 0) {
                $arg_list['vodSubAppId'] = $config['sub_app_id'];
            }

            // 按照腾讯云官方示例生成签名
            // 1. 构建原始字符串
            $original = http_build_query($arg_list);

            // 2. 生成签名 - 按照官方格式：base64_encode(hash_hmac + original)
            $signature = base64_encode(hash_hmac('SHA1', $original, $config['secret_key'], true) . $original);

            return [
                'success' => true,
                'data' => [
                    'signature' => $signature,
                    'expire_time' => $expireTime,
                    'current_time' => $currentTimeStamp,
                    'original_string' => $original // 用于调试
                ],
                'message' => '签名生成成功'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '签名生成失败: ' . $e->getMessage()
            ];
        }
    }
    

    
    /**
     * 生成客户端上传签名（简化版）
     * @param array $options 选项
     * @return array 签名结果
     */
    public static function generateClientUploadSignature($options = []) {
        $params = [];
        
        // 设置来源上下文，用于标识上传来源
        if (!empty($options['lesson_id'])) {
            $params['sourceContext'] = json_encode([
                'type' => 'lesson',
                'lesson_id' => $options['lesson_id'],
                'upload_time' => time()
            ]);
        }
        
        // 设置分类ID
        if (!empty($options['class_id'])) {
            $params['classId'] = $options['class_id'];
        }
        
        // 设置任务流
        if (!empty($options['procedure'])) {
            $params['procedure'] = $options['procedure'];
        }
        
        return self::generateUploadSignature($params);
    }
    
    /**
     * 验证签名是否有效
     * @param string $signature 签名
     * @param int $expireTime 过期时间
     * @return bool 是否有效
     */
    public static function validateSignature($signature, $expireTime) {
        // 检查签名格式
        if (empty($signature) || !is_string($signature)) {
            return false;
        }
        
        // 检查是否过期
        if ($expireTime < time()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取上传配置信息
     * @return array 配置信息
     */
    public static function getUploadConfig() {
        $config = VodConfig::getConfig();
        
        return [
            'region' => $config['upload_region'],
            'allowed_video_types' => $config['allowed_video_types'],
            'allowed_image_types' => $config['allowed_image_types'],
            'max_video_size' => $config['max_video_size'],
            'max_image_size' => $config['max_image_size'],
            'signature_expire_time' => $config['signature_expire_time']
        ];
    }
}

/**
 * 快速生成上传签名的函数
 * @param array $options 选项
 * @return array 签名结果
 */
function generateVodUploadSignature($options = []) {
    return VodSignature::generateClientUploadSignature($options);
}
?>
