<?php
session_start();
require_once '../includes/config.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '未登录']);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
    exit;
}

// 检查是否有文件上传
if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => '没有文件上传或上传失败']);
    exit;
}

$file = $_FILES['image'];

// 验证文件类型
$allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
$finfo = finfo_open(FILEINFO_MIME_TYPE);
$mime_type = finfo_file($finfo, $file['tmp_name']);
finfo_close($finfo);

if (!in_array($mime_type, $allowed_types)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => '不支持的文件类型，只支持 JPG、PNG、GIF、WebP 格式']);
    exit;
}

// 验证文件大小（限制5MB）
$max_size = 5 * 1024 * 1024; // 5MB
if ($file['size'] > $max_size) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => '图片大小不能超过5MB']);
    exit;
}

// 创建上传目录
$upload_dir = dirname(__DIR__) . '/uploads/lesson_thumbnails/' . date('Y/m');
if (!is_dir($upload_dir)) {
    if (!mkdir($upload_dir, 0755, true)) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => '创建上传目录失败']);
        exit;
    }
}

// 生成文件名
$file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
if (empty($file_extension)) {
    // 根据MIME类型确定扩展名
    $mime_to_ext = [
        'image/jpeg' => 'jpg',
        'image/png' => 'png',
        'image/gif' => 'gif',
        'image/webp' => 'webp'
    ];
    $file_extension = $mime_to_ext[$mime_type] ?? 'jpg';
}

$file_name = 'lesson_' . time() . '_' . uniqid() . '.' . $file_extension;
$file_path = $upload_dir . '/' . $file_name;

// 移动上传文件
if (!move_uploaded_file($file['tmp_name'], $file_path)) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '文件保存失败']);
    exit;
}

// 生成完整的网络地址URL（关键修复）
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$relative_url = '/uploads/lesson_thumbnails/' . date('Y/m') . '/' . $file_name;
$full_url = $protocol . '://' . $host . $relative_url;

// 记录上传日志
function logLessonThumbnailUpload($admin_id, $file_name, $file_size, $file_url) {
    global $conn;
    try {
        $stmt = $conn->prepare("INSERT INTO upload_logs (admin_id, file_name, file_size, file_url, upload_type, created_at) VALUES (?, ?, ?, ?, 'lesson_thumbnail', NOW())");
        $stmt->bind_param("isis", $admin_id, $file_name, $file_size, $file_url);
        $stmt->execute();
    } catch (Exception $e) {
        error_log("记录课时缩略图上传日志失败: " . $e->getMessage());
    }
}

logLessonThumbnailUpload($_SESSION['admin_id'], $file_name, $file['size'], $full_url);

echo json_encode([
    'success' => true,
    'message' => '图片上传成功',
    'data' => [
        'file_name' => $file_name,
        'file_url' => $full_url,  // 返回完整的网络地址
        'relative_url' => $relative_url,  // 同时提供相对路径供参考
        'file_size' => $file['size'],
        'uploaded_at' => date('Y-m-d H:i:s')
    ]
]);
?>
