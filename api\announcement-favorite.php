<?php
/**
 * 公告收藏API
 * 支持JWT认证，处理公告收藏和取消收藏功能
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    // 验证用户认证
    $user = $auth->requireAuth();
    
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'POST':
            addFavorite($auth, $user);
            break;
        case 'DELETE':
            removeFavorite($auth, $user);
            break;
        case 'GET':
            getFavoriteStatus($auth, $user);
            break;
        default:
            $auth->jsonResponse(405, '不支持的请求方法');
    }
} catch (Exception $e) {
    error_log('公告收藏API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 添加收藏
 */
function addFavorite($auth, $user) {
    $conn = $auth->getConn();
    
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    $announcement_id = $input['announcement_id'] ?? 0;
    $user_id = $user['id'];
    
    if (!$announcement_id) {
        $auth->jsonResponse(400, '缺少公告ID');
    }
    
    // 检查公告是否存在且已发布
    $check_stmt = $conn->prepare("SELECT id FROM announcements WHERE id = ? AND status = 'published'");
    $check_stmt->bind_param("i", $announcement_id);
    $check_stmt->execute();
    $result = $check_stmt->get_result();
    
    if ($result->num_rows === 0) {
        $auth->jsonResponse(404, '公告不存在或未发布');
    }
    
    // 检查是否已经收藏
    $favorite_check = $conn->prepare("SELECT id FROM announcement_favorites WHERE user_id = ? AND announcement_id = ?");
    $favorite_check->bind_param("ii", $user_id, $announcement_id);
    $favorite_check->execute();
    $favorite_result = $favorite_check->get_result();
    
    if ($favorite_result->num_rows > 0) {
        $auth->jsonResponse(400, '已经收藏过此公告');
    }
    
    // 添加收藏
    $stmt = $conn->prepare("INSERT INTO announcement_favorites (user_id, announcement_id) VALUES (?, ?)");
    $stmt->bind_param("ii", $user_id, $announcement_id);
    
    if ($stmt->execute()) {
        $auth->jsonResponse(200, '收藏成功', [
            'favorite_id' => $conn->insert_id,
            'user_id' => $user_id,
            'announcement_id' => $announcement_id,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    } else {
        error_log('添加收藏失败: ' . $conn->error);
        $auth->jsonResponse(500, '收藏失败');
    }
}

/**
 * 取消收藏
 */
function removeFavorite($auth, $user) {
    $conn = $auth->getConn();
    
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    $announcement_id = $input['announcement_id'] ?? 0;
    $user_id = $user['id'];
    
    if (!$announcement_id) {
        $auth->jsonResponse(400, '缺少公告ID');
    }
    
    // 检查收藏是否存在
    $check_stmt = $conn->prepare("SELECT id FROM announcement_favorites WHERE user_id = ? AND announcement_id = ?");
    $check_stmt->bind_param("ii", $user_id, $announcement_id);
    $check_stmt->execute();
    $result = $check_stmt->get_result();
    
    if ($result->num_rows === 0) {
        $auth->jsonResponse(404, '未收藏此公告');
    }
    
    // 删除收藏
    $stmt = $conn->prepare("DELETE FROM announcement_favorites WHERE user_id = ? AND announcement_id = ?");
    $stmt->bind_param("ii", $user_id, $announcement_id);
    
    if ($stmt->execute()) {
        $auth->jsonResponse(200, '取消收藏成功', [
            'user_id' => $user_id,
            'announcement_id' => $announcement_id
        ]);
    } else {
        error_log('取消收藏失败: ' . $conn->error);
        $auth->jsonResponse(500, '取消收藏失败');
    }
}

/**
 * 获取收藏状态
 */
function getFavoriteStatus($auth, $user) {
    $conn = $auth->getConn();
    
    $announcement_id = $_GET['announcement_id'] ?? 0;
    $user_id = $user['id'];
    
    if (!$announcement_id) {
        $auth->jsonResponse(400, '缺少公告ID');
    }
    
    // 检查收藏状态
    $stmt = $conn->prepare("SELECT id, created_at FROM announcement_favorites WHERE user_id = ? AND announcement_id = ?");
    $stmt->bind_param("ii", $user_id, $announcement_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $is_favorited = $result->num_rows > 0;
    $favorite_data = null;
    
    if ($is_favorited) {
        $favorite_data = $result->fetch_assoc();
    }
    
    $auth->jsonResponse(200, '获取收藏状态成功', [
        'is_favorited' => $is_favorited,
        'announcement_id' => $announcement_id,
        'user_id' => $user_id,
        'favorite_data' => $favorite_data
    ]);
}
?>
