<template>
	<view class="announcements-container">
		<!-- 搜索栏 -->
		<view class="search-section">
			<uni-search-bar 
				v-model="searchKeyword" 
				placeholder="搜索公告..."
				@confirm="handleSearch"
				@clear="handleSearchClear"
				:focus="false"
				cancelButton="none"
			></uni-search-bar>
		</view>
		
		<!-- 筛选栏 -->
		<view class="filter-section">
			<scroll-view class="filter-scroll" scroll-x>
				<view class="filter-list">
					<view 
						class="filter-item" 
						:class="{ active: currentType === type.value }"
						v-for="type in typeOptions" 
						:key="type.value"
						@click="handleTypeFilter(type.value)"
					>
						<text class="filter-text">{{ type.label }}</text>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 置顶公告 -->
		<view class="pinned-section" v-if="pinnedAnnouncements.length > 0">
			<view class="section-header">
				<simple-icon type="notification" size="18" color="#007bff" class="section-icon"></simple-icon>
				<text class="section-title">置顶公告</text>
			</view>
			<view class="pinned-list">
				<view
					class="pinned-item"
					v-for="announcement in pinnedAnnouncements"
					:key="announcement.id"
					@click="goToAnnouncementDetail(announcement.id)"
				>
					<view class="pinned-content">
						<simple-icon type="paperclip" size="14" color="#ff6b6b" class="pinned-icon"></simple-icon>
						<text class="pinned-title">{{ announcement.title }}</text>
					</view>
					<uni-tag 
						:text="getTypeText(announcement.type)" 
						:type="getTypeColor(announcement.type)"
						size="mini"
					></uni-tag>
				</view>
			</view>
		</view>
		
		<!-- 公告列表 -->
		<view class="announcement-list">
			<view 
				class="announcement-item" 
				v-for="announcement in announcementList" 
				:key="announcement.id"
				@click="goToAnnouncementDetail(announcement.id)"
			>
				<view class="announcement-header">
					<view class="title-section">
						<text class="announcement-title">{{ announcement.title }}</text>
						<view class="announcement-tags">
							<uni-tag 
								:text="getTypeText(announcement.type)" 
								:type="getTypeColor(announcement.type)"
								size="mini"
							></uni-tag>
							<uni-tag 
								v-if="announcement.priority > 0"
								:text="getPriorityText(announcement.priority)" 
								type="error"
								size="mini"
							></uni-tag>
						</view>
					</view>
				</view>
				
				<text class="announcement-content">{{ announcement.content }}</text>
				
				<view class="announcement-footer">
					<view class="footer-left">
						<text class="announcement-time">{{ formatDate(announcement.publish_time) }}</text>
						<text class="announcement-author" v-if="announcement.author_name">
							{{ announcement.author_name }}
						</text>
					</view>
					<view class="footer-right">
						<view class="view-count">
							<simple-icon type="eye" size="12" color="#999" class="view-icon"></simple-icon>
							<text class="count-text">{{ announcement.view_count || 0 }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="!loading && announcementList.length === 0 && pinnedAnnouncements.length === 0">
			<simple-icon type="notification" size="40" color="#999" class="empty-icon"></simple-icon>
			<text class="empty-text">{{ getEmptyText() }}</text>
		</view>
		
		<!-- 加载更多 -->
		<view class="load-more" v-if="announcementList.length > 0">
			<uni-load-more 
				:status="loadMoreStatus" 
				:content-text="loadMoreText"
				@clickLoadMore="loadMoreAnnouncements"
			></uni-load-more>
		</view>
	</view>
</template>

<script>
	import { formatDate } from '../../utils/storage.js';
	import { getAnnouncementList, getPinnedAnnouncements } from '../../api/announcement.js';
	import CONFIG from '../../utils/config.js';
	import SimpleIcon from '../../components/simple-icon/simple-icon.vue';

	export default {
		components: {
			SimpleIcon
		},
		data() {
			return {
				loading: false,
				searchKeyword: '',
				currentType: '',
				currentPage: 1,
				hasMore: true,
				announcementList: [],
				pinnedAnnouncements: [],
				loadMoreStatus: 'more',
				loadMoreText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多数据了'
				},
				
				// 类型筛选选项
				typeOptions: [
					{ label: '全部', value: '' },
					{ label: '通知', value: 'notice' },
					{ label: '紧急', value: 'urgent' },
					{ label: '系统', value: 'system' },
					{ label: '活动', value: 'activity' }
				]
			};
		},
		
		onLoad() {
			this.initPage();
		},
		
		onShow() {
			this.loadAnnouncementData();
		},
		
		onPullDownRefresh() {
			this.refreshAnnouncementData().finally(() => {
				uni.stopPullDownRefresh();
			});
		},
		
		onReachBottom() {
			if (this.hasMore && this.loadMoreStatus === 'more') {
				this.loadMoreAnnouncements();
			}
		},
		
		methods: {
			/**
			 * 初始化页面
			 */
			initPage() {
				this.loadAnnouncementData();
				this.loadPinnedAnnouncements();
			},
			
			/**
			 * 加载公告数据
			 */
			async loadAnnouncementData() {
				this.loading = true;
				this.currentPage = 1;
				this.hasMore = true;
				this.loadMoreStatus = 'more';
				
				try {
					const response = await this.fetchAnnouncements();
					if (response.code === 200) {
						this.announcementList = response.data.list || [];
						
						// 检查是否还有更多数据
						const pagination = response.data.pagination;
						if (pagination) {
							this.hasMore = pagination.page < pagination.pages;
							this.loadMoreStatus = this.hasMore ? 'more' : 'noMore';
						}
					}
				} catch (error) {
					console.error('加载公告数据失败:', error);
					uni.showToast({
						title: error.message || '加载失败',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			},
			
			/**
			 * 加载置顶公告
			 */
			async loadPinnedAnnouncements() {
				try {
					const response = await getPinnedAnnouncements(5);
					if (response.code === 200) {
						this.pinnedAnnouncements = response.data.list || [];
					}
				} catch (error) {
					console.error('加载置顶公告失败:', error);
				}
			},
			
			/**
			 * 刷新公告数据
			 */
			async refreshAnnouncementData() {
				await Promise.all([
					this.loadAnnouncementData(),
					this.loadPinnedAnnouncements()
				]);
			},
			
			/**
			 * 加载更多公告
			 */
			async loadMoreAnnouncements() {
				if (!this.hasMore || this.loadMoreStatus !== 'more') return;
				
				this.loadMoreStatus = 'loading';
				this.currentPage++;
				
				try {
					const response = await this.fetchAnnouncements();
					if (response.code === 200) {
						const newAnnouncements = response.data.list || [];
						this.announcementList = [...this.announcementList, ...newAnnouncements];
						
						// 检查是否还有更多数据
						const pagination = response.data.pagination;
						if (pagination) {
							this.hasMore = pagination.page < pagination.pages;
							this.loadMoreStatus = this.hasMore ? 'more' : 'noMore';
						}
					}
				} catch (error) {
					console.error('加载更多公告失败:', error);
					this.currentPage--; // 回退页码
					this.loadMoreStatus = 'more';
					uni.showToast({
						title: error.message || '加载失败',
						icon: 'none'
					});
				}
			},
			
			/**
			 * 获取公告数据
			 */
			fetchAnnouncements() {
				const params = {
					page: this.currentPage,
					limit: CONFIG.PAGE_SIZE,
					search: this.searchKeyword,
					type: this.currentType,
					status: 'published'
				};
				
				return getAnnouncementList(params);
			},
			
			/**
			 * 处理搜索
			 */
			handleSearch() {
				this.loadAnnouncementData();
			},
			
			/**
			 * 清除搜索
			 */
			handleSearchClear() {
				this.searchKeyword = '';
				this.loadAnnouncementData();
			},
			
			/**
			 * 类型筛选
			 */
			handleTypeFilter(type) {
				if (this.currentType === type) return;
				
				this.currentType = type;
				this.loadAnnouncementData();
			},
			
			/**
			 * 跳转到公告详情
			 */
			goToAnnouncementDetail(announcementId) {
				uni.navigateTo({
					url: `/pages/announcements/detail?id=${announcementId}`
				});
			},
			
			/**
			 * 获取类型文本
			 */
			getTypeText(type) {
				const typeMap = {
					notice: '通知',
					urgent: '紧急',
					system: '系统',
					activity: '活动'
				};
				return typeMap[type] || '通知';
			},
			
			/**
			 * 获取类型颜色
			 */
			getTypeColor(type) {
				const colorMap = {
					notice: 'primary',
					urgent: 'error',
					system: 'warning',
					activity: 'success'
				};
				return colorMap[type] || 'primary';
			},
			
			/**
			 * 获取优先级文本
			 */
			getPriorityText(priority) {
				const priorityMap = {
					1: '重要',
					2: '紧急'
				};
				return priorityMap[priority] || '';
			},
			
			/**
			 * 获取空状态文本
			 */
			getEmptyText() {
				if (this.searchKeyword) {
					return '没有找到相关公告';
				}
				
				if (this.currentType) {
					return `暂无${this.getTypeText(this.currentType)}公告`;
				}
				
				return '暂无公告';
			},
			
			/**
			 * 格式化日期
			 */
			formatDate(date) {
				return formatDate(date, 'MM-DD HH:mm');
			}
		}
	};
</script>

<style lang="scss" scoped>
.announcements-container {
	background: #f8f9fa;
	min-height: 100vh;
}

.search-section {
	background: #fff;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.filter-section {
	background: #fff;
	border-bottom: 1px solid #f0f0f0;

	.filter-scroll {
		white-space: nowrap;

		.filter-list {
			display: inline-flex;
			padding: 10px 15px;
			gap: 15px;

			.filter-item {
				padding: 8px 16px;
				border-radius: 20px;
				background: #f8f9fa;
				border: 1px solid #e9ecef;

				&.active {
					background: #007bff;
					border-color: #007bff;

					.filter-text {
						color: #fff;
					}
				}

				.filter-text {
					font-size: 14px;
					color: #666;
					white-space: nowrap;
				}
			}
		}
	}
}

.pinned-section {
	background: #fff;
	margin: 10px 15px;
	border-radius: 12px;
	padding: 15px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	border-left: 4px solid #ff6b6b;

	.section-header {
		display: flex;
		align-items: center;
		gap: 8px;
		margin-bottom: 10px;

		.section-icon {
			margin-right: 5px;
		}

		.section-title {
			font-size: 14px;
			font-weight: 500;
			color: #ff6b6b;
		}
	}

	.pinned-list {
		.pinned-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 8px 0;
			border-bottom: 1px solid #f8f9fa;

			&:last-child {
				border-bottom: none;
			}

			.pinned-content {
				display: flex;
				align-items: center;
				gap: 8px;
				flex: 1;

				.pinned-icon {
					margin-right: 5px;
				}

				.pinned-title {
					font-size: 14px;
					color: #333;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}
		}
	}
}

.announcement-list {
	padding: 10px 15px;

	.announcement-item {
		background: #fff;
		border-radius: 12px;
		margin-bottom: 15px;
		padding: 20px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

		.announcement-header {
			margin-bottom: 12px;

			.title-section {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;

				.announcement-title {
					flex: 1;
					font-size: 16px;
					font-weight: 500;
					color: #333;
					line-height: 1.4;
					margin-right: 10px;
				}

				.announcement-tags {
					display: flex;
					gap: 5px;
					flex-shrink: 0;
				}
			}
		}

		.announcement-content {
			font-size: 14px;
			color: #666;
			line-height: 1.5;
			margin-bottom: 15px;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 3;
			-webkit-box-orient: vertical;
		}

		.announcement-footer {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.footer-left {
				display: flex;
				gap: 15px;

				.announcement-time {
					font-size: 12px;
					color: #999;
				}

				.announcement-author {
					font-size: 12px;
					color: #999;
				}
			}

			.footer-right {
				.view-count {
					display: flex;
					align-items: center;
					gap: 4px;

					.view-icon {
						margin-right: 3px;
					}

					.count-text {
						font-size: 12px;
						color: #999;
					}
				}
			}
		}
	}
}

.empty-state {
	text-align: center;
	padding: 60px 20px;

	.empty-icon {
		margin-bottom: 15px;
	}

	.empty-text {
		display: block;
		font-size: 16px;
		color: #999;
		margin: 20px 0;
	}
}

.load-more {
	padding: 20px;
}
</style>
