<template>
	<view class="empty-container" v-if="show">
		<view class="empty-content">
			<!-- 图标 -->
			<view class="empty-icon">
				<uni-icons 
					:type="iconType" 
					:size="iconSize" 
					:color="iconColor"
				></uni-icons>
			</view>
			
			<!-- 主要文本 -->
			<text class="empty-title" v-if="title">{{ title }}</text>
			
			<!-- 描述文本 -->
			<text class="empty-description" v-if="description">{{ description }}</text>
			
			<!-- 操作按钮 -->
			<button 
				class="empty-action" 
				v-if="actionText"
				:class="actionType"
				@click="handleAction"
			>
				{{ actionText }}
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'Empty',
		props: {
			// 是否显示
			show: {
				type: Boolean,
				default: true
			},
			// 图标类型
			iconType: {
				type: String,
				default: 'info'
			},
			// 图标大小
			iconSize: {
				type: [String, Number],
				default: 60
			},
			// 图标颜色
			iconColor: {
				type: String,
				default: '#ccc'
			},
			// 主要文本
			title: {
				type: String,
				default: '暂无数据'
			},
			// 描述文本
			description: {
				type: String,
				default: ''
			},
			// 操作按钮文本
			actionText: {
				type: String,
				default: ''
			},
			// 按钮类型
			actionType: {
				type: String,
				default: 'primary',
				validator: (value) => {
					return ['primary', 'secondary', 'success', 'warning', 'error'].includes(value);
				}
			}
		},
		
		methods: {
			/**
			 * 处理操作按钮点击
			 */
			handleAction() {
				this.$emit('action');
			}
		}
	};
</script>

<style lang="scss" scoped>
.empty-container {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 300px;
	padding: 40px 20px;
}

.empty-content {
	text-align: center;
	max-width: 300px;
}

.empty-icon {
	margin-bottom: 20px;
}

.empty-title {
	display: block;
	font-size: 16px;
	color: #666;
	margin-bottom: 10px;
	line-height: 1.4;
}

.empty-description {
	display: block;
	font-size: 14px;
	color: #999;
	margin-bottom: 30px;
	line-height: 1.5;
}

.empty-action {
	border: none;
	border-radius: 20px;
	padding: 10px 30px;
	font-size: 14px;
	
	&.primary {
		background: #007bff;
		color: #fff;
	}
	
	&.secondary {
		background: #6c757d;
		color: #fff;
	}
	
	&.success {
		background: #28a745;
		color: #fff;
	}
	
	&.warning {
		background: #ffc107;
		color: #212529;
	}
	
	&.error {
		background: #dc3545;
		color: #fff;
	}
}
</style>
