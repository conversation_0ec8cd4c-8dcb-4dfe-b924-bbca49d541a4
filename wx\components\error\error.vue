<template>
	<view class="error-container" v-if="show">
		<view class="error-content">
			<!-- 错误图标 -->
			<view class="error-icon">
				<uni-icons 
					:type="iconType" 
					:size="iconSize" 
					:color="iconColor"
				></uni-icons>
			</view>
			
			<!-- 错误标题 -->
			<text class="error-title" v-if="title">{{ title }}</text>
			
			<!-- 错误描述 -->
			<text class="error-message" v-if="message">{{ message }}</text>
			
			<!-- 错误详情 -->
			<view class="error-details" v-if="showDetails && details">
				<text class="details-toggle" @click="toggleDetails">
					{{ detailsVisible ? '隐藏详情' : '查看详情' }}
				</text>
				<view class="details-content" v-if="detailsVisible">
					<text class="details-text">{{ details }}</text>
				</view>
			</view>
			
			<!-- 操作按钮 -->
			<view class="error-actions" v-if="showRetry || showBack">
				<button 
					class="error-btn retry-btn" 
					v-if="showRetry"
					@click="handleRetry"
				>
					{{ retryText }}
				</button>
				<button 
					class="error-btn back-btn" 
					v-if="showBack"
					@click="handleBack"
				>
					{{ backText }}
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'Error',
		props: {
			// 是否显示
			show: {
				type: Boolean,
				default: true
			},
			// 图标类型
			iconType: {
				type: String,
				default: 'closeempty'
			},
			// 图标大小
			iconSize: {
				type: [String, Number],
				default: 60
			},
			// 图标颜色
			iconColor: {
				type: String,
				default: '#ff6b6b'
			},
			// 错误标题
			title: {
				type: String,
				default: '出错了'
			},
			// 错误消息
			message: {
				type: String,
				default: '请稍后重试'
			},
			// 错误详情
			details: {
				type: String,
				default: ''
			},
			// 是否显示详情
			showDetails: {
				type: Boolean,
				default: false
			},
			// 是否显示重试按钮
			showRetry: {
				type: Boolean,
				default: true
			},
			// 重试按钮文本
			retryText: {
				type: String,
				default: '重试'
			},
			// 是否显示返回按钮
			showBack: {
				type: Boolean,
				default: false
			},
			// 返回按钮文本
			backText: {
				type: String,
				default: '返回'
			}
		},
		
		data() {
			return {
				detailsVisible: false
			};
		},
		
		methods: {
			/**
			 * 切换详情显示
			 */
			toggleDetails() {
				this.detailsVisible = !this.detailsVisible;
			},
			
			/**
			 * 处理重试
			 */
			handleRetry() {
				this.$emit('retry');
			},
			
			/**
			 * 处理返回
			 */
			handleBack() {
				this.$emit('back');
			}
		}
	};
</script>

<style lang="scss" scoped>
.error-container {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 300px;
	padding: 40px 20px;
}

.error-content {
	text-align: center;
	max-width: 300px;
}

.error-icon {
	margin-bottom: 20px;
}

.error-title {
	display: block;
	font-size: 18px;
	color: #333;
	font-weight: 500;
	margin-bottom: 10px;
	line-height: 1.4;
}

.error-message {
	display: block;
	font-size: 14px;
	color: #666;
	margin-bottom: 20px;
	line-height: 1.5;
}

.error-details {
	margin-bottom: 30px;
	
	.details-toggle {
		display: block;
		font-size: 12px;
		color: #007bff;
		margin-bottom: 10px;
		text-decoration: underline;
	}
	
	.details-content {
		background: #f8f9fa;
		border-radius: 8px;
		padding: 15px;
		text-align: left;
		
		.details-text {
			font-size: 12px;
			color: #666;
			line-height: 1.4;
			word-break: break-all;
		}
	}
}

.error-actions {
	display: flex;
	gap: 15px;
	justify-content: center;
	
	.error-btn {
		border: none;
		border-radius: 20px;
		padding: 10px 25px;
		font-size: 14px;
		
		&.retry-btn {
			background: #007bff;
			color: #fff;
		}
		
		&.back-btn {
			background: #6c757d;
			color: #fff;
		}
	}
}
</style>
