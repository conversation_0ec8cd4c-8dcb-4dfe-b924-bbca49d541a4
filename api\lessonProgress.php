<?php
/**
 * 课时观看进度API
 * 记录和获取用户的课时观看进度
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db.php';
require_once '../includes/lesson_db_setup.php';

// 确保数据库表结构正确
$setup_result = setupLessonTables($conn);
if (!$setup_result['success']) {
    echo json_encode([
        'success' => false,
        'message' => '数据库初始化失败',
        'error' => implode(', ', $setup_result['messages'])
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 简单的JWT验证（这里应该使用完整的JWT验证）
function getUserIdFromToken() {
    $headers = getallheaders();
    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : '';
    
    if (empty($token)) {
        return null;
    }
    
    // 这里应该实现完整的JWT验证
    // 暂时使用简单的解析方式
    try {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return null;
        }
        
        $payload = json_decode(base64_decode($parts[1]), true);
        return isset($payload['user_id']) ? intval($payload['user_id']) : null;
    } catch (Exception $e) {
        return null;
    }
}

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'POST') {
        // 记录观看进度
        $input = json_decode(file_get_contents('php://input'), true);
        
        $user_id = getUserIdFromToken();
        if (!$user_id) {
            echo json_encode([
                'success' => false,
                'message' => '用户未登录或token无效'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        $lesson_id = isset($input['lesson_id']) ? intval($input['lesson_id']) : 0;
        $course_id = isset($input['course_id']) ? intval($input['course_id']) : 0;
        $watch_time = isset($input['watch_time']) ? intval($input['watch_time']) : 0;
        $progress_position = isset($input['progress_position']) ? intval($input['progress_position']) : 0;
        
        if ($lesson_id <= 0 || $course_id <= 0) {
            echo json_encode([
                'success' => false,
                'message' => '参数无效'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        // 验证课时是否存在
        $lesson_check_sql = "SELECT id, duration FROM lessons WHERE id = ? AND course_id = ? AND status = 1";
        $lesson_check_stmt = $conn->prepare($lesson_check_sql);
        $lesson_check_stmt->bind_param("ii", $lesson_id, $course_id);
        $lesson_check_stmt->execute();
        $lesson_result = $lesson_check_stmt->get_result();
        $lesson = $lesson_result->fetch_assoc();
        
        if (!$lesson) {
            echo json_encode([
                'success' => false,
                'message' => '课时不存在'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        // 计算完成率
        $completion_rate = 0;
        $is_completed = 0;
        if ($lesson['duration'] > 0) {
            $completion_rate = min(100, ($progress_position / $lesson['duration']) * 100);
            $is_completed = $completion_rate >= 90 ? 1 : 0; // 观看90%以上算完成
        }
        
        // 获取客户端信息
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // 更新或插入观看记录
        $upsert_sql = "INSERT INTO lesson_watch_logs 
                       (user_id, lesson_id, course_id, watch_time, progress_position, is_completed, completion_rate, ip_address, user_agent)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                       ON DUPLICATE KEY UPDATE
                       watch_time = GREATEST(watch_time, VALUES(watch_time)),
                       progress_position = GREATEST(progress_position, VALUES(progress_position)),
                       is_completed = VALUES(is_completed),
                       completion_rate = VALUES(completion_rate),
                       updated_at = CURRENT_TIMESTAMP";
        
        $upsert_stmt = $conn->prepare($upsert_sql);
        $upsert_stmt->bind_param("iiiiiiiss", $user_id, $lesson_id, $course_id, $watch_time, $progress_position, $is_completed, $completion_rate, $ip_address, $user_agent);
        
        if ($upsert_stmt->execute()) {
            // 更新用户课程的观看统计
            updateUserCourseStats($conn, $user_id, $course_id);

            echo json_encode([
                'success' => true,
                'data' => [
                    'completion_rate' => round($completion_rate, 2),
                    'is_completed' => $is_completed,
                    'progress_position' => $progress_position,
                    'watch_time' => $watch_time
                ],
                'message' => '观看进度记录成功'
            ], JSON_UNESCAPED_UNICODE);
        } else {
            echo json_encode([
                'success' => false,
                'message' => '记录观看进度失败'
            ], JSON_UNESCAPED_UNICODE);
        }
        
    } else if ($method === 'GET') {
        // 获取观看进度
        $user_id = getUserIdFromToken();
        if (!$user_id) {
            echo json_encode([
                'success' => false,
                'message' => '用户未登录或token无效'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        $lesson_id = isset($_GET['lesson_id']) ? intval($_GET['lesson_id']) : 0;
        $course_id = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;
        
        if ($lesson_id > 0) {
            // 获取单个课时的观看进度
            $progress_sql = "SELECT 
                                lwl.*,
                                l.title as lesson_title,
                                l.duration as lesson_duration
                             FROM lesson_watch_logs lwl
                             LEFT JOIN lessons l ON lwl.lesson_id = l.id
                             WHERE lwl.user_id = ? AND lwl.lesson_id = ?";
            $progress_stmt = $conn->prepare($progress_sql);
            $progress_stmt->bind_param("ii", $user_id, $lesson_id);
            $progress_stmt->execute();
            $progress_result = $progress_stmt->get_result();
            $progress = $progress_result->fetch_assoc();
            
            if ($progress) {
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'lesson_id' => intval($progress['lesson_id']),
                        'course_id' => intval($progress['course_id']),
                        'watch_time' => intval($progress['watch_time']),
                        'progress_position' => intval($progress['progress_position']),
                        'completion_rate' => floatval($progress['completion_rate']),
                        'is_completed' => intval($progress['is_completed']),
                        'lesson_title' => $progress['lesson_title'],
                        'lesson_duration' => intval($progress['lesson_duration']),
                        'last_watch_time' => $progress['updated_at']
                    ],
                    'message' => '获取观看进度成功'
                ], JSON_UNESCAPED_UNICODE);
            } else {
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'lesson_id' => $lesson_id,
                        'course_id' => $course_id,
                        'watch_time' => 0,
                        'progress_position' => 0,
                        'completion_rate' => 0,
                        'is_completed' => 0,
                        'last_watch_time' => null
                    ],
                    'message' => '暂无观看记录'
                ], JSON_UNESCAPED_UNICODE);
            }
            
        } else if ($course_id > 0) {
            // 获取整个课程的观看进度
            $course_progress_sql = "SELECT 
                                        lwl.lesson_id,
                                        lwl.watch_time,
                                        lwl.progress_position,
                                        lwl.completion_rate,
                                        lwl.is_completed,
                                        lwl.updated_at,
                                        l.title as lesson_title,
                                        l.duration as lesson_duration,
                                        l.sort_order
                                    FROM lesson_watch_logs lwl
                                    LEFT JOIN lessons l ON lwl.lesson_id = l.id
                                    WHERE lwl.user_id = ? AND lwl.course_id = ?
                                    ORDER BY l.sort_order ASC, l.id ASC";
            $course_progress_stmt = $conn->prepare($course_progress_sql);
            $course_progress_stmt->bind_param("ii", $user_id, $course_id);
            $course_progress_stmt->execute();
            $course_progress_result = $course_progress_stmt->get_result();
            
            $lessons_progress = [];
            while ($row = $course_progress_result->fetch_assoc()) {
                $lessons_progress[] = [
                    'lesson_id' => intval($row['lesson_id']),
                    'lesson_title' => $row['lesson_title'],
                    'lesson_duration' => intval($row['lesson_duration']),
                    'sort_order' => intval($row['sort_order']),
                    'watch_time' => intval($row['watch_time']),
                    'progress_position' => intval($row['progress_position']),
                    'completion_rate' => floatval($row['completion_rate']),
                    'is_completed' => intval($row['is_completed']),
                    'last_watch_time' => $row['updated_at']
                ];
            }
            
            // 计算课程整体进度
            $total_lessons = count($lessons_progress);
            $completed_lessons = count(array_filter($lessons_progress, function($lesson) {
                return $lesson['is_completed'] === 1;
            }));
            $course_completion_rate = $total_lessons > 0 ? ($completed_lessons / $total_lessons) * 100 : 0;
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'course_id' => $course_id,
                    'lessons_progress' => $lessons_progress,
                    'statistics' => [
                        'total_lessons' => $total_lessons,
                        'completed_lessons' => $completed_lessons,
                        'course_completion_rate' => round($course_completion_rate, 2)
                    ]
                ],
                'message' => '获取课程观看进度成功'
            ], JSON_UNESCAPED_UNICODE);
            
        } else {
            echo json_encode([
                'success' => false,
                'message' => '参数无效'
            ], JSON_UNESCAPED_UNICODE);
        }
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '服务器错误',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 更新用户课程的观看统计数据
 */
function updateUserCourseStats($conn, $user_id, $course_id) {
    try {
        // 计算该课程的总观看进度和观看次数
        $stats_sql = "SELECT
                        COUNT(DISTINCT lwl.lesson_id) as watched_lessons,
                        COUNT(DISTINCT DATE(lwl.created_at)) as watch_sessions,
                        AVG(lwl.completion_rate) as avg_completion,
                        MAX(lwl.updated_at) as last_watched
                      FROM lesson_watch_logs lwl
                      WHERE lwl.user_id = ? AND lwl.course_id = ?";

        $stats_stmt = $conn->prepare($stats_sql);
        $stats_stmt->bind_param("ii", $user_id, $course_id);
        $stats_stmt->execute();
        $stats = $stats_stmt->get_result()->fetch_assoc();

        // 获取课程总课时数
        $lesson_count_sql = "SELECT COUNT(*) as total_lessons FROM lessons WHERE course_id = ? AND status = 1";
        $lesson_count_stmt = $conn->prepare($lesson_count_sql);
        $lesson_count_stmt->bind_param("i", $course_id);
        $lesson_count_stmt->execute();
        $lesson_count = $lesson_count_stmt->get_result()->fetch_assoc()['total_lessons'];

        // 计算课程整体进度
        $course_progress = 0;
        if ($lesson_count > 0 && $stats['watched_lessons'] > 0) {
            $course_progress = ($stats['watched_lessons'] / $lesson_count) * ($stats['avg_completion'] ?? 0);
        }

        // 更新user_courses表
        $update_sql = "UPDATE user_courses
                       SET watch_progress = ?,
                           watch_count = ?,
                           last_watched_at = ?
                       WHERE user_id = ? AND course_id = ?";

        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("disii",
            $course_progress,
            $stats['watch_sessions'],
            $stats['last_watched'],
            $user_id,
            $course_id
        );
        $update_stmt->execute();

    } catch (Exception $e) {
        error_log("更新用户课程统计失败: " . $e->getMessage());
    }
}
?>
