# 腾讯云VOD视频上传修复设计文档

## 概述

本设计文档描述了解决腾讯云VOD视频上传中"signature lack secret id"错误的方案。通过代码分析，我们发现问题可能出在签名生成过程中，特别是在将SecretId传递给腾讯云VOD SDK的过程中。

## 架构

系统当前的VOD上传架构如下：

1. 前端通过`getVodSignature`函数获取上传签名
2. 后端API (`api/vod-signature.php`) 调用`VodSignature::generateClientUploadSignature`生成签名
3. 签名生成过程依赖于`includes/vod_config.php`中的配置信息
4. 前端使用获取的签名初始化腾讯云VOD SDK并上传视频

## 问题分析

根据错误信息"signature lack secret id"，问题可能出在以下几个环节：

1. **签名格式问题**：腾讯云VOD SDK期望的签名格式与我们生成的不一致
2. **签名内容问题**：生成的签名中没有包含SecretId或格式不正确
3. **SDK版本兼容性**：我们使用的SDK版本可能与签名生成方式不兼容

通过代码分析，我们发现当前的签名生成方式是将SecretId作为签名参数的一部分，但SDK可能期望签名结果中直接包含SecretId。

## 组件和接口

### 1. 签名生成组件

**当前实现**：
- `VodSignature::generateUploadSignature` - 生成基本签名
- `VodSignature::generateClientUploadSignature` - 为客户端生成上传签名

**修改方案**：
- 修改签名生成逻辑，确保返回的数据结构符合SDK要求
- 在签名结果中明确包含SecretId字段

### 2. API接口

**当前实现**：
- `api/vod-signature.php` - 提供签名生成服务

**修改方案**：
- 确保API返回的数据结构包含所有SDK需要的字段
- 添加更详细的错误日志以便调试

### 3. 前端集成

**当前实现**：
- 通过`getVodSignature`函数获取签名
- 使用签名初始化腾讯云VOD SDK

**修改方案**：
- 确保前端正确处理API返回的签名数据
- 添加更详细的错误处理和日志记录

## 数据模型

签名数据结构需要修改为：

```json
{
  "signature": "签名字符串",
  "secretId": "腾讯云SecretId",
  "currentTimeStamp": 当前时间戳,
  "expireTime": 过期时间戳,
  "random": 随机数
}
```

## 错误处理

1. 配置错误处理：
   - 检查SecretId和SecretKey是否正确配置
   - 提供明确的错误信息指导用户修复配置

2. 签名生成错误处理：
   - 捕获并记录签名生成过程中的异常
   - 返回用户友好的错误信息

3. 前端错误处理：
   - 添加详细的错误日志
   - 显示用户友好的错误提示

## 测试策略

1. 单元测试：
   - 测试签名生成函数是否返回正确的数据结构
   - 测试配置验证函数是否正确识别配置问题

2. 集成测试：
   - 测试API是否返回正确的签名数据
   - 测试前端是否能正确处理签名数据

3. 端到端测试：
   - 测试完整的视频上传流程
   - 验证不同类型和大小的视频文件上传

## 安全考虑

1. 确保SecretKey不会暴露在前端
2. 验证用户权限，防止未授权访问
3. 实施适当的请求限制，防止滥用签名生成API