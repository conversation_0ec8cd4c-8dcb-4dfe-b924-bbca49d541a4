<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

$success_message = '';
$error_message = '';

// 分配课程给用户
if (isset($_POST['assign_course'])) {
    $user_id = intval($_POST['user_id']);
    $course_id = intval($_POST['course_id']);
    $expires_at = !empty($_POST['expires_at']) ? $_POST['expires_at'] : null;
    $assigned_by = $_SESSION['admin_id'] ?? 1;

    if ($user_id <= 0 || $course_id <= 0) {
        $error_message = '请选择用户和课程';
    } else {
        // 检查是否已经分配过
        $check_stmt = $conn->prepare("SELECT id, status FROM user_courses WHERE user_id = ? AND course_id = ?");
        $check_stmt->bind_param("ii", $user_id, $course_id);
        $check_stmt->execute();
        $existing = $check_stmt->get_result()->fetch_assoc();

        if ($existing) {
            if ($existing['status'] === 'active') {
                $error_message = '该用户已经拥有此课程的有效权限';
            } else {
                // 重新激活
                $stmt = $conn->prepare("UPDATE user_courses SET status = 'active', assigned_by = ?, assigned_at = NOW(), expires_at = ? WHERE id = ?");
                $stmt->bind_param("isi", $assigned_by, $expires_at, $existing['id']);
                if ($stmt->execute()) {
                    $success_message = '课程权限重新激活成功';
                } else {
                    $error_message = '激活失败：' . $conn->error;
                }
            }
        } else {
            // 新分配
            $stmt = $conn->prepare("INSERT INTO user_courses (user_id, course_id, assigned_by, expires_at) VALUES (?, ?, ?, ?)");
            $stmt->bind_param("iiis", $user_id, $course_id, $assigned_by, $expires_at);
            if ($stmt->execute()) {
                $success_message = '课程分配成功';
            } else {
                $error_message = '分配失败：' . $conn->error;
            }
        }
    }
}

// 撤销课程权限
if (isset($_GET['revoke'])) {
    $id = intval($_GET['revoke']);
    $stmt = $conn->prepare("UPDATE user_courses SET status = 'revoked' WHERE id = ?");
    $stmt->bind_param("i", $id);
    if ($stmt->execute()) {
        $success_message = '课程权限撤销成功';
    } else {
        $error_message = '撤销失败';
    }
}

// 删除分配记录
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);
    $stmt = $conn->prepare("DELETE FROM user_courses WHERE id = ?");
    $stmt->bind_param("i", $id);
    if ($stmt->execute()) {
        $success_message = '分配记录删除成功';
    } else {
        $error_message = '删除失败';
    }
}

// 获取用户列表（用于下拉选择）
$users_result = $conn->query("SELECT id, name, email FROM users ORDER BY name");

// 获取课程列表（用于下拉选择）
$courses_result = $conn->query("SELECT id, title FROM courses WHERE status = 'active' ORDER BY sort_order, title");

// 获取用户课程分配列表（分页）
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 15;
$offset = ($page - 1) * $limit;

// 搜索和筛选
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$course_filter = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;

$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_conditions[] = "(u.name LIKE ? OR u.email LIKE ? OR c.title LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
    $param_types .= 'sss';
}

if (!empty($status_filter)) {
    $where_conditions[] = "uc.status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

if ($course_filter > 0) {
    $where_conditions[] = "uc.course_id = ?";
    $params[] = $course_filter;
    $param_types .= 'i';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// 获取总数
$count_sql = "SELECT COUNT(*) as total 
              FROM user_courses uc 
              LEFT JOIN users u ON uc.user_id = u.id 
              LEFT JOIN courses c ON uc.course_id = c.id 
              $where_clause";
$count_stmt = $conn->prepare($count_sql);
if (!empty($params)) {
    $count_stmt->bind_param($param_types, ...$params);
}
$count_stmt->execute();
$total = $count_stmt->get_result()->fetch_assoc()['total'];
$total_pages = ceil($total / $limit);

// 获取分配数据
$sql = "SELECT uc.*, u.name as user_name, u.email as user_email, 
               c.title as course_title, a.username as assigned_by_name
        FROM user_courses uc 
        LEFT JOIN users u ON uc.user_id = u.id 
        LEFT JOIN courses c ON uc.course_id = c.id 
        LEFT JOIN admins a ON uc.assigned_by = a.id
        $where_clause 
        ORDER BY uc.assigned_at DESC 
        LIMIT ? OFFSET ?";

$stmt = $conn->prepare($sql);
$params[] = $limit;
$params[] = $offset;
$param_types .= 'ii';
$stmt->bind_param($param_types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

render_admin_header('用户课程管理', 'user_courses');
?>

<?php if ($success_message): ?>
    <?php show_success_message($success_message); ?>
<?php endif; ?>

<?php if ($error_message): ?>
    <?php show_error_message($error_message); ?>
<?php endif; ?>

<!-- 搜索筛选和分配按钮 -->
<div style="display: flex; justify-content: space-between; align-items: flex-end; margin-bottom: 20px; gap: 20px;">
    <form method="GET" style="display: flex; gap: 10px; flex-wrap: wrap; align-items: flex-end;">
        <div>
            <label style="display: block; margin-bottom: 5px; font-size: 12px; color: #666;">搜索</label>
            <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                   placeholder="用户名、邮箱或课程..." class="admin-form-input" style="width: 200px;">
        </div>
        
        <div>
            <label style="display: block; margin-bottom: 5px; font-size: 12px; color: #666;">状态</label>
            <select name="status" class="admin-form-input" style="width: 120px;">
                <option value="">全部状态</option>
                <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>有效</option>
                <option value="expired" <?php echo $status_filter === 'expired' ? 'selected' : ''; ?>>过期</option>
                <option value="revoked" <?php echo $status_filter === 'revoked' ? 'selected' : ''; ?>>撤销</option>
            </select>
        </div>
        
        <div>
            <label style="display: block; margin-bottom: 5px; font-size: 12px; color: #666;">课程</label>
            <select name="course_id" class="admin-form-input" style="width: 150px;">
                <option value="">全部课程</option>
                <?php 
                $courses_result->data_seek(0); // 重置结果集指针
                while ($course = $courses_result->fetch_assoc()): 
                ?>
                    <option value="<?php echo $course['id']; ?>" <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($course['title']); ?>
                    </option>
                <?php endwhile; ?>
            </select>
        </div>
        
        <div>
            <button type="submit" class="admin-btn admin-btn-secondary">筛选</button>
            <?php if (!empty($search) || !empty($status_filter) || $course_filter > 0): ?>
                <a href="user_courses.php" class="admin-btn admin-btn-secondary">清除</a>
            <?php endif; ?>
        </div>
    </form>
    
    <button type="button" class="admin-btn admin-btn-primary" onclick="showAssignForm()">
        <i class="fas fa-plus"></i> 分配课程
    </button>
</div>

<!-- 用户课程列表 -->
<?php render_card_start('用户课程分配'); ?>

<?php if ($result->num_rows > 0): ?>
    <?php 
    $headers = ['用户', '课程', '状态', '分配时间', '过期时间', '观看进度', '分配者', '操作'];
    render_table_start($headers); 
    ?>
    
    <?php while ($row = $result->fetch_assoc()): ?>
        <tr>
            <td>
                <div>
                    <strong><?php echo htmlspecialchars($row['user_name']); ?></strong>
                    <br><small style="color: #666;"><?php echo htmlspecialchars($row['user_email']); ?></small>
                </div>
            </td>
            <td><?php echo htmlspecialchars($row['course_title']); ?></td>
            <td>
                <?php 
                $status_class = 'info';
                $status_text = '未知';
                
                if ($row['status'] === 'active') {
                    // 检查是否过期
                    if ($row['expires_at'] && strtotime($row['expires_at']) < time()) {
                        $status_class = 'warning';
                        $status_text = '已过期';
                    } else {
                        $status_class = 'success';
                        $status_text = '有效';
                    }
                } elseif ($row['status'] === 'expired') {
                    $status_class = 'warning';
                    $status_text = '过期';
                } elseif ($row['status'] === 'revoked') {
                    $status_class = 'danger';
                    $status_text = '撤销';
                }
                
                render_badge($status_text, $status_class);
                ?>
            </td>
            <td><?php echo date('Y-m-d H:i', strtotime($row['assigned_at'])); ?></td>
            <td>
                <?php if ($row['expires_at']): ?>
                    <?php echo date('Y-m-d H:i', strtotime($row['expires_at'])); ?>
                <?php else: ?>
                    <span style="color: #999;">永久</span>
                <?php endif; ?>
            </td>
            <td>
                <div style="display: flex; align-items: center; gap: 5px;">
                    <div style="width: 60px; height: 6px; background: #e1e8ed; border-radius: 3px; overflow: hidden;">
                        <div style="width: <?php echo $row['watch_progress']; ?>%; height: 100%; background: #28a745;"></div>
                    </div>
                    <small><?php echo number_format($row['watch_progress'], 1); ?>%</small>
                </div>
                <small style="color: #666;">观看 <?php echo $row['watch_count']; ?> 次</small>
            </td>
            <td><?php echo htmlspecialchars($row['assigned_by_name'] ?? '未知'); ?></td>
            <td>
                <?php if ($row['status'] === 'active'): ?>
                    <a href="?revoke=<?php echo $row['id']; ?>" 
                       class="admin-btn admin-btn-sm admin-btn-warning"
                       onclick="return confirm('确定要撤销该用户的课程权限吗？')">
                        撤销
                    </a>
                <?php endif; ?>
                <a href="?delete=<?php echo $row['id']; ?>" 
                   class="admin-btn admin-btn-sm admin-btn-danger"
                   onclick="return confirmDelete('确定要删除这条分配记录吗？')">
                    删除
                </a>
            </td>
        </tr>
    <?php endwhile; ?>
    
    <?php render_table_end(); ?>
    
    <!-- 分页 -->
    <?php if ($total_pages > 1): ?>
        <div style="text-align: center; margin-top: 20px;">
            <?php 
            $query_params = [];
            if (!empty($search)) $query_params[] = 'search=' . urlencode($search);
            if (!empty($status_filter)) $query_params[] = 'status=' . urlencode($status_filter);
            if ($course_filter > 0) $query_params[] = 'course_id=' . $course_filter;
            $query_string = !empty($query_params) ? '&' . implode('&', $query_params) : '';
            ?>
            
            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <?php if ($i == $page): ?>
                    <span class="admin-btn admin-btn-primary" style="margin: 0 2px;"><?php echo $i; ?></span>
                <?php else: ?>
                    <a href="?page=<?php echo $i . $query_string; ?>" 
                       class="admin-btn admin-btn-secondary" style="margin: 0 2px;"><?php echo $i; ?></a>
                <?php endif; ?>
            <?php endfor; ?>
        </div>
        <div style="text-align: center; margin-top: 10px; color: #666;">
            共 <?php echo $total; ?> 条记录，第 <?php echo $page; ?> / <?php echo $total_pages; ?> 页
        </div>
    <?php endif; ?>
    
<?php else: ?>
    <p style="text-align: center; color: #666; padding: 40px;">
        <?php echo (!empty($search) || !empty($status_filter) || $course_filter > 0) ? '没有找到匹配的记录' : '暂无课程分配记录'; ?>
    </p>
<?php endif; ?>

<?php render_card_end(); ?>

<!-- 分配课程模态框 -->
<div id="assignModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 2000;">
    <div style="background: white; margin: 100px auto; padding: 30px; width: 90%; max-width: 500px; border-radius: 8px;">
        <h3>分配课程给用户</h3>
        
        <?php render_form_start('', 'post', 'assignForm'); ?>
            <div class="admin-form-group">
                <label class="admin-form-label" for="user_id">选择用户</label>
                <select class="admin-form-input" name="user_id" id="user_id" required>
                    <option value="">请选择用户</option>
                    <?php 
                    $users_result->data_seek(0); // 重置结果集指针
                    while ($user = $users_result->fetch_assoc()): 
                    ?>
                        <option value="<?php echo $user['id']; ?>">
                            <?php echo htmlspecialchars($user['name'] . ' (' . $user['email'] . ')'); ?>
                        </option>
                    <?php endwhile; ?>
                </select>
            </div>
            
            <div class="admin-form-group">
                <label class="admin-form-label" for="course_id">选择课程</label>
                <select class="admin-form-input" name="course_id" id="course_id" required>
                    <option value="">请选择课程</option>
                    <?php 
                    $courses_result->data_seek(0); // 重置结果集指针
                    while ($course = $courses_result->fetch_assoc()): 
                    ?>
                        <option value="<?php echo $course['id']; ?>">
                            <?php echo htmlspecialchars($course['title']); ?>
                        </option>
                    <?php endwhile; ?>
                </select>
            </div>
            
            <?php render_form_input('过期时间', 'expires_at', 'datetime-local', '', false, '留空表示永久有效'); ?>
            
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="admin-btn admin-btn-secondary" onclick="closeAssignModal()">取消</button>
                <button type="submit" name="assign_course" class="admin-btn admin-btn-primary">分配</button>
            </div>
        <?php render_form_end(); ?>
    </div>
</div>

<script>
function showAssignForm() {
    document.getElementById('assignForm').reset();
    document.getElementById('assignModal').style.display = 'block';
}

function closeAssignModal() {
    document.getElementById('assignModal').style.display = 'none';
}

// 点击模态框外部关闭
document.getElementById('assignModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeAssignModal();
    }
});
</script>

<?php render_admin_footer(); ?>
