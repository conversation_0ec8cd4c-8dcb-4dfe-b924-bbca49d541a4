<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);

require_once '../includes/db.php';
require_once '../includes/auth.php';

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 验证用户身份
$user = authenticate_user();
if (!$user) {
    http_response_code(401);
    echo json_encode([
        'code' => 401,
        'message' => '未授权访问',
        'data' => null
    ]);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
if (!isset($input['order_id']) || !isset($input['payment_type'])) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少必需参数',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $user_id = $user['id'];
    $order_id = intval($input['order_id']);
    $payment_type = trim($input['payment_type']); // alipay, wxpay, qqpay
    
    // 验证支付类型
    $allowed_types = ['alipay', 'wxpay', 'qqpay'];
    if (!in_array($payment_type, $allowed_types)) {
        echo json_encode([
            'code' => 400,
            'message' => '不支持的支付类型',
            'data' => null
        ]);
        exit;
    }
    
    // 验证订单
    $stmt = $conn->prepare("
        SELECT * FROM orders 
        WHERE id = ? AND user_id = ? AND order_status = 'pending' AND payment_status = 'unpaid'
    ");
    $stmt->bind_param("ii", $order_id, $user_id);
    $stmt->execute();
    $order = $stmt->get_result()->fetch_assoc();
    
    if (!$order) {
        echo json_encode([
            'code' => 404,
            'message' => '订单不存在或状态不正确',
            'data' => null
        ]);
        exit;
    }
    
    // 检查订单是否过期
    if (strtotime($order['expire_time']) < time()) {
        // 更新订单状态为过期
        $stmt = $conn->prepare("UPDATE orders SET order_status = 'expired' WHERE id = ?");
        $stmt->bind_param("i", $order_id);
        $stmt->execute();
        
        echo json_encode([
            'code' => 400,
            'message' => '订单已过期',
            'data' => null
        ]);
        exit;
    }
    
    // 获取码支付易支付配置
    $stmt = $conn->prepare("
        SELECT setting_key, setting_value 
        FROM settings 
        WHERE setting_key IN ('epay_enabled', 'epay_api_url', 'epay_partner_id', 
                              'epay_partner_key', 'epay_notify_url', 'epay_return_url', 'epay_supported_methods')
    ");
    $stmt->execute();
    $settings_result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    $settings = [];
    foreach ($settings_result as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
    
    // 检查码支付易支付是否启用
    if (empty($settings['epay_enabled']) || $settings['epay_enabled'] !== '1') {
        echo json_encode([
            'code' => 400,
            'message' => '码支付易支付功能暂未开放，请联系客服或稍后再试',
            'data' => null,
            'user_message' => '支付功能正在维护中，请稍后再试或联系客服'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 检查必需的配置
    $required_settings = ['epay_api_url', 'epay_partner_id', 'epay_partner_key'];
    $missing_settings = [];

    foreach ($required_settings as $key) {
        if (empty($settings[$key])) {
            $missing_settings[] = $key;
        }
    }

    if (!empty($missing_settings)) {
        echo json_encode([
            'code' => 500,
            'message' => '支付系统配置异常，请联系客服处理',
            'data' => null,
            'user_message' => '支付功能暂时不可用，请联系客服或稍后再试',
            'admin_debug' => [
                'missing_settings' => $missing_settings,
                'available_settings' => array_keys($settings)
            ]
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查支付方式是否支持
    $supported_methods = explode(',', $settings['epay_supported_methods'] ?? 'alipay,wxpay,qqpay');
    if (!in_array($payment_type, $supported_methods)) {
        echo json_encode([
            'code' => 400,
            'message' => '当前支付方式暂不支持',
            'data' => null
        ]);
        exit;
    }
    
    // 生成支付流水号
    $payment_no = 'EPAY' . date('YmdHis') . sprintf('%06d', $user_id) . sprintf('%04d', rand(1000, 9999));
    
    // 创建支付记录
    $stmt = $conn->prepare("
        INSERT INTO payments (payment_no, order_id, user_id, payment_method, amount, 
                             payment_status, created_at) 
        VALUES (?, ?, ?, ?, ?, 'pending', NOW())
    ");
    $payment_method = 'epay_' . $payment_type;
    $stmt->bind_param("siiss", $payment_no, $order_id, $user_id, $payment_method, $order['actual_amount']);
    $stmt->execute();
    $payment_id = $conn->insert_id;
    
    // 准备码支付易支付参数
    $epay_params = [
        'pid' => $settings['epay_partner_id'],
        'type' => $payment_type,
        'out_trade_no' => $payment_no,
        'notify_url' => $settings['epay_notify_url'],
        'return_url' => $settings['epay_return_url'],
        'name' => '课程购买-订单号:' . $order['order_no'],
        'money' => number_format($order['actual_amount'], 2, '.', ''),
        'sitename' => '在线学习平台'
    ];
    
    // 生成签名
    $epay_params['sign'] = generate_epay_sign($epay_params, $settings['epay_partner_key']);
    $epay_params['sign_type'] = 'MD5';
    
    // 构建支付URL
    $api_url = rtrim($settings['epay_api_url'], '/') . '/submit.php';
    $payment_url = $api_url . '?' . http_build_query($epay_params);
    
    echo json_encode([
        'code' => 200,
        'message' => '预支付订单创建成功',
        'data' => [
            'payment_id' => $payment_id,
            'payment_no' => $payment_no,
            'payment_url' => $payment_url,
            'payment_type' => $payment_type,
            'amount' => $order['actual_amount']
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage(),
        'data' => null
    ]);
}

/**
 * 生成码支付易支付签名
 */
function generate_epay_sign($params, $key) {
    // 过滤空值和签名参数
    $filtered_params = [];
    foreach ($params as $k => $v) {
        if ($k !== 'sign' && $k !== 'sign_type' && $v !== '' && $v !== null) {
            $filtered_params[$k] = $v;
        }
    }
    
    // 按键名排序
    ksort($filtered_params);
    
    // 构建签名字符串
    $sign_string = '';
    foreach ($filtered_params as $k => $v) {
        $sign_string .= $k . '=' . $v . '&';
    }
    $sign_string .= 'key=' . $key;
    
    return md5($sign_string);
}

/**
 * 获取客户端IP
 */
function get_client_ip() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}
?>
