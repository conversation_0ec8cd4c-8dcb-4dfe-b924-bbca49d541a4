<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

$success_message = '';
$error_message = '';

// 检查并添加课程标签字段（如果不存在）
$check_tags_column = $conn->query("SHOW COLUMNS FROM courses LIKE 'tags'");
if ($check_tags_column->num_rows == 0) {
    $add_tags_column = "ALTER TABLE courses ADD COLUMN tags VARCHAR(500) DEFAULT NULL COMMENT '课程标签，逗号分隔' AFTER is_on_sale";
    $conn->query($add_tags_column);
}

// 检查并添加课程难度字段（如果不存在）
$check_difficulty_column = $conn->query("SHOW COLUMNS FROM courses LIKE 'difficulty'");
if ($check_difficulty_column->num_rows == 0) {
    $add_difficulty_column = "ALTER TABLE courses ADD COLUMN difficulty ENUM('beginner','intermediate','advanced') DEFAULT 'beginner' COMMENT '课程难度' AFTER tags";
    $conn->query($add_difficulty_column);
}

// 添加或编辑课程
if (isset($_POST['add_course'])) {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $video_url = trim($_POST['video_url']);
    $thumbnail = trim($_POST['thumbnail']);
    $duration = intval($_POST['duration']);
    $price = floatval($_POST['price']);
    $original_price = floatval($_POST['original_price']);
    $course_type = $_POST['course_type'];
    $tags = trim($_POST['tags']);
    $difficulty = $_POST['difficulty'];
    $status = $_POST['status'];
    $sort_order = intval($_POST['sort_order']);
    $course_id = intval($_POST['course_id']);

    if (empty($title) || empty($video_url)) {
        $error_message = '请填写课程标题和视频链接';
    } else {
        // 如果是付费课程但价格为0，提示错误
        if ($course_type === 'paid' && $price <= 0) {
            $error_message = '付费课程必须设置价格';
        } else {
            // 将course_type转换为is_free字段
            $is_free = ($course_type === 'free') ? 1 : 0;

            if ($course_id > 0) {
                // 编辑课程
                $stmt = $conn->prepare("UPDATE courses SET title = ?, description = ?, video_url = ?, thumbnail = ?, duration = ?, price = ?, original_price = ?, is_free = ?, tags = ?, difficulty = ?, status = ?, sort_order = ? WHERE id = ?");
                if ($stmt === false) {
                    $error_message = '准备更新语句失败：' . $conn->error;
                } else {
                    $stmt->bind_param("ssssiddisssii", $title, $description, $video_url, $thumbnail, $duration, $price, $original_price, $is_free, $tags, $difficulty, $status, $sort_order, $course_id);

                    if ($stmt->execute()) {
                        $success_message = '课程更新成功';
                    } else {
                        $error_message = '更新课程失败：' . $conn->error;
                    }
                }
            } else {
                // 添加课程
                $stmt = $conn->prepare("INSERT INTO courses (title, description, video_url, thumbnail, duration, price, original_price, is_free, tags, difficulty, status, sort_order, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                if ($stmt === false) {
                    $error_message = '准备插入语句失败：' . $conn->error;
                } else {
                    $created_by = $_SESSION['admin_id'] ?? 1; // 使用当前管理员ID
                    $stmt->bind_param("ssssiddisssiii", $title, $description, $video_url, $thumbnail, $duration, $price, $original_price, $is_free, $tags, $difficulty, $status, $sort_order, $created_by);

                    if ($stmt->execute()) {
                        $success_message = '课程添加成功';
                    } else {
                        $error_message = '添加课程失败：' . $conn->error;
                    }
                }
            }
        }
    }
}

// 删除课程
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);
    
    // 先检查是否有用户已分配此课程
    $check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_courses WHERE course_id = ?");
    if ($check_stmt === false) {
        $error_message = '准备检查语句失败：' . $conn->error;
    } else {
        $check_stmt->bind_param("i", $id);
        if ($check_stmt->execute()) {
            $count = $check_stmt->get_result()->fetch_assoc()['count'];
        } else {
            $error_message = '执行检查语句失败：' . $check_stmt->error;
            $count = 1; // 设置为1以防止删除
        }
    }
    
    if ($count > 0) {
        $error_message = '该课程已分配给用户，无法删除';
    } else {
        $stmt = $conn->prepare("DELETE FROM courses WHERE id = ?");
        if ($stmt === false) {
            $error_message = '准备删除语句失败：' . $conn->error;
        } else {
            $stmt->bind_param("i", $id);
            if ($stmt->execute()) {
                $success_message = '课程删除成功';
            } else {
                $error_message = '删除课程失败';
            }
        }
    }
}

// 更新课程状态
if (isset($_GET['toggle_status'])) {
    $id = intval($_GET['toggle_status']);
    $stmt = $conn->prepare("UPDATE courses SET status = CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END WHERE id = ?");
    if ($stmt === false) {
        $error_message = '准备状态更新语句失败：' . $conn->error;
    } else {
        $stmt->bind_param("i", $id);
        if ($stmt->execute()) {
            $success_message = '课程状态更新成功';
        } else {
            $error_message = '更新状态失败';
        }
    }
}

// 获取课程列表（分页）
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// 搜索功能
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$where_clause = '';
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_clause = "WHERE title LIKE ? OR description LIKE ?";
    $search_param = "%$search%";
    $params = [$search_param, $search_param];
    $param_types = 'ss';
}

// 获取总数
$count_sql = "SELECT COUNT(*) as total FROM courses $where_clause";
$count_stmt = $conn->prepare($count_sql);
if ($count_stmt === false) {
    die("Error preparing count statement: " . $conn->error);
}
if (!empty($params)) {
    $count_stmt->bind_param($param_types, ...$params);
}
if (!$count_stmt->execute()) {
    die("Error executing count statement: " . $count_stmt->error);
}
$count_result = $count_stmt->get_result();
if ($count_result === false) {
    die("Error getting count result: " . $count_stmt->error);
}
$total = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total / $limit);

// 获取课程数据
$sql = "SELECT c.*, a.username as creator_name
        FROM courses c
        LEFT JOIN admins a ON c.created_by = a.id
        $where_clause
        ORDER BY c.sort_order ASC, c.created_at DESC
        LIMIT ? OFFSET ?";

$stmt = $conn->prepare($sql);
if ($stmt === false) {
    die("Error preparing main statement: " . $conn->error);
}
$params[] = $limit;
$params[] = $offset;
$param_types .= 'ii';
if (!$stmt->bind_param($param_types, ...$params)) {
    die("Error binding parameters: " . $stmt->error);
}
if (!$stmt->execute()) {
    die("Error executing main statement: " . $stmt->error);
}
$result = $stmt->get_result();
if ($result === false) {
    die("Error getting main result: " . $stmt->error);
}

render_admin_header('课程管理', 'courses');
?>

<?php if ($success_message): ?>
    <?php show_success_message($success_message); ?>
<?php endif; ?>

<?php if ($error_message): ?>
    <?php show_error_message($error_message); ?>
<?php endif; ?>

<!-- 搜索和添加按钮 -->
<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
    <form method="GET" style="display: flex; gap: 10px;">
        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
               placeholder="搜索课程标题或描述..." class="admin-form-input" style="width: 300px;">
        <button type="submit" class="admin-btn admin-btn-secondary">搜索</button>
        <?php if (!empty($search)): ?>
            <a href="courses.php" class="admin-btn admin-btn-secondary">清除</a>
        <?php endif; ?>
    </form>
    <button type="button" class="admin-btn admin-btn-primary" onclick="showAddForm()">
        <i class="fas fa-plus"></i> 添加课程
    </button>
</div>

<!-- 课程列表 -->
<?php render_card_start('课程列表'); ?>

<?php if ($result->num_rows > 0): ?>
    <?php
    $headers = ['ID', '课程信息', '价格信息', '类型/难度', '时长', '状态', '排序', '创建者', '创建时间', '操作'];
    render_table_start($headers);
    ?>

    <?php while ($row = $result->fetch_assoc()): ?>
        <tr>
            <td><?php echo $row['id']; ?></td>
            <td>
                <div style="max-width: 250px;">
                    <strong><?php echo htmlspecialchars($row['title']); ?></strong>
                    <?php if ($row['description']): ?>
                        <br><small style="color: #666;"><?php echo htmlspecialchars(mb_substr($row['description'], 0, 50)); ?>...</small>
                    <?php endif; ?>
                    <?php if (!empty($row['tags'])): ?>
                        <br><div style="margin-top: 5px;">
                            <?php
                            $tags = explode(',', $row['tags']);
                            foreach ($tags as $tag):
                                $tag = trim($tag);
                                if (!empty($tag)):
                            ?>
                                <span style="background: #e3f2fd; color: #1976d2; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-right: 3px;"><?php echo htmlspecialchars($tag); ?></span>
                            <?php
                                endif;
                            endforeach;
                            ?>
                        </div>
                    <?php endif; ?>
                </div>
            </td>
            <td>
                <?php if ($row['is_free'] == 0 && $row['price'] > 0): ?>
                    <div>
                        <strong style="color: #f44336;">¥<?php echo number_format($row['price'], 2); ?></strong>
                        <?php if ($row['original_price'] && $row['original_price'] > $row['price']): ?>
                            <br><small style="color: #999; text-decoration: line-through;">¥<?php echo number_format($row['original_price'], 2); ?></small>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <?php render_badge('免费', 'success'); ?>
                <?php endif; ?>
            </td>
            <td>
                <div>
                    <?php if ($row['is_free'] == 0 && $row['price'] > 0): ?>
                        <?php render_badge('付费', 'warning'); ?>
                    <?php else: ?>
                        <?php render_badge('免费', 'success'); ?>
                    <?php endif; ?>

                    <?php if (isset($row['difficulty'])): ?>
                        <br><small style="margin-top: 3px; display: inline-block;">
                            <?php
                            $difficulty_map = [
                                'beginner' => '初级',
                                'intermediate' => '中级',
                                'advanced' => '高级'
                            ];
                            echo $difficulty_map[$row['difficulty']] ?? '初级';
                            ?>
                        </small>
                    <?php endif; ?>
                </div>
            </td>
            <td>
                <?php if ($row['duration']): ?>
                    <?php echo gmdate("H:i:s", $row['duration']); ?>
                <?php else: ?>
                    <span style="color: #999;">未设置</span>
                <?php endif; ?>
            </td>
            <td>
                <?php if ($row['status'] === 'active'): ?>
                    <?php render_badge('启用', 'success'); ?>
                <?php else: ?>
                    <?php render_badge('禁用', 'danger'); ?>
                <?php endif; ?>
            </td>
            <td><?php echo $row['sort_order']; ?></td>
            <td><?php echo htmlspecialchars($row['creator_name'] ?? '未知'); ?></td>
            <td><?php echo date('Y-m-d H:i', strtotime($row['created_at'])); ?></td>
            <td>
                <a href="?toggle_status=<?php echo $row['id']; ?>"
                   class="admin-btn admin-btn-sm admin-btn-warning"
                   onclick="return confirm('确定要切换课程状态吗？')">
                    <?php echo $row['status'] === 'active' ? '禁用' : '启用'; ?>
                </a>
                <a href="lessons.php?course_id=<?php echo $row['id']; ?>"
                   class="admin-btn admin-btn-sm admin-btn-info"
                   title="管理课时">
                    课时
                </a>
                <button type="button" class="admin-btn admin-btn-sm admin-btn-secondary"
                        onclick="editCourse(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                    编辑
                </button>
                <a href="?delete=<?php echo $row['id']; ?>"
                   class="admin-btn admin-btn-sm admin-btn-danger"
                   onclick="return confirmDelete('确定要删除这个课程吗？删除后无法恢复！')">
                    删除
                </a>
            </td>
        </tr>
    <?php endwhile; ?>
    
    <?php render_table_end(); ?>
    
    <!-- 分页 -->
    <?php if ($total_pages > 1): ?>
        <div style="text-align: center; margin-top: 20px;">
            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <?php if ($i == $page): ?>
                    <span class="admin-btn admin-btn-primary" style="margin: 0 2px;"><?php echo $i; ?></span>
                <?php else: ?>
                    <a href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                       class="admin-btn admin-btn-secondary" style="margin: 0 2px;"><?php echo $i; ?></a>
                <?php endif; ?>
            <?php endfor; ?>
        </div>
        <div style="text-align: center; margin-top: 10px; color: #666;">
            共 <?php echo $total; ?> 个课程，第 <?php echo $page; ?> / <?php echo $total_pages; ?> 页
        </div>
    <?php endif; ?>
    
<?php else: ?>
    <p style="text-align: center; color: #666; padding: 40px;">
        <?php echo !empty($search) ? '没有找到匹配的课程' : '暂无课程数据'; ?>
    </p>
<?php endif; ?>

<?php render_card_end(); ?>

<!-- 添加/编辑课程模态框 -->
<div id="courseModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 2000;">
    <div style="background: white; margin: 30px auto; padding: 30px; width: 95%; max-width: 800px; border-radius: 8px; max-height: 90vh; overflow-y: auto;">
        <h3 id="modalTitle">添加课程</h3>

        <?php render_form_start('', 'post', 'courseForm'); ?>
            <input type="hidden" id="courseId" name="course_id" value="">

            <!-- 基本信息 -->
            <div style="border: 1px solid #e0e0e0; border-radius: 6px; padding: 20px; margin-bottom: 20px;">
                <h4 style="margin-top: 0; color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">基本信息</h4>

                <?php render_form_input('课程标题', 'title', 'text', '', true, '请输入课程标题'); ?>

                <div class="admin-form-group">
                    <label class="admin-form-label" for="description">课程描述</label>
                    <textarea class="admin-form-input" name="description" id="description" rows="3" placeholder="请输入课程描述（可选）"></textarea>
                </div>

                <?php render_form_input('视频链接', 'video_url', 'url', '', true, 'https://example.com/video.mp4'); ?>

                <div class="admin-form-group">
                    <label class="admin-form-label" for="thumbnail">缩略图</label>
                    <div style="display: flex; gap: 10px; align-items: flex-start;">
                        <input type="url" class="admin-form-input" name="thumbnail" id="thumbnail" placeholder="https://example.com/thumbnail.jpg" style="flex: 1;">
                        <div style="display: flex; flex-direction: column; gap: 5px;">
                            <button type="button" class="admin-btn admin-btn-secondary" onclick="triggerImageUpload()" style="white-space: nowrap;">
                                <i class="fas fa-upload"></i> 上传图片
                            </button>
                            <input type="file" id="imageUpload" accept="image/*" style="display: none;" onchange="uploadImage(this)">
                        </div>
                    </div>
                    <div id="imagePreview" style="margin-top: 10px; display: none;">
                        <img id="previewImg" src="" alt="预览图" style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;">
                        <div style="margin-top: 5px;">
                            <small id="imageInfo" style="color: #666;"></small>
                            <button type="button" class="admin-btn admin-btn-sm admin-btn-danger" onclick="removeImage()" style="margin-left: 10px;">删除</button>
                        </div>
                    </div>
                    <div id="uploadProgress" style="margin-top: 10px; display: none;">
                        <div style="background: #f0f0f0; border-radius: 4px; overflow: hidden;">
                            <div id="progressBar" style="height: 4px; background: #007bff; width: 0%; transition: width 0.3s;"></div>
                        </div>
                        <small id="uploadStatus" style="color: #666; margin-top: 5px; display: block;"></small>
                    </div>
                </div>

                <?php render_form_input('视频时长（秒）', 'duration', 'number', '', false, '1800'); ?>
            </div>

            <!-- 价格设置 -->
            <div style="border: 1px solid #e0e0e0; border-radius: 6px; padding: 20px; margin-bottom: 20px;">
                <h4 style="margin-top: 0; color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">价格设置</h4>

                <div class="admin-form-group">
                    <label class="admin-form-label" for="course_type">课程类型</label>
                    <select class="admin-form-input" name="course_type" id="course_type" onchange="togglePriceFields()">
                        <option value="free">免费课程</option>
                        <option value="paid">付费课程</option>
                    </select>
                </div>

                <div id="priceFields" style="display: none;">
                    <div style="display: flex; gap: 15px;">
                        <div style="flex: 1;">
                            <div class="admin-form-group">
                                <label class="admin-form-label" for="price">课程价格（元）</label>
                                <input type="number" class="admin-form-input" name="price" id="price" value="0.00" step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                        <div style="flex: 1;">
                            <div class="admin-form-group">
                                <label class="admin-form-label" for="original_price">原价（元）</label>
                                <input type="number" class="admin-form-input" name="original_price" id="original_price" value="0.00" step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                    </div>
                    <small style="color: #666;">原价可以高于现价，用于显示优惠信息</small>
                </div>
            </div>

            <!-- 课程属性 -->
            <div style="border: 1px solid #e0e0e0; border-radius: 6px; padding: 20px; margin-bottom: 20px;">
                <h4 style="margin-top: 0; color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">课程属性</h4>

                <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                    <div style="flex: 1;">
                        <div class="admin-form-group">
                            <label class="admin-form-label" for="difficulty">课程难度</label>
                            <select class="admin-form-input" name="difficulty" id="difficulty">
                                <option value="beginner">初级</option>
                                <option value="intermediate">中级</option>
                                <option value="advanced">高级</option>
                            </select>
                        </div>
                    </div>
                    <div style="flex: 1;">
                        <div class="admin-form-group">
                            <label class="admin-form-label" for="status">状态</label>
                            <select class="admin-form-input" name="status" id="status">
                                <option value="active">启用</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="admin-form-group">
                    <label class="admin-form-label" for="tags">课程标签</label>
                    <input type="text" class="admin-form-input" name="tags" id="tags" placeholder="请输入课程标签，多个标签用逗号分隔，如：编程,前端,JavaScript">
                    <small style="color: #666;">多个标签用逗号分隔，如：编程,前端,JavaScript</small>
                </div>

                <?php render_form_input('排序', 'sort_order', 'number', '0', false, '数字越小排序越靠前'); ?>
            </div>

            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="admin-btn admin-btn-secondary" onclick="closeModal()">取消</button>
                <button type="submit" name="add_course" class="admin-btn admin-btn-primary">保存</button>
            </div>
        <?php render_form_end(); ?>
    </div>
</div>

<script>
function showAddForm() {
    document.getElementById('modalTitle').textContent = '添加课程';
    document.getElementById('courseForm').reset();
    document.getElementById('courseId').value = '';
    document.getElementById('course_type').value = 'free';
    document.getElementById('imagePreview').style.display = 'none';
    togglePriceFields();
    document.querySelector('button[name="add_course"]').textContent = '保存';
    document.getElementById('courseModal').style.display = 'block';
}

function editCourse(course) {
    document.getElementById('modalTitle').textContent = '编辑课程';
    document.getElementById('courseId').value = course.id;
    document.getElementById('title').value = course.title;
    document.getElementById('description').value = course.description || '';
    document.getElementById('video_url').value = course.video_url;
    document.getElementById('thumbnail').value = course.thumbnail || '';
    document.getElementById('duration').value = course.duration || '';
    document.getElementById('price').value = course.price || '0.00';
    document.getElementById('original_price').value = course.original_price || '0.00';

    // 根据数据库中的is_free字段设置课程类型
    if (course.is_free == 1) {
        document.getElementById('course_type').value = 'free';
    } else {
        document.getElementById('course_type').value = 'paid';
    }

    document.getElementById('tags').value = course.tags || '';
    document.getElementById('difficulty').value = course.difficulty || 'beginner';
    document.getElementById('status').value = course.status;
    document.getElementById('sort_order').value = course.sort_order;

    // 显示现有缩略图预览
    if (course.thumbnail) {
        showImagePreview(course.thumbnail, '现有图片', 0);
    } else {
        document.getElementById('imagePreview').style.display = 'none';
    }

    togglePriceFields();
    document.querySelector('button[name="add_course"]').textContent = '更新';
    document.getElementById('courseModal').style.display = 'block';
}

function togglePriceFields() {
    const courseType = document.getElementById('course_type').value;
    const priceFields = document.getElementById('priceFields');
    const priceInput = document.getElementById('price');
    const originalPriceInput = document.getElementById('original_price');

    if (courseType === 'paid') {
        priceFields.style.display = 'block';
        priceInput.required = true;
    } else {
        priceFields.style.display = 'none';
        priceInput.required = false;
        priceInput.value = '0.00';
        originalPriceInput.value = '0.00';
    }
}

function closeModal() {
    document.getElementById('courseModal').style.display = 'none';
}

// 表单提交验证
document.getElementById('courseForm').addEventListener('submit', function(e) {
    const courseType = document.getElementById('course_type').value;
    const price = parseFloat(document.getElementById('price').value);
    const originalPrice = parseFloat(document.getElementById('original_price').value);

    if (courseType === 'paid') {
        if (price <= 0) {
            alert('付费课程必须设置价格');
            e.preventDefault();
            return false;
        }

        if (originalPrice > 0 && originalPrice < price) {
            if (!confirm('原价低于现价，确定要继续吗？')) {
                e.preventDefault();
                return false;
            }
        }
    }

    return true;
});

// 点击模态框外部关闭
document.getElementById('courseModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// 初始化价格字段显示状态
document.addEventListener('DOMContentLoaded', function() {
    togglePriceFields();
});

// 图片上传相关功能
function triggerImageUpload() {
    document.getElementById('imageUpload').click();
}

function uploadImage(input) {
    const file = input.files[0];
    if (!file) return;

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type.toLowerCase())) {
        alert('只支持 JPG、PNG、GIF、WebP 格式的图片');
        input.value = '';
        return;
    }

    // 验证文件大小（5MB）
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
        alert('图片大小不能超过5MB');
        input.value = '';
        return;
    }

    // 显示上传进度
    const progressDiv = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const uploadStatus = document.getElementById('uploadStatus');

    progressDiv.style.display = 'block';
    progressBar.style.width = '0%';
    uploadStatus.textContent = '正在上传...';

    // 创建FormData
    const formData = new FormData();
    formData.append('image', file);

    // 创建XMLHttpRequest以支持进度显示
    const xhr = new XMLHttpRequest();

    // 上传进度
    xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            progressBar.style.width = percentComplete + '%';
            uploadStatus.textContent = `上传中... ${Math.round(percentComplete)}%`;
        }
    });

    // 上传完成
    xhr.addEventListener('load', function() {
        progressDiv.style.display = 'none';

        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.success) {
                    // 上传成功，更新缩略图字段和预览
                    const fullUrl = window.location.origin + response.data.file_url;
                    document.getElementById('thumbnail').value = fullUrl;
                    showImagePreview(fullUrl, response.data.file_name, response.data.file_size);

                    // 显示成功消息
                    showUploadMessage('图片上传成功！', 'success');
                } else {
                    showUploadMessage('上传失败：' + response.message, 'error');
                }
            } catch (e) {
                showUploadMessage('上传失败：服务器响应格式错误', 'error');
            }
        } else {
            showUploadMessage('上传失败：服务器错误 ' + xhr.status, 'error');
        }

        // 清空文件输入
        input.value = '';
    });

    // 上传错误
    xhr.addEventListener('error', function() {
        progressDiv.style.display = 'none';
        showUploadMessage('上传失败：网络错误', 'error');
        input.value = '';
    });

    // 发送请求
    xhr.open('POST', 'upload_course_image.php');
    xhr.send(formData);
}

function showImagePreview(imageUrl, fileName, fileSize) {
    const previewDiv = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const imageInfo = document.getElementById('imageInfo');

    previewImg.src = imageUrl;
    imageInfo.textContent = `${fileName} (${formatFileSize(fileSize)})`;
    previewDiv.style.display = 'block';
}

function removeImage() {
    document.getElementById('thumbnail').value = '';
    document.getElementById('imagePreview').style.display = 'none';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showUploadMessage(message, type) {
    // 创建消息提示
    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 4px;
        color: white;
        font-weight: bold;
        z-index: 9999;
        max-width: 300px;
        word-wrap: break-word;
    `;

    if (type === 'success') {
        messageDiv.style.backgroundColor = '#28a745';
    } else {
        messageDiv.style.backgroundColor = '#dc3545';
    }

    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}
</script>

<?php render_admin_footer(); ?>
