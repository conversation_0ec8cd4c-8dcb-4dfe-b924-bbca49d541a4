<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';
require_once '../includes/vod_config.php';

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$success_message = '';
$error_message = '';

// 处理修复操作
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'fix_missing_urls':
            // 修复缺失播放URL的VOD课时
            $fix_query = "SELECT id, vod_file_id FROM lessons WHERE video_type = 'vod' AND vod_file_id IS NOT NULL AND vod_file_id != '' AND (vod_video_url IS NULL OR vod_video_url = '')";
            $fix_result = $conn->query($fix_query);
            
            $fixed_count = 0;
            $errors = [];
            
            while ($lesson = $fix_result->fetch_assoc()) {
                // 调用VOD状态更新API
                $api_url = '../api/vod-update-status.php';
                $post_data = http_build_query([
                    'lesson_id' => $lesson['id'],
                    'file_id' => $lesson['vod_file_id'],
                    'update_db' => '1'
                ]);
                
                $context = stream_context_create([
                    'http' => [
                        'method' => 'POST',
                        'header' => 'Content-Type: application/x-www-form-urlencoded',
                        'content' => $post_data
                    ]
                ]);
                
                $response = file_get_contents($api_url, false, $context);
                $result = json_decode($response, true);
                
                if ($result && $result['success'] && isset($result['data']['updated']) && $result['data']['updated']) {
                    $fixed_count++;
                } elseif (!$result || !$result['success']) {
                    $errors[] = "课时ID {$lesson['id']} 修复失败: " . ($result['message'] ?? '未知错误');
                }
                
                usleep(500000); // 0.5秒延迟
            }
            
            $success_message = "修复完成！成功修复了 {$fixed_count} 个课时的播放URL。";
            if (!empty($errors)) {
                $error_message = "部分修复失败：\n" . implode("\n", array_slice($errors, 0, 5));
            }
            break;
            
        case 'clean_invalid_vod':
            // 清理无效的VOD数据
            $clean_sql = "UPDATE lessons SET video_type = 'url', vod_file_id = NULL, vod_video_url = NULL WHERE video_type = 'vod' AND (vod_file_id IS NULL OR vod_file_id = '')";
            $clean_result = $conn->query($clean_sql);
            
            if ($clean_result) {
                $affected_rows = $conn->affected_rows;
                $success_message = "清理完成！清理了 {$affected_rows} 个无效的VOD记录。";
            } else {
                $error_message = "清理失败：" . $conn->error;
            }
            break;
    }
}

// 获取VOD诊断数据
$diagnosis = [];

// 1. 总体统计
$total_lessons_query = "SELECT COUNT(*) as total FROM lessons";
$total_lessons_result = $conn->query($total_lessons_query);
$diagnosis['total_lessons'] = $total_lessons_result->fetch_assoc()['total'];

$vod_lessons_query = "SELECT COUNT(*) as total FROM lessons WHERE video_type = 'vod'";
$vod_lessons_result = $conn->query($vod_lessons_query);
$diagnosis['vod_lessons'] = $vod_lessons_result->fetch_assoc()['total'];

// 2. VOD状态分析
$vod_with_file_id_query = "SELECT COUNT(*) as total FROM lessons WHERE video_type = 'vod' AND vod_file_id IS NOT NULL AND vod_file_id != ''";
$vod_with_file_id_result = $conn->query($vod_with_file_id_query);
$diagnosis['vod_with_file_id'] = $vod_with_file_id_result->fetch_assoc()['total'];

$vod_with_play_url_query = "SELECT COUNT(*) as total FROM lessons WHERE video_type = 'vod' AND vod_video_url IS NOT NULL AND vod_video_url != ''";
$vod_with_play_url_result = $conn->query($vod_with_play_url_query);
$diagnosis['vod_with_play_url'] = $vod_with_play_url_result->fetch_assoc()['total'];

$vod_missing_url_query = "SELECT COUNT(*) as total FROM lessons WHERE video_type = 'vod' AND vod_file_id IS NOT NULL AND vod_file_id != '' AND (vod_video_url IS NULL OR vod_video_url = '')";
$vod_missing_url_result = $conn->query($vod_missing_url_query);
$diagnosis['vod_missing_url'] = $vod_missing_url_result->fetch_assoc()['total'];

$invalid_vod_query = "SELECT COUNT(*) as total FROM lessons WHERE video_type = 'vod' AND (vod_file_id IS NULL OR vod_file_id = '')";
$invalid_vod_result = $conn->query($invalid_vod_query);
$diagnosis['invalid_vod'] = $invalid_vod_result->fetch_assoc()['total'];

// 3. 获取问题课时列表
$problem_lessons_query = "
    SELECT l.id, l.title, l.video_type, l.vod_file_id, l.vod_video_url, l.created_at, c.title as course_title
    FROM lessons l
    LEFT JOIN courses c ON l.course_id = c.id
    WHERE (
        (l.video_type = 'vod' AND (l.vod_file_id IS NULL OR l.vod_file_id = '')) OR
        (l.video_type = 'vod' AND l.vod_file_id IS NOT NULL AND l.vod_file_id != '' AND (l.vod_video_url IS NULL OR l.vod_video_url = ''))
    )
    ORDER BY l.created_at DESC
    LIMIT 20
";
$problem_lessons_result = $conn->query($problem_lessons_query);

// 4. 检查VOD配置
$vod_config_status = VodConfig::validateConfig();

render_admin_header('VOD诊断工具');
?>

<style>
.diagnosis-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 10px;
}

.stat-number.good { color: #28a745; }
.stat-number.warning { color: #ffc107; }
.stat-number.danger { color: #dc3545; }

.stat-label {
    color: #666;
    font-size: 14px;
}

.problem-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.problem-table th,
.problem-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.problem-table th {
    background: #f8f9fa;
    font-weight: 600;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.status-missing { background: #fff3cd; color: #856404; }
.status-invalid { background: #f8d7da; color: #721c24; }

.action-buttons {
    display: flex;
    gap: 10px;
    margin: 20px 0;
}
</style>

<div class="diagnosis-container">
    <h1>🔍 VOD诊断工具</h1>
    <p>检查和修复腾讯云点播相关问题</p>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success"><?php echo htmlspecialchars($success_message); ?></div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger"><?php echo nl2br(htmlspecialchars($error_message)); ?></div>
    <?php endif; ?>
    
    <!-- 配置状态 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>📋 VOD配置状态</h5>
        </div>
        <div class="card-body">
            <?php if ($vod_config_status['valid']): ?>
                <div class="alert alert-success">✅ VOD配置正常</div>
            <?php else: ?>
                <div class="alert alert-danger">
                    ❌ VOD配置错误：<?php echo implode(', ', $vod_config_status['errors']); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number good"><?php echo $diagnosis['total_lessons']; ?></div>
            <div class="stat-label">总课时数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number <?php echo $diagnosis['vod_lessons'] > 0 ? 'good' : 'warning'; ?>"><?php echo $diagnosis['vod_lessons']; ?></div>
            <div class="stat-label">VOD课时数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number <?php echo $diagnosis['vod_with_file_id'] > 0 ? 'good' : 'warning'; ?>"><?php echo $diagnosis['vod_with_file_id']; ?></div>
            <div class="stat-label">有文件ID</div>
        </div>
        <div class="stat-card">
            <div class="stat-number <?php echo $diagnosis['vod_with_play_url'] > 0 ? 'good' : 'warning'; ?>"><?php echo $diagnosis['vod_with_play_url']; ?></div>
            <div class="stat-label">有播放URL</div>
        </div>
        <div class="stat-card">
            <div class="stat-number <?php echo $diagnosis['vod_missing_url'] == 0 ? 'good' : 'danger'; ?>"><?php echo $diagnosis['vod_missing_url']; ?></div>
            <div class="stat-label">缺失播放URL</div>
        </div>
        <div class="stat-card">
            <div class="stat-number <?php echo $diagnosis['invalid_vod'] == 0 ? 'good' : 'danger'; ?>"><?php echo $diagnosis['invalid_vod']; ?></div>
            <div class="stat-label">无效VOD记录</div>
        </div>
    </div>
    
    <!-- 修复操作 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>🔧 修复操作</h5>
        </div>
        <div class="card-body">
            <div class="action-buttons">
                <?php if ($diagnosis['vod_missing_url'] > 0): ?>
                    <form method="POST" style="display: inline;" onsubmit="return confirm('确定要修复缺失播放URL的课时吗？这可能需要一些时间。');">
                        <input type="hidden" name="action" value="fix_missing_urls">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-wrench"></i> 修复缺失播放URL (<?php echo $diagnosis['vod_missing_url']; ?>个)
                        </button>
                    </form>
                <?php endif; ?>
                
                <?php if ($diagnosis['invalid_vod'] > 0): ?>
                    <form method="POST" style="display: inline;" onsubmit="return confirm('确定要清理无效的VOD记录吗？这将把它们转换为普通视频链接类型。');">
                        <input type="hidden" name="action" value="clean_invalid_vod">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-broom"></i> 清理无效VOD记录 (<?php echo $diagnosis['invalid_vod']; ?>个)
                        </button>
                    </form>
                <?php endif; ?>
                
                <a href="vod-status-checker.php" class="btn btn-info">
                    <i class="fas fa-list"></i> VOD状态管理器
                </a>
            </div>
        </div>
    </div>
    
    <!-- 问题课时列表 -->
    <?php if ($problem_lessons_result->num_rows > 0): ?>
        <div class="card">
            <div class="card-header">
                <h5>⚠️ 问题课时列表</h5>
            </div>
            <div class="card-body">
                <table class="problem-table">
                    <thead>
                        <tr>
                            <th>课时标题</th>
                            <th>所属课程</th>
                            <th>问题类型</th>
                            <th>文件ID</th>
                            <th>播放URL</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($lesson = $problem_lessons_result->fetch_assoc()): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($lesson['title']); ?></td>
                                <td><?php echo htmlspecialchars($lesson['course_title']); ?></td>
                                <td>
                                    <?php if (empty($lesson['vod_file_id'])): ?>
                                        <span class="status-badge status-invalid">无效VOD</span>
                                    <?php else: ?>
                                        <span class="status-badge status-missing">缺失播放URL</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($lesson['vod_file_id'])): ?>
                                        <code><?php echo htmlspecialchars($lesson['vod_file_id']); ?></code>
                                    <?php else: ?>
                                        <span class="text-muted">无</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($lesson['vod_video_url'])): ?>
                                        <a href="<?php echo htmlspecialchars($lesson['vod_video_url']); ?>" target="_blank" class="btn btn-sm btn-outline-primary">播放</a>
                                    <?php else: ?>
                                        <span class="text-muted">无</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('Y-m-d H:i', strtotime($lesson['created_at'])); ?></td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php else: ?>
        <div class="card">
            <div class="card-body text-center">
                <h5>✅ 没有发现问题课时</h5>
                <p class="text-muted">所有VOD课时状态正常</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php render_admin_footer(); ?>
