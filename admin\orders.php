<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

$success_message = '';
$error_message = '';

// 处理订单操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $order_id = intval($_POST['order_id'] ?? 0);
    
    if ($action === 'cancel_order' && $order_id > 0) {
        $stmt = $conn->prepare("
            UPDATE orders 
            SET order_status = 'cancelled', updated_at = NOW() 
            WHERE id = ? AND order_status = 'pending'
        ");
        $stmt->bind_param("i", $order_id);
        if ($stmt->execute() && $stmt->affected_rows > 0) {
            $success_message = '订单已取消';
        } else {
            $error_message = '取消订单失败或订单状态不允许取消';
        }
    }
    
    if ($action === 'refund_order' && $order_id > 0) {
        $refund_reason = trim($_POST['refund_reason'] ?? '');
        if (empty($refund_reason)) {
            $error_message = '请填写退款原因';
        } else {
            try {
                $conn->begin_transaction();
                
                // 获取订单信息
                $stmt = $conn->prepare("
                    SELECT * FROM orders 
                    WHERE id = ? AND order_status = 'paid' AND payment_status = 'paid'
                ");
                $stmt->bind_param("i", $order_id);
                $stmt->execute();
                $order = $stmt->get_result()->fetch_assoc();
                
                if (!$order) {
                    throw new Exception('订单不存在或状态不允许退款');
                }
                
                // 生成退款单号
                $refund_no = 'REF' . date('YmdHis') . sprintf('%06d', $order_id) . sprintf('%04d', rand(1000, 9999));
                
                // 获取支付记录
                $stmt = $conn->prepare("
                    SELECT * FROM payments 
                    WHERE order_id = ? AND payment_status = 'success' 
                    ORDER BY created_at DESC LIMIT 1
                ");
                $stmt->bind_param("i", $order_id);
                $stmt->execute();
                $payment = $stmt->get_result()->fetch_assoc();
                
                if (!$payment) {
                    throw new Exception('未找到支付记录');
                }
                
                // 创建退款记录
                $stmt = $conn->prepare("
                    INSERT INTO refunds (refund_no, order_id, payment_id, user_id, refund_amount, 
                                       refund_reason, refund_status, processed_by, processed_at, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, 'pending', ?, NOW(), NOW())
                ");
                $admin_id = $_SESSION['admin_id'] ?? 1;
                $stmt->bind_param("siiiisi", 
                    $refund_no, $order_id, $payment['id'], $order['user_id'], 
                    $order['actual_amount'], $refund_reason, $admin_id
                );
                $stmt->execute();
                
                // 更新订单状态
                $stmt = $conn->prepare("
                    UPDATE orders 
                    SET order_status = 'refunded', payment_status = 'refunding', updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->bind_param("i", $order_id);
                $stmt->execute();
                
                // 撤销用户课程权限
                $stmt = $conn->prepare("
                    UPDATE user_courses 
                    SET status = 'revoked' 
                    WHERE order_id = ?
                ");
                $stmt->bind_param("i", $order_id);
                $stmt->execute();
                
                $conn->commit();
                $success_message = '退款申请已创建，退款单号：' . $refund_no;
                
            } catch (Exception $e) {
                $conn->rollback();
                $error_message = $e->getMessage();
            }
        }
    }
}

// 获取查询参数
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;
$search = trim($_GET['search'] ?? '');
$status_filter = $_GET['status'] ?? '';
$payment_status_filter = $_GET['payment_status'] ?? '';

// 构建查询条件
$where_conditions = [];
$params = [];
$param_types = "";

if (!empty($search)) {
    $where_conditions[] = "(o.order_no LIKE ? OR u.name LIKE ? OR u.email LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
    $param_types .= "sss";
}

if (!empty($status_filter)) {
    $where_conditions[] = "o.order_status = ?";
    $params[] = $status_filter;
    $param_types .= "s";
}

if (!empty($payment_status_filter)) {
    $where_conditions[] = "o.payment_status = ?";
    $params[] = $payment_status_filter;
    $param_types .= "s";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// 查询订单总数
$count_sql = "
    SELECT COUNT(*) as total 
    FROM orders o 
    LEFT JOIN users u ON o.user_id = u.id 
    $where_clause
";
$stmt = $conn->prepare($count_sql);
if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}
$stmt->execute();
$total = $stmt->get_result()->fetch_assoc()['total'];

// 查询订单列表
$sql = "
    SELECT 
        o.*,
        u.name as user_name,
        u.email as user_email,
        COUNT(oi.id) as item_count
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    LEFT JOIN order_items oi ON o.id = oi.order_id
    $where_clause
    GROUP BY o.id
    ORDER BY o.created_at DESC
    LIMIT ? OFFSET ?
";

$params[] = $limit;
$params[] = $offset;
$param_types .= "ii";

$stmt = $conn->prepare($sql);
$stmt->bind_param($param_types, ...$params);
$stmt->execute();
$orders = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// 渲染页面头部
render_admin_header('订单管理', 'orders');
?>

<!-- 显示消息 -->
<?php if ($success_message): ?>
    <?php show_success_message($success_message); ?>
<?php endif; ?>

<?php if ($error_message): ?>
    <?php show_error_message($error_message); ?>
<?php endif; ?>

<!-- 搜索和筛选 -->
<?php render_card_start('搜索订单'); ?>
    <form method="GET" class="admin-search-form">
        <div class="admin-form-row">
            <div class="admin-form-col">
                <input class="admin-form-input" type="text" name="search" 
                       value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="搜索订单号、用户姓名或邮箱">
            </div>
            <div class="admin-form-col">
                <select class="admin-form-input" name="status">
                    <option value="">全部订单状态</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>待支付</option>
                    <option value="paid" <?php echo $status_filter === 'paid' ? 'selected' : ''; ?>>已支付</option>
                    <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>已取消</option>
                    <option value="refunded" <?php echo $status_filter === 'refunded' ? 'selected' : ''; ?>>已退款</option>
                    <option value="expired" <?php echo $status_filter === 'expired' ? 'selected' : ''; ?>>已过期</option>
                </select>
            </div>
            <div class="admin-form-col">
                <select class="admin-form-input" name="payment_status">
                    <option value="">全部支付状态</option>
                    <option value="unpaid" <?php echo $payment_status_filter === 'unpaid' ? 'selected' : ''; ?>>未支付</option>
                    <option value="paid" <?php echo $payment_status_filter === 'paid' ? 'selected' : ''; ?>>已支付</option>
                    <option value="refunding" <?php echo $payment_status_filter === 'refunding' ? 'selected' : ''; ?>>退款中</option>
                    <option value="refunded" <?php echo $payment_status_filter === 'refunded' ? 'selected' : ''; ?>>已退款</option>
                    <option value="failed" <?php echo $payment_status_filter === 'failed' ? 'selected' : ''; ?>>支付失败</option>
                </select>
            </div>
            <div class="admin-form-col">
                <?php render_button('搜索', 'submit', 'admin-btn-primary'); ?>
                <a href="orders.php" class="admin-btn admin-btn-secondary">重置</a>
            </div>
        </div>
    </form>
<?php render_card_end(); ?>

<!-- 订单列表 -->
<?php render_card_start('订单列表 (' . $total . ' 条记录)'); ?>
    <?php if (empty($orders)): ?>
        <div class="admin-empty-state">
            <p>暂无订单数据</p>
        </div>
    <?php else: ?>
        <div class="admin-table-container">
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>订单号</th>
                        <th>用户</th>
                        <th>商品数量</th>
                        <th>订单金额</th>
                        <th>订单状态</th>
                        <th>支付状态</th>
                        <th>支付方式</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($orders as $order): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($order['order_no']); ?></strong>
                            </td>
                            <td>
                                <div>
                                    <strong><?php echo htmlspecialchars($order['user_name']); ?></strong><br>
                                    <small><?php echo htmlspecialchars($order['user_email']); ?></small>
                                </div>
                            </td>
                            <td><?php echo $order['item_count']; ?> 个课程</td>
                            <td>
                                <div>
                                    <strong>¥<?php echo number_format($order['actual_amount'], 2); ?></strong><br>
                                    <?php if ($order['discount_amount'] > 0): ?>
                                        <small>原价: ¥<?php echo number_format($order['total_amount'], 2); ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php 
                                $status_colors = [
                                    'pending' => 'warning',
                                    'paid' => 'success', 
                                    'cancelled' => 'secondary',
                                    'refunded' => 'info',
                                    'expired' => 'danger'
                                ];
                                $status_texts = [
                                    'pending' => '待支付',
                                    'paid' => '已支付',
                                    'cancelled' => '已取消', 
                                    'refunded' => '已退款',
                                    'expired' => '已过期'
                                ];
                                $color = $status_colors[$order['order_status']] ?? 'secondary';
                                $text = $status_texts[$order['order_status']] ?? $order['order_status'];
                                ?>
                                <span class="admin-badge admin-badge-<?php echo $color; ?>">
                                    <?php echo $text; ?>
                                </span>
                            </td>
                            <td>
                                <?php 
                                $payment_colors = [
                                    'unpaid' => 'warning',
                                    'paid' => 'success',
                                    'refunding' => 'info', 
                                    'refunded' => 'secondary',
                                    'failed' => 'danger'
                                ];
                                $payment_texts = [
                                    'unpaid' => '未支付',
                                    'paid' => '已支付',
                                    'refunding' => '退款中',
                                    'refunded' => '已退款', 
                                    'failed' => '支付失败'
                                ];
                                $color = $payment_colors[$order['payment_status']] ?? 'secondary';
                                $text = $payment_texts[$order['payment_status']] ?? $order['payment_status'];
                                ?>
                                <span class="admin-badge admin-badge-<?php echo $color; ?>">
                                    <?php echo $text; ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($order['payment_method']): ?>
                                    <?php echo $order['payment_method'] === 'wechat' ? '微信支付' : $order['payment_method']; ?>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <small><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></small>
                            </td>
                            <td>
                                <div class="admin-actions">
                                    <a href="order-detail.php?id=<?php echo $order['id']; ?>" 
                                       class="admin-btn admin-btn-sm admin-btn-info">详情</a>
                                    
                                    <?php if ($order['order_status'] === 'pending'): ?>
                                        <button onclick="cancelOrder(<?php echo $order['id']; ?>)" 
                                                class="admin-btn admin-btn-sm admin-btn-warning">取消</button>
                                    <?php endif; ?>
                                    
                                    <?php if ($order['order_status'] === 'paid' && $order['payment_status'] === 'paid'): ?>
                                        <button onclick="refundOrder(<?php echo $order['id']; ?>)" 
                                                class="admin-btn admin-btn-sm admin-btn-danger">退款</button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <?php if ($total > $limit): ?>
            <?php render_pagination($page, ceil($total / $limit), 'orders.php', $_GET); ?>
        <?php endif; ?>
    <?php endif; ?>
<?php render_card_end(); ?>

<!-- 操作表单 -->
<form id="actionForm" method="POST" style="display: none;">
    <input type="hidden" name="action" id="actionType">
    <input type="hidden" name="order_id" id="actionOrderId">
    <input type="hidden" name="refund_reason" id="refundReason">
</form>

<script>
function cancelOrder(orderId) {
    if (confirm('确定要取消这个订单吗？')) {
        document.getElementById('actionType').value = 'cancel_order';
        document.getElementById('actionOrderId').value = orderId;
        document.getElementById('actionForm').submit();
    }
}

function refundOrder(orderId) {
    const reason = prompt('请输入退款原因：');
    if (reason && reason.trim()) {
        if (confirm('确定要退款这个订单吗？\n退款原因：' + reason)) {
            document.getElementById('actionType').value = 'refund_order';
            document.getElementById('actionOrderId').value = orderId;
            document.getElementById('refundReason').value = reason.trim();
            document.getElementById('actionForm').submit();
        }
    }
}
</script>

<?php render_admin_footer(); ?>
