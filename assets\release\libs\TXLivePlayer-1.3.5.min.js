!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).TXLivePlayer=e()}(this,(function(){"use strict";function t(t,e){return e.forEach((function(e){e&&"string"!=typeof e&&!Array.isArray(e)&&Object.keys(e).forEach((function(r){if("default"!==r&&!(r in t)){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}}))})),Object.freeze(t)}var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var n=function(t){try{return!!t()}catch(t){return!0}},i=!n((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),o=i,a=Function.prototype,s=a.bind,c=a.call,u=o&&s.bind(c,c),l=o?function(t){return t&&u(t)}:function(t){return t&&function(){return c.apply(t,arguments)}},d=l({}.isPrototypeOf),p=function(t){return t&&t.Math==Math&&t},f=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof e&&e)||function(){return this}()||Function("return this")(),h=i,m=Function.prototype,v=m.apply,y=m.call,g="object"==typeof Reflect&&Reflect.apply||(h?y.bind(v):function(){return y.apply(v,arguments)}),b=function(t){return"function"==typeof t},C={},S=!n((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),T=i,_=Function.prototype.call,R=T?_.bind(_):function(){return _.apply(_,arguments)},E={},w={}.propertyIsEnumerable,P=Object.getOwnPropertyDescriptor,k=P&&!w.call({1:2},1);E.f=k?function(t){var e=P(this,t);return!!e&&e.enumerable}:w;var A,I,x=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},L=l,D=L({}.toString),N=L("".slice),O=function(t){return N(D(t),8,-1)},M=n,U=O,V=Object,F=l("".split),j=M((function(){return!V("z").propertyIsEnumerable(0)}))?function(t){return"String"==U(t)?F(t,""):V(t)}:V,B=TypeError,W=function(t){if(null==t)throw B("Can't call method on "+t);return t},G=j,Z=W,Y=function(t){return G(Z(t))},H=b,J=function(t){return"object"==typeof t?null!==t:H(t)},X={},K=X,q=f,Q=b,z=function(t){return Q(t)?t:void 0},$=function(t,e){return arguments.length<2?z(K[t])||z(q[t]):K[t]&&K[t][e]||q[t]&&q[t][e]},tt=$("navigator","userAgent")||"",et=f,rt=tt,nt=et.process,it=et.Deno,ot=nt&&nt.versions||it&&it.version,at=ot&&ot.v8;at&&(I=(A=at.split("."))[0]>0&&A[0]<4?1:+(A[0]+A[1])),!I&&rt&&(!(A=rt.match(/Edge\/(\d+)/))||A[1]>=74)&&(A=rt.match(/Chrome\/(\d+)/))&&(I=+A[1]);var st=I,ct=st,ut=n,lt=!!Object.getOwnPropertySymbols&&!ut((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ct&&ct<41})),dt=lt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,pt=$,ft=b,ht=d,mt=Object,vt=dt?function(t){return"symbol"==typeof t}:function(t){var e=pt("Symbol");return ft(e)&&ht(e.prototype,mt(t))},yt=String,gt=function(t){try{return yt(t)}catch(t){return"Object"}},bt=b,Ct=gt,St=TypeError,Tt=function(t){if(bt(t))return t;throw St(Ct(t)+" is not a function")},_t=Tt,Rt=function(t,e){var r=t[e];return null==r?void 0:_t(r)},Et=R,wt=b,Pt=J,kt=TypeError,At={exports:{}},It=f,xt=Object.defineProperty,Lt=function(t,e){try{xt(It,t,{value:e,configurable:!0,writable:!0})}catch(r){It[t]=e}return e},Dt="__core-js_shared__",Nt=f[Dt]||Lt(Dt,{}),Ot=Nt;(At.exports=function(t,e){return Ot[t]||(Ot[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.23.3",mode:"pure",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.23.3/LICENSE",source:"https://github.com/zloirock/core-js"});var Mt=W,Ut=Object,Vt=function(t){return Ut(Mt(t))},Ft=Vt,jt=l({}.hasOwnProperty),Bt=Object.hasOwn||function(t,e){return jt(Ft(t),e)},Wt=l,Gt=0,Zt=Math.random(),Yt=Wt(1..toString),Ht=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Yt(++Gt+Zt,36)},Jt=f,Xt=At.exports,Kt=Bt,qt=Ht,Qt=lt,zt=dt,$t=Xt("wks"),te=Jt.Symbol,ee=te&&te.for,re=zt?te:te&&te.withoutSetter||qt,ne=function(t){if(!Kt($t,t)||!Qt&&"string"!=typeof $t[t]){var e="Symbol."+t;Qt&&Kt(te,t)?$t[t]=te[t]:$t[t]=zt&&ee?ee(e):re(e)}return $t[t]},ie=R,oe=J,ae=vt,se=Rt,ce=function(t,e){var r,n;if("string"===e&&wt(r=t.toString)&&!Pt(n=Et(r,t)))return n;if(wt(r=t.valueOf)&&!Pt(n=Et(r,t)))return n;if("string"!==e&&wt(r=t.toString)&&!Pt(n=Et(r,t)))return n;throw kt("Can't convert object to primitive value")},ue=TypeError,le=ne("toPrimitive"),de=function(t,e){if(!oe(t)||ae(t))return t;var r,n=se(t,le);if(n){if(void 0===e&&(e="default"),r=ie(n,t,e),!oe(r)||ae(r))return r;throw ue("Can't convert object to primitive value")}return void 0===e&&(e="number"),ce(t,e)},pe=vt,fe=function(t){var e=de(t,"string");return pe(e)?e:e+""},he=J,me=f.document,ve=he(me)&&he(me.createElement),ye=function(t){return ve?me.createElement(t):{}},ge=ye,be=!S&&!n((function(){return 7!=Object.defineProperty(ge("div"),"a",{get:function(){return 7}}).a})),Ce=S,Se=R,Te=E,_e=x,Re=Y,Ee=fe,we=Bt,Pe=be,ke=Object.getOwnPropertyDescriptor;C.f=Ce?ke:function(t,e){if(t=Re(t),e=Ee(e),Pe)try{return ke(t,e)}catch(t){}if(we(t,e))return _e(!Se(Te.f,t,e),t[e])};var Ae=n,Ie=b,xe=/#|\.prototype\./,Le=function(t,e){var r=Ne[De(t)];return r==Me||r!=Oe&&(Ie(e)?Ae(e):!!e)},De=Le.normalize=function(t){return String(t).replace(xe,".").toLowerCase()},Ne=Le.data={},Oe=Le.NATIVE="N",Me=Le.POLYFILL="P",Ue=Le,Ve=Tt,Fe=i,je=l(l.bind),Be=function(t,e){return Ve(t),void 0===e?t:Fe?je(t,e):function(){return t.apply(e,arguments)}},We={},Ge=S&&n((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Ze=J,Ye=String,He=TypeError,Je=function(t){if(Ze(t))return t;throw He(Ye(t)+" is not an object")},Xe=S,Ke=be,qe=Ge,Qe=Je,ze=fe,$e=TypeError,tr=Object.defineProperty,er=Object.getOwnPropertyDescriptor,rr="enumerable",nr="configurable",ir="writable";We.f=Xe?qe?function(t,e,r){if(Qe(t),e=ze(e),Qe(r),"function"==typeof t&&"prototype"===e&&"value"in r&&ir in r&&!r.writable){var n=er(t,e);n&&n.writable&&(t[e]=r.value,r={configurable:nr in r?r.configurable:n.configurable,enumerable:rr in r?r.enumerable:n.enumerable,writable:!1})}return tr(t,e,r)}:tr:function(t,e,r){if(Qe(t),e=ze(e),Qe(r),Ke)try{return tr(t,e,r)}catch(t){}if("get"in r||"set"in r)throw $e("Accessors not supported");return"value"in r&&(t[e]=r.value),t};var or=We,ar=x,sr=S?function(t,e,r){return or.f(t,e,ar(1,r))}:function(t,e,r){return t[e]=r,t},cr=f,ur=g,lr=l,dr=b,pr=C.f,fr=Ue,hr=X,mr=Be,vr=sr,yr=Bt,gr=function(t){var e=function(r,n,i){if(this instanceof e){switch(arguments.length){case 0:return new t;case 1:return new t(r);case 2:return new t(r,n)}return new t(r,n,i)}return ur(t,this,arguments)};return e.prototype=t.prototype,e},br=function(t,e){var r,n,i,o,a,s,c,u,l=t.target,d=t.global,p=t.stat,f=t.proto,h=d?cr:p?cr[l]:(cr[l]||{}).prototype,m=d?hr:hr[l]||vr(hr,l,{})[l],v=m.prototype;for(i in e)r=!fr(d?i:l+(p?".":"#")+i,t.forced)&&h&&yr(h,i),a=m[i],r&&(s=t.dontCallGetSet?(u=pr(h,i))&&u.value:h[i]),o=r&&s?s:e[i],r&&typeof a==typeof o||(c=t.bind&&r?mr(o,cr):t.wrap&&r?gr(o):f&&dr(o)?lr(o):o,(t.sham||o&&o.sham||a&&a.sham)&&vr(c,"sham",!0),vr(m,i,c),f&&(yr(hr,n=l+"Prototype")||vr(hr,n,{}),vr(hr[n],i,o),t.real&&v&&!v[i]&&vr(v,i,o)))},Cr=l([].slice),Sr=l,Tr=Tt,_r=J,Rr=Bt,Er=Cr,wr=i,Pr=Function,kr=Sr([].concat),Ar=Sr([].join),Ir={},xr=function(t,e,r){if(!Rr(Ir,e)){for(var n=[],i=0;i<e;i++)n[i]="a["+i+"]";Ir[e]=Pr("C,a","return new C("+Ar(n,",")+")")}return Ir[e](t,r)},Lr=wr?Pr.bind:function(t){var e=Tr(this),r=e.prototype,n=Er(arguments,1),i=function(){var r=kr(n,Er(arguments));return this instanceof i?xr(e,r.length,r):e.apply(t,r)};return _r(r)&&(i.prototype=r),i},Dr=Lr;br({target:"Function",proto:!0,forced:Function.bind!==Dr},{bind:Dr});var Nr=X,Or=function(t){return Nr[t+"Prototype"]},Mr=Or("Function").bind,Ur=d,Vr=Mr,Fr=Function.prototype,jr=function(t){var e=t.bind;return t===Fr||Ur(Fr,t)&&e===Fr.bind?Vr:e},Br=O,Wr=Array.isArray||function(t){return"Array"==Br(t)},Gr=Math.ceil,Zr=Math.floor,Yr=Math.trunc||function(t){var e=+t;return(e>0?Zr:Gr)(e)},Hr=function(t){var e=+t;return e!=e||0===e?0:Yr(e)},Jr=Hr,Xr=Math.min,Kr=function(t){return t>0?Xr(Jr(t),9007199254740991):0},qr=Kr,Qr=function(t){return qr(t.length)},zr=TypeError,$r=function(t){if(t>9007199254740991)throw zr("Maximum allowed index exceeded");return t},tn=fe,en=We,rn=x,nn=function(t,e,r){var n=tn(e);n in t?en.f(t,n,rn(0,r)):t[n]=r},on={};on[ne("toStringTag")]="z";var an="[object z]"===String(on),sn=an,cn=b,un=O,ln=ne("toStringTag"),dn=Object,pn="Arguments"==un(function(){return arguments}()),fn=sn?un:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=dn(t),ln))?r:pn?un(e):"Object"==(n=un(e))&&cn(e.callee)?"Arguments":n},hn=b,mn=Nt,vn=l(Function.toString);hn(mn.inspectSource)||(mn.inspectSource=function(t){return vn(t)});var yn=mn.inspectSource,gn=l,bn=n,Cn=b,Sn=fn,Tn=yn,_n=function(){},Rn=[],En=$("Reflect","construct"),wn=/^\s*(?:class|function)\b/,Pn=gn(wn.exec),kn=!wn.exec(_n),An=function(t){if(!Cn(t))return!1;try{return En(_n,Rn,t),!0}catch(t){return!1}},In=function(t){if(!Cn(t))return!1;switch(Sn(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return kn||!!Pn(wn,Tn(t))}catch(t){return!0}};In.sham=!0;var xn=!En||bn((function(){var t;return An(An.call)||!An(Object)||!An((function(){t=!0}))||t}))?In:An,Ln=Wr,Dn=xn,Nn=J,On=ne("species"),Mn=Array,Un=function(t){var e;return Ln(t)&&(e=t.constructor,(Dn(e)&&(e===Mn||Ln(e.prototype))||Nn(e)&&null===(e=e[On]))&&(e=void 0)),void 0===e?Mn:e},Vn=function(t,e){return new(Un(t))(0===e?0:e)},Fn=n,jn=st,Bn=ne("species"),Wn=function(t){return jn>=51||!Fn((function(){var e=[];return(e.constructor={})[Bn]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Gn=br,Zn=n,Yn=Wr,Hn=J,Jn=Vt,Xn=Qr,Kn=$r,qn=nn,Qn=Vn,zn=Wn,$n=st,ti=ne("isConcatSpreadable"),ei=$n>=51||!Zn((function(){var t=[];return t[ti]=!1,t.concat()[0]!==t})),ri=zn("concat"),ni=function(t){if(!Hn(t))return!1;var e=t[ti];return void 0!==e?!!e:Yn(t)};Gn({target:"Array",proto:!0,arity:1,forced:!ei||!ri},{concat:function(t){var e,r,n,i,o,a=Jn(this),s=Qn(a,0),c=0;for(e=-1,n=arguments.length;e<n;e++)if(ni(o=-1===e?a:arguments[e]))for(i=Xn(o),Kn(c+i),r=0;r<i;r++,c++)r in o&&qn(s,c,o[r]);else Kn(c+1),qn(s,c++,o);return s.length=c,s}});var ii=Or("Array").concat,oi=d,ai=ii,si=Array.prototype,ci=function(t){var e=t.concat;return t===si||oi(si,t)&&e===si.concat?ai:e},ui=Hr,li=Math.max,di=Math.min,pi=function(t,e){var r=ui(t);return r<0?li(r+e,0):di(r,e)},fi=Y,hi=pi,mi=Qr,vi=function(t){return function(e,r,n){var i,o=fi(e),a=mi(o),s=hi(n,a);if(t&&r!=r){for(;a>s;)if((i=o[s++])!=i)return!0}else for(;a>s;s++)if((t||s in o)&&o[s]===r)return t||s||0;return!t&&-1}},yi={includes:vi(!0),indexOf:vi(!1)},gi=yi.includes;br({target:"Array",proto:!0,forced:n((function(){return!Array(1).includes()}))},{includes:function(t){return gi(this,t,arguments.length>1?arguments[1]:void 0)}});var bi=Or("Array").includes,Ci=J,Si=O,Ti=ne("match"),_i=function(t){var e;return Ci(t)&&(void 0!==(e=t[Ti])?!!e:"RegExp"==Si(t))},Ri=TypeError,Ei=function(t){if(_i(t))throw Ri("The method doesn't accept regular expressions");return t},wi=fn,Pi=String,ki=function(t){if("Symbol"===wi(t))throw TypeError("Cannot convert a Symbol value to a string");return Pi(t)},Ai=ne("match"),Ii=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[Ai]=!1,"/./"[t](e)}catch(t){}}return!1},xi=br,Li=Ei,Di=W,Ni=ki,Oi=Ii,Mi=l("".indexOf);xi({target:"String",proto:!0,forced:!Oi("includes")},{includes:function(t){return!!~Mi(Ni(Di(this)),Ni(Li(t)),arguments.length>1?arguments[1]:void 0)}});var Ui,Vi,Fi,ji=Or("String").includes,Bi=d,Wi=bi,Gi=ji,Zi=Array.prototype,Yi=String.prototype,Hi=function(t){var e=t.includes;return t===Zi||Bi(Zi,t)&&e===Zi.includes?Wi:"string"==typeof t||t===Yi||Bi(Yi,t)&&e===Yi.includes?Gi:e},Ji={},Xi=b,Ki=yn,qi=f.WeakMap,Qi=Xi(qi)&&/native code/.test(Ki(qi)),zi=At.exports,$i=Ht,to=zi("keys"),eo=function(t){return to[t]||(to[t]=$i(t))},ro={},no=Qi,io=f,oo=l,ao=J,so=sr,co=Bt,uo=Nt,lo=eo,po=ro,fo="Object already initialized",ho=io.TypeError,mo=io.WeakMap;if(no||uo.state){var vo=uo.state||(uo.state=new mo),yo=oo(vo.get),go=oo(vo.has),bo=oo(vo.set);Ui=function(t,e){if(go(vo,t))throw new ho(fo);return e.facade=t,bo(vo,t,e),e},Vi=function(t){return yo(vo,t)||{}},Fi=function(t){return go(vo,t)}}else{var Co=lo("state");po[Co]=!0,Ui=function(t,e){if(co(t,Co))throw new ho(fo);return e.facade=t,so(t,Co,e),e},Vi=function(t){return co(t,Co)?t[Co]:{}},Fi=function(t){return co(t,Co)}}var So={set:Ui,get:Vi,has:Fi,enforce:function(t){return Fi(t)?Vi(t):Ui(t,{})},getterFor:function(t){return function(e){var r;if(!ao(e)||(r=Vi(e)).type!==t)throw ho("Incompatible receiver, "+t+" required");return r}}},To=S,_o=Bt,Ro=Function.prototype,Eo=To&&Object.getOwnPropertyDescriptor,wo=_o(Ro,"name"),Po={EXISTS:wo,PROPER:wo&&"something"===function(){}.name,CONFIGURABLE:wo&&(!To||To&&Eo(Ro,"name").configurable)},ko={},Ao=Bt,Io=Y,xo=yi.indexOf,Lo=ro,Do=l([].push),No=function(t,e){var r,n=Io(t),i=0,o=[];for(r in n)!Ao(Lo,r)&&Ao(n,r)&&Do(o,r);for(;e.length>i;)Ao(n,r=e[i++])&&(~xo(o,r)||Do(o,r));return o},Oo=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Mo=No,Uo=Oo,Vo=Object.keys||function(t){return Mo(t,Uo)},Fo=S,jo=Ge,Bo=We,Wo=Je,Go=Y,Zo=Vo;ko.f=Fo&&!jo?Object.defineProperties:function(t,e){Wo(t);for(var r,n=Go(e),i=Zo(e),o=i.length,a=0;o>a;)Bo.f(t,r=i[a++],n[r]);return t};var Yo,Ho=$("document","documentElement"),Jo=Je,Xo=ko,Ko=Oo,qo=ro,Qo=Ho,zo=ye,$o=eo("IE_PROTO"),ta=function(){},ea=function(t){return"<script>"+t+"</"+"script>"},ra=function(t){t.write(ea("")),t.close();var e=t.parentWindow.Object;return t=null,e},na=function(){try{Yo=new ActiveXObject("htmlfile")}catch(t){}var t,e;na="undefined"!=typeof document?document.domain&&Yo?ra(Yo):((e=zo("iframe")).style.display="none",Qo.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(ea("document.F=Object")),t.close(),t.F):ra(Yo);for(var r=Ko.length;r--;)delete na.prototype[Ko[r]];return na()};qo[$o]=!0;var ia,oa,aa,sa=Object.create||function(t,e){var r;return null!==t?(ta.prototype=Jo(t),r=new ta,ta.prototype=null,r[$o]=t):r=na(),void 0===e?r:Xo.f(r,e)},ca=!n((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),ua=Bt,la=b,da=Vt,pa=ca,fa=eo("IE_PROTO"),ha=Object,ma=ha.prototype,va=pa?ha.getPrototypeOf:function(t){var e=da(t);if(ua(e,fa))return e[fa];var r=e.constructor;return la(r)&&e instanceof r?r.prototype:e instanceof ha?ma:null},ya=sr,ga=function(t,e,r,n){return n&&n.enumerable?t[e]=r:ya(t,e,r),t},ba=n,Ca=b,Sa=sa,Ta=va,_a=ga,Ra=ne("iterator"),Ea=!1;[].keys&&("next"in(aa=[].keys())?(oa=Ta(Ta(aa)))!==Object.prototype&&(ia=oa):Ea=!0);var wa=null==ia||ba((function(){var t={};return ia[Ra].call(t)!==t}));Ca((ia=wa?{}:Sa(ia))[Ra])||_a(ia,Ra,(function(){return this}));var Pa={IteratorPrototype:ia,BUGGY_SAFARI_ITERATORS:Ea},ka=fn,Aa=an?{}.toString:function(){return"[object "+ka(this)+"]"},Ia=an,xa=We.f,La=sr,Da=Bt,Na=Aa,Oa=ne("toStringTag"),Ma=function(t,e,r,n){if(t){var i=r?t:t.prototype;Da(i,Oa)||xa(i,Oa,{configurable:!0,value:e}),n&&!Ia&&La(i,"toString",Na)}},Ua=Pa.IteratorPrototype,Va=sa,Fa=x,ja=Ma,Ba=Ji,Wa=function(){return this},Ga=b,Za=String,Ya=TypeError,Ha=l,Ja=Je,Xa=function(t){if("object"==typeof t||Ga(t))return t;throw Ya("Can't set "+Za(t)+" as a prototype")},Ka=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=Ha(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return Ja(r),Xa(n),e?t(r,n):r.__proto__=n,r}}():void 0),qa=br,Qa=R,za=function(t,e,r,n){var i=e+" Iterator";return t.prototype=Va(Ua,{next:Fa(+!n,r)}),ja(t,i,!1,!0),Ba[i]=Wa,t},$a=va,ts=Ma,es=ga,rs=Ji,ns=Po.PROPER,is=Pa.BUGGY_SAFARI_ITERATORS,os=ne("iterator"),as="keys",ss="values",cs="entries",us=function(){return this},ls=function(t,e,r,n,i,o,a){za(r,e,n);var s,c,u,l=function(t){if(t===i&&m)return m;if(!is&&t in f)return f[t];switch(t){case as:case ss:case cs:return function(){return new r(this,t)}}return function(){return new r(this)}},d=e+" Iterator",p=!1,f=t.prototype,h=f[os]||f["@@iterator"]||i&&f[i],m=!is&&h||l(i),v="Array"==e&&f.entries||h;if(v&&(s=$a(v.call(new t)))!==Object.prototype&&s.next&&(ts(s,d,!0,!0),rs[d]=us),ns&&i==ss&&h&&h.name!==ss&&(p=!0,m=function(){return Qa(h,this)}),i)if(c={values:l(ss),keys:o?m:l(as),entries:l(cs)},a)for(u in c)(is||p||!(u in f))&&es(f,u,c[u]);else qa({target:e,proto:!0,forced:is||p},c);return a&&f[os]!==m&&es(f,os,m,{name:i}),rs[e]=m,c},ds=Y,ps=Ji,fs=So;We.f;var hs=ls,ms="Array Iterator",vs=fs.set,ys=fs.getterFor(ms);hs(Array,"Array",(function(t,e){vs(this,{type:ms,target:ds(t),index:0,kind:e})}),(function(){var t=ys(this),e=t.target,r=t.kind,n=t.index++;return!e||n>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==r?{value:n,done:!1}:"values"==r?{value:e[n],done:!1}:{value:[n,e[n]],done:!1}}),"values"),ps.Arguments=ps.Array;var gs={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},bs=f,Cs=fn,Ss=sr,Ts=Ji,_s=ne("toStringTag");for(var Rs in gs){var Es=bs[Rs],ws=Es&&Es.prototype;ws&&Cs(ws)!==_s&&Ss(ws,_s,Rs),Ts[Rs]=Ts.Array}var Ps=Be,ks=j,As=Vt,Is=Qr,xs=Vn,Ls=l([].push),Ds=function(t){var e=1==t,r=2==t,n=3==t,i=4==t,o=6==t,a=7==t,s=5==t||o;return function(c,u,l,d){for(var p,f,h=As(c),m=ks(h),v=Ps(u,l),y=Is(m),g=0,b=d||xs,C=e?b(c,y):r||a?b(c,0):void 0;y>g;g++)if((s||g in m)&&(f=v(p=m[g],g,h),t))if(e)C[g]=f;else if(f)switch(t){case 3:return!0;case 5:return p;case 6:return g;case 2:Ls(C,p)}else switch(t){case 4:return!1;case 7:Ls(C,p)}return o?-1:n||i?i:C}},Ns={forEach:Ds(0),map:Ds(1),filter:Ds(2),some:Ds(3),every:Ds(4),find:Ds(5),findIndex:Ds(6),filterReject:Ds(7)},Os=n,Ms=function(t,e){var r=[][t];return!!r&&Os((function(){r.call(null,e||function(){return 1},1)}))},Us=Ns.forEach,Vs=Ms("forEach")?[].forEach:function(t){return Us(this,t,arguments.length>1?arguments[1]:void 0)};br({target:"Array",proto:!0,forced:[].forEach!=Vs},{forEach:Vs});var Fs=Or("Array").forEach,js=fn,Bs=Bt,Ws=d,Gs=Fs,Zs=Array.prototype,Ys={DOMTokenList:!0,NodeList:!0},Hs=function(t){var e=t.forEach;return t===Zs||Ws(Zs,t)&&e===Zs.forEach||Bs(Ys,js(t))?Gs:e},Js=TypeError,Xs=function(t,e){if(t<e)throw Js("Not enough arguments");return t},Ks=f,qs=g,Qs=b,zs=Cr,$s=Xs,tc=/MSIE .\./.test(tt),ec=Ks.Function,rc=function(t){return tc?function(e,r){var n=$s(arguments.length,1)>2,i=Qs(e)?e:ec(e),o=n?zs(arguments,2):void 0;return t(n?function(){qs(i,this,o)}:i,r)}:t},nc={setTimeout:rc(Ks.setTimeout),setInterval:rc(Ks.setInterval)},ic=nc.setInterval;br({global:!0,bind:!0,forced:f.setInterval!==ic},{setInterval:ic});var oc=nc.setTimeout;br({global:!0,bind:!0,forced:f.setTimeout!==oc},{setTimeout:oc});var ac=X.setInterval,sc=X.setTimeout,cc=br,uc=yi.indexOf,lc=Ms,dc=l([].indexOf),pc=!!dc&&1/dc([1],1,-0)<0,fc=lc("indexOf");cc({target:"Array",proto:!0,forced:pc||!fc},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return pc?dc(this,t,e)||0:uc(this,t,e)}});var hc=Or("Array").indexOf,mc=d,vc=hc,yc=Array.prototype,gc=function(t){var e=t.indexOf;return t===yc||mc(yc,t)&&e===yc.indexOf?vc:e},bc=br,Cc=l,Sc=Kr,Tc=ki,_c=Ei,Rc=W,Ec=Ii,wc=Cc("".startsWith),Pc=Cc("".slice),kc=Math.min;bc({target:"String",proto:!0,forced:!Ec("startsWith")},{startsWith:function(t){var e=Tc(Rc(this));_c(t);var r=Sc(kc(arguments.length>1?arguments[1]:void 0,e.length)),n=Tc(t);return wc?wc(e,n,r):Pc(e,r,r+n.length)===n}});var Ac=Or("String").startsWith,Ic=d,xc=Ac,Lc=String.prototype,Dc=function(t){var e=t.startsWith;return"string"==typeof t||t===Lc||Ic(Lc,t)&&e===Lc.startsWith?xc:e},Nc=Ns.filter;br({target:"Array",proto:!0,forced:!Wn("filter")},{filter:function(t){return Nc(this,t,arguments.length>1?arguments[1]:void 0)}});var Oc=Or("Array").filter,Mc=d,Uc=Oc,Vc=Array.prototype,Fc=function(t){var e=t.filter;return t===Vc||Mc(Vc,t)&&e===Vc.filter?Uc:e},jc=function(t,e){return jc=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},jc(t,e)};function Bc(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}jc(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var Wc=function(){return Wc=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},Wc.apply(this,arguments)};function Gc(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(t);i<n.length;i++)e.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(t,n[i])&&(r[n[i]]=t[n[i]])}return r}function Zc(t,e,r,n){return new(r||(r=Promise))((function(i,o){function a(t){try{c(n.next(t))}catch(t){o(t)}}function s(t){try{c(n.throw(t))}catch(t){o(t)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(a,s)}c((n=n.apply(t,e||[])).next())}))}function Yc(t,e){var r,n,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(i=2&s[0]?n.return:s[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){a.label=s[1];break}if(6===s[0]&&a.label<i[1]){a.label=i[1],i=s;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(s);break}i[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],n=0}finally{r=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}function Hc(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function Jc(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a}function Xc(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}let Kc=!0,qc=!0;function Qc(t,e,r){const n=t.match(e);return n&&n.length>=r&&parseInt(n[r],10)}function zc(t,e,r){if(!t.RTCPeerConnection)return;const n=t.RTCPeerConnection.prototype,i=n.addEventListener;n.addEventListener=function(t,n){if(t!==e)return i.apply(this,arguments);const o=t=>{const e=r(t);e&&(n.handleEvent?n.handleEvent(e):n(e))};return this._eventMap=this._eventMap||{},this._eventMap[e]||(this._eventMap[e]=new Map),this._eventMap[e].set(n,o),i.apply(this,[t,o])};const o=n.removeEventListener;n.removeEventListener=function(t,r){if(t!==e||!this._eventMap||!this._eventMap[e])return o.apply(this,arguments);if(!this._eventMap[e].has(r))return o.apply(this,arguments);const n=this._eventMap[e].get(r);return this._eventMap[e].delete(r),0===this._eventMap[e].size&&delete this._eventMap[e],0===Object.keys(this._eventMap).length&&delete this._eventMap,o.apply(this,[t,n])},Object.defineProperty(n,"on"+e,{get(){return this["_on"+e]},set(t){this["_on"+e]&&(this.removeEventListener(e,this["_on"+e]),delete this["_on"+e]),t&&this.addEventListener(e,this["_on"+e]=t)},enumerable:!0,configurable:!0})}function $c(t){return"boolean"!=typeof t?new Error("Argument type: "+typeof t+". Please use a boolean."):(Kc=t,t?"adapter.js logging disabled":"adapter.js logging enabled")}function tu(t){return"boolean"!=typeof t?new Error("Argument type: "+typeof t+". Please use a boolean."):(qc=!t,"adapter.js deprecation warnings "+(t?"disabled":"enabled"))}function eu(){if("object"==typeof window){if(Kc)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}}function ru(t,e){qc&&console.warn(t+" is deprecated, please use "+e+" instead.")}function nu(t){return"[object Object]"===Object.prototype.toString.call(t)}function iu(t){return nu(t)?Object.keys(t).reduce((function(e,r){const n=nu(t[r]),i=n?iu(t[r]):t[r],o=n&&!Object.keys(i).length;return void 0===i||o?e:Object.assign(e,{[r]:i})}),{}):t}function ou(t,e,r){e&&!r.has(e.id)&&(r.set(e.id,e),Object.keys(e).forEach((n=>{n.endsWith("Id")?ou(t,t.get(e[n]),r):n.endsWith("Ids")&&e[n].forEach((e=>{ou(t,t.get(e),r)}))})))}function au(t,e,r){const n=r?"outbound-rtp":"inbound-rtp",i=new Map;if(null===e)return i;const o=[];return t.forEach((t=>{"track"===t.type&&t.trackIdentifier===e.id&&o.push(t)})),o.forEach((e=>{t.forEach((r=>{r.type===n&&r.trackId===e.id&&ou(t,r,i)}))})),i}const su=eu;function cu(t,e){const r=t&&t.navigator;if(!r.mediaDevices)return;const n=function(t){if("object"!=typeof t||t.mandatory||t.optional)return t;const e={};return Object.keys(t).forEach((r=>{if("require"===r||"advanced"===r||"mediaSource"===r)return;const n="object"==typeof t[r]?t[r]:{ideal:t[r]};void 0!==n.exact&&"number"==typeof n.exact&&(n.min=n.max=n.exact);const i=function(t,e){return t?t+e.charAt(0).toUpperCase()+e.slice(1):"deviceId"===e?"sourceId":e};if(void 0!==n.ideal){e.optional=e.optional||[];let t={};"number"==typeof n.ideal?(t[i("min",r)]=n.ideal,e.optional.push(t),t={},t[i("max",r)]=n.ideal,e.optional.push(t)):(t[i("",r)]=n.ideal,e.optional.push(t))}void 0!==n.exact&&"number"!=typeof n.exact?(e.mandatory=e.mandatory||{},e.mandatory[i("",r)]=n.exact):["min","max"].forEach((t=>{void 0!==n[t]&&(e.mandatory=e.mandatory||{},e.mandatory[i(t,r)]=n[t])}))})),t.advanced&&(e.optional=(e.optional||[]).concat(t.advanced)),e},i=function(t,i){if(e.version>=61)return i(t);if((t=JSON.parse(JSON.stringify(t)))&&"object"==typeof t.audio){const e=function(t,e,r){e in t&&!(r in t)&&(t[r]=t[e],delete t[e])};e((t=JSON.parse(JSON.stringify(t))).audio,"autoGainControl","googAutoGainControl"),e(t.audio,"noiseSuppression","googNoiseSuppression"),t.audio=n(t.audio)}if(t&&"object"==typeof t.video){let o=t.video.facingMode;o=o&&("object"==typeof o?o:{ideal:o});const a=e.version<66;if(o&&("user"===o.exact||"environment"===o.exact||"user"===o.ideal||"environment"===o.ideal)&&(!r.mediaDevices.getSupportedConstraints||!r.mediaDevices.getSupportedConstraints().facingMode||a)){let e;if(delete t.video.facingMode,"environment"===o.exact||"environment"===o.ideal?e=["back","rear"]:"user"!==o.exact&&"user"!==o.ideal||(e=["front"]),e)return r.mediaDevices.enumerateDevices().then((r=>{let a=(r=r.filter((t=>"videoinput"===t.kind))).find((t=>e.some((e=>t.label.toLowerCase().includes(e)))));return!a&&r.length&&e.includes("back")&&(a=r[r.length-1]),a&&(t.video.deviceId=o.exact?{exact:a.deviceId}:{ideal:a.deviceId}),t.video=n(t.video),su("chrome: "+JSON.stringify(t)),i(t)}))}t.video=n(t.video)}return su("chrome: "+JSON.stringify(t)),i(t)},o=function(t){return e.version>=64?t:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[t.name]||t.name,message:t.message,constraint:t.constraint||t.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(r.getUserMedia=function(t,e,n){i(t,(t=>{r.webkitGetUserMedia(t,e,(t=>{n&&n(o(t))}))}))}.bind(r),r.mediaDevices.getUserMedia){const t=r.mediaDevices.getUserMedia.bind(r.mediaDevices);r.mediaDevices.getUserMedia=function(e){return i(e,(e=>t(e).then((t=>{if(e.audio&&!t.getAudioTracks().length||e.video&&!t.getVideoTracks().length)throw t.getTracks().forEach((t=>{t.stop()})),new DOMException("","NotFoundError");return t}),(t=>Promise.reject(o(t))))))}}}function uu(t){t.MediaStream=t.MediaStream||t.webkitMediaStream}function lu(t){if("object"==typeof t&&t.RTCPeerConnection&&!("ontrack"in t.RTCPeerConnection.prototype)){Object.defineProperty(t.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(t){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=t)},enumerable:!0,configurable:!0});const e=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=e=>{e.stream.addEventListener("addtrack",(r=>{let n;n=t.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((t=>t.track&&t.track.id===r.track.id)):{track:r.track};const i=new Event("track");i.track=r.track,i.receiver=n,i.transceiver={receiver:n},i.streams=[e.stream],this.dispatchEvent(i)})),e.stream.getTracks().forEach((r=>{let n;n=t.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((t=>t.track&&t.track.id===r.id)):{track:r};const i=new Event("track");i.track=r,i.receiver=n,i.transceiver={receiver:n},i.streams=[e.stream],this.dispatchEvent(i)}))},this.addEventListener("addstream",this._ontrackpoly)),e.apply(this,arguments)}}else zc(t,"track",(t=>(t.transceiver||Object.defineProperty(t,"transceiver",{value:{receiver:t.receiver}}),t)))}function du(t){if("object"==typeof t&&t.RTCPeerConnection&&!("getSenders"in t.RTCPeerConnection.prototype)&&"createDTMFSender"in t.RTCPeerConnection.prototype){const e=function(t,e){return{track:e,get dtmf(){return void 0===this._dtmf&&("audio"===e.kind?this._dtmf=t.createDTMFSender(e):this._dtmf=null),this._dtmf},_pc:t}};if(!t.RTCPeerConnection.prototype.getSenders){t.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const r=t.RTCPeerConnection.prototype.addTrack;t.RTCPeerConnection.prototype.addTrack=function(t,n){let i=r.apply(this,arguments);return i||(i=e(this,t),this._senders.push(i)),i};const n=t.RTCPeerConnection.prototype.removeTrack;t.RTCPeerConnection.prototype.removeTrack=function(t){n.apply(this,arguments);const e=this._senders.indexOf(t);-1!==e&&this._senders.splice(e,1)}}const r=t.RTCPeerConnection.prototype.addStream;t.RTCPeerConnection.prototype.addStream=function(t){this._senders=this._senders||[],r.apply(this,[t]),t.getTracks().forEach((t=>{this._senders.push(e(this,t))}))};const n=t.RTCPeerConnection.prototype.removeStream;t.RTCPeerConnection.prototype.removeStream=function(t){this._senders=this._senders||[],n.apply(this,[t]),t.getTracks().forEach((t=>{const e=this._senders.find((e=>e.track===t));e&&this._senders.splice(this._senders.indexOf(e),1)}))}}else if("object"==typeof t&&t.RTCPeerConnection&&"getSenders"in t.RTCPeerConnection.prototype&&"createDTMFSender"in t.RTCPeerConnection.prototype&&t.RTCRtpSender&&!("dtmf"in t.RTCRtpSender.prototype)){const e=t.RTCPeerConnection.prototype.getSenders;t.RTCPeerConnection.prototype.getSenders=function(){const t=e.apply(this,[]);return t.forEach((t=>t._pc=this)),t},Object.defineProperty(t.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function pu(t){if(!t.RTCPeerConnection)return;const e=t.RTCPeerConnection.prototype.getStats;t.RTCPeerConnection.prototype.getStats=function(){const[t,r,n]=arguments;if(arguments.length>0&&"function"==typeof t)return e.apply(this,arguments);if(0===e.length&&(0===arguments.length||"function"!=typeof t))return e.apply(this,[]);const i=function(t){const e={};return t.result().forEach((t=>{const r={id:t.id,timestamp:t.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[t.type]||t.type};t.names().forEach((e=>{r[e]=t.stat(e)})),e[r.id]=r})),e},o=function(t){return new Map(Object.keys(t).map((e=>[e,t[e]])))};if(arguments.length>=2){const n=function(t){r(o(i(t)))};return e.apply(this,[n,t])}return new Promise(((t,r)=>{e.apply(this,[function(e){t(o(i(e)))},r])})).then(r,n)}}function fu(t){if(!("object"==typeof t&&t.RTCPeerConnection&&t.RTCRtpSender&&t.RTCRtpReceiver))return;if(!("getStats"in t.RTCRtpSender.prototype)){const e=t.RTCPeerConnection.prototype.getSenders;e&&(t.RTCPeerConnection.prototype.getSenders=function(){const t=e.apply(this,[]);return t.forEach((t=>t._pc=this)),t});const r=t.RTCPeerConnection.prototype.addTrack;r&&(t.RTCPeerConnection.prototype.addTrack=function(){const t=r.apply(this,arguments);return t._pc=this,t}),t.RTCRtpSender.prototype.getStats=function(){const t=this;return this._pc.getStats().then((e=>au(e,t.track,!0)))}}if(!("getStats"in t.RTCRtpReceiver.prototype)){const e=t.RTCPeerConnection.prototype.getReceivers;e&&(t.RTCPeerConnection.prototype.getReceivers=function(){const t=e.apply(this,[]);return t.forEach((t=>t._pc=this)),t}),zc(t,"track",(t=>(t.receiver._pc=t.srcElement,t))),t.RTCRtpReceiver.prototype.getStats=function(){const t=this;return this._pc.getStats().then((e=>au(e,t.track,!1)))}}if(!("getStats"in t.RTCRtpSender.prototype)||!("getStats"in t.RTCRtpReceiver.prototype))return;const e=t.RTCPeerConnection.prototype.getStats;t.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof t.MediaStreamTrack){const t=arguments[0];let e,r,n;return this.getSenders().forEach((r=>{r.track===t&&(e?n=!0:e=r)})),this.getReceivers().forEach((e=>(e.track===t&&(r?n=!0:r=e),e.track===t))),n||e&&r?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):e?e.getStats():r?r.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return e.apply(this,arguments)}}function hu(t){t.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map((t=>this._shimmedLocalStreams[t][0]))};const e=t.RTCPeerConnection.prototype.addTrack;t.RTCPeerConnection.prototype.addTrack=function(t,r){if(!r)return e.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const n=e.apply(this,arguments);return this._shimmedLocalStreams[r.id]?-1===this._shimmedLocalStreams[r.id].indexOf(n)&&this._shimmedLocalStreams[r.id].push(n):this._shimmedLocalStreams[r.id]=[r,n],n};const r=t.RTCPeerConnection.prototype.addStream;t.RTCPeerConnection.prototype.addStream=function(t){this._shimmedLocalStreams=this._shimmedLocalStreams||{},t.getTracks().forEach((t=>{if(this.getSenders().find((e=>e.track===t)))throw new DOMException("Track already exists.","InvalidAccessError")}));const e=this.getSenders();r.apply(this,arguments);const n=this.getSenders().filter((t=>-1===e.indexOf(t)));this._shimmedLocalStreams[t.id]=[t].concat(n)};const n=t.RTCPeerConnection.prototype.removeStream;t.RTCPeerConnection.prototype.removeStream=function(t){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[t.id],n.apply(this,arguments)};const i=t.RTCPeerConnection.prototype.removeTrack;t.RTCPeerConnection.prototype.removeTrack=function(t){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},t&&Object.keys(this._shimmedLocalStreams).forEach((e=>{const r=this._shimmedLocalStreams[e].indexOf(t);-1!==r&&this._shimmedLocalStreams[e].splice(r,1),1===this._shimmedLocalStreams[e].length&&delete this._shimmedLocalStreams[e]})),i.apply(this,arguments)}}function mu(t,e){if(!t.RTCPeerConnection)return;if(t.RTCPeerConnection.prototype.addTrack&&e.version>=65)return hu(t);const r=t.RTCPeerConnection.prototype.getLocalStreams;t.RTCPeerConnection.prototype.getLocalStreams=function(){const t=r.apply(this);return this._reverseStreams=this._reverseStreams||{},t.map((t=>this._reverseStreams[t.id]))};const n=t.RTCPeerConnection.prototype.addStream;t.RTCPeerConnection.prototype.addStream=function(e){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},e.getTracks().forEach((t=>{if(this.getSenders().find((e=>e.track===t)))throw new DOMException("Track already exists.","InvalidAccessError")})),!this._reverseStreams[e.id]){const r=new t.MediaStream(e.getTracks());this._streams[e.id]=r,this._reverseStreams[r.id]=e,e=r}n.apply(this,[e])};const i=t.RTCPeerConnection.prototype.removeStream;function o(t,e){let r=e.sdp;return Object.keys(t._reverseStreams||[]).forEach((e=>{const n=t._reverseStreams[e],i=t._streams[n.id];r=r.replace(new RegExp(i.id,"g"),n.id)})),new RTCSessionDescription({type:e.type,sdp:r})}function a(t,e){let r=e.sdp;return Object.keys(t._reverseStreams||[]).forEach((e=>{const n=t._reverseStreams[e],i=t._streams[n.id];r=r.replace(new RegExp(n.id,"g"),i.id)})),new RTCSessionDescription({type:e.type,sdp:r})}t.RTCPeerConnection.prototype.removeStream=function(t){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[t.id]||t]),delete this._reverseStreams[this._streams[t.id]?this._streams[t.id].id:t.id],delete this._streams[t.id]},t.RTCPeerConnection.prototype.addTrack=function(e,r){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const n=[].slice.call(arguments,1);if(1!==n.length||!n[0].getTracks().find((t=>t===e)))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");const i=this.getSenders().find((t=>t.track===e));if(i)throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const o=this._streams[r.id];if(o)o.addTrack(e),Promise.resolve().then((()=>{this.dispatchEvent(new Event("negotiationneeded"))}));else{const n=new t.MediaStream([e]);this._streams[r.id]=n,this._reverseStreams[n.id]=r,this.addStream(n)}return this.getSenders().find((t=>t.track===e))},["createOffer","createAnswer"].forEach((function(e){const r=t.RTCPeerConnection.prototype[e],n={[e](){const t=arguments;return arguments.length&&"function"==typeof arguments[0]?r.apply(this,[e=>{const r=o(this,e);t[0].apply(null,[r])},e=>{t[1]&&t[1].apply(null,e)},arguments[2]]):r.apply(this,arguments).then((t=>o(this,t)))}};t.RTCPeerConnection.prototype[e]=n[e]}));const s=t.RTCPeerConnection.prototype.setLocalDescription;t.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=a(this,arguments[0]),s.apply(this,arguments)):s.apply(this,arguments)};const c=Object.getOwnPropertyDescriptor(t.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(t.RTCPeerConnection.prototype,"localDescription",{get(){const t=c.get.apply(this);return""===t.type?t:o(this,t)}}),t.RTCPeerConnection.prototype.removeTrack=function(t){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!t._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(t._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");let e;this._streams=this._streams||{},Object.keys(this._streams).forEach((r=>{this._streams[r].getTracks().find((e=>t.track===e))&&(e=this._streams[r])})),e&&(1===e.getTracks().length?this.removeStream(this._reverseStreams[e.id]):e.removeTrack(t.track),this.dispatchEvent(new Event("negotiationneeded")))}}function vu(t,e){!t.RTCPeerConnection&&t.webkitRTCPeerConnection&&(t.RTCPeerConnection=t.webkitRTCPeerConnection),t.RTCPeerConnection&&e.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(e){const r=t.RTCPeerConnection.prototype[e],n={[e](){return arguments[0]=new("addIceCandidate"===e?t.RTCIceCandidate:t.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}};t.RTCPeerConnection.prototype[e]=n[e]}))}function yu(t,e){zc(t,"negotiationneeded",(t=>{const r=t.target;if(!(e.version<72||r.getConfiguration&&"plan-b"===r.getConfiguration().sdpSemantics)||"stable"===r.signalingState)return t}))}var gu=Object.freeze({__proto__:null,shimMediaStream:uu,shimOnTrack:lu,shimGetSendersWithDtmf:du,shimGetStats:pu,shimSenderReceiverGetStats:fu,shimAddTrackRemoveTrackWithNative:hu,shimAddTrackRemoveTrack:mu,shimPeerConnection:vu,fixNegotiationNeeded:yu,shimGetUserMedia:cu,shimGetDisplayMedia:function(t,e){t.navigator.mediaDevices&&"getDisplayMedia"in t.navigator.mediaDevices||t.navigator.mediaDevices&&("function"==typeof e?t.navigator.mediaDevices.getDisplayMedia=function(r){return e(r).then((e=>{const n=r.video&&r.video.width,i=r.video&&r.video.height,o=r.video&&r.video.frameRate;return r.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:e,maxFrameRate:o||3}},n&&(r.video.mandatory.maxWidth=n),i&&(r.video.mandatory.maxHeight=i),t.navigator.mediaDevices.getUserMedia(r)}))}:console.error("shimGetDisplayMedia: getSourceId argument is not a function"))}});function bu(t,e){const r=t&&t.navigator,n=t&&t.MediaStreamTrack;if(r.getUserMedia=function(t,e,n){ru("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),r.mediaDevices.getUserMedia(t).then(e,n)},!(e.version>55&&"autoGainControl"in r.mediaDevices.getSupportedConstraints())){const t=function(t,e,r){e in t&&!(r in t)&&(t[r]=t[e],delete t[e])},e=r.mediaDevices.getUserMedia.bind(r.mediaDevices);if(r.mediaDevices.getUserMedia=function(r){return"object"==typeof r&&"object"==typeof r.audio&&(r=JSON.parse(JSON.stringify(r)),t(r.audio,"autoGainControl","mozAutoGainControl"),t(r.audio,"noiseSuppression","mozNoiseSuppression")),e(r)},n&&n.prototype.getSettings){const e=n.prototype.getSettings;n.prototype.getSettings=function(){const r=e.apply(this,arguments);return t(r,"mozAutoGainControl","autoGainControl"),t(r,"mozNoiseSuppression","noiseSuppression"),r}}if(n&&n.prototype.applyConstraints){const e=n.prototype.applyConstraints;n.prototype.applyConstraints=function(r){return"audio"===this.kind&&"object"==typeof r&&(r=JSON.parse(JSON.stringify(r)),t(r,"autoGainControl","mozAutoGainControl"),t(r,"noiseSuppression","mozNoiseSuppression")),e.apply(this,[r])}}}}function Cu(t){"object"==typeof t&&t.RTCTrackEvent&&"receiver"in t.RTCTrackEvent.prototype&&!("transceiver"in t.RTCTrackEvent.prototype)&&Object.defineProperty(t.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Su(t,e){if("object"!=typeof t||!t.RTCPeerConnection&&!t.mozRTCPeerConnection)return;!t.RTCPeerConnection&&t.mozRTCPeerConnection&&(t.RTCPeerConnection=t.mozRTCPeerConnection),e.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(e){const r=t.RTCPeerConnection.prototype[e],n={[e](){return arguments[0]=new("addIceCandidate"===e?t.RTCIceCandidate:t.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}};t.RTCPeerConnection.prototype[e]=n[e]}));const r={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},n=t.RTCPeerConnection.prototype.getStats;t.RTCPeerConnection.prototype.getStats=function(){const[t,i,o]=arguments;return n.apply(this,[t||null]).then((t=>{if(e.version<53&&!i)try{t.forEach((t=>{t.type=r[t.type]||t.type}))}catch(e){if("TypeError"!==e.name)throw e;t.forEach(((e,n)=>{t.set(n,Object.assign({},e,{type:r[e.type]||e.type}))}))}return t})).then(i,o)}}function Tu(t){if("object"!=typeof t||!t.RTCPeerConnection||!t.RTCRtpSender)return;if(t.RTCRtpSender&&"getStats"in t.RTCRtpSender.prototype)return;const e=t.RTCPeerConnection.prototype.getSenders;e&&(t.RTCPeerConnection.prototype.getSenders=function(){const t=e.apply(this,[]);return t.forEach((t=>t._pc=this)),t});const r=t.RTCPeerConnection.prototype.addTrack;r&&(t.RTCPeerConnection.prototype.addTrack=function(){const t=r.apply(this,arguments);return t._pc=this,t}),t.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function _u(t){if("object"!=typeof t||!t.RTCPeerConnection||!t.RTCRtpSender)return;if(t.RTCRtpSender&&"getStats"in t.RTCRtpReceiver.prototype)return;const e=t.RTCPeerConnection.prototype.getReceivers;e&&(t.RTCPeerConnection.prototype.getReceivers=function(){const t=e.apply(this,[]);return t.forEach((t=>t._pc=this)),t}),zc(t,"track",(t=>(t.receiver._pc=t.srcElement,t))),t.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function Ru(t){t.RTCPeerConnection&&!("removeStream"in t.RTCPeerConnection.prototype)&&(t.RTCPeerConnection.prototype.removeStream=function(t){ru("removeStream","removeTrack"),this.getSenders().forEach((e=>{e.track&&t.getTracks().includes(e.track)&&this.removeTrack(e)}))})}function Eu(t){t.DataChannel&&!t.RTCDataChannel&&(t.RTCDataChannel=t.DataChannel)}function wu(t){if("object"!=typeof t||!t.RTCPeerConnection)return;const e=t.RTCPeerConnection.prototype.addTransceiver;e&&(t.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let t=arguments[1]&&arguments[1].sendEncodings;void 0===t&&(t=[]),t=[...t];const r=t.length>0;r&&t.forEach((t=>{if("rid"in t){if(!/^[a-z0-9]{0,16}$/i.test(t.rid))throw new TypeError("Invalid RID value provided.")}if("scaleResolutionDownBy"in t&&!(parseFloat(t.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in t&&!(parseFloat(t.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")}));const n=e.apply(this,arguments);if(r){const{sender:e}=n,r=e.getParameters();(!("encodings"in r)||1===r.encodings.length&&0===Object.keys(r.encodings[0]).length)&&(r.encodings=t,e.sendEncodings=t,this.setParametersPromises.push(e.setParameters(r).then((()=>{delete e.sendEncodings})).catch((()=>{delete e.sendEncodings}))))}return n})}function Pu(t){if("object"!=typeof t||!t.RTCRtpSender)return;const e=t.RTCRtpSender.prototype.getParameters;e&&(t.RTCRtpSender.prototype.getParameters=function(){const t=e.apply(this,arguments);return"encodings"in t||(t.encodings=[].concat(this.sendEncodings||[{}])),t})}function ku(t){if("object"!=typeof t||!t.RTCPeerConnection)return;const e=t.RTCPeerConnection.prototype.createOffer;t.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>e.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):e.apply(this,arguments)}}function Au(t){if("object"!=typeof t||!t.RTCPeerConnection)return;const e=t.RTCPeerConnection.prototype.createAnswer;t.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>e.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):e.apply(this,arguments)}}var Iu=Object.freeze({__proto__:null,shimOnTrack:Cu,shimPeerConnection:Su,shimSenderGetStats:Tu,shimReceiverGetStats:_u,shimRemoveStream:Ru,shimRTCDataChannel:Eu,shimAddTransceiver:wu,shimGetParameters:Pu,shimCreateOffer:ku,shimCreateAnswer:Au,shimGetUserMedia:bu,shimGetDisplayMedia:function(t,e){t.navigator.mediaDevices&&"getDisplayMedia"in t.navigator.mediaDevices||t.navigator.mediaDevices&&(t.navigator.mediaDevices.getDisplayMedia=function(r){if(!r||!r.video){const t=new DOMException("getDisplayMedia without video constraints is undefined");return t.name="NotFoundError",t.code=8,Promise.reject(t)}return!0===r.video?r.video={mediaSource:e}:r.video.mediaSource=e,t.navigator.mediaDevices.getUserMedia(r)})}});function xu(t){if("object"==typeof t&&t.RTCPeerConnection){if("getLocalStreams"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in t.RTCPeerConnection.prototype)){const e=t.RTCPeerConnection.prototype.addTrack;t.RTCPeerConnection.prototype.addStream=function(t){this._localStreams||(this._localStreams=[]),this._localStreams.includes(t)||this._localStreams.push(t),t.getAudioTracks().forEach((r=>e.call(this,r,t))),t.getVideoTracks().forEach((r=>e.call(this,r,t)))},t.RTCPeerConnection.prototype.addTrack=function(t,...r){return r&&r.forEach((t=>{this._localStreams?this._localStreams.includes(t)||this._localStreams.push(t):this._localStreams=[t]})),e.apply(this,arguments)}}"removeStream"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.removeStream=function(t){this._localStreams||(this._localStreams=[]);const e=this._localStreams.indexOf(t);if(-1===e)return;this._localStreams.splice(e,1);const r=t.getTracks();this.getSenders().forEach((t=>{r.includes(t.track)&&this.removeTrack(t)}))})}}function Lu(t){if("object"==typeof t&&t.RTCPeerConnection&&("getRemoteStreams"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in t.RTCPeerConnection.prototype))){Object.defineProperty(t.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(t){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=t),this.addEventListener("track",this._onaddstreampoly=t=>{t.streams.forEach((t=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(t))return;this._remoteStreams.push(t);const e=new Event("addstream");e.stream=t,this.dispatchEvent(e)}))})}});const e=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(){const t=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(e){e.streams.forEach((e=>{if(t._remoteStreams||(t._remoteStreams=[]),t._remoteStreams.indexOf(e)>=0)return;t._remoteStreams.push(e);const r=new Event("addstream");r.stream=e,t.dispatchEvent(r)}))}),e.apply(t,arguments)}}}function Du(t){if("object"!=typeof t||!t.RTCPeerConnection)return;const e=t.RTCPeerConnection.prototype,r=e.createOffer,n=e.createAnswer,i=e.setLocalDescription,o=e.setRemoteDescription,a=e.addIceCandidate;e.createOffer=function(t,e){const n=arguments.length>=2?arguments[2]:arguments[0],i=r.apply(this,[n]);return e?(i.then(t,e),Promise.resolve()):i},e.createAnswer=function(t,e){const r=arguments.length>=2?arguments[2]:arguments[0],i=n.apply(this,[r]);return e?(i.then(t,e),Promise.resolve()):i};let s=function(t,e,r){const n=i.apply(this,[t]);return r?(n.then(e,r),Promise.resolve()):n};e.setLocalDescription=s,s=function(t,e,r){const n=o.apply(this,[t]);return r?(n.then(e,r),Promise.resolve()):n},e.setRemoteDescription=s,s=function(t,e,r){const n=a.apply(this,[t]);return r?(n.then(e,r),Promise.resolve()):n},e.addIceCandidate=s}function Nu(t){const e=t&&t.navigator;if(e.mediaDevices&&e.mediaDevices.getUserMedia){const t=e.mediaDevices,r=t.getUserMedia.bind(t);e.mediaDevices.getUserMedia=t=>r(Ou(t))}!e.getUserMedia&&e.mediaDevices&&e.mediaDevices.getUserMedia&&(e.getUserMedia=function(t,r,n){e.mediaDevices.getUserMedia(t).then(r,n)}.bind(e))}function Ou(t){return t&&void 0!==t.video?Object.assign({},t,{video:iu(t.video)}):t}function Mu(t){if(!t.RTCPeerConnection)return;const e=t.RTCPeerConnection;t.RTCPeerConnection=function(t,r){if(t&&t.iceServers){const e=[];for(let r=0;r<t.iceServers.length;r++){let n=t.iceServers[r];void 0===n.urls&&n.url?(ru("RTCIceServer.url","RTCIceServer.urls"),n=JSON.parse(JSON.stringify(n)),n.urls=n.url,delete n.url,e.push(n)):e.push(t.iceServers[r])}t.iceServers=e}return new e(t,r)},t.RTCPeerConnection.prototype=e.prototype,"generateCertificate"in e&&Object.defineProperty(t.RTCPeerConnection,"generateCertificate",{get:()=>e.generateCertificate})}function Uu(t){"object"==typeof t&&t.RTCTrackEvent&&"receiver"in t.RTCTrackEvent.prototype&&!("transceiver"in t.RTCTrackEvent.prototype)&&Object.defineProperty(t.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Vu(t){const e=t.RTCPeerConnection.prototype.createOffer;t.RTCPeerConnection.prototype.createOffer=function(t){if(t){void 0!==t.offerToReceiveAudio&&(t.offerToReceiveAudio=!!t.offerToReceiveAudio);const e=this.getTransceivers().find((t=>"audio"===t.receiver.track.kind));!1===t.offerToReceiveAudio&&e?"sendrecv"===e.direction?e.setDirection?e.setDirection("sendonly"):e.direction="sendonly":"recvonly"===e.direction&&(e.setDirection?e.setDirection("inactive"):e.direction="inactive"):!0!==t.offerToReceiveAudio||e||this.addTransceiver("audio",{direction:"recvonly"}),void 0!==t.offerToReceiveVideo&&(t.offerToReceiveVideo=!!t.offerToReceiveVideo);const r=this.getTransceivers().find((t=>"video"===t.receiver.track.kind));!1===t.offerToReceiveVideo&&r?"sendrecv"===r.direction?r.setDirection?r.setDirection("sendonly"):r.direction="sendonly":"recvonly"===r.direction&&(r.setDirection?r.setDirection("inactive"):r.direction="inactive"):!0!==t.offerToReceiveVideo||r||this.addTransceiver("video",{direction:"recvonly"})}return e.apply(this,arguments)}}function Fu(t){"object"!=typeof t||t.AudioContext||(t.AudioContext=t.webkitAudioContext)}var ju=Object.freeze({__proto__:null,shimLocalStreamsAPI:xu,shimRemoteStreamsAPI:Lu,shimCallbacksAPI:Du,shimGetUserMedia:Nu,shimConstraints:Ou,shimRTCIceServerUrls:Mu,shimTrackEventTransceiver:Uu,shimCreateOfferLegacy:Vu,shimAudioContext:Fu}),Bu={exports:{}};!function(t){const e={generateIdentifier:function(){return Math.random().toString(36).substring(2,12)}};e.localCName=e.generateIdentifier(),e.splitLines=function(t){return t.trim().split("\n").map((t=>t.trim()))},e.splitSections=function(t){return t.split("\nm=").map(((t,e)=>(e>0?"m="+t:t).trim()+"\r\n"))},e.getDescription=function(t){const r=e.splitSections(t);return r&&r[0]},e.getMediaSections=function(t){const r=e.splitSections(t);return r.shift(),r},e.matchPrefix=function(t,r){return e.splitLines(t).filter((t=>0===t.indexOf(r)))},e.parseCandidate=function(t){let e;e=0===t.indexOf("a=candidate:")?t.substring(12).split(" "):t.substring(10).split(" ");const r={foundation:e[0],component:{1:"rtp",2:"rtcp"}[e[1]]||e[1],protocol:e[2].toLowerCase(),priority:parseInt(e[3],10),ip:e[4],address:e[4],port:parseInt(e[5],10),type:e[7]};for(let t=8;t<e.length;t+=2)switch(e[t]){case"raddr":r.relatedAddress=e[t+1];break;case"rport":r.relatedPort=parseInt(e[t+1],10);break;case"tcptype":r.tcpType=e[t+1];break;case"ufrag":r.ufrag=e[t+1],r.usernameFragment=e[t+1];break;default:void 0===r[e[t]]&&(r[e[t]]=e[t+1])}return r},e.writeCandidate=function(t){const e=[];e.push(t.foundation);const r=t.component;"rtp"===r?e.push(1):"rtcp"===r?e.push(2):e.push(r),e.push(t.protocol.toUpperCase()),e.push(t.priority),e.push(t.address||t.ip),e.push(t.port);const n=t.type;return e.push("typ"),e.push(n),"host"!==n&&t.relatedAddress&&t.relatedPort&&(e.push("raddr"),e.push(t.relatedAddress),e.push("rport"),e.push(t.relatedPort)),t.tcpType&&"tcp"===t.protocol.toLowerCase()&&(e.push("tcptype"),e.push(t.tcpType)),(t.usernameFragment||t.ufrag)&&(e.push("ufrag"),e.push(t.usernameFragment||t.ufrag)),"candidate:"+e.join(" ")},e.parseIceOptions=function(t){return t.substring(14).split(" ")},e.parseRtpMap=function(t){let e=t.substring(9).split(" ");const r={payloadType:parseInt(e.shift(),10)};return e=e[0].split("/"),r.name=e[0],r.clockRate=parseInt(e[1],10),r.channels=3===e.length?parseInt(e[2],10):1,r.numChannels=r.channels,r},e.writeRtpMap=function(t){let e=t.payloadType;void 0!==t.preferredPayloadType&&(e=t.preferredPayloadType);const r=t.channels||t.numChannels||1;return"a=rtpmap:"+e+" "+t.name+"/"+t.clockRate+(1!==r?"/"+r:"")+"\r\n"},e.parseExtmap=function(t){const e=t.substring(9).split(" ");return{id:parseInt(e[0],10),direction:e[0].indexOf("/")>0?e[0].split("/")[1]:"sendrecv",uri:e[1],attributes:e.slice(2).join(" ")}},e.writeExtmap=function(t){return"a=extmap:"+(t.id||t.preferredId)+(t.direction&&"sendrecv"!==t.direction?"/"+t.direction:"")+" "+t.uri+(t.attributes?" "+t.attributes:"")+"\r\n"},e.parseFmtp=function(t){const e={};let r;const n=t.substring(t.indexOf(" ")+1).split(";");for(let t=0;t<n.length;t++)r=n[t].trim().split("="),e[r[0].trim()]=r[1];return e},e.writeFmtp=function(t){let e="",r=t.payloadType;if(void 0!==t.preferredPayloadType&&(r=t.preferredPayloadType),t.parameters&&Object.keys(t.parameters).length){const n=[];Object.keys(t.parameters).forEach((e=>{void 0!==t.parameters[e]?n.push(e+"="+t.parameters[e]):n.push(e)})),e+="a=fmtp:"+r+" "+n.join(";")+"\r\n"}return e},e.parseRtcpFb=function(t){const e=t.substring(t.indexOf(" ")+1).split(" ");return{type:e.shift(),parameter:e.join(" ")}},e.writeRtcpFb=function(t){let e="",r=t.payloadType;return void 0!==t.preferredPayloadType&&(r=t.preferredPayloadType),t.rtcpFeedback&&t.rtcpFeedback.length&&t.rtcpFeedback.forEach((t=>{e+="a=rtcp-fb:"+r+" "+t.type+(t.parameter&&t.parameter.length?" "+t.parameter:"")+"\r\n"})),e},e.parseSsrcMedia=function(t){const e=t.indexOf(" "),r={ssrc:parseInt(t.substring(7,e),10)},n=t.indexOf(":",e);return n>-1?(r.attribute=t.substring(e+1,n),r.value=t.substring(n+1)):r.attribute=t.substring(e+1),r},e.parseSsrcGroup=function(t){const e=t.substring(13).split(" ");return{semantics:e.shift(),ssrcs:e.map((t=>parseInt(t,10)))}},e.getMid=function(t){const r=e.matchPrefix(t,"a=mid:")[0];if(r)return r.substring(6)},e.parseFingerprint=function(t){const e=t.substring(14).split(" ");return{algorithm:e[0].toLowerCase(),value:e[1].toUpperCase()}},e.getDtlsParameters=function(t,r){return{role:"auto",fingerprints:e.matchPrefix(t+r,"a=fingerprint:").map(e.parseFingerprint)}},e.writeDtlsParameters=function(t,e){let r="a=setup:"+e+"\r\n";return t.fingerprints.forEach((t=>{r+="a=fingerprint:"+t.algorithm+" "+t.value+"\r\n"})),r},e.parseCryptoLine=function(t){const e=t.substring(9).split(" ");return{tag:parseInt(e[0],10),cryptoSuite:e[1],keyParams:e[2],sessionParams:e.slice(3)}},e.writeCryptoLine=function(t){return"a=crypto:"+t.tag+" "+t.cryptoSuite+" "+("object"==typeof t.keyParams?e.writeCryptoKeyParams(t.keyParams):t.keyParams)+(t.sessionParams?" "+t.sessionParams.join(" "):"")+"\r\n"},e.parseCryptoKeyParams=function(t){if(0!==t.indexOf("inline:"))return null;const e=t.substring(7).split("|");return{keyMethod:"inline",keySalt:e[0],lifeTime:e[1],mkiValue:e[2]?e[2].split(":")[0]:void 0,mkiLength:e[2]?e[2].split(":")[1]:void 0}},e.writeCryptoKeyParams=function(t){return t.keyMethod+":"+t.keySalt+(t.lifeTime?"|"+t.lifeTime:"")+(t.mkiValue&&t.mkiLength?"|"+t.mkiValue+":"+t.mkiLength:"")},e.getCryptoParameters=function(t,r){return e.matchPrefix(t+r,"a=crypto:").map(e.parseCryptoLine)},e.getIceParameters=function(t,r){const n=e.matchPrefix(t+r,"a=ice-ufrag:")[0],i=e.matchPrefix(t+r,"a=ice-pwd:")[0];return n&&i?{usernameFragment:n.substring(12),password:i.substring(10)}:null},e.writeIceParameters=function(t){let e="a=ice-ufrag:"+t.usernameFragment+"\r\na=ice-pwd:"+t.password+"\r\n";return t.iceLite&&(e+="a=ice-lite\r\n"),e},e.parseRtpParameters=function(t){const r={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},n=e.splitLines(t)[0].split(" ");r.profile=n[2];for(let i=3;i<n.length;i++){const o=n[i],a=e.matchPrefix(t,"a=rtpmap:"+o+" ")[0];if(a){const n=e.parseRtpMap(a),i=e.matchPrefix(t,"a=fmtp:"+o+" ");switch(n.parameters=i.length?e.parseFmtp(i[0]):{},n.rtcpFeedback=e.matchPrefix(t,"a=rtcp-fb:"+o+" ").map(e.parseRtcpFb),r.codecs.push(n),n.name.toUpperCase()){case"RED":case"ULPFEC":r.fecMechanisms.push(n.name.toUpperCase())}}}e.matchPrefix(t,"a=extmap:").forEach((t=>{r.headerExtensions.push(e.parseExtmap(t))}));const i=e.matchPrefix(t,"a=rtcp-fb:* ").map(e.parseRtcpFb);return r.codecs.forEach((t=>{i.forEach((e=>{t.rtcpFeedback.find((t=>t.type===e.type&&t.parameter===e.parameter))||t.rtcpFeedback.push(e)}))})),r},e.writeRtpDescription=function(t,r){let n="";n+="m="+t+" ",n+=r.codecs.length>0?"9":"0",n+=" "+(r.profile||"UDP/TLS/RTP/SAVPF")+" ",n+=r.codecs.map((t=>void 0!==t.preferredPayloadType?t.preferredPayloadType:t.payloadType)).join(" ")+"\r\n",n+="c=IN IP4 0.0.0.0\r\n",n+="a=rtcp:9 IN IP4 0.0.0.0\r\n",r.codecs.forEach((t=>{n+=e.writeRtpMap(t),n+=e.writeFmtp(t),n+=e.writeRtcpFb(t)}));let i=0;return r.codecs.forEach((t=>{t.maxptime>i&&(i=t.maxptime)})),i>0&&(n+="a=maxptime:"+i+"\r\n"),r.headerExtensions&&r.headerExtensions.forEach((t=>{n+=e.writeExtmap(t)})),n},e.parseRtpEncodingParameters=function(t){const r=[],n=e.parseRtpParameters(t),i=-1!==n.fecMechanisms.indexOf("RED"),o=-1!==n.fecMechanisms.indexOf("ULPFEC"),a=e.matchPrefix(t,"a=ssrc:").map((t=>e.parseSsrcMedia(t))).filter((t=>"cname"===t.attribute)),s=a.length>0&&a[0].ssrc;let c;const u=e.matchPrefix(t,"a=ssrc-group:FID").map((t=>t.substring(17).split(" ").map((t=>parseInt(t,10)))));u.length>0&&u[0].length>1&&u[0][0]===s&&(c=u[0][1]),n.codecs.forEach((t=>{if("RTX"===t.name.toUpperCase()&&t.parameters.apt){let e={ssrc:s,codecPayloadType:parseInt(t.parameters.apt,10)};s&&c&&(e.rtx={ssrc:c}),r.push(e),i&&(e=JSON.parse(JSON.stringify(e)),e.fec={ssrc:s,mechanism:o?"red+ulpfec":"red"},r.push(e))}})),0===r.length&&s&&r.push({ssrc:s});let l=e.matchPrefix(t,"b=");return l.length&&(l=0===l[0].indexOf("b=TIAS:")?parseInt(l[0].substring(7),10):0===l[0].indexOf("b=AS:")?1e3*parseInt(l[0].substring(5),10)*.95-16e3:void 0,r.forEach((t=>{t.maxBitrate=l}))),r},e.parseRtcpParameters=function(t){const r={},n=e.matchPrefix(t,"a=ssrc:").map((t=>e.parseSsrcMedia(t))).filter((t=>"cname"===t.attribute))[0];n&&(r.cname=n.value,r.ssrc=n.ssrc);const i=e.matchPrefix(t,"a=rtcp-rsize");r.reducedSize=i.length>0,r.compound=0===i.length;const o=e.matchPrefix(t,"a=rtcp-mux");return r.mux=o.length>0,r},e.writeRtcpParameters=function(t){let e="";return t.reducedSize&&(e+="a=rtcp-rsize\r\n"),t.mux&&(e+="a=rtcp-mux\r\n"),void 0!==t.ssrc&&t.cname&&(e+="a=ssrc:"+t.ssrc+" cname:"+t.cname+"\r\n"),e},e.parseMsid=function(t){let r;const n=e.matchPrefix(t,"a=msid:");if(1===n.length)return r=n[0].substring(7).split(" "),{stream:r[0],track:r[1]};const i=e.matchPrefix(t,"a=ssrc:").map((t=>e.parseSsrcMedia(t))).filter((t=>"msid"===t.attribute));return i.length>0?(r=i[0].value.split(" "),{stream:r[0],track:r[1]}):void 0},e.parseSctpDescription=function(t){const r=e.parseMLine(t),n=e.matchPrefix(t,"a=max-message-size:");let i;n.length>0&&(i=parseInt(n[0].substring(19),10)),isNaN(i)&&(i=65536);const o=e.matchPrefix(t,"a=sctp-port:");if(o.length>0)return{port:parseInt(o[0].substring(12),10),protocol:r.fmt,maxMessageSize:i};const a=e.matchPrefix(t,"a=sctpmap:");if(a.length>0){const t=a[0].substring(10).split(" ");return{port:parseInt(t[0],10),protocol:t[1],maxMessageSize:i}}},e.writeSctpDescription=function(t,e){let r=[];return r="DTLS/SCTP"!==t.protocol?["m="+t.kind+" 9 "+t.protocol+" "+e.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+e.port+"\r\n"]:["m="+t.kind+" 9 "+t.protocol+" "+e.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+e.port+" "+e.protocol+" 65535\r\n"],void 0!==e.maxMessageSize&&r.push("a=max-message-size:"+e.maxMessageSize+"\r\n"),r.join("")},e.generateSessionId=function(){return Math.random().toString().substr(2,22)},e.writeSessionBoilerplate=function(t,r,n){let i;const o=void 0!==r?r:2;i=t||e.generateSessionId();return"v=0\r\no="+(n||"thisisadapterortc")+" "+i+" "+o+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},e.getDirection=function(t,r){const n=e.splitLines(t);for(let t=0;t<n.length;t++)switch(n[t]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return n[t].substring(2)}return r?e.getDirection(r):"sendrecv"},e.getKind=function(t){return e.splitLines(t)[0].split(" ")[0].substring(2)},e.isRejected=function(t){return"0"===t.split(" ",2)[1]},e.parseMLine=function(t){const r=e.splitLines(t)[0].substring(2).split(" ");return{kind:r[0],port:parseInt(r[1],10),protocol:r[2],fmt:r.slice(3).join(" ")}},e.parseOLine=function(t){const r=e.matchPrefix(t,"o=")[0].substring(2).split(" ");return{username:r[0],sessionId:r[1],sessionVersion:parseInt(r[2],10),netType:r[3],addressType:r[4],address:r[5]}},e.isValidSDP=function(t){if("string"!=typeof t||0===t.length)return!1;const r=e.splitLines(t);for(let t=0;t<r.length;t++)if(r[t].length<2||"="!==r[t].charAt(1))return!1;return!0},t.exports=e}(Bu);var Wu=Bu.exports,Gu=Object.freeze(t({__proto__:null,default:Wu},[Bu.exports]));function Zu(t){if(!t.RTCIceCandidate||t.RTCIceCandidate&&"foundation"in t.RTCIceCandidate.prototype)return;const e=t.RTCIceCandidate;t.RTCIceCandidate=function(t){if("object"==typeof t&&t.candidate&&0===t.candidate.indexOf("a=")&&((t=JSON.parse(JSON.stringify(t))).candidate=t.candidate.substring(2)),t.candidate&&t.candidate.length){const r=new e(t),n=Wu.parseCandidate(t.candidate);for(const t in n)t in r||Object.defineProperty(r,t,{value:n[t]});return r.toJSON=function(){return{candidate:r.candidate,sdpMid:r.sdpMid,sdpMLineIndex:r.sdpMLineIndex,usernameFragment:r.usernameFragment}},r}return new e(t)},t.RTCIceCandidate.prototype=e.prototype,zc(t,"icecandidate",(e=>(e.candidate&&Object.defineProperty(e,"candidate",{value:new t.RTCIceCandidate(e.candidate),writable:"false"}),e)))}function Yu(t){!t.RTCIceCandidate||t.RTCIceCandidate&&"relayProtocol"in t.RTCIceCandidate.prototype||zc(t,"icecandidate",(t=>{if(t.candidate){const e=Wu.parseCandidate(t.candidate.candidate);"relay"===e.type&&(t.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[e.priority>>24])}return t}))}function Hu(t,e){if(!t.RTCPeerConnection)return;"sctp"in t.RTCPeerConnection.prototype||Object.defineProperty(t.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});const r=function(t){if(!t||!t.sdp)return!1;const e=Wu.splitSections(t.sdp);return e.shift(),e.some((t=>{const e=Wu.parseMLine(t);return e&&"application"===e.kind&&-1!==e.protocol.indexOf("SCTP")}))},n=function(t){const e=t.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===e||e.length<2)return-1;const r=parseInt(e[1],10);return r!=r?-1:r},i=function(t){let r=65536;return"firefox"===e.browser&&(r=e.version<57?-1===t?16384:2147483637:e.version<60?57===e.version?65535:65536:2147483637),r},o=function(t,r){let n=65536;"firefox"===e.browser&&57===e.version&&(n=65535);const i=Wu.matchPrefix(t.sdp,"a=max-message-size:");return i.length>0?n=parseInt(i[0].substring(19),10):"firefox"===e.browser&&-1!==r&&(n=2147483637),n},a=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===e.browser&&e.version>=76){const{sdpSemantics:t}=this.getConfiguration();"plan-b"===t&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(r(arguments[0])){const t=n(arguments[0]),e=i(t),r=o(arguments[0],t);let a;a=0===e&&0===r?Number.POSITIVE_INFINITY:0===e||0===r?Math.max(e,r):Math.min(e,r);const s={};Object.defineProperty(s,"maxMessageSize",{get:()=>a}),this._sctp=s}return a.apply(this,arguments)}}function Ju(t){if(!t.RTCPeerConnection||!("createDataChannel"in t.RTCPeerConnection.prototype))return;function e(t,e){const r=t.send;t.send=function(){const n=arguments[0],i=n.length||n.size||n.byteLength;if("open"===t.readyState&&e.sctp&&i>e.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+e.sctp.maxMessageSize+" bytes)");return r.apply(t,arguments)}}const r=t.RTCPeerConnection.prototype.createDataChannel;t.RTCPeerConnection.prototype.createDataChannel=function(){const t=r.apply(this,arguments);return e(t,this),t},zc(t,"datachannel",(t=>(e(t.channel,t.target),t)))}function Xu(t){if(!t.RTCPeerConnection||"connectionState"in t.RTCPeerConnection.prototype)return;const e=t.RTCPeerConnection.prototype;Object.defineProperty(e,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(e,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(t){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),t&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=t)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach((t=>{const r=e[t];e[t]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=t=>{const e=t.target;if(e._lastConnectionState!==e.connectionState){e._lastConnectionState=e.connectionState;const r=new Event("connectionstatechange",t);e.dispatchEvent(r)}return t},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),r.apply(this,arguments)}}))}function Ku(t,e){if(!t.RTCPeerConnection)return;if("chrome"===e.browser&&e.version>=71)return;if("safari"===e.browser&&e.version>=605)return;const r=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(e){if(e&&e.sdp&&-1!==e.sdp.indexOf("\na=extmap-allow-mixed")){const r=e.sdp.split("\n").filter((t=>"a=extmap-allow-mixed"!==t.trim())).join("\n");t.RTCSessionDescription&&e instanceof t.RTCSessionDescription?arguments[0]=new t.RTCSessionDescription({type:e.type,sdp:r}):e.sdp=r}return r.apply(this,arguments)}}function qu(t,e){if(!t.RTCPeerConnection||!t.RTCPeerConnection.prototype)return;const r=t.RTCPeerConnection.prototype.addIceCandidate;r&&0!==r.length&&(t.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===e.browser&&e.version<78||"firefox"===e.browser&&e.version<68||"safari"===e.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():r.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function Qu(t,e){if(!t.RTCPeerConnection||!t.RTCPeerConnection.prototype)return;const r=t.RTCPeerConnection.prototype.setLocalDescription;r&&0!==r.length&&(t.RTCPeerConnection.prototype.setLocalDescription=function(){let t=arguments[0]||{};if("object"!=typeof t||t.type&&t.sdp)return r.apply(this,arguments);if(t={type:t.type,sdp:t.sdp},!t.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":t.type="offer";break;default:t.type="answer"}if(t.sdp||"offer"!==t.type&&"answer"!==t.type)return r.apply(this,[t]);const e="offer"===t.type?this.createOffer:this.createAnswer;return e.apply(this).then((t=>r.apply(this,[t])))})}var zu=Object.freeze({__proto__:null,shimRTCIceCandidate:Zu,shimRTCIceCandidateRelayProtocol:Yu,shimMaxMessageSize:Hu,shimSendThrowTypeError:Ju,shimConnectionState:Xu,removeExtmapAllowMixed:Ku,shimAddIceCandidateNullOrEmpty:qu,shimParameterlessSetLocalDescription:Qu});const $u=function({window:t}={},e={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){const r=eu,n=function(t){const e={browser:null,version:null};if(void 0===t||!t.navigator||!t.navigator.userAgent)return e.browser="Not a browser.",e;const{navigator:r}=t;if(r.mozGetUserMedia)e.browser="firefox",e.version=Qc(r.userAgent,/Firefox\/(\d+)\./,1);else if(r.webkitGetUserMedia||!1===t.isSecureContext&&t.webkitRTCPeerConnection)e.browser="chrome",e.version=Qc(r.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else{if(!t.RTCPeerConnection||!r.userAgent.match(/AppleWebKit\/(\d+)\./))return e.browser="Not a supported browser.",e;e.browser="safari",e.version=Qc(r.userAgent,/AppleWebKit\/(\d+)\./,1),e.supportsUnifiedPlan=t.RTCRtpTransceiver&&"currentDirection"in t.RTCRtpTransceiver.prototype}return e}(t),i={browserDetails:n,commonShim:zu,extractVersion:Qc,disableLog:$c,disableWarnings:tu,sdp:Gu};switch(n.browser){case"chrome":if(!gu||!vu||!e.shimChrome)return r("Chrome shim is not included in this adapter release."),i;if(null===n.version)return r("Chrome shim can not determine version, not shimming."),i;r("adapter.js shimming chrome."),i.browserShim=gu,qu(t,n),Qu(t),cu(t,n),uu(t),vu(t,n),lu(t),mu(t,n),du(t),pu(t),fu(t),yu(t,n),Zu(t),Yu(t),Xu(t),Hu(t,n),Ju(t),Ku(t,n);break;case"firefox":if(!Iu||!Su||!e.shimFirefox)return r("Firefox shim is not included in this adapter release."),i;r("adapter.js shimming firefox."),i.browserShim=Iu,qu(t,n),Qu(t),bu(t,n),Su(t,n),Cu(t),Ru(t),Tu(t),_u(t),Eu(t),wu(t),Pu(t),ku(t),Au(t),Zu(t),Xu(t),Hu(t,n),Ju(t);break;case"safari":if(!ju||!e.shimSafari)return r("Safari shim is not included in this adapter release."),i;r("adapter.js shimming safari."),i.browserShim=ju,qu(t,n),Qu(t),Mu(t),Vu(t),Du(t),xu(t),Lu(t),Uu(t),Nu(t),Fu(t),Zu(t),Yu(t),Hu(t,n),Ju(t),Ku(t,n);break;default:r("Unsupported browser!")}return i}({window:"undefined"==typeof window?void 0:window});let tl=(t=21)=>crypto.getRandomValues(new Uint8Array(t)).reduce(((t,e)=>t+=(e&=63)<36?e.toString(36):e<62?(e-26).toString(36).toUpperCase():e>62?"-":"_"),"");var el={exports:{}};!function(t,e){t.exports=function(t){var e={};function r(n){if(e[n])return e[n].exports;var i=e[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=90)}({17:function(t,e,r){e.__esModule=!0,e.default=void 0;var n=r(18),i=function(){function t(){}return t.getFirstMatch=function(t,e){var r=e.match(t);return r&&r.length>0&&r[1]||""},t.getSecondMatch=function(t,e){var r=e.match(t);return r&&r.length>1&&r[2]||""},t.matchAndReturnConst=function(t,e,r){if(t.test(e))return r},t.getWindowsVersionName=function(t){switch(t){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}},t.getMacOSVersionName=function(t){var e=t.split(".").splice(0,2).map((function(t){return parseInt(t,10)||0}));if(e.push(0),10===e[0])switch(e[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}},t.getAndroidVersionName=function(t){var e=t.split(".").splice(0,2).map((function(t){return parseInt(t,10)||0}));if(e.push(0),!(1===e[0]&&e[1]<5))return 1===e[0]&&e[1]<6?"Cupcake":1===e[0]&&e[1]>=6?"Donut":2===e[0]&&e[1]<2?"Eclair":2===e[0]&&2===e[1]?"Froyo":2===e[0]&&e[1]>2?"Gingerbread":3===e[0]?"Honeycomb":4===e[0]&&e[1]<1?"Ice Cream Sandwich":4===e[0]&&e[1]<4?"Jelly Bean":4===e[0]&&e[1]>=4?"KitKat":5===e[0]?"Lollipop":6===e[0]?"Marshmallow":7===e[0]?"Nougat":8===e[0]?"Oreo":9===e[0]?"Pie":void 0},t.getVersionPrecision=function(t){return t.split(".").length},t.compareVersions=function(e,r,n){void 0===n&&(n=!1);var i=t.getVersionPrecision(e),o=t.getVersionPrecision(r),a=Math.max(i,o),s=0,c=t.map([e,r],(function(e){var r=a-t.getVersionPrecision(e),n=e+new Array(r+1).join(".0");return t.map(n.split("."),(function(t){return new Array(20-t.length).join("0")+t})).reverse()}));for(n&&(s=a-Math.min(i,o)),a-=1;a>=s;){if(c[0][a]>c[1][a])return 1;if(c[0][a]===c[1][a]){if(a===s)return 0;a-=1}else if(c[0][a]<c[1][a])return-1}},t.map=function(t,e){var r,n=[];if(Array.prototype.map)return Array.prototype.map.call(t,e);for(r=0;r<t.length;r+=1)n.push(e(t[r]));return n},t.find=function(t,e){var r,n;if(Array.prototype.find)return Array.prototype.find.call(t,e);for(r=0,n=t.length;r<n;r+=1){var i=t[r];if(e(i,r))return i}},t.assign=function(t){for(var e,r,n=t,i=arguments.length,o=new Array(i>1?i-1:0),a=1;a<i;a++)o[a-1]=arguments[a];if(Object.assign)return Object.assign.apply(Object,[t].concat(o));var s=function(){var t=o[e];"object"==typeof t&&null!==t&&Object.keys(t).forEach((function(e){n[e]=t[e]}))};for(e=0,r=o.length;e<r;e+=1)s();return t},t.getBrowserAlias=function(t){return n.BROWSER_ALIASES_MAP[t]},t.getBrowserTypeByAlias=function(t){return n.BROWSER_MAP[t]||""},t}();e.default=i,t.exports=e.default},18:function(t,e,r){e.__esModule=!0,e.ENGINE_MAP=e.OS_MAP=e.PLATFORMS_MAP=e.BROWSER_MAP=e.BROWSER_ALIASES_MAP=void 0,e.BROWSER_ALIASES_MAP={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},e.BROWSER_MAP={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},e.PLATFORMS_MAP={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},e.OS_MAP={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},e.ENGINE_MAP={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"}},90:function(t,e,r){e.__esModule=!0,e.default=void 0;var n,i=(n=r(91))&&n.__esModule?n:{default:n},o=r(18);function a(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var s=function(){function t(){}var e,r,n;return t.getParser=function(t,e){if(void 0===e&&(e=!1),"string"!=typeof t)throw new Error("UserAgent should be a string");return new i.default(t,e)},t.parse=function(t){return new i.default(t).getResult()},e=t,n=[{key:"BROWSER_MAP",get:function(){return o.BROWSER_MAP}},{key:"ENGINE_MAP",get:function(){return o.ENGINE_MAP}},{key:"OS_MAP",get:function(){return o.OS_MAP}},{key:"PLATFORMS_MAP",get:function(){return o.PLATFORMS_MAP}}],(r=null)&&a(e.prototype,r),n&&a(e,n),t}();e.default=s,t.exports=e.default},91:function(t,e,r){e.__esModule=!0,e.default=void 0;var n=c(r(92)),i=c(r(93)),o=c(r(94)),a=c(r(95)),s=c(r(17));function c(t){return t&&t.__esModule?t:{default:t}}var u=function(){function t(t,e){if(void 0===e&&(e=!1),null==t||""===t)throw new Error("UserAgent parameter can't be empty");this._ua=t,this.parsedResult={},!0!==e&&this.parse()}var e=t.prototype;return e.getUA=function(){return this._ua},e.test=function(t){return t.test(this._ua)},e.parseBrowser=function(){var t=this;this.parsedResult.browser={};var e=s.default.find(n.default,(function(e){if("function"==typeof e.test)return e.test(t);if(e.test instanceof Array)return e.test.some((function(e){return t.test(e)}));throw new Error("Browser's test function is not valid")}));return e&&(this.parsedResult.browser=e.describe(this.getUA())),this.parsedResult.browser},e.getBrowser=function(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()},e.getBrowserName=function(t){return t?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""},e.getBrowserVersion=function(){return this.getBrowser().version},e.getOS=function(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()},e.parseOS=function(){var t=this;this.parsedResult.os={};var e=s.default.find(i.default,(function(e){if("function"==typeof e.test)return e.test(t);if(e.test instanceof Array)return e.test.some((function(e){return t.test(e)}));throw new Error("Browser's test function is not valid")}));return e&&(this.parsedResult.os=e.describe(this.getUA())),this.parsedResult.os},e.getOSName=function(t){var e=this.getOS().name;return t?String(e).toLowerCase()||"":e||""},e.getOSVersion=function(){return this.getOS().version},e.getPlatform=function(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()},e.getPlatformType=function(t){void 0===t&&(t=!1);var e=this.getPlatform().type;return t?String(e).toLowerCase()||"":e||""},e.parsePlatform=function(){var t=this;this.parsedResult.platform={};var e=s.default.find(o.default,(function(e){if("function"==typeof e.test)return e.test(t);if(e.test instanceof Array)return e.test.some((function(e){return t.test(e)}));throw new Error("Browser's test function is not valid")}));return e&&(this.parsedResult.platform=e.describe(this.getUA())),this.parsedResult.platform},e.getEngine=function(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()},e.getEngineName=function(t){return t?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""},e.parseEngine=function(){var t=this;this.parsedResult.engine={};var e=s.default.find(a.default,(function(e){if("function"==typeof e.test)return e.test(t);if(e.test instanceof Array)return e.test.some((function(e){return t.test(e)}));throw new Error("Browser's test function is not valid")}));return e&&(this.parsedResult.engine=e.describe(this.getUA())),this.parsedResult.engine},e.parse=function(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this},e.getResult=function(){return s.default.assign({},this.parsedResult)},e.satisfies=function(t){var e=this,r={},n=0,i={},o=0;if(Object.keys(t).forEach((function(e){var a=t[e];"string"==typeof a?(i[e]=a,o+=1):"object"==typeof a&&(r[e]=a,n+=1)})),n>0){var a=Object.keys(r),c=s.default.find(a,(function(t){return e.isOS(t)}));if(c){var u=this.satisfies(r[c]);if(void 0!==u)return u}var l=s.default.find(a,(function(t){return e.isPlatform(t)}));if(l){var d=this.satisfies(r[l]);if(void 0!==d)return d}}if(o>0){var p=Object.keys(i),f=s.default.find(p,(function(t){return e.isBrowser(t,!0)}));if(void 0!==f)return this.compareVersion(i[f])}},e.isBrowser=function(t,e){void 0===e&&(e=!1);var r=this.getBrowserName().toLowerCase(),n=t.toLowerCase(),i=s.default.getBrowserTypeByAlias(n);return e&&i&&(n=i.toLowerCase()),n===r},e.compareVersion=function(t){var e=[0],r=t,n=!1,i=this.getBrowserVersion();if("string"==typeof i)return">"===t[0]||"<"===t[0]?(r=t.substr(1),"="===t[1]?(n=!0,r=t.substr(2)):e=[],">"===t[0]?e.push(1):e.push(-1)):"="===t[0]?r=t.substr(1):"~"===t[0]&&(n=!0,r=t.substr(1)),e.indexOf(s.default.compareVersions(i,r,n))>-1},e.isOS=function(t){return this.getOSName(!0)===String(t).toLowerCase()},e.isPlatform=function(t){return this.getPlatformType(!0)===String(t).toLowerCase()},e.isEngine=function(t){return this.getEngineName(!0)===String(t).toLowerCase()},e.is=function(t,e){return void 0===e&&(e=!1),this.isBrowser(t,e)||this.isOS(t)||this.isPlatform(t)},e.some=function(t){var e=this;return void 0===t&&(t=[]),t.some((function(t){return e.is(t)}))},t}();e.default=u,t.exports=e.default},92:function(t,e,r){e.__esModule=!0,e.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=/version\/(\d+(\.?_?\d+)+)/i,a=[{test:[/googlebot/i],describe:function(t){var e={name:"Googlebot"},r=i.default.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,t)||i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/opera/i],describe:function(t){var e={name:"Opera"},r=i.default.getFirstMatch(o,t)||i.default.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/opr\/|opios/i],describe:function(t){var e={name:"Opera"},r=i.default.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,t)||i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/SamsungBrowser/i],describe:function(t){var e={name:"Samsung Internet for Android"},r=i.default.getFirstMatch(o,t)||i.default.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/Whale/i],describe:function(t){var e={name:"NAVER Whale Browser"},r=i.default.getFirstMatch(o,t)||i.default.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/MZBrowser/i],describe:function(t){var e={name:"MZ Browser"},r=i.default.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,t)||i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/focus/i],describe:function(t){var e={name:"Focus"},r=i.default.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,t)||i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/swing/i],describe:function(t){var e={name:"Swing"},r=i.default.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,t)||i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/coast/i],describe:function(t){var e={name:"Opera Coast"},r=i.default.getFirstMatch(o,t)||i.default.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe:function(t){var e={name:"Opera Touch"},r=i.default.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,t)||i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/yabrowser/i],describe:function(t){var e={name:"Yandex Browser"},r=i.default.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,t)||i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/ucbrowser/i],describe:function(t){var e={name:"UC Browser"},r=i.default.getFirstMatch(o,t)||i.default.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/Maxthon|mxios/i],describe:function(t){var e={name:"Maxthon"},r=i.default.getFirstMatch(o,t)||i.default.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/epiphany/i],describe:function(t){var e={name:"Epiphany"},r=i.default.getFirstMatch(o,t)||i.default.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/puffin/i],describe:function(t){var e={name:"Puffin"},r=i.default.getFirstMatch(o,t)||i.default.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/sleipnir/i],describe:function(t){var e={name:"Sleipnir"},r=i.default.getFirstMatch(o,t)||i.default.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/k-meleon/i],describe:function(t){var e={name:"K-Meleon"},r=i.default.getFirstMatch(o,t)||i.default.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/micromessenger/i],describe:function(t){var e={name:"WeChat"},r=i.default.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,t)||i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/qqbrowser/i],describe:function(t){var e={name:/qqbrowserlite/i.test(t)?"QQ Browser Lite":"QQ Browser"},r=i.default.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,t)||i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/msie|trident/i],describe:function(t){var e={name:"Internet Explorer"},r=i.default.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/\sedg\//i],describe:function(t){var e={name:"Microsoft Edge"},r=i.default.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/edg([ea]|ios)/i],describe:function(t){var e={name:"Microsoft Edge"},r=i.default.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/vivaldi/i],describe:function(t){var e={name:"Vivaldi"},r=i.default.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/seamonkey/i],describe:function(t){var e={name:"SeaMonkey"},r=i.default.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/sailfish/i],describe:function(t){var e={name:"Sailfish"},r=i.default.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,t);return r&&(e.version=r),e}},{test:[/silk/i],describe:function(t){var e={name:"Amazon Silk"},r=i.default.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/phantom/i],describe:function(t){var e={name:"PhantomJS"},r=i.default.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/slimerjs/i],describe:function(t){var e={name:"SlimerJS"},r=i.default.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(t){var e={name:"BlackBerry"},r=i.default.getFirstMatch(o,t)||i.default.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/(web|hpw)[o0]s/i],describe:function(t){var e={name:"WebOS Browser"},r=i.default.getFirstMatch(o,t)||i.default.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/bada/i],describe:function(t){var e={name:"Bada"},r=i.default.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/tizen/i],describe:function(t){var e={name:"Tizen"},r=i.default.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,t)||i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/qupzilla/i],describe:function(t){var e={name:"QupZilla"},r=i.default.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,t)||i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/firefox|iceweasel|fxios/i],describe:function(t){var e={name:"Firefox"},r=i.default.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/electron/i],describe:function(t){var e={name:"Electron"},r=i.default.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/MiuiBrowser/i],describe:function(t){var e={name:"Miui"},r=i.default.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/chromium/i],describe:function(t){var e={name:"Chromium"},r=i.default.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,t)||i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/chrome|crios|crmo/i],describe:function(t){var e={name:"Chrome"},r=i.default.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/GSA/i],describe:function(t){var e={name:"Google Search"},r=i.default.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:function(t){var e=!t.test(/like android/i),r=t.test(/android/i);return e&&r},describe:function(t){var e={name:"Android Browser"},r=i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/playstation 4/i],describe:function(t){var e={name:"PlayStation 4"},r=i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/safari|applewebkit/i],describe:function(t){var e={name:"Safari"},r=i.default.getFirstMatch(o,t);return r&&(e.version=r),e}},{test:[/.*/i],describe:function(t){var e=-1!==t.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:i.default.getFirstMatch(e,t),version:i.default.getSecondMatch(e,t)}}}];e.default=a,t.exports=e.default},93:function(t,e,r){e.__esModule=!0,e.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=r(18),a=[{test:[/Roku\/DVP/],describe:function(t){var e=i.default.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,t);return{name:o.OS_MAP.Roku,version:e}}},{test:[/windows phone/i],describe:function(t){var e=i.default.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,t);return{name:o.OS_MAP.WindowsPhone,version:e}}},{test:[/windows /i],describe:function(t){var e=i.default.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,t),r=i.default.getWindowsVersionName(e);return{name:o.OS_MAP.Windows,version:e,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(t){var e={name:o.OS_MAP.iOS},r=i.default.getSecondMatch(/(Version\/)(\d[\d.]+)/,t);return r&&(e.version=r),e}},{test:[/macintosh/i],describe:function(t){var e=i.default.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,t).replace(/[_\s]/g,"."),r=i.default.getMacOSVersionName(e),n={name:o.OS_MAP.MacOS,version:e};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe:function(t){var e=i.default.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,t).replace(/[_\s]/g,".");return{name:o.OS_MAP.iOS,version:e}}},{test:function(t){var e=!t.test(/like android/i),r=t.test(/android/i);return e&&r},describe:function(t){var e=i.default.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,t),r=i.default.getAndroidVersionName(e),n={name:o.OS_MAP.Android,version:e};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe:function(t){var e=i.default.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,t),r={name:o.OS_MAP.WebOS};return e&&e.length&&(r.version=e),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(t){var e=i.default.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,t)||i.default.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,t)||i.default.getFirstMatch(/\bbb(\d+)/i,t);return{name:o.OS_MAP.BlackBerry,version:e}}},{test:[/bada/i],describe:function(t){var e=i.default.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,t);return{name:o.OS_MAP.Bada,version:e}}},{test:[/tizen/i],describe:function(t){var e=i.default.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,t);return{name:o.OS_MAP.Tizen,version:e}}},{test:[/linux/i],describe:function(){return{name:o.OS_MAP.Linux}}},{test:[/CrOS/],describe:function(){return{name:o.OS_MAP.ChromeOS}}},{test:[/PlayStation 4/],describe:function(t){var e=i.default.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,t);return{name:o.OS_MAP.PlayStation4,version:e}}}];e.default=a,t.exports=e.default},94:function(t,e,r){e.__esModule=!0,e.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=r(18),a=[{test:[/googlebot/i],describe:function(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe:function(t){var e=i.default.getFirstMatch(/(can-l01)/i,t)&&"Nova",r={type:o.PLATFORMS_MAP.mobile,vendor:"Huawei"};return e&&(r.model=e),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(t){var e=t.test(/ipod|iphone/i),r=t.test(/like (ipod|iphone)/i);return e&&!r},describe:function(t){var e=i.default.getFirstMatch(/(ipod|iphone)/i,t);return{type:o.PLATFORMS_MAP.mobile,vendor:"Apple",model:e}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(t){return"blackberry"===t.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"BlackBerry"}}},{test:function(t){return"bada"===t.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(t){return"windows phone"===t.getBrowserName()},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Microsoft"}}},{test:function(t){var e=Number(String(t.getOSVersion()).split(".")[0]);return"android"===t.getOSName(!0)&&e>=3},describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(t){return"android"===t.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(t){return"macos"===t.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop,vendor:"Apple"}}},{test:function(t){return"windows"===t.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(t){return"linux"===t.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(t){return"playstation 4"===t.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}},{test:function(t){return"roku"===t.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}}];e.default=a,t.exports=e.default},95:function(t,e,r){e.__esModule=!0,e.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=r(18),a=[{test:function(t){return"microsoft edge"===t.getBrowserName(!0)},describe:function(t){if(/\sedg\//i.test(t))return{name:o.ENGINE_MAP.Blink};var e=i.default.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,t);return{name:o.ENGINE_MAP.EdgeHTML,version:e}}},{test:[/trident/i],describe:function(t){var e={name:o.ENGINE_MAP.Trident},r=i.default.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:function(t){return t.test(/presto/i)},describe:function(t){var e={name:o.ENGINE_MAP.Presto},r=i.default.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:function(t){var e=t.test(/gecko/i),r=t.test(/like gecko/i);return e&&!r},describe:function(t){var e={name:o.ENGINE_MAP.Gecko},r=i.default.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/(apple)?webkit\/537\.36/i],describe:function(){return{name:o.ENGINE_MAP.Blink}}},{test:[/(apple)?webkit/i],describe:function(t){var e={name:o.ENGINE_MAP.WebKit},r=i.default.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}}];e.default=a,t.exports=e.default}})}(el);var rl=r(el.exports),nl={},il=No,ol=Oo.concat("length","prototype");nl.f=Object.getOwnPropertyNames||function(t){return il(t,ol)};var al={},sl=pi,cl=Qr,ul=nn,ll=Array,dl=Math.max,pl=O,fl=Y,hl=nl.f,ml=function(t,e,r){for(var n=cl(t),i=sl(e,n),o=sl(void 0===r?n:r,n),a=ll(dl(o-i,0)),s=0;i<o;i++,s++)ul(a,s,t[i]);return a.length=s,a},vl="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];al.f=function(t){return vl&&"Window"==pl(t)?function(t){try{return hl(t)}catch(t){return ml(vl)}}(t):hl(fl(t))};var yl={};yl.f=Object.getOwnPropertySymbols;var gl={},bl=ne;gl.f=bl;var Cl=X,Sl=Bt,Tl=gl,_l=We.f,Rl=function(t){var e=Cl.Symbol||(Cl.Symbol={});Sl(e,t)||_l(e,t,{value:Tl.f(t)})},El=R,wl=$,Pl=ne,kl=ga,Al=function(){var t=wl("Symbol"),e=t&&t.prototype,r=e&&e.valueOf,n=Pl("toPrimitive");e&&!e[n]&&kl(e,n,(function(t){return El(r,this)}),{arity:1})},Il=br,xl=f,Ll=R,Dl=l,Nl=S,Ol=lt,Ml=n,Ul=Bt,Vl=d,Fl=Je,jl=Y,Bl=fe,Wl=ki,Gl=x,Zl=sa,Yl=Vo,Hl=nl,Jl=al,Xl=yl,Kl=C,ql=We,Ql=ko,zl=E,$l=ga,td=At.exports,ed=ro,rd=Ht,nd=ne,id=gl,od=Rl,ad=Al,sd=Ma,cd=So,ud=Ns.forEach,ld=eo("hidden"),dd="Symbol",pd=cd.set,fd=cd.getterFor(dd),hd=Object.prototype,md=xl.Symbol,vd=md&&md.prototype,yd=xl.TypeError,gd=xl.QObject,bd=Kl.f,Cd=ql.f,Sd=Jl.f,Td=zl.f,_d=Dl([].push),Rd=td("symbols"),Ed=td("op-symbols"),wd=td("wks"),Pd=!gd||!gd.prototype||!gd.prototype.findChild,kd=Nl&&Ml((function(){return 7!=Zl(Cd({},"a",{get:function(){return Cd(this,"a",{value:7}).a}})).a}))?function(t,e,r){var n=bd(hd,e);n&&delete hd[e],Cd(t,e,r),n&&t!==hd&&Cd(hd,e,n)}:Cd,Ad=function(t,e){var r=Rd[t]=Zl(vd);return pd(r,{type:dd,tag:t,description:e}),Nl||(r.description=e),r},Id=function(t,e,r){t===hd&&Id(Ed,e,r),Fl(t);var n=Bl(e);return Fl(r),Ul(Rd,n)?(r.enumerable?(Ul(t,ld)&&t[ld][n]&&(t[ld][n]=!1),r=Zl(r,{enumerable:Gl(0,!1)})):(Ul(t,ld)||Cd(t,ld,Gl(1,{})),t[ld][n]=!0),kd(t,n,r)):Cd(t,n,r)},xd=function(t,e){Fl(t);var r=jl(e),n=Yl(r).concat(Od(r));return ud(n,(function(e){Nl&&!Ll(Ld,r,e)||Id(t,e,r[e])})),t},Ld=function(t){var e=Bl(t),r=Ll(Td,this,e);return!(this===hd&&Ul(Rd,e)&&!Ul(Ed,e))&&(!(r||!Ul(this,e)||!Ul(Rd,e)||Ul(this,ld)&&this[ld][e])||r)},Dd=function(t,e){var r=jl(t),n=Bl(e);if(r!==hd||!Ul(Rd,n)||Ul(Ed,n)){var i=bd(r,n);return!i||!Ul(Rd,n)||Ul(r,ld)&&r[ld][n]||(i.enumerable=!0),i}},Nd=function(t){var e=Sd(jl(t)),r=[];return ud(e,(function(t){Ul(Rd,t)||Ul(ed,t)||_d(r,t)})),r},Od=function(t){var e=t===hd,r=Sd(e?Ed:jl(t)),n=[];return ud(r,(function(t){!Ul(Rd,t)||e&&!Ul(hd,t)||_d(n,Rd[t])})),n};Ol||(md=function(){if(Vl(vd,this))throw yd("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?Wl(arguments[0]):void 0,e=rd(t),r=function(t){this===hd&&Ll(r,Ed,t),Ul(this,ld)&&Ul(this[ld],e)&&(this[ld][e]=!1),kd(this,e,Gl(1,t))};return Nl&&Pd&&kd(hd,e,{configurable:!0,set:r}),Ad(e,t)},$l(vd=md.prototype,"toString",(function(){return fd(this).tag})),$l(md,"withoutSetter",(function(t){return Ad(rd(t),t)})),zl.f=Ld,ql.f=Id,Ql.f=xd,Kl.f=Dd,Hl.f=Jl.f=Nd,Xl.f=Od,id.f=function(t){return Ad(nd(t),t)},Nl&&Cd(vd,"description",{configurable:!0,get:function(){return fd(this).description}})),Il({global:!0,constructor:!0,wrap:!0,forced:!Ol,sham:!Ol},{Symbol:md}),ud(Yl(wd),(function(t){od(t)})),Il({target:dd,stat:!0,forced:!Ol},{useSetter:function(){Pd=!0},useSimple:function(){Pd=!1}}),Il({target:"Object",stat:!0,forced:!Ol,sham:!Nl},{create:function(t,e){return void 0===e?Zl(t):xd(Zl(t),e)},defineProperty:Id,defineProperties:xd,getOwnPropertyDescriptor:Dd}),Il({target:"Object",stat:!0,forced:!Ol},{getOwnPropertyNames:Nd}),ad(),sd(md,dd),ed[ld]=!0;var Md=lt&&!!Symbol.for&&!!Symbol.keyFor,Ud=br,Vd=$,Fd=Bt,jd=ki,Bd=At.exports,Wd=Md,Gd=Bd("string-to-symbol-registry"),Zd=Bd("symbol-to-string-registry");Ud({target:"Symbol",stat:!0,forced:!Wd},{for:function(t){var e=jd(t);if(Fd(Gd,e))return Gd[e];var r=Vd("Symbol")(e);return Gd[e]=r,Zd[r]=e,r}});var Yd=br,Hd=Bt,Jd=vt,Xd=gt,Kd=Md,qd=(0,At.exports)("symbol-to-string-registry");Yd({target:"Symbol",stat:!0,forced:!Kd},{keyFor:function(t){if(!Jd(t))throw TypeError(Xd(t)+" is not a symbol");if(Hd(qd,t))return qd[t]}});var Qd=br,zd=$,$d=g,tp=R,ep=l,rp=n,np=Wr,ip=b,op=J,ap=vt,sp=Cr,cp=lt,up=zd("JSON","stringify"),lp=ep(/./.exec),dp=ep("".charAt),pp=ep("".charCodeAt),fp=ep("".replace),hp=ep(1..toString),mp=/[\uD800-\uDFFF]/g,vp=/^[\uD800-\uDBFF]$/,yp=/^[\uDC00-\uDFFF]$/,gp=!cp||rp((function(){var t=zd("Symbol")();return"[null]"!=up([t])||"{}"!=up({a:t})||"{}"!=up(Object(t))})),bp=rp((function(){return'"\\udf06\\ud834"'!==up("\udf06\ud834")||'"\\udead"'!==up("\udead")})),Cp=function(t,e){var r=sp(arguments),n=e;if((op(e)||void 0!==t)&&!ap(t))return np(e)||(e=function(t,e){if(ip(n)&&(e=tp(n,this,t,e)),!ap(e))return e}),r[1]=e,$d(up,null,r)},Sp=function(t,e,r){var n=dp(r,e-1),i=dp(r,e+1);return lp(vp,t)&&!lp(yp,i)||lp(yp,t)&&!lp(vp,n)?"\\u"+hp(pp(t,0),16):t};up&&Qd({target:"JSON",stat:!0,arity:3,forced:gp||bp},{stringify:function(t,e,r){var n=sp(arguments),i=$d(gp?Cp:up,null,n);return bp&&"string"==typeof i?fp(i,mp,Sp):i}});var Tp=yl,_p=Vt;br({target:"Object",stat:!0,forced:!lt||n((function(){Tp.f(1)}))},{getOwnPropertySymbols:function(t){var e=Tp.f;return e?e(_p(t)):[]}}),Rl("asyncIterator"),Rl("hasInstance"),Rl("isConcatSpreadable"),Rl("iterator"),Rl("match"),Rl("matchAll"),Rl("replace"),Rl("search"),Rl("species"),Rl("split");var Rp=Al;Rl("toPrimitive"),Rp();var Ep=$,wp=Ma;Rl("toStringTag"),wp(Ep("Symbol"),"Symbol"),Rl("unscopables"),Ma(f.JSON,"JSON",!0);var Pp=X.Symbol;Rl("asyncDispose"),Rl("dispose"),Rl("matcher"),Rl("metadataKey"),Rl("observable"),Rl("metadata"),Rl("patternMatch"),Rl("replaceAll");var kp=Pp,Ap=l,Ip=Hr,xp=ki,Lp=W,Dp=Ap("".charAt),Np=Ap("".charCodeAt),Op=Ap("".slice),Mp=function(t){return function(e,r){var n,i,o=xp(Lp(e)),a=Ip(r),s=o.length;return a<0||a>=s?t?"":void 0:(n=Np(o,a))<55296||n>56319||a+1===s||(i=Np(o,a+1))<56320||i>57343?t?Dp(o,a):n:t?Op(o,a,a+2):i-56320+(n-55296<<10)+65536}},Up={codeAt:Mp(!1),charAt:Mp(!0)},Vp=Up.charAt,Fp=ki,jp=So,Bp=ls,Wp="String Iterator",Gp=jp.set,Zp=jp.getterFor(Wp);Bp(String,"String",(function(t){Gp(this,{type:Wp,string:Fp(t),index:0})}),(function(){var t,e=Zp(this),r=e.string,n=e.index;return n>=r.length?{value:void 0,done:!0}:(t=Vp(r,n),e.index+=t.length,{value:t,done:!1})}));var Yp=gl.f("iterator");function Hp(t){return Hp="function"==typeof kp&&"symbol"==typeof Yp?function(t){return typeof t}:function(t){return t&&"function"==typeof kp&&t.constructor===kp&&t!==kp.prototype?"symbol":typeof t},Hp(t)}br({target:"Object",stat:!0,sham:!S},{create:sa});var Jp=X.Object,Xp=function(t,e){return Jp.create(t,e)},Kp=br,qp=Wr,Qp=xn,zp=J,$p=pi,tf=Qr,ef=Y,rf=nn,nf=ne,of=Cr,af=Wn("slice"),sf=nf("species"),cf=Array,uf=Math.max;Kp({target:"Array",proto:!0,forced:!af},{slice:function(t,e){var r,n,i,o=ef(this),a=tf(o),s=$p(t,a),c=$p(void 0===e?a:e,a);if(qp(o)&&(r=o.constructor,(Qp(r)&&(r===cf||qp(r.prototype))||zp(r)&&null===(r=r[sf]))&&(r=void 0),r===cf||void 0===r))return of(o,s,c);for(n=new(void 0===r?cf:r)(uf(c-s,0)),i=0;s<c;s++,i++)s in o&&rf(n,i,o[s]);return n.length=i,n}});var lf=Or("Array").slice,df=d,pf=lf,ff=Array.prototype,hf=function(t){var e=t.slice;return t===ff||df(ff,t)&&e===ff.slice?pf:e},mf=X.Object.getOwnPropertySymbols,vf={exports:{}},yf=br,gf=S,bf=We.f;yf({target:"Object",stat:!0,forced:Object.defineProperty!==bf,sham:!gf},{defineProperty:bf});var Cf=X.Object,Sf=vf.exports=function(t,e,r){return Cf.defineProperty(t,e,r)};Cf.defineProperty.sham&&(Sf.sham=!0);var Tf=vf.exports,_f=Vt,Rf=Vo;br({target:"Object",stat:!0,forced:n((function(){Rf(1)}))},{keys:function(t){return Rf(_f(t))}});var Ef=X.Object.keys,wf={exports:{}},Pf=br,kf=n,Af=Y,If=C.f,xf=S,Lf=kf((function(){If(1)}));Pf({target:"Object",stat:!0,forced:!xf||Lf,sham:!xf},{getOwnPropertyDescriptor:function(t,e){return If(Af(t),e)}});var Df=X.Object,Nf=wf.exports=function(t,e){return Df.getOwnPropertyDescriptor(t,e)};Df.getOwnPropertyDescriptor.sham&&(Nf.sham=!0);var Of=wf.exports,Mf=$,Uf=nl,Vf=yl,Ff=Je,jf=l([].concat),Bf=Mf("Reflect","ownKeys")||function(t){var e=Uf.f(Ff(t)),r=Vf.f;return r?jf(e,r(t)):e},Wf=Bf,Gf=Y,Zf=C,Yf=nn;br({target:"Object",stat:!0,sham:!S},{getOwnPropertyDescriptors:function(t){for(var e,r,n=Gf(t),i=Zf.f,o=Wf(n),a={},s=0;o.length>s;)void 0!==(r=i(n,e=o[s++]))&&Yf(a,e,r);return a}});var Hf=X.Object.getOwnPropertyDescriptors,Jf={exports:{}},Xf=br,Kf=S,qf=ko.f;Xf({target:"Object",stat:!0,forced:Object.defineProperties!==qf,sham:!Kf},{defineProperties:qf});var Qf=X.Object,zf=Jf.exports=function(t,e){return Qf.defineProperties(t,e)};Qf.defineProperties.sham&&(zf.sham=!0);var $f=Jf.exports,th=gl.f("toPrimitive"),eh=g,rh=Y,nh=Hr,ih=Qr,oh=Ms,ah=Math.min,sh=[].lastIndexOf,ch=!!sh&&1/[1].lastIndexOf(1,-0)<0,uh=oh("lastIndexOf"),lh=ch||!uh?function(t){if(ch)return eh(sh,this,arguments)||0;var e=rh(this),r=ih(e),n=r-1;for(arguments.length>1&&(n=ah(n,nh(arguments[1]))),n<0&&(n=r+n);n>=0;n--)if(n in e&&e[n]===t)return n||0;return-1}:sh;br({target:"Array",proto:!0,forced:lh!==[].lastIndexOf},{lastIndexOf:lh});var dh=Or("Array").lastIndexOf,ph=d,fh=dh,hh=Array.prototype,mh=function(t){var e=t.lastIndexOf;return t===hh||ph(hh,t)&&e===hh.lastIndexOf?fh:e},vh=S,yh=l,gh=R,bh=n,Ch=Vo,Sh=yl,Th=E,_h=Vt,Rh=j,Eh=Object.assign,wh=Object.defineProperty,Ph=yh([].concat),kh=!Eh||bh((function(){if(vh&&1!==Eh({b:1},Eh(wh({},"a",{enumerable:!0,get:function(){wh(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},r=Symbol(),n="abcdefghijklmnopqrst";return t[r]=7,n.split("").forEach((function(t){e[t]=t})),7!=Eh({},t)[r]||Ch(Eh({},e)).join("")!=n}))?function(t,e){for(var r=_h(t),n=arguments.length,i=1,o=Sh.f,a=Th.f;n>i;)for(var s,c=Rh(arguments[i++]),u=o?Ph(Ch(c),o(c)):Ch(c),l=u.length,d=0;l>d;)s=u[d++],vh&&!gh(a,c,s)||(r[s]=c[s]);return r}:Eh,Ah=kh;br({target:"Object",stat:!0,arity:2,forced:Object.assign!==Ah},{assign:Ah});var Ih=X.Object.assign,xh={exports:{}},Lh=n((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}})),Dh=n,Nh=J,Oh=O,Mh=Lh,Uh=Object.isExtensible,Vh=Dh((function(){Uh(1)}))||Mh?function(t){return!!Nh(t)&&((!Mh||"ArrayBuffer"!=Oh(t))&&(!Uh||Uh(t)))}:Uh,Fh=!n((function(){return Object.isExtensible(Object.preventExtensions({}))})),jh=br,Bh=l,Wh=ro,Gh=J,Zh=Bt,Yh=We.f,Hh=nl,Jh=al,Xh=Vh,Kh=Fh,qh=!1,Qh=Ht("meta"),zh=0,$h=function(t){Yh(t,Qh,{value:{objectID:"O"+zh++,weakData:{}}})},tm=xh.exports={enable:function(){tm.enable=function(){},qh=!0;var t=Hh.f,e=Bh([].splice),r={};r[Qh]=1,t(r).length&&(Hh.f=function(r){for(var n=t(r),i=0,o=n.length;i<o;i++)if(n[i]===Qh){e(n,i,1);break}return n},jh({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:Jh.f}))},fastKey:function(t,e){if(!Gh(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!Zh(t,Qh)){if(!Xh(t))return"F";if(!e)return"E";$h(t)}return t[Qh].objectID},getWeakData:function(t,e){if(!Zh(t,Qh)){if(!Xh(t))return!0;if(!e)return!1;$h(t)}return t[Qh].weakData},onFreeze:function(t){return Kh&&qh&&Xh(t)&&!Zh(t,Qh)&&$h(t),t}};Wh[Qh]=!0;var em=Ji,rm=ne("iterator"),nm=Array.prototype,im=fn,om=Rt,am=Ji,sm=ne("iterator"),cm=function(t){if(null!=t)return om(t,sm)||om(t,"@@iterator")||am[im(t)]},um=R,lm=Tt,dm=Je,pm=gt,fm=cm,hm=TypeError,mm=function(t,e){var r=arguments.length<2?fm(t):e;if(lm(r))return dm(um(r,t));throw hm(pm(t)+" is not iterable")},vm=R,ym=Je,gm=Rt,bm=Be,Cm=R,Sm=Je,Tm=gt,_m=function(t){return void 0!==t&&(em.Array===t||nm[rm]===t)},Rm=Qr,Em=d,wm=mm,Pm=cm,km=function(t,e,r){var n,i;ym(t);try{if(!(n=gm(t,"return"))){if("throw"===e)throw r;return r}n=vm(n,t)}catch(t){i=!0,n=t}if("throw"===e)throw r;if(i)throw n;return ym(n),r},Am=TypeError,Im=function(t,e){this.stopped=t,this.result=e},xm=Im.prototype,Lm=function(t,e,r){var n,i,o,a,s,c,u,l=r&&r.that,d=!(!r||!r.AS_ENTRIES),p=!(!r||!r.IS_ITERATOR),f=!(!r||!r.INTERRUPTED),h=bm(e,l),m=function(t){return n&&km(n,"normal",t),new Im(!0,t)},v=function(t){return d?(Sm(t),f?h(t[0],t[1],m):h(t[0],t[1])):f?h(t,m):h(t)};if(p)n=t;else{if(!(i=Pm(t)))throw Am(Tm(t)+" is not iterable");if(_m(i)){for(o=0,a=Rm(t);a>o;o++)if((s=v(t[o]))&&Em(xm,s))return s;return new Im(!1)}n=wm(t,i)}for(c=n.next;!(u=Cm(c,n)).done;){try{s=v(u.value)}catch(t){km(n,"throw",t)}if("object"==typeof s&&s&&Em(xm,s))return s}return new Im(!1)},Dm=d,Nm=TypeError,Om=function(t,e){if(Dm(e,t))return t;throw Nm("Incorrect invocation")},Mm=br,Um=f,Vm=xh.exports,Fm=n,jm=sr,Bm=Lm,Wm=Om,Gm=b,Zm=J,Ym=Ma,Hm=We.f,Jm=Ns.forEach,Xm=S,Km=So.set,qm=So.getterFor,Qm=function(t,e,r){var n,i=-1!==t.indexOf("Map"),o=-1!==t.indexOf("Weak"),a=i?"set":"add",s=Um[t],c=s&&s.prototype,u={};if(Xm&&Gm(s)&&(o||c.forEach&&!Fm((function(){(new s).entries().next()})))){var l=(n=e((function(e,r){Km(Wm(e,l),{type:t,collection:new s}),null!=r&&Bm(r,e[a],{that:e,AS_ENTRIES:i})}))).prototype,d=qm(t);Jm(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(t){var e="add"==t||"set"==t;!(t in c)||o&&"clear"==t||jm(l,t,(function(r,n){var i=d(this).collection;if(!e&&o&&!Zm(r))return"get"==t&&void 0;var a=i[t](0===r?0:r,n);return e?this:a}))})),o||Hm(l,"size",{configurable:!0,get:function(){return d(this).collection.size}})}else n=r.getConstructor(e,t,i,a),Vm.enable();return Ym(n,t,!1,!0),u[t]=n,Mm({global:!0,forced:!0},u),o||r.setStrong(n,t,i),n},zm=ga,$m=$,tv=We,ev=S,rv=ne("species"),nv=function(t){var e=$m(t),r=tv.f;ev&&e&&!e[rv]&&r(e,rv,{configurable:!0,get:function(){return this}})},iv=We.f,ov=sa,av=function(t,e,r){for(var n in e)r&&r.unsafe&&t[n]?t[n]=e[n]:zm(t,n,e[n],r);return t},sv=Be,cv=Om,uv=Lm,lv=ls,dv=nv,pv=S,fv=xh.exports.fastKey,hv=So.set,mv=So.getterFor,vv={getConstructor:function(t,e,r,n){var i=t((function(t,i){cv(t,o),hv(t,{type:e,index:ov(null),first:void 0,last:void 0,size:0}),pv||(t.size=0),null!=i&&uv(i,t[n],{that:t,AS_ENTRIES:r})})),o=i.prototype,a=mv(e),s=function(t,e,r){var n,i,o=a(t),s=c(t,e);return s?s.value=r:(o.last=s={index:i=fv(e,!0),key:e,value:r,previous:n=o.last,next:void 0,removed:!1},o.first||(o.first=s),n&&(n.next=s),pv?o.size++:t.size++,"F"!==i&&(o.index[i]=s)),t},c=function(t,e){var r,n=a(t),i=fv(e);if("F"!==i)return n.index[i];for(r=n.first;r;r=r.next)if(r.key==e)return r};return av(o,{clear:function(){for(var t=a(this),e=t.index,r=t.first;r;)r.removed=!0,r.previous&&(r.previous=r.previous.next=void 0),delete e[r.index],r=r.next;t.first=t.last=void 0,pv?t.size=0:this.size=0},delete:function(t){var e=this,r=a(e),n=c(e,t);if(n){var i=n.next,o=n.previous;delete r.index[n.index],n.removed=!0,o&&(o.next=i),i&&(i.previous=o),r.first==n&&(r.first=i),r.last==n&&(r.last=o),pv?r.size--:e.size--}return!!n},forEach:function(t){for(var e,r=a(this),n=sv(t,arguments.length>1?arguments[1]:void 0);e=e?e.next:r.first;)for(n(e.value,e.key,this);e&&e.removed;)e=e.previous},has:function(t){return!!c(this,t)}}),av(o,r?{get:function(t){var e=c(this,t);return e&&e.value},set:function(t,e){return s(this,0===t?0:t,e)}}:{add:function(t){return s(this,t=0===t?0:t,t)}}),pv&&iv(o,"size",{get:function(){return a(this).size}}),i},setStrong:function(t,e,r){var n=e+" Iterator",i=mv(e),o=mv(n);lv(t,e,(function(t,e){hv(this,{type:n,target:t,state:i(t),kind:e,last:void 0})}),(function(){for(var t=o(this),e=t.kind,r=t.last;r&&r.removed;)r=r.previous;return t.target&&(t.last=r=r?r.next:t.state.first)?"keys"==e?{value:r.key,done:!1}:"values"==e?{value:r.value,done:!1}:{value:[r.key,r.value],done:!1}:(t.target=void 0,{value:void 0,done:!0})}),r?"entries":"values",!r,!0),dv(e)}};Qm("Set",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),vv);var yv=X.Set,gv=xn,bv=gt,Cv=TypeError,Sv=function(t){if(gv(t))return t;throw Cv(bv(t)+" is not a constructor")},Tv=Be,_v=R,Rv=Tt,Ev=Sv,wv=Lm,Pv=[].push,kv=function(t){var e,r,n,i,o=arguments.length,a=o>1?arguments[1]:void 0;return Ev(this),(e=void 0!==a)&&Rv(a),null==t?new this:(r=[],e?(n=0,i=Tv(a,o>2?arguments[2]:void 0),wv(t,(function(t){_v(Pv,r,i(t,n++))}))):wv(t,Pv,{that:r}),new this(r))};br({target:"Set",stat:!0,forced:!0},{from:kv});var Av=Cr,Iv=function(){return new this(Av(arguments))};br({target:"Set",stat:!0,forced:!0},{of:Iv});var xv=R,Lv=Tt,Dv=Je,Nv=function(){for(var t=Dv(this),e=Lv(t.add),r=0,n=arguments.length;r<n;r++)xv(e,t,arguments[r]);return t};br({target:"Set",proto:!0,real:!0,forced:!0},{addAll:Nv});var Ov=R,Mv=Tt,Uv=Je,Vv=function(){for(var t,e=Uv(this),r=Mv(e.delete),n=!0,i=0,o=arguments.length;i<o;i++)t=Ov(r,e,arguments[i]),n=n&&t;return!!n};br({target:"Set",proto:!0,real:!0,forced:!0},{deleteAll:Vv});var Fv=mm,jv=Je,Bv=Be,Wv=Fv,Gv=Lm;br({target:"Set",proto:!0,real:!0,forced:!0},{every:function(t){var e=jv(this),r=Wv(e),n=Bv(t,arguments.length>1?arguments[1]:void 0);return!Gv(r,(function(t,r){if(!n(t,t,e))return r()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var Zv=Je,Yv=Sv,Hv=ne("species"),Jv=function(t,e){var r,n=Zv(t).constructor;return void 0===n||null==(r=Zv(n)[Hv])?e:Yv(r)},Xv=$,Kv=R,qv=Tt,Qv=Je,zv=Jv,$v=Lm;br({target:"Set",proto:!0,real:!0,forced:!0},{difference:function(t){var e=Qv(this),r=new(zv(e,Xv("Set")))(e),n=qv(r.delete);return $v(t,(function(t){Kv(n,r,t)})),r}});var ty=$,ey=R,ry=Tt,ny=Je,iy=Be,oy=Jv,ay=Fv,sy=Lm;br({target:"Set",proto:!0,real:!0,forced:!0},{filter:function(t){var e=ny(this),r=ay(e),n=iy(t,arguments.length>1?arguments[1]:void 0),i=new(oy(e,ty("Set"))),o=ry(i.add);return sy(r,(function(t){n(t,t,e)&&ey(o,i,t)}),{IS_ITERATOR:!0}),i}});var cy=Je,uy=Be,ly=Fv,dy=Lm;br({target:"Set",proto:!0,real:!0,forced:!0},{find:function(t){var e=cy(this),r=ly(e),n=uy(t,arguments.length>1?arguments[1]:void 0);return dy(r,(function(t,r){if(n(t,t,e))return r(t)}),{IS_ITERATOR:!0,INTERRUPTED:!0}).result}});var py=$,fy=R,hy=Tt,my=Je,vy=Jv,yy=Lm;br({target:"Set",proto:!0,real:!0,forced:!0},{intersection:function(t){var e=my(this),r=new(vy(e,py("Set"))),n=hy(e.has),i=hy(r.add);return yy(t,(function(t){fy(n,e,t)&&fy(i,r,t)})),r}});var gy=R,by=Tt,Cy=Je,Sy=Lm;br({target:"Set",proto:!0,real:!0,forced:!0},{isDisjointFrom:function(t){var e=Cy(this),r=by(e.has);return!Sy(t,(function(t,n){if(!0===gy(r,e,t))return n()}),{INTERRUPTED:!0}).stopped}});var Ty=$,_y=R,Ry=Tt,Ey=b,wy=Je,Py=mm,ky=Lm;br({target:"Set",proto:!0,real:!0,forced:!0},{isSubsetOf:function(t){var e=Py(this),r=wy(t),n=r.has;return Ey(n)||(r=new(Ty("Set"))(t),n=Ry(r.has)),!ky(e,(function(t,e){if(!1===_y(n,r,t))return e()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var Ay=R,Iy=Tt,xy=Je,Ly=Lm;br({target:"Set",proto:!0,real:!0,forced:!0},{isSupersetOf:function(t){var e=xy(this),r=Iy(e.has);return!Ly(t,(function(t,n){if(!1===Ay(r,e,t))return n()}),{INTERRUPTED:!0}).stopped}});var Dy=br,Ny=Je,Oy=ki,My=Fv,Uy=Lm,Vy=l([].join),Fy=[].push;Dy({target:"Set",proto:!0,real:!0,forced:!0},{join:function(t){var e=Ny(this),r=My(e),n=void 0===t?",":Oy(t),i=[];return Uy(r,Fy,{that:i,IS_ITERATOR:!0}),Vy(i,n)}});var jy=$,By=Be,Wy=R,Gy=Tt,Zy=Je,Yy=Jv,Hy=Fv,Jy=Lm;br({target:"Set",proto:!0,real:!0,forced:!0},{map:function(t){var e=Zy(this),r=Hy(e),n=By(t,arguments.length>1?arguments[1]:void 0),i=new(Yy(e,jy("Set"))),o=Gy(i.add);return Jy(r,(function(t){Wy(o,i,n(t,t,e))}),{IS_ITERATOR:!0}),i}});var Xy=Tt,Ky=Je,qy=Fv,Qy=Lm,zy=TypeError;br({target:"Set",proto:!0,real:!0,forced:!0},{reduce:function(t){var e=Ky(this),r=qy(e),n=arguments.length<2,i=n?void 0:arguments[1];if(Xy(t),Qy(r,(function(r){n?(n=!1,i=r):i=t(i,r,r,e)}),{IS_ITERATOR:!0}),n)throw zy("Reduce of empty set with no initial value");return i}});var $y=Je,tg=Be,eg=Fv,rg=Lm;br({target:"Set",proto:!0,real:!0,forced:!0},{some:function(t){var e=$y(this),r=eg(e),n=tg(t,arguments.length>1?arguments[1]:void 0);return rg(r,(function(t,r){if(n(t,t,e))return r()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var ng=$,ig=R,og=Tt,ag=Je,sg=Jv,cg=Lm;br({target:"Set",proto:!0,real:!0,forced:!0},{symmetricDifference:function(t){var e=ag(this),r=new(sg(e,ng("Set")))(e),n=og(r.delete),i=og(r.add);return cg(t,(function(t){ig(n,r,t)||ig(i,r,t)})),r}});var ug=$,lg=Tt,dg=Je,pg=Jv,fg=Lm;br({target:"Set",proto:!0,real:!0,forced:!0},{union:function(t){var e=dg(this),r=new(pg(e,ug("Set")))(e);return fg(t,lg(r.add),{that:r}),r}});var hg=yv;Qm("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),vv);var mg=X.Map;br({target:"Map",stat:!0,forced:!0},{from:kv}),br({target:"Map",stat:!0,forced:!0},{of:Iv}),br({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:Vv});var vg=R,yg=Tt,gg=Je,bg=function(t,e){var r=gg(this),n=yg(r.get),i=yg(r.has),o=yg(r.set),a=vg(i,r,t)&&"update"in e?e.update(vg(n,r,t),t,r):e.insert(t,r);return vg(o,r,t,a),a};br({target:"Map",proto:!0,real:!0,forced:!0},{emplace:bg});var Cg=mm,Sg=Je,Tg=Be,_g=Cg,Rg=Lm;br({target:"Map",proto:!0,real:!0,forced:!0},{every:function(t){var e=Sg(this),r=_g(e),n=Tg(t,arguments.length>1?arguments[1]:void 0);return!Rg(r,(function(t,r,i){if(!n(r,t,e))return i()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var Eg=$,wg=Be,Pg=R,kg=Tt,Ag=Je,Ig=Jv,xg=Cg,Lg=Lm;br({target:"Map",proto:!0,real:!0,forced:!0},{filter:function(t){var e=Ag(this),r=xg(e),n=wg(t,arguments.length>1?arguments[1]:void 0),i=new(Ig(e,Eg("Map"))),o=kg(i.set);return Lg(r,(function(t,r){n(r,t,e)&&Pg(o,i,t,r)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),i}});var Dg=Je,Ng=Be,Og=Cg,Mg=Lm;br({target:"Map",proto:!0,real:!0,forced:!0},{find:function(t){var e=Dg(this),r=Og(e),n=Ng(t,arguments.length>1?arguments[1]:void 0);return Mg(r,(function(t,r,i){if(n(r,t,e))return i(r)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}});var Ug=Je,Vg=Be,Fg=Cg,jg=Lm;br({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function(t){var e=Ug(this),r=Fg(e),n=Vg(t,arguments.length>1?arguments[1]:void 0);return jg(r,(function(t,r,i){if(n(r,t,e))return i(t)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}});var Bg=br,Wg=R,Gg=Tt,Zg=mm,Yg=Lm,Hg=l([].push);Bg({target:"Map",stat:!0,forced:!0},{groupBy:function(t,e){Gg(e);var r=Zg(t),n=new this,i=Gg(n.has),o=Gg(n.get),a=Gg(n.set);return Yg(r,(function(t){var r=e(t);Wg(i,n,r)?Hg(Wg(o,n,r),t):Wg(a,n,r,[t])}),{IS_ITERATOR:!0}),n}});var Jg=Je,Xg=Cg,Kg=function(t,e){return t===e||t!=t&&e!=e},qg=Lm;br({target:"Map",proto:!0,real:!0,forced:!0},{includes:function(t){return qg(Xg(Jg(this)),(function(e,r,n){if(Kg(r,t))return n()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var Qg=R,zg=Lm,$g=Tt;br({target:"Map",stat:!0,forced:!0},{keyBy:function(t,e){var r=new this;$g(e);var n=$g(r.set);return zg(t,(function(t){Qg(n,r,e(t),t)})),r}});var tb=Je,eb=Cg,rb=Lm;br({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function(t){return rb(eb(tb(this)),(function(e,r,n){if(r===t)return n(e)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}});var nb=$,ib=Be,ob=R,ab=Tt,sb=Je,cb=Jv,ub=Cg,lb=Lm;br({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function(t){var e=sb(this),r=ub(e),n=ib(t,arguments.length>1?arguments[1]:void 0),i=new(cb(e,nb("Map"))),o=ab(i.set);return lb(r,(function(t,r){ob(o,i,n(r,t,e),r)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),i}});var db=$,pb=Be,fb=R,hb=Tt,mb=Je,vb=Jv,yb=Cg,gb=Lm;br({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function(t){var e=mb(this),r=yb(e),n=pb(t,arguments.length>1?arguments[1]:void 0),i=new(vb(e,db("Map"))),o=hb(i.set);return gb(r,(function(t,r){fb(o,i,t,n(r,t,e))}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),i}});var bb=Tt,Cb=Je,Sb=Lm;br({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function(t){for(var e=Cb(this),r=bb(e.set),n=arguments.length,i=0;i<n;)Sb(arguments[i++],r,{that:e,AS_ENTRIES:!0});return e}});var Tb=Je,_b=Tt,Rb=Cg,Eb=Lm,wb=TypeError;br({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function(t){var e=Tb(this),r=Rb(e),n=arguments.length<2,i=n?void 0:arguments[1];if(_b(t),Eb(r,(function(r,o){n?(n=!1,i=o):i=t(i,o,r,e)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),n)throw wb("Reduce of empty map with no initial value");return i}});var Pb=Je,kb=Be,Ab=Cg,Ib=Lm;br({target:"Map",proto:!0,real:!0,forced:!0},{some:function(t){var e=Pb(this),r=Ab(e),n=kb(t,arguments.length>1?arguments[1]:void 0);return Ib(r,(function(t,r,i){if(n(r,t,e))return i()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var xb=R,Lb=Je,Db=Tt,Nb=TypeError;br({target:"Map",proto:!0,real:!0,forced:!0},{update:function(t,e){var r=Lb(this),n=Db(r.get),i=Db(r.has),o=Db(r.set),a=arguments.length;Db(e);var s=xb(i,r,t);if(!s&&a<3)throw Nb("Updating absent value");var c=s?xb(n,r,t):Db(a>2?arguments[2]:void 0)(t,r);return xb(o,r,t,e(c,t,r)),r}});var Ob=R,Mb=Tt,Ub=b,Vb=Je,Fb=TypeError,jb=function(t,e){var r,n=Vb(this),i=Mb(n.get),o=Mb(n.has),a=Mb(n.set),s=arguments.length>2?arguments[2]:void 0;if(!Ub(e)&&!Ub(s))throw Fb("At least one callback required");return Ob(o,n,t)?(r=Ob(i,n,t),Ub(e)&&(r=e(r),Ob(a,n,t,r))):Ub(s)&&(r=s(),Ob(a,n,t,r)),r};br({target:"Map",proto:!0,real:!0,forced:!0},{upsert:jb}),br({target:"Map",proto:!0,real:!0,name:"upsert",forced:!0},{updateOrInsert:jb});var Bb=mg,Wb=br,Gb=Date,Zb=l(Gb.prototype.getTime);Wb({target:"Date",stat:!0},{now:function(){return Zb(new Gb)}});var Yb=X.Date.now,Hb=Tt,Jb=Vt,Xb=j,Kb=Qr,qb=TypeError,Qb=function(t){return function(e,r,n,i){Hb(r);var o=Jb(e),a=Xb(o),s=Kb(o),c=t?s-1:0,u=t?-1:1;if(n<2)for(;;){if(c in a){i=a[c],c+=u;break}if(c+=u,t?c<0:s<=c)throw qb("Reduce of empty array with no initial value")}for(;t?c>=0:s>c;c+=u)c in a&&(i=r(i,a[c],c,o));return i}},zb={left:Qb(!1),right:Qb(!0)},$b="process"==O(f.process),tC=zb.left,eC=st,rC=$b;br({target:"Array",proto:!0,forced:!Ms("reduce")||!rC&&eC>79&&eC<83},{reduce:function(t){var e=arguments.length;return tC(this,t,e,e>1?arguments[1]:void 0)}});var nC=Or("Array").reduce,iC=d,oC=nC,aC=Array.prototype,sC=function(t){var e=t.reduce;return t===aC||iC(aC,t)&&e===aC.reduce?oC:e},cC=Up.codeAt;br({target:"String",proto:!0},{codePointAt:function(t){return cC(this,t)}});var uC=Or("String").codePointAt,lC=d,dC=uC,pC=String.prototype,fC=function(t){var e=t.codePointAt;return"string"==typeof t||t===pC||lC(pC,t)&&e===pC.codePointAt?dC:e},hC=br,mC=l,vC=pi,yC=RangeError,gC=String.fromCharCode,bC=String.fromCodePoint,CC=mC([].join);hC({target:"String",stat:!0,arity:1,forced:!!bC&&1!=bC.length},{fromCodePoint:function(t){for(var e,r=[],n=arguments.length,i=0;n>i;){if(e=+arguments[i++],vC(e,1114111)!==e)throw yC(e+" is not a valid code point");r[i]=e<65536?gC(e):gC(55296+((e-=65536)>>10),e%1024+56320)}return CC(r,"")}});var SC=X.String.fromCodePoint,TC=X,_C=g;TC.JSON||(TC.JSON={stringify:JSON.stringify});var RC=function(t,e,r){return _C(TC.JSON.stringify,null,arguments)},EC=RC,wC=Bt,PC=Bf,kC=C,AC=We,IC=Error,xC=l("".replace),LC=String(IC("zxcasd").stack),DC=/\n\s*at [^:]*:[^\n]*/,NC=DC.test(LC),OC=J,MC=sr,UC=ki,VC=x,FC=!n((function(){var t=Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",VC(1,7)),7!==t.stack)})),jC=br,BC=d,WC=va,GC=Ka,ZC=function(t,e,r){for(var n=PC(e),i=AC.f,o=kC.f,a=0;a<n.length;a++){var s=n[a];wC(t,s)||r&&wC(r,s)||i(t,s,o(e,s))}},YC=sa,HC=sr,JC=x,XC=function(t,e){if(NC&&"string"==typeof t&&!IC.prepareStackTrace)for(;e--;)t=xC(t,DC,"");return t},KC=function(t,e){OC(e)&&"cause"in e&&MC(t,"cause",e.cause)},qC=Lm,QC=function(t,e){return void 0===t?arguments.length<2?"":e:UC(t)},zC=FC,$C=ne("toStringTag"),tS=Error,eS=[].push,rS=function(t,e){var r,n=arguments.length>2?arguments[2]:void 0,i=BC(nS,this);GC?r=GC(new tS,i?WC(this):nS):(r=i?this:YC(nS),HC(r,$C,"Error")),void 0!==e&&HC(r,"message",QC(e)),zC&&HC(r,"stack",XC(r.stack,1)),KC(r,n);var o=[];return qC(t,eS,{that:o}),HC(r,"errors",o),r};GC?GC(rS,tS):ZC(rS,tS,{name:!0});var nS=rS.prototype=YC(tS.prototype,{constructor:JC(1,rS),message:JC(1,""),name:JC(1,"AggregateError")});jC({global:!0,constructor:!0,arity:2},{AggregateError:rS});var iS,oS,aS,sS,cS=/(?:ipad|iphone|ipod).*applewebkit/i.test(tt),uS=f,lS=g,dS=Be,pS=b,fS=Bt,hS=n,mS=Ho,vS=Cr,yS=ye,gS=Xs,bS=cS,CS=$b,SS=uS.setImmediate,TS=uS.clearImmediate,_S=uS.process,RS=uS.Dispatch,ES=uS.Function,wS=uS.MessageChannel,PS=uS.String,kS=0,AS={},IS="onreadystatechange";try{iS=uS.location}catch(t){}var xS=function(t){if(fS(AS,t)){var e=AS[t];delete AS[t],e()}},LS=function(t){return function(){xS(t)}},DS=function(t){xS(t.data)},NS=function(t){uS.postMessage(PS(t),iS.protocol+"//"+iS.host)};SS&&TS||(SS=function(t){gS(arguments.length,1);var e=pS(t)?t:ES(t),r=vS(arguments,1);return AS[++kS]=function(){lS(e,void 0,r)},oS(kS),kS},TS=function(t){delete AS[t]},CS?oS=function(t){_S.nextTick(LS(t))}:RS&&RS.now?oS=function(t){RS.now(LS(t))}:wS&&!bS?(sS=(aS=new wS).port2,aS.port1.onmessage=DS,oS=dS(sS.postMessage,sS)):uS.addEventListener&&pS(uS.postMessage)&&!uS.importScripts&&iS&&"file:"!==iS.protocol&&!hS(NS)?(oS=NS,uS.addEventListener("message",DS,!1)):oS=IS in yS("script")?function(t){mS.appendChild(yS("script")).onreadystatechange=function(){mS.removeChild(this),xS(t)}}:function(t){setTimeout(LS(t),0)});var OS,MS,US,VS,FS,jS,BS,WS,GS={set:SS,clear:TS},ZS=f,YS=/ipad|iphone|ipod/i.test(tt)&&void 0!==ZS.Pebble,HS=/web0s(?!.*chrome)/i.test(tt),JS=f,XS=Be,KS=C.f,qS=GS.set,QS=cS,zS=YS,$S=HS,tT=$b,eT=JS.MutationObserver||JS.WebKitMutationObserver,rT=JS.document,nT=JS.process,iT=JS.Promise,oT=KS(JS,"queueMicrotask"),aT=oT&&oT.value;aT||(OS=function(){var t,e;for(tT&&(t=nT.domain)&&t.exit();MS;){e=MS.fn,MS=MS.next;try{e()}catch(t){throw MS?VS():US=void 0,t}}US=void 0,t&&t.enter()},QS||tT||$S||!eT||!rT?!zS&&iT&&iT.resolve?((BS=iT.resolve(void 0)).constructor=iT,WS=XS(BS.then,BS),VS=function(){WS(OS)}):tT?VS=function(){nT.nextTick(OS)}:(qS=XS(qS,JS),VS=function(){qS(OS)}):(FS=!0,jS=rT.createTextNode(""),new eT(OS).observe(jS,{characterData:!0}),VS=function(){jS.data=FS=!FS}));var sT=aT||function(t){var e={fn:t,next:void 0};US&&(US.next=e),MS||(MS=e,VS()),US=e},cT=f,uT=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}},lT=function(){this.head=null,this.tail=null};lT.prototype={add:function(t){var e={item:t,next:null};this.head?this.tail.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return this.head=t.next,this.tail===t&&(this.tail=null),t.item}};var dT=lT,pT=f.Promise,fT="object"==typeof window&&"object"!=typeof Deno,hT=f,mT=pT,vT=b,yT=Ue,gT=yn,bT=ne,CT=fT,ST=st,TT=mT&&mT.prototype,_T=bT("species"),RT=!1,ET=vT(hT.PromiseRejectionEvent),wT=yT("Promise",(function(){var t=gT(mT),e=t!==String(mT);if(!e&&66===ST)return!0;if(!TT.catch||!TT.finally)return!0;if(ST>=51&&/native code/.test(t))return!1;var r=new mT((function(t){t(1)})),n=function(t){t((function(){}),(function(){}))};return(r.constructor={})[_T]=n,!(RT=r.then((function(){}))instanceof n)||!e&&CT&&!ET})),PT={CONSTRUCTOR:wT,REJECTION_EVENT:ET,SUBCLASSING:RT},kT={},AT=Tt,IT=function(t){var e,r;this.promise=new t((function(t,n){if(void 0!==e||void 0!==r)throw TypeError("Bad Promise constructor");e=t,r=n})),this.resolve=AT(e),this.reject=AT(r)};kT.f=function(t){return new IT(t)};var xT,LT,DT=br,NT=$b,OT=f,MT=R,UT=ga,VT=Ma,FT=nv,jT=Tt,BT=b,WT=J,GT=Om,ZT=Jv,YT=GS.set,HT=sT,JT=function(t,e){var r=cT.console;r&&r.error&&(1==arguments.length?r.error(t):r.error(t,e))},XT=uT,KT=dT,qT=So,QT=pT,zT=kT,$T="Promise",t_=PT.CONSTRUCTOR,e_=PT.REJECTION_EVENT,r_=qT.getterFor($T),n_=qT.set,i_=QT&&QT.prototype,o_=QT,a_=i_,s_=OT.TypeError,c_=OT.document,u_=OT.process,l_=zT.f,d_=l_,p_=!!(c_&&c_.createEvent&&OT.dispatchEvent),f_="unhandledrejection",h_=function(t){var e;return!(!WT(t)||!BT(e=t.then))&&e},m_=function(t,e){var r,n,i,o=e.value,a=1==e.state,s=a?t.ok:t.fail,c=t.resolve,u=t.reject,l=t.domain;try{s?(a||(2===e.rejection&&C_(e),e.rejection=1),!0===s?r=o:(l&&l.enter(),r=s(o),l&&(l.exit(),i=!0)),r===t.promise?u(s_("Promise-chain cycle")):(n=h_(r))?MT(n,r,c,u):c(r)):u(o)}catch(t){l&&!i&&l.exit(),u(t)}},v_=function(t,e){t.notified||(t.notified=!0,HT((function(){for(var r,n=t.reactions;r=n.get();)m_(r,t);t.notified=!1,e&&!t.rejection&&g_(t)})))},y_=function(t,e,r){var n,i;p_?((n=c_.createEvent("Event")).promise=e,n.reason=r,n.initEvent(t,!1,!0),OT.dispatchEvent(n)):n={promise:e,reason:r},!e_&&(i=OT["on"+t])?i(n):t===f_&&JT("Unhandled promise rejection",r)},g_=function(t){MT(YT,OT,(function(){var e,r=t.facade,n=t.value;if(b_(t)&&(e=XT((function(){NT?u_.emit("unhandledRejection",n,r):y_(f_,r,n)})),t.rejection=NT||b_(t)?2:1,e.error))throw e.value}))},b_=function(t){return 1!==t.rejection&&!t.parent},C_=function(t){MT(YT,OT,(function(){var e=t.facade;NT?u_.emit("rejectionHandled",e):y_("rejectionhandled",e,t.value)}))},S_=function(t,e,r){return function(n){t(e,n,r)}},T_=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,v_(t,!0))},__=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw s_("Promise can't be resolved itself");var n=h_(e);n?HT((function(){var r={done:!1};try{MT(n,e,S_(__,r,t),S_(T_,r,t))}catch(e){T_(r,e,t)}})):(t.value=e,t.state=1,v_(t,!1))}catch(e){T_({done:!1},e,t)}}};t_&&(a_=(o_=function(t){GT(this,a_),jT(t),MT(xT,this);var e=r_(this);try{t(S_(__,e),S_(T_,e))}catch(t){T_(e,t)}}).prototype,(xT=function(t){n_(this,{type:$T,done:!1,notified:!1,parent:!1,reactions:new KT,rejection:!1,state:0,value:void 0})}).prototype=UT(a_,"then",(function(t,e){var r=r_(this),n=l_(ZT(this,o_));return r.parent=!0,n.ok=!BT(t)||t,n.fail=BT(e)&&e,n.domain=NT?u_.domain:void 0,0==r.state?r.reactions.add(n):HT((function(){m_(n,r)})),n.promise})),LT=function(){var t=new xT,e=r_(t);this.promise=t,this.resolve=S_(__,e),this.reject=S_(T_,e)},zT.f=l_=function(t){return t===o_||undefined===t?new LT(t):d_(t)}),DT({global:!0,constructor:!0,wrap:!0,forced:t_},{Promise:o_}),VT(o_,$T,!1,!0),FT($T);var R_=ne("iterator"),E_=!1;try{var w_=0,P_={next:function(){return{done:!!w_++}},return:function(){E_=!0}};P_[R_]=function(){return this},Array.from(P_,(function(){throw 2}))}catch(t){}var k_=pT,A_=function(t,e){if(!e&&!E_)return!1;var r=!1;try{var n={};n[R_]=function(){return{next:function(){return{done:r=!0}}}},t(n)}catch(t){}return r},I_=PT.CONSTRUCTOR||!A_((function(t){k_.all(t).then(void 0,(function(){}))})),x_=R,L_=Tt,D_=kT,N_=uT,O_=Lm;br({target:"Promise",stat:!0,forced:I_},{all:function(t){var e=this,r=D_.f(e),n=r.resolve,i=r.reject,o=N_((function(){var r=L_(e.resolve),o=[],a=0,s=1;O_(t,(function(t){var c=a++,u=!1;s++,x_(r,e,t).then((function(t){u||(u=!0,o[c]=t,--s||n(o))}),i)})),--s||n(o)}));return o.error&&i(o.value),r.promise}});var M_=br,U_=PT.CONSTRUCTOR;pT&&pT.prototype,M_({target:"Promise",proto:!0,forced:U_,real:!0},{catch:function(t){return this.then(void 0,t)}});var V_=R,F_=Tt,j_=kT,B_=uT,W_=Lm;br({target:"Promise",stat:!0,forced:I_},{race:function(t){var e=this,r=j_.f(e),n=r.reject,i=B_((function(){var i=F_(e.resolve);W_(t,(function(t){V_(i,e,t).then(r.resolve,n)}))}));return i.error&&n(i.value),r.promise}});var G_=R,Z_=kT;br({target:"Promise",stat:!0,forced:PT.CONSTRUCTOR},{reject:function(t){var e=Z_.f(this);return G_(e.reject,void 0,t),e.promise}});var Y_=Je,H_=J,J_=kT,X_=function(t,e){if(Y_(t),H_(e)&&e.constructor===t)return e;var r=J_.f(t);return(0,r.resolve)(e),r.promise},K_=br,q_=pT,Q_=PT.CONSTRUCTOR,z_=X_,$_=$("Promise"),tR=!Q_;K_({target:"Promise",stat:!0,forced:true},{resolve:function(t){return z_(tR&&this===$_?q_:this,t)}});var eR=R,rR=Tt,nR=kT,iR=uT,oR=Lm;br({target:"Promise",stat:!0},{allSettled:function(t){var e=this,r=nR.f(e),n=r.resolve,i=r.reject,o=iR((function(){var r=rR(e.resolve),i=[],o=0,a=1;oR(t,(function(t){var s=o++,c=!1;a++,eR(r,e,t).then((function(t){c||(c=!0,i[s]={status:"fulfilled",value:t},--a||n(i))}),(function(t){c||(c=!0,i[s]={status:"rejected",reason:t},--a||n(i))}))})),--a||n(i)}));return o.error&&i(o.value),r.promise}});var aR=R,sR=Tt,cR=$,uR=kT,lR=uT,dR=Lm,pR="No one promise resolved";br({target:"Promise",stat:!0},{any:function(t){var e=this,r=cR("AggregateError"),n=uR.f(e),i=n.resolve,o=n.reject,a=lR((function(){var n=sR(e.resolve),a=[],s=0,c=1,u=!1;dR(t,(function(t){var l=s++,d=!1;c++,aR(n,e,t).then((function(t){d||u||(u=!0,i(t))}),(function(t){d||u||(d=!0,a[l]=t,--c||o(new r(a,pR)))}))})),--c||o(new r(a,pR))}));return a.error&&o(a.value),n.promise}});var fR=br,hR=pT,mR=n,vR=$,yR=b,gR=Jv,bR=X_,CR=hR&&hR.prototype;fR({target:"Promise",proto:!0,real:!0,forced:!!hR&&mR((function(){CR.finally.call({then:function(){}},(function(){}))}))},{finally:function(t){var e=gR(this,vR("Promise")),r=yR(t);return this.then(r?function(r){return bR(e,t()).then((function(){return r}))}:t,r?function(r){return bR(e,t()).then((function(){throw r}))}:t)}});var SR=X.Promise,TR=kT,_R=uT;br({target:"Promise",stat:!0,forced:!0},{try:function(t){var e=TR.f(this),r=_R(t);return(r.error?e.reject:e.resolve)(r.value),e.promise}});var RR=SR,ER="\t\n\v\f\r                　\u2028\u2029\ufeff",wR=W,PR=ki,kR=l("".replace),AR="[\t\n\v\f\r                　\u2028\u2029\ufeff]",IR=RegExp("^"+AR+AR+"*"),xR=RegExp(AR+AR+"*$"),LR=function(t){return function(e){var r=PR(wR(e));return 1&t&&(r=kR(r,IR,"")),2&t&&(r=kR(r,xR,"")),r}},DR={start:LR(1),end:LR(2),trim:LR(3)},NR=f,OR=n,MR=l,UR=ki,VR=DR.trim,FR=ER,jR=NR.parseInt,BR=NR.Symbol,WR=BR&&BR.iterator,GR=/^[+-]?0x/i,ZR=MR(GR.exec),YR=8!==jR(FR+"08")||22!==jR(FR+"0x16")||WR&&!OR((function(){jR(Object(WR))}))?function(t,e){var r=VR(UR(t));return jR(r,e>>>0||(ZR(GR,r)?16:10))}:jR;br({global:!0,forced:parseInt!=YR},{parseInt:YR});var HR=X.parseInt,JR=Or("Array").entries,XR=fn,KR=Bt,qR=d,QR=JR,zR=Array.prototype,$R={DOMTokenList:!0,NodeList:!0},tE=function(t){var e=t.entries;return t===zR||qR(zR,t)&&e===zR.entries||KR($R,XR(t))?QR:e},eE=gt,rE=TypeError,nE=br,iE=Vt,oE=pi,aE=Hr,sE=Qr,cE=$r,uE=Vn,lE=nn,dE=function(t,e){if(!delete t[e])throw rE("Cannot delete property "+eE(e)+" of "+eE(t))},pE=Wn("splice"),fE=Math.max,hE=Math.min;nE({target:"Array",proto:!0,forced:!pE},{splice:function(t,e){var r,n,i,o,a,s,c=iE(this),u=sE(c),l=oE(t,u),d=arguments.length;for(0===d?r=n=0:1===d?(r=0,n=u-l):(r=d-2,n=hE(fE(aE(e),0),u-l)),cE(u+r-n),i=uE(c,n),o=0;o<n;o++)(a=l+o)in c&&lE(i,o,c[a]);if(i.length=n,r<n){for(o=l;o<u-n;o++)s=o+r,(a=o+n)in c?c[s]=c[a]:dE(c,s);for(o=u;o>u-n+r;o--)dE(c,o-1)}else if(r>n)for(o=u-n;o>l;o--)s=o+r-1,(a=o+n-1)in c?c[s]=c[a]:dE(c,s);for(o=0;o<r;o++)c[o+l]=arguments[o+2];return c.length=u-n+r,i}});var mE=Or("Array").splice,vE=d,yE=mE,gE=Array.prototype,bE=function(t){var e=t.splice;return t===gE||vE(gE,t)&&e===gE.splice?yE:e},CE=br,SE=Ns.find,TE="find",_E=!0;TE in[]&&Array(1).find((function(){_E=!1})),CE({target:"Array",proto:!0,forced:_E},{find:function(t){return SE(this,t,arguments.length>1?arguments[1]:void 0)}});var RE=Or("Array").find,EE=d,wE=RE,PE=Array.prototype,kE=function(t){var e=t.find;return t===PE||EE(PE,t)&&e===PE.find?wE:e},AE={exports:{}};!function(t,e){self,t.exports=function(){var t={729:function(t){var e=Object.prototype.hasOwnProperty,r="~";function n(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function o(t,e,n,o,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var s=new i(n,o||t,a),c=r?r+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],s]:t._events[c].push(s):(t._events[c]=s,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function s(){this._events=new n,this._eventsCount=0}Xp&&(n.prototype=Xp(null),(new n).__proto__||(r=!1)),s.prototype.eventNames=function(){var t,n,i=[];if(0===this._eventsCount)return i;for(n in t=this._events)e.call(t,n)&&i.push(r?hf(n).call(n,1):n);return mf?ci(i).call(i,mf(t)):i},s.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=new Array(o);i<o;i++)a[i]=n[i].fn;return a},s.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},s.prototype.emit=function(t,e,n,i,o,a){var s=r?r+t:t;if(!this._events[s])return!1;var c,u,l=this._events[s],d=arguments.length;if(l.fn){switch(l.once&&this.removeListener(t,l.fn,void 0,!0),d){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,e),!0;case 3:return l.fn.call(l.context,e,n),!0;case 4:return l.fn.call(l.context,e,n,i),!0;case 5:return l.fn.call(l.context,e,n,i,o),!0;case 6:return l.fn.call(l.context,e,n,i,o,a),!0}for(u=1,c=new Array(d-1);u<d;u++)c[u-1]=arguments[u];l.fn.apply(l.context,c)}else{var p,f=l.length;for(u=0;u<f;u++)switch(l[u].once&&this.removeListener(t,l[u].fn,void 0,!0),d){case 1:l[u].fn.call(l[u].context);break;case 2:l[u].fn.call(l[u].context,e);break;case 3:l[u].fn.call(l[u].context,e,n);break;case 4:l[u].fn.call(l[u].context,e,n,i);break;default:if(!c)for(p=1,c=new Array(d-1);p<d;p++)c[p-1]=arguments[p];l[u].fn.apply(l[u].context,c)}}return!0},s.prototype.on=function(t,e,r){return o(this,t,e,r,!1)},s.prototype.once=function(t,e,r){return o(this,t,e,r,!0)},s.prototype.removeListener=function(t,e,n,i){var o=r?r+t:t;if(!this._events[o])return this;if(!e)return a(this,o),this;var s=this._events[o];if(s.fn)s.fn!==e||i&&!s.once||n&&s.context!==n||a(this,o);else{for(var c=0,u=[],l=s.length;c<l;c++)(s[c].fn!==e||i&&!s[c].once||n&&s[c].context!==n)&&u.push(s[c]);u.length?this._events[o]=1===u.length?u[0]:u:a(this,o)}return this},s.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,t.exports=s},961:function(t,e,r){var n,i=function(){var t=String.fromCharCode,e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",n={};function i(t,e){if(!n[t]){n[t]={};for(var r=0;r<t.length;r++)n[t][t.charAt(r)]=r}return n[t][e]}var o={compressToBase64:function(t){if(null==t)return"";var r=o._compress(t,6,(function(t){return e.charAt(t)}));switch(r.length%4){default:case 0:return r;case 1:return r+"===";case 2:return r+"==";case 3:return r+"="}},decompressFromBase64:function(t){return null==t?"":""==t?null:o._decompress(t.length,32,(function(r){return i(e,t.charAt(r))}))},compressToUTF16:function(e){return null==e?"":o._compress(e,15,(function(e){return t(e+32)}))+" "},decompressFromUTF16:function(t){return null==t?"":""==t?null:o._decompress(t.length,16384,(function(e){return t.charCodeAt(e)-32}))},compressToUint8Array:function(t){for(var e=o.compress(t),r=new Uint8Array(2*e.length),n=0,i=e.length;n<i;n++){var a=e.charCodeAt(n);r[2*n]=a>>>8,r[2*n+1]=a%256}return r},decompressFromUint8Array:function(e){if(null==e)return o.decompress(e);for(var r=new Array(e.length/2),n=0,i=r.length;n<i;n++)r[n]=256*e[2*n]+e[2*n+1];var a=[];return Hs(r).call(r,(function(e){a.push(t(e))})),o.decompress(a.join(""))},compressToEncodedURIComponent:function(t){return null==t?"":o._compress(t,6,(function(t){return r.charAt(t)}))},decompressFromEncodedURIComponent:function(t){return null==t?"":""==t?null:(t=t.replace(/ /g,"+"),o._decompress(t.length,32,(function(e){return i(r,t.charAt(e))})))},compress:function(e){return o._compress(e,16,(function(e){return t(e)}))},_compress:function(t,e,r){if(null==t)return"";var n,i,o,a={},s={},c="",u="",l="",d=2,p=3,f=2,h=[],m=0,v=0;for(o=0;o<t.length;o+=1)if(c=t.charAt(o),Object.prototype.hasOwnProperty.call(a,c)||(a[c]=p++,s[c]=!0),u=l+c,Object.prototype.hasOwnProperty.call(a,u))l=u;else{if(Object.prototype.hasOwnProperty.call(s,l)){if(l.charCodeAt(0)<256){for(n=0;n<f;n++)m<<=1,v==e-1?(v=0,h.push(r(m)),m=0):v++;for(i=l.charCodeAt(0),n=0;n<8;n++)m=m<<1|1&i,v==e-1?(v=0,h.push(r(m)),m=0):v++,i>>=1}else{for(i=1,n=0;n<f;n++)m=m<<1|i,v==e-1?(v=0,h.push(r(m)),m=0):v++,i=0;for(i=l.charCodeAt(0),n=0;n<16;n++)m=m<<1|1&i,v==e-1?(v=0,h.push(r(m)),m=0):v++,i>>=1}0==--d&&(d=Math.pow(2,f),f++),delete s[l]}else for(i=a[l],n=0;n<f;n++)m=m<<1|1&i,v==e-1?(v=0,h.push(r(m)),m=0):v++,i>>=1;0==--d&&(d=Math.pow(2,f),f++),a[u]=p++,l=String(c)}if(""!==l){if(Object.prototype.hasOwnProperty.call(s,l)){if(l.charCodeAt(0)<256){for(n=0;n<f;n++)m<<=1,v==e-1?(v=0,h.push(r(m)),m=0):v++;for(i=l.charCodeAt(0),n=0;n<8;n++)m=m<<1|1&i,v==e-1?(v=0,h.push(r(m)),m=0):v++,i>>=1}else{for(i=1,n=0;n<f;n++)m=m<<1|i,v==e-1?(v=0,h.push(r(m)),m=0):v++,i=0;for(i=l.charCodeAt(0),n=0;n<16;n++)m=m<<1|1&i,v==e-1?(v=0,h.push(r(m)),m=0):v++,i>>=1}0==--d&&(d=Math.pow(2,f),f++),delete s[l]}else for(i=a[l],n=0;n<f;n++)m=m<<1|1&i,v==e-1?(v=0,h.push(r(m)),m=0):v++,i>>=1;0==--d&&(d=Math.pow(2,f),f++)}for(i=2,n=0;n<f;n++)m=m<<1|1&i,v==e-1?(v=0,h.push(r(m)),m=0):v++,i>>=1;for(;;){if(m<<=1,v==e-1){h.push(r(m));break}v++}return h.join("")},decompress:function(t){return null==t?"":""==t?null:o._decompress(t.length,32768,(function(e){return t.charCodeAt(e)}))},_decompress:function(e,r,n){var i,o,a,s,c,u,l,d=[],p=4,f=4,h=3,m="",v=[],y={val:n(0),position:r,index:1};for(i=0;i<3;i+=1)d[i]=i;for(a=0,c=Math.pow(2,2),u=1;u!=c;)s=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),a|=(s>0?1:0)*u,u<<=1;switch(a){case 0:for(a=0,c=Math.pow(2,8),u=1;u!=c;)s=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),a|=(s>0?1:0)*u,u<<=1;l=t(a);break;case 1:for(a=0,c=Math.pow(2,16),u=1;u!=c;)s=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),a|=(s>0?1:0)*u,u<<=1;l=t(a);break;case 2:return""}for(d[3]=l,o=l,v.push(l);;){if(y.index>e)return"";for(a=0,c=Math.pow(2,h),u=1;u!=c;)s=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),a|=(s>0?1:0)*u,u<<=1;switch(l=a){case 0:for(a=0,c=Math.pow(2,8),u=1;u!=c;)s=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),a|=(s>0?1:0)*u,u<<=1;d[f++]=t(a),l=f-1,p--;break;case 1:for(a=0,c=Math.pow(2,16),u=1;u!=c;)s=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),a|=(s>0?1:0)*u,u<<=1;d[f++]=t(a),l=f-1,p--;break;case 2:return v.join("")}if(0==p&&(p=Math.pow(2,h),h++),d[l])m=d[l];else{if(l!==f)return null;m=o+o.charAt(0)}v.push(m),d[f++]=o+m.charAt(0),o=m,0==--p&&(p=Math.pow(2,h),h++)}}};return o}();void 0===(n=function(){return i}.call(e,r,e,t))||(t.exports=n)}},e={};function r(n){var i=e[n];if(void 0!==i)return i.exports;var o=e[n]={exports:{}};return t[n](o,o.exports,r),o.exports}r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,{a:e}),e},r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Tf(t,n,{enumerable:!0,get:e[n]})},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var n={};return function(){r.d(n,{default:function(){return or}});var t=r(729),e=r.n(t),i="LEBP2PInnerEvent",o="LEBP2PEvent",a={ERR_LOAD_TIMEOUT:1003,ERR_LOAD_REACH_MAX_RETRY:1004,ERR_PLAY_WEBRTC_FAIL:1005,ERR_FETCH_REQUEST_FAIL:1006,ERR_PLAY_REQUEST_PULL_FAIL:1007,ERR_PLAY_REQUEST_STOP_FAIL:1008,INF_PLAY_EVT_SERVER_CONNECTED:3001,INF_PLAY_EVT_SERVER_RECONNECT:3002,INF_PLAY_EVT_REQUEST_PULL_BEGIN:3003,INF_PLAY_EVT_REQUEST_PULL_SUCCESS:3004,INF_PLAY_EVT_STREAM_FIRST_AUDIO_FRAME:3005,INF_PLAY_EVT_STREAM_FIRST_VIDEO_FRAME:3006,INF_PLAY_EVT_STREAM_CLOSED:3007,INF_PLAY_EVT_STREAM_SWITCH:3008};function s(t,e){var r=Ef(t);if(mf){var n=mf(t);e&&(n=Fc(n).call(n,(function(e){return Of(t,e).enumerable}))),r.push.apply(r,n)}return r}function c(t){for(var e=1;e<arguments.length;e++){var r,n,i=null!=arguments[e]?arguments[e]:{};e%2?Hs(r=s(Object(i),!0)).call(r,(function(e){u(t,e,i[e])})):Hf?$f(t,Hf(i)):Hs(n=s(Object(i))).call(n,(function(e){Tf(t,e,Of(i,e))}))}return t}function u(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var l={streamUrl:"",streamId:function(){return function(t){var e=t.split("?")[0];return hf(e).call(e,mh(e).call(e,"/")+1,gc(e).call(e,"?"))}(this.streamUrl)},pullStreamDomain:"https://overseas-webrtc.liveplay.myqcloud.com",stopStreamDomain:"https://overseas-webrtc.liveplay.myqcloud.com",get pullStreamUrl(){return"".concat(this.pullStreamDomain,"/webrtc/v1/pullstream")},get stopStreamUrl(){return"".concat(this.stopStreamDomain,"/webrtc/v1/stopstream")}},d=c(c(c(c(c(c(c(c({},{cloudAppid:0,xp2pAppid:0,token:"",xp2pDomain:""}),l),{confBaseUrl:"https://conf.qvb.qcloud.com/api/v3/live/h5/",reportUrl:"https://log.qvb.qcloud.com/reporter/vlive",signal:"wss://signal.qvb.qcloud.com/",stun:"127.0.0.1"}),{trackerUrl:"https://tracker-00.qvb.qcloud.com/api/tracker/v2/htbt",trackerInterval:5e3,trackerVersion:"v1"}),{maxCandidate:5,maxConnected:5,maxConnecting:5,tickInterval:1e4}),{stuckFrameRateRatio:.6}),{subscribeCoolingTime:2e4}),{connectRetryCount:3,connectRetryDelay:1e3});function p(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var f,h=function(){function t(){p(this,"config",Ih({},d))}return t.prototype.update=function(t){Ih(this.config,t)},t.prototype.get=function(t){return this.config[t]},t.prototype.set=function(t,e){this.config[t]=e},t}();function m(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}!function(t){t[t.base=0]="base",t[t.number=1]="number",t[t.boolean=2]="boolean",t[t.string=3]="string",t[t.array=4]="array",t[t.object=5]="object"}(f||(f={}));var v=function(){function t(t){var e=t.initVal,r=t.defaultVal;m(this,"type",f.base),m(this,"val",void 0),this.val=void 0===e?r:e}return t.prototype.set=function(t){return this.val=t,this},Tf(t.prototype,"value",{get:function(){return this.val},enumerable:!1,configurable:!0}),t.prototype.isString=function(){return this.type===f.string},t.prototype.isBoolean=function(){return this.type===f.boolean},t.prototype.isNumber=function(){return this.type===f.number},t}(),y=function(t){function e(e){var r=this;return m(r=t.call(this,{initVal:e,defaultVal:0})||this,"type",f.number),r}return Bc(e,t),e.prototype.plus=function(t){this.val+=t},e.prototype.minus=function(t){this.val-=t},e}(v),g=function(t){function e(e){var r=this;return m(r=t.call(this,{initVal:e,defaultVal:!1})||this,"type",f.boolean),r}return Bc(e,t),e}(v),b=function(t){function e(e){var r=this;return m(r=t.call(this,{initVal:e,defaultVal:""})||this,"type",f.string),r}return Bc(e,t),e.prototype.append=function(t){this.val+=t},e}(v),C=function(t){function e(e){var r=this;return m(r=t.call(this,{initVal:e,defaultVal:[]})||this,"type",f.array),r}return Bc(e,t),e}(v),S=function(t){function e(e){var r=this;return m(r=t.call(this,{initVal:e,defaultVal:{}})||this,"type",f.object),r}return Bc(e,t),e}(v),T=function(){function t(){}return t.create=function(t,e){switch(t){case f.number:return new y(e);case f.boolean:return new g(e);case f.string:return new b(e);case f.array:return new C(e);case f.object:return new S(e)}},t}();function _(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var R=function(){function t(){_(this,"config",{deleteZeroNumber:!0,fieldsReportedWithZeroNumber:[],fieldsReservedValueAfterReport:[]}),_(this,"registeredReportCallback",new hg),_(this,"storedFields",new Bb),_(this,"preHooks",new Bb)}return t.prototype.destroy=function(){this.config=null,this.reset()},t.prototype.configure=function(t){this.config=t},t.prototype.reset=function(){this.registeredReportCallback.clear(),this.preHooks.clear()},t.prototype.string=function(t){return this.findOrCreate(f.string,t)},t.prototype.number=function(t){return this.findOrCreate(f.number,t)},t.prototype.bool=function(t){return this.findOrCreate(f.boolean,t)},t.prototype.array=function(t){return this.findOrCreate(f.array,t)},t.prototype.object=function(t){return this.findOrCreate(f.object,t)},t.prototype.deleteField=function(t){this.storedFields.delete(t)},t.prototype.generate=function(){var t,e,r=this;Hs(t=this.preHooks).call(t,(function(t){t()}));var n={};return Hs(e=this.storedFields).call(e,(function(t,e){var i,o;r.config&&(!r.config.deleteZeroNumber||Hi(i=r.config.fieldsReportedWithZeroNumber).call(i,e)||t.isBoolean()||t.isString()||t.value)&&(n[e]=t.value,Hi(o=r.config.fieldsReservedValueAfterReport).call(o,e)||r.storedFields.delete(e))})),n},t.prototype.registerPreHook=function(t,e){this.preHooks.has(t)||this.preHooks.set(t,e)},t.prototype.deletePreHook=function(t){this.preHooks.delete(t)},t.prototype.findOrCreate=function(t,e,r){var n=this.storedFields.get(e);return n?(n.type!==t&&(n=T.create(t,r),this.storedFields.set(e,n)),n):(n=T.create(t,r),this.storedFields.set(e,n),n)},t}(),E=function(){function t(){_(this,"reportUnits",new Bb)}return t.prototype.registerReportUint=function(t,e){this.reportUnits.has(t)||this.reportUnits.set(t,e)},t.prototype.deleteReportUint=function(t){this.reportUnits.delete(t)},t.prototype.generate=function(){var t,e={};return Hs(t=this.reportUnits).call(t,(function(t){Ih(e,t.generate())})),e},t}();function w(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var P,k=0,A=function(t){function e(){var e=this;return w(e=t.call(this)||this,"tagName","Base"),w(e,"objId",0),w(e,"reporter",null),w(e,"bus",null),e.objId=k,k+=1,e}return Bc(e,t),Tf(e.prototype,"objName",{get:function(){var t;return ci(t="".concat(this.tagName,"_")).call(t,this.objId)},enumerable:!1,configurable:!0}),e.prototype.init=function(){},e.prototype.destroy=function(){this.reporter=null,this.removeAllListeners()},e.prototype.setReporter=function(t){return t&&(this.reporter=t),this},e.prototype.setBus=function(t){return this.bus=t,this},e}(e());function I(t,e){var r=Ef(t);if(mf){var n=mf(t);e&&(n=Fc(n).call(n,(function(e){return Of(t,e).enumerable}))),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r,n,i=null!=arguments[e]?arguments[e]:{};e%2?Hs(r=I(Object(i),!0)).call(r,(function(e){L(t,e,i[e])})):Hf?$f(t,Hf(i)):Hs(n=I(Object(i))).call(n,(function(e){Tf(t,e,Of(i,e))}))}return t}function L(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}!function(t){t[t.STALL=0]="STALL",t[t.FINE=1]="FINE"}(P||(P={}));var D=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return L(n=t.apply(this,Xc([],Jc(e),!1))||this,"stallState",P.FINE),L(n,"config",{detectSensitivity:1e3}),L(n,"timer",null),L(n,"lastFreshTime",0),n}return Bc(e,t),e.prototype.configure=function(t){return this.config=x(x({},this.config),t),this},e.prototype.init=function(){return this.clearTimer(),this.startTimer(),this},e.prototype.destroy=function(){this.clearTimer()},e.prototype.fresh=function(){this.stallState===P.STALL&&this.emit("recover",{stallDuration:Yb()-this.lastFreshTime}),this.lastFreshTime=Yb(),this.stallState=P.FINE},e.prototype.check=function(){this.stallState!==P.STALL&&this.lastFreshTime&&Yb()-this.lastFreshTime>this.config.detectSensitivity&&(this.stallState=P.STALL,this.emit("stall"))},e.prototype.startTimer=function(){var t=this;this.timer=ac((function(){t.check()}),this.config.detectSensitivity)},e.prototype.clearTimer=function(){this.timer&&(clearInterval(this.timer),this.timer=null)},e}(t.EventEmitter);function N(t,e){var r=Ef(t);if(mf){var n=mf(t);e&&(n=Fc(n).call(n,(function(e){return Of(t,e).enumerable}))),r.push.apply(r,n)}return r}function O(t){for(var e=1;e<arguments.length;e++){var r,n,i=null!=arguments[e]?arguments[e]:{};e%2?Hs(r=N(Object(i),!0)).call(r,(function(e){M(t,e,i[e])})):Hf?$f(t,Hf(i)):Hs(n=N(Object(i))).call(n,(function(e){Tf(t,e,Of(i,e))}))}return t}function M(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var U=O(O(O(O(O(O(O(O({},{APPID:"appid",PLATFORM:"platform",TYPE:"type",PARTNER:"partner",UUID:"str_user_id",PLAY_ID:"str_play_id",REFERER:"referer",USER_AGENT:"user_agent",VERSION:"version",CURRENT_TIME:"cur_time",DATA_TIME:"data_time",DATA_TYPE:"data_type",VIDEO_TYPE:"str_video_type",URL:"url",BROWSER_NAME:"browser_name",BROWSER_MAJOR:"browser_major",BROWSER_VERSION:"browser_version"}),{CDN_BYTES:"cdn_bytes",P2P_BYTES:"p2p_bytes",UPLOAD_BYTES:"upload_bytes"}),{PLAY_STARTED:"play_started",PLAY_TIME:"play_time",REQUEST:"request",LOAD_OK_TIME:"loadok_t",EXIT_REASON:"exit_reason",ROLLBACK_REASON:"rollback_reason"}),{CONN_TRY:"conn_try",CONN_TRY_TOTAL:"conn_try_total",CONN_SUCC:"conn_succ",CONN_SUCC_TOTAL:"conn_succ_total",CONNECTED:"connected",INIT_PC_TIMEOUT:"init_pc_timeout"}),{PULL_CDN_STREAM:"pull_cdn_stream",PULL_CDN_SUCC:"pull_cdn_succ",PULL_CDN_TIMEOUT:"pull_cdn_timeout",PULL_CDN_FAILED:"pull_cdn_failed",PULL_CDN_ERROR:"pull_cdn_error",BACK_CDN_STREAM:"back_cdn_stream",BACK_CDN_STREAM_REASON:"back_cdn_stream_reason"}),{SUBSCRIBE_CNT:"subscribe_request_c",SUBSCRIBE_TIMEOUT_CNT:"subscribe_timeout_c",SUBSCRIBE_SUCCESS_CNT:"subscribe_success_c",SUBSCRIBE_FAIL_CNT:"subscribe_failure_c",RECV_SUBSCRIBE:"recv_subscribe",RECV_SUBSCRIBE_SUCC:"recv_subscribe_succ",RECV_SUBSCRIBE_CANCEL:"recv_subscribe_cancel"}),{STUCK_COUNT:"stuck_count",SUBSCRIBE_P2P_STUCK:"p2p_stuck_c",STUCK_AVG_DURATION:"stuck_avg_duration",CDN_AV_DIFF:"cdn_av_diff",P2P_AV_DIFF:"p2p_av_diff"}),{FRAME_RATE:"frame_rate",VIDEO_CODEC:"vcodec",AUDIO_CODEC:"acodec",VIDEO_BITRATE:"v_bitrate",AUDIO_BITRATE:"a_bitrate",VIDEO_CLOCK_RATE:"v_clock_rate",AUDIO_CLOCK_RATE:"a_clock_rate"});function V(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var F=function(t){function e(){var e=this;return V(e=t.call(this)||this,"tagName","MediaStreamProcessor"),V(e,"mediaStream",void 0),V(e,"trackGenerator",void 0),V(e,"vwriter",void 0),V(e,"destroyed",!1),V(e,"stallDetector",void 0),V(e,"reportUnit",void 0),e.stallDetector=new D,e.reportUnit=new R,e.mediaStream=new MediaStream,e.trackGenerator=new MediaStreamTrackGenerator({kind:"video"}),e.vwriter=e.trackGenerator.writable.getWriter(),e.mediaStream.addTrack(e.trackGenerator),e}return Bc(e,t),e.prototype.setReporter=function(e){var r=this;return this.reportUnit.configure({deleteZeroNumber:!0,fieldsReservedValueAfterReport:[],fieldsReportedWithZeroNumber:[]}),null==e||e.registerReportUint(this.objName,this.reportUnit),this.reportUnit.registerPreHook(this.objName,(function(){var t;r.reportUnit.number(U.STUCK_AVG_DURATION).set(0===(t=r.reportUnit.array("stallDuration").value).length?0:sC(t).call(t,(function(t,e){return t+e}))/t.length),r.reportUnit.deleteField("stallDuration")})),t.prototype.setReporter.call(this,e)},e.prototype.init=function(){var e=this;return this.stallDetector.configure({detectSensitivity:500}).init(),this.stallDetector.on("stall",(function(){e.reportUnit.number(U.STUCK_COUNT).plus(1)})),this.stallDetector.on("recover",(function(t){var r=t.stallDuration;e.reportUnit.array("stallDuration").value.push(r)})),t.prototype.init.call(this)},e.prototype.destroy=function(){this.stallDetector.destroy(),this.destroyed=!0,this.vwriter.abort(),this.stopTrack()},e.prototype.getMediaObj=function(){return this.mediaStream},e.prototype.addMediaStreamTrack=function(t){"video"===t.kind?this.onNewVideoTrack(t):"audio"===t.kind&&this.onNewAudioTrack(t)},e.prototype.onNewVideoTrack=function(t){var e=this,r=new MediaStreamTrackProcessor({track:t}),n=new MediaStreamTrackGenerator({kind:"video"}),i=new TransformStream({transform:function(t,r){e.destroyed||(e.stallDetector.fresh(),e.vwriter.write(t))}});r.readable.pipeThrough(i).pipeTo(n.writable)},e.prototype.onNewAudioTrack=function(t){this.replaceTrack(this.mediaStream,t)},e.prototype.replaceTrack=function(t,e){var r,n,i=t.getTracks();try{for(var o=Hc(i),a=o.next();!a.done;a=o.next()){var s=a.value;e.kind===s.kind&&t.removeTrack(s)}}catch(t){r={error:t}}finally{try{a&&!a.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}t.addTrack(e)},e.prototype.stopTrack=function(){var t;Hs(t=this.mediaStream.getTracks()).call(t,(function(t){t.stop()}))},e}(A);function j(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var B=function(){function t(t,e,r,n){j(this,"config",void 0),j(this,"requestInfo",void 0),j(this,"init",void 0),j(this,"callbacks",void 0),j(this,"retryTimes",0),j(this,"timeoutTimer",null),j(this,"abortController",null),j(this,"stats",{}),j(this,"res",null),this.config=t,this.requestInfo=e,this.init=r,this.callbacks=n,this.load()}return t.prototype.destroy=function(){this.clearCallbacks(),this.clearTimer(),this.abort()},t.prototype.stat=function(){},t.prototype.abort=function(){var t;this.clearTimer(),null===(t=this.abortController)||void 0===t||t.abort()},t.prototype.load=function(){this.abort(),this.setupRequest()},t.prototype.retry=function(){if(this.abort(),this.retryTimes>=this.config.retry)return this.callbacks.onTimeout(),void this.destroy();this.retryTimes+=1,this.load()},t.prototype.setupRequest=function(){var t=this;this.abortController=new AbortController,this.init.signal=this.abortController.signal,this.res=fetch(this.requestInfo,this.init),this.setupResponse(this.res),this.clearTimer(),this.timeoutTimer=sc((function(){t.retry()}),this.config.timeout||5e3)},t.prototype.setupResponse=function(t){var e=this;t.then((function(t){return e.clearTimer(),t})).then((function(t){return t.ok?"text"===e.config.resType?t.text():"arraybuffer"===e.config.resType?t.arrayBuffer():t.json():(e.callbacks.onError({status:t.status}),void e.destroy())})).then((function(t){e.callbacks.onSuccess({resData:t}),e.destroy()})).catch((function(t){e.callbacks.onError({status:0}),e.destroy()}))},t.prototype.clearTimer=function(){this.timeoutTimer&&(clearTimeout(this.timeoutTimer),this.timeoutTimer=null)},t.prototype.clearCallbacks=function(){this.callbacks={onError:function(){},onSuccess:function(){},onTimeout:function(){}}},t}();function W(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var G=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return W(n=t.apply(this,Xc([],Jc(e),!1))||this,"req",null),W(n,"config",null),n}return Bc(e,t),e.prototype.configure=function(t){this.config=t},e.prototype.destroy=function(){this.abort()},e.prototype.load=function(){var t,e,r;if(this.abort(),this.config){var n=this.createUrl();this.req=new B({retry:2,timeout:5e3,resType:"text"},n,{method:"GET"},{onError:jr(t=this.onError).call(t,this),onSuccess:jr(e=this.onSuccess).call(e,this),onTimeout:jr(r=this.onTimeout).call(r,this)})}},e.prototype.onTimeout=function(){this.emit("EVENT_CONF_LOADER_TIMEOUT")},e.prototype.onSuccess=function(t){var e=t.resData;this.emit("EVENT_CONF_LOADER_LOADED",function(t){for(var e="",r=[99,117,105],n=0;n<t.length;n++){var i=fC(t).call(t,n)^r[n%r.length];e+=SC(i)}return JSON.parse(e)}(e))},e.prototype.onError=function(t){t.status,this.emit("EVENT_CONF_LOADER_ERROR")},e.prototype.createUrl=function(){var t,e,r;if(!this.config)throw Error("[ConfLoader] [createUrl] empty conf");return ci(t=ci(e=ci(r="".concat(this.config.confBaseUrl)).call(r,this.config.streamId,"?domain=")).call(e,this.config.domain,"&d=0&timestamp=")).call(t,Yb())},e.prototype.abort=function(){this.req&&(this.req.destroy(),this.req=null)},e}(t.EventEmitter);function Z(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Y=function(){function t(){Z(this,"config",null),Z(this,"confLoader",new G),Z(this,"confTimer",null)}return t.prototype.destroy=function(){this.stop(),this.config=null,this.confLoader.destroy()},t.prototype.configure=function(t){this.config=t,this.confLoader.configure(t)},t.prototype.start=function(){var t=this;if(this.config&&!this.confTimer&&this.config.confInterval){var e=Math.max(this.config.confInterval,6e4);this.confTimer=ac((function(){t.confLoader.load()}),e)}},t.prototype.stop=function(){this.clearTimer()},t.prototype.restart=function(){this.stop(),this.start()},Tf(t.prototype,"loader",{get:function(){return this.config?this.confLoader:null},enumerable:!1,configurable:!0}),t.prototype.clearTimer=function(){this.confTimer&&(clearTimeout(this.confTimer),this.confTimer=null)},t}();function H(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var J=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return H(n=t.apply(this,Xc([],Jc(e),!1))||this,"confService",new Y),n}return Bc(e,t),e.prototype.destroy=function(){this.confService.destroy()},e.prototype.configure=function(t){this.confService.configure(t);var e=this.confService.loader;e&&this.setupLoader(e)},e.prototype.start=function(){this.confService.start()},e.prototype.load=function(){this.confService.loader&&this.confService.loader.load()},e.prototype.setupLoader=function(t){var e=this;t.on("EVENT_CONF_LOADER_LOADED",(function(t){e.emit("EVENT_CONF_LOADED",t)})),t.on("EVENT_CONF_LOADER_TIMEOUT",(function(){})),t.on("EVENT_CONF_LOADER_ERROR",(function(){}))},e}(t.EventEmitter);function X(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var K=function(){function t(){X(this,"config",null),X(this,"backgroundReportFlag",!1),X(this,"currentReportReq",null)}return t.prototype.destroy=function(){this.config=null,this.clearReportReq()},t.prototype.configure=function(t){this.config=t},t.prototype.enableBackgroundReport=function(){this.backgroundReportFlag=!0},t.prototype.disableBackgroundReport=function(){this.backgroundReportFlag=!1},t.prototype.reportString=function(t){var e=function(t){for(var e=new ArrayBuffer(t.length),r=new Uint8Array(e),n=[99,117,105],i=0;i<t.length;i++)r[i]=fC(t).call(t,i)^n[i%n.length];return r}(t);this.report(e)},t.prototype.report=function(t){this.config&&(this.backgroundReportFlag?this.sendBackground(this.config.reportUrl,t):this.sendWithFetch(this.config.reportUrl,t))},t.prototype.sendBackground=function(t,e){var r;null!==(r=navigator)&&void 0!==r&&r.sendBeacon&&navigator.sendBeacon(t,e)},t.prototype.sendWithFetch=function(t,e){this.currentReportReq=new B({retry:2,timeout:6e3,resType:"text"},t,{method:"POST",headers:{"Content-Type":"application/octet-stream"},body:e},{onError:function(){},onSuccess:function(){},onTimeout:function(){}})},t.prototype.clearReportReq=function(){this.currentReportReq&&(this.currentReportReq.destroy(),this.currentReportReq=null)},t}();function q(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Q=function(){function t(){q(this,"beforeUnloadListener",null),q(this,"beforeUnloadCallback",null),q(this,"collectCallback",null),q(this,"reportTimer",null),q(this,"reporter",new K),q(this,"config",null)}return t.prototype.destroy=function(){var t;this.stop(),this.collectCallback=null,this.config=null,this.removeBeforeUnloadReport(),null===(t=this.reporter)||void 0===t||t.destroy(),this.reporter=null},t.prototype.configure=function(t){var e;this.config=t,null===(e=this.reporter)||void 0===e||e.configure(t)},t.prototype.setCollectCallback=function(t){this.collectCallback=t},t.prototype.start=function(){var t,e=this;this.reportTimer||(this.reportTimer=ac((function(){e.autoCollectAndReport()}),Math.max((null===(t=this.config)||void 0===t?void 0:t.interval)||0,6e4)))},t.prototype.stop=function(){this.clearTimer()},t.prototype.restart=function(){this.stop(),this.start()},t.prototype.backgroundAutoReport=function(){var t,e;null===(t=this.reporter)||void 0===t||t.enableBackgroundReport(),this.autoCollectAndReport(),null===(e=this.reporter)||void 0===e||e.disableBackgroundReport()},t.prototype.autoCollectAndReport=function(){var t;if(this.collectCallback){var e=this.collectCallback(),r=EC(e);null===(t=this.reporter)||void 0===t||t.reportString(r)}},t.prototype.manualReport=function(t){var e,r=EC(t);null===(e=this.reporter)||void 0===e||e.reportString(r)},t.prototype.enableBeforeUnloadReport=function(t){var e=this;this.removeBeforeUnloadReport(),this.beforeUnloadCallback=t||null,this.beforeUnloadListener=function(){e.beforeUnloadCallback&&e.beforeUnloadCallback(),e.backgroundAutoReport()},window.addEventListener("beforeunload",this.beforeUnloadListener)},t.prototype.removeBeforeUnloadReport=function(){this.beforeUnloadListener&&(this.beforeUnloadCallback=null,window.removeEventListener("beforeunload",this.beforeUnloadListener),this.beforeUnloadListener=null)},t.prototype.clearTimer=function(){this.reportTimer&&(clearInterval(this.reportTimer),this.reportTimer=null)},t}(),z=function(){var t="qvb_leb_uuid",e=localStorage.getItem(t);return e||(e="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)})),localStorage.setItem(t,e)),e},$="",tt=function(){return $};z();var et,rt="1.0.7";function nt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}!function(t){t[t.beforeunload=0]="beforeunload",t[t.destroy=1]="destroy",t[t.rollback=2]="rollback"}(et||(et={}));var it,ot=function(){function t(){var t;nt(this,"reportService",new Q),nt(this,"dataCollector",new E),nt(this,"basicReportUnit",new R),nt(this,"detailReportUint",new R),nt(this,"config",null),nt(this,"startTime",Yb()),this.basicReportUnit.configure({deleteZeroNumber:!0,fieldsReportedWithZeroNumber:[],fieldsReservedValueAfterReport:[]}),this.detailReportUint.configure({deleteZeroNumber:!0,fieldsReportedWithZeroNumber:[],fieldsReservedValueAfterReport:[]}),this.reportService.setCollectCallback(jr(t=this.collectCallback).call(t,this))}return t.prototype.destroy=function(){this.tryToSetCloseReason(et.destroy),this.reportService.backgroundAutoReport(),this.reportService.destroy()},t.prototype.configure=function(t){return this.config=t,this.reportService.configure(t),this},t.prototype.init=function(){var t=this;this.reportService.enableBeforeUnloadReport((function(){t.beforeFinalReport(),t.tryToSetCloseReason(et.beforeunload)})),this.registerReportUint("reporterDetailUint",this.detailReportUint)},t.prototype.start=function(){this.reportService.start()},t.prototype.registerReportUint=function(t,e){this.dataCollector.registerReportUint(t,e)},t.prototype.deleteReportUint=function(t){this.dataCollector.deleteReportUint(t)},t.prototype.beforeFinalReport=function(){this.detailReportUint.number(U.PLAY_TIME).set(Math.floor((Yb()-this.startTime)/1e3))},t.prototype.tryToSetCloseReason=function(t){t!==et.beforeunload?t!==et.rollback?t===et.destroy&&(this.basicReportUnit.string("exit_reason").value||this.basicReportUnit.string("exit_reason").set("destroy")):this.basicReportUnit.string("exit_reason").set("rollback"):this.basicReportUnit.string("exit_reason").set("close")},t.prototype.initBasicReportField=function(){if(!this.config)throw Error("report missing config");var t=this.basicReportUnit;return t.number(U.APPID).set(this.config.cloudAppid),t.number(U.PARTNER).set(this.config.xp2pAppid),t.number(U.PLATFORM).set(31),t.string(U.UUID).set(z()),t.string(U.PLAY_ID).set(tt()),t.string(U.REFERER).set(window.location.href),t.string(U.USER_AGENT).set(window.navigator.userAgent),t.number(U.CURRENT_TIME).set(Yb()),t.string(U.VERSION).set(rt),t.number(U.DATA_TIME).set(Math.round(Yb()/1e3)),t.number(U.DATA_TYPE).set(2),t.string(U.URL).set(this.config.streamUrl),t.string(U.VIDEO_TYPE).set("webrtc"),t.generate()},t.prototype.collectCallback=function(){var t=this.initBasicReportField();return this.detailReportUint.number(U.PLAY_STARTED).set(Math.floor((Yb()-this.startTime)/1e3)),t.i=this.dataCollector.generate(),t},t}();function at(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}!function(t){t[t.new=0]="new",t[t.connecting=1]="connecting",t[t.connected=2]="connected",t[t.disconnected=3]="disconnected",t[t.closed=4]="closed",t[t.fail=5]="fail",t[t.timeout=6]="timeout"}(it||(it={}));var st="closed",ct="timeout",ut="disconnect",lt="track",dt="connected",pt="error",ft="failed",ht="stuck",mt=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return at(n=t.apply(this,Xc([],Jc(e),!1))||this,"videoEncodedFrameCache",null),at(n,"audioEncodedFrameCache",null),at(n,"sourceConfig",null),at(n,"peer",null),at(n,"statUnit",null),at(n,"reportUnit",new R),at(n,"timeoutTimer",null),at(n,"closed",!1),at(n,"peerManager",null),at(n,"syncController",null),at(n,"streamInfo",null),n}return Bc(e,t),e.prototype.destroy=function(){this.peerManager=null,this.syncController=null,this.peer=null,this.streamInfo=null,t.prototype.destroy.call(this)},e.prototype.load=function(){this.setTimeout()},e.prototype.close=function(){var t,e=this;if(!this.sourceConfig)throw new Error;this.peerManager&&(this.closed=!0,this.clearTimeout(),null===(t=this.peerManager.getPeer(this.sourceConfig.srcPid))||void 0===t||t.getConnection().destroy(),sc((function(){e.destroy()})))},e.prototype.isLeb=function(){return!1},e.prototype.setPeerManager=function(t){return this.peerManager=t,this},e.prototype.setStatUnit=function(t){return this.statUnit=t,this},Tf(e.prototype,"pid",{get:function(){var t;return null===(t=this.sourceConfig)||void 0===t?void 0:t.srcPid},enumerable:!1,configurable:!0}),e.prototype.setReporter=function(e){return null==e||e.registerReportUint(this.objName,this.reportUnit),t.prototype.setReporter.call(this,e)},e.prototype.setEncodedVideoChunkCache=function(t){return this.videoEncodedFrameCache=t,this},e.prototype.setEncodedAudioChunkCache=function(t){return this.audioEncodedFrameCache=t,this},e.prototype.setSyncController=function(t){return this.syncController=t,this},e.prototype.setStreamInfo=function(t){return this.streamInfo=t,this},e.prototype.setTimeout=function(){var t=this;this.timeoutTimer=sc((function(){t.emit(ct)}),5e3)},e.prototype.clearTimeout=function(){this.timeoutTimer&&(clearTimeout(this.timeoutTimer),this.timeoutTimer=null)},e.prototype.listenConnection=function(t){var e,r=this,n={srcPid:null===(e=this.sourceConfig)||void 0===e?void 0:e.srcPid};t.on("closed",(function(){r.emit(st,n)})),t.on("failed",(function(){r.emit(ft,n)})),t.on("disconnected",(function(){r.emit(ut,n)})),t.on("connected",(function(){r.emit(dt,n)})),t.on("error",(function(t){r.emit(pt,t)})),t.on(i,(function(t){r.emit(i,t)})),t.pc.setOnTrackCallback((function(e){r.clearTimeout(),r.onTrack(e,t)}))},e.prototype.onTrack=function(t,e){},e.prototype.emitTrack=function(t,e){var r;this.emit(lt,{srcPid:null===(r=this.sourceConfig)||void 0===r?void 0:r.srcPid,track:t,rtcPeerConnction:e.getRTCPeerConnection()})},e}(A);function vt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var yt,gt=function(){function t(){}return t.create=function(){return"lebcdn_".concat(Yb())},t.isCdnPid=function(t){return Dc(t).call(t,"lebcdn_")},t}();!function(t){t.P2P="P2P",t.CDN="CDN",t.None="None"}(yt||(yt={}));var bt=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return vt(n=t.apply(this,Xc([],Jc(e),!1))||this,"tagName","Dispatch"),vt(n,"mediaStreamProcessor",null),vt(n,"streamUrl",""),vt(n,"cdnPid",""),vt(n,"p2pTimer",null),vt(n,"subscriber",null),vt(n,"config",null),vt(n,"sourceType",yt.None),vt(n,"reportUnit",new R),vt(n,"statsVar",{loadok:!1,loadStartTime:0}),vt(n,"cdnRetryCtrl",{totalRetryTimes:3,leftRetryTimes:3,retryDelay:0,reset:function(){this.leftRetryTimes=this.totalRetryTimes},retry:function(){this.leftRetryTimes-=1},allowedToRetry:function(){return this.leftRetryTimes>0}}),n}return Bc(e,t),e.prototype.destroy=function(){this.clearP2PTimer()},e.prototype.setMediaStreamProcessor=function(t){this.mediaStreamProcessor=t},e.prototype.setSubscriber=function(t){this.subscriber=t},e.prototype.setReporter=function(e){return null==e||e.registerReportUint(this.objName,this.reportUnit),t.prototype.setReporter.call(this,e)},e.prototype.configure=function(t){this.config=t,this.configCdnRetryCtrl(t)},e.prototype.load=function(t){var e=this;this.streamUrl=t,this.reportUnit.number(U.REQUEST).set(1),this.statsVar.loadStartTime=Yb();var r=this.loadFromCdn(this.streamUrl);return r&&this.statsStartStream(r),this.clearP2PTimer(),this.p2pTimer=ac((function(){e.loadFromP2P()}),5e3),RR.resolve()},e.prototype.configCdnRetryCtrl=function(t){this.cdnRetryCtrl.totalRetryTimes=t.connectRetryCount,this.cdnRetryCtrl.leftRetryTimes=t.connectRetryCount,this.cdnRetryCtrl.retryDelay=t.connectRetryDelay},e.prototype.loadFromCdn=function(t){var e=this;if(!this.subscriber)throw Error("missing subscriber");if(!this.config)throw Error("missing config");if(!this.cdnRetryCtrl.allowedToRetry())return this.reportUnit.string(U.ROLLBACK_REASON).set("".concat(a.ERR_LOAD_REACH_MAX_RETRY)),void sc((function(){var t;null===(t=e.bus)||void 0===t||t.emit(o,{code:a.ERR_LOAD_REACH_MAX_RETRY,msg:"已达最大CDN重试次数: ".concat(e.cdnRetryCtrl.totalRetryTimes)})}));this.cdnRetryCtrl.retry();var r=gt.create(),n=this.subscriber.subscribeLEB({pullStreamUrl:this.config.pullStreamUrl,stopStreamUrl:this.config.stopStreamUrl,streamUrl:t,srcPid:r,param:{uuid:z(),playid:tt()}});return n.on(lt,(function(t){var r;"video"===t.track.kind&&e.cdnRetryCtrl.reset(),e.sourceType!==yt.CDN&&(e.sourceType=yt.CDN,null===(r=e.bus)||void 0===r||r.emit(o,{code:a.INF_PLAY_EVT_STREAM_SWITCH,msg:"切换到CDN拉流",data:{source:"CDN"}})),e.onNewSourceTrack(t)})).once(dt,(function(){var t;null===(t=e.bus)||void 0===t||t.emit(o,{code:a.INF_PLAY_EVT_SERVER_CONNECTED,msg:"已经连接到cdn服务器"})})).once(ut,(function(){var t;e.reloadCDNAsync({from:"cdn",reason:"".concat(r,"-DISCONNECT")}),null===(t=e.bus)||void 0===t||t.emit(o,{code:a.INF_PLAY_EVT_SERVER_RECONNECT,msg:"连接cdn服务器失败，已启动自动重连恢复"})})).once(ft,(function(){e.reloadCDNAsync({from:"cdn",reason:"".concat(r,"-FAILED")})})).once(pt,(function(t){e.reloadCDNAsync({from:"cdn",reason:"".concat(r,"-ERROR")})})).once(st,(function(){var t;null===(t=e.bus)||void 0===t||t.emit(o,{code:a.INF_PLAY_EVT_STREAM_CLOSED,msg:"流被关闭了"})})),n},e.prototype.loadFromP2P=function(t){var e=this;if(this.subscriber){var r=this.subscriber.subscribeP2P(t);return r?(r.on(lt,(function(t){var r;e.sourceType!==yt.P2P&&(e.sourceType=yt.P2P,null===(r=e.bus)||void 0===r||r.emit(o,{code:a.INF_PLAY_EVT_STREAM_SWITCH,msg:"切换到P2P拉流",data:{source:"P2P"}})),e.onNewSourceTrack(t)})).once(ut,(function(){e.reloadCDNAsync({from:"p2p",reason:"p2p-DISCONNECT"})})).once(ht,(function(){e.reloadCDNAsync({from:"p2p",reason:"p2p-STUCK"})})).once(st,(function(){})),r):void 0}},e.prototype.onNewSourceTrack=function(t){var e=this;return new RR((function(e){e(t)})).then((function(){if(!e.mediaStreamProcessor)throw Error("miss mediaStreamProcessor");e.mediaStreamProcessor.addMediaStreamTrack(t.track)})).catch((function(t){t.fatal}))},e.prototype.reloadCDNAsync=function(t){var e=this,r=t.from,n=t.reason;"p2p"===r&&(this.reportUnit.number(U.BACK_CDN_STREAM).plus(1),this.reportUnit.string(U.BACK_CDN_STREAM_REASON).set(n));var i="p2p"===r?0:this.cdnRetryCtrl.retryDelay;sc((function(){e.loadFromCdn(e.streamUrl)}),i)},e.prototype.statsStartStream=function(t){var e=this;t.on(ct,(function(){e.statsVar.loadok||e.reportUnit.string(U.ROLLBACK_REASON).set("".concat(a.ERR_LOAD_TIMEOUT)),sc((function(){var t;null===(t=e.bus)||void 0===t||t.emit(o,{code:a.ERR_LOAD_TIMEOUT,msg:"拉流超时"})}))})).on(lt,(function(t){"video"===t.track.kind&&(e.statsVar.loadok||(e.reportUnit.number(U.LOAD_OK_TIME).set(Yb()-e.statsVar.loadStartTime),e.statsVar.loadok=!0))})).on(i,(function(t){var r;t.code>=1e3&&t.code<2e3&&e.reportUnit.string(U.ROLLBACK_REASON).set("".concat(t.code)),null===(r=e.bus)||void 0===r||r.emit(o,t)}))},e.prototype.clearP2PTimer=function(){this.p2pTimer&&(clearInterval(this.p2pTimer),this.p2pTimer=null)},e}(A);function Ct(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var St=function(t){function e(){for(var e,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];var i=this;return Ct(i=t.apply(this,Xc([],Jc(r),!1))||this,"serverName","DefaultServer"),Ct(i,"transport",null),Ct(i,"router",new Bb),Ct(i,"triggerHandler",jr(e=i.triggerAPI).call(e,i)),i}return Bc(e,t),e.prototype.destroy=function(){var e;null===(e=this.transport)||void 0===e||e.removeListener(this.serverName,this.triggerHandler),t.prototype.destroy.call(this)},e.prototype.setTransport=function(t){this.transport=t,t.on(this.serverName,this.triggerHandler)},e.prototype.registerAPI=function(t,e){this.router.set(t,e)},e.prototype.deleteAPI=function(t){this.router.delete(t)},e.prototype.triggerAPI=function(t){var e=this.router.get("".concat(t.path));e&&e(t)},e}(A);function Tt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var _t=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return Tt(n=t.apply(this,Xc([],Jc(e),!1))||this,"serverName","P2PServer"),n}return Bc(e,t),e}(St);function Rt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Et=0,wt=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return Rt(n=t.apply(this,Xc([],Jc(e),!1))||this,"tagName","TempClient"),Rt(n,"transport",null),Rt(n,"localPid",""),n}return Bc(e,t),e.prototype.setTransport=function(t){this.transport=t},e.prototype.setLocalPid=function(t){this.localPid=t},e.prototype.destroy=function(){this.transport=null,t.prototype.destroy.call(this)},e.prototype.request=function(t){var e,r;null===(r=this.transport)||void 0===r||r.send({host:t.host,path:"".concat(t.path),from:t.from||this.localPid,to:t.to,type:t.type||"app",payload:t.payload,requestId:ci(e="req_".concat(Yb(),"_")).call(e,Et+=1)})},e}(A);function Pt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var kt=function(t){function e(e,r){var n=this;return Pt(n=t.call(this)||this,"tag","WS"),Pt(n,"url",""),Pt(n,"id",""),Pt(n,"options",{retry:0}),Pt(n,"ws",null),n.url=e,n.options=r,n.init(),n}return Bc(e,t),e.prototype.destroy=function(){var e;null===(e=this.ws)||void 0===e||e.close(),this.ws=null,this.id="",t.prototype.destroy.call(this)},e.prototype.send=function(t,e){var r,n;null===(n=this.ws)||void 0===n||n.send(ci(r="S ".concat(t," ")).call(r,e))},e.prototype.init=function(){var t,e,r,n;WebSocket&&(this.ws=new WebSocket(this.url),this.ws.onopen=jr(t=this.onOpen).call(t,this),this.ws.onmessage=jr(e=this.onMessage).call(e,this),this.ws.onclose=jr(r=this.onClose).call(r,this),this.ws.onerror=jr(n=this.onError).call(n,this))},e.prototype.onOpen=function(){},e.prototype.onMessage=function(t){var e=t.data.substring(0,1);if("C"===e)this.id=t.data.substring(2),this.emit("EVENT_WS_ID",this.id);else if("S"===e){var r=t.data.substring(29);this.emit("EVENT_WS_MESSAGE",r)}},e.prototype.onClose=function(){},e.prototype.onError=function(){},e}(A),At=r(961),It=r.n(At);function xt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Lt=function(){function t(){xt(this,"totalBlock",0),xt(this,"recvBlock",0),xt(this,"blocks",new Bb)}return t.prototype.getFullContent=function(){if(!this.complete())return null;for(var t="",e=0;e<this.totalBlock;e+=1)t+=this.blocks.get(e);return t},t.prototype.set=function(t,e,r){this.blocks.has(t)||(this.blocks.set(t,e),this.totalBlock=r,this.recvBlock+=1)},t.prototype.complete=function(){return this.totalBlock===this.recvBlock},t}(),Dt=function(){function t(){xt(this,"messages",new Bb)}return t.prototype.set=function(t,e){var r=e.curIndex,n=e.total,i=e.block,o=this.messages.get(t);o||(o=new Lt,this.messages.set(t,o)),o.set(r,i,n)},t.prototype.get=function(t){var e;return null===(e=this.messages.get(t))||void 0===e?void 0:e.getFullContent()},t.prototype.delete=function(t){this.messages.delete(t)},t}(),Nt=function(){function t(){}return t.split=function(t){for(var e=Yb(),r=[],n=5e3,i=Math.ceil(t.length/n),o=0;o<i;o++){var a,s,c,u=ci(a=ci(s="".concat(e,"-")).call(s,o,"-")).call(a,i,"#");r.push(ci(c="".concat(u)).call(c,hf(t).call(t,o*n,(o+1)*n)))}return r},t.extract=function(t){var e=gc(t).call(t,"#"),r=hf(t).call(t,0,e+1),n=hf(t).call(t,e+1),i=Jc(r.split("-"),3),o=i[0],a=i[1],s=i[2];return{key:o,index:HR(a,10),total:HR(s,10),content:n}},t}();function Ot(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Mt="EVENT_SIGNAL_ID",Ut="EVENT_SIGNAL_MESSAGE",Vt=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return Ot(n=t.apply(this,Xc([],Jc(e),!1))||this,"id",""),Ot(n,"tag","Signal"),Ot(n,"messageCache",new Dt),Ot(n,"ws",null),Ot(n,"url",""),n}return Bc(e,t),e.prototype.connect=function(t){this.url=t,this.init()},e.prototype.destroy=function(){this.ws&&this.ws.destroy(),this.ws=null,this.id="",t.prototype.destroy.call(this)},e.prototype.ready=function(){return!!this.id},e.prototype.send=function(t,e){var r,n=this,i=It().compressToBase64(e);Hs(r=Nt.split(i)).call(r,(function(e){var r;null===(r=n.ws)||void 0===r||r.send(t,e)}))},e.prototype.init=function(){var t=this;this.ws||(this.ws=new kt(this.url,{retry:0}),this.ws.on("EVENT_WS_ID",(function(e){t.id=e,t.emit("EVENT_SIGNAL_ID",e)})),this.ws.on("EVENT_WS_MESSAGE",(function(e){var r=Nt.extract(e);t.messageCache.set(r.key,{curIndex:r.index,total:r.total,block:r.content});var n=t.messageCache.get(r.key);if(n){t.messageCache.delete(r.key);var i=It().decompressFromBase64(n);t.emit("EVENT_SIGNAL_MESSAGE",i)}})))},e}(A);function Ft(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var jt=function(){function t(){Ft(this,"rules",new hg)}return t.prototype.destroy=function(){this.rules.clear()},t.prototype.addRule=function(t){this.rules.add(t)},t.prototype.match=function(t){var e,r;try{for(var n=Hc(this.rules),i=n.next();!i.done;i=n.next())if(i.value.match(t))return!0}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return!1},t}(),Bt=function(){function t(){}return t.prototype.destroy=function(){},t.prototype.score=function(){return 0},t}();function Wt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Gt=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return Wt(n=t.apply(this,Xc([],Jc(e),!1))||this,"tagName","Peer"),Wt(n,"pid",""),Wt(n,"refreshTime",Yb()),Wt(n,"info",null),Wt(n,"scorer",new Bt),Wt(n,"filter",new jt),Wt(n,"connection",null),n}return Bc(e,t),e.prototype.setPid=function(t){this.pid=t},e.prototype.setConnection=function(t){this.connection=t},e.prototype.getConnection=function(){if(!this.connection)throw Error("empty conn");return this.connection},e.prototype.setFilter=function(t){this.filter=t},e.prototype.setScorer=function(t){this.scorer=t},e.prototype.destroy=function(){var e;this.scorer.destroy(),Fc(this).destroy(),null===(e=this.connection)||void 0===e||e.destroy(),this.connection=null,t.prototype.destroy.call(this)},e.prototype.fresh=function(){return Yb()-this.refreshTime<6e4},e}(A);function Zt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Yt=function(t){function e(e,r){var n,i,o=e.pid,a=this;return Zt(a=t.call(this)||this,"rtcPC",void 0),Zt(a,"pid",""),Zt(a,"onIceCandidateCallback",null),a.pid=o,a.tagName="PeerConnection_".concat(o),a.rtcPC=new RTCPeerConnection(r),a.rtcPC.onicecandidate=jr(n=a.onIceCandidate).call(n,a),a.rtcPC.oniceconnectionstatechange=jr(i=a.onIceConnectionStateChange).call(i,a),a}return Bc(e,t),e.prototype.destroy=function(){this.rtcPC.close(),this.onIceCandidateCallback=null,t.prototype.destroy.call(this)},e.prototype.setOnTrackCallback=function(t){this.rtcPC.ontrack=t},e.prototype.clearOnTrackCallbak=function(){this.rtcPC.ontrack=null},e.prototype.setOnIceCandidateCallback=function(t){this.onIceCandidateCallback=t},e.prototype.createOffer=function(t){var e=this;return this.rtcPC.createOffer(t).then((function(t){return e.rtcPC.setLocalDescription(t)})).then((function(){return e.rtcPC.localDescription})).catch((function(e){return RR.reject({code:a.ERR_PLAY_WEBRTC_FAIL,msg:"createOffer fail, offer: ".concat(t)})}))},e.prototype.recvOffer=function(t){return this.rtcPC.setRemoteDescription(new RTCSessionDescription(t))},e.prototype.createAnswer=function(){var t=this;return this.rtcPC.createAnswer().then((function(e){return t.rtcPC.setLocalDescription(e)})).then((function(){return t.rtcPC.localDescription}))},e.prototype.recvAnswer=function(t){return this.rtcPC.setRemoteDescription(new RTCSessionDescription(t)).catch((function(e){return RR.reject({code:a.ERR_PLAY_WEBRTC_FAIL,msg:"setRemoteDescription fail, answer: ".concat(t.sdp)})}))},e.prototype.recvCandidate=function(t){if(t)return this.rtcPC.addIceCandidate(new RTCIceCandidate(t))},e.prototype.onIceCandidate=function(t){var e=null==t?void 0:t.candidate;null!=e&&e.candidate&&this.onIceCandidateCallback&&this.onIceCandidateCallback(e.toJSON())},e.prototype.onIceConnectionStateChange=function(){this.rtcPC.iceConnectionState,"failed"===this.rtcPC.iceConnectionState&&this.rtcPC.restartIce()},e}(A);function Ht(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Jt=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return Ht(n=t.apply(this,Xc([],Jc(e),!1))||this,"tagName","DataChannel"),Ht(n,"rtcDataChannel",null),Ht(n,"config",null),Ht(n,"pc",null),n}return Bc(e,t),e.prototype.configure=function(t){return this.config=t,this},e.prototype.destroy=function(){var t;null===(t=this.rtcDataChannel)||void 0===t||t.close(),this.rtcDataChannel=null,this.pc=null},e.prototype.setRTCPeerConnection=function(t){return this.pc=t,this},e.prototype.init=function(){if(!this.config||!this.pc)throw Error("missing param");return this.rtcDataChannel=this.pc.createDataChannel(this.config.label,{id:this.config.id,negotiated:!0}),this.setCallback(this.rtcDataChannel),this},e.prototype.setCallback=function(t){var e=this;t.onopen=function(){e.emit("open")},t.onmessage=function(t){e.emit(t.data)},t.onerror=function(t){e.emit("error")},t.onclosing=function(){e.emit("closing")},t.onclose=function(){e.emit("close")}},e}(A);function Xt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Kt=function(t){function e(e,r){var n=this;return Xt(n=t.call(this)||this,"tagName","P2PConnection"),Xt(n,"pc",void 0),Xt(n,"connInit",void 0),Xt(n,"makingOffer",!1),Xt(n,"isSettingRemoteAnswerPending",!1),Xt(n,"closed",!1),Xt(n,"connectTimer",null),Xt(n,"nego",!1),Xt(n,"dc",void 0),Xt(n,"destroyed",!1),n.connInit=e,n.tagName="P2PConnection_".concat(n.connInit.targetPid),n.pc=new Yt({pid:e.targetPid},r),n.dc=new Jt,n}return Bc(e,t),e.prototype.destroy=function(){this.destroyed||(this.destroyed=!0,this.dc.destroy(),this.pc.destroy(),this.close(),t.prototype.destroy.call(this))},e.prototype.getRTCPeerConnection=function(){return this.pc.rtcPC},e.prototype.init=function(){var t,e=this;this.on("EVENT_P2P_SIGNAL_RECV",jr(t=this.recvSignalMessage).call(t,this)),this.pc.setOnIceCandidateCallback((function(t){e.sendSignal({from:e.connInit.localPid,to:e.connInit.targetPid,type:"iceCandidate",payload:t})})),this.pc.rtcPC.onnegotiationneeded=function(){return Zc(e,void 0,void 0,(function(){return Yc(this,(function(t){switch(t.label){case 0:return this.nego=!0,[4,this.connect()];case 1:return t.sent(),this.nego=!1,[2]}}))}))},this.pc.rtcPC.onconnectionstatechange=function(){var t=e.pc.rtcPC.connectionState;e.emit(t)},this.dc.setRTCPeerConnection(this.pc.rtcPC).configure({label:"base",id:0}).init().on("error",(function(){e.emit("disconnected")}))},e.prototype.recvSignalMessage=function(t){return Zc(this,void 0,void 0,(function(){var e;return Yc(this,(function(r){switch(r.label){case 0:switch(t.type){case"answer":return[3,1];case"offer":return[3,5];case"iceCandidate":return[3,10]}return[3,13];case 1:return r.trys.push([1,3,,4]),this.isSettingRemoteAnswerPending=!0,[4,this.pc.recvAnswer(t.payload)];case 2:return r.sent(),this.isSettingRemoteAnswerPending=!1,[3,4];case 3:return r.sent(),[3,4];case 4:case 9:return[3,13];case 5:return r.trys.push([5,8,,9]),this.ignoreOffer?[2]:[4,this.pc.recvOffer(t.payload)];case 6:return r.sent(),[4,this.pc.rtcPC.setLocalDescription()];case 7:return r.sent(),(e=this.pc.rtcPC.localDescription)?(this.sendSignal({from:this.connInit.localPid,to:this.connInit.targetPid,type:"answer",payload:e}),[3,9]):[2];case 8:return r.sent(),[3,9];case 10:return r.trys.push([10,12,,13]),[4,this.pc.recvCandidate(t.payload)];case 11:return r.sent(),[3,13];case 12:return r.sent(),this.ignoreOffer,[3,13];case 13:return[2]}}))}))},e.prototype.connect=function(){return Zc(this,void 0,void 0,(function(){var t,e;return Yc(this,(function(r){switch(r.label){case 0:if(!this.nego)return[2];t={offerToReceiveAudio:!0,offerToReceiveVideo:!0},this.makingOffer=!0,r.label=1;case 1:return r.trys.push([1,3,4,5]),[4,this.pc.createOffer(t)];case 2:return(e=r.sent())?(this.sendSignal({from:this.connInit.localPid,to:this.connInit.targetPid,type:"offer",payload:e}),[3,5]):[2];case 3:return r.sent(),[3,5];case 4:return this.makingOffer=!1,[7];case 5:return[2]}}))}))},e.prototype.close=function(){this.closed||(this.closed=!0,this.emit("closed"))},e.prototype.sendSignal=function(t){this.emit("EVENT_P2P_SIGNAL_SEND",t)},Tf(e.prototype,"politeRole",{get:function(){var t=this.connInit;return t.localPid>t.targetPid},enumerable:!1,configurable:!0}),Tf(e.prototype,"readyForOffer",{get:function(){return!this.makingOffer&&("stable"===this.pc.rtcPC.signalingState||this.isSettingRemoteAnswerPending)},enumerable:!1,configurable:!0}),Tf(e.prototype,"offerCollision",{get:function(){return!this.readyForOffer},enumerable:!1,configurable:!0}),Tf(e.prototype,"ignoreOffer",{get:function(){return!this.politeRole&&this.offerCollision},enumerable:!1,configurable:!0}),e}(A);function qt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Qt=0,zt=function(t){function e(e,r){var n,i=this;return qt(i=t.call(this)||this,"tagName","LEBConnection"),qt(i,"pc",void 0),qt(i,"initConfig",void 0),qt(i,"svrSig",""),qt(i,"closed",!1),qt(i,"sessionId",Yb()),qt(i,"destroyed",!1),i.initConfig=e,i.tagName=ci(n="".concat(i.tagName,"_")).call(n,i.initConfig.srcPid),i.pc=new Yt({pid:e.srcPid},r),i.pc.rtcPC.onconnectionstatechange=function(){var t=i.pc.rtcPC.connectionState;i.emit(t)},i.pc.rtcPC.oniceconnectionstatechange=function(){i.pc.rtcPC.connectionState},i}return Bc(e,t),Tf(e.prototype,"isSdpStreamUrl",{get:function(){return/^https?:\/\/.+\.sdp/.test(this.initConfig.streamUrl)},enumerable:!1,configurable:!0}),e.prototype.destroy=function(){this.destroyed||(this.destroyed=!0,this.close(),this.pc.destroy(),t.prototype.destroy.call(this))},e.prototype.getRTCPeerConnection=function(){return this.pc.rtcPC},e.prototype.connect=function(){return Zc(this,void 0,void 0,(function(){var t,e,r,n,o;return Yc(this,(function(s){switch(s.label){case 0:t={offerToReceiveAudio:!0,offerToReceiveVideo:!0,voiceActivityDetection:!1},s.label=1;case 1:return s.trys.push([1,7,,8]),[4,this.pc.createOffer(t)];case 2:return e=s.sent(),this.emit(i,{code:a.INF_PLAY_EVT_REQUEST_PULL_BEGIN,msg:"开始拉流时触发",data:{localsdp:null==e?void 0:e.sdp,sessionid:this.sessionId}}),this.isSdpStreamUrl?[4,this.fetchStreamSdp(e)]:[3,4];case 3:return n=s.sent(),[3,6];case 4:return[4,this.fetchStream(e)];case 5:n=s.sent(),s.label=6;case 6:return r=n,this.svrSig=r.svrsig,this.emit(i,{code:a.INF_PLAY_EVT_REQUEST_PULL_SUCCESS,msg:"拉流成功后触发",data:{localsdp:null==e?void 0:e.sdp,remotesdp:r.remotesdp.sdp,sessionid:this.sessionId}}),this.pc.recvAnswer(r.remotesdp),[3,8];case 7:return o=s.sent(),this.emit(i,o),this.emit("error",{errcode:0,errmsg:"leb pull stream fetch error: ".concat(o)}),[3,8];case 8:return[2]}}))}))},e.prototype.fetchStream=function(t){return Zc(this,void 0,void 0,(function(){var e,r,n,i;return Yc(this,(function(o){var s,c,u,l,d;switch(o.label){case 0:e={streamurl:"".concat(this.initConfig.streamUrl),sessionid:this.sessionId,clientinfo:this.initConfig.param.uuid,localsdp:t,seipass:1},r=ci(s=ci(c=ci(u=ci(l=ci(d="".concat(this.initConfig.pullStreamUrl,"?from=")).call(d,this.initConfig.srcPid,"&uuid=")).call(l,this.initConfig.param.uuid,"&playid=")).call(u,this.initConfig.param.playid,"&sessionid=")).call(c,this.sessionId,"&reqCounter=")).call(s,Qt+=1),o.label=1;case 1:return o.trys.push([1,4,,5]),[4,fetch(r,{method:"POST",body:EC(e),headers:{"Content-Type":"application/json"}})];case 2:return[4,o.sent().json()];case 3:return[2,(n=o.sent()).errcode||!n.svrsig?RR.reject({code:a.ERR_PLAY_REQUEST_PULL_FAIL,msg:"拉流接⼝后台返回报错",data:n}):n];case 4:return i=o.sent(),[2,RR.reject({code:a.ERR_FETCH_REQUEST_FAIL,msg:"拉流接⼝网络异常",data:{from:"PULL_STREAM",url:r,error:i}})];case 5:return[2]}}))}))},e.prototype.fetchStreamSdp=function(t){return Zc(this,void 0,void 0,(function(){var e,r,n,i,o,s,c,u;return Yc(this,(function(l){switch(l.label){case 0:return e={version:"v1.0",sessionid:this.sessionId,localSdp:t},[4,fetch(this.initConfig.streamUrl,{method:"POST",body:EC(e),headers:{"Content-Type":"application/json"}})];case 1:r=l.sent(),l.label=2;case 2:return l.trys.push([2,4,,5]),[4,r.json()];case 3:return n=l.sent(),i=n.code,o=n.message,s=n.remoteSdp,c=n.svrsig,200!==i?[2,RR.reject({code:a.ERR_PLAY_REQUEST_PULL_FAIL,msg:"拉流接⼝后台返回报错",data:n})]:[2,{remotesdp:s,svrsig:c,errcode:0,errmsg:o}];case 4:return u=l.sent(),[2,RR.reject({code:a.ERR_FETCH_REQUEST_FAIL,msg:"拉流接⼝网络异常",data:{from:"PULL_STREAM",url:this.initConfig.streamUrl,error:u}})];case 5:return[2]}}))}))},e.prototype.close=function(){return Zc(this,void 0,void 0,(function(){return Yc(this,(function(t){switch(t.label){case 0:return this.closed?[3,2]:(this.closed=!0,this.emit("closed"),[4,this.closeLeb()]);case 1:t.sent(),t.label=2;case 2:return[2]}}))}))},e.prototype.closeLeb=function(){return Zc(this,void 0,void 0,(function(){var t,e,r;return Yc(this,(function(n){var o,s;switch(n.label){case 0:if(this.isSdpStreamUrl&&!this.svrSig)return[2];t={streamurl:this.initConfig.streamUrl,svrsig:this.svrSig},e=ci(o="".concat(this.initConfig.stopStreamUrl,"?from=")).call(o,this.initConfig.srcPid),n.label=1;case 1:return n.trys.push([1,4,,5]),[4,fetch(e,{method:"post",body:EC(t),headers:{"Content-Type":"application/json"}})];case 2:return[4,n.sent().json()];case 3:return(r=n.sent()).errcode&&(this.emit("error",{errcode:1,errmsg:ci(s="leb stop stream response error: errcode: ".concat(r.errcode,", errmsg: ")).call(s,r.errmsg)}),this.emit(i,{code:a.ERR_PLAY_REQUEST_STOP_FAIL,msg:"停流接⼝后台返回报错",data:{url:e}})),[3,5];case 4:return n.sent(),this.emit(i,{code:a.ERR_FETCH_REQUEST_FAIL,msg:"停流接⼝网络异常",data:{from:"STOP_STREAM",url:e}}),[3,5];case 5:return[2]}}))}))},e}(A);function $t(t,e){var r=Ef(t);if(mf){var n=mf(t);e&&(n=Fc(n).call(n,(function(e){return Of(t,e).enumerable}))),r.push.apply(r,n)}return r}function te(t){for(var e=1;e<arguments.length;e++){var r,n,i=null!=arguments[e]?arguments[e]:{};e%2?Hs(r=$t(Object(i),!0)).call(r,(function(e){ee(t,e,i[e])})):Hf?$f(t,Hf(i)):Hs(n=$t(Object(i))).call(n,(function(e){Tf(t,e,Of(i,e))}))}return t}function ee(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var re=function(){function t(){}return t.prototype.create=function(t){var e=this.createPeer({pid:t.srcPid}),r=this.createConnection(t);return e.setConnection(r),e},t.prototype.createConnection=function(t){return new zt(t,{iceServers:[],encodedInsertableStreams:!0})},t.prototype.createPeer=function(t){var e=t.pid,r=new Gt;return r.setPid(e),r},t}(),ne=function(){function t(){ee(this,"client",null),ee(this,"server",null),ee(this,"localPid",""),ee(this,"config",null)}return t.prototype.create=function(t){var e=this.createConnection(t),r=this.createPeer({pid:t.targetPid});return r.setConnection(e),r},t.prototype.setClient=function(t){return this.client=t,this},t.prototype.setServer=function(t){return this.server=t,this},t.prototype.setLocalPid=function(t){return this.localPid=t,this},t.prototype.configure=function(t){this.config=t},t.prototype.createConnection=function(t){var e=this;if(!this.server)throw Error("P2PPeerBuilder empty server");if(!this.config)throw Error("P2PPeerBuilder empty config");var r=new Kt({targetPid:t.targetPid,localPid:this.localPid},{iceServers:[{urls:["stun:".concat(this.config.stun)]}],encodedInsertableStreams:!0});return r.on("EVENT_P2P_SIGNAL_SEND",(function(t){if(!e.server||!e.client||!e.localPid)throw Error("P2PPeerBuilder param error");var r=te(te({},t),{},{from:e.localPid,path:"signal",host:e.server.serverName});e.client.request(r)})),r.init(),r},t.prototype.createPeer=function(t){var e=t.pid,r=new Gt;return r.setPid(e),r},t}();function ie(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var oe=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Bc(e,t),e.prototype.send=function(t){},e.prototype.recv=function(t){},e}(e()),ae=function(t){function e(){for(var e,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];var i=this;return ie(i=t.apply(this,Xc([],Jc(r),!1))||this,"signal",null),ie(i,"recvHandler",jr(e=i.recv).call(e,i)),i}return Bc(e,t),e.prototype.destroy=function(){var t;null===(t=this.signal)||void 0===t||t.removeListener(Ut,this.recvHandler),this.signal=null,this.removeAllListeners()},e.prototype.setSignal=function(t){this.signal=t,t.on(Ut,this.recvHandler)},e.prototype.send=function(t){var e;null===(e=this.signal)||void 0===e||e.send(t.to,EC(t))},e.prototype.recv=function(t){var e;try{e=JSON.parse(t)}catch(t){return}this.emit(e.host,e)},e}(oe);function se(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var ce=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return se(n=t.apply(this,Xc([],Jc(e),!1))||this,"tagName","PeerPool"),se(n,"reportUnit",new R),se(n,"candidate",new Bb),se(n,"connected",new Bb),se(n,"connecting",new Bb),se(n,"config",null),se(n,"tickTimer",null),n}return Bc(e,t),e.prototype.configure=function(t){var e=this;this.config=t,this.reportUnit.configure({deleteZeroNumber:!0,fieldsReportedWithZeroNumber:[],fieldsReservedValueAfterReport:["conn_try_total","conn_succ_total"]}),this.reportUnit.registerPreHook(this.objName,(function(){var t=e.stats();e.reportUnit.number("connected").set(t.connected)}))},e.prototype.init=function(){var t;if(!this.config)throw Error("missing config");this.clearTimer(),this.tickTimer=ac(jr(t=this.tick).call(t,this),this.config.tickInterval)},e.prototype.destroy=function(){var e,r,n;this.clearTimer(),Hs(e=this.connecting).call(e,(function(t){t.destroy()})),this.connecting.clear(),Hs(r=this.candidate).call(r,(function(t){t.destroy()})),this.candidate.clear(),Hs(n=this.connected).call(n,(function(t){t.destroy()})),this.connected.clear(),this.config=null,t.prototype.destroy.call(this)},e.prototype.stats=function(){return{connected:this.connected.size,connecting:this.connecting.size,candidate:this.candidate.size}},e.prototype.addCandidate=function(t){this.get(t.pid)||(this.candidate.set(t.pid,t),this.tick())},e.prototype.addConnecting=function(t){this.get(t.pid)||(this.connecting.set(t.pid,t),this.setupConnection(t))},e.prototype.tick=function(){if(this.config&&!(this.connecting.size>this.config.maxConnecting)){var t=this.config.maxConnected-this.connected.size;if(t)for(var e,r=tE(e=this.candidate).call(e),n=function(){var t=r.next();return t.done?null:t.value[1]},i=t;i>0;){var o=n();if(!o)return void this.emit("EVENT_LESS_CANDIDATE",{lackCnt:t});o.fresh()?this.connecting.has(o.pid)||this.connected.has(o.pid)||(this.reportUnit.number("conn_try").plus(1),this.reportUnit.number("conn_try_total").plus(1),this.candidate.delete(o.pid),this.prepareConnectCandiate(o),i-=1):this.remove(o.pid)}}},e.prototype.bestOne=function(t){var e,r;try{for(var n=Hc(this.connected),i=n.next();!i.done;i=n.next()){var o=Jc(i.value,2),a=o[0],s=o[1];if(!Hi(t).call(t,a))return s}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return null},e.prototype.get=function(t){return this.candidate.get(t)||this.connected.get(t)||this.connecting.get(t)},e.prototype.getConnected=function(t){return this.connected.get(t)},e.prototype.remove=function(t){var e=this.get(t);e&&(this.connecting.delete(t),this.connected.delete(t),this.candidate.delete(t),sc((function(){e.destroy()})))},e.prototype.prepareConnectCandiate=function(t){this.setupConnection(t),this.connected.delete(t.pid),this.connecting.set(t.pid,t),t.getConnection().connect()},e.prototype.setupConnection=function(t){var e=this,r=function(r){e.remove(t.pid)},n=sc((function(){e.reportUnit.number("init_pc_timeout").plus(1),r()}),1e4),i=t.getConnection();i.on("connected",(function(){e.reportUnit.number("conn_succ").plus(1),e.reportUnit.number("conn_succ_total").plus(1),clearTimeout(n),e.connecting.delete(t.pid),e.connected.set(t.pid,t)})),i.on("closed",(function(){clearTimeout(n),r()})),i.on("disconnected",(function(){r()})),i.on("failed",(function(){r()}))},e.prototype.clearTimer=function(){this.tickTimer&&(clearTimeout(this.tickTimer),this.tickTimer=null)},e}(A);function ue(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var le=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return ue(n=t.apply(this,Xc([],Jc(e),!1))||this,"tagName","PeerManager"),ue(n,"peerPool",new ce),ue(n,"peerCache",null),ue(n,"peerBuilder",null),ue(n,"config",null),n}return Bc(e,t),e.prototype.destroy=function(){this.peerPool&&this.peerPool.destroy(),this.peerBuilder=null,t.prototype.destroy.call(this)},e.prototype.configure=function(t){this.config=t,this.peerPool.configure(t)},e.prototype.setReporter=function(e){return null==e||e.registerReportUint(this.objName,this.peerPool.reportUnit),t.prototype.setReporter.call(this,e)},e.prototype.init=function(){var t=this;this.peerPool.on("EVENT_LESS_CANDIDATE",(function(e){if(e.lackCnt,!t.peerCache)throw Error("missing param");var r=t.peerCache.getPid();t.addPeerId(r)})),this.peerPool.init()},e.prototype.setPeerBuilder=function(t){return this.peerBuilder=t,this},e.prototype.setPeerCache=function(t){return this.peerCache=t,this},e.prototype.setSortStrategy=function(){},e.prototype.setKickStrategy=function(){},e.prototype.addPeerId=function(t){if(t&&!this.peerPool.get(t)&&this.peerBuilder&&this.config){var e=this.peerBuilder.create({targetPid:t});this.addPeer(e)}},e.prototype.addPeer=function(t){this.peerPool.addCandidate(t)},e.prototype.addConnectingPeer=function(t){this.peerPool.addConnecting(t)},e.prototype.getPeer=function(t){return this.peerPool.get(t)},e.prototype.getConnectedPeer=function(t){return this.peerPool.getConnected(t)},e.prototype.getBestPeer=function(t){var e=t.excludePeerId;return this.peerPool.bestOne(e)},e}(A);function de(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var pe=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return de(n=t.apply(this,Xc([],Jc(e),!1))||this,"pid",""),de(n,"streamId",""),de(n,"timer",null),de(n,"config",null),n}return Bc(e,t),e.prototype.destroy=function(){this.timer&&clearInterval(this.timer),this.removeAllListeners(),this.timer=null},e.prototype.configure=function(t){this.config=t},e.prototype.setPid=function(t){return this.pid=t,this},e.prototype.setStreamId=function(t){return this.streamId=t,this},e.prototype.start=function(){var t=this;if(!this.config)throw Error("missing config");!this.timer&&this.config.trackerInterval&&(this.timer=ac((function(){t.request()}),this.config.trackerInterval))},e.prototype.ready=function(){return this.pid&&this.streamId},e.prototype.request=function(){var t,e,r,n=this;if(!this.config)throw Error("missing config");if(this.ready()){var i=ci(t=ci(e=ci(r="".concat(this.config.trackerUrl,"?pid=")).call(r,this.pid,"&channel=")).call(e,this.streamId,"_")).call(t,this.config.trackerVersion,"&streamId=0&mode=bat");fetch(i).then((function(t){return t.text()})).then((function(t){return JSON.parse(t)})).then((function(t){n.parsePeer(t)})).catch((function(t){}))}},e.prototype.parsePeer=function(t){var e,r=this;Hs(e=t.peers).call(e,(function(t){var e=t.pid;r.emit("EVENT_NEW_PEER",{pid:e})}))},e}(e());function fe(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var he=function(){function t(){fe(this,"defaultMaxCandidateSize",100),fe(this,"localPid",""),fe(this,"peers",new hg),fe(this,"tracker",new pe),fe(this,"config",null)}return t.prototype.destroy=function(){this.tracker.destroy(),this.peers.clear(),this.config=null},t.prototype.setStreamId=function(t){return this.tracker.setStreamId(t),this},t.prototype.setPeerId=function(t){return this.tracker.setPid(t),this},t.prototype.start=function(){this.tracker.start()},t.prototype.configure=function(t){this.config=t,this.tracker.configure(this.config)},t.prototype.init=function(){var t=this;if(!this.config)throw Error("miss config");this.tracker.on("EVENT_NEW_PEER",(function(e){var r=e.pid;t.addPid(r)}))},t.prototype.addPid=function(t){var e;if(this.peers.has(t)&&this.peers.delete(t),this.peers.add(t),this.peers.size>((null===(e=this.config)||void 0===e?void 0:e.maxCandidateSize)||this.defaultMaxCandidateSize)){var r=this.getOldestPid();r&&this.peers.delete(r)}},t.prototype.getPid=function(){var t=this.getFreshPid();return t&&this.peers.delete(t),t},t.prototype.getFreshPid=function(){return this.peers.size?Xc([],Jc(this.peers),!1)[this.peers.size-1]:null},t.prototype.getOldestPid=function(){return this.peers.size?Xc([],Jc(this.peers),!1)[0]:null},t}();function me(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var ve=function(){function t(){me(this,"path","signal"),me(this,"peerManager",null),me(this,"peerBuilder",null),me(this,"server",null)}return t.prototype.destroy=function(){this.server&&(this.server.deleteAPI(this.path),this.server=null)},t.prototype.setPeerManager=function(t){return this.peerManager=t,this},t.prototype.setPeerBuilder=function(t){return this.peerBuilder=t,this},t.prototype.setServer=function(t){var e;return this.server=t,this.server.registerAPI(this.path,jr(e=this.handler).call(e,this)),this},t.prototype.handler=function(t){var e;if(this.peerBuilder&&this.peerManager){var r=null===(e=this.peerManager)||void 0===e?void 0:e.getPeer(t.from);"offer"===t.type&&(r||(r=this.peerBuilder.create({targetPid:t.from}),this.peerManager.addConnectingPeer(r))),r&&r.getConnection().emit("EVENT_P2P_SIGNAL_RECV",t)}},t}();function ye(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var ge=function(){function t(){ye(this,"path","SubscribeTrack"),ye(this,"server",null),ye(this,"subscriber",null)}return t.prototype.destroy=function(){var t;null===(t=this.server)||void 0===t||t.deleteAPI(this.path),this.server=null,this.subscriber=null},t.prototype.setServer=function(t){var e;return this.server=t,this.server.registerAPI(this.path,jr(e=this.handler).call(e,this)),this},t.prototype.setSubscriber=function(t){this.subscriber=t},t.prototype.handler=function(t){if(this.subscriber){var e=t.from;"subscribe"===t.payload?this.subscriber.onSubscribe(e):"cancelSubscribe"===t.payload&&this.subscriber.onCancelSubscribe(e)}},t}();function be(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ce,Se=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return be(n=t.apply(this,Xc([],Jc(e),!1))||this,"chunks",[]),be(n,"rtcEncodedStreams",null),be(n,"aborted",!1),be(n,"lastTimestamp",0),be(n,"maxCache",200),n}return Bc(e,t),e.prototype.destroy=function(){this.lastTimestamp=0,this.chunks=[],t.prototype.removeAllListeners.call(this)},e.prototype.configure=function(t){var e=t.max;this.maxCache=e},e.prototype.reset=function(){this.chunks=[]},e.prototype.getHead=function(){return this.chunks[0]},e.prototype.shift=function(){return this.chunks.shift()},e.prototype.removeHead=function(){return this.chunks.shift()},Tf(e.prototype,"size",{get:function(){return this.chunks.length},enumerable:!1,configurable:!0}),e.prototype.cache=function(t){this.lastTimestamp=t.timestamp;var e={type:t.type,timestamp:t.timestamp,data:new Uint8Array(t.data).buffer,chunk:t.chunk};this.chunks.push(e),this.chunks.length>this.maxCache&&this.removeHead(),this.emit("new_chunk",e)},e.prototype.remove=function(t){for(var e;this.chunks.length>t;)this.chunks.shift();var r=this.chunks.length;return r<=t?0:bE(e=this.chunks).call(e,0,r-t)},e.prototype.dropOutdateChunk=function(t){for(var e=this.getHead();e&&e.timestamp<t;)this.removeHead(),e=this.getHead()},e}(t.EventEmitter);function Te(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}!function(t){t[t.key=31]="key",t[t.delta=32]="delta",t[t.empty=33]="empty"}(Ce||(Ce={}));var _e=function(){function t(){}return t.encode=function(t){var e=t.chunkData.byteLength+4+1,r=new Ee({totalBytes:e});return r.setArraybuffer(t.chunkData),r.setUint32(t.timestamp),r.setUnit8(Ce[t.type]),r.getArraybuffer()},t.decode=function(t){var e=new Re(t),r=e.getArraybuffer(t.byteLength-5);return{timestamp:e.getUint32(),type:Ce[e.getUint8()],chunkData:r}},t}(),Re=function(){function t(t){Te(this,"u8a",void 0),Te(this,"dataview",void 0),Te(this,"bytesOffset",0),Te(this,"totalBytes",0),this.u8a=new Uint8Array(t),this.dataview=new DataView(t),this.totalBytes=t.byteLength}return t.prototype.getUint8=function(){var t=this.dataview.getUint8(this.bytesOffset);return this.bytesOffset+=1,t},t.prototype.getUint32=function(){var t=this.dataview.getUint32(this.bytesOffset);return this.bytesOffset+=4,t},t.prototype.getArraybuffer=function(t,e){var r;void 0===e&&(e=0);var n=hf(r=this.u8a).call(r,e,t);return this.bytesOffset+=e+t,n.buffer},t}(),Ee=function(){function t(t){var e=t.totalBytes;Te(this,"totalBytes",0),Te(this,"u8a",void 0),Te(this,"dataview",void 0),Te(this,"bytesOffset",0),this.totalBytes=e,this.u8a=new Uint8Array(e),this.dataview=new DataView(this.u8a.buffer)}return t.prototype.setUnit8=function(t){this.dataview.setUint8(this.bytesOffset,t),this.bytesOffset+=1},t.prototype.setUint32=function(t){this.dataview.setUint32(this.bytesOffset,t),this.bytesOffset+=4},t.prototype.setArraybuffer=function(t){this.u8a.set(new Uint8Array(t),this.bytesOffset),this.bytesOffset+=t.byteLength},t.prototype.getArraybuffer=function(){return this.u8a.buffer},t}();function we(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Pe=function(){function t(){we(this,"videoTrack",new MediaStreamTrackGenerator({kind:"video"})),we(this,"audioTrack",new MediaStreamTrackGenerator({kind:"audio"})),we(this,"videoWriter",this.videoTrack.writable.getWriter()),we(this,"audioWriter",this.audioTrack.writable.getWriter()),we(this,"timer",null)}return t.prototype.destroy=function(){this.timer&&(clearInterval(this.timer),this.timer=null)},t.prototype.getVideoTrack=function(){return this.videoTrack},t.prototype.getAudioTrack=function(){return this.audioTrack},t.prototype.writeAudioDataToTrack=function(){var t=this.createAudioData();this.audioWriter.write(t).catch((function(t){}))},t.prototype.writeVideoFrameToTrack=function(t){var e=t.timestamp,r=this.createVideoFrame({timestamp:e});return this.videoWriter.write(r).catch((function(t){})),r},t.prototype.createAudioData=function(){var t={timestamp:Yb(),data:new ArrayBuffer(10),sampleRate:48e3,format:"u8",numberOfFrames:1,numberOfChannels:1};return new AudioData(t)},t.prototype.createVideoFrame=function(t){for(var e={format:"I420",codedHeight:16,codedWidth:16,colorSpace:{matrix:"bt709",primaries:"bt709",transfer:"bt709"},displayHeight:16,displayWidth:16,timestamp:t.timestamp},r=new Uint8Array(e.codedWidth*e.codedHeight*4),n=0;n<e.codedWidth;n++)for(var i=0;i<e.codedHeight;i++){var o=4*(i*e.codedWidth+n);r[o]=127,r[o+1]=255,r[o+2]=212,r[o+3]=255}return new VideoFrame(r,e)},t}();function ke(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ae=function(t,e){var r,n,i,o=null===(i=RTCRtpReceiver.getCapabilities(e))||void 0===i?void 0:i.codecs,a=[];if(!o)return a;try{for(var s=Hc(o),c=s.next();!c.done;c=s.next()){var u=c.value;u.mimeType===t&&a.push(u)}}catch(t){r={error:t}}finally{try{c&&!c.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}return a},Ie=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return ke(n=t.apply(this,Xc([],Jc(e),!1))||this,"tagName","StreamTrackProvider"),ke(n,"targetPid",""),ke(n,"srcPid",""),ke(n,"peerManager",null),ke(n,"driverTrack",new Pe),ke(n,"videoEncodedFrameCache",null),ke(n,"audioEncodedFrameCache",null),ke(n,"statUnit",null),ke(n,"reportUnit",new R),ke(n,"audioAbortController",new AbortController),ke(n,"videoAbortController",new AbortController),ke(n,"newVideoChunkHandler",null),ke(n,"videoEmitChunkTimer",null),n}return Bc(e,t),e.prototype.destroy=function(){var e;this.clearTimer(),this.onCancelSubscribe(),this.driverTrack.destroy(),this.peerManager=null,this.audioAbortController.abort("StreamTrackProvider_abort_audio"),this.videoAbortController.abort("StreamTrackProvider_abort_video"),this.newVideoChunkHandler&&(null===(e=this.videoEncodedFrameCache)||void 0===e||e.off("new_chunk",this.newVideoChunkHandler)),this.videoEncodedFrameCache=null,this.audioEncodedFrameCache=null,t.prototype.destroy.call(this)},e.prototype.setStatUnit=function(t){return this.statUnit=t,this},e.prototype.setPeerManager=function(t){return this.peerManager=t,this},e.prototype.setVideoEncodedFrameCache=function(t){return this.videoEncodedFrameCache=t,this},e.prototype.setAudioEncodedFrameCache=function(t){return this.audioEncodedFrameCache=t,this},e.prototype.setTargetPid=function(t){return this.targetPid=t,this},e.prototype.setSrcPid=function(t){return this.srcPid=t,this},e.prototype.setReporter=function(e){return null==e||e.registerReportUint(this.objName,this.reportUnit),t.prototype.setReporter.call(this,e)},e.prototype.onSubscribe=function(){var t,e,r=null===(t=this.peerManager)||void 0===t?void 0:t.getPeer(this.targetPid),n=null===(e=this.peerManager)||void 0===e?void 0:e.getPeer(this.srcPid);if(!r)return!1;if(!n)return!1;var i=r.getConnection().getRTCPeerConnection(),o=n.getConnection().getRTCPeerConnection();return"closed"!==i.signalingState&&(this.listenConnection(r.getConnection()),this.provideVideoTrack(o,i),this.provideAudioTrack(o,i),!0)},e.prototype.onCancelSubscribe=function(){var t,e=null===(t=this.peerManager)||void 0===t?void 0:t.getPeer(this.targetPid);if(!e)return!1;e.getConnection().destroy()},e.prototype.updateTrack=function(t,e){var r,n,i;this.srcPid=e;var o=null===(i=this.peerManager)||void 0===i?void 0:i.getPeer(this.targetPid);if(!o)return!1;if("video"!==t.kind){var a=o.getConnection().getRTCPeerConnection().getSenders();try{for(var s=Hc(a),c=s.next();!c.done;c=s.next()){var u,l=c.value;if(t.kind===(null===(u=l.track)||void 0===u?void 0:u.kind)){l.replaceTrack(t);break}}}catch(t){r={error:t}}finally{try{c&&!c.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}return!0}},e.prototype.provideAudioTrack=function(t,e){var r,n,i,o,a,s,c=this,u=t.getReceivers();try{for(var l=Hc(u),d=l.next();!d.done;d=l.next()){var p=d.value;if("audio"===p.track.kind){e.addTrack(p.track);break}}}catch(t){r={error:t}}finally{try{d&&!d.done&&(n=l.return)&&n.call(l)}finally{if(r)throw r.error}}var f=e.getTransceivers();try{for(var h=Hc(f),m=h.next();!m.done;m=h.next()){var v,y=m.value;if("audio"===(null===(v=y.sender.track)||void 0===v?void 0:v.kind)){y.setCodecPreferences(Ae("audio/opus","audio"));break}}}catch(t){i={error:t}}finally{try{m&&!m.done&&(o=h.return)&&o.call(h)}finally{if(i)throw i.error}}var g,b=e.getSenders(),C=function(t){if("audio"!==(null===(g=t.track)||void 0===g?void 0:g.kind))return"continue";var e=!1,r=t.createEncodedStreams();return r.readable.pipeThrough(new TransformStream({transform:function(t,r){if(c.audioEncodedFrameCache){if(!e){var n=1;c.audioEncodedFrameCache.remove(n),e=!0}var i=c.audioEncodedFrameCache.shift();i&&(t.data=_e.encode({chunkData:i.data,timestamp:i.timestamp,type:"key"}),r.enqueue(t))}}})).pipeThrough(new TransformStream({transform:function(t,e){var r,n;null===(r=c.statUnit)||void 0===r||r.number("uploadBytes").plus(t.data.byteLength),null===(n=c.statUnit)||void 0===n||n.number("uploadAuidoBytes").plus(t.data.byteLength),c.reportUnit.number(U.UPLOAD_BYTES).plus(t.data.byteLength),e.enqueue(t)}})).pipeTo(r.writable,{signal:S.audioAbortController.signal}).catch((function(t){})),"break"},S=this;try{for(var T=Hc(b),_=T.next();!_.done&&"break"!==C(_.value);_=T.next());}catch(t){a={error:t}}finally{try{_&&!_.done&&(s=T.return)&&s.call(T)}finally{if(a)throw a.error}}},e.prototype.provideVideoTrack=function(t,e){var r,n,i,o,a,s=this,c=this.driverTrack.getVideoTrack();e.addTrack(c),this.newVideoChunkHandler=function(){s.driverTrack.writeVideoFrameToTrack({timestamp:0})},null===(a=this.videoEncodedFrameCache)||void 0===a||a.on("new_chunk",this.newVideoChunkHandler),this.clearTimer(),this.videoEmitChunkTimer=ac((function(){s.videoEncodedFrameCache&&s.videoEncodedFrameCache.size>1&&s.driverTrack.writeVideoFrameToTrack({timestamp:0}),s.driverTrack.writeVideoFrameToTrack({timestamp:0})}),30);var u=e.getTransceivers();try{for(var l=Hc(u),d=l.next();!d.done;d=l.next()){var p,f=d.value;if("video"===(null===(p=f.sender.track)||void 0===p?void 0:p.kind)){f.setCodecPreferences(Ae("video/H264","video"));break}}}catch(t){r={error:t}}finally{try{d&&!d.done&&(n=l.return)&&n.call(l)}finally{if(r)throw r.error}}var h,m=e.getSenders(),v=function(t){if("video"!==(null===(h=t.track)||void 0===h?void 0:h.kind))return"continue";var e=t.createEncodedStreams(),r=!1;return e.readable.pipeThrough(new TransformStream({transform:function(t,e){if(!s.videoEncodedFrameCache)throw Error("provideVideoTrack missing param");if(!r){var n=1;s.videoEncodedFrameCache.remove(n),r=!0}var i=s.videoEncodedFrameCache.shift();i&&(t.data=_e.encode({chunkData:i.data,timestamp:i.timestamp,type:i.type}),e.enqueue(t))}})).pipeThrough(new TransformStream({transform:function(t,e){var r,n;null===(r=s.statUnit)||void 0===r||r.number("uploadBytes").plus(t.data.byteLength),null===(n=s.statUnit)||void 0===n||n.number("uploadVideoBytes").plus(t.data.byteLength),s.reportUnit.number(U.UPLOAD_BYTES).plus(t.data.byteLength),e.enqueue(t)}})).pipeTo(e.writable,{signal:y.videoAbortController.signal}).catch((function(t){})),"break"},y=this;try{for(var g=Hc(m),b=g.next();!b.done&&"break"!==v(b.value);b=g.next());}catch(t){i={error:t}}finally{try{b&&!b.done&&(o=g.return)&&o.call(g)}finally{if(i)throw i.error}}},e.prototype.listenConnection=function(t){var e=this;t.on("disconnected",(function(){e.emit("EVENT_RPOVIDER_DISCONNECT")})),t.on("failed",(function(){e.emit("EVENT_RPOVIDER_FAILED")}))},e.prototype.clearTimer=function(){this.videoEmitChunkTimer&&(clearInterval(this.videoEmitChunkTimer),this.videoEmitChunkTimer=null)},e}(A);function xe(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Le=function(){function t(){xe(this,"config",null),xe(this,"coolingPeers",new hg)}return t.prototype.configure=function(t){this.config=t},t.prototype.destroy=function(){this.coolingPeers.clear()},t.prototype.add=function(t){var e,r=this;if(!this.config)throw Error("PeerCooler missing param");this.coolingPeers.add(t),sc((function(){r.coolingPeers.delete(t)}),null===(e=this.config)||void 0===e?void 0:e.coolingTime)},t.prototype.getList=function(){return Xc([],Jc(this.coolingPeers),!1)},t}();function De(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ne,Oe=function(){function t(){De(this,"lastWriteAudioTimestamp",0),De(this,"lastWriteVideoTimestamp",0),De(this,"p2pVideoRecvEnough",!1),De(this,"_prepareToStopCdn",!1),De(this,"_cdnStoppingReady",!1),De(this,"_lastestCdnKeyTimestamp",0),De(this,"_lastRecvCdnVideoTimestamp",0),De(this,"_lastRecvCdnAudioTimestamp",0)}return Tf(t.prototype,"lastRecvCdnVideoTimestamp",{get:function(){return this._lastRecvCdnVideoTimestamp},set:function(t){this._lastRecvCdnVideoTimestamp=t},enumerable:!1,configurable:!0}),Tf(t.prototype,"lastRecvCdnAudioTimestamp",{get:function(){return this._lastRecvCdnAudioTimestamp},set:function(t){this._lastRecvCdnAudioTimestamp=t},enumerable:!1,configurable:!0}),Tf(t.prototype,"prepareToStopCdn",{get:function(){return this._prepareToStopCdn},set:function(t){this._prepareToStopCdn=t},enumerable:!1,configurable:!0}),Tf(t.prototype,"cdnStoppingReady",{get:function(){return this._cdnStoppingReady},set:function(t){this._cdnStoppingReady=t},enumerable:!1,configurable:!0}),Tf(t.prototype,"lastestCdnKeyTimestamp",{get:function(){return this._lastestCdnKeyTimestamp},set:function(t){this._lastestCdnKeyTimestamp=t},enumerable:!1,configurable:!0}),t.prototype.reset=function(){this._prepareToStopCdn=!1,this._cdnStoppingReady=!1,this._lastestCdnKeyTimestamp=0,this._lastRecvCdnVideoTimestamp=0,this._lastRecvCdnAudioTimestamp=0,this.lastWriteAudioTimestamp=0,this.lastWriteVideoTimestamp=0,this.p2pVideoRecvEnough=!1},t}();function Me(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}!function(t){t[t.smooth=0]="smooth",t[t.recentAvg=1]="recentAvg",t[t.latestOne=2]="latestOne"}(Ne||(Ne={}));var Ue=function(){function t(){Me(this,"cnt",0),Me(this,"frameRateRecords",[]),Me(this,"config",{maxFrameRateRecordsLen:10}),Me(this,"timer",null)}return t.prototype.configure=function(t){this.config=t},t.prototype.destroy=function(){this.clearTimer()},t.prototype.reset=function(){this.frameRateRecords=[],this.clearTimer()},t.prototype.getFrameRate=function(t){return this.frameRateRecords.length?t===Ne.smooth?this.smooth(this.frameRateRecords):t===Ne.recentAvg?this.recentAvg(this.frameRateRecords):this.latestOne(this.frameRateRecords):null},t.prototype.sample=function(){this.start(),this.cnt+=1},t.prototype.start=function(){this.setTimer()},t.prototype.stop=function(){this.clearTimer()},t.prototype.setTimer=function(){var t=this;this.timer||(this.timer=ac((function(){t.frameRateRecords.push(t.cnt),t.frameRateRecords.length>t.config.maxFrameRateRecordsLen&&t.frameRateRecords.shift(),t.cnt=0}),1e3))},t.prototype.clearTimer=function(){this.timer&&(clearInterval(this.timer),this.timer=null)},t.prototype.smooth=function(t){if(t.length<5)return null;var e=hf(t).call(t,3);return Math.floor(sC(e).call(e,(function(t,e){return t+e}))/e.length)},t.prototype.recentAvg=function(t){if(t.length<3)return null;var e=hf(t).call(t,t.length-3);return Math.floor(sC(e).call(e,(function(t,e){return t+e}))/e.length)},t.prototype.latestOne=function(t){return t.length?t[this.frameRateRecords.length-1]:null},t}();function Ve(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Fe=function(){function t(){Ve(this,"audioClockRate",0),Ve(this,"videoClockRate",0),Ve(this,"videoMimeType",""),Ve(this,"audioMimeType",""),Ve(this,"frameRate",new Ue)}return t.prototype.reset=function(){this.audioClockRate=0,this.videoClockRate=0},t.prototype.ready=function(){return this.audioClockRate&&this.videoClockRate},t.prototype.sample=function(t){var e,r=this;Hs(e=t.getReceivers()).call(e,(function(t){r.sampleClockRate(t)}))},t.prototype.sampleClockRate=function(t){var e=this;t.getStats().then((function(t){var r,n;try{for(var i=Hc(t),o=i.next();!o.done;o=i.next()){var a,s,c=o.value,u=kE(c).call(c,(function(t){return"codec"===t.type}));u&&"number"==typeof u.clockRate&&(Dc(a=u.mimeType).call(a,"audio")?(e.audioClockRate=u.clockRate,e.audioMimeType=u.mimeType):Dc(s=u.mimeType).call(s,"video")&&(e.videoClockRate=u.clockRate,e.videoMimeType=u.mimeType))}}catch(t){r={error:t}}finally{try{o&&!o.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}}))},t}();function je(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Be=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return je(n=t.apply(this,Xc([],Jc(e),!1))||this,"tagName","LEBSource"),je(n,"sourceConfig",null),je(n,"stopWriting",!1),je(n,"videoAbortController",new AbortController),je(n,"audioAbortController",new AbortController),n}return Bc(e,t),e.prototype.configure=function(t){var e=this;return this.sourceConfig=t,this.reportUnit.configure({deleteZeroNumber:!0,fieldsReportedWithZeroNumber:[U.CDN_BYTES],fieldsReservedValueAfterReport:[]}),this.reportUnit.registerPreHook(this.objName,(function(){if(e.syncController){var t=e.syncController,r=t.lastRecvCdnVideoTimestamp,n=t.lastRecvCdnAudioTimestamp;e.reportUnit.number(U.CDN_AV_DIFF).set(r-n)}})),this},e.prototype.isLeb=function(){return!0},e.prototype.load=function(){if(!this.sourceConfig)throw new Error;if(!this.peerManager)throw new Error("miss peerManager");t.prototype.load.call(this);var e=(new re).create(Ih({targetPid:this.sourceConfig.srcPid},this.sourceConfig));this.peer=e,this.peerManager.addPeer(e),this.listenConnection(e.getConnection())},e.prototype.close=function(){this.closed||(this.audioAbortController.abort("LEBSource_abort_audio"),this.videoAbortController.abort("LEBSource_abort_video"),t.prototype.close.call(this))},e.prototype.onTrack=function(t,e){var r=this;if(!this.videoEncodedFrameCache||!this.audioEncodedFrameCache)throw Error("miss cache");var n=t.track,o=t.receiver.createEncodedStreams();if(o){if(!this.syncController)throw Error("missing param");if(!this.streamInfo)throw Error("missing param");var s=o.readable.pipeThrough(new TransformStream({transform:function(e,n){if(!r.closed){if(!r.syncController)throw Error("missing param");if(!r.stopWriting){var o,s,c;if(!r.stopWriting&&r.syncController.prepareToStopCdn&&"video"===t.track.kind&&"key"===e.type)return r.syncController.cdnStoppingReady=!0,r.syncController.lastestCdnKeyTimestamp=e.timestamp,r.stopWriting=!0,void(null===(o=r.streamInfo)||void 0===o||o.frameRate.stop());r.syncController&&("audio"===t.track.kind?(null!==(s=r.syncController)&&void 0!==s&&s.lastRecvCdnAudioTimestamp||r.emit(i,{code:a.INF_PLAY_EVT_STREAM_FIRST_AUDIO_FRAME,msg:"收到了第一个音频帧"}),r.syncController.lastRecvCdnAudioTimestamp=e.timestamp):(null!==(c=r.syncController)&&void 0!==c&&c.lastRecvCdnVideoTimestamp||r.emit(i,{code:a.INF_PLAY_EVT_STREAM_FIRST_VIDEO_FRAME,msg:"收到了第一个视频帧"}),r.syncController.lastRecvCdnVideoTimestamp=e.timestamp)),n.enqueue(e)}}}})).pipeThrough(new TransformStream({transform:function(t,e){var n,i;null===(n=r.statUnit)||void 0===n||n.number("cdnDownloadBytes").plus(t.data.byteLength),null===(i=r.reportUnit)||void 0===i||i.number(U.CDN_BYTES).plus(t.data.byteLength),e.enqueue(t)}}));"video"===n.kind?s.pipeThrough(new TransformStream({transform:function(t,e){var n;null===(n=r.statUnit)||void 0===n||n.number("cdnDownloadVideoBytes").plus(t.data.byteLength),e.enqueue(t)}})).pipeThrough(new TransformStream({transform:function(t,e){var n,i;null===(n=r.streamInfo)||void 0===n||n.frameRate.sample(),null===(i=r.videoEncodedFrameCache)||void 0===i||i.cache(t),e.enqueue(t)}})).pipeTo(o.writable,{signal:this.videoAbortController.signal}).catch((function(t){})):s.pipeThrough(new TransformStream({transform:function(t,e){var n;null===(n=r.statUnit)||void 0===n||n.number("cdnDownloadAuidoBytes").plus(t.data.byteLength),e.enqueue(t)}})).pipeThrough(new TransformStream({transform:function(t,e){var n;null===(n=r.audioEncodedFrameCache)||void 0===n||n.cache(t),e.enqueue(t)}})).pipeTo(o.writable,{signal:this.audioAbortController.signal}).catch((function(t){})),this.emitTrack(n,e)}},e}(mt);function We(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ge=function(){function t(){We(this,"targetWritableStream",null),We(this,"targetWriter",null),We(this,"proxiedWritableStream",null),We(this,"getNextChunkCb",null)}return t.prototype.destroy=function(){var t;this.targetWritableStream=null,null===(t=this.targetWriter)||void 0===t||t.abort(),this.targetWriter=null,this.proxiedWritableStream=null,this.getNextChunkCb=null},t.prototype.setTargetWriableStream=function(t){return this.targetWritableStream=t,this.targetWriter=t.getWriter(),this},t.prototype.setGetNextChunkCb=function(t){this.getNextChunkCb=t},t.prototype.getStream=function(){var t=this;if(this.proxiedWritableStream)return this.proxiedWritableStream;var e=new CountQueuingStrategy({highWaterMark:10});return this.proxiedWritableStream=new WritableStream({write:function(e){t.writeChunk()},close:function(){},abort:function(t){}},e),this.proxiedWritableStream},t.prototype.write=function(){this.writeChunk()},t.prototype.writeChunk=function(){if(!this.getNextChunkCb)throw Error("missing param");for(var t=this.getNextChunkCb();t;){var e;null===(e=this.targetWriter)||void 0===e||e.write(t).catch((function(t){})),t=this.getNextChunkCb()}},t}();function Ze(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ye,He=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return Ze(n=t.apply(this,Xc([],Jc(e),!1))||this,"tagName","P2PSource"),Ze(n,"sourceConfig",null),Ze(n,"p2pClient",null),Ze(n,"videoReady",!1),Ze(n,"p2pAudioCache",new Se),Ze(n,"p2pVideoCache",new Se),Ze(n,"audioProxyStream",new Ge),Ze(n,"videoProxyStream",new Ge),Ze(n,"recvFrameRate",new Ue),Ze(n,"audioAbortController",new AbortController),Ze(n,"videoAbortController",new AbortController),n}return Bc(e,t),e.prototype.configure=function(t){var e=this;return this.sourceConfig=t,this.reportUnit.configure({deleteZeroNumber:!0,fieldsReportedWithZeroNumber:[U.P2P_BYTES,U.P2P_AV_DIFF],fieldsReservedValueAfterReport:[]}),this.reportUnit.registerPreHook(this.objName,(function(){if(e.syncController){var t=e.syncController,r=t.lastWriteAudioTimestamp,n=t.lastWriteVideoTimestamp;e.reportUnit.number(U.P2P_AV_DIFF).set(n-r)}})),this},e.prototype.setClient=function(t){return this.p2pClient=t,this},e.prototype.setPeerManager=function(t){return this.peerManager=t,this},e.prototype.destroy=function(){this.p2pAudioCache.destroy(),this.p2pVideoCache.destroy(),this.audioProxyStream.destroy(),this.videoProxyStream.destroy(),this.audioAbortController.abort("P2PSource_abort_audio"),this.videoAbortController.abort("P2PSource_abort_video"),t.prototype.destroy.call(this)},e.prototype.isLeb=function(){return!1},e.prototype.load=function(){if(!this.sourceConfig)throw new Error("missing p2p source config");if(this.peerManager&&this.p2pClient){var e=this.peerManager.getPeer(this.sourceConfig.srcPid);if(e){this.peer=e;var r=e.getConnection();r&&(t.prototype.load.call(this),this.p2pClient.request({host:"P2PServer",to:this.sourceConfig.srcPid,path:"SubscribeTrack",payload:"subscribe",type:"app"}),this.listenConnection(r))}}},e.prototype.close=function(){if(!this.sourceConfig)throw new Error("missing p2p source config");this.p2pClient&&(this.closed||(this.p2pClient.request({host:"P2PServer",to:this.sourceConfig.srcPid,path:"SubscribeTrack",payload:"cancelSubscribe",type:"app"}),t.prototype.close.call(this)))},e.prototype.onTrack=function(t,e){var r=t.track;"video"===r.kind?this.onVideoTrack(t,e):"audio"===r.kind&&this.onAudioTrack(t,e)},e.prototype.onAudioTrack=function(t,e){var r=this,n=t.receiver.createEncodedStreams();this.audioProxyStream.setTargetWriableStream(n.writable).setGetNextChunkCb((function(){var t;if(r.syncController&&r.p2pAudioCache&&r.videoReady&&r.streamInfo&&r.streamInfo.ready()&&null!==(t=r.syncController)&&void 0!==t&&t.cdnStoppingReady&&r.syncController.p2pVideoRecvEnough){var e=r.p2pAudioCache.getHead();return e?(r.p2pAudioCache.removeHead(),r.syncController.lastWriteAudioTimestamp=e.timestamp,r.videoProxyStream.write(),e.chunk):void 0}})),n.readable.pipeThrough(new TransformStream({transform:function(t,e){r.closed||e.enqueue(t)}})).pipeThrough(new TransformStream({transform:function(t,e){var n,i,o;null===(n=r.reportUnit)||void 0===n||n.number(U.P2P_BYTES).plus(t.data.byteLength),null===(i=r.statUnit)||void 0===i||i.number("p2pDownloadBytes").plus(t.data.byteLength),null===(o=r.statUnit)||void 0===o||o.number("p2pDownloadAuidoBytes").plus(t.data.byteLength),e.enqueue(t)}})).pipeThrough(new TransformStream({transform:function(t,e){if(0!==t.data.byteLength){if(r.syncController){var n=_e.decode(t.data);n.timestamp<r.syncController.lastRecvCdnAudioTimestamp||(t.data=n.chunkData,r.p2pAudioCache.cache({data:n.chunkData,timestamp:n.timestamp,type:n.type,chunk:t}),e.enqueue({}))}}else e.enqueue(t)}})).pipeTo(this.audioProxyStream.getStream(),{signal:this.audioAbortController.signal}).catch((function(t){})),this.emitTrack(t.track,e)},e.prototype.onVideoTrack=function(t,e){var r=this,n=t.receiver,i=1,o=0,a=n.createEncodedStreams();this.videoProxyStream.setTargetWriableStream(a.writable).setGetNextChunkCb((function(){if(r.syncController&&r.p2pVideoCache&&r.streamInfo&&r.streamInfo.ready()&&(r.p2pVideoCache.size>5&&!r.syncController.p2pVideoRecvEnough&&(r.syncController.p2pVideoRecvEnough=!0),r.syncController.lastWriteAudioTimestamp)){var t=r.p2pVideoCache.getHead();if(t){var e=r.syncController,n=e.lastRecvCdnVideoTimestamp,i=e.lastRecvCdnAudioTimestamp,o=r.streamInfo,a=o.videoClockRate,s=o.audioClockRate,c=i/s-n/a;return r.syncController.lastWriteAudioTimestamp/s-c<t.timestamp/a+.1?void 0:(r.p2pVideoCache.removeHead(),t.chunk)}}})),a.readable.pipeThrough(new TransformStream({transform:function(t,e){r.closed||e.enqueue(t)}})).pipeThrough(new TransformStream({transform:function(t,e){if(!r.streamInfo||!r.sourceConfig)throw Error("missing param");r.recvFrameRate.sample();var n=r.recvFrameRate.getFrameRate(Ne.recentAvg),i=r.streamInfo.frameRate.getFrameRate(Ne.smooth);null!==n&&null!==i&&n<i*r.sourceConfig.stuckFrameRateRatio&&2===(o+=1)?r.emit(ht):e.enqueue(t)}})).pipeThrough(new TransformStream({transform:function(t,e){var n,i,o;null===(n=r.reportUnit)||void 0===n||n.number(U.P2P_BYTES).plus(t.data.byteLength),null===(i=r.statUnit)||void 0===i||i.number("p2pDownloadBytes").plus(t.data.byteLength),null===(o=r.statUnit)||void 0===o||o.number("p2pDownloadVideoBytes").plus(t.data.byteLength),e.enqueue(t)}})).pipeThrough(new TransformStream({transform:function(n,o){if(r.syncController){if(n.data.byteLength){var a=_e.decode(n.data);if("key"===a.type&&(i-=1),n.data=a.chunkData,r.syncController.prepareToStopCdn||"key"!==a.type||0!==i||(r.syncController.prepareToStopCdn=!0),!r.syncController.cdnStoppingReady)return;if(a.timestamp<r.syncController.lastestCdnKeyTimestamp)return;a.timestamp,r.syncController.lastestCdnKeyTimestamp,r.videoReady||(r.videoReady=!0,r.emitTrack(t.track,e)),r.p2pVideoCache.cache({data:a.chunkData,timestamp:a.timestamp,type:a.type,chunk:n})}o.enqueue(n)}}})).pipeTo(this.videoProxyStream.getStream(),{signal:this.videoAbortController.signal}).catch((function(t){}))},e}(mt);!function(t){t[t.LEB=0]="LEB",t[t.P2P=1]="P2P"}(Ye||(Ye={}));var Je,Xe=function(){function t(){}return t.create=function(t){switch(t){case Ye.LEB:return new Be;case Ye.P2P:return new He}},t}();function Ke(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}!function(t){t[t.IDEL=0]="IDEL",t[t.WAITING=1]="WAITING",t[t.COMPLETE=2]="COMPLETE"}(Je||(Je={}));var qe=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=this;return Ke(n=t.apply(this,Xc([],Jc(e),!1))||this,"tagName","Subscriber"),Ke(n,"parent",{pid:""}),Ke(n,"child",{pid:""}),Ke(n,"subscribeStatus",{status:Je.IDEL,pid:""}),Ke(n,"config",null),Ke(n,"p2pClient",null),Ke(n,"peerManager",null),Ke(n,"videoEncodedFrameCache",new Se),Ke(n,"audioEncodedFrameCache",new Se),Ke(n,"statUnit",null),Ke(n,"peerCooler",new Le),Ke(n,"syncController",new Oe),Ke(n,"streamInfo",new Fe),Ke(n,"sampleTimer",null),Ke(n,"reportUnit",new R),Ke(n,"lebSource",null),Ke(n,"p2pSource",null),n}return Bc(e,t),e.prototype.destroy=function(){this.peerManager=null,this.reporter=null,this.p2pClient=null,this.peerCooler.destroy(),this.cancelsubscribe(),this.removeChild(this.child.pid),this.clearSampleTimer(),this.closeSource(Ye.LEB),this.closeSource(Ye.P2P),t.prototype.destroy.call(this)},e.prototype.configure=function(t){return this.config=t,this.peerCooler.configure({coolingTime:this.config.subscribeCoolingTime}),this.reportUnit.configure({deleteZeroNumber:!0,fieldsReservedValueAfterReport:[],fieldsReportedWithZeroNumber:[]}),this},e.prototype.setPeerManager=function(t){return this.peerManager=t,this},e.prototype.setReporter=function(e){var r=this;return null==e||e.registerReportUint(this.objName,this.reportUnit),this.reportUnit.registerPreHook(this.objName,(function(){var t=r.streamInfo.frameRate.getFrameRate(Ne.recentAvg);null!==t&&r.reportUnit.number(U.FRAME_RATE).set(t);var e=r.streamInfo,n=e.videoMimeType,i=e.videoClockRate,o=e.audioClockRate,a=e.audioMimeType;r.reportUnit.number(U.AUDIO_CLOCK_RATE).set(o),r.reportUnit.number(U.VIDEO_CLOCK_RATE).set(i),r.reportUnit.string(U.AUDIO_CODEC).set(a),r.reportUnit.string(U.VIDEO_CODEC).set(n)})),t.prototype.setReporter.call(this,e)},e.prototype.setClient=function(t){return this.p2pClient=t,this},e.prototype.setStatUnit=function(t){this.statUnit=t},Tf(e.prototype,"currentSourcePeerconnection",{get:function(){var t,e;return this.parent.pid&&(null===(t=this.peerManager)||void 0===t||null===(e=t.getPeer(this.parent.pid))||void 0===e?void 0:e.getConnection().getRTCPeerConnection())||null},enumerable:!1,configurable:!0}),e.prototype.subscribeLEB=function(t){var e=this;if(!this.peerManager||!this.statUnit)throw Error("subscribeLEB missing param");this.syncController.reset(),this.streamInfo.reset(),this.streamInfo.frameRate.reset(),this.reportUnit.number(U.PULL_CDN_STREAM).plus(1);var r=Xe.create(Ye.LEB);return this.lebSource=r,r.configure(t).setSyncController(this.syncController).setReporter(this.reporter).setPeerManager(this.peerManager).setEncodedVideoChunkCache(this.videoEncodedFrameCache).setEncodedAudioChunkCache(this.audioEncodedFrameCache).setStatUnit(this.statUnit).setStreamInfo(this.streamInfo).on(lt,(function(t){"video"===t.track.kind&&(e.reportUnit.number(U.PULL_CDN_SUCC).plus(1),e.updateParent({pid:t.srcPid,source:r}),e.updateChildTrack(t.track,{srcPid:t.srcPid}),e.sampleTimer||(e.sampleTimer=ac((function(){e.streamInfo.ready()?e.clearSampleTimer():e.streamInfo.sample(t.rtcPeerConnction)}),1e3)))})).once(ct,(function(){e.reportUnit.number(U.PULL_CDN_TIMEOUT).plus(1),e.closeSource(Ye.LEB)})).once(ft,(function(t){t.srcPid,e.reportUnit.number(U.PULL_CDN_FAILED).plus(1),e.closeSource(Ye.LEB)})).once(ut,(function(t){t.srcPid,e.closeSource(Ye.LEB)})).once(pt,(function(t){t.srcPid,e.reportUnit.number(U.PULL_CDN_ERROR).plus(1),e.closeSource(Ye.LEB)})).load(),r},e.prototype.subscribeP2P=function(t){var e=this;if(!(this.p2pClient&&this.peerManager&&this.statUnit&&this.config))throw Error("[Subscriber] [subscribeP2P] missing param");if(!0===this.allowSubscribe()){var r=t||this.selectAvaliableSubscribePeer();if(r){var n=Xe.create(Ye.P2P);return this.p2pSource=n,this.parent.pid&&this.parent.source&&this.parent.source.setSyncController(this.syncController),this.reportUnit.number(U.SUBSCRIBE_CNT).plus(1),n.configure({srcPid:r,stuckFrameRateRatio:this.config.stuckFrameRateRatio}).setReporter(this.reporter).setClient(this.p2pClient).setPeerManager(this.peerManager).setStatUnit(this.statUnit).setSyncController(this.syncController).setStreamInfo(this.streamInfo).on(lt,(function(t){"video"===t.track.kind&&e.updateParent({pid:t.srcPid,source:n})})).once(ct,(function(){e.reportUnit.number(U.SUBSCRIBE_TIMEOUT_CNT).plus(1),e.peerCooler.add(r),e.closeSource(Ye.P2P),e.unmarkSubscribeP2P()})).once(ft,(function(t){t.srcPid,e.reportUnit.number(U.SUBSCRIBE_FAIL_CNT).plus(1),e.closeSource(Ye.P2P),e.unmarkSubscribeP2P()})).once(ut,(function(){e.closeSource(Ye.P2P),e.unmarkSubscribeP2P()})).once(pt,(function(){e.closeSource(Ye.P2P),e.unmarkSubscribeP2P()})).once(ht,(function(){e.reportUnit.number(U.SUBSCRIBE_P2P_STUCK).plus(1),e.closeSource(Ye.P2P),e.unmarkSubscribeP2P()})).on(lt,(function(t){"video"===t.track.kind&&(e.reportUnit.number(U.SUBSCRIBE_SUCCESS_CNT).plus(1),e.subscribeStatus.status=Je.COMPLETE)})).load(),this.markSubscribeP2P(),n}}},e.prototype.cancelsubscribe=function(){this.parent.pid&&this.parent.source&&(this.parent.source.close(),this.parent.pid="",this.parent.source=void 0)},e.prototype.onSubscribe=function(t){var e=this;if(this.reportUnit.number(U.RECV_SUBSCRIBE).plus(1),!this.peerManager||!this.statUnit)throw Error("[Subscriber] [onSubscribe] missing param");if(!0===this.allowToBeSubscribed()){var r=new Ie,n=r.setSrcPid(this.parent.pid).setTargetPid(t).setReporter(this.reporter).setPeerManager(this.peerManager).setVideoEncodedFrameCache(this.videoEncodedFrameCache).setAudioEncodedFrameCache(this.audioEncodedFrameCache).setStatUnit(this.statUnit).onSubscribe();r.on("EVENT_RPOVIDER_DISCONNECT",(function(){e.removeChild(t)})).on("EVENT_RPOVIDER_FAILED",(function(){e.removeChild(t)})),n?(this.setChild(t,r),this.reportUnit.number(U.RECV_SUBSCRIBE_SUCC).plus(1)):r.destroy()}},e.prototype.onCancelSubscribe=function(t){this.reportUnit.number(U.RECV_SUBSCRIBE_CANCEL).plus(1),this.removeChild(t)},e.prototype.getParentId=function(){return this.parent.pid},e.prototype.getChildId=function(){return this.child.pid},e.prototype.allowToBeSubscribed=function(){var t;return this.parent.pid&&this.parent.source?""!==this.child.pid?"有子节点":!!(null===(t=this.parent.source)||void 0===t?void 0:t.isLeb())||"父节点不是cdn":"无上游节点"},e.prototype.allowSubscribe=function(){var t;return this.child.pid?"有子节点":this.streamInfo.ready()?this.subscribeStatus.status===Je.WAITING?"正在订阅中":this.subscribeStatus.status===Je.COMPLETE?"已经订阅":!(this.parent.pid&&(null===(t=this.parent.source)||void 0===t||!t.isLeb()))||"父节点不是cdn":"流信息未采集到"},e.prototype.selectAvaliableSubscribePeer=function(){var t,e="";if(this.parent.pid&&null!==(t=this.parent.source)&&void 0!==t&&t.isLeb()){var r,n=this.peerCooler.getList(),i=null===(r=this.peerManager)||void 0===r?void 0:r.getBestPeer({excludePeerId:Xc([this.parent.pid],Jc(n),!1)});i&&(e=i.pid)}return e},e.prototype.closeSource=function(t){var e,r;t===Ye.LEB&&(null===(e=this.lebSource)||void 0===e||e.close(),this.lebSource=null),t===Ye.P2P&&(null===(r=this.p2pSource)||void 0===r||r.close(),this.p2pSource=null)},e.prototype.removeChild=function(t){var e;this.child.pid&&t!==this.child.pid||(this.child.pid="",null===(e=this.child.provider)||void 0===e||e.destroy(),this.child.provider=void 0)},e.prototype.setChild=function(t,e){this.child.pid=t,this.child.provider=e},e.prototype.updateParent=function(t){t.pid!==this.parent.pid&&this.cancelsubscribe(),t.source&&(this.parent.pid=t.pid,this.parent.source=t.source)},e.prototype.updateChildTrack=function(t,e){this.child.pid&&this.child.provider&&this.child.provider.updateTrack(t,e.srcPid)},e.prototype.markSubscribeP2P=function(){this.subscribeStatus.status=Je.WAITING},e.prototype.unmarkSubscribeP2P=function(){this.subscribeStatus.status=Je.IDEL},e.prototype.clearSampleTimer=function(){this.sampleTimer&&(clearTimeout(this.sampleTimer),this.sampleTimer=null)},e}(A);function Qe(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var ze=function(t){function e(){var e=this;return Qe(e=t.call(this)||this,"tagName","StreamSourceModule"),Qe(e,"config",null),Qe(e,"reporter",null),Qe(e,"dispatch",new bt),Qe(e,"signal",new Vt),Qe(e,"p2pServer",new _t),Qe(e,"p2pClient",new wt),Qe(e,"signalTransport",new ae),Qe(e,"p2pPeerBuilder",new ne),Qe(e,"peerManager",new le),Qe(e,"peerCache",new he),Qe(e,"signalApi",new ve),Qe(e,"subscribeTrackApi",new ge),Qe(e,"subscriber",new qe),e.signal.on(Mt,(function(t){if(!e.config)throw Error("missing config");e.p2pClient.setLocalPid(t),e.p2pPeerBuilder.setLocalPid(t),e.peerCache.setPeerId(t).setStreamId(e.config.streamId()),e.peerCache.start()})),e.signalTransport.setSignal(e.signal),e.p2pClient.setTransport(e.signalTransport),e.p2pServer.setTransport(e.signalTransport),e.p2pPeerBuilder.setClient(e.p2pClient).setServer(e.p2pServer),e.peerManager.setPeerCache(e.peerCache).setPeerBuilder(e.p2pPeerBuilder),e.subscriber.setClient(e.p2pClient).setPeerManager(e.peerManager),e.subscribeTrackApi.setSubscriber(e.subscriber),e.dispatch.setSubscriber(e.subscriber),e.initApi(),e}return Bc(e,t),Tf(e.prototype,"currentSourcePeerconnection",{get:function(){return this.subscriber.currentSourcePeerconnection},enumerable:!1,configurable:!0}),e.prototype.setMediaStreamProcessor=function(t){return this.dispatch.setMediaStreamProcessor(t),this},e.prototype.setReporter=function(t){return this.reporter=t,this.subscriber.setReporter(t),this.dispatch.setReporter(t),this.peerManager.setReporter(t),this},e.prototype.setStatUnit=function(t){return this.subscriber.setStatUnit(t),this},e.prototype.setBus=function(e){return t.prototype.setBus.call(this,e),this.dispatch.setBus(e),this},e.prototype.destroy=function(){this.dispatch.destroy(),this.peerCache.destroy(),this.peerManager.destroy(),this.p2pClient.destroy(),this.p2pServer.destroy(),this.signal.destroy(),this.signalApi.destroy(),this.subscribeTrackApi.destroy(),this.subscriber.destroy(),t.prototype.destroy.call(this)},e.prototype.configure=function(t){return this.config=t,this.peerCache.configure(this.config),this.peerManager.configure(this.config),this.p2pPeerBuilder.configure(this.config),this.dispatch.configure(t),this.subscriber.configure(t),this},e.prototype.init=function(){if(!this.config)throw Error("missing config");this.peerCache.init(),this.peerManager.init(),this.signal.connect(this.config.signal),this.dispatch.init()},e.prototype.load=function(t){var e=t.streamUrl;if(!this.config)throw Error("missing config");return this.dispatch.load(e)},e.prototype.initApi=function(){this.signalApi.setPeerBuilder(this.p2pPeerBuilder).setPeerManager(this.peerManager).setServer(this.p2pServer),this.subscribeTrackApi.setServer(this.p2pServer)},e}(A),$e=function(){try{var t,e,r;return!!(window.MediaStreamTrack&&window.MediaStream&&window.MediaStreamTrack&&null!==(t=window.RTCPeerConnection)&&void 0!==t&&t.prototype.getReceivers&&null!==(e=window.RTCPeerConnection)&&void 0!==e&&e.prototype.getSenders&&null!==(r=window.RTCRtpReceiver)&&void 0!==r&&r.prototype.createEncodedStreams&&window.HTMLCanvasElement.prototype.captureStream&&window.MediaStreamTrackGenerator&&window.MediaStreamTrackProcessor)}catch(t){return!1}};function tr(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var er=function(){function t(){tr(this,"video",null)}return t.prototype.destroy=function(){this.video&&(this.video.srcObject=null,this.video=null)},t.prototype.setVideoSrcObj=function(t){if(!this.video)throw Error("miss video");var e=this.video;e.onwaiting=function(){},e.ontimeupdate=function(){},e.onplay=function(){},e.onplaying=function(){},e.onpause=function(){},e.srcObject=t,e.onseeking=function(t){},e.onseeked=function(t){}},t.prototype.play=function(){var t;null===(t=this.video)||void 0===t||t.play()},t.prototype.setVideo=function(t){this.video=t},t}();function rr(t,e){var r=Ef(t);if(mf){var n=mf(t);e&&(n=Fc(n).call(n,(function(e){return Of(t,e).enumerable}))),r.push.apply(r,n)}return r}function nr(t){for(var e=1;e<arguments.length;e++){var r,n,i=null!=arguments[e]?arguments[e]:{};e%2?Hs(r=rr(Object(i),!0)).call(r,(function(e){ir(t,e,i[e])})):Hf?$f(t,Hf(i)):Hs(n=rr(Object(i))).call(n,(function(e){Tf(t,e,Of(i,e))}))}return t}function ir(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Hp(t)||null===t)return t;var r=t[th];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hp(e)?e:String(e)}(e))in t?Tf(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var or=function(t){function e(){var e,r=this;return ir(r=t.call(this)||this,"player",new er),ir(r,"mediaStreamProcessor",new F),ir(r,"source",new ze),ir(r,"reporter",new ot),ir(r,"stats",new R),ir(r,"statsTotal",new R),ir(r,"config",new h),ir(r,"conf",new J),$=ci(e="".concat(Yb(),"_")).call(e,z().split("-")[0]),r.stats.configure({deleteZeroNumber:!1,fieldsReportedWithZeroNumber:[],fieldsReservedValueAfterReport:[]}),r.statsTotal.configure({deleteZeroNumber:!1,fieldsReportedWithZeroNumber:[],fieldsReservedValueAfterReport:["uploadBytesTotal","uploadAuidoBytesTotal","uploadVideoBytesTotal","cdnDownloadBytesTotal","cdnDownloadAuidoBytesTotal","cdnDownloadVideoBytesTotal","p2pDownloadBytesTotal","p2pDownloadAuidoBytesTotal","p2pDownloadVideoBytesTotal"]}),r.source.setMediaStreamProcessor(r.mediaStreamProcessor).setReporter(r.reporter).setStatUnit(r.stats).setBus(r),r.source.configure(r.config.config),r.reporter.configure(r.config.config).init(),r.mediaStreamProcessor.init(),r.conf.on("EVENT_CONF_LOADED",(function(t){r.config.update(t.pconf)})).once("EVENT_CONF_LOADED",(function(){r.source.init()})),r.reporter.start(),r}return Bc(e,t),e.support=function(){return $e()},e.create=function(t){var r=new e;return r.configure(t),r},Tf(e,"Event",{get:function(){return o},enumerable:!1,configurable:!0}),Tf(e,"EventCode",{get:function(){return a},enumerable:!1,configurable:!0}),Tf(e,"version",{get:function(){return rt},enumerable:!1,configurable:!0}),Tf(e,"uuid",{get:function(){return z()},enumerable:!1,configurable:!0}),e.prototype.destroy=function(){this.removeAllListeners(),this.reporter.destroy(),this.conf.destroy(),this.source.destroy(),this.mediaStreamProcessor.destroy(),this.player.destroy()},e.prototype.load=function(t){return Zc(this,void 0,void 0,(function(){var e=this;return Yc(this,(function(r){return[2,(this.config.update({streamUrl:t}),this.source.load({streamUrl:t}).then((function(){e.conf.configure({confBaseUrl:e.config.config.confBaseUrl,confInterval:0,domain:e.config.config.xp2pDomain,streamId:e.config.config.streamId()}),e.conf.load()})),{srcObject:this.mediaStreamProcessor.getMediaObj()})]}))}))},e.prototype.setVideoHtmlElement=function(t){this.player.setVideo(t),this.player.setVideoSrcObj(this.mediaStreamProcessor.getMediaObj())},e.prototype.getSDKStats=function(){return this.stats.number("uploadBytes").plus(0),this.stats.number("uploadAuidoBytes").plus(0),this.stats.number("uploadVideoBytes").plus(0),this.stats.number("cdnDownloadBytes").plus(0),this.stats.number("cdnDownloadAuidoBytes").plus(0),this.stats.number("cdnDownloadVideoBytes").plus(0),this.stats.number("p2pDownloadBytes").plus(0),this.stats.number("p2pDownloadAuidoBytes").plus(0),this.stats.number("p2pDownloadVideoBytes").plus(0),this.statsTotal.number("uploadBytesTotal").plus(this.stats.number("uploadBytes").value),this.statsTotal.number("uploadAuidoBytesTotal").plus(this.stats.number("uploadAuidoBytes").value),this.statsTotal.number("uploadVideoBytesTotal").plus(this.stats.number("uploadVideoBytes").value),this.statsTotal.number("cdnDownloadBytesTotal").plus(this.stats.number("cdnDownloadBytes").value),this.statsTotal.number("cdnDownloadAuidoBytesTotal").plus(this.stats.number("cdnDownloadAuidoBytes").value),this.statsTotal.number("cdnDownloadVideoBytesTotal").plus(this.stats.number("cdnDownloadVideoBytes").value),this.statsTotal.number("p2pDownloadBytesTotal").plus(this.stats.number("p2pDownloadBytes").value),this.statsTotal.number("p2pDownloadAuidoBytesTotal").plus(this.stats.number("p2pDownloadAuidoBytes").value),this.statsTotal.number("p2pDownloadVideoBytesTotal").plus(this.stats.number("p2pDownloadVideoBytes").value),nr(nr({},this.stats.generate()),this.statsTotal.generate())},e.prototype.getStats=function(){var t=this.source.currentSourcePeerconnection;return null==t?void 0:t.getStats()},Tf(e.prototype,"playId",{get:function(){return tt()},enumerable:!1,configurable:!0}),e.prototype.configure=function(t){this.config.update(t)},e}(t.EventEmitter)}(),n.default}()}(AE);var IE,xE,LE,DE,NE,OE,ME,UE,VE=r(AE.exports),FE="1.3.5",jE=["overseas-webrtc.liveplay.myqcloud.com","oswebrtc-lint.liveplay.myqcloud.com"],BE="TX_LIVE_PLAYER_SIGNAL_DATA",WE=["webrtc-signal-scheduler.tlivesource.com","bak-webrtc-signal-scheduler.tlivesource.com"],GE="max-width:100%;max-height:100%;object-fit:contain;display:block;margin:0 auto;",ZE="kdccmd",YE="kdccmdp",HE="RequestSignalError";!function(t){t[t.DISCONNECTED=0]="DISCONNECTED",t[t.CONNECTING=1]="CONNECTING",t[t.CONNECTED=2]="CONNECTED"}(IE||(IE={})),function(t){t[t.FAILURE=-1]="FAILURE",t[t.SUCCESS=0]="SUCCESS"}(xE||(xE={})),function(t){t[t.NEED_RECONNECT=-1]="NEED_RECONNECT",t[t.MANUAL_CLOSE=0]="MANUAL_CLOSE"}(LE||(LE={})),function(t){t[t.SEND=0]="SEND",t[t.RECEIVE=1]="RECEIVE"}(DE||(DE={})),function(t){t[t.INIT=0]="INIT",t[t.PLAYING=1]="PLAYING",t[t.WAITING=2]="WAITING"}(NE||(NE={})),function(t){t[t.PLAY_EVT_STREAM_BEGIN=1001]="PLAY_EVT_STREAM_BEGIN",t[t.PLAY_EVT_SERVER_CONNECTED=1002]="PLAY_EVT_SERVER_CONNECTED",t[t.PLAY_EVT_PLAY_BEGIN=1003]="PLAY_EVT_PLAY_BEGIN",t[t.PLAY_EVT_PLAY_STOP=1004]="PLAY_EVT_PLAY_STOP",t[t.PLAY_EVT_SERVER_RECONNECT=1005]="PLAY_EVT_SERVER_RECONNECT",t[t.PLAY_EVT_STREAM_EMPTY=1006]="PLAY_EVT_STREAM_EMPTY",t[t.PLAY_EVT_REQUEST_PULL_BEGIN=1007]="PLAY_EVT_REQUEST_PULL_BEGIN",t[t.PLAY_EVT_REQUEST_PULL_SUCCESS=1008]="PLAY_EVT_REQUEST_PULL_SUCCESS",t[t.PLAY_EVT_PLAY_WAITING_BEGIN=1009]="PLAY_EVT_PLAY_WAITING_BEGIN",t[t.PLAY_EVT_PLAY_WAITING_STOP=1010]="PLAY_EVT_PLAY_WAITING_STOP",t[t.PLAY_ERR_WEBRTC_FAIL=-2001]="PLAY_ERR_WEBRTC_FAIL",t[t.PLAY_ERR_REQUEST_PULL_FAIL=-2002]="PLAY_ERR_REQUEST_PULL_FAIL",t[t.PLAY_ERR_PLAY_FAIL=-2003]="PLAY_ERR_PLAY_FAIL",t[t.PLAY_ERR_SERVER_DISCONNECT=-2004]="PLAY_ERR_SERVER_DISCONNECT",t[t.PLAY_ERR_DECODE_FAIL=-2005]="PLAY_ERR_DECODE_FAIL",t[t.PLAY_ERR_REQUEST_ABR_FAIL=-2006]="PLAY_ERR_REQUEST_ABR_FAIL",t[t.PLAY_EVT_P2P_START_SUCCESS=3001]="PLAY_EVT_P2P_START_SUCCESS",t[t.PLAY_EVT_P2P_SOURCE_SWITCH=3002]="PLAY_EVT_P2P_SOURCE_SWITCH",t[t.PLAY_ERR_P2P_START_FAIL=-4001]="PLAY_ERR_P2P_START_FAIL"}(OE||(OE={}));var JE,XE,KE,qE=/tbs\/(\d+) /i,QE=/OS (\d+)_(\d+)_?(\d+)?/,zE=qE.test(navigator.userAgent),$E=/firefox\/(\d+)\./i.test(navigator.userAgent),tw=$E,ew=/UCBrowser\/(\d+)\./i.test(navigator.userAgent),rw=/safari\/(\d+)\./i.test(navigator.userAgent)&&!/chrome\/(\d+)\./i.test(navigator.userAgent),nw=/iPhone|iPad|iOS/i.test(navigator.userAgent),iw=nw,ow=function(){var t=navigator.userAgent.match(QE);return t&&[HR(t[1],10),HR(t[2],10),HR(t[3]||"0",10)]||[]},aw=!!(null===(ME=window.RTCRtpReceiver)||void 0===ME?void 0:ME.prototype.createEncodedStreams),sw=!!(null===(UE=window.RTCRtpReceiver)||void 0===UE?void 0:UE.prototype.getSynchronizationSources),cw=function(t,e,r){var n=t.match(e);return n&&n.length>=r&&HR(n[r],10)},uw=function(t,e){var r,n,i=/^(?:webrtc:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)(?:[^?#]*)(?:\?*)([^?#]*)/.exec(t);if(i){var o=i[1].split("&");try{for(var a=Hc(o),s=a.next();!s.done;s=a.next()){var c=Jc(s.value.split("="),2),u=c[0],l=c[1];if(u===e)return l}}catch(t){r={error:t}}finally{try{s&&!s.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}}return null},lw=function(t,e,r){var n,i,o=/^(?:webrtc:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)(?:[^?#]*)(?:\?*)([^?#]*)/.exec(t);if(o){var a,s,c=o[1],u=[],l=ci(a="".concat(e,"=")).call(a,r);try{for(var d=Hc(c.split("&")),p=d.next();!p.done;p=d.next()){var f,h=Jc(p.value.split("="),2),m=h[0],v=h[1];if(m===e)null!==r&&u.push(l);else u.push(ci(f="".concat(m,"=")).call(f,v))}}catch(t){n={error:t}}finally{try{p&&!p.done&&(i=d.return)&&i.call(d)}finally{if(n)throw n.error}}return null===r||Hi(u).call(u,l)||u.push(l),ci(s="".concat(t.replace(c,""))).call(s,u.join("&"))}return t},dw={},pw=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],fw=pw[0];try{for(var hw=Hc(pw),mw=hw.next();!mw.done;mw=hw.next()){var vw=mw.value;if(vw[1]in document){KE=vw;break}}}catch(t){JE={error:t}}finally{try{mw&&!mw.done&&(XE=hw.return)&&XE.call(hw)}finally{if(JE)throw JE.error}}if(KE)for(var yw=0;yw<KE.length;yw++)dw[fw[yw]]=KE[yw];var gw,bw=function(t){if("function"==typeof t.webkitEnterFullScreen){var e=navigator.userAgent||"";if(/Android/.test(e)||!/Chrome|Mac OS X 10.5/.test(e))return!0}return!1};!function(t){t[t.UNKNOWN=0]="UNKNOWN",t[t.EXCELLENT=1]="EXCELLENT",t[t.GOOD=2]="GOOD",t[t.POOR=3]="POOR",t[t.BAD=4]="BAD",t[t.VERY_BAD=5]="VERY_BAD"}(gw||(gw={}));var Cw=Ns.map;br({target:"Array",proto:!0,forced:!Wn("map")},{map:function(t){return Cw(this,t,arguments.length>1?arguments[1]:void 0)}});var Sw=Or("Array").map,Tw=d,_w=Sw,Rw=Array.prototype,Ew=function(t){var e=t.map;return t===Rw||Tw(Rw,t)&&e===Rw.map?_w:e},ww=function(t){var e,r=t.split("\r\n"),n=[];Hs(r).call(r,(function(t){var e=t.toLowerCase();Hi(e).call(e,"a=rtpmap")&&Hi(e).call(e,"h264")&&n.push(t)}));var i=Fc(e=Ew(n).call(n,(function(t){var e=/a=rtpmap:(\d+)\s/.exec(t);return e&&e.length>1?e[1]:null}))).call(e,(function(t){return null!==t})),o=[];return Hs(r).call(r,(function(t){var e=t;if(Hi(t).call(t,"a=fmtp:111")&&(e="".concat(t,";stereo=1")),Hi(t).call(t,"a=fmtp")){var r=/a=fmtp:(\d+)\s/.exec(t);r&&r.length>1&&Hi(i).call(i,r[1])&&(e="".concat(t,";sps-pps-idr-in-keyframe=1"))}o.push(e)})),function(t){var e;if(!tw)return t;var r=t.split("\r\n"),n=[],i=[];Hs(r).call(r,(function(t){var e=t.toLowerCase();Hi(e).call(e,"a=rtpmap")&&Hi(e).call(e,"h264")&&n.push(t)})),n.length>1&&i.push.apply(i,Xc([],Jc(hf(n).call(n,1)),!1));var o=Fc(e=Ew(i).call(i,(function(t){var e=/a=rtpmap:(\d+)\s/.exec(t);return e&&e.length>1?e[1]:null}))).call(e,(function(t){return null!==t})),a=[];return Hs(r).call(r,(function(t){var e,r=t;if(Hi(t).call(t,"a=setup")&&(r="a=setup:passive"),(Hi(t).call(t,"m=audio")||Hi(t).call(t,"m=video"))&&(r=Fc(e=t.split(" ")).call(e,(function(t,e){return e<3||!Hi(o).call(o,t)})).join(" ")),Hi(t).call(t,"a=fmtp")||Hi(t).call(t,"a=rtcp-fb")||Hi(t).call(t,"a=rtpmap")){var n=/a=(?:fmtp|rtcp-fb|rtpmap):(\d+)\s/.exec(t);if(n&&n.length>1&&Hi(o).call(o,n[1]))return}a.push(r)})),a.join("\r\n")}(o.join("\r\n"))},Pw=function(t){var e,r,n="",i="";return Hs(e=t.split("\r\n")).call(e,(function(t){var e=/(?:a=ice-ufrag:)(.+)/.exec(t);if(e)n=e[1];else{var r=/(?:a=ice-pwd:)(.+)/.exec(t);r&&(i=r[1])}})),ci(r="".concat(n,"_")).call(r,i)};br({target:"Array",stat:!0},{isArray:Wr});var kw=X.Array.isArray,Aw=Or("Array").keys,Iw=fn,xw=Bt,Lw=d,Dw=Aw,Nw=Array.prototype,Ow={DOMTokenList:!0,NodeList:!0},Mw=function(t){var e=t.keys;return t===Nw||Lw(Nw,t)&&e===Nw.keys||xw(Ow,Iw(t))?Dw:e},Uw={exports:{}},Vw={};Object.defineProperty(Vw,"__esModule",{value:!0});var Fw=function(t){this.bitReservoir=t,this.originalBitReservoir=t};Vw.ExpGolombDecoder=Fw,Fw.prototype.byteAlign=function(){var t=8*Math.ceil((this.originalBitReservoir.length-this.bitReservoir.length)/8),e=this.originalBitReservoir.length-t,r=this.bitReservoir.length-e;return this.readRawBits(r)},Fw.prototype.countLeadingZeros=function(){for(var t=0;t<this.bitReservoir.length;t++)if("1"===this.bitReservoir[t])return t;return-1},Fw.prototype.readUnsignedExpGolomb=function(){var t=this.countLeadingZeros(),e=t+1;if(-1===t)throw new Error("Error reading exp-golomb value.");this.readBits(t);var r=this.readBits(e);return r-=1},Fw.prototype.readExpGolomb=function(){var t=this.readUnsignedExpGolomb();return 0!==t&&(t=1&t?(t+1)/2:-t/2),t},Fw.prototype.readBits=function(t){if(this.bitReservoir.length<t)throw new Error("Error reading bit stream value expected ("+t+") bits remaining but found ("+this.bitReservoir.length+")");var e=parseInt(this.bitReservoir.slice(0,t),2);return this.bitReservoir=this.bitReservoir.slice(t),e},Fw.prototype.readRawBits=function(t){if(this.bitReservoir.length<t)throw new Error("Error reading bit stream value expected ("+t+") bits remaining but found ("+this.bitReservoir.length+")");var e=this.bitReservoir.slice(0,t);return this.bitReservoir=this.bitReservoir.slice(t),e},Fw.prototype.readUnsignedByte=function(){return this.readBits(8)};var jw=function(t){this.bitReservoir=t||""};Vw.ExpGolombEncoder=jw,jw.prototype.writeUnsignedExpGolomb=function(t){for(var e="",r=(t+1).toString(2),n=r.length-1,i=0;i<n;i++)e+="0";this.bitReservoir+=e+r},jw.prototype.writeExpGolomb=function(t){t=t<=0?2*-t:2*t-1,this.writeUnsignedExpGolomb(t)},jw.prototype.writeBits=function(t,e){for(var r="",n=(e&(1<<t)-1).toString(2),i=t-n.length,o=0;o<i;o++)r+="0";this.bitReservoir+=r+n},jw.prototype.writeRawBits=function(t,e){for(var r="",n=t-e.length,i=0;i<n;i++)r+="0";this.bitReservoir+=r+e},jw.prototype.writeUnsignedByte=function(t){this.writeBits(8,t)};var Bw={},Ww={};Object.defineProperty(Ww,"__esModule",{value:!0});Ww.typedArrayToBitString=function(t){for(var e=[],r=t.BYTES_PER_ELEMENT||1,n="",i=0;i<t.length;i++)e.push(t[i]);for(i=0;i<r;i++)n+="00000000";return e.map((function(t){return(n+t.toString(2)).slice(8*-r)})).join("")};Ww.bitStringToTypedArray=function(t){for(var e=8-t.length%8,r=0;8!==e&&r<e;r++)t+="0";var n=t.match(/(.{8})/g).map((function(t){return parseInt(t,2)}));return new Uint8Array(n)};Ww.removeRBSPTrailingBits=function(t){return t.split(/10*$/)[0]};Ww.appendRBSPTrailingBits=function(t){var e=t+"10000000",r=e.length%8;return 0===r?e:e.slice(0,-r)};var Gw={};Object.defineProperty(Gw,"__esModule",{value:!0});Gw.mergeObj=function(t,e){var r={};return t&&Object.keys(t).forEach((function(e){r[e]=t[e]})),e&&Object.keys(e).forEach((function(t){r[t]=e[t]})),r};var Zw={};Object.defineProperty(Zw,"__esModule",{value:!0});Zw.list=function(t,e){return{decode:function(r){var n=r.expGolomb,i=r.output,o=r.options,a=r.indexes,s=r.path,c=e?s:s.concat("[list]");return t.forEach((function(t){i=t.decode({expGolomb:n,output:i,options:o,indexes:a,path:c})||i})),i},encode:function(e,r,n,i){t.forEach((function(t){t.encode(e,r,n,i)}))}}};var Yw={};Object.defineProperty(Yw,"__esModule",{value:!0});Yw.propertyHandler=function(t){var e=t.split("["),r=e[0],n=null;return e.length>1&&(n=e.slice(1).map(parseFloat)),{propertyName:r,indexArray:n}};var Hw={};Object.defineProperty(Hw,"__esModule",{value:!0});Hw.getProperty=function(t,e,r,n){var i=t[r]||e[r];return n.length?n.reduce((function(t,e){return Array.isArray(t)||Object.isObject(t)?t[e]:t}),i):i};Hw.writeProperty=function(t,e,r,n,i){var o=t[r]||e[r],a=n[n.length-1];if(!o){if(!n.length)return void(t[r]=i);o=t[r]=[]}n.slice(0,-1).reduce((function(t,e){return t[e],Array.isArray(t[e])||(t[e]=[]),t[e]}),o)[a]=i};Hw.indexArrayMerge=function(t,e){return t.slice(-e.length).map((function(t,r){return isNaN(e[r])?t:e[r]}))},Object.defineProperty(Bw,"__esModule",{value:!0});var Jw=Vw,Xw=Ww,Kw=Zw,qw=Yw,Qw=Hw,zw=function(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];var i=(0,Kw.list)(r,!0);return{decode:function(e,r,n){var o=(0,Xw.typedArrayToBitString)(e),a=o;n=n||{},(r=r||{}).no_trailer_bits||(a=(0,Xw.removeRBSPTrailingBits)(o));var s=new Jw.ExpGolombDecoder(a);try{return i.decode({options:r,output:n,expGolomb:s,indexes:[],path:[t]})}catch(t){return n}},encode:function(e,r){var n=new Jw.ExpGolombEncoder;r=r||{},i.encode({options:r,input:e,expGolomb:n,indexes:[],path:[t]});var o=n.bitReservoir,a=(0,Xw.appendRBSPTrailingBits)(o);return(0,Xw.bitStringToTypedArray)(a)}}};Bw.start=zw;Bw.startArray=function(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];var i=zw.apply(void 0,[t].concat(r));return{decode:function(t,e){return i.decode(t,e,[])},encode:i.encode}};Bw.data=function(t,e){var r=(0,qw.propertyHandler)(t),n=r.propertyName,i=r.indexArray;return{name:t,decode:function(t){var r=t.expGolomb,o=t.output,a=t.options,s=t.indexes,c=t.path,u=void 0;try{u=e.read(r,o,a,s)}catch(t){throw o["Parse Error:"]=t.message+" at "+c.join("/"),t}return i?(0,Qw.writeProperty)(o,a,n,(0,Qw.indexArrayMerge)(s,i),u):o[n]=u,o},encode:function(t){var r=t.expGolomb,o=t.input,a=t.options,s=t.indexes;t.path;var c=void 0;"number"==typeof(c=i?(0,Qw.getProperty)(o,a,n,(0,Qw.indexArrayMerge)(s,i)):o[n])&&(c=e.write(r,o,a,s,c))}}};Bw.debug=function(t){return{decode:function(e){var r=e.expGolomb,n=e.output,i=e.options,o=e.indexes,a=e.path;console.log(t,a.join(","),r.bitReservoir,n,i,o)},encode:function(e){var r=e.expGolomb,n=e.input,i=e.options,o=e.indexes,a=e.path;console.log(t,a.join(","),r.bitReservoir,n,i,o)}}};Bw.newObj=function(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];var i=(0,qw.propertyHandler)(t),o=i.propertyName,a=i.indexArray,s=(0,Kw.list)(r,!0);return{name:t,decode:function(e){var r=e.expGolomb,n=e.output,i=e.options,c=e.indexes,u=e.path.concat(t),l=s.decode({expGolomb:r,output:Object.create(n),options:i,indexes:c,path:u});return a?(0,Qw.writeProperty)(n,i,o,(0,Qw.indexArrayMerge)(c,a),l):n[o]=l,n},encode:function(e){var r=e.expGolomb,n=e.input,i=e.options,c=e.indexes,u=void 0;if("number"==typeof(u=nameArray?(0,Qw.getProperty)(n,i,o,(0,Qw.indexArrayMerge)(c,a)):n[o])){var l=path.concat(t);s.encode({expGolomb:r,input:u,options:i,indexes:c,path:l})}}}};Bw.verify=function(t){return{decode:function(e){var r=e.expGolomb,n=e.output;e.options,e.indexes;var i=r.bitReservoir.length;0!==i&&(n["Validation Error:"]=t+" was not completely parsed - there were ("+i+") bits remaining")},encode:function(t){t.expGolomb,t.input,t.options,t.indexes}}};var $w={};Object.defineProperty($w,"__esModule",{value:!0});var tP=Zw,eP=Yw,rP=Hw;$w.when=function(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];var i=(0,tP.list)(r,!0);return{decode:function(e){var r=e.expGolomb,n=e.output,o=e.options,a=e.indexes,s=e.path.concat("[when]");return t(n,o,a)?i.decode({expGolomb:r,output:n,options:o,indexes:a,path:s}):n},encode:function(e){var r=e.expGolomb,n=e.input,o=e.options,a=e.indexes,s=e.path.concat("[when]");t(n,o,a)&&i.encode({expGolomb:r,input:n,options:o,indexes:a,path:s})}}};$w.each=function(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];var i=(0,tP.list)(r,!0);return{decode:function(e){var r=e.expGolomb,n=e.output,o=e.options,a=e.indexes,s=e.path.concat("[each]"),c=a.length;for(a[c]=0;t(a[c],n,o);)i.decode({expGolomb:r,output:n,options:o,indexes:a,path:s}),a[c]++;return a.length=c,n},encode:function(e){var r=e.expGolomb,n=e.input,o=e.options,a=e.indexes,s=e.path.concat("[each]"),c=a.length;for(a[c]=0;t(a[c],n,o);)i.encode({expGolomb:r,input:n,options:o,indexes:a,path:s}),a[c]++;a.length=c}}};$w.inArray=function(t,e){var r=(0,eP.propertyHandler)(t),n=r.propertyName,i=r.indexArray;return function(t,r,o){return i?-1!==e.indexOf((0,rP.getProperty)(t,r,n,(0,rP.indexArrayMerge)(o,i))):-1!==e.indexOf(t[n])||-1!==e.indexOf(r[n])}};$w.equals=function(t,e){var r=(0,eP.propertyHandler)(t),n=r.propertyName,i=r.indexArray;return function(t,r,o){return i?(0,rP.getProperty)(t,r,n,(0,rP.indexArrayMerge)(o,i))===e:t[n]===e||r[n]===e}};$w.gt=function(t,e){var r=(0,eP.propertyHandler)(t),n=r.propertyName,i=r.indexArray;return function(t,r,o){return i?(0,rP.getProperty)(t,r,n,(0,rP.indexArrayMerge)(o,i))===e:t[n]>e||r[n]>e}};$w.not=function(t){return function(e,r,n){return!t(e,r,n)}};$w.some=function(t){return function(e,r,n){return t.some((function(t){return t(e,r,n)}))}};$w.every=function(t){return function(e,r,n){return t.every((function(t){return t(e,r,n)}))}};$w.whenMoreData=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=(0,tP.list)(e,!0);return{decode:function(t){var e=t.expGolomb,r=t.output,i=t.options,o=t.indexes,a=t.path.concat("[whenMoreData]");return e.bitReservoir.length?n.decode({expGolomb:e,output:r,options:i,indexes:o,path:a}):r},encode:function(t){var e=t.expGolomb,r=t.input,i=t.options,o=t.indexes,a=t.path.concat("[whenMoreData]");n.encode({expGolomb:e,input:r,options:i,indexes:o,path:a})}}};$w.whileMoreData=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=(0,tP.list)(e,!0);return{decode:function(t){var e=t.expGolomb,r=t.output,i=t.options,o=t.indexes,a=t.path.concat("[whileMoreData]"),s=o.length;for(o[s]=0;e.bitReservoir.length;)n.decode({expGolomb:e,output:r,options:i,indexes:o,path:a}),o[s]++;return o.length=s,r},encode:function(t){var e=t.expGolomb,r=t.input,i=t.options,o=t.indexes,a=t.path.concat("[whenMoreData]"),s=o.length;o[s]=0;var c=0;for(Array.isArray(r)&&(c=r.length);index<c;)n.encode({expGolomb:e,input:r,options:i,indexes:o,path:a}),o[s]++;o.length=s}}};var nP={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0});var r=function(t,e,r,n,i){return"function"==typeof t?t(e,r,n,i):t},n={u:function(t){return{read:function(e,n,i,o){var a=r(t,e,n,i,o);return e.readBits(a)},write:function(e,n,i,o,a){var s=r(t,e,n,i,o);e.writeBits(s,a)}}},f:function(t){return{read:function(e,n,i,o){var a=r(t,e,n,i,o);return e.readBits(a)},write:function(e,n,i,o,a){var s=r(t,e,n,i,o);e.writeBits(s,a)}}},ue:function(){return{read:function(t,e,r,n){return t.readUnsignedExpGolomb()},write:function(t,e,r,n,i){t.writeUnsignedExpGolomb(i)}}},se:function(){return{read:function(t,e,r,n){return t.readExpGolomb()},write:function(t,e,r,n,i){t.writeExpGolomb(i)}}},b:function(){return{read:function(t,e,r,n){return t.readUnsignedByte()},write:function(t,e,r,n,i){t.writeUnsignedByte(i)}}},val:function(t){return{read:function(e,r,n,i){return"function"==typeof t?t(e,r,n,i):t},write:function(e,r,n,i,o){"function"==typeof t&&t(ExpGolomb,output,n,i)}}},byteAlign:function(){return{read:function(t,e,r,n){return t.byteAlign()},write:function(t,e,r,n,i){}}}};e.default=n,t.exports=e.default}(nP,nP.exports),function(t,e){Object.defineProperty(e,"__esModule",{value:!0});var r=Vw,n=Bw,i=Zw,o=$w,a=nP.exports,s=Ww;(0,i.list)([]);var c=function(t,e,r,n){return r.initial_cpb_removal_delay_length_minus1+1},u={0:{name:"buffering_period",codec:(0,i.list)([(0,n.data)("seq_parameter_set_id",(0,a.ue)(null)),(0,o.when)((0,o.equals)("nal_hrd_parameters_present_flag",1),(0,o.each)((function(t,e,r){return t<=r.cpb_cnt_minus1}),(0,n.data)("initial_cpb_removal_delay[]",(0,a.u)(c)),(0,n.data)("initial_cpb_removal_delay_offset[]",(0,a.u)(c)))),(0,o.when)((0,o.equals)("vcl_hrd_parameters_present_flag",1),(0,o.each)((function(t,e,r){return t<=r.cpb_cnt_minus1}),(0,n.data)("initial_cpb_removal_delay[]",(0,a.u)(c)),(0,n.data)("initial_cpb_removal_delay_offset[]",(0,a.u)(c))))])},1:{name:"pic_timing",codec:(0,i.list)([(0,o.when)((0,o.some)([(0,o.equals)("nal_hrd_parameters_present_flag",1),(0,o.equals)("vcl_hrd_parameters_present_flag",1)]),(0,n.data)("cpb_removal_delay",(0,a.u)((function(t,e,r,n){return r.cpb_removal_delay_length_minus1+1}))),(0,n.data)("dpb_output_delay",(0,a.u)((function(t,e,r,n){return r.dpb_output_delay_length_minus1+1})))),(0,o.when)((0,o.equals)("pic_struct_present_flag",1),(0,n.data)("pic_struct",(0,a.u)(4)),(0,o.when)((0,o.equals)("pic_struct",0),(0,n.data)("NumClockTS",(0,a.val)(1))),(0,o.when)((0,o.equals)("pic_struct",1),(0,n.data)("NumClockTS",(0,a.val)(1))),(0,o.when)((0,o.equals)("pic_struct",2),(0,n.data)("NumClockTS",(0,a.val)(1))),(0,o.when)((0,o.equals)("pic_struct",3),(0,n.data)("NumClockTS",(0,a.val)(2))),(0,o.when)((0,o.equals)("pic_struct",4),(0,n.data)("NumClockTS",(0,a.val)(2))),(0,o.when)((0,o.equals)("pic_struct",5),(0,n.data)("NumClockTS",(0,a.val)(3))),(0,o.when)((0,o.equals)("pic_struct",6),(0,n.data)("NumClockTS",(0,a.val)(3))),(0,o.when)((0,o.equals)("pic_struct",7),(0,n.data)("NumClockTS",(0,a.val)(2))),(0,o.when)((0,o.equals)("pic_struct",8),(0,n.data)("NumClockTS",(0,a.val)(2))),(0,o.each)((function(t,e){return t<e.NumClockTS}),(0,n.data)("clock_timestamp_flag[]",(0,a.u)(1)),(0,o.when)((0,o.equals)("clock_timestamp_flag[]",1),(0,n.data)("ct_type[]",(0,a.u)(2)),(0,n.data)("nuit_field_based_flag[]",(0,a.u)(1)),(0,n.data)("counting_type[]",(0,a.u)(5)),(0,n.data)("full_timestamp_flag[]",(0,a.u)(1)),(0,n.data)("discontinuity_flag[]",(0,a.u)(1)),(0,n.data)("cnt_dropped_flag[]",(0,a.u)(1)),(0,n.data)("n_frames[]",(0,a.u)(8)),(0,o.when)((0,o.equals)("full_timestamp_flag[]",1),(0,n.data)("seconds_value[]",(0,a.u)(6)),(0,n.data)("minutes_value[]",(0,a.u)(6)),(0,n.data)("hours_value[]",(0,a.u)(5))),(0,o.when)((0,o.equals)("full_timestamp_flag[]",0),(0,n.data)("seconds_flag[]",(0,a.u)(1)),(0,o.when)((0,o.equals)("seconds_flag[]",1),(0,n.data)("seconds_value[]",(0,a.u)(6)),(0,n.data)("minutes_flag[]",(0,a.u)(1)),(0,o.when)((0,o.equals)("minutes_flag[]",1),(0,n.data)("minutes_value[]",(0,a.u)(6)),(0,n.data)("hours_flag[]",(0,a.u)(1)),(0,o.when)((0,o.equals)("hours_flag[]",1),(0,n.data)("hours_value[]",(0,a.u)(5)))))),(0,o.when)((0,o.gt)("time_offset_length",0),(0,n.data)("time_offset",(0,a.u)((function(t,e,r,n){return r.time_offset_length})))))))])},2:{name:"pan_scan_rect"},3:{name:"filler_payload"},4:{name:"user_data_registered_itu_t_t35",codec:(0,i.list)([(0,n.data)("itu_t_t35_country_code",(0,a.u)(8)),(0,n.data)("itu_t_t35_provider_code",(0,a.u)(16)),(0,o.when)((0,o.equals)("itu_t_t35_provider_code",49),(0,n.data)("ATSC_user_identifier",(0,a.u)(32))),(0,o.when)((0,o.inArray)("itu_t_t35_provider_code",[47,49]),(0,n.data)("ATSC1_data_user_data_type_code",(0,a.u)(8))),(0,o.when)((0,o.equals)("itu_t_t35_provider_code",47),(0,n.data)("DIRECTV_user_data_length",(0,a.u)(8))),(0,n.data)("process_em_data_flag",(0,a.u)(1)),(0,n.data)("process_cc_data_flag",(0,a.u)(1)),(0,n.data)("additional_data_flag",(0,a.u)(1)),(0,n.data)("cc_count",(0,a.u)(5)),(0,n.data)("em_data",(0,a.u)(8)),(0,o.each)((function(t,e){return t<e.cc_count}),(0,n.newObj)("cc_data_pkts[]",(0,n.data)("type",(0,a.val)("cc_data_pkt")),(0,n.data)("marker_bits",(0,a.u)(5)),(0,n.data)("cc_valid",(0,a.u)(1)),(0,n.data)("cc_type",(0,a.u)(2)),(0,n.data)("cc_data_1",(0,a.u)(8)),(0,n.data)("cc_data_2",(0,a.u)(8)))),(0,n.data)("marker_bits",(0,a.u)(8))])},5:{name:"user_data_unregistered"},6:{name:"recovery_point",codec:(0,i.list)([(0,n.data)("recovery_frame_cnt",(0,a.ue)(null)),(0,n.data)("exact_match_flag",(0,a.u)(1)),(0,n.data)("broken_link_flag",(0,a.u)(1)),(0,n.data)("changing_slice_group_idc",(0,a.u)(2))])},7:{name:"dec_ref_pic_marking_repetition"},8:{name:"spare_pic"},9:{name:"scene_info"},10:{name:"sub_seq_info"},11:{name:"sub_seq_layer_characteristics"},12:{name:"sub_seq_characteristics"},13:{name:"full_frame_freeze"},14:{name:"full_frame_freeze_release"},15:{name:"full_frame_snapshot"},16:{name:"progressive_refinement_segment_start"},17:{name:"progressive_refinement_segment_end"},18:{name:"motion_constrained_slice_group_set"},19:{name:"film_grain_characteristics"},20:{name:"deblocking_filter_display_preference"},21:{name:"stereo_video_info"},22:{name:"post_filter_hint"},23:{name:"tone_mapping_info"},24:{name:"scalability_info"},25:{name:"sub_pic_scalable_layer"},26:{name:"non_required_layer_rep"},27:{name:"priority_layer_info"},28:{name:"layers_not_present"},29:{name:"layer_dependency_change"},30:{name:"scalable_nesting"},31:{name:"base_layer_temporal_hrd"},32:{name:"quality_layer_integrity_check"},33:{name:"redundant_pic_property"},34:{name:"tl"},35:{name:"tl_switching_point"},36:{name:"parallel_decoding_info"},37:{name:"mvc_scalable_nesting"},38:{name:"view_scalability_info"},39:{name:"multiview_scene_info"},40:{name:"multiview_acquisition_info"},41:{name:"non_required_view_component"},42:{name:"view_dependency_change"},43:{name:"operation_points_not_present"},44:{name:"base_view_temporal_hrd"},45:{name:"frame_packing_arrangement"}},l={decode:function(t){var e=t.expGolomb,n=t.output,i=t.options,o=t.indexes,a=t.path,c=o[0],l={payloadType:0,payloadSize:0},d=void 0;do{d=e.readUnsignedByte(),l.payloadType+=d}while(255===d);do{d=e.readUnsignedByte(),l.payloadSize+=d}while(255===d);var p=u[l.payloadType],f=e.readRawBits(8*l.payloadSize);if(p)if(l.type=p.name,p.codec){var h=new r.ExpGolombDecoder(f);p.codec.decode({expGolomb:h,output:l,options:i,path:a,indexes:o})}else l.data=(0,s.bitStringToTypedArray)(f);else l.type="unknown type",l.data=(0,s.bitStringToTypedArray)(f);return n[c]=l,n},encode:function(t){for(var e=t.expGolomb,n=t.input,i=t.options,o=t.indexes,a=t.path,c=n[o[0]],l=c.payloadType;l>255;)l-=255,e.writeUnsignedByte(255);e.writeUnsignedByte(l);for(var d=c.payloadSize;d>255;)d-=255,e.writeUnsignedByte(255);e.writeUnsignedByte(d);var p=u[c.payloadType];if(p&&p.codec){var f=new r.ExpGolombEncoder;p.codec.encode({expGolomb:f,input:c,options:i,path:a,indexes:o});var h=f.bitReservoir;h.length%8!=0&&(h=(0,s.appendRBSPTrailingBits)(h)),e.writeRawBits(8*c.payloadSize,h)}else c.data?e.writeRawBits(8*c.payloadSize,(0,s.typedArrayToBitString)(c.data)):e.writeRawBits(8*c.payloadSize,"")}},d=(0,n.startArray)("sei_message",(0,o.whileMoreData)(l));e.default=d,t.exports=e.default}(Uw,Uw.exports);var iP=r(Uw.exports),oP=function(t){return iP.decode(t)},aP=function(){function t(){this.consoleEnabled=!1}return t.prototype.log=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this.consoleEnabled&&console.log.apply(console,Xc([],Jc(t),!1))},t.prototype.enableConsole=function(t){this.consoleEnabled=t},t}(),sP=new aP;function cP(t,e,r){var n=void 0===e?null:e,i=function(t,e){var r=atob(t);if(e){for(var n=new Uint8Array(r.length),i=0,o=r.length;i<o;++i)n[i]=r.charCodeAt(i);return String.fromCharCode.apply(null,new Uint16Array(n.buffer))}return r}(t,void 0!==r&&r),o=i.indexOf("\n",10)+1,a=i.substring(o)+(n?"//# sourceMappingURL="+n:""),s=new Blob([a],{type:"application/javascript"});return URL.createObjectURL(s)}var uP,lP,dP,pP,fP=(uP="Lyogcm9sbHVwLXBsdWdpbi13ZWItd29ya2VyLWxvYWRlciAqLwp2YXIgd29ya2VyX2NvZGU9ZnVuY3Rpb24oKXsidXNlIHN0cmljdCI7dmFyIHQ9InVuZGVmaW5lZCIhPXR5cGVvZiBnbG9iYWxUaGlzP2dsb2JhbFRoaXM6InVuZGVmaW5lZCIhPXR5cGVvZiB3aW5kb3c/d2luZG93OiJ1bmRlZmluZWQiIT10eXBlb2YgZ2xvYmFsP2dsb2JhbDoidW5kZWZpbmVkIiE9dHlwZW9mIHNlbGY/c2VsZjp7fSxlPWZ1bmN0aW9uKHQpe3JldHVybiB0JiZ0Lk1hdGg9PU1hdGgmJnR9LHI9ZSgib2JqZWN0Ij09dHlwZW9mIGdsb2JhbFRoaXMmJmdsb2JhbFRoaXMpfHxlKCJvYmplY3QiPT10eXBlb2Ygd2luZG93JiZ3aW5kb3cpfHxlKCJvYmplY3QiPT10eXBlb2Ygc2VsZiYmc2VsZil8fGUoIm9iamVjdCI9PXR5cGVvZiB0JiZ0KXx8ZnVuY3Rpb24oKXtyZXR1cm4gdGhpc30oKXx8RnVuY3Rpb24oInJldHVybiB0aGlzIikoKSxuPWZ1bmN0aW9uKHQpe3RyeXtyZXR1cm4hIXQoKX1jYXRjaCh0KXtyZXR1cm4hMH19LG89IW4oKGZ1bmN0aW9uKCl7dmFyIHQ9ZnVuY3Rpb24oKXt9LmJpbmQoKTtyZXR1cm4iZnVuY3Rpb24iIT10eXBlb2YgdHx8dC5oYXNPd25Qcm9wZXJ0eSgicHJvdG90eXBlIil9KSksaT1vLHU9RnVuY3Rpb24ucHJvdG90eXBlLGM9dS5hcHBseSxhPXUuY2FsbCxmPSJvYmplY3QiPT10eXBlb2YgUmVmbGVjdCYmUmVmbGVjdC5hcHBseXx8KGk/YS5iaW5kKGMpOmZ1bmN0aW9uKCl7cmV0dXJuIGEuYXBwbHkoYyxhcmd1bWVudHMpfSksbD1vLHM9RnVuY3Rpb24ucHJvdG90eXBlLHA9cy5iaW5kLGI9cy5jYWxsLHk9bCYmcC5iaW5kKGIsYiksdj1sP2Z1bmN0aW9uKHQpe3JldHVybiB0JiZ5KHQpfTpmdW5jdGlvbih0KXtyZXR1cm4gdCYmZnVuY3Rpb24oKXtyZXR1cm4gYi5hcHBseSh0LGFyZ3VtZW50cyl9fSxkPWZ1bmN0aW9uKHQpe3JldHVybiJmdW5jdGlvbiI9PXR5cGVvZiB0fSxoPXt9LG09IW4oKGZ1bmN0aW9uKCl7cmV0dXJuIDchPU9iamVjdC5kZWZpbmVQcm9wZXJ0eSh7fSwxLHtnZXQ6ZnVuY3Rpb24oKXtyZXR1cm4gN319KVsxXX0pKSxnPW8sdz1GdW5jdGlvbi5wcm90b3R5cGUuY2FsbCxqPWc/dy5iaW5kKHcpOmZ1bmN0aW9uKCl7cmV0dXJuIHcuYXBwbHkodyxhcmd1bWVudHMpfSxPPXt9LFM9e30ucHJvcGVydHlJc0VudW1lcmFibGUsRT1PYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yLFA9RSYmIVMuY2FsbCh7MToyfSwxKTtPLmY9UD9mdW5jdGlvbih0KXt2YXIgZT1FKHRoaXMsdCk7cmV0dXJuISFlJiZlLmVudW1lcmFibGV9OlM7dmFyIFQsSSxrPWZ1bmN0aW9uKHQsZSl7cmV0dXJue2VudW1lcmFibGU6ISgxJnQpLGNvbmZpZ3VyYWJsZTohKDImdCksd3JpdGFibGU6ISg0JnQpLHZhbHVlOmV9fSxDPXYsXz1DKHt9LnRvU3RyaW5nKSxGPUMoIiIuc2xpY2UpLEw9bix6PWZ1bmN0aW9uKHQpe3JldHVybiBGKF8odCksOCwtMSl9LEQ9T2JqZWN0LE09digiIi5zcGxpdCksTj1MKChmdW5jdGlvbigpe3JldHVybiFEKCJ6IikucHJvcGVydHlJc0VudW1lcmFibGUoMCl9KSk/ZnVuY3Rpb24odCl7cmV0dXJuIlN0cmluZyI9PXoodCk/TSh0LCIiKTpEKHQpfTpELHg9VHlwZUVycm9yLEE9ZnVuY3Rpb24odCl7aWYobnVsbD09dCl0aHJvdyB4KCJDYW4ndCBjYWxsIG1ldGhvZCBvbiAiK3QpO3JldHVybiB0fSxSPU4sRz1BLFY9ZCxZPWZ1bmN0aW9uKHQpe3JldHVybiJvYmplY3QiPT10eXBlb2YgdD9udWxsIT09dDpWKHQpfSxxPXt9LEI9cSxIPXIsSj1kLEs9ZnVuY3Rpb24odCl7cmV0dXJuIEoodCk/dDp2b2lkIDB9LFE9ZnVuY3Rpb24odCxlKXtyZXR1cm4gYXJndW1lbnRzLmxlbmd0aDwyP0soQlt0XSl8fEsoSFt0XSk6Qlt0XSYmQlt0XVtlXXx8SFt0XSYmSFt0XVtlXX0sVT12KHt9LmlzUHJvdG90eXBlT2YpLFc9USgibmF2aWdhdG9yIiwidXNlckFnZW50Iil8fCIiLFg9cixaPVcsJD1YLnByb2Nlc3MsdHQ9WC5EZW5vLGV0PSQmJiQudmVyc2lvbnN8fHR0JiZ0dC52ZXJzaW9uLHJ0PWV0JiZldC52ODtydCYmKEk9KFQ9cnQuc3BsaXQoIi4iKSlbMF0+MCYmVFswXTw0PzE6KyhUWzBdK1RbMV0pKSwhSSYmWiYmKCEoVD1aLm1hdGNoKC9FZGdlXC8oXGQrKS8pKXx8VFsxXT49NzQpJiYoVD1aLm1hdGNoKC9DaHJvbWVcLyhcZCspLykpJiYoST0rVFsxXSk7dmFyIG50PUksb3Q9bixpdD0hIU9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMmJiFvdCgoZnVuY3Rpb24oKXt2YXIgdD1TeW1ib2woKTtyZXR1cm4hU3RyaW5nKHQpfHwhKE9iamVjdCh0KWluc3RhbmNlb2YgU3ltYm9sKXx8IVN5bWJvbC5zaGFtJiZudCYmbnQ8NDF9KSksdXQ9aXQmJiFTeW1ib2wuc2hhbSYmInN5bWJvbCI9PXR5cGVvZiBTeW1ib2wuaXRlcmF0b3IsY3Q9USxhdD1kLGZ0PVUsbHQ9T2JqZWN0LHN0PXV0P2Z1bmN0aW9uKHQpe3JldHVybiJzeW1ib2wiPT10eXBlb2YgdH06ZnVuY3Rpb24odCl7dmFyIGU9Y3QoIlN5bWJvbCIpO3JldHVybiBhdChlKSYmZnQoZS5wcm90b3R5cGUsbHQodCkpfSxwdD1TdHJpbmcsYnQ9ZCx5dD1mdW5jdGlvbih0KXt0cnl7cmV0dXJuIHB0KHQpfWNhdGNoKHQpe3JldHVybiJPYmplY3QifX0sdnQ9VHlwZUVycm9yLGR0PWZ1bmN0aW9uKHQpe2lmKGJ0KHQpKXJldHVybiB0O3Rocm93IHZ0KHl0KHQpKyIgaXMgbm90IGEgZnVuY3Rpb24iKX0saHQ9ZHQsbXQ9aixndD1kLHd0PVksanQ9VHlwZUVycm9yLE90PXtleHBvcnRzOnt9fSxTdD1yLEV0PU9iamVjdC5kZWZpbmVQcm9wZXJ0eSxQdD1mdW5jdGlvbih0LGUpe3RyeXtFdChTdCx0LHt2YWx1ZTplLGNvbmZpZ3VyYWJsZTohMCx3cml0YWJsZTohMH0pfWNhdGNoKHIpe1N0W3RdPWV9cmV0dXJuIGV9LFR0PSJfX2NvcmUtanNfc2hhcmVkX18iLEl0PXJbVHRdfHxQdChUdCx7fSk7KE90LmV4cG9ydHM9ZnVuY3Rpb24odCxlKXtyZXR1cm4gSXRbdF18fChJdFt0XT12b2lkIDAhPT1lP2U6e30pfSkoInZlcnNpb25zIixbXSkucHVzaCh7dmVyc2lvbjoiMy4yMy4zIixtb2RlOiJwdXJlIixjb3B5cmlnaHQ6IsKpIDIwMTQtMjAyMiBEZW5pcyBQdXNoa2FyZXYgKHpsb2lyb2NrLnJ1KSIsbGljZW5zZToiaHR0cHM6Ly9naXRodWIuY29tL3psb2lyb2NrL2NvcmUtanMvYmxvYi92My4yMy4zL0xJQ0VOU0UiLHNvdXJjZToiaHR0cHM6Ly9naXRodWIuY29tL3psb2lyb2NrL2NvcmUtanMifSk7dmFyIGt0PUEsQ3Q9T2JqZWN0LF90PWZ1bmN0aW9uKHQpe3JldHVybiBDdChrdCh0KSl9LEZ0PXYoe30uaGFzT3duUHJvcGVydHkpLEx0PU9iamVjdC5oYXNPd258fGZ1bmN0aW9uKHQsZSl7cmV0dXJuIEZ0KF90KHQpLGUpfSx6dD12LER0PTAsTXQ9TWF0aC5yYW5kb20oKSxOdD16dCgxLi50b1N0cmluZykseHQ9cixBdD1PdC5leHBvcnRzLFJ0PUx0LEd0PWZ1bmN0aW9uKHQpe3JldHVybiJTeW1ib2woIisodm9pZCAwPT09dD8iIjp0KSsiKV8iK050KCsrRHQrTXQsMzYpfSxWdD1pdCxZdD11dCxxdD1BdCgid2tzIiksQnQ9eHQuU3ltYm9sLEh0PUJ0JiZCdC5mb3IsSnQ9WXQ/QnQ6QnQmJkJ0LndpdGhvdXRTZXR0ZXJ8fEd0LEt0PWosUXQ9WSxVdD1zdCxXdD1mdW5jdGlvbih0LGUpe3ZhciByPXRbZV07cmV0dXJuIG51bGw9PXI/dm9pZCAwOmh0KHIpfSxYdD1mdW5jdGlvbih0LGUpe3ZhciByLG47aWYoInN0cmluZyI9PT1lJiZndChyPXQudG9TdHJpbmcpJiYhd3Qobj1tdChyLHQpKSlyZXR1cm4gbjtpZihndChyPXQudmFsdWVPZikmJiF3dChuPW10KHIsdCkpKXJldHVybiBuO2lmKCJzdHJpbmciIT09ZSYmZ3Qocj10LnRvU3RyaW5nKSYmIXd0KG49bXQocix0KSkpcmV0dXJuIG47dGhyb3cganQoIkNhbid0IGNvbnZlcnQgb2JqZWN0IHRvIHByaW1pdGl2ZSB2YWx1ZSIpfSxadD1UeXBlRXJyb3IsJHQ9ZnVuY3Rpb24odCl7aWYoIVJ0KHF0LHQpfHwhVnQmJiJzdHJpbmciIT10eXBlb2YgcXRbdF0pe3ZhciBlPSJTeW1ib2wuIit0O1Z0JiZSdChCdCx0KT9xdFt0XT1CdFt0XTpxdFt0XT1ZdCYmSHQ/SHQoZSk6SnQoZSl9cmV0dXJuIHF0W3RdfSgidG9QcmltaXRpdmUiKSx0ZT1mdW5jdGlvbih0LGUpe2lmKCFRdCh0KXx8VXQodCkpcmV0dXJuIHQ7dmFyIHIsbj1XdCh0LCR0KTtpZihuKXtpZih2b2lkIDA9PT1lJiYoZT0iZGVmYXVsdCIpLHI9S3Qobix0LGUpLCFRdChyKXx8VXQocikpcmV0dXJuIHI7dGhyb3cgWnQoIkNhbid0IGNvbnZlcnQgb2JqZWN0IHRvIHByaW1pdGl2ZSB2YWx1ZSIpfXJldHVybiB2b2lkIDA9PT1lJiYoZT0ibnVtYmVyIiksWHQodCxlKX0sZWU9c3QscmU9ZnVuY3Rpb24odCl7dmFyIGU9dGUodCwic3RyaW5nIik7cmV0dXJuIGVlKGUpP2U6ZSsiIn0sbmU9WSxvZT1yLmRvY3VtZW50LGllPW5lKG9lKSYmbmUob2UuY3JlYXRlRWxlbWVudCksdWU9ZnVuY3Rpb24odCl7cmV0dXJuIGllP29lLmNyZWF0ZUVsZW1lbnQodCk6e319LGNlPSFtJiYhbigoZnVuY3Rpb24oKXtyZXR1cm4gNyE9T2JqZWN0LmRlZmluZVByb3BlcnR5KHVlKCJkaXYiKSwiYSIse2dldDpmdW5jdGlvbigpe3JldHVybiA3fX0pLmF9KSksYWU9bSxmZT1qLGxlPU8sc2U9ayxwZT1mdW5jdGlvbih0KXtyZXR1cm4gUihHKHQpKX0sYmU9cmUseWU9THQsdmU9Y2UsZGU9T2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcjtoLmY9YWU/ZGU6ZnVuY3Rpb24odCxlKXtpZih0PXBlKHQpLGU9YmUoZSksdmUpdHJ5e3JldHVybiBkZSh0LGUpfWNhdGNoKHQpe31pZih5ZSh0LGUpKXJldHVybiBzZSghZmUobGUuZix0LGUpLHRbZV0pfTt2YXIgaGU9bixtZT1kLGdlPS8jfFwucHJvdG90eXBlXC4vLHdlPWZ1bmN0aW9uKHQsZSl7dmFyIHI9T2VbamUodCldO3JldHVybiByPT1FZXx8ciE9U2UmJihtZShlKT9oZShlKTohIWUpfSxqZT13ZS5ub3JtYWxpemU9ZnVuY3Rpb24odCl7cmV0dXJuIFN0cmluZyh0KS5yZXBsYWNlKGdlLCIuIikudG9Mb3dlckNhc2UoKX0sT2U9d2UuZGF0YT17fSxTZT13ZS5OQVRJVkU9Ik4iLEVlPXdlLlBPTFlGSUxMPSJQIixQZT13ZSxUZT1kdCxJZT1vLGtlPXYodi5iaW5kKSxDZT17fSxfZT1tJiZuKChmdW5jdGlvbigpe3JldHVybiA0MiE9T2JqZWN0LmRlZmluZVByb3BlcnR5KChmdW5jdGlvbigpe30pLCJwcm90b3R5cGUiLHt2YWx1ZTo0Mix3cml0YWJsZTohMX0pLnByb3RvdHlwZX0pKSxGZT1ZLExlPVN0cmluZyx6ZT1UeXBlRXJyb3IsRGU9bSxNZT1jZSxOZT1fZSx4ZT1mdW5jdGlvbih0KXtpZihGZSh0KSlyZXR1cm4gdDt0aHJvdyB6ZShMZSh0KSsiIGlzIG5vdCBhbiBvYmplY3QiKX0sQWU9cmUsUmU9VHlwZUVycm9yLEdlPU9iamVjdC5kZWZpbmVQcm9wZXJ0eSxWZT1PYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yLFllPSJlbnVtZXJhYmxlIixxZT0iY29uZmlndXJhYmxlIixCZT0id3JpdGFibGUiO0NlLmY9RGU/TmU/ZnVuY3Rpb24odCxlLHIpe2lmKHhlKHQpLGU9QWUoZSkseGUociksImZ1bmN0aW9uIj09dHlwZW9mIHQmJiJwcm90b3R5cGUiPT09ZSYmInZhbHVlImluIHImJkJlIGluIHImJiFyLndyaXRhYmxlKXt2YXIgbj1WZSh0LGUpO24mJm4ud3JpdGFibGUmJih0W2VdPXIudmFsdWUscj17Y29uZmlndXJhYmxlOnFlIGluIHI/ci5jb25maWd1cmFibGU6bi5jb25maWd1cmFibGUsZW51bWVyYWJsZTpZZSBpbiByP3IuZW51bWVyYWJsZTpuLmVudW1lcmFibGUsd3JpdGFibGU6ITF9KX1yZXR1cm4gR2UodCxlLHIpfTpHZTpmdW5jdGlvbih0LGUscil7aWYoeGUodCksZT1BZShlKSx4ZShyKSxNZSl0cnl7cmV0dXJuIEdlKHQsZSxyKX1jYXRjaCh0KXt9aWYoImdldCJpbiByfHwic2V0ImluIHIpdGhyb3cgUmUoIkFjY2Vzc29ycyBub3Qgc3VwcG9ydGVkIik7cmV0dXJuInZhbHVlImluIHImJih0W2VdPXIudmFsdWUpLHR9O3ZhciBIZT1DZSxKZT1rLEtlPW0/ZnVuY3Rpb24odCxlLHIpe3JldHVybiBIZS5mKHQsZSxKZSgxLHIpKX06ZnVuY3Rpb24odCxlLHIpe3JldHVybiB0W2VdPXIsdH0sUWU9cixVZT1mLFdlPXYsWGU9ZCxaZT1oLmYsJGU9UGUsdHI9cSxlcj1mdW5jdGlvbih0LGUpe3JldHVybiBUZSh0KSx2b2lkIDA9PT1lP3Q6SWU/a2UodCxlKTpmdW5jdGlvbigpe3JldHVybiB0LmFwcGx5KGUsYXJndW1lbnRzKX19LHJyPUtlLG5yPUx0LG9yPWZ1bmN0aW9uKHQpe3ZhciBlPWZ1bmN0aW9uKHIsbixvKXtpZih0aGlzIGluc3RhbmNlb2YgZSl7c3dpdGNoKGFyZ3VtZW50cy5sZW5ndGgpe2Nhc2UgMDpyZXR1cm4gbmV3IHQ7Y2FzZSAxOnJldHVybiBuZXcgdChyKTtjYXNlIDI6cmV0dXJuIG5ldyB0KHIsbil9cmV0dXJuIG5ldyB0KHIsbixvKX1yZXR1cm4gVWUodCx0aGlzLGFyZ3VtZW50cyl9O3JldHVybiBlLnByb3RvdHlwZT10LnByb3RvdHlwZSxlfSxpcj1mdW5jdGlvbih0LGUpe3ZhciByLG4sbyxpLHUsYyxhLGYsbD10LnRhcmdldCxzPXQuZ2xvYmFsLHA9dC5zdGF0LGI9dC5wcm90byx5PXM/UWU6cD9RZVtsXTooUWVbbF18fHt9KS5wcm90b3R5cGUsdj1zP3RyOnRyW2xdfHxycih0cixsLHt9KVtsXSxkPXYucHJvdG90eXBlO2ZvcihvIGluIGUpcj0hJGUocz9vOmwrKHA/Ii4iOiIjIikrbyx0LmZvcmNlZCkmJnkmJm5yKHksbyksdT12W29dLHImJihjPXQuZG9udENhbGxHZXRTZXQ/KGY9WmUoeSxvKSkmJmYudmFsdWU6eVtvXSksaT1yJiZjP2M6ZVtvXSxyJiZ0eXBlb2YgdT09dHlwZW9mIGl8fChhPXQuYmluZCYmcj9lcihpLFFlKTp0LndyYXAmJnI/b3IoaSk6YiYmWGUoaSk/V2UoaSk6aSwodC5zaGFtfHxpJiZpLnNoYW18fHUmJnUuc2hhbSkmJnJyKGEsInNoYW0iLCEwKSxycih2LG8sYSksYiYmKG5yKHRyLG49bCsiUHJvdG90eXBlIil8fHJyKHRyLG4se30pLHJyKHRyW25dLG8saSksdC5yZWFsJiZkJiYhZFtvXSYmcnIoZCxvLGkpKSl9LHVyPXYoW10uc2xpY2UpLGNyPVR5cGVFcnJvcixhcj1yLGZyPWYsbHI9ZCxzcj11cixwcj1mdW5jdGlvbih0LGUpe2lmKHQ8ZSl0aHJvdyBjcigiTm90IGVub3VnaCBhcmd1bWVudHMiKTtyZXR1cm4gdH0sYnI9L01TSUUgLlwuLy50ZXN0KFcpLHlyPWFyLkZ1bmN0aW9uLHZyPWZ1bmN0aW9uKHQpe3JldHVybiBicj9mdW5jdGlvbihlLHIpe3ZhciBuPXByKGFyZ3VtZW50cy5sZW5ndGgsMSk+MixvPWxyKGUpP2U6eXIoZSksaT1uP3NyKGFyZ3VtZW50cywyKTp2b2lkIDA7cmV0dXJuIHQobj9mdW5jdGlvbigpe2ZyKG8sdGhpcyxpKX06byxyKX06dH0sZHI9e3NldFRpbWVvdXQ6dnIoYXIuc2V0VGltZW91dCksc2V0SW50ZXJ2YWw6dnIoYXIuc2V0SW50ZXJ2YWwpfSxocj1kci5zZXRJbnRlcnZhbDtpcih7Z2xvYmFsOiEwLGJpbmQ6ITAsZm9yY2VkOnIuc2V0SW50ZXJ2YWwhPT1ocn0se3NldEludGVydmFsOmhyfSk7dmFyIG1yPWRyLnNldFRpbWVvdXQ7aXIoe2dsb2JhbDohMCxiaW5kOiEwLGZvcmNlZDpyLnNldFRpbWVvdXQhPT1tcn0se3NldFRpbWVvdXQ6bXJ9KTt2YXIgZ3I9cS5zZXRJbnRlcnZhbCx3cj0hMSxqcj1udWxsO3NlbGYuYWRkRXZlbnRMaXN0ZW5lcigibWVzc2FnZSIsKGZ1bmN0aW9uKHQpe3ZhciBlPXQuZGF0YSxyPWUudHlwZSxuPWUuZGF0YTtzd2l0Y2gocil7Y2FzZSJzdGFydCI6d3J8fCh3cj0hMCxqcj1ncigoZnVuY3Rpb24oKXt3cj0hMSxzZWxmLnBvc3RNZXNzYWdlKCJ0aWNrIil9KSxuLnRpbWUpKTticmVhaztjYXNlInN0b3AiOmNsZWFySW50ZXJ2YWwoanIpLHdyPSExfX0pLCExKTtyZXR1cm57fX0oKTsKCg==",lP=null,dP=!1,function(t){return pP=pP||cP(uP,lP,dP),new Worker(pP,t)}),hP=function(){function t(t){void 0===t&&(t=60),this.times=60,this.worker=null,this.times=t,this.worker=new fP}return t.prototype.tick=function(t){this.requestAnimationFrameWrapper(t)},t.prototype.destroy=function(){var t;null===(t=this.worker)||void 0===t||t.terminate(),this.worker=null},t.prototype.setWorkerInterval=function(t,e){var r,n,i=this;null===(r=this.worker)||void 0===r||r.addEventListener("message",t),null===(n=this.worker)||void 0===n||n.postMessage({type:"start",data:{time:e}});return function(){var e,r;null===(e=i.worker)||void 0===e||e.postMessage({type:"stop"}),null===(r=i.worker)||void 0===r||r.removeEventListener("message",t)}},t.prototype.requestAnimationFrameWrapper=function(t){var e=!1,r=this.setWorkerInterval((function(){!e&&document.hidden&&(e=!0,r(),t())}),1e3/this.times);requestAnimationFrame((function(){e||(e=!0,r(),t())}))},t}(),mP={exports:{}};!function(t,e){self,t.exports=function(){var t={d:function(e,r){for(var n in r)t.i(r,n)&&!t.i(e,n)&&Tf(e,n,{enumerable:!0,get:r[n]})},i:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}},e={};t.d(e,{default:function(){return l}});var r=function(t,e,r,n){return new(r||(r=RR))((function(i,o){function a(t){try{c(n.next(t))}catch(t){o(t)}}function s(t){try{c(n.h(t))}catch(t){o(t)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(a,s)}c((n=n.apply(t,e||[])).next())}))},n=function(t,e){var r,n,i,o,a={label:0,o:function(){if(1&i[0])throw i[1];return i[1]},u:[],v:[]};return o={next:s(0),h:s(1),l:s(2)},"function"==typeof kp&&(o[Yp]=function(){return this}),o;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(i=2&s[0]?n.l:s[0]?n.h||((i=n.l)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.v.pop(),a.u.pop();continue;default:if(!((i=(i=a.u).length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){a.label=s[1];break}if(6===s[0]&&a.label<i[1]){a.label=i[1],i=s;break}if(i&&a.label<i[2]){a.label=i[2],a.v.push(s);break}i[2]&&a.v.pop(),a.u.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],n=0}finally{r=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},i=function(){function t(){this.items=[]}return t.prototype.M=function(){this.items=[]},t.prototype.m=function(){return this.items[0]},t.prototype.pop=function(){return this.items.shift()},t.prototype.size=function(){return this.items.length},t.prototype.p=function(){return 0===this.items.length},t.prototype.k=function(){return this.items.length<=1?0:(this.items[this.items.length-1].timestamp-this.items[0].timestamp)/90},t.prototype.N=function(t){for(var e,r=!1,n=this.items.length,i=0;i<this.items.length;i++)if(t.timestamp<this.items[i].timestamp){n=i,r=!0;break}return bE(e=this.items).call(e,n,0,t),r},t}(),o=function(){function t(t){this.audioSampleRate=48,this.items=[],this.audioSampleRate=t}return t.prototype.M=function(){this.items=[]},t.prototype.m=function(){return this.items[0]},t.prototype.pop=function(){return this.items.shift()},t.prototype.size=function(){return this.items.length},t.prototype.p=function(){return 0===this.items.length},t.prototype.k=function(){return this.items.length<=1?0:(this.items[this.items.length-1].timestamp-this.items[0].timestamp)/this.audioSampleRate},t.prototype.N=function(t){for(var e,r=this.items.length,n=0;n<this.items.length;n++)if(t.timestamp<this.items[n].timestamp){r=n;break}bE(e=this.items).call(e,r,0,t)},t}(),a=function(){function t(){this.T=0,this.j=0,this.A=0,this.C=0}return t.prototype.F=function(t,e){if(0===this.A)return this.A=e,this.j=t,0;var r=this.T;this.O(t);var n=this.T-r;if(0===n&&t<this.j||n<0)return 0;this.C=Math.round((t+1*n-this.j)/90);var i=Math.floor(e-this.A-this.C);return this.j=t,this.A=e,i},t.prototype.O=function(t){t<this.j?Math.floor(t-this.j)>0&&(this.T=this.T+1):Math.floor(this.j-t)>0&&(this.T=this.T-1)},t}(),s=function(){function t(){this.size=0,this.R=0,this.S=0,this.max=Number.MIN_VALUE,this.min=Number.MAX_VALUE}return t.prototype.G=function(t){this.max=Math.max(this.max,t),this.min=Math.min(this.min,t),this.size=this.size+1;var e=t-this.R;this.R+=e/this.size;var r=t-this.R;this.S+=e*r},t.prototype.P=function(t){if(0!==this.V()){this.size=this.size-1;var e=t-this.R;this.R-=e/this.size;var r=t-this.R;this.S-=e*r}},t.prototype.V=function(){return this.size},t.prototype.q=function(){return 0===this.size?0:this.R},t.prototype.B=function(){return 0===this.size?Number.MAX_VALUE:this.min},t.prototype.D=function(){return 0===this.size?Number.MIN_VALUE:this.max},t.prototype.H=function(){return 0===this.size?Number.MIN_VALUE:this.S/this.size},t}(),c=function(){function t(){this.I=new s,this.J=new Array(30),this.K=0,this.L=!1,this.U=!1,this.max=Number.MIN_VALUE,this.min=Number.MAX_VALUE}return t.prototype.W=function(){return this.J.length},t.prototype.count=function(){return this.I.V()},t.prototype.G=function(t){if(this.count()===this.W()){var e=this.J[this.K];this.I.P(e),e>=this.max&&(this.L=!0),e<=this.min&&(this.U=!0)}this.J[this.K]=t,(0===this.count()||t>=this.max)&&(this.max=t,this.L=!1),(0===this.count()||t<=this.min)&&(this.min=t,this.U=!1),this.I.G(t),this.K=(this.K+1)%this.W()},t.prototype.X=function(){return this.I.q()},t.prototype.Y=function(){if(this.L){this.max=this.J[this.K];for(var t=1;t<this.count();t++)this.max=Math.max(this.max,this.J[(this.K+t)%this.W()]);this.L=!1}return this.max},t.prototype.Z=function(){if(this.U){this.min=this.J[this.K];for(var t=1;t<this.count();t++)this.min=Math.min(this.min,this.J[(this.K+t)%this.W()]);this.U=!1}return this.min},t}(),u=function(){function t(){this.$=new c,this._=.97,this.tt=.9999,this.it=400,this.ht=1e-6,this.st=3,this.nt=15,this.rt=3,this.ot=2.33,this.ut=30,this.et=[[1e-4,0],[0,100]],this.ft=[[2.5e-10,0],[0,1e-10]],this.ct=500,this.vt=100,this.lt=500,this.dt=0,this.Mt=0,this.bt=-1,this.wt=-1,this.yt=0,this.kt=0,this.Nt=1,this.Tt=0,this.jt=0,this.xt=0,this.Et=0,this.gt=0,this.At=0,this.Ct=5,this.Ft=3.5,this.Ot=[1/64e3,0],this.Rt=4}return t.prototype.St=function(t,e){var r=!1;if(0!==e){var n=e-this.yt;this.Mt<5?(this.dt+=e,this.Mt=this.Mt+1):5===this.Mt&&(this.ct=this.dt/this.Mt,this.Mt=this.Mt+1);var i=this._*this.ct+(1-this._)*e;if(e<this.ct+2*Math.sqrt(this.vt)&&(this.ct=i),this.vt=Math.max(this._*this.vt+(1-this._)*(e-i)*(e-i),1),this.lt=Math.max(this.tt*this.lt,e),0!==this.yt){this.yt=e;var o=Math.round(this.Ft*Math.sqrt(this.Rt)),a=Math.max(Math.min(t,o),-o),s=this.Gt(a,n);if(Math.abs(s)<this.nt*Math.sqrt(this.Rt)||e>this.ct+this.rt*Math.sqrt(this.vt))this.Pt(s,r),n>-.25*this.lt&&this.Vt(a,n);else{var c=s>=0?this.nt:-this.nt;this.Pt(c*Math.sqrt(this.Rt),r)}this.jt>=30?this.qt():this.jt=this.jt+1}else this.yt=e}},t.prototype.zt=function(){this.gt<this.Ct&&(this.gt=this.gt+1),this.At=performance.now()},t.prototype.Bt=function(t){var e=this.Dt()+10,r=performance.now();r-this.xt>6e4&&(this.Et=0),this.gt>0&&r-this.At>5e3&&(this.gt=this.gt-1,this.At=r),this.Tt>e&&(e=this.Tt),this.Et,this.st,this.gt>0&&(e+=50*this.gt*t);var n=this.Ht();return n<5?0===n?Math.round(Math.max(0,e)):0:(n<10&&(e*=.2*(n-5)),Math.round(Math.max(0,e)))},t.prototype.Gt=function(t,e){return t-(this.Ot[0]*e+this.Ot[1])},t.prototype.Pt=function(t,e){var r=performance.now();if(-1!==this.bt&&this.$.G(r-this.bt),this.bt=r,0!==this.Nt){var n=(this.Nt-1)/this.Nt;this.Nt=this.Nt+1,this.Nt>this.it&&(this.Nt=this.it);var i=this.Ht();if(i>0){var o=30/i;this.Nt<30&&(o=(this.Nt*o+(30-this.Nt))/30),n=Math.pow(n,o)}var a=n*this.kt+(1-n)*t,s=n*this.Rt+(1-n)*(t-this.kt)*(t-this.kt);(!e||s>this.Rt)&&(this.kt=a,this.Rt=s),this.Rt<1&&(this.Rt=1)}},t.prototype.Ht=function(){if(this.$.X()<=0)return 0;var t=1e3/this.$.X();return t>200&&(t=200),t},t.prototype.Vt=function(t,e){var r=[0,0],n=[0,0];if(this.et[0][0]+=this.ft[0][0],this.et[0][1]+=this.ft[0][1],this.et[1][0]+=this.ft[1][0],this.et[1][1]+=this.ft[1][1],r[0]=this.et[0][0]*e+this.et[0][1],r[1]=this.et[1][0]*e+this.et[1][1],!(this.lt<1)){var i=(300*Math.exp(-Math.abs(e)/(1*this.lt))+1)*Math.sqrt(this.Rt);i<1&&(i=1);var o=e*r[0]+r[1]+i;if(!(o<1e-9&&o>=0||o>-1e-9&&o<=0)){n[0]=r[0]/o,n[1]=r[1]/o;var a=t-(e*this.Ot[0]+this.Ot[1]);this.Ot[0]+=n[0]*a,this.Ot[1]+=n[1]*a,this.Ot[0]<this.ht&&(this.Ot[0]=this.ht);var s=this.et[0][0],c=this.et[0][1];this.et[0][0]=(1-n[0]*e)*s-n[0]*this.et[1][0],this.et[0][1]=(1-n[0]*e)*c-n[0]*this.et[1][1],this.et[1][0]=this.et[1][0]*(1-n[1])-n[1]*e*s,this.et[1][1]=this.et[1][1]*(1-n[1])-n[1]*e*c}}},t.prototype.qt=function(){this.Tt=this.Dt()},t.prototype.It=function(){var t=this.ot*Math.sqrt(this.Rt)-this.ut;return t<1&&(t=1),t},t.prototype.Dt=function(){var t=this.Ot[0]*(this.lt-this.ct)+this.It();return t<1&&(t=this.wt<=.01?1:this.wt),t>1e4&&(t=1e4),this.wt=t,t},t}(),l=function(){function t(t){this.Jt=new i,this.Kt=null,this.Lt=new a,this.Qt=new u,this.Ut=null,this.Wt=null,this.Xt=!1,this.Yt=0,this.Zt=0,this.$t=!1,this._t=0,this.ti=0,this.startTime=0,this.audioSampleRate=48,this.maxJitterDelay=1e3,this.minJitterDelay=100,this.ii=200,this.audioSampleRate=t.audioSampleRate,this.maxJitterDelay=t.maxJitterDelay,this.minJitterDelay=t.minJitterDelay,this.Kt=new o(this.audioSampleRate)}return t.create=function(e){return new t(e)},t.prototype.getVersion=function(){return"1.0.0"},t.prototype.destroy=function(){var t,e;null===(t=this.Jt)||void 0===t||t.M(),null===(e=this.Kt)||void 0===e||e.M()},t.prototype.enqueue=function(t,e){var r,n;if(this.startTime||(this.startTime=performance.now()),t instanceof RTCEncodedVideoFrame)(null===(r=this.Jt)||void 0===r?void 0:r.N(t))&&this.zt(),this.Ut=e,this.hi(t),this.ii=this.Bt(),this.si();else{if(!(t instanceof RTCEncodedAudioFrame))throw new Error("bad chunk type!");null===(n=this.Kt)||void 0===n||n.N(t),this.Wt=e,this.ni()}},t.prototype.getVideoJitterDelay=function(){var t;return null===(t=this.Jt)||void 0===t?void 0:t.k()},t.prototype.getAudioJitterDelay=function(){var t;return null===(t=this.Kt)||void 0===t?void 0:t.k()},t.prototype.zt=function(){var t;null===(t=this.Qt)||void 0===t||t.zt()},t.prototype.hi=function(t){var e,r=null===(e=this.Lt)||void 0===e?void 0:e.F(t.timestamp,performance.now());0!==r&&this.Qt.St(r,t.data.byteLength)},t.prototype.Bt=function(){var t;return t=this.Qt.Bt(1),t=Math.min(t,this.ii+100),t=Math.max(t,this.ii-100),t=Math.min(t,this.maxJitterDelay),Math.max(t,this.minJitterDelay)},t.prototype.si=function(){var t,e,i,o,a,s,c,u,l,d,p=this;if(0!==(null===(t=this.Jt)||void 0===t?void 0:t.size())){if(!this.Xt)return this.Yt=(null===(i=null===(e=this.Jt)||void 0===e?void 0:e.m())||void 0===i?void 0:i.timestamp)/90,this.Zt=performance.now(),this.Ut.enqueue(null===(o=this.Jt)||void 0===o?void 0:o.pop()),void(this.Xt=!0);var f=null===(a=this.Jt)||void 0===a?void 0:a.k(),h=(null===(c=null===(s=this.Jt)||void 0===s?void 0:s.m())||void 0===c?void 0:c.timestamp)/90-this.Yt;h=Math.min(h,200);var m=h=Math.max(h,16);f>this.maxJitterDelay?m=10:f>this.ii+300?m=.9*h:f>this.ii+100?m=.95*h:f<this.ii&&(m=1.1*h),performance.now()-this.Zt>m&&(this.Yt=(null===(l=null===(u=this.Jt)||void 0===u?void 0:u.m())||void 0===l?void 0:l.timestamp)/90,this.Ut.enqueue(null===(d=this.Jt)||void 0===d?void 0:d.pop()),this.Zt=performance.now()),f>this.ii+200&&sc((function(){return v(p)}),0)}function v(t){var e,i,o,a,s,c,u,l;return r(this,void 0,void 0,(function(){var r,d,p;return n(this,(function(n){return(null===(e=t.Jt)||void 0===e?void 0:e.k())<t.ii||(r=null===(i=t.Jt)||void 0===i?void 0:i.k(),d=(null===(a=null===(o=t.Jt)||void 0===o?void 0:o.m())||void 0===a?void 0:a.timestamp)/90-t.Yt,p=d,r>t.maxJitterDelay?p=0:r>t.ii+400?p=.9*d:r>t.ii+200?p=.95*d:r<t.ii&&(p=1.1*d),performance.now()-t.Zt>p&&(null===(s=t.Jt)||void 0===s?void 0:s.k())>0?(t.Yt=(null===(u=null===(c=t.Jt)||void 0===c?void 0:c.m())||void 0===u?void 0:u.timestamp)/90,t.Ut.enqueue(null===(l=t.Jt)||void 0===l?void 0:l.pop()),t.Zt=performance.now(),sc((function(){return v(t)}),p)):sc((function(){return v(t)}),performance.now()-t.Zt-p)),[2]}))}))}},t.prototype.ni=function(){for(var t,e,r,n,i,o,a,s=(null===(e=null===(t=this.Kt)||void 0===t?void 0:t.m())||void 0===e?void 0:e.timestamp)/this.audioSampleRate;(this.Yt+100>s||!this.Yt)&&(null===(r=this.Kt)||void 0===r?void 0:r.k())>0||(null===(n=this.Kt)||void 0===n?void 0:n.k())>this.maxJitterDelay;)this._t=(null===(o=null===(i=this.Kt)||void 0===i?void 0:i.m())||void 0===o?void 0:o.timestamp)/this.audioSampleRate,s=this._t,this.Wt.enqueue(null===(a=this.Kt)||void 0===a?void 0:a.pop())},t}();return e.default}()}(mP);var vP=mP.exports,yP=function(){function t(){var t,e,r,n,i,o,a,s;this.peerConnection=null,this.dataChannel=null,this.clientSideDescription=null,this.serverSideDescription=null,this.connectStatus=IE.DISCONNECTED,this.connectDirection=DE.RECEIVE,this.connectConfig=null,this.negotiating=!1,this.ticker=null,this.seiDataMap=null,this.lebEQ=null,this.onAddTrack=null,this.onConnect=null,this.onDisconnect=null,this.onSetLocalDescription=null,this.onError=null,this.onReceiveSEI=null,this.onTrack=jr(t=this.onTrack).call(t,this),this.onIceConnectionStateChange=jr(e=this.onIceConnectionStateChange).call(e,this),this.onSignalingStateChange=jr(r=this.onSignalingStateChange).call(r,this),this.onConnectionStateChange=jr(n=this.onConnectionStateChange).call(n,this),this.onNegotiationNeeded=jr(i=this.onNegotiationNeeded).call(i,this),this.onDataChannelOpen=jr(o=this.onDataChannelOpen).call(o,this),this.onDataChannelMessage=jr(a=this.onDataChannelMessage).call(a,this),this.handleSeiData=jr(s=this.handleSeiData).call(s,this)}return t.prototype.init=function(t){var e=void 0===t?{}:t,r=e.onAddTrack,n=void 0===r?null:r,i=e.onConnect,o=void 0===i?null:i,a=e.onDisconnect,s=void 0===a?null:a,c=e.onSetLocalDescription,u=void 0===c?null:c,l=e.onError,d=void 0===l?null:l,p=e.onReceiveSEI,f=void 0===p?null:p;this.onAddTrack=n,this.onConnect=o,this.onDisconnect=s,this.onSetLocalDescription=u,this.onError=d,this.onReceiveSEI=f},t.prototype.initWebRTCConnect=function(t){var e,r=void 0===t?{}:t,n=r.config,i=void 0===n?{}:n,o=r.direction,a=void 0===o?DE.RECEIVE:o,s=r.stream;sP.log("WebRTC init"),this.peerConnection?sP.log("peerConnection is existed"):(this.connectDirection=a,this.connectConfig=null!==(e=i.connection)&&void 0!==e?e:{},this.createWebRTCConnect(i,s))},t.prototype.connect=function(t){t&&this.onAnswer(t)},t.prototype.disconnect=function(t){var e,r=void 0===t?{}:t,n=r.msg,i=void 0===n?"":n,o=r.code,a=void 0===o?LE.MANUAL_CLOSE:o;sP.log("connection close".concat(a===LE.NEED_RECONNECT?", needs to reconnect":"")),this.connectStatus!==IE.DISCONNECTED&&(this.connectStatus=IE.DISCONNECTED,this.negotiating=!1,this.peerConnection&&(this.peerConnection.removeEventListener("track",this.onTrack),this.peerConnection.removeEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.peerConnection.removeEventListener("signalingstatechange",this.onSignalingStateChange),this.peerConnection.removeEventListener("connectionstatechange",this.onConnectionStateChange),this.peerConnection.removeEventListener("negotiationneeded",this.onNegotiationNeeded),this.peerConnection.close(),this.peerConnection=null),this.dataChannel&&(this.dataChannel.removeEventListener("open",this.onDataChannelOpen),this.dataChannel.removeEventListener("message",this.onDataChannelMessage),this.dataChannel.close(),this.dataChannel=null),this.seiDataMap&&(this.seiDataMap.clear(),this.seiDataMap=null),this.ticker&&(this.ticker.destroy(),this.ticker=null),this.lebEQ&&(this.lebEQ.destroy(),this.lebEQ=null),this.clientSideDescription=null,this.serverSideDescription=null,null===(e=this.onDisconnect)||void 0===e||e.call(this,{code:a,msg:i}))},t.prototype.getClientSideDescription=function(){return this.clientSideDescription||sP.log("WebRTC is not initialized"),this.clientSideDescription},t.prototype.getConnectStatus=function(){return this.connectStatus},t.prototype.getStats=function(){return Zc(this,void 0,void 0,(function(){return Yc(this,(function(t){switch(t.label){case 0:return this.peerConnection?[4,this.peerConnection.getStats(null)]:[2];case 1:return[2,t.sent()]}}))}))},t.prototype.createWebRTCConnect=function(t,e){var r,n,i,o=this,a=null!==(r=t.connection)&&void 0!==r?r:{},s=a.isReceiveSEI,c=void 0!==s&&s,u=a.isCreateSEIDataChannel,l=void 0!==u&&u;try{var d={iceServers:[],sdpSemantics:"unified-plan",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",tcpCandidatePolicy:"disable",IceTransportsType:"nohost"};aw?(sP.log("enable insertable streams"),d.encodedInsertableStreams=!0,this.lebEQ=vP.create({audioSampleRate:48,maxJitterDelay:1e3,minJitterDelay:100}),sP.log("create LebEQ, version is ".concat(this.lebEQ.getVersion()))):sP.log("insertable streams is not supported"),this.peerConnection=new RTCPeerConnection(d)}catch(t){var p;return sP.log(t),void("ReferenceError"===t.name&&Hi(p=t.message).call(p,"RTCPeerConnection")&&(sP.log("WebRTC is not supported"),null===(n=this.onError)||void 0===n||n.call(this,"WebRTC is not supported")))}if(this.connectStatus=IE.CONNECTING,this.negotiating=!1,this.peerConnection.addEventListener("track",this.onTrack),this.peerConnection.addEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.peerConnection.addEventListener("signalingstatechange",this.onSignalingStateChange),this.peerConnection.addEventListener("connectionstatechange",this.onConnectionStateChange),this.peerConnection.addEventListener("negotiationneeded",this.onNegotiationNeeded),c&&!aw&&l)if(sw){sP.log("create data channel to receive sei messages");var f=ZE,h=YE;this.dataChannel=this.peerConnection.createDataChannel(f,{protocol:h,ordered:!1,maxPacketLifeTime:100}),this.dataChannel.binaryType="arraybuffer",this.ticker=new hP(40),this.seiDataMap=new Bb,this.dataChannel.addEventListener("open",this.onDataChannelOpen),this.dataChannel.addEventListener("message",this.onDataChannelMessage)}else sP.log("RTCRtpReceiver getSynchronizationSources is not supported");this.connectDirection===DE.SEND&&e instanceof MediaStream&&Hs(i=e.getTracks()).call(i,(function(t){o.peerConnection.addTrack(t,e)}));this.connectDirection===DE.RECEIVE&&this.createOffer(t.offer)},t.prototype.onTrack=function(t){var e,r,n,i=this;sP.log("".concat(t.track.kind," track added to peerConnection")),null===(e=this.onAddTrack)||void 0===e||e.call(this,t.track);var o=this.connectConfig.isReceiveSEI,a=void 0!==o&&o;if(aw)if("video"===t.track.kind){sP.log("insertable streams for video is started");try{var s=t.receiver.createEncodedStreams(),c=new TransformStream({transform:function(t,e){var r,n;if(a){var o=function(t){var e=new DataView(t);if(e.byteLength<3)return[];for(var r=[],n=0;n<e.byteLength-2;n++)if(0===e.getUint8(n)&&0===e.getUint8(n+1)&&1===e.getUint8(n+2)){var i=3==(c=n>0&&0===e.getUint8(n-1)?4:3)?n:n-1;r.push({startOffset:i,startCodeSize:c}),n+=2}for(var o=[],a=0;a<r.length;a++){for(var s=r[a],c=(i=s.startOffset,s.startCodeSize),u=i,l=r[a+1]?r[a+1].startOffset:e.byteLength,d=31&e.getUint8(u+c),p=l-u-c-1,f=new Uint8Array(p),h=0;h<p;h++)f[h]=e.getUint8(u+c+1+h);o.push({nalUnitType:d,rbspData:f})}return o}(t.data);try{for(var s=Hc(o),c=s.next();!c.done;c=s.next()){var u=c.value,l=u.nalUnitType,d=u.rbspData;if(6===l){var p=oP(d);Hs(p).call(p,(function(t){var e;null===(e=i.onReceiveSEI)||void 0===e||e.call(i,t)}))}}}catch(t){r={error:t}}finally{try{c&&!c.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}}i.lebEQ?i.lebEQ.enqueue(t,e):e.enqueue(t)}});s.readable.pipeThrough(c).pipeTo(s.writable)}catch(t){sP.log("insertable streams for video has error,",t),null===(r=this.onError)||void 0===r||r.call(this,"parse video data is failed")}}else{sP.log("insertable streams for audio is started");try{s=t.receiver.createEncodedStreams(),c=new TransformStream({transform:function(t,e){i.lebEQ?i.lebEQ.enqueue(t,e):e.enqueue(t)}});s.readable.pipeThrough(c).pipeTo(s.writable)}catch(t){sP.log("insertable streams for audio has error,",t),null===(n=this.onError)||void 0===n||n.call(this,"parse audio data is failed")}}},t.prototype.onIceConnectionStateChange=function(){var t;switch(sP.log("onIceConnectionStateChange",this.peerConnection.iceConnectionState),this.peerConnection.iceConnectionState){case"failed":case"disconnected":sP.log("iceConnection disconnected"),this.disconnect({code:LE.NEED_RECONNECT,msg:"Connection disconnected, please try again"});break;case"connected":case"completed":if(!tw)return;if(this.connectStatus===IE.CONNECTED)return;sP.log("iceConnection connected"),this.connectStatus=IE.CONNECTED,null===(t=this.onConnect)||void 0===t||t.call(this,{code:xE.SUCCESS,msg:"Connection connected"})}},t.prototype.onSignalingStateChange=function(){sP.log("onSignalingStateChange",this.peerConnection.signalingState)},t.prototype.onConnectionStateChange=function(){var t;switch(sP.log("onConnectionStateChange",this.peerConnection.connectionState),this.peerConnection.connectionState){case"failed":case"disconnected":sP.log("connection disconnected"),this.disconnect({code:LE.NEED_RECONNECT,msg:"Connection disconnected, please try again"});break;case"connected":if(this.connectStatus===IE.CONNECTED)return;sP.log("connection connected"),this.connectStatus=IE.CONNECTED,null===(t=this.onConnect)||void 0===t||t.call(this,{code:xE.SUCCESS,msg:"Connection connected"})}},t.prototype.onNegotiationNeeded=function(){if(sP.log("peerConnection need negotiation"),this.connectDirection!==DE.RECEIVE)try{if(this.negotiating||"stable"!==this.peerConnection.signalingState)return;this.negotiating=!0,this.createOffer()}catch(t){sP.log("onNegotiationNeeded error,",t)}finally{this.negotiating=!1}},t.prototype.createOffer=function(t){var e;return void 0===t&&(t={}),Zc(this,void 0,void 0,(function(){var r,n,i,o,a,s,c;return Yc(this,(function(u){switch(u.label){case 0:r=t.offerToReceiveVideo,n=void 0===r||r,i=t.offerToReceiveAudio,o=void 0===i||i,a={voiceActivityDetection:!1},this.connectDirection===DE.RECEIVE&&(!!this.peerConnection.addTransceiver?(sP.log("peerConnection addTransceiver"),o&&this.peerConnection.addTransceiver("audio",{direction:"recvonly"}),n&&this.peerConnection.addTransceiver("video",{direction:"recvonly"})):(sP.log("init createOffer options"),a=Wc(Wc({},a),{offerToReceiveVideo:n,offerToReceiveAudio:o}))),u.label=1;case 1:return u.trys.push([1,3,,4]),sP.log("createOffer start"),[4,this.peerConnection.createOffer(a)];case 2:return s=u.sent(),sP.log("createOffer success"),this.onOffer(s),[3,4];case 3:return c=u.sent(),sP.log("create offer error,",c),this.disconnect({code:LE.NEED_RECONNECT,msg:"create offer is failed"}),null===(e=this.onError)||void 0===e||e.call(this,"create offer is failed"),[3,4];case 4:return[2]}}))}))},t.prototype.onOffer=function(t){var e,r;return Zc(this,void 0,void 0,(function(){var n,i;return Yc(this,(function(o){switch(o.label){case 0:n=t.sdp,n=this.connectDirection===DE.RECEIVE?ww(n):Ew(a=n.split("\r\n")).call(a,(function(t){return Hi(t).call(t,"a=fmtp:111")?"".concat(t,";stereo=1"):t})).join("\r\n"),t.sdp=n,o.label=1;case 1:return o.trys.push([1,3,,4]),sP.log("setLocalDescription start"),[4,this.peerConnection.setLocalDescription(t)];case 2:return o.sent(),sP.log("setLocalDescription success"),this.clientSideDescription=this.peerConnection.localDescription,null===(e=this.onSetLocalDescription)||void 0===e||e.call(this,this.clientSideDescription),[3,4];case 3:return i=o.sent(),sP.log("setLocalDescription error,",i),null===(r=this.onError)||void 0===r||r.call(this,"set local sdp is failed"),[3,4];case 4:return[2]}var a}))}))},t.prototype.onAnswer=function(t){var e;return Zc(this,void 0,void 0,(function(){var r;return Yc(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),sP.log("setRemoteDescription start"),[4,this.peerConnection.setRemoteDescription(new RTCSessionDescription(t))];case 1:return n.sent(),sP.log("setRemoteDescription success"),this.serverSideDescription=this.peerConnection.remoteDescription,[3,3];case 2:return r=n.sent(),sP.log("setRemoteDescription error,",r),null===(e=this.onError)||void 0===e||e.call(this,"set remote sdp is failed"),[3,3];case 3:return[2]}}))}))},t.prototype.onDataChannelOpen=function(){sP.log("data channel open"),this.handleSeiData()},t.prototype.onDataChannelMessage=function(t){var e=this;if(t.data&&this.seiDataMap)try{var r=String.fromCharCode.apply(null,new Uint8Array(t.data)),n=JSON.parse(r),i=kw(n)?n:[n];Hs(i).call(i,(function(t){var r,n=t.type,i=t.rtp_time,o=t.data;if("sync_sei"===n){var a=new Uint8Array(Ew(r=o.match(/[\da-f]{2}/gi)).call(r,(function(t){return HR(t,16)}))),s=oP(a);Hs(s).call(s,(function(t){var r=e.seiDataMap.get(i)||[];e.seiDataMap.set(i,Xc(Xc([],Jc(r),!1),[t],!1))}))}}))}catch(e){sP.log("data channel error: data parsing failed",e,t.data)}},t.prototype.handleSeiData=function(){var t,e=this;if(this.ticker&&this.seiDataMap&&this.peerConnection){var r=this.connectConfig.maxSEIDelayTime,n=void 0===r?100:r,i=kE(t=this.peerConnection.getReceivers()).call(t,(function(t){return t.track&&"video"===t.track.kind}));if(i){var o=i.getSynchronizationSources();if(o.length>0){var a,s=o[0].rtpTimestamp,c=Xc([],Jc(Mw(a=this.seiDataMap).call(a)),!1);Hs(c).call(c,(function(t){var r=90*n,i=s-t;if(i>=0&&i<=r){var o=e.seiDataMap.get(t)||[];Hs(o).call(o,(function(t){var r;null===(r=e.onReceiveSEI)||void 0===r||r.call(e,t)})),e.seiDataMap.delete(t)}else i>r&&e.seiDataMap.delete(t)}))}}this.ticker.tick(this.handleSeiData)}else sP.log("sei data handler stop")},t}(),gP=Po.PROPER,bP=n,CP=ER,SP=DR.trim;br({target:"String",proto:!0,forced:function(t){return bP((function(){return!!CP[t]()||"​᠎"!=="​᠎"[t]()||gP&&CP[t].name!==t}))}("trim")},{trim:function(){return SP(this)}});var TP=Or("String").trim,_P=d,RP=TP,EP=String.prototype,wP=function(t){var e=t.trim;return"string"==typeof t||t===EP||_P(EP,t)&&e===EP.trim?RP:e},PP=function(){function t(){this.timeMark=null,this.commonData=null,this.reportData=null,this.tempData=null,this.onReport=null,this.timeMark=new Bb,this.commonData={},this.initCommonData(),this.reportData=Wc({},this.commonData),this.tempData={}}return t.prototype.setData=function(t,e,r){void 0===r&&(r=!1),this.reportData[t]=e,r&&(this.commonData[t]=e)},t.prototype.setTempData=function(t,e){this.tempData[t]=e},t.prototype.getTempData=function(t){return this.tempData[t]},t.prototype.startReport=function(t){var e;switch(t){case"start":this.setData("uint32_data_type",1),this.setData("uint32_command",40101);break;case"interval":this.setData("uint32_data_type",2),this.setData("uint32_command",40100);break;case"stop":this.setData("uint32_data_type",1),this.setData("uint32_command",40102)}var r=(new Date).getTime();this.setData("uint64_data_time",Math.round(r/1e3)),null===(e=this.onReport)||void 0===e||e.call(this,this.reportData),this.reportData=Wc({},this.commonData)},t.prototype.setHandler=function(t){this.onReport=t},t.prototype.markTime=function(t){this.timeMark.set(t,(new Date).getTime())},t.prototype.measureTime=function(t,e,r,n){if(void 0===n&&(n=!1),this.timeMark.has(e)&&this.timeMark.has(r)){var i=Math.round(this.timeMark.get(r)-this.timeMark.get(e));this.setData(t,i),n&&this.setTempData(t,i)}},t.prototype.clearData=function(){this.timeMark?this.timeMark.clear():this.timeMark=new Bb,this.commonData={},this.initCommonData(),this.reportData=Wc({},this.commonData),this.tempData={}},t.prototype.destroy=function(){var t;null===(t=this.timeMark)||void 0===t||t.clear(),this.timeMark=null,this.commonData=null,this.reportData=null,this.tempData=null,this.onReport=null},t.prototype.initCommonData=function(){var t,e;this.commonData.str_app_version="TXLivePlayer-".concat(FE),this.commonData.bytes_version="TXLivePlayer-".concat(FE);var r=window.navigator.userAgent,n=rl.parse(r),i=n.platform,o=n.os,a=n.browser;this.commonData.str_device_type=i.model||i.vendor||"",this.commonData.str_os_info=wP(t=ci(e="".concat(o.name||""," ")).call(e,o.version||"")).call(t),this.commonData.str_browser_model=a.name||"",this.commonData.str_browser_version=a.version||"",this.commonData.str_user_agent=r,this.commonData.u32_link_type=4,this.commonData.u32_channel_type=3},t}(),kP=function(t,e,r){return void 0===r&&(r={}),Zc(void 0,void 0,void 0,(function(){var n,i,o,a,s,c;return Yc(this,(function(u){switch(u.label){case 0:return n=r.timeout,i=void 0===n?10:n,o=null,a=null,s={},window.AbortController&&(o=new window.AbortController,s={signal:o.signal},a=sc((function(){return o.abort()}),1e3*i)),[4,fetch(t,Wc({body:EC(e),cache:"no-cache",credentials:"same-origin",headers:{"content-type":"application/json"},method:"POST",mode:"cors"},s))];case 1:if(c=u.sent(),a&&window.clearTimeout(a),200!==c.status)throw new Error("Network Error, status code:".concat(c.status));return[2,c.json()]}}))}))},AP=function(){function t(){this.baseUrl="",this.signalConfig={protocol:"https",domain:"",query:!0,timeout:5}}return t.prototype.setSignalConfig=function(t){this.signalConfig=Wc(Wc({},this.signalConfig),t)},t.prototype.fetchPullStream=function(t){var e;return Zc(this,void 0,void 0,(function(){var r,n,i,o,a,s,c,u,l,d,p=this;return Yc(this,(function(f){switch(f.label){case 0:return r=function(t,e,r){return Zc(p,void 0,void 0,(function(){var n,i,o,a,s,c,u;return Yc(this,(function(l){switch(l.label){case 0:return n="".concat(t,"/webrtc/v1/pullstream"),[4,kP(n,e,{timeout:r})];case 1:var d;if(i=l.sent(),o=i.errcode,a=i.errmsg,s=i.remotesdp,c=i.svrsig,0!==o)throw(u=new Error(ci(d="errCode:".concat(o,", errMsg:")).call(d,a))).name=HE,u;return[2,{url:t,remoteSdp:s,svrSig:c}]}}))}))},this.baseUrl?[4,r(this.baseUrl,t,this.signalConfig.timeout)]:[3,2];case 1:return n=f.sent(),u=n.url,[2,l=Gc(n,["url"])];case 2:i=this.signalConfig,o=i.protocol,a=i.timeout,f.label=3;case 3:return f.trys.push([3,5,,6]),s=Ew(jE).call(jE,(function(e){var n;return r(ci(n="".concat(o,"://")).call(n,e),t,a)})),[4,RR.any(s)];case 4:return c=f.sent(),u=c.url,l=Gc(c,["url"]),this.baseUrl=u,[2,l];case 5:throw d=f.sent(),(null===(e=null==d?void 0:d.errors)||void 0===e?void 0:e[0])||d;case 6:return[2]}}))}))},t.prototype.fetchStopStream=function(t){return Zc(this,void 0,void 0,(function(){var e,r,n,i;return Yc(this,(function(o){switch(o.label){case 0:if(!this.baseUrl)throw new Error("signal domain name is empty");return e="".concat(this.baseUrl,"/webrtc/v1/stopstream"),[4,kP(e,t,{timeout:this.signalConfig.timeout})];case 1:var a;if(r=o.sent(),n=r.errcode,i=r.errmsg,0!==n)throw new Error(ci(a="errCode:".concat(n,", errmsg:")).call(a,i));return[2,r]}}))}))},t.prototype.fetchStreamSdp=function(t){return Zc(this,void 0,void 0,(function(){var e,r,n,i,o,a,s,c;return Yc(this,(function(u){switch(u.label){case 0:return e=t.streamurl,r=t.sessionid,n=t.localsdp,[4,kP(e,{version:"v1.0",sessionId:r,localSdp:n},{timeout:this.signalConfig.timeout})];case 1:var l;if(i=u.sent(),o=i.code,a=i.message,s=i.remoteSdp,200!==o)throw(c=new Error(ci(l="errCode:".concat(o,", errMsg:")).call(l,a))).name=HE,c;return[2,{remoteSdp:s,svrSig:null}]}}))}))},t.prototype.fetchAbrControl=function(t){return Zc(this,void 0,void 0,(function(){var e,r,n,i;return Yc(this,(function(o){switch(o.label){case 0:if(!this.baseUrl)throw new Error("signal domain name is empty");return e="".concat(this.baseUrl,"/webrtc/v1/tabrctl"),[4,kP(e,t,{timeout:this.signalConfig.timeout})];case 1:var a;if(r=o.sent(),n=r.errcode,i=r.seq,0!==n)throw new Error(ci(a="errCode:".concat(n,", errMsg:")).call(a,403===n?"not allowed error":"error"));return[2,{seq:i}]}}))}))},t.prototype.updateSignalDomain=function(t){var e,r=this.signalConfig,n=r.protocol,i=r.domain,o=r.query;if(t&&o){var a=window.localStorage.getItem(BE);if(a){var s=JSON.parse(a)[t];if(s){var c,u=s.signal,l=s.expire;return(new Date).getTime()>l&&this.fetchSignalDomain(t,WE[0]),void(this.baseUrl=ci(c="".concat(n,"://")).call(c,u))}}this.baseUrl=i?ci(e="".concat(n,"://")).call(e,i):"",this.fetchSignalDomain(t,WE[0])}else{var d;this.baseUrl=i?ci(d="".concat(n,"://")).call(d,i):""}},t.prototype.getSignalDomain=function(){return this.baseUrl},t.prototype.fetchSignalDomain=function(t,e){return Zc(this,void 0,void 0,(function(){var r,n,i,o,a,s,c,u,l;return Yc(this,(function(d){switch(d.label){case 0:if(!t||!e)return[2];r="https://".concat(e,"/signal_query"),d.label=1;case 1:return d.trys.push([1,3,,4]),[4,kP(r,{domain:t,requestid:tl(16),client_type:"Web",client_info:window.navigator.userAgent})];case 2:if(n=d.sent(),i=n.errcode,o=n.data,0===i){a=o.signal_domain,s=o.cache_time,c={},(u=window.localStorage.getItem(BE))&&(c=JSON.parse(u)),c[t]={signal:a,expire:(new Date).getTime()+1e3*s};try{window.localStorage.setItem(BE,EC(c))}catch(t){}}return[3,4];case 3:return d.sent(),-1!==(l=gc(WE).call(WE,e))&&this.fetchSignalDomain(t,WE[l+1]),[3,4];case 4:return[2]}}))}))},t}(),IP=window.LEBP2P||VE,xP=function(){function t(){var t,e,r,n,i,o,a,s,c,u,l;this.playerView=null,this.playUrl=null,this.rawUrl=null,this.isVideoExisted=!1,this.webrtcConnection=null,this.svrSig=null,this.stream=null,this.timer={retryTimeout:null,disconnectTimeout:null,statsInterval:null,reportInterval:null},this.connectTimeout=5,this.connectRetry={maxNum:3,curNum:0,delay:1,playing:!1},this.streamDecodeFail={maxNum:3,curNum:0},this.streamReceiveFail={maxNum:10,curNum:0},this.streamPlaying={threshold:5,status:NE.INIT},this.docOrigOverflow=null,this.receiveConfig={video:!0,audio:!0},this.seiConfig={enable:!1,fallback:!1,maxDelayTime:100},this.listener={onPlayEvent:null,onPlayStats:null,onPlayReport:null,onPlaySEI:null},this.lastStatsReport=null,this.report=null,this.signal=null,this.abrClient={curBitrate:"",seq:0},this.p2pMode={enable:!1,config:{cloudAppid:0,xp2pAppid:"",appKey:"",appSecret:"",xp2pDomain:""},source:""},this.p2pConnection=null,this.report=new PP,this.signal=new AP,this.onAddTrack=jr(t=this.onAddTrack).call(t,this),this.onSetLocalDescription=jr(e=this.onSetLocalDescription).call(e,this),this.onConnect=jr(r=this.onConnect).call(r,this),this.onDisconnect=jr(n=this.onDisconnect).call(n,this),this.onError=jr(i=this.onError).call(i,this),this.onStats=jr(o=this.onStats).call(o,this),this.onBeforeUnload=jr(a=this.onBeforeUnload).call(a,this),this.onPagehide=jr(s=this.onPagehide).call(s,this),this.onP2PEvent=jr(c=this.onP2PEvent).call(c,this),this.onReceiveSEI=jr(u=this.onReceiveSEI).call(u,this),window.addEventListener("beforeunload",this.onBeforeUnload),window.addEventListener("pagehide",this.onPagehide);var d=$u.browserDetails,p=d.browser,f=d.version;sP.log(ci(l="browser is ".concat(p,", version is ")).call(l,f))}return t.checkSupport=function(t){Zc(void 0,void 0,void 0,(function(){var t,e,r;return Yc(this,(function(n){var i;switch(n.label){case 0:return t=!1,Hs(i=["RTCPeerConnection","webkitRTCPeerConnection"]).call(i,(function(e){t||e in window&&(t=!0)})),zE||(ew&&nw||rw&&nw&&(0===(e=ow()).length||e[0]<11||11===e[0]&&e[1]<1||11===e[0]&&1===e[1]&&e[2]<2))&&(t=!1),[4,Zc(void 0,void 0,void 0,(function(){var t,e,r,n;return Yc(this,(function(i){var o;switch(i.label){case 0:return i.trys.push([0,2,,3]),t=new RTCPeerConnection({iceServers:[],sdpSemantics:"unified-plan"}),e={},t.addTransceiver?(t.addTransceiver("audio",{direction:"recvonly"}),t.addTransceiver("video",{direction:"recvonly"})):e={offerToReceiveVideo:!0,offerToReceiveAudio:!0},[4,t.createOffer(e)];case 1:return r=i.sent(),n=gc(o=r.sdp.toLowerCase()).call(o,"h264")>-1,t.close(),[2,n];case 2:return i.sent(),[2,!1];case 3:return[2]}}))}))];case 1:return r=n.sent(),[2,{support:t,isTbs:zE,tbsVersion:zE?cw(navigator.userAgent,qE,1):null,isFirefox:$E,isSafari:rw,isIOS:nw,iOSVersion:nw?ow().join("."):null,h264Support:r}]}}))})).then((function(e){var r=e.support,n=e.h264Support;null==t||t({support:r,h264Support:n,p2pSupport:IP.support()})}))},t.prototype.setConfig=function(t){var e=t.connectRetryCount,r=t.connectRetryDelay,n=t.connectTimeout,i=t.receiveVideo,o=t.receiveAudio,a=t.receiveSEI,s=t.showLog,c=t.p2pEnable,u=t.p2pConfig,l=t.signalDomain,d=t.signalTimeout,p=t.maxDecodeFailCount,f=t.maxReceiveFailCount,h=t.minDecodeFramerate;if(void 0!==e&&("number"==typeof e&&e>=0?this.connectRetry.maxNum=e:sP.log("connectRetryCount must be a number greater than or equal to 0")),void 0!==r&&("number"==typeof r&&r>=0?this.connectRetry.delay=r:sP.log("connectRetryDelay must be a number greater than or equal to 0")),void 0!==n&&("number"==typeof n&&n>=0?this.connectTimeout=n:sP.log("connectTimeout must be a number greater than or equal to 0")),void 0!==i&&(this.receiveConfig.video=!!i),void 0!==o&&(this.receiveConfig.audio=!!o),void 0!==a)if("boolean"==typeof a)this.seiConfig={enable:a,fallback:!1,maxDelayTime:100};else{var m=null!=a?a:{},v=m.fallback,y=void 0!==v&&v,g=m.maxDelayTime,b=void 0===g?100:g;this.seiConfig={enable:!0,fallback:y,maxDelayTime:b}}if(void 0!==s&&sP.enableConsole(!!s),void 0!==c&&(this.p2pMode.enable=!!c),void 0!==u){var C=null!=u?u:{},S=C.cloudAppid,T=C.appKey,_=C.appSecret,R=C.xp2pAppid,E=C.xp2pDomain;this.p2pMode.config=Wc(Wc({},this.p2pMode.config),{cloudAppid:S,appKey:T,appSecret:_,xp2pAppid:R,xp2pDomain:E})}if(void 0!==l)if("string"==typeof l)this.signal.setSignalConfig({domain:l});else{var w,P=null!=l?l:{},k=P.protocol,A=P.domain,I=P.query;this.signal.setSignalConfig({protocol:Hi(w=["https","http"]).call(w,k)?k:"https",domain:A,query:I})}void 0!==d&&("number"==typeof d&&d>=0?this.signal.setSignalConfig({timeout:d}):sP.log("signalTimeout must be a number greater than or equal to 0")),void 0!==p&&("number"==typeof p&&p>=0?this.streamDecodeFail.maxNum=p:sP.log("maxDecodeFailCount must be a number greater than or equal to 0")),void 0!==f&&("number"==typeof f&&f>=0?this.streamReceiveFail.maxNum=f:sP.log("maxReceiveFailCount must be a number greater than or equal to 0")),void 0!==h&&("number"==typeof h&&h>=0?this.streamPlaying.threshold=h:sP.log("minDecodeFramerate must be a number greater than or equal to 0"))},t.prototype.setPlayListener=function(t){var e=this,r=["onPlayEvent","onPlayStats","onPlayReport","onPlaySEI"];Hs(r).call(r,(function(r){var n=t[r];void 0!==n&&("function"==typeof n?(e.listener[r]=n,"onPlayReport"===r&&e.report.setHandler(n)):sP.log("".concat(r," must be function")))}))},t.prototype.setPlayerView=function(t){if(this.playerView)sP.log("player view is existed");else{var e="string"==typeof t?document.getElementById(t):t;if(e&&(e instanceof HTMLDivElement||e instanceof HTMLVideoElement))if(e instanceof HTMLVideoElement)this.playerView=e;else{var r=document.createElement("video");r.autoplay=!0,r.muted=!0,r.controls=!0,r.playsInline=!0,r.setAttribute("webkit-playsinline",""),r.setAttribute("x5-playsinline",""),r.setAttribute("style",GE),e.appendChild(r),this.playerView=r}else sP.log("Require container element id or HTMLDivElement")}},t.prototype.startPlay=function(t){var e,r,n,i,o=this;if(this.playerView){var a=function(t){var e=/^(?:webrtc:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)([^?#]*)(?:\?*)(?:[^?#]*)/.exec(t);if(e)return e[1];var r=/^(?:https?:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)([^?#]*)(?:\.sdp)(?:\?*)(?:[^?#]*)/.exec(t);return r?r[1]:null}(t);if(null!==a)if(this.isVideoExisted)sP.log("Video is existed, please stop playing first");else if(this.receiveConfig.video||this.receiveConfig.audio){var s=(new Date).getTime();this.report.clearData(),this.report.setData("u64_timestamp",s),this.report.setTempData("pull_start_time",s),this.report.markTime("pull_start"),this.report.setData("bytes_stream_id",a,!0),this.report.setData("str_stream_url",t,!0);var c=function(t){var e=/^(?:webrtc:\/\/)([0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)(?:[^?#]*)(?:\?*)(?:[^?#]*)/.exec(t);return e?e[1]:null}(t);if(this.signal.updateSignalDomain(c),this.rawUrl=t,this.playUrl=function(t){var e=uw(t,"tabr_bitrates"),r=uw(t,"tabr_start_bitrate"),n=uw(t,"tabr_control"),i=t;return e&&r&&(i=lw(i,"tabr_control",null),i=lw(i,"webrtc_tabr_level","auto"===n?"server_control":"client_control")),i}(t),this.streamReceiveFail.curNum=0,this.streamDecodeFail.curNum=0,this.streamPlaying.status=NE.INIT,this.abrClient.seq=0,this.p2pMode.source="",this.p2pMode.enable){if(this.p2pConnection)return void sP.log("p2p connection is existed, please stop playing first");if(IP.support()&&this.receiveConfig.video&&this.receiveConfig.audio&&!function(t){var e=uw(t,"tabr_bitrates"),r=uw(t,"tabr_start_bitrate");return!(!e||!r)}(t)){this.report.setData("u32_isp2p",1,!0),null===(r=(e=this.listener).onPlayEvent)||void 0===r||r.call(e,OE.PLAY_EVT_P2P_START_SUCCESS);var u=this.signal.getSignalDomain();return this.p2pConnection=IP.create(Wc(Wc({},this.p2pMode.config),{connectRetryCount:this.connectRetry.maxNum,connectRetryDelay:1e3*this.connectRetry.delay,pullStreamDomain:u,stopStreamDomain:u})),this.p2pConnection.on(IP.Event,this.onP2PEvent),void this.p2pConnection.load(this.playUrl).then((function(t){var e=t.srcObject;o.stream=e,o.onAddStream(o.stream)}))}sP.log("p2p mode start failed"),null===(i=(n=this.listener).onPlayEvent)||void 0===i||i.call(n,OE.PLAY_ERR_P2P_START_FAIL)}this.report.setData("u32_isp2p",0,!0),this.webrtcConnection||(this.webrtcConnection=new yP,this.webrtcConnection.init({onAddTrack:this.onAddTrack,onSetLocalDescription:this.onSetLocalDescription,onConnect:this.onConnect,onDisconnect:this.onDisconnect,onError:this.onError,onReceiveSEI:this.onReceiveSEI})),this.webrtcConnection.initWebRTCConnect({config:{connection:{isReceiveSEI:this.seiConfig.enable&&this.receiveConfig.video,isCreateSEIDataChannel:this.seiConfig.fallback,maxSEIDelayTime:this.seiConfig.maxDelayTime},offer:{offerToReceiveVideo:this.receiveConfig.video,offerToReceiveAudio:this.receiveConfig.audio}}})}else sP.log("Both receiveVideo and receiveAudio are false");else sP.log("Play url is not correct")}else sP.log("Please set player view first")},t.prototype.stopPlay=function(t){var e,r,n;return void 0===t&&(t=!0),Zc(this,void 0,void 0,(function(){return Yc(this,(function(i){switch(i.label){case 0:return this.isVideoExisted?(this.isVideoExisted=!1,this.timer.disconnectTimeout&&(window.clearTimeout(this.timer.disconnectTimeout),this.timer.disconnectTimeout=null),this.timer.retryTimeout&&(window.clearTimeout(this.timer.retryTimeout),this.timer.retryTimeout=null),this.abrClient.curBitrate="",this.connectRetry.curNum=0,this.connectRetry.playing=!1,this.timer.statsInterval&&(window.clearInterval(this.timer.statsInterval),this.timer.statsInterval=null),this.timer.reportInterval&&(window.clearInterval(this.timer.reportInterval),this.timer.reportInterval=null),this.playUrl&&this.svrSig&&(this.signal.fetchStopStream({streamurl:this.playUrl,svrsig:this.svrSig}).catch((function(t){sP.log("request stopStream error,",t)})),this.playUrl=null,this.svrSig=null),[4,this.startReport("stop")]):(sP.log("Video is not existed"),[2]);case 1:var o;if(i.sent(),this.p2pConnection?(this.p2pConnection.destroy(),this.p2pConnection=null):this.webrtcConnection.disconnect(),t)null===(e=this.stream)||void 0===e||Hs(o=e.getTracks()).call(o,(function(t){t.stop()}));return this.stream=null,this.playerView&&(this.playerView.pause(),t&&(this.playerView.srcObject=null,this.playerView.load())),null===(n=(r=this.listener).onPlayEvent)||void 0===n||n.call(r,OE.PLAY_EVT_PLAY_STOP),[2]}}))}))},t.prototype.switchStream=function(t){var e,r;return Zc(this,void 0,void 0,(function(){var n,i,o;return Yc(this,(function(a){switch(a.label){case 0:if(s=this.rawUrl,c=t,u=function(t){var e=uw(t,"tabr_bitrates"),r=uw(t,"tabr_start_bitrate"),n=uw(t,"tabr_control");return!(!e||!r||"auto"===n)},!u(s)||!u(c)||lw(s,"tabr_start_bitrate",null)!==lw(c,"tabr_start_bitrate",null))return[3,5];if(!this.svrSig)return[3,4];a.label=1;case 1:return a.trys.push([1,3,,4]),n=uw(t,"tabr_start_bitrate"),sP.log("abr control: switch ".concat(n)),this.abrClient.seq=this.abrClient.seq+1,[4,this.signal.fetchAbrControl({svrsig:this.svrSig,seq:this.abrClient.seq,bitrate_name:n})];case 2:return i=a.sent().seq,this.abrClient.seq=Math.max(this.abrClient.seq,i),this.abrClient.curBitrate=n,[3,4];case 3:return o=a.sent(),sP.log(o.message),null===(r=(e=this.listener).onPlayEvent)||void 0===r||r.call(e,OE.PLAY_ERR_REQUEST_ABR_FAIL,{message:o.message}),[3,4];case 4:return[3,7];case 5:return[4,this.stopPlay()];case 6:a.sent(),this.startPlay(t),a.label=7;case 7:return[2]}var s,c,u}))}))},t.prototype.isPlaying=function(){return this.isVideoExisted&&this.isVideoPlaying()},t.prototype.pause=function(){this.playerView&&this.isVideoExisted&&this.isVideoPlaying()&&this.playerView.pause()},t.prototype.resume=function(){return Zc(this,void 0,void 0,(function(){var t;return Yc(this,(function(e){switch(e.label){case 0:if(!this.playerView||!this.isVideoExisted||this.isVideoPlaying())return[3,4];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,this.playerView.play()];case 2:return e.sent(),[3,4];case 3:return t=e.sent(),sP.log("resume failed",t),[3,4];case 4:return[2]}}))}))},t.prototype.setMute=function(t){this.playerView&&(this.playerView.muted=!!t)},t.prototype.setVolume=function(t){"number"==typeof t&&t>=0&&t<=100?this.playerView&&(this.playerView.volume=t/100):sP.log("volume must be a number between 0 and 100")},t.prototype.setControls=function(t){this.playerView&&(this.playerView.controls=!!t)},t.prototype.setFullscreen=function(t){this.playerView&&(t?this.requestFullscreen():this.exitFullscreen())},t.prototype.getVideoElement=function(){return this.playerView},t.prototype.destroy=function(){return Zc(this,void 0,void 0,(function(){return Yc(this,(function(t){switch(t.label){case 0:return window.removeEventListener("pagehide",this.onPagehide),window.removeEventListener("beforeunload",this.onBeforeUnload),this.timer.disconnectTimeout&&(window.clearTimeout(this.timer.disconnectTimeout),this.timer.disconnectTimeout=null),this.timer.retryTimeout&&(window.clearTimeout(this.timer.retryTimeout),this.timer.retryTimeout=null),this.timer.statsInterval&&(window.clearInterval(this.timer.statsInterval),this.timer.statsInterval=null),this.timer.reportInterval&&(window.clearInterval(this.timer.reportInterval),this.timer.reportInterval=null),this.isVideoExisted&&(this.webrtcConnection||this.p2pConnection)?[4,this.startReport("stop")]:[3,2];case 1:t.sent(),t.label=2;case 2:var e;if(this.stream)Hs(e=this.stream.getTracks()).call(e,(function(t){t.stop()})),this.stream=null;return this.playerView&&(this.playerView.pause(),this.playerView.srcObject=null,this.playerView.parentElement.removeChild(this.playerView),this.playerView=null),this.p2pConnection&&(this.p2pConnection.destroy(),this.p2pConnection=null),this.webrtcConnection&&(this.webrtcConnection.disconnect(),this.webrtcConnection=null),this.lastStatsReport=null,this.report&&(this.report.destroy(),this.report=null),this.signal=null,[2]}}))}))},t.prototype.isVideoPlaying=function(){return!(!this.playerView||!1!==this.playerView.paused)},t.prototype.requestFullscreen=function(){var t=dw;try{t.requestFullscreen?this.playerView[t.requestFullscreen]({navigationUI:"hide"}):bw(this.playerView)?this.playerView.webkitEnterFullScreen():this.enterFullWindow()}catch(t){sP.log("enter full screen failed, ",t)}},t.prototype.exitFullscreen=function(){var t,e=dw;try{if(e.requestFullscreen){var r=document[e.exitFullscreen]();null===(t=null==r?void 0:r.catch)||void 0===t||t.call(r,(function(t){return sP.log("exit full screen failed, ",null==t?void 0:t.message)}))}else bw(this.playerView)?this.playerView.webkitExitFullScreen():this.exitFullWindow()}catch(t){sP.log("exit full screen failed, ",t)}},t.prototype.enterFullWindow=function(){this.docOrigOverflow=document.documentElement.style.overflow,document.documentElement.style.overflow="hidden",this.playerView.setAttribute("style","position:fixed;overflow:hidden;z-index:9999;left:0;top:0;bottom:0;right:0;width:100% !important;height:100% !important;padding-top:0 !important;background-color:#000;")},t.prototype.exitFullWindow=function(){document.documentElement.style.overflow=this.docOrigOverflow,this.playerView.setAttribute("style",GE)},t.prototype.onAddStream=function(t){var e,r,n,i;return Zc(this,void 0,void 0,(function(){var o,a,s,c,u,l=this;return Yc(this,(function(d){switch(d.label){case 0:if(null===(r=(e=this.listener).onPlayEvent)||void 0===r||r.call(e,OE.PLAY_EVT_STREAM_BEGIN),!this.playerView)return[2];if(sP.log("video play 0"),o=function(){l.playerView.removeEventListener("loadedmetadata",o),l.playerView.removeEventListener("canplay",o),l.report.markTime("video_play"),l.receiveConfig.video?(l.report.measureTime("u32_first_video_decode_time","pull_start","video_play"),l.report.measureTime("u32_first_i_frame","pull_start","video_play",!0)):(l.report.setData("u32_first_video_decode_time",0),l.report.setData("u32_first_i_frame",0),l.report.setTempData("u32_first_i_frame",0)),l.receiveConfig.audio?l.report.measureTime("u32_first_audio_render_time","pull_start","video_play"):l.report.setData("u32_first_audio_render_time",0),l.startReport("start"),l.timer.reportInterval&&(window.clearInterval(l.timer.reportInterval),l.timer.reportInterval=null),l.timer.reportInterval=ac((function(){l.startReport("interval")}),5e3)},this.playerView.addEventListener("loadedmetadata",o),this.playerView.addEventListener("canplay",o),this.playerView.srcObject=t,this.isVideoExisted=!0,sP.log("video play 1"),!this.playerView.autoplay&&!this.connectRetry.playing)return[2];sP.log("video play 2"),a=function(t){var e,r,n;sP.log("play failed,",t),l.isVideoExisted&&(Hi(e=t.toString()).call(e,"NotAllowedError")?sc((function(){var e;null===(e=l.getConnectionStats())||void 0===e||e.then((function(e){var r,n,i,o,a=function(t){if(t){var e=null,r=null;Hs(t).call(t,(function(t){"inbound-rtp"===t.type&&("video"===t.kind||"video"===t.mediaType?e=t.id:"audio"!==t.kind&&"audio"!==t.mediaType||(r=t.id))}));var n=t.get(e),i=t.get(r);return{video:{bytesReceived:null==n?void 0:n.bytesReceived},audio:{bytesReceived:null==i?void 0:i.bytesReceived}}}}(e);null===(n=(r=l.listener).onPlayEvent)||void 0===n||n.call(r,OE.PLAY_ERR_PLAY_FAIL,{message:t.toString(),videoActive:(null===(i=null==a?void 0:a.video)||void 0===i?void 0:i.bytesReceived)>0,audioActive:(null===(o=null==a?void 0:a.audio)||void 0===o?void 0:o.bytesReceived)>0})}))}),1e3):null===(n=(r=l.listener).onPlayEvent)||void 0===n||n.call(r,OE.PLAY_ERR_PLAY_FAIL,{message:t.toString()}))},d.label=1;case 1:return d.trys.push([1,3,,4]),sP.log("video play 3"),[4,this.playerView.play()];case 2:return d.sent(),s=ow(),iw&&s.length>0&&13===s[0]?(this.playerView.pause(),c=function(){return Zc(l,void 0,void 0,(function(){var t,e,r;return Yc(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,this.playerView.play()];case 1:return n.sent(),sP.log("play ok ios"),null===(r=(e=this.listener).onPlayEvent)||void 0===r||r.call(e,OE.PLAY_EVT_PLAY_BEGIN),[3,3];case 2:return t=n.sent(),a(t),[3,3];case 3:return[2]}}))}))},sc((function(){c()}),200)):(sP.log("play ok"),null===(i=(n=this.listener).onPlayEvent)||void 0===i||i.call(n,OE.PLAY_EVT_PLAY_BEGIN)),[3,4];case 3:return u=d.sent(),a(u),[3,4];case 4:return[2]}}))}))},t.prototype.onAddTrack=function(t){"video"===t.kind?(this.report.markTime("video_down"),this.report.measureTime("u32_first_frame_down","pull_start","video_down")):"audio"===t.kind&&(this.report.markTime("audio_down"),this.report.measureTime("u32_first_audio_frame_down","pull_start","audio_down")),this.stream||(this.stream=new MediaStream),this.stream.addTrack(t)},t.prototype.onSetLocalDescription=function(t){var e,r,n,i;return Zc(this,void 0,void 0,(function(){var o,a,s,c,u,l,d,p,f,h,m,v,y=this;return Yc(this,(function(g){var b,C,S,T,_;switch(g.label){case 0:return g.trys.push([0,5,,6]),o=tl(),a=Pw(t.sdp),s=gc(b=t.sdp.toLowerCase()).call(b,"h264")>-1,c=rl.parse(window.navigator.userAgent),u=c.browser,l=c.os,d=ci(C=ci(S=ci(T="".concat(l.name||"other"," ")).call(T,l.version,";")).call(S,u.name||"other"," ")).call(C,u.version),null===(r=(e=this.listener).onPlayEvent)||void 0===r||r.call(e,OE.PLAY_EVT_REQUEST_PULL_BEGIN,{localSdp:t}),this.report.setData("str_session_id",o,!0),this.report.setData("bytes_token",o,!0),this.report.setData("str_ice_info",a,!0),this.report.markTime("request_start"),sP.log("H264 is ".concat(s?"":"not ","supported")),sP.log("traceId is webrtc_user=".concat(a)),sP.log("request pullStream start"),p=null,Dc(_=this.playUrl).call(_,"webrtc")?[4,this.signal.fetchPullStream({streamurl:this.playUrl,sessionid:o,clientinfo:d,localsdp:t})]:[3,2];case 1:return p=g.sent(),[3,4];case 2:return[4,this.signal.fetchStreamSdp({streamurl:this.playUrl,sessionid:o,localsdp:t})];case 3:p=g.sent(),g.label=4;case 4:return sP.log("request pullStream success"),this.report.markTime("request_end"),this.report.measureTime("u32_signal_time","request_start","request_end"),f=p.remoteSdp,h=p.svrSig,m=function(t){var e,r,n=t.split("\r\n");try{for(var i=Hc(n),o=i.next();!o.done;o=i.next()){var a=o.value;if(Dc(a).call(a,"i="))return a.replace("i=","")}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return""}(f.sdp),null===(i=(n=this.listener).onPlayEvent)||void 0===i||i.call(n,OE.PLAY_EVT_REQUEST_PULL_SUCCESS,{remoteSdp:f}),this.report.setData("bytes_server_label",m,!0),this.webrtcConnection.connect(f),this.svrSig=h,this.timer.disconnectTimeout&&window.clearTimeout(this.timer.disconnectTimeout),this.timer.disconnectTimeout=sc((function(){sP.log("connect timeout, try reconnect"),y.webrtcConnection.disconnect({code:LE.NEED_RECONNECT,msg:"Connection disconnected, please try again"})}),1e3*this.connectTimeout),[3,6];case 5:return v=g.sent(),sP.log("request pullStream error,",v),v.name===HE?(this.report.markTime("request_end"),this.report.measureTime("u32_signal_time","request_start","request_end"),this.webrtcConnection.disconnect(),this.onPlayError(OE.PLAY_ERR_REQUEST_PULL_FAIL,{message:v.message})):(sP.log("request signal ".concat("AbortError"===v.name?"timeout":"failed",", try reconnect")),this.webrtcConnection.disconnect({code:LE.NEED_RECONNECT,msg:"request signal failed, please try again"})),[3,6];case 6:return[2]}}))}))},t.prototype.onConnect=function(t){var e,r,n=this;t.code===xE.SUCCESS&&(this.stream&&this.onAddStream(this.stream),this.timer.disconnectTimeout&&(window.clearTimeout(this.timer.disconnectTimeout),this.timer.disconnectTimeout=null),this.startStat(),null===(r=(e=this.listener).onPlayEvent)||void 0===r||r.call(e,OE.PLAY_EVT_SERVER_CONNECTED),this.abrClient.curBitrate&&this.svrSig&&(sP.log("abr control: reconnect ".concat(this.abrClient.curBitrate)),this.abrClient.seq=this.abrClient.seq+1,this.signal.fetchAbrControl({svrsig:this.svrSig,seq:this.abrClient.seq,bitrate_name:this.abrClient.curBitrate}).then((function(t){var e=t.seq;n.abrClient.seq=Math.max(n.abrClient.seq,e)})).catch((function(t){sP.log(t.message)}))))},t.prototype.onDisconnect=function(t){var e=this;if(this.lastStatsReport=null,this.streamReceiveFail.curNum=0,this.streamDecodeFail.curNum=0,this.streamPlaying.status=NE.INIT,this.timer.disconnectTimeout&&(window.clearTimeout(this.timer.disconnectTimeout),this.timer.disconnectTimeout=null),this.timer.statsInterval&&(window.clearInterval(this.timer.statsInterval),this.timer.statsInterval=null),this.timer.reportInterval&&(window.clearInterval(this.timer.reportInterval),this.timer.reportInterval=null),t.code===LE.NEED_RECONNECT){var r;if(0===this.connectRetry.curNum&&(this.connectRetry.playing=this.isPlaying()),this.playUrl&&this.svrSig&&(this.signal.fetchStopStream({streamurl:this.playUrl,svrsig:this.svrSig}).catch((function(t){sP.log("request stopStream error,",t)})),this.svrSig=null),this.stream)Hs(r=this.stream.getTracks()).call(r,(function(t){t.stop()})),this.stream=null;this.isVideoExisted&&this.playerView&&(this.playerView.pause(),this.playerView.srcObject=null,this.playerView.load(),this.isVideoExisted=!1),this.timer.retryTimeout&&(window.clearTimeout(this.timer.retryTimeout),this.timer.retryTimeout=null),this.timer.retryTimeout=sc((function(){var t,r,n;e.connectRetry.curNum+=1;var i=e.connectRetry,o=i.curNum,a=i.maxNum;sP.log(ci(t="current retry num: ".concat(o,", max retry num: ")).call(t,a)),o<=a?(sP.log("start connection retry"),null===(n=(r=e.listener).onPlayEvent)||void 0===n||n.call(r,OE.PLAY_EVT_SERVER_RECONNECT),e.startPlay(e.rawUrl)):(sP.log("stop connection retry"),e.onPlayError(OE.PLAY_ERR_SERVER_DISCONNECT),e.connectRetry.curNum=0,e.connectRetry.playing=!1),e.timer.retryTimeout=null}),1e3*this.connectRetry.delay)}},t.prototype.onError=function(t){this.onPlayError(OE.PLAY_ERR_WEBRTC_FAIL,{message:t})},t.prototype.startStat=function(){return Zc(this,void 0,void 0,(function(){var t,e=this;return Yc(this,(function(r){switch(r.label){case 0:return this.report.markTime("server_connected"),this.report.measureTime("u32_connect_server_time","pull_start","server_connected"),this.timer.statsInterval&&(window.clearInterval(this.timer.statsInterval),this.timer.statsInterval=null),[4,this.getConnectionStats()];case 1:return(t=r.sent())&&(this.report.setTempData("start_stats",t),this.report.setTempData("last_stats",t),this.report.setTempData("last_time",(new Date).getTime()),this.onStats(t)),this.timer.statsInterval=ac((function(){var t;null===(t=e.getConnectionStats())||void 0===t||t.then((function(t){t&&e.onStats(t)}))}),1e3),[2]}}))}))},t.prototype.onStats=function(t){var e,r,n,i,o,a,s,c,u,l,d=function(t,e){var r,n,i,o,a;if(void 0===e&&(e=null),e){var s=null,c=null,u=null,l=null,d=null;Hs(t).call(t,(function(t){"track"===t.type&&("video"===t.kind||t.frameWidth?s=t.id:("audio"===t.kind||t.audioLevel)&&(u=t.id)),"inbound-rtp"===t.type&&("video"===t.kind||"video"===t.mediaType?c=t.id:"audio"!==t.kind&&"audio"!==t.mediaType||(l=t.id)),"candidate-pair"===t.type&&t.selected?d=t.id:"transport"===t.type&&t.selectedCandidatePairId&&(d=t.selectedCandidatePairId)}));var p=t.get(s),f=t.get(c),h=e.get(s),m=e.get(c),v=0;void 0!==(null==f?void 0:f.timestamp)&&void 0!==(null==m?void 0:m.timestamp)&&(v=(f.timestamp-m.timestamp)/1e3);var y=void 0;if((null==f?void 0:f.codecId)&&(x=t.get(f.codecId))){var g,b,C=x.mimeType,S=x.payloadType,T=x.sdpFmtpLine;y=(null==C?void 0:C.replace("video/",""))||"",S&&T&&(y=ci(g=ci(b="".concat(y," (")).call(b,S,", ")).call(g,T,")"))}var _=void 0;void 0!==(null==f?void 0:f.framesPerSecond)?_=f.framesPerSecond:void 0!==(null==f?void 0:f.framerateMean)?_=f.framerateMean:void 0!==(null==f?void 0:f.framesReceived)&&void 0!==(null==m?void 0:m.framesReceived)&&v?_=(f.framesReceived-m.framesReceived)/v:void 0!==(null==p?void 0:p.framesReceived)&&void 0!==(null==h?void 0:h.framesReceived)&&v&&(_=(p.framesReceived-h.framesReceived)/v);var R=void 0;void 0!==(null==f?void 0:f.framesDecoded)&&void 0!==(null==m?void 0:m.framesDecoded)&&v&&(R=(f.framesDecoded-m.framesDecoded)/v);var E=void 0;void 0!==(null==f?void 0:f.bytesReceived)&&void 0!==(null==m?void 0:m.bytesReceived)&&v&&(E=8*(f.bytesReceived-m.bytesReceived)/v);var w=void 0;void 0!==(null==f?void 0:f.jitterBufferDelay)&&void 0!==(null==f?void 0:f.jitterBufferEmittedCount)&&void 0!==(null==m?void 0:m.jitterBufferDelay)&&void 0!==(null==m?void 0:m.jitterBufferEmittedCount)&&(f.jitterBufferEmittedCount-m.jitterBufferEmittedCount?w=(f.jitterBufferDelay-m.jitterBufferDelay)/(f.jitterBufferEmittedCount-m.jitterBufferEmittedCount)*1e3:f.jitterBufferEmittedCount&&(w=f.jitterBufferDelay/f.jitterBufferEmittedCount*1e3));var P=void 0;void 0!==(null==f?void 0:f.totalDecodeTime)&&void 0!==(null==f?void 0:f.framesDecoded)&&void 0!==(null==m?void 0:m.totalDecodeTime)&&void 0!==(null==m?void 0:m.framesDecoded)&&(f.framesDecoded-m.framesDecoded?P=(f.totalDecodeTime-m.totalDecodeTime)/(f.framesDecoded-m.framesDecoded)*1e3:f.framesDecoded&&(P=f.totalDecodeTime/f.framesDecoded*1e3));var k=t.get(u),A=t.get(l),I=e.get(l);void 0!==(null==A?void 0:A.timestamp)&&void 0!==(null==I?void 0:I.timestamp)&&(v=(A.timestamp-I.timestamp)/1e3);var x,L,D,N=void 0;if((null==A?void 0:A.codecId)&&(x=t.get(A.codecId)))C=x.mimeType,S=x.payloadType,T=x.sdpFmtpLine,N=(null==C?void 0:C.replace("audio/",""))||"",S&&T&&(N=ci(L=ci(D="".concat(N," (")).call(D,S,", ")).call(L,T,")"));var O=void 0;void 0!==(null==A?void 0:A.audioLevel)?O=null==A?void 0:A.audioLevel:void 0!==(null==k?void 0:k.audioLevel)&&(O=null==k?void 0:k.audioLevel);var M=void 0;void 0!==(null==A?void 0:A.bytesReceived)&&void 0!==(null==I?void 0:I.bytesReceived)&&v&&(M=8*(A.bytesReceived-I.bytesReceived)/v);var U=void 0;void 0!==(null==A?void 0:A.jitterBufferDelay)&&void 0!==(null==A?void 0:A.jitterBufferEmittedCount)&&void 0!==(null==I?void 0:I.jitterBufferDelay)&&void 0!==(null==I?void 0:I.jitterBufferEmittedCount)&&(A.jitterBufferEmittedCount-I.jitterBufferEmittedCount?U=(A.jitterBufferDelay-I.jitterBufferDelay)/(A.jitterBufferEmittedCount-I.jitterBufferEmittedCount)*1e3:A.jitterBufferEmittedCount&&(U=A.jitterBufferDelay/A.jitterBufferEmittedCount*1e3));var V=t.get(d),F=void 0;void 0!==(null==V?void 0:V.currentRoundTripTime)&&(F=1e3*V.currentRoundTripTime);var j=void 0,B=void 0,W=void 0,G=void 0;void 0===(null==f?void 0:f.packetsLost)&&void 0===(null==A?void 0:A.packetsLost)||(j=((null==f?void 0:f.packetsLost)||0)+((null==A?void 0:A.packetsLost)||0)),void 0===(null==f?void 0:f.packetsReceived)&&void 0===(null==A?void 0:A.packetsReceived)||(B=((null==f?void 0:f.packetsReceived)||0)+((null==A?void 0:A.packetsReceived)||0)),void 0===(null==m?void 0:m.packetsLost)&&void 0===(null==I?void 0:I.packetsLost)||(W=((null==m?void 0:m.packetsLost)||0)+((null==I?void 0:I.packetsLost)||0)),void 0===(null==m?void 0:m.packetsReceived)&&void 0===(null==I?void 0:I.packetsReceived)||(G=((null==m?void 0:m.packetsReceived)||0)+((null==I?void 0:I.packetsReceived)||0));var Z=void 0;if(void 0!==j&&void 0!==B&&void 0!==W&&void 0!==G){var Y=j-W,H=B-G;Z=Y<=0||H<0?0:Y/(Y+H)*100}var J=gw.UNKNOWN;return void 0===F&&void 0===Z||(Z>50||F>500?J=gw.VERY_BAD:Z>30||F>350?J=gw.BAD:Z>20||F>200?J=gw.POOR:Z>10||F>100?J=gw.GOOD:(Z>=0||F>=0)&&(J=gw.EXCELLENT)),{timestamp:(null==f?void 0:f.timestamp)||(null==A?void 0:A.timestamp),video:{codec:y,bitrate:E&&Number(E.toFixed(2)),framesPerSecond:_&&Math.round(_),framesDecodedPerSecond:R&&Math.round(R),frameWidth:null!==(r=null==f?void 0:f.frameWidth)&&void 0!==r?r:null==p?void 0:p.frameWidth,frameHeight:null!==(n=null==f?void 0:f.frameHeight)&&void 0!==n?n:null==p?void 0:p.frameHeight,framesDecoded:null!==(i=null==f?void 0:f.framesDecoded)&&void 0!==i?i:null==p?void 0:p.framesDecoded,framesDropped:null!==(o=null==f?void 0:f.framesDropped)&&void 0!==o?o:null==p?void 0:p.framesDropped,framesReceived:null!==(a=null==f?void 0:f.framesReceived)&&void 0!==a?a:null==p?void 0:p.framesReceived,packetsLost:null==f?void 0:f.packetsLost,packetsReceived:null==f?void 0:f.packetsReceived,nackCount:null==f?void 0:f.nackCount,firCount:null==f?void 0:f.firCount,pliCount:null==f?void 0:f.pliCount,jitterBufferDelay:w&&Number(w.toFixed(2)),frameDecodeAvgTime:P&&Number(P.toFixed(2))},audio:{codec:N,audioLevel:O,bitrate:M&&Number(M.toFixed(2)),packetsLost:null==A?void 0:A.packetsLost,packetsReceived:null==A?void 0:A.packetsReceived,jitterBufferDelay:U&&Number(U.toFixed(2))},network:{roundTripTime:F,quality:J}}}}(t,this.lastStatsReport);if(this.lastStatsReport=t,d){d.video.frameWidth=d.video.frameWidth||(null===(e=this.playerView)||void 0===e?void 0:e.videoWidth)||void 0,d.video.frameHeight=d.video.frameHeight||(null===(r=this.playerView)||void 0===r?void 0:r.videoHeight)||void 0;var p={};if(this.p2pConnection){var f=this.p2pConnection.getSDKStats(),h=f.uploadBytesTotal,m=f.uploadAuidoBytesTotal,v=f.uploadVideoBytesTotal,y=f.cdnDownloadBytesTotal,g=f.cdnDownloadAuidoBytesTotal,b=f.cdnDownloadVideoBytesTotal,C=f.p2pDownloadBytesTotal,S=f.p2pDownloadAuidoBytesTotal,T=f.p2pDownloadVideoBytesTotal;p.p2p={uploadBytes:h,uploadAuidoBytes:m,uploadVideoBytes:v,cdnDownloadBytes:y,cdnDownloadAuidoBytes:g,cdnDownloadVideoBytes:b,p2pDownloadBytes:C,p2pDownloadAuidoBytes:S,p2pDownloadVideoBytes:T}}if(null===(i=(n=this.listener).onPlayStats)||void 0===i||i.call(n,Wc(Wc({},d),p)),d.video.bitrate||d.audio.bitrate)this.streamReceiveFail.curNum=0,this.connectRetry.curNum=0;else if(this.streamReceiveFail.curNum+=1,this.streamReceiveFail.curNum>=this.streamReceiveFail.maxNum)return null===(a=(o=this.listener).onPlayEvent)||void 0===a||a.call(o,OE.PLAY_EVT_STREAM_EMPTY),sP.log("stream is empty, try reconnect"),void this.webrtcConnection.disconnect({code:LE.NEED_RECONNECT,msg:"stream is empty, please try again"})}if((null==d?void 0:d.video)&&this.receiveConfig.video&&this.isVideoExisted){var _=d.video,R=_.bitrate,E=_.framesPerSecond,w=_.framesDecoded,P=_.framesDecodedPerSecond;if(R&&E&&!w?(this.streamDecodeFail.curNum+=1,this.streamDecodeFail.curNum===this.streamDecodeFail.maxNum&&this.onPlayError(OE.PLAY_ERR_DECODE_FAIL,{message:"decode failed",bitrate:R,framesPerSecond:E})):this.streamDecodeFail.curNum=0,P>=0||E>=0)if(P<=this.streamPlaying.threshold||E<=this.streamPlaying.threshold){var k=this.report.getTempData("freeze_interval_count")||0;this.report.setTempData("freeze_interval_count",k+1),this.streamPlaying.status!==NE.WAITING&&(this.streamPlaying.status=NE.WAITING,this.report.setTempData("freeze_start_time",(new Date).getTime()),null===(c=(s=this.listener).onPlayEvent)||void 0===c||c.call(s,OE.PLAY_EVT_PLAY_WAITING_BEGIN))}else{if(this.streamPlaying.status===NE.WAITING){var A=this.report.getTempData("freeze_start_time"),I=(new Date).getTime(),x=I-A,L=this.report.getTempData("video_freeze")||{},D=L.count,N=void 0===D?0:D,O=L.totalTime,M=void 0===O?0:O,U=L.maxTime,V=void 0===U?0:U,F=L.endTimes,j=void 0===F?[]:F;N+=1,M+=x,V=Math.max(x,V),j=Xc(Xc([],Jc(j),!1),[I],!1),this.report.setTempData("video_freeze",{count:N,totalTime:M,maxTime:V,endTimes:j}),null===(l=(u=this.listener).onPlayEvent)||void 0===l||l.call(u,OE.PLAY_EVT_PLAY_WAITING_STOP)}this.streamPlaying.status=NE.PLAYING}}},t.prototype.startReport=function(t,e){var r,n;return void 0===e&&(e=""),Zc(this,void 0,void 0,(function(){var i,o,a,s,c,u,l,d,p,f,h,m,v,y,g,b,C,S,T,_,R,E,w,P,k,A,I,x,L,D,N,O,M,U;return Yc(this,(function(V){switch(V.label){case 0:return i=(new Date).getTime(),o=this.report.getTempData("pull_start_time"),"stop"===t?(a=this.report.getTempData("u32_first_i_frame")||0,this.report.setData("u64_timestamp",i),this.report.setData("u64_begin_timestamp",o),this.report.setData("u32_result",Math.round((i-o)/1e3)),this.report.setData("u32_first_i_frame",a),this.report.setData("u32_first_frame_black",a>1e4?1:0),s=this.report.getTempData("video_freeze")||{},c=s.count,v=void 0===c?0:c,u=s.totalTime,l=void 0===u?0:u,d=s.maxTime,p=void 0===d?0:d,this.report.setData("u64_block_count",v),this.report.setData("u32_video_block_time",l),this.report.setData("u64_block_duration_max",p),this.report.setData("u32_avg_block_time",l&&v?Math.round(l/v):0),this.report.setData("u32_delay_report",e?1:0)):"interval"===t&&(this.report.setData("u64_timestamp",i),this.report.setData("u64_playtime",i-o),f=(this.report.getTempData("video_freeze")||{}).endTimes,h=void 0===f?[]:f,m=this.report.getTempData("last_time")||0,v=Fc(h).call(h,(function(t){return t>=m&&t<i})).length,this.report.setData("u32_video_block_count",v),this.report.setTempData("last_time",i),y=this.report.getTempData("freeze_interval_count")||0,this.report.setData("u32_block_usage",y/5*1e3),this.report.setTempData("freeze_interval_count",0)),g=null,"stop"!==t||"pagehide"!==e?[3,1]:(g=this.report.getTempData("last_stats"),[3,3]);case 1:return[4,this.getConnectionStats()];case 2:g=V.sent(),V.label=3;case 3:var F,j;if(g)b=function(t){var e=null;Hs(t).call(t,(function(r){var n;"candidate-pair"===r.type&&r.selected?e=r.remoteCandidateId:"transport"===r.type&&r.selectedCandidatePairId&&(e=null===(n=t.get(r.selectedCandidatePairId))||void 0===n?void 0:n.remoteCandidateId)}));var r=t.get(e)||{},n=r.address,i=void 0===n?"":n,o=r.port;return{address:i,port:void 0===o?"":o}}(g),C=b.address,S=b.port,this.report.setData("u32_server_ip",ci(F=ci(j="".concat(C)).call(j,S?":":"")).call(F,S)),"stop"===t?(T=this.report.getTempData("start_stats"),_=function(t,e){var r,n,i=null,o=null,a=null;Hs(t).call(t,(function(t){"track"!==t.type||"video"!==t.kind&&!t.frameWidth||(i=t.id),"inbound-rtp"===t.type&&("video"===t.kind||"video"===t.mediaType?o=t.id:"audio"!==t.kind&&"audio"!==t.mediaType||(a=t.id))}));var s=t.get(i),c=t.get(o),u=t.get(a),l=null==e?void 0:e.get(o),d=0;void 0!==(null==c?void 0:c.timestamp)&&void 0!==(null==l?void 0:l.timestamp)&&(d=(c.timestamp-l.timestamp)/1e3);var p=0;void 0!==(null==c?void 0:c.packetsLost)&&void 0!==(null==c?void 0:c.packetsReceived)&&(p=c.packetsLost/(c.packetsLost+c.packetsReceived)*1e3);var f=0;void 0!==(null==u?void 0:u.packetsLost)&&void 0!==(null==u?void 0:u.packetsReceived)&&(f=u.packetsLost/(u.packetsLost+u.packetsReceived)*1e3);var h=0;return void 0!==(null==c?void 0:c.framesDecoded)&&void 0!==(null==l?void 0:l.framesDecoded)&&d&&(h=Math.round((c.framesDecoded-l.framesDecoded)/d)),{width:(null!==(r=null==c?void 0:c.frameWidth)&&void 0!==r?r:null==s?void 0:s.frameWidth)||0,height:(null!==(n=null==c?void 0:c.frameHeight)&&void 0!==n?n:null==s?void 0:s.frameHeight)||0,videoFractionLost:Number(p.toFixed(2)),audioFractionLost:Number(f.toFixed(2)),framerateMean:h}}(g,T),R=_.width,E=_.height,M=_.videoFractionLost,x=_.audioFractionLost,w=_.framerateMean,this.report.setData("u32_video_width",R||(null===(r=this.playerView)||void 0===r?void 0:r.videoWidth)),this.report.setData("u32_video_height",E||(null===(n=this.playerView)||void 0===n?void 0:n.videoHeight)),this.report.setData("u32_video_drop_usage",M),this.report.setData("u32_audio_drop_usage",x),this.report.setData("u32_video_avg_fps",w)):"interval"===t&&(P=this.report.getTempData("last_stats"),k=function(t,e){var r=null,n=null,i=null,o=null;Hs(t).call(t,(function(t){"track"===t.type&&("video"===t.kind||t.frameWidth)&&(r=t.id),"inbound-rtp"===t.type&&("video"===t.kind||"video"===t.mediaType?n=t.id:"audio"!==t.kind&&"audio"!==t.mediaType||(i=t.id)),"candidate-pair"===t.type&&t.selected?o=t.id:"transport"===t.type&&t.selectedCandidatePairId&&(o=t.selectedCandidatePairId)}));var a=t.get(r),s=t.get(n),c=null==e?void 0:e.get(r),u=null==e?void 0:e.get(n),l=0;void 0!==(null==s?void 0:s.timestamp)&&void 0!==(null==u?void 0:u.timestamp)&&(l=(s.timestamp-u.timestamp)/1e3);var d=0;void 0!==(null==s?void 0:s.packetsLost)&&void 0!==(null==u?void 0:u.packetsLost)&&(d=s.packetsLost-u.packetsLost);var p=0;void 0!==(null==s?void 0:s.framesReceived)&&void 0!==(null==u?void 0:u.framesReceived)&&l?p=(s.framesReceived-u.framesReceived)/l:void 0!==(null==a?void 0:a.framesReceived)&&void 0!==(null==c?void 0:c.framesReceived)&&l?p=(a.framesReceived-c.framesReceived)/l:void 0!==(null==s?void 0:s.framerateMean)&&void 0!==(null==u?void 0:u.framerateMean)&&(p=(s.framerateMean+u.framerateMean)/2);var f=0;void 0!==(null==s?void 0:s.framesDecoded)&&void 0!==(null==u?void 0:u.framesDecoded)&&l&&(f=(s.framesDecoded-u.framesDecoded)/l);var h=0;void 0!==(null==s?void 0:s.bytesReceived)&&void 0!==(null==u?void 0:u.bytesReceived)&&l&&(h=8*(s.bytesReceived-u.bytesReceived)/l/1024);var m=0;void 0!==(null==s?void 0:s.packetsLost)&&void 0!==(null==s?void 0:s.packetsReceived)&&void 0!==(null==u?void 0:u.packetsLost)&&void 0!==(null==u?void 0:u.packetsReceived)&&(m=(C=s.packetsLost-u.packetsLost)/(C+(s.packetsReceived-u.packetsReceived))*1e3);var v=t.get(i),y=null==e?void 0:e.get(i);void 0!==(null==v?void 0:v.timestamp)&&void 0!==(null==y?void 0:y.timestamp)&&(l=(v.timestamp-y.timestamp)/1e3);var g=0;void 0!==(null==v?void 0:v.packetsLost)&&void 0!==(null==y?void 0:y.packetsLost)&&(g=v.packetsLost-y.packetsLost);var b=0;void 0!==(null==v?void 0:v.bytesReceived)&&void 0!==(null==y?void 0:y.bytesReceived)&&l&&(b=8*(v.bytesReceived-y.bytesReceived)/l/1024);var C,S=0;void 0!==(null==v?void 0:v.packetsLost)&&void 0!==(null==v?void 0:v.packetsReceived)&&void 0!==(null==y?void 0:y.packetsLost)&&void 0!==(null==y?void 0:y.packetsReceived)&&(S=(C=v.packetsLost-y.packetsLost)/(C+(v.packetsReceived-y.packetsReceived))*1e3);var T=t.get(o),_=null==e?void 0:e.get(o),R=0;return void 0!==(null==T?void 0:T.totalRoundTripTime)&&void 0!==(null==T?void 0:T.responsesReceived)&&void 0!==(null==_?void 0:_.totalRoundTripTime)&&void 0!==(null==_?void 0:_.responsesReceived)&&(R=(T.totalRoundTripTime-_.totalRoundTripTime)/(T.responsesReceived-_.responsesReceived)*1e3),{videoPacketsLost:Math.max(d,0),videoReceiveFps:Math.round(p),videoDecodeFps:Math.round(f),videoBitrate:Number(h.toFixed(2)),videoFractionLost:Math.max(Math.round(m),0),audioPacketsLost:Math.max(g,0),audioBitrate:Number(b.toFixed(2)),audioFractionLost:Math.max(Math.round(S),0),roundTripTime:Math.round(R)}}(g,P),A=k.audioPacketsLost,I=k.audioBitrate,x=k.audioFractionLost,L=k.videoReceiveFps,D=k.videoDecodeFps,N=k.videoBitrate,O=k.videoPacketsLost,M=k.videoFractionLost,U=k.roundTripTime,this.report.setData("u32_audio_drop",A),this.report.setData("u32_audio_drop_usage",x),this.report.setData("u32_avg_audio_bitrate",I),this.report.setData("u32_video_drop",O),this.report.setData("u32_video_drop_usage",M),this.report.setData("u32_video_recv_fps",L),this.report.setData("u32_fps",D),this.report.setData("u32_avg_video_bitrate",N),this.report.setData("u32_avg_net_speed",Number((N+I).toFixed(2))),this.report.setData("u64_rtt",U),this.report.setTempData("last_stats",g));return this.report.startReport(t),[2]}}))}))},t.prototype.onPlayError=function(t,e){var r,n,i=(e||{}).message;null===(n=(r=this.listener).onPlayEvent)||void 0===n||n.call(r,t,e),this.report.setData("u64_err_code",t),this.report.setData("str_err_info",i||""),t===OE.PLAY_ERR_SERVER_DISCONNECT?this.startReport("stop"):this.startReport("start")},t.prototype.onBeforeUnload=function(){window.removeEventListener("pagehide",this.onPagehide),this.isVideoExisted&&(this.webrtcConnection||this.p2pConnection)&&this.report&&this.startReport("stop","beforeunload")},t.prototype.onPagehide=function(){this.isVideoExisted&&(this.webrtcConnection||this.p2pConnection)&&this.report&&this.startReport("stop","pagehide")},t.prototype.getConnectionStats=function(){return this.p2pConnection?this.p2pConnection.getStats():this.webrtcConnection?this.webrtcConnection.getStats():void 0},t.prototype.onP2PEvent=function(t){var e,r,n,i,o,a,s,c,u,l,d,p,f=t.code,h=t.msg,m=t.data;switch(sP.log(ci(e="P2P Event, code: ".concat(f,", msg: ")).call(e,h,", data:"),m),f){case IP.EventCode.INF_PLAY_EVT_STREAM_FIRST_AUDIO_FRAME:this.report.markTime("audio_down"),this.report.measureTime("u32_first_audio_frame_down","pull_start","audio_down");break;case IP.EventCode.INF_PLAY_EVT_STREAM_FIRST_VIDEO_FRAME:this.report.markTime("video_down"),this.report.measureTime("u32_first_frame_down","pull_start","video_down");break;case IP.EventCode.INF_PLAY_EVT_REQUEST_PULL_BEGIN:var v={type:"offer",sdp:m.localsdp},y=m.sessionid;null===(n=(r=this.listener).onPlayEvent)||void 0===n||n.call(r,OE.PLAY_EVT_REQUEST_PULL_BEGIN,{localSdp:v}),this.report.setData("str_session_id",y,!0),this.report.setData("bytes_token",y,!0),this.report.setData("str_ice_info",Pw(v.sdp),!0),this.report.markTime("request_start");break;case IP.EventCode.INF_PLAY_EVT_REQUEST_PULL_SUCCESS:var g={type:"answer",sdp:m.remotesdp};null===(o=(i=this.listener).onPlayEvent)||void 0===o||o.call(i,OE.PLAY_EVT_REQUEST_PULL_SUCCESS,{remoteSdp:g}),this.report.markTime("request_end"),this.report.measureTime("u32_signal_time","request_start","request_end");break;case IP.EventCode.ERR_PLAY_REQUEST_PULL_FAIL:this.onPlayError(OE.PLAY_ERR_REQUEST_PULL_FAIL,{message:h});break;case IP.EventCode.ERR_FETCH_REQUEST_FAIL:"PULL_STREAM"===m.from&&this.onPlayError(OE.PLAY_ERR_REQUEST_PULL_FAIL,{message:h});break;case IP.EventCode.INF_PLAY_EVT_SERVER_CONNECTED:this.startStat(),null===(s=(a=this.listener).onPlayEvent)||void 0===s||s.call(a,OE.PLAY_EVT_SERVER_CONNECTED);break;case IP.EventCode.INF_PLAY_EVT_SERVER_RECONNECT:this.connectRetry.playing=this.isPlaying(),null===(u=(c=this.listener).onPlayEvent)||void 0===u||u.call(c,OE.PLAY_EVT_SERVER_RECONNECT);break;case IP.EventCode.ERR_LOAD_REACH_MAX_RETRY:this.onPlayError(OE.PLAY_ERR_SERVER_DISCONNECT);break;case IP.EventCode.ERR_PLAY_WEBRTC_FAIL:this.onPlayError(OE.PLAY_ERR_WEBRTC_FAIL,{message:h});break;case IP.EventCode.INF_PLAY_EVT_STREAM_SWITCH:var b=null===(l=m.source)||void 0===l?void 0:l.toLowerCase();this.p2pMode.source=b,null===(p=(d=this.listener).onPlayEvent)||void 0===p||p.call(d,OE.PLAY_EVT_P2P_SOURCE_SWITCH,{source:b})}if(f>1e3&&f<2e3){var C;if(this.lastStatsReport=null,this.timer.statsInterval&&(window.clearInterval(this.timer.statsInterval),this.timer.statsInterval=null),this.timer.reportInterval&&(window.clearInterval(this.timer.reportInterval),this.timer.reportInterval=null),this.stream)Hs(C=this.stream.getTracks()).call(C,(function(t){t.stop()})),this.stream=null;this.isVideoExisted&&this.playerView&&(this.playerView.pause(),this.playerView.srcObject=null,this.playerView.load(),this.isVideoExisted=!1),this.p2pConnection.destroy(),this.p2pConnection=null}},t.prototype.onReceiveSEI=function(t){var e,r;null===(r=(e=this.listener).onPlaySEI)||void 0===r||r.call(e,t)},t.version=FE,t}();return xP}));
