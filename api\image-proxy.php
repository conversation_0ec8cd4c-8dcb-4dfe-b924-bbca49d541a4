<?php
/**
 * 图片代理脚本
 * 用于安全地提供上传的图片文件
 */

// 获取请求的图片路径
$image_path = $_GET['path'] ?? '';

if (empty($image_path)) {
    http_response_code(400);
    die('Missing image path');
}

// 安全检查：防止路径遍历攻击
if (strpos($image_path, '..') !== false || strpos($image_path, '/') === 0) {
    http_response_code(403);
    die('Invalid path');
}

// 构建完整的文件路径
$base_dir = $_SERVER['DOCUMENT_ROOT'] . '/uploads/logos/';
$full_path = $base_dir . basename($image_path);

// 检查文件是否存在
if (!file_exists($full_path)) {
    http_response_code(404);
    die('Image not found');
}

// 检查是否为图片文件
$allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
$file_extension = strtolower(pathinfo($full_path, PATHINFO_EXTENSION));

if (!in_array($file_extension, $allowed_extensions)) {
    http_response_code(403);
    die('Invalid file type');
}

// 获取文件信息
$file_size = filesize($full_path);
$file_time = filemtime($full_path);

// 设置适当的Content-Type
$mime_types = [
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif'
];

$content_type = $mime_types[$file_extension] ?? 'application/octet-stream';

// 设置缓存头
$etag = md5($full_path . $file_time . $file_size);
header('ETag: "' . $etag . '"');
header('Last-Modified: ' . gmdate('D, d M Y H:i:s', $file_time) . ' GMT');
header('Cache-Control: public, max-age=3600'); // 缓存1小时

// 检查客户端缓存
$client_etag = $_SERVER['HTTP_IF_NONE_MATCH'] ?? '';
$client_modified = $_SERVER['HTTP_IF_MODIFIED_SINCE'] ?? '';

if ($client_etag === '"' . $etag . '"' || 
    ($client_modified && strtotime($client_modified) >= $file_time)) {
    http_response_code(304);
    exit;
}

// 输出图片
header('Content-Type: ' . $content_type);
header('Content-Length: ' . $file_size);
header('Accept-Ranges: bytes');

// 支持范围请求（用于大文件）
if (isset($_SERVER['HTTP_RANGE'])) {
    $range = $_SERVER['HTTP_RANGE'];
    if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
        $start = intval($matches[1]);
        $end = $matches[2] ? intval($matches[2]) : $file_size - 1;
        
        if ($start < $file_size && $end < $file_size && $start <= $end) {
            http_response_code(206);
            header('Content-Range: bytes ' . $start . '-' . $end . '/' . $file_size);
            header('Content-Length: ' . ($end - $start + 1));
            
            $file = fopen($full_path, 'rb');
            fseek($file, $start);
            echo fread($file, $end - $start + 1);
            fclose($file);
            exit;
        }
    }
}

// 输出完整文件
readfile($full_path);
?>
