<?php
/**
 * 测试管理面板配置功能
 * 用于验证标题配置是否正常工作
 */

session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

// 简单的登录检查（用于测试）
if (!isset($_SESSION['loggedin'])) {
    $_SESSION['loggedin'] = true;
    $_SESSION['username'] = 'test_admin';
}

// 测试配置读取
$panel_config = get_admin_panel_config();

// 渲染页面头部
render_admin_header('管理面板配置测试', 'test');
?>

<!-- 测试结果显示 -->
<?php render_card_start('管理面板配置测试'); ?>
    <div class="admin-form-group">
        <h4>当前配置值：</h4>
        <p><strong>主标题：</strong> <?php echo htmlspecialchars($panel_config['admin_panel_title']); ?></p>
        <p><strong>副标题：</strong> <?php echo htmlspecialchars($panel_config['admin_panel_subtitle']); ?></p>
    </div>
    
    <div class="admin-form-group">
        <h4>测试说明：</h4>
        <ul>
            <li>查看左侧边栏顶部是否显示了正确的标题</li>
            <li>前往 <a href="settings.php">系统设置</a> 修改管理面板标题</li>
            <li>修改后刷新此页面查看是否生效</li>
        </ul>
    </div>
    
    <div class="admin-actions">
        <?php render_link_button('前往系统设置', 'settings.php', 'admin-btn-primary'); ?>
        <?php render_button('刷新页面', 'button', 'admin-btn-secondary', 'location.reload()'); ?>
    </div>
<?php render_card_end(); ?>

<!-- 数据库配置检查 -->
<?php render_card_start('数据库配置检查'); ?>
    <?php
    try {
        $result = $conn->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('admin_panel_title', 'admin_panel_subtitle')");
        if ($result && $result->num_rows > 0) {
            echo '<table class="admin-table">';
            echo '<thead><tr><th>配置项</th><th>配置值</th></tr></thead>';
            echo '<tbody>';
            while ($row = $result->fetch_assoc()) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($row['setting_key']) . '</td>';
                echo '<td>' . htmlspecialchars($row['setting_value']) . '</td>';
                echo '</tr>';
            }
            echo '</tbody></table>';
        } else {
            echo '<p style="color: #e74c3c;">数据库中未找到管理面板配置项，将使用默认值。</p>';
            echo '<p>请前往系统设置页面保存一次配置以创建数据库记录。</p>';
        }
    } catch (Exception $e) {
        echo '<p style="color: #e74c3c;">数据库查询失败：' . htmlspecialchars($e->getMessage()) . '</p>';
    }
    ?>
<?php render_card_end(); ?>

<?php render_admin_footer(); ?>
