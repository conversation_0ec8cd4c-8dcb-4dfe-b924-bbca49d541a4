<template>
	<view class="orders-container">
		<!-- 状态筛选 -->
		<view class="status-tabs">
			<view 
				class="tab-item" 
				:class="{ active: currentStatus === item.value }"
				v-for="item in statusTabs" 
				:key="item.value"
				@click="switchStatus(item.value)"
			>
				{{ item.label }}
			</view>
		</view>
		
		<!-- 订单列表 -->
		<view class="orders-list">
			<!-- 加载状态 -->
			<view class="loading-container" v-if="loading && orders.length === 0">
				<uni-load-more status="loading"></uni-load-more>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-else-if="orders.length === 0 && !loading">
				<simple-icon type="order" size="60" color="#ccc"></simple-icon>
				<text class="empty-text">暂无订单</text>
				<button class="go-shopping-btn" @click="goShopping">去购买课程</button>
			</view>
			
			<!-- 订单项 -->
			<view class="order-item" v-for="order in orders" :key="order.id" @click="goToDetail(order.id)">
				<view class="order-header">
					<view class="order-info">
						<text class="order-no">订单号：{{ order.order_no }}</text>
						<text class="order-time">{{ formatTime(order.created_at) }}</text>
					</view>
					<view class="order-status">
						<uni-tag 
							:text="order.status_text" 
							:type="getStatusColor(order.order_status)"
							size="mini"
						></uni-tag>
					</view>
				</view>
				
				<view class="order-content">
					<!-- 课程列表 -->
					<view class="course-list">
						<view class="course-item" v-for="item in order.items" :key="item.id">
							<image 
								class="course-image" 
								:src="item.thumbnail || item.cover_image || '/static/images/default-course.png'"
								mode="aspectFill"
							></image>
							<view class="course-info">
								<text class="course-title">{{ item.course_title }}</text>
								<view class="course-price">
									<text class="current-price">¥{{ item.course_price }}</text>
									<text class="original-price" v-if="item.original_price > item.course_price">
										¥{{ item.original_price }}
									</text>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 订单金额 -->
					<view class="order-amount">
						<text class="amount-label">实付金额：</text>
						<text class="amount-value">¥{{ order.actual_amount }}</text>
					</view>
				</view>
				
				<!-- 订单操作 -->
				<view class="order-actions">
					<button 
						class="action-btn secondary" 
						v-if="order.order_status === 'pending' && !order.is_expired"
						@click.stop="payOrder(order)"
					>
						立即支付
					</button>
					<button 
						class="action-btn secondary" 
						v-if="order.order_status === 'pending'"
						@click.stop="cancelOrder(order.id)"
					>
						取消订单
					</button>
					<button 
						class="action-btn secondary" 
						v-if="order.order_status === 'paid'"
						@click.stop="goToStudy(order)"
					>
						去学习
					</button>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view class="load-more" v-if="orders.length > 0">
			<uni-load-more 
				:status="loadMoreStatus"
				@clickLoadMore="loadMore"
			></uni-load-more>
		</view>
	</view>
</template>

<script>
	import { isLoggedIn, formatDate, showSuccess, showError, showConfirm } from '../../utils/storage.js';
	import { authMixin } from '../../utils/auth.js';
	import { getOrderList, cancelOrder, wechatPrepay } from '../../api/order.js';
	import SimpleIcon from '../../components/simple-icon/simple-icon.vue';

	export default {
		mixins: [authMixin],
		components: {
			SimpleIcon
		},
		
		data() {
			return {
				loading: false,
				orders: [],
				currentStatus: '',
				page: 1,
				hasMore: true,
				loadMoreStatus: 'more',
				
				statusTabs: [
					{ label: '全部', value: '' },
					{ label: '待支付', value: 'pending' },
					{ label: '已支付', value: 'paid' },
					{ label: '已取消', value: 'cancelled' },
					{ label: '已退款', value: 'refunded' }
				]
			};
		},
		
		onLoad(options) {
			// 检查登录状态
			if (!isLoggedIn()) {
				uni.reLaunch({
					url: '/pages/login/login'
				});
				return;
			}
			
			// 获取状态参数
			if (options.status) {
				this.currentStatus = options.status;
			}
			
			this.loadOrders();
		},
		
		onShow() {
			// 刷新订单列表
			if (this.orders.length > 0) {
				this.refreshOrders();
			}
		},
		
		onPullDownRefresh() {
			this.refreshOrders().finally(() => {
				uni.stopPullDownRefresh();
			});
		},
		
		onReachBottom() {
			if (this.hasMore && this.loadMoreStatus === 'more') {
				this.loadMore();
			}
		},
		
		methods: {
			/**
			 * 切换状态
			 */
			switchStatus(status) {
				if (this.currentStatus === status) return;
				
				this.currentStatus = status;
				this.refreshOrders();
			},
			
			/**
			 * 加载订单列表
			 */
			async loadOrders(isRefresh = false) {
				if (this.loading) return;
				
				this.loading = true;
				
				if (isRefresh) {
					this.page = 1;
					this.hasMore = true;
					this.loadMoreStatus = 'more';
				}
				
				try {
					const params = {
						page: this.page,
						limit: 10
					};
					
					if (this.currentStatus) {
						params.status = this.currentStatus;
					}
					
					const response = await getOrderList(params);
					
					if (response.code === 200) {
						const newOrders = response.data.orders || [];
						
						if (isRefresh) {
							this.orders = newOrders;
						} else {
							this.orders = [...this.orders, ...newOrders];
						}
						
						// 检查是否还有更多数据
						const pagination = response.data.pagination;
						this.hasMore = pagination.page < pagination.pages;
						this.loadMoreStatus = this.hasMore ? 'more' : 'noMore';
						
						if (newOrders.length > 0) {
							this.page++;
						}
					} else {
						showError(response.message || '加载订单失败');
					}
				} catch (error) {
					console.error('加载订单失败:', error);
					showError('加载订单失败');
				} finally {
					this.loading = false;
				}
			},
			
			/**
			 * 刷新订单列表
			 */
			refreshOrders() {
				return this.loadOrders(true);
			},
			
			/**
			 * 加载更多
			 */
			loadMore() {
				if (!this.hasMore || this.loading) return;
				
				this.loadMoreStatus = 'loading';
				this.loadOrders();
			},
			
			/**
			 * 支付订单
			 */
			async payOrder(order) {
				try {
					// 检查订单是否过期
					if (order.is_expired) {
						showError('订单已过期');
						return;
					}
					
					// 获取用户openid
					const openid = uni.getStorageSync('user_openid');
					if (!openid) {
						showError('获取用户信息失败，请重新登录');
						return;
					}
					
					uni.showLoading({
						title: '正在创建支付订单...'
					});
					
					const response = await wechatPrepay(order.id, openid);
					
					if (response.code === 200) {
						const payParams = response.data.pay_params;
						
						// 调用微信支付
						uni.requestPayment({
							...payParams,
							success: () => {
								showSuccess('支付成功');
								this.refreshOrders();
							},
							fail: (err) => {
								console.error('支付失败:', err);
								if (err.errMsg !== 'requestPayment:fail cancel') {
									showError('支付失败');
								}
							}
						});
					} else {
						showError(response.message || '创建支付订单失败');
					}
				} catch (error) {
					console.error('支付失败:', error);
					showError('支付失败');
				} finally {
					uni.hideLoading();
				}
			},
			
			/**
			 * 取消订单
			 */
			async cancelOrder(orderId) {
				const confirmed = await showConfirm('确定要取消这个订单吗？');
				if (!confirmed) return;
				
				try {
					uni.showLoading({
						title: '正在取消订单...'
					});
					
					const response = await cancelOrder(orderId);
					
					if (response.code === 200) {
						showSuccess('订单已取消');
						this.refreshOrders();
					} else {
						showError(response.message || '取消订单失败');
					}
				} catch (error) {
					console.error('取消订单失败:', error);
					showError('取消订单失败');
				} finally {
					uni.hideLoading();
				}
			},
			
			/**
			 * 跳转到订单详情
			 */
			goToDetail(orderId) {
				uni.navigateTo({
					url: `/pages/orders/detail?id=${orderId}`
				});
			},
			
			/**
			 * 去学习
			 */
			goToStudy(order) {
				if (order.items && order.items.length > 0) {
					const courseId = order.items[0].course_id;
					uni.navigateTo({
						url: `/pages/courses/detail?id=${courseId}`
					});
				}
			},
			
			/**
			 * 去购物
			 */
			goShopping() {
				uni.switchTab({
					url: '/pages/courses/list'
				});
			},
			
			/**
			 * 格式化时间
			 */
			formatTime(time) {
				return formatDate(time, 'MM-dd HH:mm');
			},
			
			/**
			 * 获取状态颜色
			 */
			getStatusColor(status) {
				const colorMap = {
					pending: 'warning',
					paid: 'success',
					cancelled: 'default',
					refunded: 'info',
					expired: 'error'
				};
				return colorMap[status] || 'default';
			}
		}
	};
</script>

<style lang="scss" scoped>
.orders-container {
	background: #f8f9fa;
	min-height: 100vh;
}

.status-tabs {
	background: #fff;
	display: flex;
	padding: 0 15px;
	border-bottom: 1px solid #eee;
	
	.tab-item {
		flex: 1;
		text-align: center;
		padding: 15px 0;
		font-size: 14px;
		color: #666;
		position: relative;
		
		&.active {
			color: #007bff;
			font-weight: 500;
			
			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 30px;
				height: 2px;
				background: #007bff;
			}
		}
	}
}

.orders-list {
	padding: 15px;
}

.loading-container {
	padding: 40px 0;
}

.empty-state {
	text-align: center;
	padding: 80px 20px;
	
	.empty-text {
		display: block;
		color: #999;
		font-size: 16px;
		margin: 20px 0 30px;
	}
	
	.go-shopping-btn {
		background: #007bff;
		color: #fff;
		border: none;
		border-radius: 25px;
		padding: 12px 30px;
		font-size: 14px;
	}
}

.order-item {
	background: #fff;
	border-radius: 12px;
	margin-bottom: 15px;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid #f5f5f5;
	
	.order-info {
		.order-no {
			display: block;
			font-size: 14px;
			color: #333;
			font-weight: 500;
			margin-bottom: 5px;
		}
		
		.order-time {
			display: block;
			font-size: 12px;
			color: #999;
		}
	}
}

.order-content {
	padding: 15px;
}

.course-list {
	margin-bottom: 15px;
}

.course-item {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
	
	&:last-child {
		margin-bottom: 0;
	}
	
	.course-image {
		width: 60px;
		height: 45px;
		border-radius: 6px;
		margin-right: 12px;
		background: #f5f5f5;
	}
	
	.course-info {
		flex: 1;
		
		.course-title {
			display: block;
			font-size: 14px;
			color: #333;
			margin-bottom: 5px;
			line-height: 1.4;
		}
		
		.course-price {
			display: flex;
			align-items: center;
			gap: 8px;
			
			.current-price {
				font-size: 14px;
				color: #e74c3c;
				font-weight: 500;
			}
			
			.original-price {
				font-size: 12px;
				color: #999;
				text-decoration: line-through;
			}
		}
	}
}

.order-amount {
	text-align: right;
	padding-top: 10px;
	border-top: 1px solid #f5f5f5;
	
	.amount-label {
		font-size: 14px;
		color: #666;
	}
	
	.amount-value {
		font-size: 16px;
		color: #e74c3c;
		font-weight: 600;
		margin-left: 5px;
	}
}

.order-actions {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
	padding: 15px;
	border-top: 1px solid #f5f5f5;
	
	.action-btn {
		padding: 8px 20px;
		border-radius: 20px;
		font-size: 14px;
		border: 1px solid #ddd;
		background: #fff;
		color: #666;
		
		&.primary {
			background: #007bff;
			border-color: #007bff;
			color: #fff;
		}
		
		&.secondary {
			background: #fff;
			border-color: #007bff;
			color: #007bff;
		}
	}
}

.load-more {
	padding: 20px 0;
}
</style>
