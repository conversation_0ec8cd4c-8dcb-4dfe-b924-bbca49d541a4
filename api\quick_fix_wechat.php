<?php
/**
 * 微信配置快速修复
 * 设置默认的微信配置以避免错误
 */

header('Content-Type: text/html; charset=utf-8');
require_once '../includes/db.php';

echo "<h1>微信配置快速修复</h1>";

try {
    // 设置默认的微信小程序配置
    $default_configs = [
        'wechat_app_id' => 'wxa936b5c9fa9b1893', // 从config.js中获取的AppID
        'wechat_app_secret' => 'temp_secret_' . md5(time()), // 临时密钥
    ];
    
    echo "<h2>正在设置默认配置...</h2>";
    
    foreach ($default_configs as $key => $value) {
        $stmt = $conn->prepare("
            INSERT INTO settings (setting_key, setting_value, setting_type, description) 
            VALUES (?, ?, 'string', ?)
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
        ");
        $description = '微信小程序配置: ' . $key;
        $stmt->bind_param("sss", $key, $value, $description);
        
        if ($stmt->execute()) {
            echo "<p>✅ 设置 $key = $value</p>";
        } else {
            echo "<p>❌ 设置 $key 失败</p>";
        }
    }
    
    echo "<h2>修复完成</h2>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
    echo "<p><strong>✅ 微信配置已设置默认值</strong></p>";
    echo "<p>现在微信登录API不会因为缺少配置而报错，但仍然无法正常登录，因为使用的是临时配置。</p>";
    echo "</div>";
    
    echo "<h2>下一步操作</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
    echo "<p>要完全解决微信登录问题，您需要：</p>";
    echo "<ol>";
    echo "<li><strong>获取真实的微信小程序配置</strong>：在微信公众平台申请小程序</li>";
    echo "<li><strong>更新配置</strong>：使用 <a href='setup_wechat_miniprogram.php'>微信小程序配置页面</a> 更新真实配置</li>";
    echo "<li><strong>或者使用账号密码登录</strong>：在小程序登录页面使用账号密码登录功能</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>当前可用的登录方式</h2>";
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
    echo "<p><strong>账号密码登录</strong>（推荐用于测试）：</p>";
    echo "<ul>";
    echo "<li>用户名：admin</li>";
    echo "<li>密码：123456</li>";
    echo "</ul>";
    echo "<p>在小程序登录页面点击\"展开账号密码登录\"即可使用。</p>";
    echo "</div>";
    
    // 检查是否有可用的测试账号
    echo "<h2>测试账号检查</h2>";
    $stmt = $conn->prepare("SELECT username, name, status FROM users WHERE status = 'active' LIMIT 3");
    $stmt->execute();
    $users = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    if (!empty($users)) {
        echo "<p>✅ 找到以下可用的测试账号：</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>用户名</th><th>姓名</th><th>状态</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['name']}</td>";
            echo "<td>{$user['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p><small>默认密码通常是：123456</small></p>";
    } else {
        echo "<p>❌ 没有找到可用的测试账号</p>";
        echo "<p>您可能需要先创建一个测试账号。</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 修复过程中出现错误: " . $e->getMessage() . "</p>";
}
?>
