<template>
	<view class="courses-container">
		<!-- 搜索栏 -->
		<view class="search-section">
			<uni-search-bar 
				v-model="searchKeyword" 
				placeholder="搜索课程..."
				@confirm="handleSearch"
				@clear="handleSearchClear"
				:focus="false"
				cancelButton="none"
			></uni-search-bar>
		</view>
		
		<!-- 筛选栏 -->
		<view class="filter-section">
			<scroll-view class="filter-scroll" scroll-x>
				<view class="filter-list">
					<view 
						class="filter-item" 
						:class="{ active: currentStatus === status.value }"
						v-for="status in statusOptions" 
						:key="status.value"
						@click="handleStatusFilter(status.value)"
					>
						<text class="filter-text">{{ status.label }}</text>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 课程统计 -->
		<view class="stats-section" v-if="isLoggedIn && courseStats">
			<view class="stats-grid">
				<view class="stat-item">
					<text class="stat-number">{{ courseStats.total_courses || 0 }}</text>
					<text class="stat-label">总课程</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ courseStats.active_courses || 0 }}</text>
					<text class="stat-label">进行中</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ courseStats.average_progress || 0 }}%</text>
					<text class="stat-label">平均进度</text>
				</view>
			</view>

			<!-- 新增学习统计行 -->
			<view class="stats-grid stats-secondary">
				<view class="stat-item">
					<text class="stat-number">{{ courseStats.learning_speed || 0 }}</text>
					<text class="stat-label">学习速度</text>
					<text class="stat-unit">%/小时</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ courseStats.total_watch_hours || 0 }}</text>
					<text class="stat-label">学习时长</text>
					<text class="stat-unit">小时</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ courseStats.study_days || 0 }}</text>
					<text class="stat-label">学习天数</text>
					<text class="stat-unit">天</text>
				</view>
			</view>
		</view>
		
		<!-- 课程列表 -->
		<view class="course-list">
			<view 
				class="course-item" 
				v-for="course in courseList" 
				:key="course.id"
				@click="goToCourseDetail(course.id)"
			>
				<image 
					class="course-thumbnail" 
					:src="course.thumbnail || '/static/default-course.png'" 
					mode="aspectFill"
				></image>
				<view class="course-content">
					<view class="course-header">
						<text class="course-title">{{ course.title }}</text>
						<view class="course-tags">
							<!-- 课程类型标签 -->
							<uni-tag
								v-if="course.user_info && course.user_info.purchase_type"
								:text="getPurchaseTypeText(course.user_info.purchase_type)"
								:type="getPurchaseTypeColor(course.user_info.purchase_type)"
								size="mini"
							></uni-tag>
							<!-- 状态标签 -->
							<uni-tag
								v-if="course.user_info && course.user_info.status"
								:text="getStatusText(course.user_info.status, course.user_info.is_expired)"
								:type="getStatusColor(course.user_info.status, course.user_info.is_expired)"
								size="mini"
							></uni-tag>
						</view>
					</view>
					<text class="course-description">{{ course.description || '暂无描述' }}</text>
					
					<!-- 学习进度 -->
					<view class="course-progress" v-if="course.user_info">
						<view class="progress-info">
							<text class="progress-text">学习进度: {{ course.user_info.watch_progress.toFixed(1) }}%</text>
							<text class="watch-count">观看 {{ course.user_info.watch_count }} 次</text>
						</view>
						<view class="progress-bar">
							<view 
								class="progress-fill" 
								:style="{ width: course.user_info.watch_progress + '%' }"
							></view>
						</view>
					</view>
					
					<view class="course-footer">
						<view class="course-meta-info">
							<!-- 如果用户有课程信息，显示课程信息 -->
							<template v-if="course.user_info">
								<text class="course-duration">{{ course.duration_formatted || '未知时长' }}</text>
								<text class="course-purchase-date" v-if="course.user_info.assigned_at">
									{{ course.user_info.purchase_type === 'purchased' ? '购买时间' : '获得时间' }}: {{ formatDate(course.user_info.assigned_at) }}
								</text>
								<text class="course-expires" v-if="course.user_info.expires_at">
									到期时间: {{ formatDate(course.user_info.expires_at) }}
								</text>
							</template>
							<template v-else>
								<!-- 未购买课程：显示详细信息 -->
								<text class="course-duration">{{ course.duration_formatted || '未知时长' }}</text>
								<text class="course-lessons" v-if="course.lesson_count">{{ course.lesson_count }}课时</text>
								<text class="course-price" v-if="!course.is_free">¥{{ parseFloat(course.price || 0).toFixed(2) }}</text>
								<text class="course-free" v-else>免费</text>
							</template>
						</view>
						<text class="course-time" v-if="course.user_info && course.user_info.last_watched_at">
							最后学习: {{ formatDate(course.user_info.last_watched_at) }}
						</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="!loading && courseList.length === 0">
			<simple-icon type="video" size="40" color="#999" class="empty-icon"></simple-icon>
			<text class="empty-text">{{ getEmptyText() }}</text>
			<button class="empty-action" @click="handleEmptyAction" v-if="!isLoggedIn">
				立即登录
			</button>
		</view>
		
		<!-- 加载更多 -->
		<view class="load-more" v-if="courseList.length > 0">
			<uni-load-more 
				:status="loadMoreStatus" 
				:content-text="loadMoreText"
				@clickLoadMore="loadMoreCourses"
			></uni-load-more>
		</view>
	</view>
</template>

<script>
	import { isLoggedIn, getUserInfo, formatDate } from '../../utils/storage.js';
	import { authMixin } from '../../utils/auth.js';
	import { getUserCourses, getCourseList } from '../../api/course.js';
	import CONFIG from '../../utils/config.js';
	import SimpleIcon from '../../components/simple-icon/simple-icon.vue';

	export default {
		mixins: [authMixin],
		components: {
			SimpleIcon
		},
		
		data() {
			return {
				loading: false,
				isLoggedIn: false,
				searchKeyword: '',
				currentStatus: 'all',
				currentPage: 1,
				hasMore: true,
				courseList: [],
				courseStats: null,
				loadMoreStatus: 'more',
				loadMoreText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多数据了'
				},
				
				// 状态筛选选项
				statusOptions: [
					{ label: '全部', value: 'all' },
					{ label: '已购买', value: 'purchased' },
					{ label: '分配的', value: 'assigned' },
					{ label: '进行中', value: 'active' },
					{ label: '已过期', value: 'expired' },
					{ label: '已撤销', value: 'revoked' }
				]
			};
		},
		
		onLoad() {
			this.initPage();
		},
		
		onShow() {
			this.checkLoginStatus();
			// 只有在登录状态下才加载数据
			if (this.isLoggedIn) {
				this.loadCourseData();
			}
		},
		
		onPullDownRefresh() {
			this.refreshCourseData().finally(() => {
				uni.stopPullDownRefresh();
			});
		},
		
		onReachBottom() {
			if (this.hasMore && this.loadMoreStatus === 'more') {
				this.loadMoreCourses();
			}
		},
		
		methods: {
			/**
			 * 初始化页面
			 */
			initPage() {
				this.checkLoginStatus();
				this.loadCourseData();
			},
			
			/**
			 * 检查登录状态
			 */
			checkLoginStatus() {
				this.isLoggedIn = isLoggedIn();
			},
			
			/**
			 * 加载课程数据
			 */
			async loadCourseData() {
				this.loading = true;
				this.currentPage = 1;
				this.hasMore = true;
				this.loadMoreStatus = 'more';
				
				try {
					const response = await this.fetchCourses();
					if (response.code === 200) {
						this.courseList = response.data.list || [];
						this.courseStats = response.data.statistics || null;
						
						// 检查是否还有更多数据
						const pagination = response.data.pagination;
						if (pagination) {
							this.hasMore = pagination.page < pagination.pages;
							this.loadMoreStatus = this.hasMore ? 'more' : 'noMore';
						}
					}
				} catch (error) {
					console.error('加载课程数据失败:', error);
					uni.showToast({
						title: error.message || '加载失败',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			},
			
			/**
			 * 刷新课程数据
			 */
			async refreshCourseData() {
				await this.loadCourseData();
			},
			
			/**
			 * 加载更多课程
			 */
			async loadMoreCourses() {
				if (!this.hasMore || this.loadMoreStatus !== 'more') return;
				
				this.loadMoreStatus = 'loading';
				this.currentPage++;
				
				try {
					const response = await this.fetchCourses();
					if (response.code === 200) {
						const newCourses = response.data.list || [];
						this.courseList = [...this.courseList, ...newCourses];
						
						// 检查是否还有更多数据
						const pagination = response.data.pagination;
						if (pagination) {
							this.hasMore = pagination.page < pagination.pages;
							this.loadMoreStatus = this.hasMore ? 'more' : 'noMore';
						}
					}
				} catch (error) {
					console.error('加载更多课程失败:', error);
					this.currentPage--; // 回退页码
					this.loadMoreStatus = 'more';
					uni.showToast({
						title: error.message || '加载失败',
						icon: 'none'
					});
				}
			},
			
			/**
			 * 获取课程数据
			 */
			fetchCourses() {
				const params = {
					page: this.currentPage,
					limit: CONFIG.PAGE_SIZE,
					search: this.searchKeyword
				};

				if (this.isLoggedIn) {
					// 已登录用户获取个人课程
					// 根据筛选条件设置参数
					if (this.currentStatus === 'purchased') {
						// 只获取购买的课程
						params.purchase_type = 'purchased';
						params.status = 'active'; // 只获取有效的课程
					} else if (this.currentStatus === 'assigned') {
						// 只获取分配的课程
						params.purchase_type = 'assigned';
						params.status = 'active'; // 只获取有效的课程
					} else {
						// 其他状态筛选
						params.status = this.currentStatus;
					}
					return getUserCourses(params);
				} else {
					// 未登录用户获取公开课程列表
					return getCourseList(params);
				}
			},
			
			/**
			 * 处理搜索
			 */
			handleSearch() {
				this.loadCourseData();
			},
			
			/**
			 * 清除搜索
			 */
			handleSearchClear() {
				this.searchKeyword = '';
				this.loadCourseData();
			},
			
			/**
			 * 状态筛选
			 */
			handleStatusFilter(status) {
				if (this.currentStatus === status) return;
				
				this.currentStatus = status;
				this.loadCourseData();
			},
			
			/**
			 * 跳转到课程详情
			 */
			goToCourseDetail(courseId) {
				uni.navigateTo({
					url: `/pages/courses/detail?id=${courseId}`
				});
			},
			
			/**
			 * 获取状态文本
			 */
			getStatusText(status, isExpired) {
				if (status === 'active' && !isExpired) return '进行中';
				if (status === 'active' && isExpired) return '已过期';
				if (status === 'expired') return '已过期';
				if (status === 'revoked') return '已撤销';
				return '未知';
			},
			
			/**
			 * 获取状态颜色
			 */
			getStatusColor(status, isExpired) {
				if (status === 'active' && !isExpired) return 'success';
				if (status === 'active' && isExpired) return 'warning';
				if (status === 'expired') return 'warning';
				if (status === 'revoked') return 'error';
				return 'default';
			},

			/**
			 * 获取课程类型文本
			 */
			getPurchaseTypeText(purchaseType) {
				switch (purchaseType) {
					case 'purchased':
						return '已购买';
					case 'assigned':
						return '分配的';
					default:
						return '未知';
				}
			},

			/**
			 * 获取课程类型颜色
			 */
			getPurchaseTypeColor(purchaseType) {
				switch (purchaseType) {
					case 'purchased':
						return 'primary';
					case 'assigned':
						return 'success';
					default:
						return 'default';
				}
			},
			
			/**
			 * 获取空状态文本
			 */
			getEmptyText() {
				if (!this.isLoggedIn) {
					return '登录后查看您的课程';
				}

				if (this.searchKeyword) {
					return '没有找到相关课程';
				}

				switch (this.currentStatus) {
					case 'purchased':
						return '暂无已购买的课程';
					case 'assigned':
						return '暂无分配的课程';
					case 'active':
						return '暂无进行中的课程';
					case 'expired':
						return '暂无过期课程';
					case 'revoked':
						return '暂无撤销课程';
					case 'all':
						return '暂无课程';
					default:
						return '暂无课程';
				}
			},
			
			/**
			 * 空状态操作
			 */
			handleEmptyAction() {
				if (!this.isLoggedIn) {
					uni.navigateTo({
						url: '/pages/login/login'
					});
				}
			},
			
			/**
			 * 格式化日期
			 */
			formatDate(date) {
				return formatDate(date, 'MM-DD HH:mm');
			}
		}
	};
</script>

<style lang="scss" scoped>
.courses-container {
	background: #f8f9fa;
	min-height: 100vh;
}

.search-section {
	background: #fff;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.filter-section {
	background: #fff;
	border-bottom: 1px solid #f0f0f0;

	.filter-scroll {
		white-space: nowrap;

		.filter-list {
			display: inline-flex;
			padding: 10px 15px;
			gap: 15px;

			.filter-item {
				padding: 8px 16px;
				border-radius: 20px;
				background: #f8f9fa;
				border: 1px solid #e9ecef;

				&.active {
					background: #007bff;
					border-color: #007bff;

					.filter-text {
						color: #fff;
					}
				}

				.filter-text {
					font-size: 14px;
					color: #666;
					white-space: nowrap;
				}
			}
		}
	}
}

.stats-section {
	background: #fff;
	margin: 10px 15px;
	border-radius: 12px;
	padding: 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20px;

		&.stats-secondary {
			margin-top: 20px;
			padding-top: 20px;
			border-top: 1px solid #f0f0f0;
		}

		.stat-item {
			text-align: center;

			.stat-number {
				display: block;
				font-size: 24px;
				font-weight: bold;
				color: #007bff;
				margin-bottom: 5px;
			}

			.stat-label {
				display: block;
				font-size: 12px;
				color: #666;
				margin-bottom: 2px;
			}

			.stat-unit {
				display: block;
				font-size: 10px;
				color: #999;
			}
		}
	}

	.stats-secondary .stat-item .stat-number {
		color: #28a745;
		font-size: 20px;
	}
}

.course-list {
	padding: 10px 15px;

	.course-item {
		background: #fff;
		border-radius: 12px;
		margin-bottom: 15px;
		overflow: hidden;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		display: flex;

		.course-thumbnail {
			width: 120px;
			height: 90px;
			flex-shrink: 0;
		}

		.course-content {
			flex: 1;
			padding: 15px;
			display: flex;
			flex-direction: column;

			.course-header {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				margin-bottom: 8px;

				.course-title {
					flex: 1;
					font-size: 16px;
					font-weight: 500;
					color: #333;
					margin-right: 10px;
					line-height: 1.4;
				}

				.course-tags {
					display: flex;
					flex-direction: column;
					gap: 4px;
					align-items: flex-end;
				}
			}

			.course-description {
				font-size: 14px;
				color: #666;
				margin-bottom: 12px;
				line-height: 1.4;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
			}

			.course-progress {
				margin-bottom: 12px;

				.progress-info {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 8px;

					.progress-text {
						font-size: 12px;
						color: #007bff;
					}

					.watch-count {
						font-size: 12px;
						color: #999;
					}
				}

				.progress-bar {
					height: 4px;
					background: #e9ecef;
					border-radius: 2px;
					overflow: hidden;

					.progress-fill {
						height: 100%;
						background: #007bff;
						border-radius: 2px;
						transition: width 0.3s ease;
					}
				}
			}

			.course-footer {
				margin-top: auto;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.course-duration {
					font-size: 12px;
					color: #999;
				}

				.course-time {
					font-size: 12px;
					color: #999;
				}
			}
		}
	}
}

.empty-state {
	text-align: center;
	padding: 60px 20px;

	.empty-icon {
		margin-bottom: 15px;
	}

	.empty-text {
		display: block;
		font-size: 16px;
		color: #999;
		margin: 20px 0;
	}

	.empty-action {
		background: #007bff;
		color: #fff;
		border: none;
		border-radius: 20px;
		padding: 10px 30px;
		font-size: 14px;
	}
}

.load-more {
	padding: 20px;
}
</style>

<style lang="scss" scoped>
.courses-container {
	background: #f8f9fa;
	min-height: 100vh;
}

.search-section {
	background: #fff;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.filter-section {
	background: #fff;
	border-bottom: 1px solid #f0f0f0;

	.filter-scroll {
		white-space: nowrap;

		.filter-list {
			display: inline-flex;
			padding: 10px 15px;
			gap: 15px;

			.filter-item {
				padding: 8px 16px;
				border-radius: 20px;
				background: #f8f9fa;
				border: 1px solid #e9ecef;

				&.active {
					background: #007bff;
					border-color: #007bff;

					.filter-text {
						color: #fff;
					}
				}

				.filter-text {
					font-size: 14px;
					color: #666;
					white-space: nowrap;
				}
			}
		}
	}
}

.stats-section {
	background: #fff;
	margin: 10px 15px;
	border-radius: 12px;
	padding: 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20px;

		.stat-item {
			text-align: center;

			.stat-number {
				display: block;
				font-size: 24px;
				font-weight: bold;
				color: #007bff;
				margin-bottom: 5px;
			}

			.stat-label {
				display: block;
				font-size: 12px;
				color: #666;
			}
		}
	}
}

.course-list {
	padding: 10px 15px;

	.course-item {
		background: #fff;
		border-radius: 12px;
		margin-bottom: 15px;
		overflow: hidden;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		display: flex;

		.course-thumbnail {
			width: 120px;
			height: 90px;
			flex-shrink: 0;
		}

		.course-content {
			flex: 1;
			padding: 15px;
			display: flex;
			flex-direction: column;

			.course-header {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				margin-bottom: 8px;

				.course-title {
					flex: 1;
					font-size: 16px;
					font-weight: 500;
					color: #333;
					margin-right: 10px;
					line-height: 1.4;
				}

				.course-tags {
					display: flex;
					flex-direction: column;
					gap: 4px;
					align-items: flex-end;
				}
			}

			.course-description {
				font-size: 14px;
				color: #666;
				margin-bottom: 12px;
				line-height: 1.4;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
			}

			.course-progress {
				margin-bottom: 12px;

				.progress-info {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 8px;

					.progress-text {
						font-size: 12px;
						color: #007bff;
					}

					.watch-count {
						font-size: 12px;
						color: #999;
					}
				}

				.progress-bar {
					height: 4px;
					background: #e9ecef;
					border-radius: 2px;
					overflow: hidden;

					.progress-fill {
						height: 100%;
						background: #007bff;
						border-radius: 2px;
						transition: width 0.3s ease;
					}
				}
			}

			.course-footer {
				margin-top: auto;

				.course-meta-info {
					display: flex;
					align-items: center;
					gap: 8px;
					margin-bottom: 5px;

					.course-duration {
						font-size: 12px;
						color: #999;
					}

					.course-lessons {
						font-size: 12px;
						color: #007bff;
						background: #e3f2fd;
						padding: 2px 6px;
						border-radius: 10px;
					}

					.course-price {
						font-size: 12px;
						color: #dc3545;
						font-weight: 500;
					}

					.course-free {
						font-size: 12px;
						color: #28a745;
						background: #d4edda;
						padding: 2px 6px;
						border-radius: 10px;
					}

					.course-status {
						font-size: 12px;
						color: #007bff;
						background: #e3f2fd;
						padding: 2px 6px;
						border-radius: 10px;
						font-weight: 500;
					}

					.course-purchase-date,
					.course-expires {
						font-size: 12px;
						color: #666;
					}
				}

				.course-time {
					font-size: 12px;
					color: #999;
				}
			}
		}
	}
}

.empty-state {
	text-align: center;
	padding: 60px 20px;

	.empty-text {
		display: block;
		font-size: 16px;
		color: #999;
		margin: 20px 0;
	}

	.empty-action {
		background: #007bff;
		color: #fff;
		border: none;
		border-radius: 20px;
		padding: 10px 30px;
		font-size: 14px;
	}
}

.load-more {
	padding: 20px;
}
</style>
