/**
 * 系统设置API服务
 * 处理系统配置、设置等相关接口
 */

import request from '../utils/request.js';

/**
 * 获取系统设置
 * @returns {Promise} 系统设置数据
 */
export function getSystemSettings() {
	return request.get('system-settings.php');
}

/**
 * 检查功能是否启用
 * @param {String} featureName 功能名称
 * @param {Object} settings 系统设置对象
 * @returns {Boolean} 功能是否启用
 */
export function isFeatureEnabled(featureName, settings) {
	if (!settings || typeof settings !== 'object') {
		return false;
	}

	switch (featureName) {
		case 'password_change':
			return settings.allow_password_change === true;
		case 'profile_edit':
			return settings.allow_profile_edit === true;
		case 'phone_bind':
			return settings.phone_bind_required === true;
		case 'customer_service':
			return settings.enable_customer_service !== false; // 默认启用
		default:
			return false;
	}
}

/**
 * 获取功能禁用提示信息
 * @param {String} featureName 功能名称
 * @param {Object} settings 系统设置对象
 * @returns {String} 提示信息
 */
export function getFeatureDisabledMessage(featureName, settings) {
	if (!settings || typeof settings !== 'object') {
		return '功能暂时不可用';
	}

	switch (featureName) {
		case 'password_change':
			return settings.password_change_notice || '管理员已禁用密码修改功能，如需修改密码请联系客服。';
		case 'profile_edit':
			return '管理员已禁用个人信息编辑功能，如需修改请联系客服。';
		case 'phone_bind':
			return '手机号绑定功能暂时不可用。';
		case 'customer_service':
			return settings.customer_service_disabled_notice || '客服功能暂时不可用，请稍后再试。';
		default:
			return '该功能暂时不可用。';
	}
}

/**
 * 缓存系统设置到本地存储
 * @param {Object} settings 系统设置对象
 */
export function cacheSystemSettings(settings) {
	try {
		uni.setStorageSync('system_settings', settings);
		uni.setStorageSync('system_settings_cache_time', Date.now());
	} catch (error) {
		console.error('缓存系统设置失败:', error);
	}
}

/**
 * 从本地存储获取系统设置
 * @param {Number} maxAge 最大缓存时间（毫秒），默认5分钟
 * @returns {Object|null} 系统设置对象或null
 */
export function getCachedSystemSettings(maxAge = 5 * 60 * 1000) {
	try {
		const cacheTime = uni.getStorageSync('system_settings_cache_time');
		if (!cacheTime || (Date.now() - cacheTime) > maxAge) {
			return null;
		}
		
		return uni.getStorageSync('system_settings');
	} catch (error) {
		console.error('获取缓存的系统设置失败:', error);
		return null;
	}
}

/**
 * 获取系统设置（优先使用缓存）
 * @param {Boolean} forceRefresh 是否强制刷新
 * @returns {Promise} 系统设置数据
 */
export function getSystemSettingsWithCache(forceRefresh = false) {
	return new Promise((resolve, reject) => {
		// 如果不强制刷新，先尝试从缓存获取
		if (!forceRefresh) {
			const cachedSettings = getCachedSystemSettings();
			if (cachedSettings) {
				resolve({
					code: 200,
					message: '获取系统设置成功（缓存）',
					data: cachedSettings
				});
				return;
			}
		}
		
		// 从服务器获取最新设置
		getSystemSettings()
			.then(response => {
				if (response.code === 200 && response.data) {
					// 缓存到本地
					cacheSystemSettings(response.data);
				}
				resolve(response);
			})
			.catch(error => {
				// 如果网络请求失败，尝试使用缓存
				const cachedSettings = getCachedSystemSettings(24 * 60 * 60 * 1000); // 24小时内的缓存
				if (cachedSettings) {
					console.warn('网络请求失败，使用缓存的系统设置:', error);
					resolve({
						code: 200,
						message: '获取系统设置成功（缓存）',
						data: cachedSettings
					});
				} else {
					reject(error);
				}
			});
	});
}

/**
 * 清除系统设置缓存
 */
export function clearSystemSettingsCache() {
	try {
		uni.removeStorageSync('system_settings');
		uni.removeStorageSync('system_settings_cache_time');
	} catch (error) {
		console.error('清除系统设置缓存失败:', error);
	}
}

/**
 * 获取客服配置信息
 * @param {Object} settings 系统设置对象
 * @returns {Object} 客服配置信息
 */
export function getCustomerServiceConfig(settings) {
	if (!settings || typeof settings !== 'object') {
		return {
			enabled: true,
			type: 'wechat', // wechat, enterprise_wechat, phone
			wechat_id: '',
			enterprise_wechat_id: '',
			phone: '',
			service_hours: '9:00-18:00',
			auto_reply: '您好，我们会尽快为您处理，请稍等。'
		};
	}

	return {
		enabled: settings.enable_customer_service !== false,
		type: settings.customer_service_type || 'wechat',
		wechat_id: settings.customer_service_wechat_id || '',
		enterprise_wechat_id: settings.customer_service_enterprise_wechat_id || '',
		phone: settings.customer_service_phone || '',
		service_hours: settings.customer_service_hours || '9:00-18:00',
		auto_reply: settings.customer_service_auto_reply || '您好，我们会尽快为您处理，请稍等。'
	};
}
