<?php
/**
 * 测试课程图片上传功能
 */
session_start();

// 模拟管理员登录（仅用于测试）
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = 1;
    $_SESSION['admin_username'] = 'test_admin';
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试课程图片上传</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background: #fafafa;
        }
        .upload-area:hover {
            border-color: #007bff;
            background: #f0f8ff;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .preview-area {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .preview-img {
            max-width: 300px;
            max-height: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s;
        }
        .message {
            padding: 12px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-upload"></i> 课程图片上传测试</h1>
        <p>这是一个测试页面，用于验证课程图片上传功能是否正常工作。</p>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #ccc; margin-bottom: 20px;"></i>
            <h3>点击选择图片或拖拽图片到此处</h3>
            <p>支持 JPG、PNG、GIF、WebP 格式，最大 5MB</p>
            <button type="button" class="btn">
                <i class="fas fa-folder-open"></i> 选择文件
            </button>
        </div>
        
        <input type="file" id="fileInput" accept="image/*" style="display: none;">
        
        <div id="progressArea" style="display: none;">
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <p id="progressText">准备上传...</p>
        </div>
        
        <div id="messageArea"></div>
        
        <div id="previewArea" style="display: none;" class="preview-area">
            <h4>上传结果</h4>
            <img id="previewImage" class="preview-img" src="" alt="预览图">
            <div style="margin-top: 10px;">
                <p><strong>文件名:</strong> <span id="fileName"></span></p>
                <p><strong>文件大小:</strong> <span id="fileSize"></span></p>
                <p><strong>访问链接:</strong> <a id="fileUrl" href="" target="_blank"></a></p>
                <button type="button" class="btn btn-danger" onclick="clearPreview()">
                    <i class="fas fa-trash"></i> 清除
                </button>
            </div>
        </div>
    </div>

    <script>
        const fileInput = document.getElementById('fileInput');
        const progressArea = document.getElementById('progressArea');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const messageArea = document.getElementById('messageArea');
        const previewArea = document.getElementById('previewArea');

        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                uploadFile(file);
            }
        });

        function uploadFile(file) {
            // 验证文件类型
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(file.type.toLowerCase())) {
                showMessage('只支持 JPG、PNG、GIF、WebP 格式的图片', 'error');
                return;
            }

            // 验证文件大小
            const maxSize = 5 * 1024 * 1024; // 5MB
            if (file.size > maxSize) {
                showMessage('图片大小不能超过5MB', 'error');
                return;
            }

            // 显示进度
            progressArea.style.display = 'block';
            previewArea.style.display = 'none';
            clearMessages();

            const formData = new FormData();
            formData.append('image', file);

            const xhr = new XMLHttpRequest();

            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percent = (e.loaded / e.total) * 100;
                    progressFill.style.width = percent + '%';
                    progressText.textContent = `上传中... ${Math.round(percent)}%`;
                }
            });

            xhr.addEventListener('load', function() {
                progressArea.style.display = 'none';
                
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            showUploadSuccess(response.data);
                            showMessage('图片上传成功！', 'success');
                        } else {
                            showMessage('上传失败：' + response.message, 'error');
                        }
                    } catch (e) {
                        showMessage('上传失败：服务器响应格式错误', 'error');
                    }
                } else {
                    showMessage('上传失败：服务器错误 ' + xhr.status, 'error');
                }
            });

            xhr.addEventListener('error', function() {
                progressArea.style.display = 'none';
                showMessage('上传失败：网络错误', 'error');
            });

            xhr.open('POST', 'upload_course_image.php');
            xhr.send(formData);
        }

        function showUploadSuccess(data) {
            const fullUrl = window.location.origin + data.file_url;
            
            document.getElementById('previewImage').src = fullUrl;
            document.getElementById('fileName').textContent = data.file_name;
            document.getElementById('fileSize').textContent = formatFileSize(data.file_size);
            document.getElementById('fileUrl').textContent = fullUrl;
            document.getElementById('fileUrl').href = fullUrl;
            
            previewArea.style.display = 'block';
        }

        function clearPreview() {
            previewArea.style.display = 'none';
            fileInput.value = '';
        }

        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + type;
            messageDiv.textContent = message;
            messageArea.appendChild(messageDiv);
            
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 5000);
        }

        function clearMessages() {
            messageArea.innerHTML = '';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 拖拽上传支持
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.background = '#f0f8ff';
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#ddd';
            uploadArea.style.background = '#fafafa';
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#ddd';
            uploadArea.style.background = '#fafafa';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                uploadFile(files[0]);
            }
        });
    </script>
</body>
</html>
