<?php
/**
 * 用户认证API基础类
 * 提供JWT认证、用户验证等基础功能
 */

// 动态确定数据库连接文件路径
$db_path = __DIR__ . '/../includes/db.php';
if (!file_exists($db_path)) {
    $db_path = dirname(__DIR__) . '/includes/db.php';
}
if (!file_exists($db_path)) {
    $db_path = 'includes/db.php';
}
require_once $db_path;

class AuthAPI {
    private $conn;
    private $jwt_secret;
    private $access_token_expire;
    private $refresh_token_expire;
    
    public function __construct() {
        global $conn;
        
        if (!$conn || $conn->connect_error) {
            throw new Exception('数据库连接失败: ' . ($conn->connect_error ?? 'Unknown error'));
        }
        
        $this->conn = $conn;
        $this->loadSettings();
    }
    
    /**
     * 加载系统设置
     */
    private function loadSettings() {
        try {
            // Check if settings table exists
            $table_check = $this->conn->query("SHOW TABLES LIKE 'settings'");
            if (!$table_check || $table_check->num_rows == 0) {
                // Settings table doesn't exist, use defaults
                $this->jwt_secret = 'default-secret-key-' . md5(__DIR__);
                $this->access_token_expire = 7200;
                $this->refresh_token_expire = 604800;
                return;
            }
            
            $result = $this->conn->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('jwt_secret_key', 'jwt_access_token_expire', 'jwt_refresh_token_expire')");
            $settings = [];
            
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $settings[$row['setting_key']] = $row['setting_value'];
                }
            }
            
            $this->jwt_secret = $settings['jwt_secret_key'] ?? 'default-secret-key-' . md5(__DIR__);
            $this->access_token_expire = intval($settings['jwt_access_token_expire'] ?? 7200);
            $this->refresh_token_expire = intval($settings['jwt_refresh_token_expire'] ?? 604800);
            
        } catch (Exception $e) {
            // If any error occurs, use safe defaults
            error_log('Settings loading error: ' . $e->getMessage());
            $this->jwt_secret = 'default-secret-key-' . md5(__DIR__);
            $this->access_token_expire = 7200;
            $this->refresh_token_expire = 604800;
        }
    }
    
    /**
     * 生成JWT令牌
     */
    public function generateJWT($user_id, $type = 'access') {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        
        $expire_time = $type === 'access' ? $this->access_token_expire : $this->refresh_token_expire;
        $payload = json_encode([
            'user_id' => $user_id,
            'type' => $type,
            'iat' => time(),
            'exp' => time() + $expire_time
        ]);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $this->jwt_secret, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    /**
     * 验证JWT令牌
     */
    public function verifyJWT($token) {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return false;
        }
        
        list($header, $payload, $signature) = $parts;
        
        $valid_signature = str_replace(['+', '/', '='], ['-', '_', ''], 
            base64_encode(hash_hmac('sha256', $header . "." . $payload, $this->jwt_secret, true)));
        
        if ($signature !== $valid_signature) {
            return false;
        }
        
        $payload_data = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $payload)), true);
        
        if (!$payload_data || $payload_data['exp'] < time()) {
            return false;
        }
        
        return $payload_data;
    }
    
    /**
     * 保存令牌到数据库
     */
    public function saveToken($user_id, $token, $type = 'access', $device_info = null) {
        $expires_at = date('Y-m-d H:i:s', time() + ($type === 'access' ? $this->access_token_expire : $this->refresh_token_expire));
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $stmt = $this->conn->prepare("INSERT INTO user_tokens (user_id, token, token_type, expires_at, device_info, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("issssss", $user_id, $token, $type, $expires_at, $device_info, $ip_address, $user_agent);
        
        return $stmt->execute();
    }
    
    /**
     * 撤销令牌
     */
    public function revokeToken($token) {
        $stmt = $this->conn->prepare("UPDATE user_tokens SET is_revoked = 1 WHERE token = ?");
        $stmt->bind_param("s", $token);
        return $stmt->execute();
    }
    
    /**
     * 检查令牌是否被撤销
     */
    public function isTokenRevoked($token) {
        $stmt = $this->conn->prepare("SELECT is_revoked FROM user_tokens WHERE token = ?");
        $stmt->bind_param("s", $token);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            return $row['is_revoked'] == 1;
        }
        
        return true; // 如果找不到令牌，认为已撤销
    }
    
    /**
     * 验证用户密码
     */
    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * 哈希密码
     */
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * 记录登录日志
     */
    public function logLogin($user_id, $login_type, $login_method, $status, $failure_reason = null) {
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $stmt = $this->conn->prepare("INSERT INTO user_login_logs (user_id, login_type, login_method, login_status, failure_reason, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("issssss", $user_id, $login_type, $login_method, $status, $failure_reason, $ip_address, $user_agent);
        
        return $stmt->execute();
    }
    
    /**
     * 更新用户最后登录信息
     */
    public function updateLastLogin($user_id) {
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        $stmt = $this->conn->prepare("UPDATE users SET last_login_at = NOW(), last_login_ip = ?, login_count = login_count + 1 WHERE id = ?");
        $stmt->bind_param("si", $ip_address, $user_id);
        return $stmt->execute();
    }
    
    /**
     * 获取用户信息
     */
    public function getUserById($user_id) {
        $stmt = $this->conn->prepare("SELECT id, name, email, username, phone, avatar, nickname, gender, birthday, status, login_type, phone_verified, phone_bind_required, phone_bind_time, last_login_at, login_count, created_at, updated_at FROM users WHERE id = ? AND status != 'banned'");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        return $result->fetch_assoc();
    }
    
    /**
     * 通过用户名或邮箱获取用户
     */
    public function getUserByCredential($credential) {
        $stmt = $this->conn->prepare("SELECT id, name, email, username, password, status, login_type FROM users WHERE (username = ? OR email = ?) AND status != 'banned'");
        $stmt->bind_param("ss", $credential, $credential);
        $stmt->execute();
        $result = $stmt->get_result();
        
        return $result->fetch_assoc();
    }
    
    /**
     * 响应JSON数据
     * @param int $code 状态码
     * @param string $message 消息
     * @param mixed $data 数据
     * @param array $meta 元数据
     */
    public function jsonResponse($code, $message, $data = null, $meta = null) {
        header('Content-Type: application/json; charset=utf-8');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');

        $response = [
            'code' => $code,
            'message' => $message,
            'timestamp' => time()
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        // 添加元数据
        if ($meta !== null && is_array($meta)) {
            foreach ($meta as $key => $value) {
                if ($key !== 'data' && $key !== 'code' && $key !== 'message') {
                    $response[$key] = $value;
                }
            }
        }

        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 获取Authorization头中的Bearer令牌
     */
    public function getBearerToken() {
        $headers = getallheaders();
        if (isset($headers['Authorization'])) {
            if (preg_match('/Bearer\s+(.*)$/i', $headers['Authorization'], $matches)) {
                return $matches[1];
            }
        }
        return null;
    }
    
    /**
     * 验证当前请求的用户
     */
    public function getCurrentUser() {
        $token = $this->getBearerToken();
        if (!$token) {
            return null;
        }
        
        if ($this->isTokenRevoked($token)) {
            return null;
        }
        
        $payload = $this->verifyJWT($token);
        if (!$payload || $payload['type'] !== 'access') {
            return null;
        }
        
        return $this->getUserById($payload['user_id']);
    }
    
    /**
     * 要求用户认证
     */
    public function requireAuth() {
        $user = $this->getCurrentUser();
        if (!$user) {
            $this->jsonResponse(401, '未授权访问，请先登录');
        }
        return $user;
    }

    /**
     * 获取数据库连接
     */
    public function getConn() {
        return $this->conn;
    }
}