<?php
/**
 * 课程详情API
 * 支持JWT认证，获取课程详情和更新观看进度
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

/**
 * 验证用户课程访问权限
 */
function validateUserAccess($auth, $user_id, $course_id) {
    $conn = $auth->getConn();

    // 检查用户是否有课程权限
    $access_stmt = $conn->prepare("
        SELECT id FROM user_courses
        WHERE user_id = ? AND course_id = ? AND status = 'active'
        AND (expires_at IS NULL OR expires_at > NOW())
    ");
    $access_stmt->bind_param("ii", $user_id, $course_id);
    $access_stmt->execute();
    return $access_stmt->get_result()->num_rows > 0;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            // GET请求可以不需要认证，但需要user_id参数来获取用户相关信息
            $user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;
            if ($user_id > 0) {
                $user = $auth->requireAuth();
                if ($user['id'] != $user_id) {
                    $auth->jsonResponse(403, '无权访问其他用户的课程信息');
                }
                getCourseDetail($auth, $user);
            } else {
                getCourseDetail($auth, null);
            }
            break;
        case 'POST':
            // POST请求需要认证
            $user = $auth->requireAuth();
            updateWatchProgress($auth, $user);
            break;
        default:
            $auth->jsonResponse(405, '不支持的请求方法');
    }

} catch (Exception $e) {
    error_log('课程详情API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

function getCourseDetail($auth, $user) {
    $conn = $auth->getConn();

    $course_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    $user_id = $user ? $user['id'] : 0;

    if ($course_id <= 0) {
        $auth->jsonResponse(400, '缺少课程ID');
    }

    // 先检查用户是否有课程权限
    $user_has_access = false;
    if ($user_id > 0) {
        $user_has_access = validateUserAccess($auth, $user_id, $course_id);
    }

    // 构建课程查询条件：如果用户有权限，则忽略课程状态；否则只显示启用的课程
    $status_condition = $user_has_access ? "" : "AND status = 'active'";

    // 获取课程基本信息
    $stmt = $conn->prepare("
        SELECT id, title, description, video_url, thumbnail, duration,
               sort_order, created_at, status,
               COALESCE(cover_image, thumbnail) as cover_image,
               COALESCE(price, 0.00) as price,
               original_price,
               COALESCE(is_free, 1) as is_free,
               COALESCE(is_on_sale, 1) as is_on_sale,
               COALESCE(teacher_name, '专业讲师') as teacher_name,
               subtitle,
               COALESCE(rating, 4.5) as rating,
               COALESCE(student_count, 0) as student_count,
               COALESCE(view_count, 0) as view_count,
               COALESCE(lesson_count, 0) as lesson_count,
               COALESCE(total_duration, 0) as total_duration
        FROM courses
        WHERE id = ? $status_condition
    ");
    $stmt->bind_param("i", $course_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $auth->jsonResponse(404, '课程不存在或已禁用');
    }

    $course = $result->fetch_assoc();

    // 格式化时长
    if ($course['duration']) {
        $course['duration_formatted'] = gmdate("H:i:s", $course['duration']);
    } else {
        $course['duration_formatted'] = null;
    }

    // 格式化课时信息
    $course['lesson_count'] = intval($course['lesson_count']);
    $course['total_duration'] = intval($course['total_duration']);
    if ($course['total_duration']) {
        $course['total_duration_formatted'] = gmdate("H:i:s", $course['total_duration']);
    } else {
        $course['total_duration_formatted'] = null;
    }

    // 初始化用户信息
    $has_access = false;
    $user_info = null;

    // 如果用户已登录，检查权限并获取用户相关信息
    if ($user_id > 0) {
        // 检查用户是否有课程权限
        $has_purchased_access = validateUserAccess($auth, $user_id, $course_id);

        if ($has_purchased_access) {
            // 获取用户课程信息
            $user_course_stmt = $conn->prepare("
                SELECT assigned_at, expires_at, last_watched_at,
                       watch_progress, watch_count, status
                FROM user_courses
                WHERE user_id = ? AND course_id = ?
            ");
            $user_course_stmt->bind_param("ii", $user_id, $course_id);
            $user_course_stmt->execute();
            $user_course_result = $user_course_stmt->get_result();

            if ($user_course_result->num_rows > 0) {
                $user_course = $user_course_result->fetch_assoc();
                $is_expired = $user_course['expires_at'] && strtotime($user_course['expires_at']) < time();

                $user_info = [
                    'assigned_at' => $user_course['assigned_at'],
                    'expires_at' => $user_course['expires_at'],
                    'last_watched_at' => $user_course['last_watched_at'],
                    'watch_progress' => floatval($user_course['watch_progress']),
                    'watch_count' => intval($user_course['watch_count']),
                    'status' => $user_course['status'],
                    'is_expired' => $is_expired,
                    'has_access' => !$is_expired && $user_course['status'] === 'active'
                ];

                // 如果课程已过期或状态不是active，则没有访问权限
                if (!$is_expired && $user_course['status'] === 'active') {
                    $has_access = true;
                }
            }
        }

        // 如果是免费课程且用户已登录，则有访问权限
        if ($course['is_free'] == 1) {
            $has_access = true;
            // 如果没有用户课程记录，为免费课程创建默认的用户信息
            if (!$user_info) {
                $user_info = [
                    'assigned_at' => null,
                    'expires_at' => null,
                    'last_watched_at' => null,
                    'watch_progress' => 0.0,
                    'watch_count' => 0,
                    'status' => 'active',
                    'is_expired' => false,
                    'has_access' => true
                ];
            }
        }
    }

    // 如果没有访问权限，移除视频链接
    if (!$has_access) {
        unset($course['video_url']);
    }

    // 添加用户信息到课程数据
    if ($user_info) {
        $course['user_info'] = $user_info;
    }

    $auth->jsonResponse(200, '获取成功', $course);
}

function updateWatchProgress($auth, $user) {
    $conn = $auth->getConn();
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        $auth->jsonResponse(400, '无效的请求数据');
    }

    $user_id = $user['id'];
    $course_id = isset($input['course_id']) ? (int)$input['course_id'] : 0;
    $watch_time = isset($input['watch_time']) ? (int)$input['watch_time'] : 0;
    $progress_position = isset($input['progress_position']) ? (int)$input['progress_position'] : 0;
    $watch_progress = isset($input['watch_progress']) ? (float)$input['watch_progress'] : 0;

    if ($course_id <= 0) {
        $auth->jsonResponse(400, '缺少课程ID');
    }

    // 验证用户权限
    if (!validateUserAccess($auth, $user_id, $course_id)) {
        $auth->jsonResponse(403, '您没有权限访问此课程');
    }
    
    // 限制进度范围
    if ($watch_progress < 0) $watch_progress = 0;
    if ($watch_progress > 100) $watch_progress = 100;
    
    try {
        $conn->begin_transaction();
        
        // 更新用户课程观看信息
        $update_stmt = $conn->prepare("
            UPDATE user_courses 
            SET last_watched_at = NOW(), 
                watch_progress = GREATEST(watch_progress, ?),
                watch_count = watch_count + 1
            WHERE user_id = ? AND course_id = ?
        ");
        $update_stmt->bind_param("dii", $watch_progress, $user_id, $course_id);
        $update_stmt->execute();
        
        // 记录观看日志
        $log_stmt = $conn->prepare("
            INSERT INTO course_watch_logs 
            (user_id, course_id, watch_time, progress_position, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $log_stmt->bind_param("iiiiss", $user_id, $course_id, $watch_time, 
                             $progress_position, $ip_address, $user_agent);
        $log_stmt->execute();
        
        $conn->commit();
        
        // 获取更新后的进度信息
        $progress_stmt = $conn->prepare("
            SELECT watch_progress, watch_count, last_watched_at 
            FROM user_courses 
            WHERE user_id = ? AND course_id = ?
        ");
        $progress_stmt->bind_param("ii", $user_id, $course_id);
        $progress_stmt->execute();
        $progress_result = $progress_stmt->get_result()->fetch_assoc();
        
        $auth->jsonResponse(200, '观看进度更新成功', [
            'watch_progress' => floatval($progress_result['watch_progress']),
            'watch_count' => intval($progress_result['watch_count']),
            'last_watched_at' => $progress_result['last_watched_at']
        ]);

    } catch (Exception $e) {
        $conn->rollback();
        $auth->jsonResponse(500, '更新失败：' . $e->getMessage());
    }
}
