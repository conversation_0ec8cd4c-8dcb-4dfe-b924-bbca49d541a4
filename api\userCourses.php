<?php
require_once '../includes/db.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

function sendResponse($code, $message, $data = null) {
    $response = [
        'code' => $code,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ];
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 验证用户是否存在
function validateUser($user_id) {
    global $conn;
    if (!$user_id || $user_id <= 0) {
        return false;
    }
    
    $stmt = $conn->prepare("SELECT id FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->num_rows > 0;
}

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getUserCourses();
        break;
    default:
        sendResponse(405, '不支持的请求方法');
}

function getUserCourses() {
    global $conn;
    
    $user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $status = isset($_GET['status']) ? $_GET['status'] : 'active';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $purchase_type = isset($_GET['purchase_type']) ? $_GET['purchase_type'] : '';
    
    if ($user_id <= 0) {
        sendResponse(400, '缺少用户ID');
    }
    
    if (!validateUser($user_id)) {
        sendResponse(400, '无效的用户ID');
    }
    
    // 限制每页数量
    if ($limit > 50) $limit = 50;
    if ($limit < 1) $limit = 10;
    if ($page < 1) $page = 1;
    
    $offset = ($page - 1) * $limit;
    
    // 构建查询条件
    $where_conditions = [
        "uc.user_id = ?"
        // 移除课程状态检查，因为用户已购买的课程即使下架也应该可以访问
    ];
    $params = [$user_id];
    $param_types = "i";
    
    // 状态筛选
    if ($status === 'active') {
        $where_conditions[] = "uc.status = 'active'";
        $where_conditions[] = "(uc.expires_at IS NULL OR uc.expires_at > NOW())";
    } elseif ($status === 'expired') {
        $where_conditions[] = "(uc.status = 'expired' OR (uc.status = 'active' AND uc.expires_at IS NOT NULL AND uc.expires_at <= NOW()))";
    } elseif ($status === 'revoked') {
        $where_conditions[] = "uc.status = 'revoked'";
    } elseif ($status === 'all') {
        // 不添加状态条件，显示所有
    } else {
        $where_conditions[] = "uc.status = ?";
        $params[] = $status;
        $param_types .= "s";
    }

    // 课程类型筛选
    if (!empty($purchase_type) && in_array($purchase_type, ['purchased', 'assigned'])) {
        $where_conditions[] = "uc.purchase_type = ?";
        $params[] = $purchase_type;
        $param_types .= "s";
    }

    // 搜索条件
    if (!empty($search)) {
        $where_conditions[] = "(c.title LIKE ? OR c.description LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $param_types .= "ss";
    }
    
    $where_clause = "WHERE " . implode(' AND ', $where_conditions);
    
    // 获取总数
    $count_sql = "SELECT COUNT(*) as total 
                  FROM user_courses uc 
                  LEFT JOIN courses c ON uc.course_id = c.id 
                  $where_clause";
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param($param_types, ...$params);
    $count_stmt->execute();
    $total = $count_stmt->get_result()->fetch_assoc()['total'];
    
    // 获取用户课程数据
    $sql = "SELECT c.id, c.title, c.description, c.video_url, c.thumbnail,
                   c.duration, c.sort_order, c.created_at,
                   uc.assigned_at, uc.expires_at, uc.last_watched_at,
                   uc.watch_progress, uc.watch_count, uc.status as user_course_status,
                   uc.purchase_type, uc.purchase_price
            FROM user_courses uc
            LEFT JOIN courses c ON uc.course_id = c.id
            $where_clause
            ORDER BY uc.assigned_at DESC
            LIMIT ? OFFSET ?";
    
    $stmt = $conn->prepare($sql);
    $params[] = $limit;
    $params[] = $offset;
    $param_types .= "ii";
    
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $courses = [];
    while ($row = $result->fetch_assoc()) {
        // 判断实际状态
        $actual_status = $row['user_course_status'];
        $is_expired = false;
        
        if ($actual_status === 'active' && $row['expires_at']) {
            if (strtotime($row['expires_at']) <= time()) {
                $actual_status = 'expired';
                $is_expired = true;
            }
        }
        
        // 格式化时长
        if ($row['duration']) {
            $row['duration_formatted'] = gmdate("H:i:s", $row['duration']);
        } else {
            $row['duration_formatted'] = null;
        }
        
        // 构建课程信息
        $course_data = [
            'id' => $row['id'],
            'title' => $row['title'],
            'description' => $row['description'],
            'thumbnail' => $row['thumbnail'],
            'duration' => $row['duration'],
            'duration_formatted' => $row['duration_formatted'],
            'sort_order' => $row['sort_order'],
            'created_at' => $row['created_at'],
            'user_info' => [
                'assigned_at' => $row['assigned_at'],
                'expires_at' => $row['expires_at'],
                'last_watched_at' => $row['last_watched_at'],
                'watch_progress' => floatval($row['watch_progress']),
                'watch_count' => intval($row['watch_count']),
                'status' => $actual_status,
                'is_expired' => $is_expired,
                'purchase_type' => $row['purchase_type'],
                'purchase_price' => $row['purchase_price'] ? floatval($row['purchase_price']) : null,
                'has_access' => ($actual_status === 'active' && !$is_expired)
            ]
        ];
        
        // 只有有权限的课程才包含视频链接
        if ($course_data['user_info']['has_access']) {
            $course_data['video_url'] = $row['video_url'];
        }
        
        $courses[] = $course_data;
    }
    
    // 获取用户统计信息
    $stats_sql = "SELECT
                    COUNT(*) as total_courses,
                    SUM(CASE WHEN uc.status = 'active' AND (uc.expires_at IS NULL OR uc.expires_at > NOW()) THEN 1 ELSE 0 END) as active_courses,
                    SUM(CASE WHEN uc.status = 'active' AND uc.expires_at IS NOT NULL AND uc.expires_at <= NOW() THEN 1 ELSE 0 END) as expired_courses,
                    SUM(CASE WHEN uc.status = 'revoked' THEN 1 ELSE 0 END) as revoked_courses,
                    AVG(CASE WHEN uc.status = 'active' AND (uc.expires_at IS NULL OR uc.expires_at > NOW()) THEN uc.watch_progress ELSE NULL END) as avg_progress,
                    SUM(uc.watch_count) as total_watch_count
                  FROM user_courses uc
                  LEFT JOIN courses c ON uc.course_id = c.id
                  WHERE uc.user_id = ?";

    $stats_stmt = $conn->prepare($stats_sql);
    $stats_stmt->bind_param("i", $user_id);
    $stats_stmt->execute();
    $stats = $stats_stmt->get_result()->fetch_assoc();

    // 获取学习时间统计（从lesson_watch_logs表）
    $learning_stats_sql = "SELECT
                            SUM(lwl.watch_time) as total_watch_time_seconds,
                            COUNT(DISTINCT DATE(lwl.created_at)) as study_days,
                            COUNT(DISTINCT lwl.lesson_id) as watched_lessons,
                            AVG(lwl.completion_rate) as avg_lesson_completion
                          FROM lesson_watch_logs lwl
                          INNER JOIN user_courses uc ON lwl.course_id = uc.course_id AND lwl.user_id = uc.user_id
                          WHERE lwl.user_id = ? AND uc.status = 'active'";

    $learning_stats_stmt = $conn->prepare($learning_stats_sql);
    $learning_stats_stmt->bind_param("i", $user_id);
    $learning_stats_stmt->execute();
    $learning_stats = $learning_stats_stmt->get_result()->fetch_assoc();

    // 计算学习速度（进度%/小时）
    $learning_speed = 0;
    $average_speed = 0;
    $total_watch_hours = 0;

    if ($learning_stats['total_watch_time_seconds'] > 0) {
        $total_watch_hours = $learning_stats['total_watch_time_seconds'] / 3600; // 转换为小时
        if ($total_watch_hours > 0 && $stats['avg_progress'] > 0) {
            $learning_speed = $stats['avg_progress'] / $total_watch_hours; // 进度%/小时
        }
        $average_speed = $learning_speed; // 当前用户的平均速度就是学习速度
    }
    
    $response_data = [
        'list' => $courses,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $total,
            'pages' => ceil($total / $limit)
        ],
        'statistics' => [
            'total_courses' => intval($stats['total_courses']),
            'active_courses' => intval($stats['active_courses']),
            'expired_courses' => intval($stats['expired_courses']),
            'revoked_courses' => intval($stats['revoked_courses']),
            'average_progress' => $stats['avg_progress'] ? round(floatval($stats['avg_progress']), 2) : 0,
            'total_watch_count' => intval($stats['total_watch_count']),
            // 新增的学习统计数据
            'learning_speed' => round($learning_speed, 2), // 学习速度（进度%/小时）
            'average_speed' => round($average_speed, 2), // 平均速度
            'total_watch_hours' => round($total_watch_hours, 2), // 总学习时长（小时）
            'study_days' => intval($learning_stats['study_days'] ?? 0), // 学习天数
            'watched_lessons' => intval($learning_stats['watched_lessons'] ?? 0), // 已观看课时数
            'avg_lesson_completion' => $learning_stats['avg_lesson_completion'] ? round(floatval($learning_stats['avg_lesson_completion']), 2) : 0 // 平均课时完成率
        ]
    ];
    
    sendResponse(200, '获取成功', $response_data);
}
?>
