# 故障排除指南

本文档提供了腾讯云VOD课时管理系统常见问题的解决方案。

## 🔍 问题诊断流程

遇到问题时，请按以下步骤进行诊断：

1. **确认错误信息** - 记录完整的错误信息
2. **检查系统日志** - 查看PHP错误日志和浏览器控制台
3. **验证配置** - 确认相关配置是否正确
4. **测试网络连接** - 检查与腾讯云的网络连接
5. **查看本指南** - 寻找对应的解决方案

## 🚨 常见问题及解决方案

### 1. 腾讯云VOD相关问题

#### 问题1.1：获取上传签名失败

**错误信息**:
```
获取签名失败: SECRET_ID未配置
获取签名失败: SECRET_KEY未配置
```

**可能原因**:
- 腾讯云API密钥未正确配置
- 密钥格式错误
- 文件权限问题

**解决方案**:
```php
// 1. 检查 includes/vod_config.php 配置
const SECRET_ID = 'AKID...';  // 确保不是默认值
const SECRET_KEY = '...';     // 确保不是默认值

// 2. 验证密钥格式
// SecretId 通常以 AKID 开头，长度约36位
// SecretKey 长度约32位，不包含特殊字符

// 3. 检查文件权限
chmod 644 includes/vod_config.php
```

#### 问题1.2：签名验证失败

**错误信息**:
```
腾讯云API错误: SignatureDoesNotMatch
腾讯云API错误: AuthFailure.SecretIdNotFound
```

**可能原因**:
- 密钥错误或已失效
- 服务器时间不准确
- 签名算法错误

**解决方案**:
```bash
# 1. 验证密钥有效性
# 登录腾讯云控制台检查密钥状态

# 2. 同步服务器时间
ntpdate -s time.nist.gov
# 或者
timedatectl set-ntp true

# 3. 检查时区设置
date
timedatectl status
```

#### 问题1.3：视频上传失败

**错误信息**:
```
上传失败: 文件格式不支持
上传失败: 文件大小超出限制
上传失败: 网络连接超时
```

**解决方案**:
```javascript
// 1. 检查文件格式
const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'];
if (!allowedTypes.includes(file.type)) {
    console.error('不支持的文件格式:', file.type);
}

// 2. 检查文件大小
const maxSize = 5 * 1024 * 1024 * 1024; // 5GB
if (file.size > maxSize) {
    console.error('文件大小超出限制:', file.size);
}

// 3. 增加重试机制
function uploadWithRetry(file, maxRetries = 3) {
    return new Promise((resolve, reject) => {
        let retries = 0;
        
        function attempt() {
            uploadFile(file)
                .then(resolve)
                .catch(error => {
                    if (retries < maxRetries) {
                        retries++;
                        console.log(`上传失败，重试第${retries}次...`);
                        setTimeout(attempt, 2000 * retries);
                    } else {
                        reject(error);
                    }
                });
        }
        
        attempt();
    });
}
```

### 2. 数据库相关问题

#### 问题2.1：数据库表结构错误

**错误信息**:
```
Unknown column 'vod_file_id' in 'field list'
Table 'lessons' doesn't exist
```

**解决方案**:
```sql
-- 1. 检查表是否存在
SHOW TABLES LIKE 'lessons';

-- 2. 检查字段是否存在
DESCRIBE lessons;

-- 3. 手动添加缺失字段
ALTER TABLE `lessons` ADD COLUMN `vod_file_id` varchar(100) DEFAULT NULL COMMENT '腾讯云点播文件ID' AFTER `video_url`;
ALTER TABLE `lessons` ADD COLUMN `vod_video_url` varchar(500) DEFAULT NULL COMMENT '腾讯云点播视频URL' AFTER `vod_file_id`;
ALTER TABLE `lessons` ADD COLUMN `video_type` enum('url','vod') DEFAULT 'url' COMMENT '视频类型' AFTER `vod_video_url`;

-- 4. 创建索引
CREATE INDEX idx_vod_file_id ON lessons(vod_file_id);
```

#### 问题2.2：数据库连接失败

**错误信息**:
```
Connection refused
Access denied for user
Too many connections
```

**解决方案**:
```php
// 1. 检查数据库配置
// includes/db.php
$servername = "localhost";
$username = "your_username";
$password = "your_password";
$dbname = "your_database";

// 2. 测试连接
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}

// 3. 检查数据库服务状态
// systemctl status mysql
// systemctl restart mysql
```

### 3. 文件上传问题

#### 问题3.1：PHP文件上传限制

**错误信息**:
```
文件大小超出服务器限制
上传超时
内存不足
```

**解决方案**:
```ini
; 修改 php.ini 配置
upload_max_filesize = 5G
post_max_size = 5G
max_execution_time = 3600
max_input_time = 3600
memory_limit = 512M

; 重启Web服务器
systemctl restart apache2
# 或
systemctl restart nginx
```

#### 问题3.2：目录权限问题

**错误信息**:
```
Permission denied
Failed to write file
Directory not writable
```

**解决方案**:
```bash
# 1. 设置目录权限
chmod 755 uploads/
chmod 755 uploads/lesson_thumbnails/
chmod 755 admin/uploads/

# 2. 设置所有者
chown -R www-data:www-data uploads/
# 或
chown -R apache:apache uploads/

# 3. 检查SELinux设置（如适用）
setsebool -P httpd_can_network_connect 1
setsebool -P httpd_unified 1
```

### 4. 小程序相关问题

#### 问题4.1：域名白名单配置

**错误信息**:
```
不在以下 request 合法域名列表中
不在以下 uploadFile 合法域名列表中
```

**解决方案**:
1. 登录微信小程序管理后台
2. 进入"开发" -> "开发设置"
3. 配置服务器域名：

```
request合法域名：
https://your-domain.com

uploadFile合法域名：
https://vod2.qcloud.com
https://your-domain.com

downloadFile合法域名：
https://your-domain.com
https://your-vod-domain.com
```

#### 问题4.2：视频播放失败

**错误信息**:
```
视频加载失败
网络错误
格式不支持
```

**解决方案**:
```javascript
// 1. 检查视频URL格式
function validateVideoUrl(url) {
    const validFormats = ['.mp4', '.m3u8', '.flv'];
    return validFormats.some(format => url.includes(format));
}

// 2. 添加错误处理
<video
    src="{{videoUrl}}"
    binderror="handleVideoError"
    bindloadedmetadata="handleVideoLoaded"
>
</video>

handleVideoError(e) {
    console.error('视频播放错误:', e.detail);
    // 尝试备用播放地址或提示用户
}

// 3. 检查网络状态
wx.getNetworkType({
    success(res) {
        if (res.networkType === 'none') {
            wx.showToast({
                title: '网络连接失败',
                icon: 'none'
            });
        }
    }
});
```

### 5. 性能相关问题

#### 问题5.1：页面加载缓慢

**可能原因**:
- 数据库查询效率低
- 图片/视频文件过大
- 网络带宽不足

**解决方案**:
```sql
-- 1. 优化数据库查询
-- 添加索引
CREATE INDEX idx_course_id ON lessons(course_id);
CREATE INDEX idx_sort_order ON lessons(sort_order);

-- 2. 分页查询
SELECT * FROM lessons 
WHERE course_id = ? 
ORDER BY sort_order ASC 
LIMIT ? OFFSET ?;
```

```javascript
// 3. 图片懒加载
<image 
    src="{{item.thumbnail}}" 
    lazy-load="{{true}}"
    mode="aspectFill"
>
</image>

// 4. 视频预加载控制
<video 
    src="{{videoUrl}}"
    preload="metadata"  // 只预加载元数据
>
</video>
```

#### 问题5.2：内存使用过高

**解决方案**:
```php
// 1. 优化PHP内存使用
ini_set('memory_limit', '512M');

// 2. 及时释放资源
$stmt->close();
$conn->close();

// 3. 使用流式处理大文件
function streamLargeFile($filename) {
    $handle = fopen($filename, 'rb');
    while (!feof($handle)) {
        echo fread($handle, 8192);
        flush();
    }
    fclose($handle);
}
```

## 🛠️ 调试工具和方法

### 1. 启用调试模式

在URL中添加 `debug=1` 参数：
```
http://your-domain.com/admin/lessons.php?course_id=1&debug=1
```

### 2. 查看日志文件

```bash
# PHP错误日志
tail -f /var/log/php_errors.log

# Apache错误日志
tail -f /var/log/apache2/error.log

# Nginx错误日志
tail -f /var/log/nginx/error.log

# MySQL错误日志
tail -f /var/log/mysql/error.log
```

### 3. 浏览器调试

```javascript
// 在浏览器控制台中调试
console.log('调试信息:', data);

// 网络请求监控
// 打开浏览器开发者工具 -> Network 面板
// 查看API请求和响应

// 性能分析
// 打开浏览器开发者工具 -> Performance 面板
// 录制页面加载过程
```

### 4. API测试工具

使用curl测试API接口：
```bash
# 测试签名生成
curl -X GET "https://your-domain.com/api/vod-signature.php?lesson_id=1"

# 测试视频状态查询
curl -X GET "https://your-domain.com/api/vod-status.php?file_id=123456"

# 测试课时列表
curl -X GET "https://your-domain.com/api/lessonList.php?course_id=1"
```

## 📞 获取技术支持

如果以上解决方案无法解决您的问题，请：

1. **收集错误信息**：
   - 完整的错误消息
   - 错误发生的具体步骤
   - 浏览器和操作系统信息
   - 相关的日志文件内容

2. **提供系统信息**：
   - PHP版本
   - MySQL版本
   - Web服务器类型和版本
   - 腾讯云VOD配置信息

3. **联系支持团队**：
   - 发送邮件至技术支持邮箱
   - 提供详细的问题描述和系统信息
   - 附上相关的截图或日志文件

---

**提示**: 在联系技术支持前，请先尝试本指南中的解决方案，这样可以更快地解决问题。
