<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

$success_message = '';
$error_message = '';

// 处理URL参数中的成功消息
if (isset($_GET['success'])) {
    $success_message = $_GET['success'];
}

// 处理URL参数中的错误消息
if (isset($_GET['error'])) {
    $error_message = $_GET['error'];
}

// 添加用户
if (isset($_POST['add_user'])) {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $phone = trim($_POST['phone']);
    $login_type = $_POST['login_type'] ?? 'traditional';
    $status = $_POST['status'] ?? 'active';

    if (empty($name) || empty($email)) {
        $error_message = '请填写姓名和邮箱';
    } elseif ($login_type === 'traditional' && (empty($username) || empty($password))) {
        $error_message = '传统登录方式需要填写用户名和密码';
    } elseif ($login_type === 'traditional' && strlen($password) < 6) {
        $error_message = '密码长度不能少于6位';
    } else {
        // 检查邮箱是否已存在
        $check_stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $check_stmt->bind_param("s", $email);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error_message = '该邮箱已被注册';
        } else {
            // 检查用户名是否已存在（如果提供了用户名）
            if (!empty($username)) {
                $check_username_stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
                $check_username_stmt->bind_param("s", $username);
                $check_username_stmt->execute();
                $check_username_result = $check_username_stmt->get_result();

                if ($check_username_result->num_rows > 0) {
                    $error_message = '该用户名已被使用';
                }
            }

            if (empty($error_message)) {
                $hashed_password = $login_type === 'traditional' ? password_hash($password, PASSWORD_DEFAULT) : null;
                $username = empty($username) ? null : $username;
                $phone = empty($phone) ? null : $phone;

                $stmt = $conn->prepare("INSERT INTO users (name, email, username, password, phone, login_type, status, nickname) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                if ($stmt) {
                    $stmt->bind_param("ssssssss", $name, $email, $username, $hashed_password, $phone, $login_type, $status, $name);

                    if ($stmt->execute()) {
                        header('Location: users.php?success=' . urlencode('用户添加成功'));
                        exit();
                    } else {
                        $error_message = '添加用户失败：' . $conn->error;
                    }
                    $stmt->close();
                } else {
                    $error_message = '数据库操作失败：' . $conn->error;
                }
            }
        }
    }
}

// 处理用户状态更新
if (isset($_POST['update_status'])) {
    $user_id = intval($_POST['user_id']);
    $status = $_POST['status'];

    if (in_array($status, ['active', 'inactive', 'banned'])) {
        $stmt = $conn->prepare("UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?");
        if ($stmt) {
            $stmt->bind_param("si", $status, $user_id);
            if ($stmt->execute()) {
                header('Location: users.php?success=' . urlencode('用户状态更新成功'));
                exit();
            } else {
                $error_message = '更新用户状态失败';
            }
            $stmt->close();
        } else {
            $error_message = '数据库操作失败';
        }
    }
}

// 处理密码重置
if (isset($_POST['reset_password'])) {
    $user_id = intval($_POST['user_id']);
    $new_password = $_POST['new_password'];

    if (strlen($new_password) >= 6) {
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
        if ($stmt) {
            $stmt->bind_param("si", $hashed_password, $user_id);
            if ($stmt->execute()) {
                $success_message = '密码重置成功';
            } else {
                $error_message = '密码重置失败';
            }
            $stmt->close();
        } else {
            $error_message = '数据库操作失败';
        }
    } else {
        $error_message = '密码长度不能少于6位';
    }
}

// 处理登录类型更新
if (isset($_POST['update_login_type'])) {
    $user_id = intval($_POST['user_id']);
    $login_type = $_POST['login_type'];

    if (in_array($login_type, ['traditional', 'wechat', 'both'])) {
        // 检查用户是否有对应的登录方式
        $user_stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
        $user_stmt->bind_param("i", $user_id);
        $user_stmt->execute();
        $user_result = $user_stmt->get_result()->fetch_assoc();

        $wechat_stmt = $conn->prepare("SELECT id FROM wechat_users WHERE user_id = ?");
        $wechat_stmt->bind_param("i", $user_id);
        $wechat_stmt->execute();
        $has_wechat = $wechat_stmt->get_result()->num_rows > 0;

        $has_password = !empty($user_result['password']);

        // 验证登录类型设置的合理性
        $can_update = true;
        $error_msg = '';

        if ($login_type === 'traditional' && !$has_password) {
            $can_update = false;
            $error_msg = '用户未设置密码，无法设置为仅密码登录';
        } elseif ($login_type === 'wechat' && !$has_wechat) {
            $can_update = false;
            $error_msg = '用户未绑定微信，无法设置为仅微信登录';
        } elseif ($login_type === 'both' && (!$has_password || !$has_wechat)) {
            $can_update = false;
            $error_msg = '用户需要同时设置密码和绑定微信才能使用两种登录方式';
        }

        if ($can_update) {
            $stmt = $conn->prepare("UPDATE users SET login_type = ?, updated_at = NOW() WHERE id = ?");
            if ($stmt) {
                $stmt->bind_param("si", $login_type, $user_id);
                if ($stmt->execute()) {
                    $success_message = '登录类型更新成功';
                } else {
                    $error_message = '更新登录类型失败';
                }
                $stmt->close();
            } else {
                $error_message = '数据库操作失败';
            }
        } else {
            $error_message = $error_msg;
        }
    }
}

// 处理手机号设置
if (isset($_POST['set_phone'])) {
    $user_id = intval($_POST['user_id']);
    $phone_number = trim($_POST['phone_number']);
    $phone_verified = isset($_POST['phone_verified']) ? 1 : 0;
    $phone_required = isset($_POST['phone_required']) ? 1 : 0;

    if (empty($phone_number)) {
        $error_message = '请输入手机号';
    } elseif (!preg_match('/^1[3-9]\d{9}$/', $phone_number)) {
        $error_message = '手机号格式不正确';
    } else {
        // 检查手机号是否已被其他用户使用
        $check_stmt = $conn->prepare("SELECT id, name FROM users WHERE phone = ? AND id != ? AND phone_verified = 1");
        $check_stmt->bind_param("si", $phone_number, $user_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $existing_user = $check_result->fetch_assoc();
            $error_message = "该手机号已被用户 {$existing_user['name']} 使用";
        } else {
            $phone_bind_time = $phone_verified ? 'NOW()' : 'NULL';
            $stmt = $conn->prepare("UPDATE users SET phone = ?, phone_verified = ?, phone_bind_required = ?, phone_bind_time = $phone_bind_time, updated_at = NOW() WHERE id = ?");
            if ($stmt) {
                $stmt->bind_param("siii", $phone_number, $phone_verified, $phone_required, $user_id);

                if ($stmt->execute()) {
                    $success_message = '手机号设置成功';
                } else {
                    $error_message = '设置手机号失败：' . $conn->error;
                }
                $stmt->close();
            } else {
                $error_message = '数据库操作失败：' . $conn->error;
            }
        }
    }
}

// 处理手机号清除
if (isset($_POST['clear_phone'])) {
    $user_id = intval($_POST['user_id']);
    $stmt = $conn->prepare("UPDATE users SET phone = NULL, phone_verified = 0, phone_bind_required = 0, phone_bind_time = NULL, updated_at = NOW() WHERE id = ?");

    if ($stmt) {
        $stmt->bind_param("i", $user_id);
        if ($stmt->execute()) {
            $success_message = '手机号清除成功';
        } else {
            $error_message = '清除手机号失败';
        }
        $stmt->close();
    } else {
        $error_message = '数据库操作失败';
    }
}

// 处理令牌撤销
if (isset($_POST['revoke_tokens'])) {
    $user_id = intval($_POST['user_id']);
    $stmt = $conn->prepare("UPDATE user_tokens SET is_revoked = 1 WHERE user_id = ?");
    if ($stmt) {
        $stmt->bind_param("i", $user_id);
        if ($stmt->execute()) {
            $success_message = '用户令牌已全部撤销';
        } else {
            $error_message = '撤销令牌失败';
        }
        $stmt->close();
    } else {
        $error_message = '数据库操作失败';
    }
}

// 处理用户编辑
if (isset($_POST['edit_user'])) {
    $user_id = intval($_POST['user_id']);
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $username = trim($_POST['username']);
    $phone = trim($_POST['phone']);
    $nickname = trim($_POST['nickname']);
    $gender = intval($_POST['gender']);

    if (empty($name) || empty($email)) {
        $error_message = '请填写姓名和邮箱';
    } else {
        // 检查邮箱是否被其他用户使用
        $check_stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $check_stmt->bind_param("si", $email, $user_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error_message = '该邮箱已被其他用户使用';
        } else {
            // 检查用户名是否被其他用户使用
            if (!empty($username)) {
                $check_username_stmt = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
                $check_username_stmt->bind_param("si", $username, $user_id);
                $check_username_stmt->execute();
                $check_username_result = $check_username_stmt->get_result();

                if ($check_username_result->num_rows > 0) {
                    $error_message = '该用户名已被其他用户使用';
                }
            }

            if (empty($error_message)) {
                $username = empty($username) ? null : $username;
                $phone = empty($phone) ? null : $phone;
                $nickname = empty($nickname) ? $name : $nickname;

                $stmt = $conn->prepare("UPDATE users SET name = ?, email = ?, username = ?, phone = ?, nickname = ?, gender = ?, updated_at = NOW() WHERE id = ?");
                if ($stmt) {
                    $stmt->bind_param("sssssii", $name, $email, $username, $phone, $nickname, $gender, $user_id);

                    if ($stmt->execute()) {
                        // 重定向到同一页面以避免重复提交，并显示成功消息
                        header('Location: users.php?success=' . urlencode('用户信息更新成功'));
                        exit();
                    } else {
                        $error_message = '更新用户信息失败：' . $conn->error;
                    }
                    $stmt->close();
                } else {
                    $error_message = '数据库操作失败：' . $conn->error;
                }
            }
        }
    }
}

// 批量操作处理
if (isset($_POST['batch_action']) && isset($_POST['selected_users'])) {
    $action = $_POST['batch_action'];
    $selected_users = $_POST['selected_users'];
    $success_count = 0;
    $error_count = 0;

    foreach ($selected_users as $user_id) {
        $user_id = intval($user_id);

        switch ($action) {
            case 'activate':
                $stmt = $conn->prepare("UPDATE users SET status = 'active', updated_at = NOW() WHERE id = ?");
                if ($stmt) {
                    $stmt->bind_param("i", $user_id);
                    if ($stmt->execute()) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                    $stmt->close();
                } else {
                    $error_count++;
                }
                break;

            case 'deactivate':
                $stmt = $conn->prepare("UPDATE users SET status = 'inactive', updated_at = NOW() WHERE id = ?");
                if ($stmt) {
                    $stmt->bind_param("i", $user_id);
                    if ($stmt->execute()) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                    $stmt->close();
                } else {
                    $error_count++;
                }
                break;

            case 'ban':
                $stmt = $conn->prepare("UPDATE users SET status = 'banned', updated_at = NOW() WHERE id = ?");
                if ($stmt) {
                    $stmt->bind_param("i", $user_id);
                    if ($stmt->execute()) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                    $stmt->close();
                } else {
                    $error_count++;
                }
                break;

            case 'revoke_tokens':
                $stmt = $conn->prepare("UPDATE user_tokens SET is_revoked = 1 WHERE user_id = ?");
                if ($stmt) {
                    $stmt->bind_param("i", $user_id);
                    if ($stmt->execute()) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                    $stmt->close();
                } else {
                    $error_count++;
                }
                break;

            case 'delete':
                // 开始事务
                $conn->begin_transaction();

                try {
                    // 删除相关数据
                    $stmt1 = $conn->prepare("DELETE FROM user_tokens WHERE user_id = ?");
                    if ($stmt1) {
                        $stmt1->bind_param("i", $user_id);
                        $stmt1->execute();
                        $stmt1->close();
                    }

                    $stmt2 = $conn->prepare("DELETE FROM user_login_logs WHERE user_id = ?");
                    if ($stmt2) {
                        $stmt2->bind_param("i", $user_id);
                        $stmt2->execute();
                        $stmt2->close();
                    }

                    $stmt3 = $conn->prepare("DELETE FROM wechat_users WHERE user_id = ?");
                    if ($stmt3) {
                        $stmt3->bind_param("i", $user_id);
                        $stmt3->execute();
                        $stmt3->close();
                    }

                    $stmt4 = $conn->prepare("DELETE FROM user_courses WHERE user_id = ?");
                    if ($stmt4) {
                        $stmt4->bind_param("i", $user_id);
                        $stmt4->execute();
                        $stmt4->close();
                    }

                    $stmt5 = $conn->prepare("DELETE FROM announcement_reads WHERE user_id = ?");
                    if ($stmt5) {
                        $stmt5->bind_param("i", $user_id);
                        $stmt5->execute();
                        $stmt5->close();
                    }

                    // 删除用户
                    $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
                    $stmt->bind_param("i", $user_id);
                    $stmt->execute();

                    $conn->commit();
                    $success_count++;
                } catch (Exception $e) {
                    $conn->rollback();
                    $error_count++;
                }
                break;
        }
    }

    if ($success_count > 0) {
        $action_names = [
            'activate' => '激活',
            'deactivate' => '停用',
            'ban' => '禁用',
            'revoke_tokens' => '撤销令牌',
            'delete' => '删除'
        ];
        $success_message = "批量{$action_names[$action]}操作完成：成功 {$success_count} 个";
        if ($error_count > 0) {
            $success_message .= "，失败 {$error_count} 个";
        }
    } else {
        $error_message = '批量操作失败';
    }
}

// 删除用户
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);

    // 开始事务
    $conn->begin_transaction();

    try {
        // 删除相关数据
        $stmt1 = $conn->prepare("DELETE FROM user_tokens WHERE user_id = ?");
        if ($stmt1) {
            $stmt1->bind_param("i", $id);
            $stmt1->execute();
            $stmt1->close();
        }

        $stmt2 = $conn->prepare("DELETE FROM user_login_logs WHERE user_id = ?");
        if ($stmt2) {
            $stmt2->bind_param("i", $id);
            $stmt2->execute();
            $stmt2->close();
        }

        $stmt3 = $conn->prepare("DELETE FROM wechat_users WHERE user_id = ?");
        if ($stmt3) {
            $stmt3->bind_param("i", $id);
            $stmt3->execute();
            $stmt3->close();
        }

        $stmt4 = $conn->prepare("DELETE FROM user_courses WHERE user_id = ?");
        if ($stmt4) {
            $stmt4->bind_param("i", $id);
            $stmt4->execute();
            $stmt4->close();
        }

        $stmt5 = $conn->prepare("DELETE FROM announcement_reads WHERE user_id = ?");
        if ($stmt5) {
            $stmt5->bind_param("i", $id);
            $stmt5->execute();
            $stmt5->close();
        }

        // 删除用户
        $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
        if ($stmt) {
            $stmt->bind_param("i", $id);
            $stmt->execute();
            $stmt->close();
        } else {
            throw new Exception('删除用户语句准备失败');
        }

        $conn->commit();
        $success_message = '用户删除成功';
    } catch (Exception $e) {
        $conn->rollback();
        $error_message = '删除用户失败：' . $e->getMessage();
    }
}

// 获取用户列表（带分页和筛选）
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$per_page = 15;
$offset = ($page - 1) * $per_page;

$status_filter = $_GET['status'] ?? '';
$login_type_filter = $_GET['login_type'] ?? '';
$search = trim($_GET['search'] ?? '');

// 构建查询条件
$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($status_filter)) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

if (!empty($login_type_filter)) {
    $where_conditions[] = "login_type = ?";
    $params[] = $login_type_filter;
    $param_types .= 's';
}

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ? OR username LIKE ? OR nickname LIKE ?)";
    $search_param = "%{$search}%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
    $param_types .= 'ssss';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// 获取总数
$count_sql = "SELECT COUNT(*) as total FROM users {$where_clause}";
if (!empty($params)) {
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param($param_types, ...$params);
    $count_stmt->execute();
    $total_users = $count_stmt->get_result()->fetch_assoc()['total'];
} else {
    $total_users = $conn->query($count_sql)->fetch_assoc()['total'];
}

$total_pages = ceil($total_users / $per_page);

// 获取用户列表
$sql = "SELECT id, name, email, username, nickname, avatar, phone, gender, status, login_type, last_login_at, login_count, created_at FROM users {$where_clause} ORDER BY created_at DESC LIMIT {$per_page} OFFSET {$offset}";

if (!empty($params)) {
    $stmt = $conn->prepare($sql);
    if ($stmt) {
        $stmt->bind_param($param_types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
    } else {
        die('数据库查询失败：' . $conn->error);
    }
} else {
    $result = $conn->query($sql);
}

// 渲染页面头部
render_admin_header('用户管理', 'users');
?>

<!-- 显示消息 -->
<?php if ($success_message): ?>
    <?php show_success_message($success_message); ?>
<?php endif; ?>

<?php if ($error_message): ?>
    <?php show_error_message($error_message); ?>
<?php endif; ?>

<!-- 用户统计卡片 -->
<?php
$stats_sql = "SELECT
    COUNT(*) as total_users,
    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
    SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_users,
    SUM(CASE WHEN status = 'banned' THEN 1 ELSE 0 END) as banned_users,
    SUM(CASE WHEN login_type = 'traditional' THEN 1 ELSE 0 END) as traditional_users,
    SUM(CASE WHEN login_type = 'wechat' THEN 1 ELSE 0 END) as wechat_users,
    SUM(CASE WHEN login_type = 'both' THEN 1 ELSE 0 END) as both_users,
    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_registered,
    SUM(CASE WHEN DATE(last_login_at) = CURDATE() THEN 1 ELSE 0 END) as today_active
FROM users";
$stats_result = $conn->query($stats_sql);
$stats = $stats_result->fetch_assoc();
?>

<div class="admin-stats-grid">
    <div class="admin-stat-card">
        <div class="admin-stat-icon">
            <i class="fas fa-users"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($stats['total_users']); ?></div>
            <div class="admin-stat-label">总用户数</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-success">
            <i class="fas fa-user-check"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($stats['active_users']); ?></div>
            <div class="admin-stat-label">活跃用户</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-warning">
            <i class="fas fa-user-clock"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($stats['inactive_users']); ?></div>
            <div class="admin-stat-label">非活跃用户</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-danger">
            <i class="fas fa-user-slash"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($stats['banned_users']); ?></div>
            <div class="admin-stat-label">已禁用用户</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-info">
            <i class="fas fa-user-plus"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($stats['today_registered']); ?></div>
            <div class="admin-stat-label">今日注册</div>
        </div>
    </div>

    <div class="admin-stat-card">
        <div class="admin-stat-icon admin-stat-primary">
            <i class="fas fa-user-friends"></i>
        </div>
        <div class="admin-stat-content">
            <div class="admin-stat-number"><?php echo number_format($stats['today_active']); ?></div>
            <div class="admin-stat-label">今日活跃</div>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<?php render_card_start('筛选和搜索'); ?>
    <form method="get" class="admin-form">
        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label">状态筛选</label>
                    <select class="admin-form-input" name="status">
                        <option value="">全部状态</option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>活跃</option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>非活跃</option>
                        <option value="banned" <?php echo $status_filter === 'banned' ? 'selected' : ''; ?>>已禁用</option>
                    </select>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label">登录类型</label>
                    <select class="admin-form-input" name="login_type">
                        <option value="">全部类型</option>
                        <option value="traditional" <?php echo $login_type_filter === 'traditional' ? 'selected' : ''; ?>>传统登录</option>
                        <option value="wechat" <?php echo $login_type_filter === 'wechat' ? 'selected' : ''; ?>>微信登录</option>
                        <option value="both" <?php echo $login_type_filter === 'both' ? 'selected' : ''; ?>>两种方式</option>
                    </select>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label">搜索用户</label>
                    <input class="admin-form-input" type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="姓名、邮箱、用户名、昵称">
                </div>
            </div>
        </div>
        <div class="admin-actions">
            <?php render_button('搜索', 'submit', 'admin-btn-primary'); ?>
            <?php render_link_button('重置', 'users.php', 'admin-btn-secondary'); ?>
            <button type="button" class="admin-btn admin-btn-success" onclick="showAddUserModal()">
                <i class="fas fa-plus"></i> 添加用户
            </button>
        </div>
    </form>
<?php render_card_end(); ?>

<!-- 用户列表 -->
<?php render_card_start('用户列表 (共 ' . $total_users . ' 个用户)'); ?>
    <?php if ($result->num_rows > 0): ?>
        <div class="table-responsive">
            <?php render_table_start(['头像', '用户信息', '联系方式', '状态', '登录信息', '操作']); ?>
                <?php while ($row = $result->fetch_assoc()): ?>
                <tr>
                    <td style="width: 60px;">
                        <?php if ($row['avatar']): ?>
                            <img src="<?php echo htmlspecialchars($row['avatar']); ?>" alt="头像" style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;">
                        <?php else: ?>
                            <div style="width: 40px; height: 40px; border-radius: 50%; background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #999;">
                                <i class="fas fa-user"></i>
                            </div>
                        <?php endif; ?>
                    </td>
                    <td>
                        <div><strong><?php echo htmlspecialchars($row['name']); ?></strong></div>
                        <div style="font-size: 12px; color: #666;">
                            ID: <?php echo $row['id']; ?>
                            <?php if ($row['username']): ?>
                                | 用户名: <?php echo htmlspecialchars($row['username']); ?>
                            <?php endif; ?>
                        </div>
                        <?php if ($row['nickname'] && $row['nickname'] !== $row['name']): ?>
                            <div style="font-size: 12px; color: #888;">昵称: <?php echo htmlspecialchars($row['nickname']); ?></div>
                        <?php endif; ?>
                    </td>
                    <td>
                        <div><?php echo htmlspecialchars($row['email']); ?></div>
                        <?php if ($row['phone']): ?>
                            <div style="font-size: 12px; color: #666;"><?php echo htmlspecialchars($row['phone']); ?></div>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php
                        $status_colors = [
                            'active' => 'success',
                            'inactive' => 'warning',
                            'banned' => 'danger'
                        ];
                        $status_texts = [
                            'active' => '活跃',
                            'inactive' => '非活跃',
                            'banned' => '已禁用'
                        ];
                        ?>
                        <span class="admin-badge admin-badge-<?php echo $status_colors[$row['status']]; ?>">
                            <?php echo $status_texts[$row['status']]; ?>
                        </span>
                        <br>
                        <span class="admin-badge admin-badge-info" style="margin-top: 5px;">
                            <?php
                            $login_type_texts = [
                                'traditional' => '传统',
                                'wechat' => '微信',
                                'both' => '两种'
                            ];
                            echo $login_type_texts[$row['login_type']];
                            ?>
                        </span>
                    </td>
                    <td>
                        <div style="font-size: 12px;">
                            登录次数: <?php echo $row['login_count']; ?>
                        </div>
                        <?php if ($row['last_login_at']): ?>
                            <div style="font-size: 12px; color: #666;">
                                最后登录: <?php echo date('m-d H:i', strtotime($row['last_login_at'])); ?>
                            </div>
                        <?php endif; ?>
                        <div style="font-size: 12px; color: #888;">
                            注册: <?php echo date('Y-m-d', strtotime($row['created_at'])); ?>
                        </div>
                    </td>
                    <td>
                        <div class="admin-actions">
                            <button type="button" class="admin-btn admin-btn-primary admin-btn-sm" onclick="showUserModal(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                管理
                            </button>
                            <button type="button" class="admin-btn admin-btn-info admin-btn-sm" onclick="showEditUserModal(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                编辑
                            </button>
                            <a href="users.php?delete=<?php echo $row['id']; ?>" class="admin-btn admin-btn-danger admin-btn-sm" onclick="return confirmDelete('确定要删除用户 <?php echo htmlspecialchars($row['name']); ?> 吗？这将删除所有相关数据！')">
                                删除
                            </a>
                        </div>
                    </td>
                </tr>
                <?php endwhile; ?>
            <?php render_table_end(); ?>
        </div>

        <!-- 分页 -->
        <?php if ($total_pages > 1): ?>
        <div style="margin-top: 20px; text-align: center;">
            <?php
            $query_params = http_build_query(array_filter([
                'status' => $status_filter,
                'login_type' => $login_type_filter,
                'search' => $search
            ]));
            $base_url = 'users.php' . ($query_params ? '?' . $query_params . '&' : '?');
            ?>

            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <?php if ($i == $page): ?>
                    <span class="admin-btn admin-btn-primary admin-btn-sm" style="margin: 0 2px;"><?php echo $i; ?></span>
                <?php else: ?>
                    <?php render_link_button($i, $base_url . 'page=' . $i, 'admin-btn-secondary admin-btn-sm', '', 'margin: 0 2px;'); ?>
                <?php endif; ?>
            <?php endfor; ?>
        </div>
        <?php endif; ?>
    <?php else: ?>
        <div style="text-align: center; padding: 40px; color: #666;">
            <i class="fas fa-users" style="font-size: 48px; margin-bottom: 15px; opacity: 0.3;"></i>
            <p>暂无用户数据</p>
        </div>
    <?php endif; ?>
<?php render_card_end(); ?>

<!-- 添加用户模态框 -->
<div id="addUserModal" class="admin-modal" style="display: none;">
    <div class="admin-modal-content">
        <div class="admin-modal-header">
            <h3>添加新用户</h3>
            <span class="admin-modal-close" onclick="closeAddUserModal()">&times;</span>
        </div>
        <div class="admin-modal-body">
            <form method="post" id="addUserForm">
                <div class="admin-form-row">
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">姓名 *</label>
                            <input type="text" class="admin-form-input" name="name" required>
                        </div>
                    </div>
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">邮箱 *</label>
                            <input type="email" class="admin-form-input" name="email" required>
                        </div>
                    </div>
                </div>

                <div class="admin-form-row">
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">用户名</label>
                            <input type="text" class="admin-form-input" name="username">
                        </div>
                    </div>
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">手机号</label>
                            <input type="tel" class="admin-form-input" name="phone">
                        </div>
                    </div>
                </div>

                <div class="admin-form-row">
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">登录类型</label>
                            <select class="admin-form-input" name="login_type" id="loginType" onchange="togglePasswordField()">
                                <option value="traditional">传统登录</option>
                                <option value="wechat">微信登录</option>
                                <option value="both">两种方式</option>
                            </select>
                        </div>
                    </div>
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">状态</label>
                            <select class="admin-form-input" name="status">
                                <option value="active">活跃</option>
                                <option value="inactive">非活跃</option>
                                <option value="banned">已禁用</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="admin-form-group" id="passwordField">
                    <label class="admin-form-label">密码 *</label>
                    <input type="password" class="admin-form-input" name="password" minlength="6">
                    <small class="admin-form-help">密码长度至少6位</small>
                </div>

                <div class="admin-actions">
                    <button type="submit" name="add_user" class="admin-btn admin-btn-primary">添加用户</button>
                    <button type="button" class="admin-btn admin-btn-secondary" onclick="closeAddUserModal()">取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 用户管理模态框 -->
<div id="userModal" class="admin-modal" style="display: none;">
    <div class="admin-modal-content">
        <div class="admin-modal-header">
            <h3>用户管理</h3>
            <span class="admin-modal-close" onclick="closeUserModal()">&times;</span>
        </div>
        <div class="admin-modal-body">
            <div id="userInfo"></div>

            <!-- 状态管理 -->
            <div class="admin-form-group">
                <label class="admin-form-label">用户状态</label>
                <form method="post" style="display: inline;">
                    <input type="hidden" name="user_id" id="modalUserId">
                    <select class="admin-form-input" name="status" id="modalStatus" onchange="this.form.submit()">
                        <option value="active">活跃</option>
                        <option value="inactive">非活跃</option>
                        <option value="banned">已禁用</option>
                    </select>
                    <input type="hidden" name="update_status" value="1">
                </form>
            </div>

            <!-- 密码重置 -->
            <div class="admin-form-group">
                <label class="admin-form-label">重置密码</label>
                <form method="post" onsubmit="return confirm('确定要重置该用户的密码吗？')">
                    <input type="hidden" name="user_id" id="modalUserIdPassword">
                    <div style="display: flex; gap: 10px;">
                        <input type="password" class="admin-form-input" name="new_password" placeholder="新密码（至少6位）" required minlength="6" style="flex: 1;">
                        <button type="submit" name="reset_password" class="admin-btn admin-btn-warning admin-btn-sm">重置</button>
                    </div>
                </form>
            </div>

            <!-- 登录类型管理 -->
            <div class="admin-form-group">
                <label class="admin-form-label">登录类型设置</label>
                <form method="post" style="display: inline;">
                    <input type="hidden" name="user_id" id="modalUserIdLoginType">
                    <select class="admin-form-input" name="login_type" id="modalLoginType" onchange="this.form.submit()">
                        <option value="traditional">仅密码登录</option>
                        <option value="wechat">仅微信登录</option>
                        <option value="both">两种方式</option>
                    </select>
                    <input type="hidden" name="update_login_type" value="1">
                </form>
                <small class="admin-form-help">修改用户可用的登录方式</small>
            </div>

            <!-- 手机号管理 -->
            <div class="admin-form-group">
                <label class="admin-form-label">手机号管理</label>
                <form method="post" onsubmit="return confirm('确定要设置该用户的手机号吗？')">
                    <input type="hidden" name="user_id" id="modalUserIdPhone">
                    <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                        <input type="tel" class="admin-form-input" name="phone_number" id="modalPhoneNumber" placeholder="请输入手机号" pattern="^1[3-9]\d{9}$" style="flex: 1;">
                        <button type="submit" name="set_phone" class="admin-btn admin-btn-success admin-btn-sm">设置</button>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <label style="font-size: 12px;">
                            <input type="checkbox" name="phone_verified" value="1" id="modalPhoneVerified" checked>
                            标记为已验证
                        </label>
                        <label style="font-size: 12px;">
                            <input type="checkbox" name="phone_required" value="1" id="modalPhoneRequired">
                            设为必须绑定
                        </label>
                    </div>
                </form>
                <div style="margin-top: 10px;">
                    <form method="post" onsubmit="return confirm('确定要清除该用户的手机号吗？')" style="display: inline;">
                        <input type="hidden" name="user_id" id="modalUserIdPhoneClear">
                        <button type="submit" name="clear_phone" class="admin-btn admin-btn-warning admin-btn-sm">
                            <i class="fas fa-times"></i> 清除手机号
                        </button>
                    </form>
                </div>
            </div>

            <!-- 令牌管理 -->
            <div class="admin-form-group">
                <label class="admin-form-label">令牌管理</label>
                <form method="post" onsubmit="return confirm('确定要撤销该用户的所有登录令牌吗？用户需要重新登录。')">
                    <input type="hidden" name="user_id" id="modalUserIdToken">
                    <button type="submit" name="revoke_tokens" class="admin-btn admin-btn-danger admin-btn-sm">
                        <i class="fas fa-ban"></i> 撤销所有令牌
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div id="editUserModal" class="admin-modal" style="display: none;">
    <div class="admin-modal-content">
        <div class="admin-modal-header">
            <h3>编辑用户信息</h3>
            <span class="admin-modal-close" onclick="closeEditUserModal()">&times;</span>
        </div>
        <div class="admin-modal-body">
            <form method="post" id="editUserForm">
                <input type="hidden" name="user_id" id="editUserId">

                <div class="admin-form-row">
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">姓名 *</label>
                            <input type="text" class="admin-form-input" name="name" id="editUserName" required>
                        </div>
                    </div>
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">邮箱 *</label>
                            <input type="email" class="admin-form-input" name="email" id="editUserEmail" required>
                        </div>
                    </div>
                </div>

                <div class="admin-form-row">
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">用户名</label>
                            <input type="text" class="admin-form-input" name="username" id="editUserUsername">
                        </div>
                    </div>
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">昵称</label>
                            <input type="text" class="admin-form-input" name="nickname" id="editUserNickname">
                        </div>
                    </div>
                </div>

                <div class="admin-form-row">
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">手机号</label>
                            <input type="tel" class="admin-form-input" name="phone" id="editUserPhone">
                        </div>
                    </div>
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">性别</label>
                            <select class="admin-form-input" name="gender" id="editUserGender">
                                <option value="0">未知</option>
                                <option value="1">男</option>
                                <option value="2">女</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="admin-actions">
                    <button type="submit" name="edit_user" class="admin-btn admin-btn-primary">保存修改</button>
                    <button type="button" class="admin-btn admin-btn-secondary" onclick="closeEditUserModal()">取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// 显示添加用户模态框
function showAddUserModal() {
    document.getElementById('addUserModal').style.display = 'block';
}

// 关闭添加用户模态框
function closeAddUserModal() {
    document.getElementById('addUserModal').style.display = 'none';
    document.getElementById('addUserForm').reset();
}

// 显示用户管理模态框
function showUserModal(user) {
    const modal = document.getElementById('userModal');
    const userInfo = document.getElementById('userInfo');

    // 填充用户信息
    userInfo.innerHTML = `
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0;">${user.name}</h4>
            <p style="margin: 5px 0;"><strong>邮箱:</strong> ${user.email}</p>
            ${user.username ? `<p style="margin: 5px 0;"><strong>用户名:</strong> ${user.username}</p>` : ''}
            ${user.phone ? `<p style="margin: 5px 0;"><strong>手机:</strong> ${user.phone}</p>` : ''}
            <p style="margin: 5px 0;"><strong>登录类型:</strong> ${user.login_type === 'traditional' ? '传统登录' : user.login_type === 'wechat' ? '微信登录' : '两种方式'}</p>
            <p style="margin: 5px 0;"><strong>登录次数:</strong> ${user.login_count}</p>
            <p style="margin: 5px 0;"><strong>注册时间:</strong> ${user.created_at}</p>
        </div>
    `;

    // 设置表单值
    document.getElementById('modalUserId').value = user.id;
    document.getElementById('modalUserIdPassword').value = user.id;
    document.getElementById('modalUserIdToken').value = user.id;
    document.getElementById('modalUserIdLoginType').value = user.id;
    document.getElementById('modalUserIdPhone').value = user.id;
    document.getElementById('modalUserIdPhoneClear').value = user.id;
    document.getElementById('modalStatus').value = user.status;
    document.getElementById('modalLoginType').value = user.login_type;

    // 设置手机号相关字段
    document.getElementById('modalPhoneNumber').value = user.phone || '';
    document.getElementById('modalPhoneVerified').checked = user.phone_verified == 1;
    document.getElementById('modalPhoneRequired').checked = user.phone_bind_required == 1;

    modal.style.display = 'block';
}

// 关闭用户管理模态框
function closeUserModal() {
    document.getElementById('userModal').style.display = 'none';
}

// 显示编辑用户模态框
function showEditUserModal(user) {
    const modal = document.getElementById('editUserModal');

    // 填充表单数据
    document.getElementById('editUserId').value = user.id;
    document.getElementById('editUserName').value = user.name;
    document.getElementById('editUserEmail').value = user.email;
    document.getElementById('editUserUsername').value = user.username || '';
    document.getElementById('editUserNickname').value = user.nickname || '';
    document.getElementById('editUserPhone').value = user.phone || '';
    document.getElementById('editUserGender').value = user.gender || '0';

    modal.style.display = 'block';
}

// 关闭编辑用户模态框
function closeEditUserModal() {
    document.getElementById('editUserModal').style.display = 'none';
}

// 切换密码字段显示
function togglePasswordField() {
    const loginType = document.getElementById('loginType').value;
    const passwordField = document.getElementById('passwordField');
    const passwordInput = passwordField.querySelector('input[name="password"]');

    if (loginType === 'wechat') {
        passwordField.style.display = 'none';
        passwordInput.removeAttribute('required');
    } else {
        passwordField.style.display = 'block';
        passwordInput.setAttribute('required', 'required');
    }
}

// 确认删除
function confirmDelete(message) {
    return confirm(message);
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const addModal = document.getElementById('addUserModal');
    const userModal = document.getElementById('userModal');
    const editModal = document.getElementById('editUserModal');

    // 只有当点击的是模态框背景时才关闭，避免干扰表单提交
    if (event.target === addModal) {
        closeAddUserModal();
    } else if (event.target === userModal) {
        closeUserModal();
    } else if (event.target === editModal) {
        closeEditUserModal();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    togglePasswordField();

    // 确保编辑用户表单能正常提交
    const editUserForm = document.getElementById('editUserForm');
    if (editUserForm) {
        editUserForm.addEventListener('submit', function(e) {
            // 确保表单数据完整
            const userId = document.getElementById('editUserId').value;
            const userName = document.getElementById('editUserName').value;
            const userEmail = document.getElementById('editUserEmail').value;

            if (!userId || !userName.trim() || !userEmail.trim()) {
                e.preventDefault();
                alert('请填写完整的用户信息');
                return false;
            }

            // 显示提交中状态
            const submitBtn = editUserForm.querySelector('button[name="edit_user"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = '保存中...';
            }

            // 允许表单正常提交
            return true;
        });
    }
});
</script>

<style>
/* 统计卡片样式 */
.admin-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.admin-stat-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #fff;
    background: #6c757d;
}

.admin-stat-icon.admin-stat-success {
    background: #28a745;
}

.admin-stat-icon.admin-stat-warning {
    background: #ffc107;
}

.admin-stat-icon.admin-stat-danger {
    background: #dc3545;
}

.admin-stat-icon.admin-stat-info {
    background: #17a2b8;
}

.admin-stat-icon.admin-stat-primary {
    background: #007bff;
}

.admin-stat-content {
    flex: 1;
}

.admin-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.admin-stat-label {
    font-size: 14px;
    color: #6c757d;
}

/* 模态框样式 */
.admin-modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.admin-modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.admin-modal-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.admin-modal-close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.admin-modal-close:hover {
    color: #000;
}

.admin-modal-body {
    padding: 20px;
}

/* 徽章样式 */
.admin-badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 500;
    border-radius: 4px;
    text-transform: uppercase;
}

.admin-badge-success {
    background-color: #d4edda;
    color: #155724;
}

.admin-badge-warning {
    background-color: #fff3cd;
    color: #856404;
}

.admin-badge-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.admin-badge-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* 表格响应式 */
.table-responsive {
    overflow-x: auto;
}

/* 表单帮助文本 */
.admin-form-help {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-stats-grid {
        grid-template-columns: 1fr;
    }

    .admin-modal-content {
        width: 95%;
        margin: 2% auto;
    }

    .admin-form-row {
        flex-direction: column;
    }

    .admin-form-col {
        width: 100%;
        margin-bottom: 15px;
    }
}
</style>

<?php render_admin_footer(); ?>
