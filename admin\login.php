<?php
session_start();
require_once '../includes/db.php';

$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];

    if (empty($username) || empty($password)) {
        $error_message = '请填写用户名和密码';
    } else {
        $sql = "SELECT id, username, password FROM admins WHERE username = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();
            if (password_verify($password, $user['password'])) {
                $_SESSION['loggedin'] = true;
                $_SESSION['username'] = $username;
                $_SESSION['admin_id'] = $user['id'];
                header("Location: index.php");
                exit;
            } else {
                $error_message = "密码错误";
            }
        } else {
            $error_message = "用户不存在";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .login-header {
            margin-bottom: 30px;
        }

        .login-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 10px 0;
        }

        .login-subtitle {
            color: #666;
            font-size: 14px;
        }

        .login-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }

        .login-form {
            text-align: left;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            border: 1px solid #f5c6cb;
        }

        .login-footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-icon">
                <i class="fas fa-user-shield" style="font-size: 36px; color: white;"></i>
            </div>
            <h1 class="login-title">管理员登录</h1>
            <p class="login-subtitle">请输入您的登录凭据</p>
        </div>

        <?php if ($error_message): ?>
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <form class="login-form" method="post" id="loginForm">
            <div class="admin-form-group">
                <label class="admin-form-label" for="username">
                    <i class="fas fa-user"></i> 用户名
                </label>
                <input class="admin-form-input" type="text" name="username" id="username"
                       placeholder="请输入用户名" required
                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>">
            </div>

            <div class="admin-form-group">
                <label class="admin-form-label" for="password">
                    <i class="fas fa-lock"></i> 密码
                </label>
                <input class="admin-form-input" type="password" name="password" id="password"
                       placeholder="请输入密码" required>
            </div>

            <button type="submit" class="admin-btn admin-btn-primary" style="width: 100%; margin-top: 10px;">
                <i class="fas fa-sign-in-alt"></i> 登录
            </button>
        </form>

        <div class="login-footer">
            <p>© 2024 管理系统. 保留所有权利.</p>
        </div>
    </div>

    <script>
        // 表单验证
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            if (!username || !password) {
                e.preventDefault();
                alert('请填写用户名和密码');
                return false;
            }
        });

        // 自动聚焦到用户名输入框
        document.getElementById('username').focus();
    </script>
</body>
</html>
