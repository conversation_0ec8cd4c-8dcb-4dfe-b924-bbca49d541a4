<template>
	<view class="change-password-container">
		<view class="form-container">
			<view class="form-header">
				<view class="header-icon">
					<simple-icon type="locked" color="#007bff" size="32"></simple-icon>
				</view>
				<view class="header-title">
					{{ checkingPassword ? '密码设置' : (hasPassword ? '修改密码' : '设置密码') }}
				</view>
				<view class="header-subtitle">
					{{ checkingPassword ? '正在检查密码状态...' : (hasPassword ? '为了您的账户安全，请定期修改密码' : '请设置您的登录密码以保护账户安全') }}
				</view>
			</view>

			<view class="form-content">
				<!-- 旧密码（仅在已设置密码时显示） -->
				<view class="form-group" v-if="hasPassword && !checkingPassword">
					<view class="form-label">当前密码</view>
					<view class="password-input-wrapper">
						<input
							v-model="formData.oldPassword"
							class="form-input"
							:type="showOldPassword ? 'text' : 'password'"
							placeholder="请输入当前密码"
							maxlength="50"
						/>
						<view class="password-toggle" @click="toggleOldPassword">
							<simple-icon
								:type="showOldPassword ? 'eye-off' : 'eye'"
								color="#999"
								size="20"
							></simple-icon>
						</view>
					</view>
				</view>

				<!-- 首次设置密码提示 -->
				<view class="first-time-tip" v-if="!hasPassword && !checkingPassword">
					<view class="tip-icon">🔐</view>
					<view class="tip-text">您还未设置密码，请设置您的登录密码</view>
				</view>

				<!-- 加载状态 -->
				<view class="loading-tip" v-if="checkingPassword">
					<view class="loading-icon">⏳</view>
					<view class="loading-text">正在检查密码状态...</view>
				</view>

				<!-- 新密码 -->
				<view class="form-group">
					<view class="form-label">新密码</view>
					<view class="password-input-wrapper">
						<input 
							v-model="formData.newPassword" 
							class="form-input" 
							:type="showNewPassword ? 'text' : 'password'"
							placeholder="请输入新密码（6-50位）"
							maxlength="50"
						/>
						<view class="password-toggle" @click="toggleNewPassword">
							<simple-icon 
								:type="showNewPassword ? 'eye-off' : 'eye'" 
								color="#999" 
								size="20"
							></simple-icon>
						</view>
					</view>
					<view class="password-strength">
						<view class="strength-label">密码强度：</view>
						<view class="strength-indicator">
							<view 
								class="strength-bar" 
								:class="passwordStrength.level"
							></view>
						</view>
						<view class="strength-text" :class="passwordStrength.level">
							{{ passwordStrength.text }}
						</view>
					</view>
				</view>

				<!-- 确认新密码 -->
				<view class="form-group">
					<view class="form-label">确认新密码</view>
					<view class="password-input-wrapper">
						<input 
							v-model="formData.confirmPassword" 
							class="form-input" 
							:type="showConfirmPassword ? 'text' : 'password'"
							placeholder="请再次输入新密码"
							maxlength="50"
						/>
						<view class="password-toggle" @click="toggleConfirmPassword">
							<simple-icon 
								:type="showConfirmPassword ? 'eye-off' : 'eye'" 
								color="#999" 
								size="20"
							></simple-icon>
						</view>
					</view>
					<view v-if="formData.confirmPassword && !passwordsMatch" class="error-tip">
						两次输入的密码不一致
					</view>
				</view>
			</view>
		</view>

		<!-- 密码要求提示 -->
		<view class="password-tips" v-if="!checkingPassword">
			<view class="tips-title">密码要求：</view>
			<view class="tips-list">
				<view class="tip-item">• 长度为6-50个字符</view>
				<view class="tip-item">• 建议包含字母、数字和特殊字符</view>
				<view class="tip-item" v-if="hasPassword">• 不能与当前密码相同</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="submit-section">
			<button 
				class="submit-btn" 
				:class="{ disabled: !canSubmit }"
				:disabled="!canSubmit"
				@click="changePassword"
			>
				{{ submitting ? (hasPassword ? '修改中...' : '设置中...') : (hasPassword ? '确认修改' : '设置密码') }}
			</button>
		</view>
	</view>
</template>

<script>
	import { changePassword } from '../../api/auth.js';
	import { showSuccess, showError, showLoading, hideLoading, clearUserInfo } from '../../utils/storage.js';
	import request from '../../utils/request.js';
	import SimpleIcon from '../../components/simple-icon/simple-icon.vue';

	export default {
		components: {
			SimpleIcon
		},
		data() {
			return {
				formData: {
					oldPassword: '',
					newPassword: '',
					confirmPassword: ''
				},
				showOldPassword: false,
				showNewPassword: false,
				showConfirmPassword: false,
				submitting: false,
				hasPassword: true, // 用户是否已设置密码
				checkingPassword: true // 是否正在检查密码状态
			};
		},

		onLoad() {
			this.checkPasswordStatus();
		},

		computed: {
			/**
			 * 密码强度检测
			 */
			passwordStrength() {
				const password = this.formData.newPassword;
				if (!password) {
					return { level: 'none', text: '请输入密码' };
				}
				
				let score = 0;
				
				// 长度检查
				if (password.length >= 8) score += 1;
				if (password.length >= 12) score += 1;
				
				// 字符类型检查
				if (/[a-z]/.test(password)) score += 1;
				if (/[A-Z]/.test(password)) score += 1;
				if (/[0-9]/.test(password)) score += 1;
				if (/[^A-Za-z0-9]/.test(password)) score += 1;
				
				if (score <= 2) {
					return { level: 'weak', text: '弱' };
				} else if (score <= 4) {
					return { level: 'medium', text: '中' };
				} else {
					return { level: 'strong', text: '强' };
				}
			},
			
			/**
			 * 密码是否匹配
			 */
			passwordsMatch() {
				return this.formData.newPassword === this.formData.confirmPassword;
			},
			
			/**
			 * 是否可以提交
			 */
			canSubmit() {
				if (this.submitting || this.checkingPassword) {
					return false;
				}

				// 基本验证：新密码长度和确认密码匹配
				const basicValid = this.formData.newPassword.length >= 6 && this.passwordsMatch;

				if (this.hasPassword) {
					// 已设置密码：需要旧密码，且新密码不能与旧密码相同
					return basicValid &&
						   this.formData.oldPassword.trim() &&
						   this.formData.oldPassword !== this.formData.newPassword;
				} else {
					// 未设置密码：只需要新密码和确认密码
					return basicValid;
				}
			}
		},
		
		methods: {
			/**
			 * 检查用户密码设置状态
			 */
			async checkPasswordStatus() {
				try {
					this.checkingPassword = true;

					const response = await request.get('auth-check-password.php');

					if (response.code === 200) {
						this.hasPassword = response.data.has_password;
					} else {
						console.error('检查密码状态失败:', response.message);
						// 默认假设已设置密码
						this.hasPassword = true;
					}
				} catch (error) {
					console.error('检查密码状态错误:', error);
					// 默认假设已设置密码
					this.hasPassword = true;
				} finally {
					this.checkingPassword = false;
				}
			},

			/**
			 * 切换旧密码显示
			 */
			toggleOldPassword() {
				this.showOldPassword = !this.showOldPassword;
			},
			
			/**
			 * 切换新密码显示
			 */
			toggleNewPassword() {
				this.showNewPassword = !this.showNewPassword;
			},
			
			/**
			 * 切换确认密码显示
			 */
			toggleConfirmPassword() {
				this.showConfirmPassword = !this.showConfirmPassword;
			},
			
			/**
			 * 修改密码
			 */
			async changePassword() {
				if (!this.canSubmit) return;
				
				// 最终验证
				if (this.formData.newPassword.length < 6) {
					showError('新密码长度不能少于6位');
					return;
				}

				if (!this.passwordsMatch) {
					showError('两次输入的密码不一致');
					return;
				}

				// 如果已设置密码，需要验证旧密码
				if (this.hasPassword) {
					if (!this.formData.oldPassword.trim()) {
						showError('请输入当前密码');
						return;
					}

					if (this.formData.oldPassword === this.formData.newPassword) {
						showError('新密码不能与当前密码相同');
						return;
					}
				}
				
				try {
					this.submitting = true;
					showLoading('修改中...');
					
					// 根据是否已设置密码传递不同的参数
					const requestData = {
						new_password: this.formData.newPassword
					};

					// 只有在已设置密码时才传递旧密码
					if (this.hasPassword) {
						requestData.old_password = this.formData.oldPassword;
					}

					const response = await changePassword(requestData);
					
					if (response.code === 200) {
						showSuccess('密码修改成功，请重新登录');
						
						// 清除用户信息
						clearUserInfo();
						
						// 延迟跳转到登录页
						setTimeout(() => {
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}, 2000);
					} else {
						showError(response.message || '密码修改失败');
					}
				} catch (error) {
					console.error('修改密码失败:', error);
					showError('修改密码失败，请稍后重试');
				} finally {
					this.submitting = false;
					hideLoading();
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
.change-password-container {
	background: #f8f9fa;
	min-height: 100vh;
	padding: 15px;
}

.form-container {
	background: #fff;
	border-radius: 12px;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-header {
	text-align: center;
	padding: 30px 20px 20px;
	background: linear-gradient(135deg, #007bff, #0056b3);
	color: #fff;

	.header-icon {
		margin-bottom: 15px;
	}

	.header-title {
		font-size: 20px;
		font-weight: 600;
		margin-bottom: 8px;
	}

	.header-subtitle {
		font-size: 14px;
		opacity: 0.9;
	}
}

.form-content {
	padding: 25px 20px;
}

/* 首次设置密码提示 */
.first-time-tip {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 20px;
	margin: 15px 0;
	background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
	border-radius: 12px;
	border-left: 4px solid #2196f3;

	.tip-icon {
		font-size: 24px;
	}

	.tip-text {
		flex: 1;
		font-size: 14px;
		color: #1976d2;
		font-weight: 500;
		line-height: 1.4;
	}
}

/* 加载状态提示 */
.loading-tip {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12px;
	padding: 30px;
	margin: 15px 0;

	.loading-icon {
		font-size: 20px;
		animation: rotate 1s linear infinite;
	}

	.loading-text {
		font-size: 14px;
		color: #666;
	}
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.form-group {
	margin-bottom: 25px;

	.form-label {
		font-size: 16px;
		color: #333;
		margin-bottom: 10px;
		font-weight: 500;
	}
}

.password-input-wrapper {
	position: relative;

	.form-input {
		width: 100%;
		height: 45px;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		padding: 0 50px 0 15px;
		font-size: 16px;
		color: #333;
		background: #fff;

		&:focus {
			border-color: #007bff;
		}
	}

	.password-toggle {
		position: absolute;
		right: 15px;
		top: 50%;
		transform: translateY(-50%);
		cursor: pointer;
	}
}

.password-strength {
	display: flex;
	align-items: center;
	margin-top: 8px;
	gap: 8px;

	.strength-label {
		font-size: 12px;
		color: #666;
	}

	.strength-indicator {
		flex: 1;
		height: 4px;
		background: #e9ecef;
		border-radius: 2px;
		overflow: hidden;

		.strength-bar {
			height: 100%;
			border-radius: 2px;
			transition: all 0.3s;

			&.weak {
				width: 33%;
				background: #dc3545;
			}

			&.medium {
				width: 66%;
				background: #ffc107;
			}

			&.strong {
				width: 100%;
				background: #28a745;
			}
		}
	}

	.strength-text {
		font-size: 12px;
		font-weight: 500;

		&.weak { color: #dc3545; }
		&.medium { color: #ffc107; }
		&.strong { color: #28a745; }
	}
}

.error-tip {
	font-size: 12px;
	color: #dc3545;
	margin-top: 5px;
}

.password-tips {
	background: #fff;
	border-radius: 12px;
	padding: 20px;
	margin-top: 15px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

	.tips-title {
		font-size: 16px;
		color: #333;
		font-weight: 500;
		margin-bottom: 10px;
	}

	.tips-list {
		.tip-item {
			font-size: 14px;
			color: #666;
			line-height: 1.6;
			margin-bottom: 5px;
		}
	}
}

.submit-section {
	margin-top: 30px;

	.submit-btn {
		width: 100%;
		height: 50px;
		background: #007bff;
		color: #fff;
		border: none;
		border-radius: 25px;
		font-size: 16px;
		font-weight: 500;

		&.disabled {
			background: #6c757d;
			opacity: 0.6;
		}
	}
}
</style>
