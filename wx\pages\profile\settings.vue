<template>
	<view class="settings-container">
		<!-- 账户设置 -->
		<view class="settings-group" v-if="isLoggedIn && showAccountSettings">
			<view class="group-title">账户设置</view>
			<view class="settings-list">
				<view class="setting-item" @click="editProfile" v-if="showProfileEdit">
					<view class="setting-left">
						<simple-icon type="person" color="#007bff" size="20"></simple-icon>
						<text class="setting-text">个人信息</text>
					</view>
					<view class="setting-right">
						<text class="setting-value">{{ userInfo.name || '未设置' }}</text>
						<simple-icon type="forward" color="#ccc" size="16"></simple-icon>
					</view>
				</view>

				<view class="setting-item" @click="changePassword" v-if="showPasswordChange">
					<view class="setting-left">
						<simple-icon type="locked" color="#28a745" size="20"></simple-icon>
						<text class="setting-text">修改密码</text>
					</view>
					<view class="setting-right">
						<simple-icon type="forward" color="#ccc" size="16"></simple-icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 应用设置 -->
		<view class="settings-group">
			<view class="group-title">应用设置</view>
			<view class="settings-list">
				<view class="setting-item" @click="clearCache">
					<view class="setting-left">
						<simple-icon type="trash" color="#dc3545" size="20"></simple-icon>
						<text class="setting-text">清除缓存</text>
					</view>
					<view class="setting-right">
						<text class="setting-value">{{ cacheSize }}</text>
						<simple-icon type="forward" color="#ccc" size="16"></simple-icon>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 其他设置 -->
		<view class="settings-group">
			<view class="group-title">其他</view>
			<view class="settings-list">
				<view class="setting-item" @click="checkUpdate">
					<view class="setting-left">
						<simple-icon type="reload" color="#28a745" size="20"></simple-icon>
						<text class="setting-text">检查更新</text>
					</view>
					<view class="setting-right">
						<text class="setting-value">v1.0.0</text>
						<simple-icon type="forward" color="#ccc" size="16"></simple-icon>
					</view>
				</view>

				<view class="setting-item" @click="showPrivacy">
					<view class="setting-left">
						<simple-icon type="paperplane" color="#fd7e14" size="20"></simple-icon>
						<text class="setting-text">隐私政策</text>
					</view>
					<view class="setting-right">
						<simple-icon type="forward" color="#ccc" size="16"></simple-icon>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 退出登录 -->
		<view class="logout-section" v-if="isLoggedIn">
			<button class="logout-btn" @click="handleLogout">退出登录</button>
		</view>
	</view>
</template>

<script>
	import { isLoggedIn, getUserInfo, clearUserInfo, showSuccess, showConfirm, showError } from '../../utils/storage.js';
	import { logout } from '../../api/auth.js';
	import { getSystemSettingsWithCache, isFeatureEnabled, getFeatureDisabledMessage } from '../../api/system.js';
	import SimpleIcon from '../../components/simple-icon/simple-icon.vue';

	export default {
		components: {
			SimpleIcon
		},
		data() {
			return {
				isLoggedIn: false,
				userInfo: {},

				// 设置选项
				notificationEnabled: true,
				soundEnabled: true,
				wifiOnlyEnabled: false,
				cacheSize: '0MB',

				// 系统设置
				systemSettings: {},
				systemSettingsLoaded: false
			};
		},

		computed: {
			/**
			 * 是否显示修改密码功能
			 */
			showPasswordChange() {
				const result = this.systemSettingsLoaded && isFeatureEnabled('password_change', this.systemSettings);
				console.log('showPasswordChange计算:', {
					systemSettingsLoaded: this.systemSettingsLoaded,
					systemSettings: this.systemSettings,
					allow_password_change: this.systemSettings.allow_password_change,
					result: result
				});
				return result;
			},

			/**
			 * 是否显示个人信息编辑功能
			 */
			showProfileEdit() {
				const result = this.systemSettingsLoaded && isFeatureEnabled('profile_edit', this.systemSettings);
				console.log('showProfileEdit计算:', {
					systemSettingsLoaded: this.systemSettingsLoaded,
					systemSettings: this.systemSettings,
					allow_profile_edit: this.systemSettings.allow_profile_edit,
					result: result
				});
				return result;
			},

			/**
			 * 是否显示账户设置区域
			 * 只有当个人信息编辑或修改密码功能至少有一个可用时才显示
			 */
			showAccountSettings() {
				const result = this.showProfileEdit || this.showPasswordChange;
				console.log('showAccountSettings计算:', {
					showProfileEdit: this.showProfileEdit,
					showPasswordChange: this.showPasswordChange,
					result: result
				});
				return result;
			}
		},

		onLoad() {
			this.initPage();
		},
		
		onShow() {
			this.checkLoginStatus();
			this.loadSettings();
		},
		
		methods: {
			/**
			 * 初始化页面
			 */
			initPage() {
				this.checkLoginStatus();
				this.loadSettings();
				this.calculateCacheSize();
				this.loadSystemSettings();
			},
			
			/**
			 * 检查登录状态
			 */
			checkLoginStatus() {
				this.isLoggedIn = isLoggedIn();
				if (this.isLoggedIn) {
					this.userInfo = getUserInfo() || {};
				}
			},
			
			/**
			 * 加载设置
			 */
			loadSettings() {
				// 从本地存储加载设置
				this.notificationEnabled = uni.getStorageSync('notification_enabled') !== false;
				this.soundEnabled = uni.getStorageSync('sound_enabled') !== false;
				this.wifiOnlyEnabled = uni.getStorageSync('wifi_only_enabled') === true;
			},
			
			/**
			 * 计算缓存大小
			 */
			calculateCacheSize() {
				// 这里可以计算实际的缓存大小
				this.cacheSize = '2.5MB';
			},

			/**
			 * 加载系统设置
			 */
			async loadSystemSettings() {
				try {
					// 强制刷新系统设置，确保获取最新配置
					const response = await getSystemSettingsWithCache(true);
					if (response.code === 200) {
						this.systemSettings = response.data || {};
						this.systemSettingsLoaded = true;
						console.log('系统设置加载成功:', this.systemSettings);
					} else {
						console.error('获取系统设置失败:', response.message);
					}
				} catch (error) {
					console.error('加载系统设置失败:', error);
					// 使用默认设置
					this.systemSettings = {
						allow_password_change: true,
						allow_profile_edit: true,
						password_change_notice: '管理员已禁用密码修改功能，如需修改密码请联系客服。'
					};
					this.systemSettingsLoaded = true;
				}
			},
			
			/**
			 * 编辑个人信息
			 */
			editProfile() {
				// 检查是否允许编辑个人信息
				if (!isFeatureEnabled('profile_edit', this.systemSettings)) {
					const message = getFeatureDisabledMessage('profile_edit', this.systemSettings);
					uni.showModal({
						title: '功能不可用',
						content: message,
						showCancel: false,
						confirmText: '我知道了'
					});
					return;
				}

				uni.navigateTo({
					url: '/pages/profile/edit-profile'
				});
			},

			/**
			 * 修改密码
			 */
			changePassword() {
				// 检查是否允许修改密码
				if (!isFeatureEnabled('password_change', this.systemSettings)) {
					const message = getFeatureDisabledMessage('password_change', this.systemSettings);
					uni.showModal({
						title: '功能不可用',
						content: message,
						showCancel: false,
						confirmText: '我知道了'
					});
					return;
				}

				uni.navigateTo({
					url: '/pages/profile/change-password'
				});
			},
			

			
			/**
			 * 切换消息通知
			 */
			toggleNotification(e) {
				this.notificationEnabled = e.detail.value;
				uni.setStorageSync('notification_enabled', this.notificationEnabled);
			},
			
			/**
			 * 切换声音提醒
			 */
			toggleSound(e) {
				this.soundEnabled = e.detail.value;
				uni.setStorageSync('sound_enabled', this.soundEnabled);
			},
			
			/**
			 * 切换WiFi播放
			 */
			toggleWifiOnly(e) {
				this.wifiOnlyEnabled = e.detail.value;
				uni.setStorageSync('wifi_only_enabled', this.wifiOnlyEnabled);
			},
			
			/**
			 * 清除缓存
			 */
			async clearCache() {
				const confirmed = await showConfirm('确定要清除缓存吗？');
				if (!confirmed) return;
				
				try {
					// 清除缓存逻辑
					uni.clearStorageSync();
					this.cacheSize = '0MB';
					showSuccess('缓存清除成功');
				} catch (error) {
					console.error('清除缓存失败:', error);
					showError('清除缓存失败');
				}
			},
			
			/**
			 * 检查更新
			 */
			checkUpdate() {
				uni.showModal({
					title: '检查更新',
					content: '当前已是最新版本',
					showCancel: false
				});
			},
			
			/**
			 * 联系客服 - 直接跳转到小程序页面客服
			 */
			contactCustomerService() {
				// 显示加载提示
				uni.showLoading({
					title: '正在连接客服...'
				});

				try {
					// 直接跳转到小程序的页面客服
					// 使用微信小程序的客服会话功能
					// #ifdef MP-WEIXIN
					wx.openCustomerServiceChat({
						extInfo: {
							url: 'https://work.weixin.qq.com/kfid/kf_001' // 可以配置具体的客服ID
						},
						corpId: '', // 企业微信的corpId，如果有的话
						success: (res) => {
							console.log('客服聊天打开成功:', res);
							uni.hideLoading();
							uni.showToast({
								title: '客服连接成功',
								icon: 'success'
							});
						},
						fail: (err) => {
							console.error('客服聊天打开失败:', err);
							uni.hideLoading();

							// 检查是否是开发者工具环境
							if (err.errMsg && err.errMsg.includes('开发者工具暂时不支持')) {
								uni.showModal({
									title: '开发者工具提示',
									content: '客服功能在开发者工具中无法调试，请在真机上测试。\n\n真机测试时客服功能将正常工作。\n\n您也可以使用小程序右上角菜单中的"联系客服"。',
									showCancel: false,
									confirmText: '我知道了'
								});
							} else {
								// 其他错误情况
								uni.showModal({
									title: '客服连接失败',
									content: '无法打开客服聊天，请稍后再试或通过其他方式联系我们。\n\n您也可以在小程序右上角菜单中选择"联系客服"。',
									showCancel: false,
									confirmText: '我知道了'
								});
							}
						}
					});
					// #endif

					// #ifndef MP-WEIXIN
					// 非微信小程序环境的处理
					uni.hideLoading();
					uni.showModal({
						title: '客服功能',
						content: '客服功能仅在微信小程序中可用，请在微信小程序中使用此功能。',
						showCancel: false,
						confirmText: '我知道了'
					});
					// #endif

				} catch (error) {
					console.error('联系客服失败:', error);
					uni.hideLoading();
					uni.showModal({
						title: '连接失败',
						content: '无法连接到客服，请稍后再试。\n\n您也可以在小程序右上角菜单中选择"联系客服"。',
						showCancel: false,
						confirmText: '我知道了'
					});
				}
			},


			
			/**
			 * 显示隐私政策
			 */
			showPrivacy() {
				uni.showModal({
					title: '隐私政策',
					content: '我们重视您的隐私保护，详细隐私政策请查看官网。',
					showCancel: false
				});
			},
			
			/**
			 * 退出登录
			 */
			async handleLogout() {
				const confirmed = await showConfirm('确定要退出登录吗？');
				if (!confirmed) return;
				
				try {
					// 调用退出登录接口
					await logout();
				} catch (error) {
					console.error('退出登录失败:', error);
				} finally {
					// 清除本地用户信息
					clearUserInfo();
					
					showSuccess('已退出登录');
					
					// 跳转到登录页
					uni.reLaunch({
						url: '/pages/login/login'
					});
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
.settings-container {
	background: #f8f9fa;
	min-height: 100vh;
	padding: 15px;
}

.settings-group {
	margin-bottom: 20px;

	.group-title {
		font-size: 14px;
		color: #666;
		margin-bottom: 10px;
		padding: 0 5px;
	}

	.settings-list {
		background: #fff;
		border-radius: 12px;
		overflow: hidden;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

		.setting-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 18px 20px;
			border-bottom: 1px solid #f8f9fa;

			&:last-child {
				border-bottom: none;
			}

			.setting-left {
				display: flex;
				align-items: center;
				gap: 15px;
				flex: 1;

				.setting-text {
					font-size: 16px;
					color: #333;
				}
			}

			.setting-right {
				display: flex;
				align-items: center;
				gap: 10px;

				.setting-value {
					font-size: 14px;
					color: #999;
				}

				.unbind-btn {
					background: #dc3545;
					color: #fff;
					border: none;
					border-radius: 12px;
					padding: 4px 12px;
					font-size: 12px;
				}
			}
		}
	}
}

.logout-section {
	margin-top: 30px;

	.logout-btn {
		width: 100%;
		height: 50px;
		background: #dc3545;
		color: #fff;
		border: none;
		border-radius: 25px;
		font-size: 16px;
		font-weight: 500;
	}
}
</style>
