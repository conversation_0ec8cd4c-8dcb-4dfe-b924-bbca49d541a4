<?php
/**
 * 微信小程序登录API
 * 支持微信code登录和用户信息绑定
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

$auth = new AuthAPI();

// 获取微信小程序配置
try {
    // Check if settings table exists
    $table_check = $auth->getConn()->query("SHOW TABLES LIKE 'settings'");
    if (!$table_check || $table_check->num_rows == 0) {
        $auth->jsonResponse(500, '系统配置表不存在，请先安装系统');
    }
    
    $settings_result = $auth->getConn()->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('wechat_app_id', 'wechat_app_secret')");
    
    if (!$settings_result) {
        $auth->jsonResponse(500, '无法读取系统配置，请检查数据库连接');
    }
    
    $wechat_config = [];
    while ($row = $settings_result->fetch_assoc()) {
        $wechat_config[$row['setting_key']] = $row['setting_value'];
    }
    
    $app_id = $wechat_config['wechat_app_id'] ?? '';
    $app_secret = $wechat_config['wechat_app_secret'] ?? '';
    
    if (empty($app_id)) {
        $auth->jsonResponse(500, '微信小程序AppID未配置，请在管理后台设置');
    }
    
    if (empty($app_secret)) {
        $auth->jsonResponse(500, '微信小程序AppSecret未配置，请在管理后台设置');
    }
    
} catch (Exception $e) {
    error_log('WeChat config error: ' . $e->getMessage());
    $auth->jsonResponse(500, '读取微信配置时发生错误：' . $e->getMessage());
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    $auth->jsonResponse(400, '无效的请求数据');
}

$code = $input['code'] ?? '';
$user_info = $input['userInfo'] ?? null;
$device_info = $input['device_info'] ?? null;

// 验证必填字段
if (empty($code)) {
    $auth->jsonResponse(400, '缺少微信授权码');
}

try {
    // 调用微信API获取session_key和openid
    $url = "https://api.weixin.qq.com/sns/jscode2session?appid={$app_id}&secret={$app_secret}&js_code={$code}&grant_type=authorization_code";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code !== 200 || !$response) {
        $auth->jsonResponse(500, '微信服务器连接失败');
    }
    
    $wechat_data = json_decode($response, true);
    
    if (isset($wechat_data['errcode'])) {
        $error_msg = '微信登录失败';
        switch ($wechat_data['errcode']) {
            case 40013:
                $error_msg = '无效的微信授权码';
                break;
            case 40029:
                $error_msg = '微信授权码已过期';
                break;
            case 45011:
                $error_msg = '微信API调用频率限制';
                break;
        }
        $auth->jsonResponse(400, $error_msg);
    }
    
    $openid = $wechat_data['openid'] ?? '';
    $session_key = $wechat_data['session_key'] ?? '';
    $unionid = $wechat_data['unionid'] ?? null;
    
    if (empty($openid)) {
        $auth->jsonResponse(500, '获取微信用户标识失败');
    }
    
    // 查找是否已有微信用户记录
    $stmt = $auth->getConn()->prepare("SELECT wu.*, u.* FROM wechat_users wu JOIN users u ON wu.user_id = u.id WHERE wu.openid = ? AND u.status != 'banned'");
    $stmt->bind_param("s", $openid);
    $stmt->execute();
    $existing_user = $stmt->get_result()->fetch_assoc();
    
    if ($existing_user) {
        // 已有用户，更新session_key
        $stmt = $auth->getConn()->prepare("UPDATE wechat_users SET session_key = ?, unionid = ?, updated_at = NOW() WHERE openid = ?");
        $stmt->bind_param("sss", $session_key, $unionid, $openid);
        $stmt->execute();

        $user_id = $existing_user['user_id'];

        // 如果用户原来是traditional类型，现在使用微信登录，更新为both类型
        if ($existing_user['login_type'] === 'traditional') {
            $stmt = $auth->getConn()->prepare("UPDATE users SET login_type = 'both', updated_at = NOW() WHERE id = ?");
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
        }

        // 如果提供了用户信息，更新用户资料
        if ($user_info && is_array($user_info)) {
            $nickname = $user_info['nickName'] ?? $existing_user['nickname'];
            $avatar = $user_info['avatarUrl'] ?? $existing_user['avatar'];
            $gender = isset($user_info['gender']) ? intval($user_info['gender']) : $existing_user['gender'];

            $stmt = $auth->getConn()->prepare("UPDATE users SET nickname = ?, avatar = ?, gender = ?, updated_at = NOW() WHERE id = ?");
            $stmt->bind_param("ssii", $nickname, $avatar, $gender, $user_id);
            $stmt->execute();
        }
        
    } else {
        // 新用户，创建账户
        $nickname = '微信用户';
        $avatar = '';
        $gender = 0;
        
        if ($user_info && is_array($user_info)) {
            $nickname = $user_info['nickName'] ?? '微信用户';
            $avatar = $user_info['avatarUrl'] ?? '';
            $gender = isset($user_info['gender']) ? intval($user_info['gender']) : 0;
        }
        
        // 生成唯一的邮箱（使用openid）
        $email = 'wechat_' . substr($openid, -8) . '@wechat.local';
        $name = $nickname;
        
        // 开始事务
        $auth->getConn()->begin_transaction();
        
        try {
            // 创建用户记录
            $stmt = $auth->getConn()->prepare("INSERT INTO users (name, email, nickname, avatar, gender, status, login_type) VALUES (?, ?, ?, ?, ?, 'active', 'wechat')");
            $stmt->bind_param("ssssi", $name, $email, $nickname, $avatar, $gender);
            $stmt->execute();
            
            $user_id = $auth->getConn()->insert_id;
            
            // 创建微信用户记录
            $stmt = $auth->getConn()->prepare("INSERT INTO wechat_users (user_id, openid, unionid, session_key) VALUES (?, ?, ?, ?)");
            $stmt->bind_param("isss", $user_id, $openid, $unionid, $session_key);
            $stmt->execute();
            
            $auth->getConn()->commit();
            
        } catch (Exception $e) {
            $auth->getConn()->rollback();
            throw $e;
        }
    }
    
    // 生成令牌
    $access_token = $auth->generateJWT($user_id, 'access');
    $refresh_token = $auth->generateJWT($user_id, 'refresh');
    
    // 保存令牌
    $auth->saveToken($user_id, $access_token, 'access', $device_info);
    $auth->saveToken($user_id, $refresh_token, 'refresh', $device_info);
    
    // 更新最后登录信息
    $auth->updateLastLogin($user_id);
    
    // 记录登录日志
    $auth->logLogin($user_id, 'wechat', 'wechat_code', 'success');
    
    // 获取完整用户信息
    $user_info_result = $auth->getUserById($user_id);

    // 检查手机号绑定配置
    $phone_settings_result = $auth->getConn()->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('phone_bind_required', 'phone_bind_force_new_user', 'phone_bind_mode')");
    $phone_settings = [];
    while ($row = $phone_settings_result->fetch_assoc()) {
        $phone_settings[$row['setting_key']] = $row['setting_value'];
    }

    $phone_bind_required = $phone_settings['phone_bind_required'] ?? '0';
    $phone_bind_force_new_user = $phone_settings['phone_bind_force_new_user'] ?? '0';
    $phone_bind_mode = $phone_settings['phone_bind_mode'] ?? 'direct';

    // 检查是否需要绑定手机号
    $need_phone_bind = false;
    $phone_bind_reason = '';

    if ($phone_bind_required === '1') {
        if (!$user_info_result['phone_verified']) {
            if (!$existing_user && $phone_bind_force_new_user === '1') {
                // 新用户强制绑定
                $need_phone_bind = true;
                $phone_bind_reason = 'new_user_required';
            } elseif ($user_info_result['phone_bind_required']) {
                // 用户被标记为需要绑定
                $need_phone_bind = true;
                $phone_bind_reason = 'user_required';
            }
        }
    }

    // 如果需要绑定手机号，返回特殊状态
    if ($need_phone_bind) {
        $auth->jsonResponse(202, '登录成功，需要绑定手机号', [
            'access_token' => $access_token,
            'refresh_token' => $refresh_token,
            'token_type' => 'Bearer',
            'expires_in' => 7200,
            'is_new_user' => !$existing_user,
            'phone_bind_required' => true,
            'phone_bind_mode' => $phone_bind_mode,
            'phone_bind_reason' => $phone_bind_reason,
            'openid' => $openid, // 添加openid给前端使用
            'user' => [
                'id' => $user_info_result['id'],
                'name' => $user_info_result['name'],
                'email' => $user_info_result['email'],
                'username' => $user_info_result['username'],
                'nickname' => $user_info_result['nickname'],
                'avatar' => $user_info_result['avatar'],
                'phone' => $user_info_result['phone'],
                'phone_verified' => (bool)$user_info_result['phone_verified'],
                'gender' => $user_info_result['gender'],
                'login_type' => $user_info_result['login_type'],
                'last_login_at' => $user_info_result['last_login_at'],
                'login_count' => $user_info_result['login_count']
            ]
        ]);
    }

    // 正常登录响应
    $auth->jsonResponse(200, '微信登录成功', [
        'access_token' => $access_token,
        'refresh_token' => $refresh_token,
        'token_type' => 'Bearer',
        'expires_in' => 7200,
        'is_new_user' => !$existing_user,
        'phone_bind_required' => false,
        'openid' => $openid, // 添加openid给前端使用
        'user' => [
            'id' => $user_info_result['id'],
            'name' => $user_info_result['name'],
            'email' => $user_info_result['email'],
            'username' => $user_info_result['username'],
            'nickname' => $user_info_result['nickname'],
            'avatar' => $user_info_result['avatar'],
            'phone' => $user_info_result['phone'],
            'phone_verified' => (bool)$user_info_result['phone_verified'],
            'gender' => $user_info_result['gender'],
            'login_type' => $user_info_result['login_type'],
            'last_login_at' => $user_info_result['last_login_at'],
            'login_count' => $user_info_result['login_count']
        ]
    ]);
    
} catch (Exception $e) {
    error_log('微信登录错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}