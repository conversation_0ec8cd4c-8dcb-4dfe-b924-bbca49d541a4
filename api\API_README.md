# 公告系统 API 接口文档

## 基础信息

**Base URL**: `https://wx.yx420.cn/api/`
**Content-Type**: `application/json; charset=utf-8`
**CORS**: 支持跨域请求

## 接口列表

### 认证相关接口

#### 1. 用户登录
**接口地址**: `auth-login.php`
**请求方式**: `POST`

#### 2. 微信登录
**接口地址**: `auth-wechat.php`
**请求方式**: `POST`

#### 3. 获取用户信息
**接口地址**: `auth-me.php`
**请求方式**: `GET`

#### 4. 刷新Token
**接口地址**: `auth-refresh.php`
**请求方式**: `POST`

#### 5. 用户注册
**接口地址**: `auth-register.php`
**请求方式**: `POST`

### 公告相关接口

#### 6. 获取公告列表

**接口地址**: `announcementList.php`
**请求方式**: `GET`

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码 |
| limit | int | 否 | 10 | 每页数量 |
| type | string | 否 | - | 公告类型：notice/urgent/system/activity |
| category_id | int | 否 | 0 | 分类ID |
| status | string | 否 | published | 状态：draft/published/archived |

#### 7. 获取公告详情
**接口地址**: `announcementDetail.php`
**请求方式**: `GET`

#### 8. 标记公告已读
**接口地址**: `announcementDetail.php`
**请求方式**: `POST`

#### 9. 收藏公告
**接口地址**: `announcement-favorite.php`
**请求方式**: `POST`

#### 10. 取消收藏公告
**接口地址**: `announcement-favorite.php`
**请求方式**: `DELETE`

#### 11. 检查收藏状态
**接口地址**: `announcement-favorite.php`
**请求方式**: `GET`

#### 12. 获取收藏列表
**接口地址**: `announcement-favorites.php`
**请求方式**: `GET`

#### 13. 获取未读公告数量
**接口地址**: `announcement-unread-count.php`
**请求方式**: `GET`

#### 14. 获取已读公告列表
**接口地址**: `announcement-read.php`
**请求方式**: `GET`

#### 15. 批量标记已读
**接口地址**: `announcement-batch-read.php`
**请求方式**: `POST`

#### 16. 获取阅读历史
**接口地址**: `announcement-read-history.php`
**请求方式**: `GET`

#### 17. 获取公告统计
**接口地址**: `announcement-statistics.php`
**请求方式**: `GET`

### 课程相关接口

#### 18. 获取课程列表
**接口地址**: `courseList.php`
**请求方式**: `GET`

#### 19. 获取课程详情
**接口地址**: `courseDetail.php`
**请求方式**: `GET`

#### 20. 更新学习进度
**接口地址**: `courseDetail.php`
**请求方式**: `POST`

#### 21. 获取用户课程
**接口地址**: `userCourses.php`
**请求方式**: `GET`

### 用户相关接口

#### 22. 获取用户资料
**接口地址**: `profile.php`
**请求方式**: `GET`

#### 23. 更新用户资料
**接口地址**: `profile.php`
**请求方式**: `POST/PUT`

#### 24. 获取分类列表
**接口地址**: `categoryList.php`
**请求方式**: `GET`

#### 请求示例

```javascript
// 获取已发布的公告列表
fetch('https://wx.yx420.cn/api/announcementList.php?page=1&limit=10&status=published')

// 获取紧急公告
fetch('https://wx.yx420.cn/api/announcementList.php?type=urgent')

// 获取指定分类的公告
fetch('https://wx.yx420.cn/api/announcementList.php?category_id=1')
```

#### 响应格式

```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "title": "欢迎使用公告系统",
                "content": "这是一个示例公告...",
                "type": "notice",
                "priority": 0,
                "is_pinned": 1,
                "category_id": 3,
                "category_name": "通知公告",
                "publish_time": "2025-07-13 03:00:00",
                "expire_time": "2025-08-12 03:00:00",
                "view_count": 0,
                "author_name": "admin",
                "created_at": "2025-07-13 03:00:00",
                "updated_at": "2025-07-13 03:00:00"
            }
        ],
        "pagination": {
            "page": 1,
            "limit": 10,
            "total": 1,
            "pages": 1
        }
    },
    "timestamp": 1720819200
}
```

### 2. 获取公告详情

**接口地址**: `announcementDetail.php`
**请求方式**: `GET`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 公告ID |

#### 请求示例

```javascript
fetch('https://wx.yx420.cn/api/announcementDetail.php?id=1')
```

#### 响应格式

```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "title": "欢迎使用公告系统",
        "content": "这是一个示例公告，用于测试公告系统的功能。",
        "type": "notice",
        "priority": 0,
        "is_pinned": 1,
        "category_id": 3,
        "category_name": "通知公告",
        "publish_time": "2025-07-13 03:00:00",
        "expire_time": "2025-08-12 03:00:00",
        "view_count": 1,
        "author_name": "admin",
        "created_at": "2025-07-13 03:00:00",
        "updated_at": "2025-07-13 03:00:00"
    },
    "timestamp": 1720819200
}
```

### 3. 标记公告为已读

**接口地址**: `announcementDetail.php`
**请求方式**: `POST`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| announcement_id | int | 是 | 公告ID |
| user_id | int | 是 | 用户ID |

#### 请求示例

```javascript
fetch('https://wx.yx420.cn/api/announcementDetail.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        announcement_id: 1,
        user_id: 123
    })
})
```

#### 响应格式

```json
{
    "code": 200,
    "message": "标记为已读成功",
    "data": null,
    "timestamp": 1720819200
}
```

### 4. 获取分类列表

**接口地址**: `categoryList.php`
**请求方式**: `GET`

#### 请求示例

```javascript
fetch('https://wx.yx420.cn/api/categoryList.php')
```

#### 响应格式

```json
{
    "code": 200,
    "message": "获取成功",
    "data": [
        {
            "id": 1,
            "name": "系统公告",
            "description": "系统维护、更新等相关公告",
            "sort_order": 1
        },
        {
            "id": 2,
            "name": "活动公告",
            "description": "各类活动、促销等公告",
            "sort_order": 2
        },
        {
            "id": 3,
            "name": "通知公告",
            "description": "一般性通知公告",
            "sort_order": 3
        },
        {
            "id": 4,
            "name": "紧急公告",
            "description": "紧急重要公告",
            "sort_order": 0
        }
    ],
    "timestamp": 1720819200
}
```

### 5. 创建公告（管理员功能）

**接口地址**: `announcementList.php`
**请求方式**: `POST`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| title | string | 是 | 公告标题 |
| content | string | 是 | 公告内容 |
| type | string | 是 | 公告类型：notice/urgent/system/activity |
| author_id | int | 是 | 发布者ID |
| status | string | 否 | 状态，默认draft |
| priority | int | 否 | 优先级，默认0 |
| is_pinned | int | 否 | 是否置顶，默认0 |
| category_id | int | 否 | 分类ID |
| publish_time | string | 否 | 发布时间 |
| expire_time | string | 否 | 过期时间 |

#### 请求示例

```javascript
fetch('https://wx.yx420.cn/api/announcementList.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        title: "新公告标题",
        content: "公告内容详情...",
        type: "notice",
        status: "published",
        priority: 1,
        is_pinned: 0,
        category_id: 3,
        author_id: 1,
        publish_time: "2025-07-13 10:00:00",
        expire_time: "2025-08-13 10:00:00"
    })
})
```

### 6. 更新公告（管理员功能）

**接口地址**: `announcementList.php`
**请求方式**: `PUT`

#### 请求参数

同创建公告，但需要额外传入：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 公告ID |

### 7. 删除公告（管理员功能）

**接口地址**: `announcementList.php`
**请求方式**: `DELETE`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 公告ID（通过URL参数传递） |

#### 请求示例

```javascript
fetch('https://wx.yx420.cn/api/announcementList.php?id=1', {
    method: 'DELETE'
})
```

## 前端使用示例

### HTML 页面示例

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告系统前端示例</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .announcement { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .announcement.urgent { border-left: 5px solid #f44336; }
        .announcement.pinned { background-color: #fff3cd; }
        .badge { padding: 3px 8px; border-radius: 3px; font-size: 12px; color: white; }
        .badge.notice { background-color: #007bff; }
        .badge.urgent { background-color: #dc3545; }
        .badge.system { background-color: #ffc107; color: #212529; }
        .badge.activity { background-color: #28a745; }
        .pagination { text-align: center; margin: 20px 0; }
        .pagination button { margin: 0 5px; padding: 5px 10px; }
    </style>
</head>
<body>
    <h1>公告系统</h1>
    
    <!-- 分类筛选 -->
    <div>
        <label>分类筛选：</label>
        <select id="categoryFilter">
            <option value="">全部分类</option>
        </select>
        
        <label>类型筛选：</label>
        <select id="typeFilter">
            <option value="">全部类型</option>
            <option value="notice">通知</option>
            <option value="urgent">紧急</option>
            <option value="system">系统</option>
            <option value="activity">活动</option>
        </select>
        
        <button onclick="loadAnnouncements()">刷新</button>
    </div>
    
    <!-- 公告列表 -->
    <div id="announcementList"></div>
    
    <!-- 分页 -->
    <div id="pagination"></div>
    
    <!-- 公告详情模态框 -->
    <div id="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="background: white; margin: 50px auto; padding: 20px; width: 80%; max-width: 600px; border-radius: 5px;">
            <div id="modalContent"></div>
            <button onclick="closeModal()">关闭</button>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 1;
        const userId = 123; // 示例用户ID
        
        // 加载分类列表
        async function loadCategories() {
            try {
                const response = await fetch('https://wx.yx420.cn/api/categoryList.php');
                const result = await response.json();
                
                if (result.code === 200) {
                    const select = document.getElementById('categoryFilter');
                    result.data.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载分类失败:', error);
            }
        }
        
        // 加载公告列表
        async function loadAnnouncements(page = 1) {
            try {
                const categoryId = document.getElementById('categoryFilter').value;
                const type = document.getElementById('typeFilter').value;
                
                let url = `https://wx.yx420.cn/api/announcementList.php?page=${page}&limit=10`;
                if (categoryId) url += `&category_id=${categoryId}`;
                if (type) url += `&type=${type}`;
                
                const response = await fetch(url);
                const result = await response.json();
                
                if (result.code === 200) {
                    displayAnnouncements(result.data.list);
                    displayPagination(result.data.pagination);
                    currentPage = result.data.pagination.page;
                    totalPages = result.data.pagination.pages;
                } else {
                    console.error('获取公告失败:', result.message);
                }
            } catch (error) {
                console.error('网络错误:', error);
            }
        }
        
        // 显示公告列表
        function displayAnnouncements(announcements) {
            const container = document.getElementById('announcementList');
            container.innerHTML = '';
            
            if (announcements.length === 0) {
                container.innerHTML = '<p>暂无公告</p>';
                return;
            }
            
            announcements.forEach(announcement => {
                const div = document.createElement('div');
                div.className = `announcement ${announcement.type} ${announcement.is_pinned ? 'pinned' : ''}`;
                
                div.innerHTML = `
                    <h3>${announcement.title} 
                        <span class="badge ${announcement.type}">${getTypeText(announcement.type)}</span>
                        ${announcement.is_pinned ? '<span style="color: red;">📌</span>' : ''}
                    </h3>
                    <p>${announcement.content.substring(0, 100)}${announcement.content.length > 100 ? '...' : ''}</p>
                    <small>
                        分类: ${announcement.category_name || '无'} | 
                        发布时间: ${announcement.publish_time} | 
                        查看次数: ${announcement.view_count}
                    </small>
                    <br>
                    <button onclick="viewDetail(${announcement.id})">查看详情</button>
                `;
                
                container.appendChild(div);
            });
        }
        
        // 显示分页
        function displayPagination(pagination) {
            const container = document.getElementById('pagination');
            container.innerHTML = '';
            
            if (pagination.pages <= 1) return;
            
            for (let i = 1; i <= pagination.pages; i++) {
                const button = document.createElement('button');
                button.textContent = i;
                button.disabled = i === pagination.page;
                button.onclick = () => loadAnnouncements(i);
                container.appendChild(button);
            }
        }
        
        // 查看公告详情
        async function viewDetail(id) {
            try {
                const response = await fetch(`https://wx.yx420.cn/api/announcementDetail.php?id=${id}`);
                const result = await response.json();
                
                if (result.code === 200) {
                    const announcement = result.data;
                    document.getElementById('modalContent').innerHTML = `
                        <h2>${announcement.title}</h2>
                        <div class="badge ${announcement.type}">${getTypeText(announcement.type)}</div>
                        <p>${announcement.content}</p>
                        <small>
                            发布时间: ${announcement.publish_time}<br>
                            作者: ${announcement.author_name}<br>
                            查看次数: ${announcement.view_count}
                        </small>
                    `;
                    document.getElementById('modal').style.display = 'block';
                    
                    // 标记为已读
                    markAsRead(id);
                } else {
                    alert('获取公告详情失败: ' + result.message);
                }
            } catch (error) {
                console.error('获取详情失败:', error);
            }
        }
        
        // 标记为已读
        async function markAsRead(announcementId) {
            try {
                await fetch('https://wx.yx420.cn/api/announcementDetail.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        announcement_id: announcementId,
                        user_id: userId
                    })
                });
            } catch (error) {
                console.error('标记已读失败:', error);
            }
        }
        
        // 关闭模态框
        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }
        
        // 获取类型文本
        function getTypeText(type) {
            const types = {
                'notice': '通知',
                'urgent': '紧急',
                'system': '系统',
                'activity': '活动'
            };
            return types[type] || type;
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadCategories();
            loadAnnouncements();
        });
        
        // 筛选变化时重新加载
        document.getElementById('categoryFilter').addEventListener('change', () => loadAnnouncements(1));
        document.getElementById('typeFilter').addEventListener('change', () => loadAnnouncements(1));
    </script>
</body>
</html>
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 405 | 请求方法不支持 |
| 500 | 服务器内部错误 |

## 数据字段说明

### 公告字段 (announcements)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 公告ID |
| title | string | 公告标题 |
| content | text | 公告内容 |
| type | enum | 公告类型：notice(通知)/urgent(紧急)/system(系统)/activity(活动) |
| status | enum | 状态：draft(草稿)/published(已发布)/archived(已归档) |
| priority | tinyint | 优先级：0(普通)/1(重要)/2(紧急) |
| is_pinned | tinyint | 是否置顶：0(否)/1(是) |
| category_id | int | 分类ID |
| publish_time | datetime | 发布时间 |
| expire_time | datetime | 过期时间 |
| author_id | int | 发布者ID |
| view_count | int | 查看次数 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

### 分类字段 (announcement_categories)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 分类ID |
| name | string | 分类名称 |
| description | text | 分类描述 |
| sort_order | int | 排序 |
| is_active | tinyint | 是否启用：0(否)/1(是) |

## 用户管理相关接口

### 修改密码
**接口地址**: `auth-change-password.php`
**请求方式**: `POST`
**需要认证**: 是

**请求参数**:
```json
{
    "old_password": "当前密码",
    "new_password": "新密码"
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "密码修改成功，请重新登录",
    "data": {
        "user_id": 1,
        "changed_at": "2025-07-15 20:30:00",
        "force_relogin": true
    },
    "timestamp": 1721048200
}
```

### 更新用户资料
**接口地址**: `profile.php`
**请求方式**: `PUT`
**需要认证**: 是

**请求参数**:
```json
{
    "name": "姓名",
    "nickname": "昵称",
    "phone": "手机号",
    "gender": 1,
    "birthday": "1990-01-01",
    "avatar": "头像URL"
}
```

### 文件上传
**接口地址**: `upload.php`
**请求方式**: `POST`
**需要认证**: 是
**Content-Type**: `multipart/form-data`

**请求参数**:
- `file`: 上传的文件
- `type`: 上传类型（avatar/document）

**响应示例**:
```json
{
    "code": 200,
    "message": "文件上传成功",
    "data": {
        "file_name": "1_1721048200_abc123.jpg",
        "file_url": "/uploads/avatar/2025/07/1_1721048200_abc123.jpg",
        "file_size": 102400,
        "upload_type": "avatar",
        "uploaded_at": "2025-07-15 20:30:00"
    },
    "timestamp": 1721048200
}
```

## 注意事项

1. 所有需要认证的接口都需要在请求头中携带 `Authorization: Bearer {access_token}`
2. 所有时间字段都使用 `Y-m-d H:i:s` 格式
3. 分页参数 `page` 从 1 开始
4. 所有接口都支持 CORS 跨域请求
5. 错误响应格式统一为：`{"code": 错误码, "message": "错误信息", "timestamp": 时间戳}`
6. 成功响应格式统一为：`{"code": 200, "message": "成功信息", "data": 数据, "timestamp": 时间戳}`
7. 文件上传支持的格式：
   - 头像：JPEG, PNG, GIF, WebP（最大2MB）
   - 文档：PDF, DOC, DOCX（最大10MB）
8. 密码修改后会强制用户重新登录（撤销所有现有token）