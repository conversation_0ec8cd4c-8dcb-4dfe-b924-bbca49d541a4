!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).TXLivePlayer=t()}(this,(function(){"use strict";function e(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if("default"!==r&&!(r in e)){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var n=function(e){try{return!!e()}catch(e){return!0}},i=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})),o=i,a=Function.prototype,s=a.call,c=o&&a.bind.bind(s,s),u=o?c:function(e){return function(){return s.apply(e,arguments)}},l=u({}.isPrototypeOf),d=function(e){return e&&e.Math===Math&&e},p=d("object"==typeof globalThis&&globalThis)||d("object"==typeof window&&window)||d("object"==typeof self&&self)||d("object"==typeof t&&t)||function(){return this}()||t||Function("return this")(),f=i,h=Function.prototype,m=h.apply,v=h.call,y="object"==typeof Reflect&&Reflect.apply||(f?v.bind(m):function(){return v.apply(m,arguments)}),g=u,b=g({}.toString),C=g("".slice),S=function(e){return C(b(e),8,-1)},_=S,T=u,w=function(e){if("Function"===_(e))return T(e)},R="object"==typeof document&&document.all,P={all:R,IS_HTMLDDA:void 0===R&&void 0!==R},E=P.all,k=P.IS_HTMLDDA?function(e){return"function"==typeof e||e===E}:function(e){return"function"==typeof e},A={},x=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),L=i,I=Function.prototype.call,D=L?I.bind(I):function(){return I.apply(I,arguments)},M={},O={}.propertyIsEnumerable,N=Object.getOwnPropertyDescriptor,V=N&&!O.call({1:2},1);M.f=V?function(e){var t=N(this,e);return!!t&&t.enumerable}:O;var F,U,j=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},B=n,W=S,G=Object,Z=u("".split),Y=B((function(){return!G("z").propertyIsEnumerable(0)}))?function(e){return"String"===W(e)?Z(e,""):G(e)}:G,H=function(e){return null==e},X=H,J=TypeError,K=function(e){if(X(e))throw J("Can't call method on "+e);return e},Q=Y,q=K,z=function(e){return Q(q(e))},$=k,ee=P.all,te=P.IS_HTMLDDA?function(e){return"object"==typeof e?null!==e:$(e)||e===ee}:function(e){return"object"==typeof e?null!==e:$(e)},re={},ne=re,ie=p,oe=k,ae=function(e){return oe(e)?e:void 0},se=function(e,t){return arguments.length<2?ae(ne[e])||ae(ie[e]):ne[e]&&ne[e][t]||ie[e]&&ie[e][t]},ce="undefined"!=typeof navigator&&String(navigator.userAgent)||"",ue=p,le=ce,de=ue.process,pe=ue.Deno,fe=de&&de.versions||pe&&pe.version,he=fe&&fe.v8;he&&(U=(F=he.split("."))[0]>0&&F[0]<4?1:+(F[0]+F[1])),!U&&le&&(!(F=le.match(/Edge\/(\d+)/))||F[1]>=74)&&(F=le.match(/Chrome\/(\d+)/))&&(U=+F[1]);var me=U,ve=me,ye=n,ge=p.String,be=!!Object.getOwnPropertySymbols&&!ye((function(){var e=Symbol("symbol detection");return!ge(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&ve&&ve<41})),Ce=be&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Se=se,_e=k,Te=l,we=Object,Re=Ce?function(e){return"symbol"==typeof e}:function(e){var t=Se("Symbol");return _e(t)&&Te(t.prototype,we(e))},Pe=String,Ee=function(e){try{return Pe(e)}catch(e){return"Object"}},ke=k,Ae=Ee,xe=TypeError,Le=function(e){if(ke(e))return e;throw xe(Ae(e)+" is not a function")},Ie=Le,De=H,Me=function(e,t){var r=e[t];return De(r)?void 0:Ie(r)},Oe=D,Ne=k,Ve=te,Fe=TypeError,Ue={exports:{}},je=p,Be=Object.defineProperty,We=function(e,t){try{Be(je,e,{value:t,configurable:!0,writable:!0})}catch(r){je[e]=t}return t},Ge="__core-js_shared__",Ze=p[Ge]||We(Ge,{}),Ye=Ze;(Ue.exports=function(e,t){return Ye[e]||(Ye[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.32.1",mode:"pure",copyright:"漏 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.32.1/LICENSE",source:"https://github.com/zloirock/core-js"});var He=K,Xe=Object,Je=function(e){return Xe(He(e))},Ke=Je,Qe=u({}.hasOwnProperty),qe=Object.hasOwn||function(e,t){return Qe(Ke(e),t)},ze=u,$e=0,et=Math.random(),tt=ze(1..toString),rt=function(e){return"Symbol("+(void 0===e?"":e)+")_"+tt(++$e+et,36)},nt=p,it=Ue.exports,ot=qe,at=rt,st=be,ct=Ce,ut=nt.Symbol,lt=it("wks"),dt=ct?ut.for||ut:ut&&ut.withoutSetter||at,pt=function(e){return ot(lt,e)||(lt[e]=st&&ot(ut,e)?ut[e]:dt("Symbol."+e)),lt[e]},ft=D,ht=te,mt=Re,vt=Me,yt=function(e,t){var r,n;if("string"===t&&Ne(r=e.toString)&&!Ve(n=Oe(r,e)))return n;if(Ne(r=e.valueOf)&&!Ve(n=Oe(r,e)))return n;if("string"!==t&&Ne(r=e.toString)&&!Ve(n=Oe(r,e)))return n;throw Fe("Can't convert object to primitive value")},gt=TypeError,bt=pt("toPrimitive"),Ct=function(e,t){if(!ht(e)||mt(e))return e;var r,n=vt(e,bt);if(n){if(void 0===t&&(t="default"),r=ft(n,e,t),!ht(r)||mt(r))return r;throw gt("Can't convert object to primitive value")}return void 0===t&&(t="number"),yt(e,t)},St=Re,_t=function(e){var t=Ct(e,"string");return St(t)?t:t+""},Tt=te,wt=p.document,Rt=Tt(wt)&&Tt(wt.createElement),Pt=function(e){return Rt?wt.createElement(e):{}},Et=Pt,kt=!x&&!n((function(){return 7!==Object.defineProperty(Et("div"),"a",{get:function(){return 7}}).a})),At=x,xt=D,Lt=M,It=j,Dt=z,Mt=_t,Ot=qe,Nt=kt,Vt=Object.getOwnPropertyDescriptor;A.f=At?Vt:function(e,t){if(e=Dt(e),t=Mt(t),Nt)try{return Vt(e,t)}catch(e){}if(Ot(e,t))return It(!xt(Lt.f,e,t),e[t])};var Ft=n,Ut=k,jt=/#|\.prototype\./,Bt=function(e,t){var r=Gt[Wt(e)];return r===Yt||r!==Zt&&(Ut(t)?Ft(t):!!t)},Wt=Bt.normalize=function(e){return String(e).replace(jt,".").toLowerCase()},Gt=Bt.data={},Zt=Bt.NATIVE="N",Yt=Bt.POLYFILL="P",Ht=Bt,Xt=Le,Jt=i,Kt=w(w.bind),Qt=function(e,t){return Xt(e),void 0===t?e:Jt?Kt(e,t):function(){return e.apply(t,arguments)}},qt={},zt=x&&n((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),$t=te,er=String,tr=TypeError,rr=function(e){if($t(e))return e;throw tr(er(e)+" is not an object")},nr=x,ir=kt,or=zt,ar=rr,sr=_t,cr=TypeError,ur=Object.defineProperty,lr=Object.getOwnPropertyDescriptor,dr="enumerable",pr="configurable",fr="writable";qt.f=nr?or?function(e,t,r){if(ar(e),t=sr(t),ar(r),"function"==typeof e&&"prototype"===t&&"value"in r&&fr in r&&!r[fr]){var n=lr(e,t);n&&n[fr]&&(e[t]=r.value,r={configurable:pr in r?r[pr]:n[pr],enumerable:dr in r?r[dr]:n[dr],writable:!1})}return ur(e,t,r)}:ur:function(e,t,r){if(ar(e),t=sr(t),ar(r),ir)try{return ur(e,t,r)}catch(e){}if("get"in r||"set"in r)throw cr("Accessors not supported");return"value"in r&&(e[t]=r.value),e};var hr=qt,mr=j,vr=x?function(e,t,r){return hr.f(e,t,mr(1,r))}:function(e,t,r){return e[t]=r,e},yr=p,gr=y,br=w,Cr=k,Sr=A.f,_r=Ht,Tr=re,wr=Qt,Rr=vr,Pr=qe,Er=function(e){var t=function(r,n,i){if(this instanceof t){switch(arguments.length){case 0:return new e;case 1:return new e(r);case 2:return new e(r,n)}return new e(r,n,i)}return gr(e,this,arguments)};return t.prototype=e.prototype,t},kr=function(e,t){var r,n,i,o,a,s,c,u,l,d=e.target,p=e.global,f=e.stat,h=e.proto,m=p?yr:f?yr[d]:(yr[d]||{}).prototype,v=p?Tr:Tr[d]||Rr(Tr,d,{})[d],y=v.prototype;for(o in t)n=!(r=_r(p?o:d+(f?".":"#")+o,e.forced))&&m&&Pr(m,o),s=v[o],n&&(c=e.dontCallGetSet?(l=Sr(m,o))&&l.value:m[o]),a=n&&c?c:t[o],n&&typeof s==typeof a||(u=e.bind&&n?wr(a,yr):e.wrap&&n?Er(a):h&&Cr(a)?br(a):a,(e.sham||a&&a.sham||s&&s.sham)&&Rr(u,"sham",!0),Rr(v,o,u),h&&(Pr(Tr,i=d+"Prototype")||Rr(Tr,i,{}),Rr(Tr[i],o,a),e.real&&y&&(r||!y[o])&&Rr(y,o,a)))},Ar=u([].slice),xr=u,Lr=Le,Ir=te,Dr=qe,Mr=Ar,Or=i,Nr=Function,Vr=xr([].concat),Fr=xr([].join),Ur={},jr=Or?Nr.bind:function(e){var t=Lr(this),r=t.prototype,n=Mr(arguments,1),i=function(){var r=Vr(n,Mr(arguments));return this instanceof i?function(e,t,r){if(!Dr(Ur,t)){for(var n=[],i=0;i<t;i++)n[i]="a["+i+"]";Ur[t]=Nr("C,a","return new C("+Fr(n,",")+")")}return Ur[t](e,r)}(t,r.length,r):t.apply(e,r)};return Ir(r)&&(i.prototype=r),i},Br=jr;kr({target:"Function",proto:!0,forced:Function.bind!==Br},{bind:Br});var Wr=re,Gr=function(e){return Wr[e+"Prototype"]},Zr=Gr("Function").bind,Yr=l,Hr=Zr,Xr=Function.prototype,Jr=function(e){var t=e.bind;return e===Xr||Yr(Xr,e)&&t===Xr.bind?Hr:t},Kr=S,Qr=Array.isArray||function(e){return"Array"===Kr(e)},qr=Math.ceil,zr=Math.floor,$r=Math.trunc||function(e){var t=+e;return(t>0?zr:qr)(t)},en=function(e){var t=+e;return t!=t||0===t?0:$r(t)},tn=en,rn=Math.min,nn=function(e){return e>0?rn(tn(e),9007199254740991):0},on=nn,an=function(e){return on(e.length)},sn=TypeError,cn=function(e){if(e>9007199254740991)throw sn("Maximum allowed index exceeded");return e},un=_t,ln=qt,dn=j,pn=function(e,t,r){var n=un(t);n in e?ln.f(e,n,dn(0,r)):e[n]=r},fn={};fn[pt("toStringTag")]="z";var hn="[object z]"===String(fn),mn=hn,vn=k,yn=S,gn=pt("toStringTag"),bn=Object,Cn="Arguments"===yn(function(){return arguments}()),Sn=mn?yn:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=bn(e),gn))?r:Cn?yn(t):"Object"===(n=yn(t))&&vn(t.callee)?"Arguments":n},_n=k,Tn=Ze,wn=u(Function.toString);_n(Tn.inspectSource)||(Tn.inspectSource=function(e){return wn(e)});var Rn=Tn.inspectSource,Pn=u,En=n,kn=k,An=Sn,xn=Rn,Ln=function(){},In=[],Dn=se("Reflect","construct"),Mn=/^\s*(?:class|function)\b/,On=Pn(Mn.exec),Nn=!Mn.exec(Ln),Vn=function(e){if(!kn(e))return!1;try{return Dn(Ln,In,e),!0}catch(e){return!1}},Fn=function(e){if(!kn(e))return!1;switch(An(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Nn||!!On(Mn,xn(e))}catch(e){return!0}};Fn.sham=!0;var Un=!Dn||En((function(){var e;return Vn(Vn.call)||!Vn(Object)||!Vn((function(){e=!0}))||e}))?Fn:Vn,jn=Qr,Bn=Un,Wn=te,Gn=pt("species"),Zn=Array,Yn=function(e){var t;return jn(e)&&(t=e.constructor,(Bn(t)&&(t===Zn||jn(t.prototype))||Wn(t)&&null===(t=t[Gn]))&&(t=void 0)),void 0===t?Zn:t},Hn=function(e,t){return new(Yn(e))(0===t?0:t)},Xn=n,Jn=me,Kn=pt("species"),Qn=function(e){return Jn>=51||!Xn((function(){var t=[];return(t.constructor={})[Kn]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},qn=kr,zn=n,$n=Qr,ei=te,ti=Je,ri=an,ni=cn,ii=pn,oi=Hn,ai=Qn,si=me,ci=pt("isConcatSpreadable"),ui=si>=51||!zn((function(){var e=[];return e[ci]=!1,e.concat()[0]!==e})),li=function(e){if(!ei(e))return!1;var t=e[ci];return void 0!==t?!!t:$n(e)};qn({target:"Array",proto:!0,arity:1,forced:!ui||!ai("concat")},{concat:function(e){var t,r,n,i,o,a=ti(this),s=oi(a,0),c=0;for(t=-1,n=arguments.length;t<n;t++)if(li(o=-1===t?a:arguments[t]))for(i=ri(o),ni(c+i),r=0;r<i;r++,c++)r in o&&ii(s,c,o[r]);else ni(c+1),ii(s,c++,o);return s.length=c,s}});var di=Gr("Array").concat,pi=l,fi=di,hi=Array.prototype,mi=function(e){var t=e.concat;return e===hi||pi(hi,e)&&t===hi.concat?fi:t},vi=en,yi=Math.max,gi=Math.min,bi=function(e,t){var r=vi(e);return r<0?yi(r+t,0):gi(r,t)},Ci=z,Si=bi,_i=an,Ti=function(e){return function(t,r,n){var i,o=Ci(t),a=_i(o),s=Si(n,a);if(e&&r!=r){for(;a>s;)if((i=o[s++])!=i)return!0}else for(;a>s;s++)if((e||s in o)&&o[s]===r)return e||s||0;return!e&&-1}},wi={includes:Ti(!0),indexOf:Ti(!1)},Ri=wi.includes;kr({target:"Array",proto:!0,forced:n((function(){return!Array(1).includes()}))},{includes:function(e){return Ri(this,e,arguments.length>1?arguments[1]:void 0)}});var Pi=Gr("Array").includes,Ei=te,ki=S,Ai=pt("match"),xi=function(e){var t;return Ei(e)&&(void 0!==(t=e[Ai])?!!t:"RegExp"===ki(e))},Li=TypeError,Ii=function(e){if(xi(e))throw Li("The method doesn't accept regular expressions");return e},Di=Sn,Mi=String,Oi=function(e){if("Symbol"===Di(e))throw TypeError("Cannot convert a Symbol value to a string");return Mi(e)},Ni=pt("match"),Vi=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[Ni]=!1,"/./"[e](t)}catch(e){}}return!1},Fi=kr,Ui=Ii,ji=K,Bi=Oi,Wi=Vi,Gi=u("".indexOf);Fi({target:"String",proto:!0,forced:!Wi("includes")},{includes:function(e){return!!~Gi(Bi(ji(this)),Bi(Ui(e)),arguments.length>1?arguments[1]:void 0)}});var Zi,Yi,Hi,Xi=Gr("String").includes,Ji=l,Ki=Pi,Qi=Xi,qi=Array.prototype,zi=String.prototype,$i=function(e){var t=e.includes;return e===qi||Ji(qi,e)&&t===qi.includes?Ki:"string"==typeof e||e===zi||Ji(zi,e)&&t===zi.includes?Qi:t},eo={},to=k,ro=p.WeakMap,no=to(ro)&&/native code/.test(String(ro)),io=Ue.exports,oo=rt,ao=io("keys"),so=function(e){return ao[e]||(ao[e]=oo(e))},co={},uo=no,lo=p,po=te,fo=vr,ho=qe,mo=Ze,vo=so,yo=co,go="Object already initialized",bo=lo.TypeError,Co=lo.WeakMap;if(uo||mo.state){var So=mo.state||(mo.state=new Co);So.get=So.get,So.has=So.has,So.set=So.set,Zi=function(e,t){if(So.has(e))throw bo(go);return t.facade=e,So.set(e,t),t},Yi=function(e){return So.get(e)||{}},Hi=function(e){return So.has(e)}}else{var _o=vo("state");yo[_o]=!0,Zi=function(e,t){if(ho(e,_o))throw bo(go);return t.facade=e,fo(e,_o,t),t},Yi=function(e){return ho(e,_o)?e[_o]:{}},Hi=function(e){return ho(e,_o)}}var To={set:Zi,get:Yi,has:Hi,enforce:function(e){return Hi(e)?Yi(e):Zi(e,{})},getterFor:function(e){return function(t){var r;if(!po(t)||(r=Yi(t)).type!==e)throw bo("Incompatible receiver, "+e+" required");return r}}},wo=x,Ro=qe,Po=Function.prototype,Eo=wo&&Object.getOwnPropertyDescriptor,ko=Ro(Po,"name"),Ao={EXISTS:ko,PROPER:ko&&"something"===function(){}.name,CONFIGURABLE:ko&&(!wo||wo&&Eo(Po,"name").configurable)},xo={},Lo=qe,Io=z,Do=wi.indexOf,Mo=co,Oo=u([].push),No=function(e,t){var r,n=Io(e),i=0,o=[];for(r in n)!Lo(Mo,r)&&Lo(n,r)&&Oo(o,r);for(;t.length>i;)Lo(n,r=t[i++])&&(~Do(o,r)||Oo(o,r));return o},Vo=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Fo=No,Uo=Vo,jo=Object.keys||function(e){return Fo(e,Uo)},Bo=x,Wo=zt,Go=qt,Zo=rr,Yo=z,Ho=jo;xo.f=Bo&&!Wo?Object.defineProperties:function(e,t){Zo(e);for(var r,n=Yo(t),i=Ho(t),o=i.length,a=0;o>a;)Go.f(e,r=i[a++],n[r]);return e};var Xo,Jo=se("document","documentElement"),Ko=rr,Qo=xo,qo=Vo,zo=co,$o=Jo,ea=Pt,ta="prototype",ra="script",na=so("IE_PROTO"),ia=function(){},oa=function(e){return"<"+ra+">"+e+"</"+ra+">"},aa=function(e){e.write(oa("")),e.close();var t=e.parentWindow.Object;return e=null,t},sa=function(){try{Xo=new ActiveXObject("htmlfile")}catch(e){}var e,t,r;sa="undefined"!=typeof document?document.domain&&Xo?aa(Xo):(t=ea("iframe"),r="java"+ra+":",t.style.display="none",$o.appendChild(t),t.src=String(r),(e=t.contentWindow.document).open(),e.write(oa("document.F=Object")),e.close(),e.F):aa(Xo);for(var n=qo.length;n--;)delete sa[ta][qo[n]];return sa()};zo[na]=!0;var ca,ua,la,da=Object.create||function(e,t){var r;return null!==e?(ia[ta]=Ko(e),r=new ia,ia[ta]=null,r[na]=e):r=sa(),void 0===t?r:Qo.f(r,t)},pa=!n((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),fa=qe,ha=k,ma=Je,va=pa,ya=so("IE_PROTO"),ga=Object,ba=ga.prototype,Ca=va?ga.getPrototypeOf:function(e){var t=ma(e);if(fa(t,ya))return t[ya];var r=t.constructor;return ha(r)&&t instanceof r?r.prototype:t instanceof ga?ba:null},Sa=vr,_a=function(e,t,r,n){return n&&n.enumerable?e[t]=r:Sa(e,t,r),e},Ta=n,wa=k,Ra=te,Pa=da,Ea=Ca,ka=_a,Aa=pt("iterator"),xa=!1;[].keys&&("next"in(la=[].keys())?(ua=Ea(Ea(la)))!==Object.prototype&&(ca=ua):xa=!0);var La=!Ra(ca)||Ta((function(){var e={};return ca[Aa].call(e)!==e}));wa((ca=La?{}:Pa(ca))[Aa])||ka(ca,Aa,(function(){return this}));var Ia={IteratorPrototype:ca,BUGGY_SAFARI_ITERATORS:xa},Da=Sn,Ma=hn?{}.toString:function(){return"[object "+Da(this)+"]"},Oa=hn,Na=qt.f,Va=vr,Fa=qe,Ua=Ma,ja=pt("toStringTag"),Ba=function(e,t,r,n){if(e){var i=r?e:e.prototype;Fa(i,ja)||Na(i,ja,{configurable:!0,value:t}),n&&!Oa&&Va(i,"toString",Ua)}},Wa=Ia.IteratorPrototype,Ga=da,Za=j,Ya=Ba,Ha=eo,Xa=function(){return this},Ja=u,Ka=Le,Qa=k,qa=String,za=TypeError,$a=function(e,t,r){try{return Ja(Ka(Object.getOwnPropertyDescriptor(e,t)[r]))}catch(e){}},es=rr,ts=function(e){if("object"==typeof e||Qa(e))return e;throw za("Can't set "+qa(e)+" as a prototype")},rs=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=$a(Object.prototype,"__proto__","set"))(r,[]),t=r instanceof Array}catch(e){}return function(r,n){return es(r),ts(n),t?e(r,n):r.__proto__=n,r}}():void 0),ns=kr,is=D,os=Ao,as=function(e,t,r,n){var i=t+" Iterator";return e.prototype=Ga(Wa,{next:Za(+!n,r)}),Ya(e,i,!1,!0),Ha[i]=Xa,e},ss=Ca,cs=Ba,us=_a,ls=eo,ds=Ia,ps=os.PROPER,fs=ds.BUGGY_SAFARI_ITERATORS,hs=pt("iterator"),ms="keys",vs="values",ys="entries",gs=function(){return this},bs=function(e,t,r,n,i,o,a){as(r,t,n);var s,c,u,l=function(e){if(e===i&&m)return m;if(!fs&&e in f)return f[e];switch(e){case ms:case vs:case ys:return function(){return new r(this,e)}}return function(){return new r(this)}},d=t+" Iterator",p=!1,f=e.prototype,h=f[hs]||f["@@iterator"]||i&&f[i],m=!fs&&h||l(i),v="Array"===t&&f.entries||h;if(v&&(s=ss(v.call(new e)))!==Object.prototype&&s.next&&(cs(s,d,!0,!0),ls[d]=gs),ps&&i===vs&&h&&h.name!==vs&&(p=!0,m=function(){return is(h,this)}),i)if(c={values:l(vs),keys:o?m:l(ms),entries:l(ys)},a)for(u in c)(fs||p||!(u in f))&&us(f,u,c[u]);else ns({target:t,proto:!0,forced:fs||p},c);return a&&f[hs]!==m&&us(f,hs,m,{name:i}),ls[t]=m,c},Cs=function(e,t){return{value:e,done:t}},Ss=z,_s=function(){},Ts=eo,ws=To,Rs=(qt.f,bs),Ps=Cs,Es="Array Iterator",ks=ws.set,As=ws.getterFor(Es);Rs(Array,"Array",(function(e,t){ks(this,{type:Es,target:Ss(e),index:0,kind:t})}),(function(){var e=As(this),t=e.target,r=e.kind,n=e.index++;if(!t||n>=t.length)return e.target=void 0,Ps(void 0,!0);switch(r){case"keys":return Ps(n,!1);case"values":return Ps(t[n],!1)}return Ps([n,t[n]],!1)}),"values");Ts.Arguments=Ts.Array;_s(),_s(),_s();var xs={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Ls=p,Is=Sn,Ds=vr,Ms=eo,Os=pt("toStringTag");for(var Ns in xs){var Vs=Ls[Ns],Fs=Vs&&Vs.prototype;Fs&&Is(Fs)!==Os&&Ds(Fs,Os,Ns),Ms[Ns]=Ms.Array}var Us=Qt,js=Y,Bs=Je,Ws=an,Gs=Hn,Zs=u([].push),Ys=function(e){var t=1===e,r=2===e,n=3===e,i=4===e,o=6===e,a=7===e,s=5===e||o;return function(c,u,l,d){for(var p,f,h=Bs(c),m=js(h),v=Us(u,l),y=Ws(m),g=0,b=d||Gs,C=t?b(c,y):r||a?b(c,0):void 0;y>g;g++)if((s||g in m)&&(f=v(p=m[g],g,h),e))if(t)C[g]=f;else if(f)switch(e){case 3:return!0;case 5:return p;case 6:return g;case 2:Zs(C,p)}else switch(e){case 4:return!1;case 7:Zs(C,p)}return o?-1:n||i?i:C}},Hs={forEach:Ys(0),map:Ys(1),filter:Ys(2),some:Ys(3),every:Ys(4),find:Ys(5),findIndex:Ys(6),filterReject:Ys(7)},Xs=n,Js=function(e,t){var r=[][e];return!!r&&Xs((function(){r.call(null,t||function(){return 1},1)}))},Ks=Hs.forEach,Qs=Js("forEach")?[].forEach:function(e){return Ks(this,e,arguments.length>1?arguments[1]:void 0)};kr({target:"Array",proto:!0,forced:[].forEach!==Qs},{forEach:Qs});var qs=Gr("Array").forEach,zs=Sn,$s=qe,ec=l,tc=qs,rc=Array.prototype,nc={DOMTokenList:!0,NodeList:!0},ic=function(e){var t=e.forEach;return e===rc||ec(rc,e)&&t===rc.forEach||$s(nc,zs(e))?tc:t},oc="function"==typeof Bun&&Bun&&"string"==typeof Bun.version,ac=TypeError,sc=function(e,t){if(e<t)throw ac("Not enough arguments");return e},cc=p,uc=y,lc=k,dc=oc,pc=ce,fc=Ar,hc=sc,mc=cc.Function,vc=/MSIE .\./.test(pc)||dc&&function(){var e=cc.Bun.version.split(".");return e.length<3||"0"===e[0]&&(e[1]<3||"3"===e[1]&&"0"===e[2])}(),yc=function(e,t){var r=t?2:1;return vc?function(n,i){var o=hc(arguments.length,1)>r,a=lc(n)?n:mc(n),s=o?fc(arguments,r):[],c=o?function(){uc(a,this,s)}:a;return t?e(c,i):e(c)}:e},gc=kr,bc=p,Cc=yc(bc.setInterval,!0);gc({global:!0,bind:!0,forced:bc.setInterval!==Cc},{setInterval:Cc});var Sc=kr,_c=p,Tc=yc(_c.setTimeout,!0);Sc({global:!0,bind:!0,forced:_c.setTimeout!==Tc},{setTimeout:Tc});var wc=re.setInterval,Rc=re.setTimeout,Pc=kr,Ec=wi.indexOf,kc=Js,Ac=w([].indexOf),xc=!!Ac&&1/Ac([1],1,-0)<0;Pc({target:"Array",proto:!0,forced:xc||!kc("indexOf")},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return xc?Ac(this,e,t)||0:Ec(this,e,t)}});var Lc=Gr("Array").indexOf,Ic=l,Dc=Lc,Mc=Array.prototype,Oc=function(e){var t=e.indexOf;return e===Mc||Ic(Mc,e)&&t===Mc.indexOf?Dc:t},Nc=kr,Vc=w,Fc=nn,Uc=Oi,jc=Ii,Bc=K,Wc=Vi,Gc=Vc("".startsWith),Zc=Vc("".slice),Yc=Math.min;Nc({target:"String",proto:!0,forced:!Wc("startsWith")},{startsWith:function(e){var t=Uc(Bc(this));jc(e);var r=Fc(Yc(arguments.length>1?arguments[1]:void 0,t.length)),n=Uc(e);return Gc?Gc(t,n,r):Zc(t,r,r+n.length)===n}});var Hc=Gr("String").startsWith,Xc=l,Jc=Hc,Kc=String.prototype,Qc=function(e){var t=e.startsWith;return"string"==typeof e||e===Kc||Xc(Kc,e)&&t===Kc.startsWith?Jc:t},qc=Hs.filter;kr({target:"Array",proto:!0,forced:!Qn("filter")},{filter:function(e){return qc(this,e,arguments.length>1?arguments[1]:void 0)}});var zc=Gr("Array").filter,$c=l,eu=zc,tu=Array.prototype,ru=function(e){var t=e.filter;return e===tu||$c(tu,e)&&t===tu.filter?eu:t},nu=function(e,t){return nu=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},nu(e,t)};function iu(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}nu(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var ou=function(){return ou=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},ou.apply(this,arguments)};function au(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]])}return r}function su(e,t,r,n){return new(r||(r=Promise))((function(i,o){function a(e){try{c(n.next(e))}catch(e){o(e)}}function s(e){try{c(n.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((n=n.apply(e,t||[])).next())}))}function cu(e,t){var r,n,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(i=2&s[0]?n.return:s[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){a.label=s[1];break}if(6===s[0]&&a.label<i[1]){a.label=i[1],i=s;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(s);break}i[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}function uu(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function lu(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a}function du(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError;let pu=!0,fu=!0;function hu(e,t,r){const n=e.match(t);return n&&n.length>=r&&parseInt(n[r],10)}function mu(e,t,r){if(!e.RTCPeerConnection)return;const n=e.RTCPeerConnection.prototype,i=n.addEventListener;n.addEventListener=function(e,n){if(e!==t)return i.apply(this,arguments);const o=e=>{const t=r(e);t&&(n.handleEvent?n.handleEvent(t):n(t))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(n,o),i.apply(this,[e,o])};const o=n.removeEventListener;n.removeEventListener=function(e,r){if(e!==t||!this._eventMap||!this._eventMap[t])return o.apply(this,arguments);if(!this._eventMap[t].has(r))return o.apply(this,arguments);const n=this._eventMap[t].get(r);return this._eventMap[t].delete(r),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,o.apply(this,[e,n])},Object.defineProperty(n,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function vu(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(pu=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function yu(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(fu=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function gu(){if("object"==typeof window){if(pu)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}}function bu(e,t){fu&&console.warn(e+" is deprecated, please use "+t+" instead.")}function Cu(e){return"[object Object]"===Object.prototype.toString.call(e)}function Su(e){return Cu(e)?Object.keys(e).reduce((function(t,r){const n=Cu(e[r]),i=n?Su(e[r]):e[r],o=n&&!Object.keys(i).length;return void 0===i||o?t:Object.assign(t,{[r]:i})}),{}):e}function _u(e,t,r){t&&!r.has(t.id)&&(r.set(t.id,t),Object.keys(t).forEach((n=>{n.endsWith("Id")?_u(e,e.get(t[n]),r):n.endsWith("Ids")&&t[n].forEach((t=>{_u(e,e.get(t),r)}))})))}function Tu(e,t,r){const n=r?"outbound-rtp":"inbound-rtp",i=new Map;if(null===t)return i;const o=[];return e.forEach((e=>{"track"===e.type&&e.trackIdentifier===t.id&&o.push(e)})),o.forEach((t=>{e.forEach((r=>{r.type===n&&r.trackId===t.id&&_u(e,r,i)}))})),i}const wu=gu;function Ru(e,t){const r=e&&e.navigator;if(!r.mediaDevices)return;const n=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;const t={};return Object.keys(e).forEach((r=>{if("require"===r||"advanced"===r||"mediaSource"===r)return;const n="object"==typeof e[r]?e[r]:{ideal:e[r]};void 0!==n.exact&&"number"==typeof n.exact&&(n.min=n.max=n.exact);const i=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==n.ideal){t.optional=t.optional||[];let e={};"number"==typeof n.ideal?(e[i("min",r)]=n.ideal,t.optional.push(e),e={},e[i("max",r)]=n.ideal,t.optional.push(e)):(e[i("",r)]=n.ideal,t.optional.push(e))}void 0!==n.exact&&"number"!=typeof n.exact?(t.mandatory=t.mandatory||{},t.mandatory[i("",r)]=n.exact):["min","max"].forEach((e=>{void 0!==n[e]&&(t.mandatory=t.mandatory||{},t.mandatory[i(e,r)]=n[e])}))})),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},i=function(e,i){if(t.version>=61)return i(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){const t=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])};t((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),t(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=n(e.audio)}if(e&&"object"==typeof e.video){let o=e.video.facingMode;o=o&&("object"==typeof o?o:{ideal:o});const a=t.version<66;if(o&&("user"===o.exact||"environment"===o.exact||"user"===o.ideal||"environment"===o.ideal)&&(!r.mediaDevices.getSupportedConstraints||!r.mediaDevices.getSupportedConstraints().facingMode||a)){let t;if(delete e.video.facingMode,"environment"===o.exact||"environment"===o.ideal?t=["back","rear"]:"user"!==o.exact&&"user"!==o.ideal||(t=["front"]),t)return r.mediaDevices.enumerateDevices().then((r=>{let a=(r=r.filter((e=>"videoinput"===e.kind))).find((e=>t.some((t=>e.label.toLowerCase().includes(t)))));return!a&&r.length&&t.includes("back")&&(a=r[r.length-1]),a&&(e.video.deviceId=o.exact?{exact:a.deviceId}:{ideal:a.deviceId}),e.video=n(e.video),wu("chrome: "+JSON.stringify(e)),i(e)}))}e.video=n(e.video)}return wu("chrome: "+JSON.stringify(e)),i(e)},o=function(e){return t.version>=64?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(r.getUserMedia=function(e,t,n){i(e,(e=>{r.webkitGetUserMedia(e,t,(e=>{n&&n(o(e))}))}))}.bind(r),r.mediaDevices.getUserMedia){const e=r.mediaDevices.getUserMedia.bind(r.mediaDevices);r.mediaDevices.getUserMedia=function(t){return i(t,(t=>e(t).then((e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach((e=>{e.stop()})),new DOMException("","NotFoundError");return e}),(e=>Promise.reject(o(e))))))}}}function Pu(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function Eu(e){if("object"==typeof e&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",(r=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((e=>e.track&&e.track.id===r.track.id)):{track:r.track};const i=new Event("track");i.track=r.track,i.receiver=n,i.transceiver={receiver:n},i.streams=[t.stream],this.dispatchEvent(i)})),t.stream.getTracks().forEach((r=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((e=>e.track&&e.track.id===r.id)):{track:r};const i=new Event("track");i.track=r,i.receiver=n,i.transceiver={receiver:n},i.streams=[t.stream],this.dispatchEvent(i)}))},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}else mu(e,"track",(e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e)))}function ku(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const r=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){let i=r.apply(this,arguments);return i||(i=t(this,e),this._senders.push(i)),i};const n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){n.apply(this,arguments);const t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],r.apply(this,[e]),e.getTracks().forEach((e=>{this._senders.push(t(this,e))}))};const n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],n.apply(this,[e]),e.getTracks().forEach((e=>{const t=this._senders.find((t=>t.track===e));t&&this._senders.splice(this._senders.indexOf(t),1)}))}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function Au(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,r,n]=arguments;if(arguments.length>0&&"function"==typeof e)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof e))return t.apply(this,[]);const i=function(e){const t={};return e.result().forEach((e=>{const r={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach((t=>{r[t]=e.stat(t)})),t[r.id]=r})),t},o=function(e){return new Map(Object.keys(e).map((t=>[t,e[t]])))};if(arguments.length>=2){const n=function(e){r(o(i(e)))};return t.apply(this,[n,e])}return new Promise(((e,r)=>{t.apply(this,[function(t){e(o(i(t)))},r])})).then(r,n)}}function xu(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e});const r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=r.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){const e=this;return this._pc.getStats().then((t=>Tu(t,e.track,!0)))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e}),mu(e,"track",(e=>(e.receiver._pc=e.srcElement,e))),e.RTCRtpReceiver.prototype.getStats=function(){const e=this;return this._pc.getStats().then((t=>Tu(t,e.track,!1)))}}if(!("getStats"in e.RTCRtpSender.prototype)||!("getStats"in e.RTCRtpReceiver.prototype))return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){const e=arguments[0];let t,r,n;return this.getSenders().forEach((r=>{r.track===e&&(t?n=!0:t=r)})),this.getReceivers().forEach((t=>(t.track===e&&(r?n=!0:r=t),t.track===e))),n||t&&r?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):t?t.getStats():r?r.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function Lu(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map((e=>this._shimmedLocalStreams[e][0]))};const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,r){if(!r)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const n=t.apply(this,arguments);return this._shimmedLocalStreams[r.id]?-1===this._shimmedLocalStreams[r.id].indexOf(n)&&this._shimmedLocalStreams[r.id].push(n):this._shimmedLocalStreams[r.id]=[r,n],n};const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach((e=>{if(this.getSenders().find((t=>t.track===e)))throw new DOMException("Track already exists.","InvalidAccessError")}));const t=this.getSenders();r.apply(this,arguments);const n=this.getSenders().filter((e=>-1===t.indexOf(e)));this._shimmedLocalStreams[e.id]=[e].concat(n)};const n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],n.apply(this,arguments)};const i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach((t=>{const r=this._shimmedLocalStreams[t].indexOf(e);-1!==r&&this._shimmedLocalStreams[t].splice(r,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]})),i.apply(this,arguments)}}function Iu(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return Lu(e);const r=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){const e=r.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map((e=>this._reverseStreams[e.id]))};const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach((e=>{if(this.getSenders().find((t=>t.track===e)))throw new DOMException("Track already exists.","InvalidAccessError")})),!this._reverseStreams[t.id]){const r=new e.MediaStream(t.getTracks());this._streams[t.id]=r,this._reverseStreams[r.id]=t,t=r}n.apply(this,[t])};const i=e.RTCPeerConnection.prototype.removeStream;function o(e,t){let r=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((t=>{const n=e._reverseStreams[t],i=e._streams[n.id];r=r.replace(new RegExp(i.id,"g"),n.id)})),new RTCSessionDescription({type:t.type,sdp:r})}e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,r){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const n=[].slice.call(arguments,1);if(1!==n.length||!n[0].getTracks().find((e=>e===t)))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find((e=>e.track===t)))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const i=this._streams[r.id];if(i)i.addTrack(t),Promise.resolve().then((()=>{this.dispatchEvent(new Event("negotiationneeded"))}));else{const n=new e.MediaStream([t]);this._streams[r.id]=n,this._reverseStreams[n.id]=r,this.addStream(n)}return this.getSenders().find((e=>e.track===t))},["createOffer","createAnswer"].forEach((function(t){const r=e.RTCPeerConnection.prototype[t],n={[t](){const e=arguments;return arguments.length&&"function"==typeof arguments[0]?r.apply(this,[t=>{const r=o(this,t);e[0].apply(null,[r])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):r.apply(this,arguments).then((e=>o(this,e)))}};e.RTCPeerConnection.prototype[t]=n[t]}));const a=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=function(e,t){let r=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((t=>{const n=e._reverseStreams[t],i=e._streams[n.id];r=r.replace(new RegExp(n.id,"g"),i.id)})),new RTCSessionDescription({type:t.type,sdp:r})}(this,arguments[0]),a.apply(this,arguments)):a.apply(this,arguments)};const s=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){const e=s.get.apply(this);return""===e.type?e:o(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(e._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");let t;this._streams=this._streams||{},Object.keys(this._streams).forEach((r=>{this._streams[r].getTracks().find((t=>e.track===t))&&(t=this._streams[r])})),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function Du(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const r=e.RTCPeerConnection.prototype[t],n={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=n[t]}))}function Mu(e,t){mu(e,"negotiationneeded",(e=>{const r=e.target;if(!(t.version<72||r.getConfiguration&&"plan-b"===r.getConfiguration().sdpSemantics)||"stable"===r.signalingState)return e}))}var Ou=Object.freeze({__proto__:null,shimMediaStream:Pu,shimOnTrack:Eu,shimGetSendersWithDtmf:ku,shimGetStats:Au,shimSenderReceiverGetStats:xu,shimAddTrackRemoveTrackWithNative:Lu,shimAddTrackRemoveTrack:Iu,shimPeerConnection:Du,fixNegotiationNeeded:Mu,shimGetUserMedia:Ru,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&("function"==typeof t?e.navigator.mediaDevices.getDisplayMedia=function(r){return t(r).then((t=>{const n=r.video&&r.video.width,i=r.video&&r.video.height,o=r.video&&r.video.frameRate;return r.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:t,maxFrameRate:o||3}},n&&(r.video.mandatory.maxWidth=n),i&&(r.video.mandatory.maxHeight=i),e.navigator.mediaDevices.getUserMedia(r)}))}:console.error("shimGetDisplayMedia: getSourceId argument is not a function"))}});function Nu(e,t){const r=e&&e.navigator,n=e&&e.MediaStreamTrack;if(r.getUserMedia=function(e,t,n){bu("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),r.mediaDevices.getUserMedia(e).then(t,n)},!(t.version>55&&"autoGainControl"in r.mediaDevices.getSupportedConstraints())){const e=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])},t=r.mediaDevices.getUserMedia.bind(r.mediaDevices);if(r.mediaDevices.getUserMedia=function(r){return"object"==typeof r&&"object"==typeof r.audio&&(r=JSON.parse(JSON.stringify(r)),e(r.audio,"autoGainControl","mozAutoGainControl"),e(r.audio,"noiseSuppression","mozNoiseSuppression")),t(r)},n&&n.prototype.getSettings){const t=n.prototype.getSettings;n.prototype.getSettings=function(){const r=t.apply(this,arguments);return e(r,"mozAutoGainControl","autoGainControl"),e(r,"mozNoiseSuppression","noiseSuppression"),r}}if(n&&n.prototype.applyConstraints){const t=n.prototype.applyConstraints;n.prototype.applyConstraints=function(r){return"audio"===this.kind&&"object"==typeof r&&(r=JSON.parse(JSON.stringify(r)),e(r,"autoGainControl","mozAutoGainControl"),e(r,"noiseSuppression","mozNoiseSuppression")),t.apply(this,[r])}}}}function Vu(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Fu(e,t){if("object"!=typeof e||!e.RTCPeerConnection&&!e.mozRTCPeerConnection)return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const r=e.RTCPeerConnection.prototype[t],n={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=n[t]}));const r={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},n=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,i,o]=arguments;return n.apply(this,[e||null]).then((e=>{if(t.version<53&&!i)try{e.forEach((e=>{e.type=r[e.type]||e.type}))}catch(t){if("TypeError"!==t.name)throw t;e.forEach(((t,n)=>{e.set(n,Object.assign({},t,{type:r[t.type]||t.type}))}))}return e})).then(i,o)}}function Uu(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e});const r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=r.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function ju(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e}),mu(e,"track",(e=>(e.receiver._pc=e.srcElement,e))),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function Bu(e){e.RTCPeerConnection&&!("removeStream"in e.RTCPeerConnection.prototype)&&(e.RTCPeerConnection.prototype.removeStream=function(e){bu("removeStream","removeTrack"),this.getSenders().forEach((t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)}))})}function Wu(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function Gu(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let e=arguments[1]&&arguments[1].sendEncodings;void 0===e&&(e=[]),e=[...e];const r=e.length>0;r&&e.forEach((e=>{if("rid"in e){if(!/^[a-z0-9]{0,16}$/i.test(e.rid))throw new TypeError("Invalid RID value provided.")}if("scaleResolutionDownBy"in e&&!(parseFloat(e.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(parseFloat(e.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")}));const n=t.apply(this,arguments);if(r){const{sender:t}=n,r=t.getParameters();(!("encodings"in r)||1===r.encodings.length&&0===Object.keys(r.encodings[0]).length)&&(r.encodings=e,t.sendEncodings=e,this.setParametersPromises.push(t.setParameters(r).then((()=>{delete t.sendEncodings})).catch((()=>{delete t.sendEncodings}))))}return n})}function Zu(e){if("object"!=typeof e||!e.RTCRtpSender)return;const t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){const e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})}function Yu(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>t.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):t.apply(this,arguments)}}function Hu(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>t.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):t.apply(this,arguments)}}var Xu=Object.freeze({__proto__:null,shimOnTrack:Vu,shimPeerConnection:Fu,shimSenderGetStats:Uu,shimReceiverGetStats:ju,shimRemoveStream:Bu,shimRTCDataChannel:Wu,shimAddTransceiver:Gu,shimGetParameters:Zu,shimCreateOffer:Yu,shimCreateAnswer:Hu,shimGetUserMedia:Nu,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(r){if(!r||!r.video){const e=new DOMException("getDisplayMedia without video constraints is undefined");return e.name="NotFoundError",e.code=8,Promise.reject(e)}return!0===r.video?r.video={mediaSource:t}:r.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(r)})}});function Ju(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach((r=>t.call(this,r,e))),e.getVideoTracks().forEach((r=>t.call(this,r,e)))},e.RTCPeerConnection.prototype.addTrack=function(e,...r){return r&&r.forEach((e=>{this._localStreams?this._localStreams.includes(e)||this._localStreams.push(e):this._localStreams=[e]})),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);const t=this._localStreams.indexOf(e);if(-1===t)return;this._localStreams.splice(t,1);const r=e.getTracks();this.getSenders().forEach((e=>{r.includes(e.track)&&this.removeTrack(e)}))})}}function Ku(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach((e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);const t=new Event("addstream");t.stream=e,this.dispatchEvent(t)}))})}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach((t=>{if(e._remoteStreams||(e._remoteStreams=[]),e._remoteStreams.indexOf(t)>=0)return;e._remoteStreams.push(t);const r=new Event("addstream");r.stream=t,e.dispatchEvent(r)}))}),t.apply(e,arguments)}}}function Qu(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype,r=t.createOffer,n=t.createAnswer,i=t.setLocalDescription,o=t.setRemoteDescription,a=t.addIceCandidate;t.createOffer=function(e,t){const n=arguments.length>=2?arguments[2]:arguments[0],i=r.apply(this,[n]);return t?(i.then(e,t),Promise.resolve()):i},t.createAnswer=function(e,t){const r=arguments.length>=2?arguments[2]:arguments[0],i=n.apply(this,[r]);return t?(i.then(e,t),Promise.resolve()):i};let s=function(e,t,r){const n=i.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n};t.setLocalDescription=s,s=function(e,t,r){const n=o.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.setRemoteDescription=s,s=function(e,t,r){const n=a.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.addIceCandidate=s}function qu(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const e=t.mediaDevices,r=e.getUserMedia.bind(e);t.mediaDevices.getUserMedia=e=>r(zu(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,r,n){t.mediaDevices.getUserMedia(e).then(r,n)}.bind(t))}function zu(e){return e&&void 0!==e.video?Object.assign({},e,{video:Su(e.video)}):e}function $u(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,r){if(e&&e.iceServers){const t=[];for(let r=0;r<e.iceServers.length;r++){let n=e.iceServers[r];void 0===n.urls&&n.url?(bu("RTCIceServer.url","RTCIceServer.urls"),n=JSON.parse(JSON.stringify(n)),n.urls=n.url,delete n.url,t.push(n)):t.push(e.iceServers[r])}e.iceServers=t}return new t(e,r)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function el(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function tl(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);const t=this.getTransceivers().find((e=>"audio"===e.receiver.track.kind));!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio",{direction:"recvonly"}),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);const r=this.getTransceivers().find((e=>"video"===e.receiver.track.kind));!1===e.offerToReceiveVideo&&r?"sendrecv"===r.direction?r.setDirection?r.setDirection("sendonly"):r.direction="sendonly":"recvonly"===r.direction&&(r.setDirection?r.setDirection("inactive"):r.direction="inactive"):!0!==e.offerToReceiveVideo||r||this.addTransceiver("video",{direction:"recvonly"})}return t.apply(this,arguments)}}function rl(e){"object"!=typeof e||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}var nl=Object.freeze({__proto__:null,shimLocalStreamsAPI:Ju,shimRemoteStreamsAPI:Ku,shimCallbacksAPI:Qu,shimGetUserMedia:qu,shimConstraints:zu,shimRTCIceServerUrls:$u,shimTrackEventTransceiver:el,shimCreateOfferLegacy:tl,shimAudioContext:rl}),il={exports:{}};!function(e){const t={generateIdentifier:function(){return Math.random().toString(36).substring(2,12)}};t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map((e=>e.trim()))},t.splitSections=function(e){return e.split("\nm=").map(((e,t)=>(t>0?"m="+e:e).trim()+"\r\n"))},t.getDescription=function(e){const r=t.splitSections(e);return r&&r[0]},t.getMediaSections=function(e){const r=t.splitSections(e);return r.shift(),r},t.matchPrefix=function(e,r){return t.splitLines(e).filter((e=>0===e.indexOf(r)))},t.parseCandidate=function(e){let t;t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" ");const r={foundation:t[0],component:{1:"rtp",2:"rtcp"}[t[1]]||t[1],protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]};for(let e=8;e<t.length;e+=2)switch(t[e]){case"raddr":r.relatedAddress=t[e+1];break;case"rport":r.relatedPort=parseInt(t[e+1],10);break;case"tcptype":r.tcpType=t[e+1];break;case"ufrag":r.ufrag=t[e+1],r.usernameFragment=t[e+1];break;default:void 0===r[t[e]]&&(r[t[e]]=t[e+1])}return r},t.writeCandidate=function(e){const t=[];t.push(e.foundation);const r=e.component;"rtp"===r?t.push(1):"rtcp"===r?t.push(2):t.push(r),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);const n=e.type;return t.push("typ"),t.push(n),"host"!==n&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substring(14).split(" ")},t.parseRtpMap=function(e){let t=e.substring(9).split(" ");const r={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),r.name=t[0],r.clockRate=parseInt(t[1],10),r.channels=3===t.length?parseInt(t[2],10):1,r.numChannels=r.channels,r},t.writeRtpMap=function(e){let t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);const r=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==r?"/"+r:"")+"\r\n"},t.parseExtmap=function(e){const t=e.substring(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1],attributes:t.slice(2).join(" ")}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+(e.attributes?" "+e.attributes:"")+"\r\n"},t.parseFmtp=function(e){const t={};let r;const n=e.substring(e.indexOf(" ")+1).split(";");for(let e=0;e<n.length;e++)r=n[e].trim().split("="),t[r[0].trim()]=r[1];return t},t.writeFmtp=function(e){let t="",r=e.payloadType;if(void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){const n=[];Object.keys(e.parameters).forEach((t=>{void 0!==e.parameters[t]?n.push(t+"="+e.parameters[t]):n.push(t)})),t+="a=fmtp:"+r+" "+n.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){const t=e.substring(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){let t="",r=e.payloadType;return void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach((e=>{t+="a=rtcp-fb:"+r+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"})),t},t.parseSsrcMedia=function(e){const t=e.indexOf(" "),r={ssrc:parseInt(e.substring(7,t),10)},n=e.indexOf(":",t);return n>-1?(r.attribute=e.substring(t+1,n),r.value=e.substring(n+1)):r.attribute=e.substring(t+1),r},t.parseSsrcGroup=function(e){const t=e.substring(13).split(" ");return{semantics:t.shift(),ssrcs:t.map((e=>parseInt(e,10)))}},t.getMid=function(e){const r=t.matchPrefix(e,"a=mid:")[0];if(r)return r.substring(6)},t.parseFingerprint=function(e){const t=e.substring(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1].toUpperCase()}},t.getDtlsParameters=function(e,r){return{role:"auto",fingerprints:t.matchPrefix(e+r,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){let r="a=setup:"+t+"\r\n";return e.fingerprints.forEach((e=>{r+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"})),r},t.parseCryptoLine=function(e){const t=e.substring(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},t.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;const t=e.substring(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,r){return t.matchPrefix(e+r,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(e,r){const n=t.matchPrefix(e+r,"a=ice-ufrag:")[0],i=t.matchPrefix(e+r,"a=ice-pwd:")[0];return n&&i?{usernameFragment:n.substring(12),password:i.substring(10)}:null},t.writeIceParameters=function(e){let t="a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n";return e.iceLite&&(t+="a=ice-lite\r\n"),t},t.parseRtpParameters=function(e){const r={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},n=t.splitLines(e)[0].split(" ");r.profile=n[2];for(let i=3;i<n.length;i++){const o=n[i],a=t.matchPrefix(e,"a=rtpmap:"+o+" ")[0];if(a){const n=t.parseRtpMap(a),i=t.matchPrefix(e,"a=fmtp:"+o+" ");switch(n.parameters=i.length?t.parseFmtp(i[0]):{},n.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+o+" ").map(t.parseRtcpFb),r.codecs.push(n),n.name.toUpperCase()){case"RED":case"ULPFEC":r.fecMechanisms.push(n.name.toUpperCase())}}}t.matchPrefix(e,"a=extmap:").forEach((e=>{r.headerExtensions.push(t.parseExtmap(e))}));const i=t.matchPrefix(e,"a=rtcp-fb:* ").map(t.parseRtcpFb);return r.codecs.forEach((e=>{i.forEach((t=>{e.rtcpFeedback.find((e=>e.type===t.type&&e.parameter===t.parameter))||e.rtcpFeedback.push(t)}))})),r},t.writeRtpDescription=function(e,r){let n="";n+="m="+e+" ",n+=r.codecs.length>0?"9":"0",n+=" "+(r.profile||"UDP/TLS/RTP/SAVPF")+" ",n+=r.codecs.map((e=>void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType)).join(" ")+"\r\n",n+="c=IN IP4 0.0.0.0\r\n",n+="a=rtcp:9 IN IP4 0.0.0.0\r\n",r.codecs.forEach((e=>{n+=t.writeRtpMap(e),n+=t.writeFmtp(e),n+=t.writeRtcpFb(e)}));let i=0;return r.codecs.forEach((e=>{e.maxptime>i&&(i=e.maxptime)})),i>0&&(n+="a=maxptime:"+i+"\r\n"),r.headerExtensions&&r.headerExtensions.forEach((e=>{n+=t.writeExtmap(e)})),n},t.parseRtpEncodingParameters=function(e){const r=[],n=t.parseRtpParameters(e),i=-1!==n.fecMechanisms.indexOf("RED"),o=-1!==n.fecMechanisms.indexOf("ULPFEC"),a=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"cname"===e.attribute)),s=a.length>0&&a[0].ssrc;let c;const u=t.matchPrefix(e,"a=ssrc-group:FID").map((e=>e.substring(17).split(" ").map((e=>parseInt(e,10)))));u.length>0&&u[0].length>1&&u[0][0]===s&&(c=u[0][1]),n.codecs.forEach((e=>{if("RTX"===e.name.toUpperCase()&&e.parameters.apt){let t={ssrc:s,codecPayloadType:parseInt(e.parameters.apt,10)};s&&c&&(t.rtx={ssrc:c}),r.push(t),i&&(t=JSON.parse(JSON.stringify(t)),t.fec={ssrc:s,mechanism:o?"red+ulpfec":"red"},r.push(t))}})),0===r.length&&s&&r.push({ssrc:s});let l=t.matchPrefix(e,"b=");return l.length&&(l=0===l[0].indexOf("b=TIAS:")?parseInt(l[0].substring(7),10):0===l[0].indexOf("b=AS:")?1e3*parseInt(l[0].substring(5),10)*.95-16e3:void 0,r.forEach((e=>{e.maxBitrate=l}))),r},t.parseRtcpParameters=function(e){const r={},n=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"cname"===e.attribute))[0];n&&(r.cname=n.value,r.ssrc=n.ssrc);const i=t.matchPrefix(e,"a=rtcp-rsize");r.reducedSize=i.length>0,r.compound=0===i.length;const o=t.matchPrefix(e,"a=rtcp-mux");return r.mux=o.length>0,r},t.writeRtcpParameters=function(e){let t="";return e.reducedSize&&(t+="a=rtcp-rsize\r\n"),e.mux&&(t+="a=rtcp-mux\r\n"),void 0!==e.ssrc&&e.cname&&(t+="a=ssrc:"+e.ssrc+" cname:"+e.cname+"\r\n"),t},t.parseMsid=function(e){let r;const n=t.matchPrefix(e,"a=msid:");if(1===n.length)return r=n[0].substring(7).split(" "),{stream:r[0],track:r[1]};const i=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"msid"===e.attribute));return i.length>0?(r=i[0].value.split(" "),{stream:r[0],track:r[1]}):void 0},t.parseSctpDescription=function(e){const r=t.parseMLine(e),n=t.matchPrefix(e,"a=max-message-size:");let i;n.length>0&&(i=parseInt(n[0].substring(19),10)),isNaN(i)&&(i=65536);const o=t.matchPrefix(e,"a=sctp-port:");if(o.length>0)return{port:parseInt(o[0].substring(12),10),protocol:r.fmt,maxMessageSize:i};const a=t.matchPrefix(e,"a=sctpmap:");if(a.length>0){const e=a[0].substring(10).split(" ");return{port:parseInt(e[0],10),protocol:e[1],maxMessageSize:i}}},t.writeSctpDescription=function(e,t){let r=[];return r="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&r.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),r.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,22)},t.writeSessionBoilerplate=function(e,r,n){let i;const o=void 0!==r?r:2;i=e||t.generateSessionId();return"v=0\r\no="+(n||"thisisadapterortc")+" "+i+" "+o+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.getDirection=function(e,r){const n=t.splitLines(e);for(let e=0;e<n.length;e++)switch(n[e]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return n[e].substring(2)}return r?t.getDirection(r):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substring(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){const r=t.splitLines(e)[0].substring(2).split(" ");return{kind:r[0],port:parseInt(r[1],10),protocol:r[2],fmt:r.slice(3).join(" ")}},t.parseOLine=function(e){const r=t.matchPrefix(e,"o=")[0].substring(2).split(" ");return{username:r[0],sessionId:r[1],sessionVersion:parseInt(r[2],10),netType:r[3],addressType:r[4],address:r[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;const r=t.splitLines(e);for(let e=0;e<r.length;e++)if(r[e].length<2||"="!==r[e].charAt(1))return!1;return!0},e.exports=t}(il);var ol=il.exports,al=e({__proto__:null,default:ol},[il.exports]);function sl(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;const t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substring(2)),e.candidate&&e.candidate.length){const r=new t(e),n=ol.parseCandidate(e.candidate);for(const e in n)e in r||Object.defineProperty(r,e,{value:n[e]});return r.toJSON=function(){return{candidate:r.candidate,sdpMid:r.sdpMid,sdpMLineIndex:r.sdpMLineIndex,usernameFragment:r.usernameFragment}},r}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,mu(e,"icecandidate",(t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t)))}function cl(e){!e.RTCIceCandidate||e.RTCIceCandidate&&"relayProtocol"in e.RTCIceCandidate.prototype||mu(e,"icecandidate",(e=>{if(e.candidate){const t=ol.parseCandidate(e.candidate.candidate);"relay"===t.type&&(e.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[t.priority>>24])}return e}))}function ul(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});const r=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){const{sdpSemantics:e}=this.getConfiguration();"plan-b"===e&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(function(e){if(!e||!e.sdp)return!1;const t=ol.splitSections(e.sdp);return t.shift(),t.some((e=>{const t=ol.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")}))}(arguments[0])){const e=function(e){const t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return-1;const r=parseInt(t[1],10);return r!=r?-1:r}(arguments[0]),r=function(e){let r=65536;return"firefox"===t.browser&&(r=t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),r}(e),n=function(e,r){let n=65536;"firefox"===t.browser&&57===t.version&&(n=65535);const i=ol.matchPrefix(e.sdp,"a=max-message-size:");return i.length>0?n=parseInt(i[0].substring(19),10):"firefox"===t.browser&&-1!==r&&(n=2147483637),n}(arguments[0],e);let i;i=0===r&&0===n?Number.POSITIVE_INFINITY:0===r||0===n?Math.max(r,n):Math.min(r,n);const o={};Object.defineProperty(o,"maxMessageSize",{get:()=>i}),this._sctp=o}return r.apply(this,arguments)}}function ll(e){if(!e.RTCPeerConnection||!("createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){const r=e.send;e.send=function(){const n=arguments[0],i=n.length||n.size||n.byteLength;if("open"===e.readyState&&t.sctp&&i>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return r.apply(e,arguments)}}const r=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){const e=r.apply(this,arguments);return t(e,this),e},mu(e,"datachannel",(e=>(t(e.channel,e.target),e)))}function dl(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach((e=>{const r=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{const t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;const r=new Event("connectionstatechange",e);t.dispatchEvent(r)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),r.apply(this,arguments)}}))}function pl(e,t){if(!e.RTCPeerConnection)return;if("chrome"===t.browser&&t.version>=71)return;if("safari"===t.browser&&t.version>=605)return;const r=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(t){if(t&&t.sdp&&-1!==t.sdp.indexOf("\na=extmap-allow-mixed")){const r=t.sdp.split("\n").filter((e=>"a=extmap-allow-mixed"!==e.trim())).join("\n");e.RTCSessionDescription&&t instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:t.type,sdp:r}):t.sdp=r}return r.apply(this,arguments)}}function fl(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const r=e.RTCPeerConnection.prototype.addIceCandidate;r&&0!==r.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&t.version<78||"firefox"===t.browser&&t.version<68||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():r.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function hl(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const r=e.RTCPeerConnection.prototype.setLocalDescription;r&&0!==r.length&&(e.RTCPeerConnection.prototype.setLocalDescription=function(){let e=arguments[0]||{};if("object"!=typeof e||e.type&&e.sdp)return r.apply(this,arguments);if(e={type:e.type,sdp:e.sdp},!e.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":e.type="offer";break;default:e.type="answer"}if(e.sdp||"offer"!==e.type&&"answer"!==e.type)return r.apply(this,[e]);return("offer"===e.type?this.createOffer:this.createAnswer).apply(this).then((e=>r.apply(this,[e])))})}var ml=Object.freeze({__proto__:null,shimRTCIceCandidate:sl,shimRTCIceCandidateRelayProtocol:cl,shimMaxMessageSize:ul,shimSendThrowTypeError:ll,shimConnectionState:dl,removeExtmapAllowMixed:pl,shimAddIceCandidateNullOrEmpty:fl,shimParameterlessSetLocalDescription:hl});const vl=function({window:e}={},t={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){const r=gu,n=function(e){const t={browser:null,version:null};if(void 0===e||!e.navigator||!e.navigator.userAgent)return t.browser="Not a browser.",t;const{navigator:r}=e;if(r.mozGetUserMedia)t.browser="firefox",t.version=hu(r.userAgent,/Firefox\/(\d+)\./,1);else if(r.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection)t.browser="chrome",t.version=hu(r.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else{if(!e.RTCPeerConnection||!r.userAgent.match(/AppleWebKit\/(\d+)\./))return t.browser="Not a supported browser.",t;t.browser="safari",t.version=hu(r.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}return t}(e),i={browserDetails:n,commonShim:ml,extractVersion:hu,disableLog:vu,disableWarnings:yu,sdp:al};switch(n.browser){case"chrome":if(!Ou||!Du||!t.shimChrome)return r("Chrome shim is not included in this adapter release."),i;if(null===n.version)return r("Chrome shim can not determine version, not shimming."),i;r("adapter.js shimming chrome."),i.browserShim=Ou,fl(e,n),hl(e),Ru(e,n),Pu(e),Du(e,n),Eu(e),Iu(e,n),ku(e),Au(e),xu(e),Mu(e,n),sl(e),cl(e),dl(e),ul(e,n),ll(e),pl(e,n);break;case"firefox":if(!Xu||!Fu||!t.shimFirefox)return r("Firefox shim is not included in this adapter release."),i;r("adapter.js shimming firefox."),i.browserShim=Xu,fl(e,n),hl(e),Nu(e,n),Fu(e,n),Vu(e),Bu(e),Uu(e),ju(e),Wu(e),Gu(e),Zu(e),Yu(e),Hu(e),sl(e),dl(e),ul(e,n),ll(e);break;case"safari":if(!nl||!t.shimSafari)return r("Safari shim is not included in this adapter release."),i;r("adapter.js shimming safari."),i.browserShim=nl,fl(e,n),hl(e),$u(e),tl(e),Qu(e),Ju(e),Ku(e),el(e),qu(e),rl(e),sl(e),cl(e),ul(e,n),ll(e),pl(e,n);break;default:r("Unsupported browser!")}return i}({window:"undefined"==typeof window?void 0:window});let yl=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"");var gl={exports:{}};!function(e,t){e.exports=function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=90)}({17:function(e,t,r){t.__esModule=!0,t.default=void 0;var n=r(18),i=function(){function e(){}return e.getFirstMatch=function(e,t){var r=t.match(e);return r&&r.length>0&&r[1]||""},e.getSecondMatch=function(e,t){var r=t.match(e);return r&&r.length>1&&r[2]||""},e.matchAndReturnConst=function(e,t,r){if(e.test(t))return r},e.getWindowsVersionName=function(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}},e.getMacOSVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}},e.getAndroidVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0},e.getVersionPrecision=function(e){return e.split(".").length},e.compareVersions=function(t,r,n){void 0===n&&(n=!1);var i=e.getVersionPrecision(t),o=e.getVersionPrecision(r),a=Math.max(i,o),s=0,c=e.map([t,r],(function(t){var r=a-e.getVersionPrecision(t),n=t+new Array(r+1).join(".0");return e.map(n.split("."),(function(e){return new Array(20-e.length).join("0")+e})).reverse()}));for(n&&(s=a-Math.min(i,o)),a-=1;a>=s;){if(c[0][a]>c[1][a])return 1;if(c[0][a]===c[1][a]){if(a===s)return 0;a-=1}else if(c[0][a]<c[1][a])return-1}},e.map=function(e,t){var r,n=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(r=0;r<e.length;r+=1)n.push(t(e[r]));return n},e.find=function(e,t){var r,n;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(r=0,n=e.length;r<n;r+=1){var i=e[r];if(t(i,r))return i}},e.assign=function(e){for(var t,r,n=e,i=arguments.length,o=new Array(i>1?i-1:0),a=1;a<i;a++)o[a-1]=arguments[a];if(Object.assign)return Object.assign.apply(Object,[e].concat(o));var s=function(){var e=o[t];"object"==typeof e&&null!==e&&Object.keys(e).forEach((function(t){n[t]=e[t]}))};for(t=0,r=o.length;t<r;t+=1)s();return e},e.getBrowserAlias=function(e){return n.BROWSER_ALIASES_MAP[e]},e.getBrowserTypeByAlias=function(e){return n.BROWSER_MAP[e]||""},e}();t.default=i,e.exports=t.default},18:function(e,t,r){t.__esModule=!0,t.ENGINE_MAP=t.OS_MAP=t.PLATFORMS_MAP=t.BROWSER_MAP=t.BROWSER_ALIASES_MAP=void 0,t.BROWSER_ALIASES_MAP={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},t.BROWSER_MAP={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},t.PLATFORMS_MAP={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},t.OS_MAP={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},t.ENGINE_MAP={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"}},90:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(91))&&n.__esModule?n:{default:n},o=r(18);function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var s=function(){function e(){}var t,r,n;return e.getParser=function(e,t){if(void 0===t&&(t=!1),"string"!=typeof e)throw new Error("UserAgent should be a string");return new i.default(e,t)},e.parse=function(e){return new i.default(e).getResult()},t=e,n=[{key:"BROWSER_MAP",get:function(){return o.BROWSER_MAP}},{key:"ENGINE_MAP",get:function(){return o.ENGINE_MAP}},{key:"OS_MAP",get:function(){return o.OS_MAP}},{key:"PLATFORMS_MAP",get:function(){return o.PLATFORMS_MAP}}],(r=null)&&a(t.prototype,r),n&&a(t,n),e}();t.default=s,e.exports=t.default},91:function(e,t,r){t.__esModule=!0,t.default=void 0;var n=c(r(92)),i=c(r(93)),o=c(r(94)),a=c(r(95)),s=c(r(17));function c(e){return e&&e.__esModule?e:{default:e}}var u=function(){function e(e,t){if(void 0===t&&(t=!1),null==e||""===e)throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}var t=e.prototype;return t.getUA=function(){return this._ua},t.test=function(e){return e.test(this._ua)},t.parseBrowser=function(){var e=this;this.parsedResult.browser={};var t=s.default.find(n.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.browser=t.describe(this.getUA())),this.parsedResult.browser},t.getBrowser=function(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()},t.getBrowserName=function(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""},t.getBrowserVersion=function(){return this.getBrowser().version},t.getOS=function(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()},t.parseOS=function(){var e=this;this.parsedResult.os={};var t=s.default.find(i.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.os=t.describe(this.getUA())),this.parsedResult.os},t.getOSName=function(e){var t=this.getOS().name;return e?String(t).toLowerCase()||"":t||""},t.getOSVersion=function(){return this.getOS().version},t.getPlatform=function(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()},t.getPlatformType=function(e){void 0===e&&(e=!1);var t=this.getPlatform().type;return e?String(t).toLowerCase()||"":t||""},t.parsePlatform=function(){var e=this;this.parsedResult.platform={};var t=s.default.find(o.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.platform=t.describe(this.getUA())),this.parsedResult.platform},t.getEngine=function(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()},t.getEngineName=function(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""},t.parseEngine=function(){var e=this;this.parsedResult.engine={};var t=s.default.find(a.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.engine=t.describe(this.getUA())),this.parsedResult.engine},t.parse=function(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this},t.getResult=function(){return s.default.assign({},this.parsedResult)},t.satisfies=function(e){var t=this,r={},n=0,i={},o=0;if(Object.keys(e).forEach((function(t){var a=e[t];"string"==typeof a?(i[t]=a,o+=1):"object"==typeof a&&(r[t]=a,n+=1)})),n>0){var a=Object.keys(r),c=s.default.find(a,(function(e){return t.isOS(e)}));if(c){var u=this.satisfies(r[c]);if(void 0!==u)return u}var l=s.default.find(a,(function(e){return t.isPlatform(e)}));if(l){var d=this.satisfies(r[l]);if(void 0!==d)return d}}if(o>0){var p=Object.keys(i),f=s.default.find(p,(function(e){return t.isBrowser(e,!0)}));if(void 0!==f)return this.compareVersion(i[f])}},t.isBrowser=function(e,t){void 0===t&&(t=!1);var r=this.getBrowserName().toLowerCase(),n=e.toLowerCase(),i=s.default.getBrowserTypeByAlias(n);return t&&i&&(n=i.toLowerCase()),n===r},t.compareVersion=function(e){var t=[0],r=e,n=!1,i=this.getBrowserVersion();if("string"==typeof i)return">"===e[0]||"<"===e[0]?(r=e.substr(1),"="===e[1]?(n=!0,r=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?r=e.substr(1):"~"===e[0]&&(n=!0,r=e.substr(1)),t.indexOf(s.default.compareVersions(i,r,n))>-1},t.isOS=function(e){return this.getOSName(!0)===String(e).toLowerCase()},t.isPlatform=function(e){return this.getPlatformType(!0)===String(e).toLowerCase()},t.isEngine=function(e){return this.getEngineName(!0)===String(e).toLowerCase()},t.is=function(e,t){return void 0===t&&(t=!1),this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)},t.some=function(e){var t=this;return void 0===e&&(e=[]),e.some((function(e){return t.is(e)}))},e}();t.default=u,e.exports=t.default},92:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=/version\/(\d+(\.?_?\d+)+)/i,a=[{test:[/googlebot/i],describe:function(e){var t={name:"Googlebot"},r=i.default.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/opera/i],describe:function(e){var t={name:"Opera"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opr\/|opios/i],describe:function(e){var t={name:"Opera"},r=i.default.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/SamsungBrowser/i],describe:function(e){var t={name:"Samsung Internet for Android"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Whale/i],describe:function(e){var t={name:"NAVER Whale Browser"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MZBrowser/i],describe:function(e){var t={name:"MZ Browser"},r=i.default.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/focus/i],describe:function(e){var t={name:"Focus"},r=i.default.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/swing/i],describe:function(e){var t={name:"Swing"},r=i.default.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/coast/i],describe:function(e){var t={name:"Opera Coast"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe:function(e){var t={name:"Opera Touch"},r=i.default.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/yabrowser/i],describe:function(e){var t={name:"Yandex Browser"},r=i.default.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/ucbrowser/i],describe:function(e){var t={name:"UC Browser"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Maxthon|mxios/i],describe:function(e){var t={name:"Maxthon"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/epiphany/i],describe:function(e){var t={name:"Epiphany"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/puffin/i],describe:function(e){var t={name:"Puffin"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sleipnir/i],describe:function(e){var t={name:"Sleipnir"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/k-meleon/i],describe:function(e){var t={name:"K-Meleon"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/micromessenger/i],describe:function(e){var t={name:"WeChat"},r=i.default.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/qqbrowser/i],describe:function(e){var t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},r=i.default.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/msie|trident/i],describe:function(e){var t={name:"Internet Explorer"},r=i.default.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/\sedg\//i],describe:function(e){var t={name:"Microsoft Edge"},r=i.default.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/edg([ea]|ios)/i],describe:function(e){var t={name:"Microsoft Edge"},r=i.default.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/vivaldi/i],describe:function(e){var t={name:"Vivaldi"},r=i.default.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/seamonkey/i],describe:function(e){var t={name:"SeaMonkey"},r=i.default.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sailfish/i],describe:function(e){var t={name:"Sailfish"},r=i.default.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return r&&(t.version=r),t}},{test:[/silk/i],describe:function(e){var t={name:"Amazon Silk"},r=i.default.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/phantom/i],describe:function(e){var t={name:"PhantomJS"},r=i.default.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/slimerjs/i],describe:function(e){var t={name:"SlimerJS"},r=i.default.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t={name:"BlackBerry"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t={name:"WebOS Browser"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/bada/i],describe:function(e){var t={name:"Bada"},r=i.default.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/tizen/i],describe:function(e){var t={name:"Tizen"},r=i.default.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/qupzilla/i],describe:function(e){var t={name:"QupZilla"},r=i.default.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/firefox|iceweasel|fxios/i],describe:function(e){var t={name:"Firefox"},r=i.default.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/electron/i],describe:function(e){var t={name:"Electron"},r=i.default.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MiuiBrowser/i],describe:function(e){var t={name:"Miui"},r=i.default.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/chromium/i],describe:function(e){var t={name:"Chromium"},r=i.default.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/chrome|crios|crmo/i],describe:function(e){var t={name:"Chrome"},r=i.default.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/GSA/i],describe:function(e){var t={name:"Google Search"},r=i.default.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t={name:"Android Browser"},r=i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/playstation 4/i],describe:function(e){var t={name:"PlayStation 4"},r=i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/safari|applewebkit/i],describe:function(e){var t={name:"Safari"},r=i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/.*/i],describe:function(e){var t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:i.default.getFirstMatch(t,e),version:i.default.getSecondMatch(t,e)}}}];t.default=a,e.exports=t.default},93:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=r(18),a=[{test:[/Roku\/DVP/],describe:function(e){var t=i.default.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:o.OS_MAP.Roku,version:t}}},{test:[/windows phone/i],describe:function(e){var t=i.default.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.WindowsPhone,version:t}}},{test:[/windows /i],describe:function(e){var t=i.default.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),r=i.default.getWindowsVersionName(t);return{name:o.OS_MAP.Windows,version:t,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(e){var t={name:o.OS_MAP.iOS},r=i.default.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return r&&(t.version=r),t}},{test:[/macintosh/i],describe:function(e){var t=i.default.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),r=i.default.getMacOSVersionName(t),n={name:o.OS_MAP.MacOS,version:t};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe:function(e){var t=i.default.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:o.OS_MAP.iOS,version:t}}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t=i.default.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),r=i.default.getAndroidVersionName(t),n={name:o.OS_MAP.Android,version:t};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t=i.default.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),r={name:o.OS_MAP.WebOS};return t&&t.length&&(r.version=t),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t=i.default.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||i.default.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||i.default.getFirstMatch(/\bbb(\d+)/i,e);return{name:o.OS_MAP.BlackBerry,version:t}}},{test:[/bada/i],describe:function(e){var t=i.default.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.Bada,version:t}}},{test:[/tizen/i],describe:function(e){var t=i.default.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.Tizen,version:t}}},{test:[/linux/i],describe:function(){return{name:o.OS_MAP.Linux}}},{test:[/CrOS/],describe:function(){return{name:o.OS_MAP.ChromeOS}}},{test:[/PlayStation 4/],describe:function(e){var t=i.default.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.PlayStation4,version:t}}}];t.default=a,e.exports=t.default},94:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=r(18),a=[{test:[/googlebot/i],describe:function(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe:function(e){var t=i.default.getFirstMatch(/(can-l01)/i,e)&&"Nova",r={type:o.PLATFORMS_MAP.mobile,vendor:"Huawei"};return t&&(r.model=t),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(e){var t=e.test(/ipod|iphone/i),r=e.test(/like (ipod|iphone)/i);return t&&!r},describe:function(e){var t=i.default.getFirstMatch(/(ipod|iphone)/i,e);return{type:o.PLATFORMS_MAP.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"blackberry"===e.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"BlackBerry"}}},{test:function(e){return"bada"===e.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"windows phone"===e.getBrowserName()},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Microsoft"}}},{test:function(e){var t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(e){return"android"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"macos"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop,vendor:"Apple"}}},{test:function(e){return"windows"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(e){return"linux"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(e){return"playstation 4"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}},{test:function(e){return"roku"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}}];t.default=a,e.exports=t.default},95:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=r(18),a=[{test:function(e){return"microsoft edge"===e.getBrowserName(!0)},describe:function(e){if(/\sedg\//i.test(e))return{name:o.ENGINE_MAP.Blink};var t=i.default.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:o.ENGINE_MAP.EdgeHTML,version:t}}},{test:[/trident/i],describe:function(e){var t={name:o.ENGINE_MAP.Trident},r=i.default.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){return e.test(/presto/i)},describe:function(e){var t={name:o.ENGINE_MAP.Presto},r=i.default.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=e.test(/gecko/i),r=e.test(/like gecko/i);return t&&!r},describe:function(e){var t={name:o.ENGINE_MAP.Gecko},r=i.default.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(apple)?webkit\/537\.36/i],describe:function(){return{name:o.ENGINE_MAP.Blink}}},{test:[/(apple)?webkit/i],describe:function(e){var t={name:o.ENGINE_MAP.WebKit},r=i.default.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}}];t.default=a,e.exports=t.default}})}(gl);var bl=r(gl.exports);kr({target:"Object",stat:!0,sham:!x},{create:da});var Cl=re.Object,Sl=function(e,t){return Cl.create(e,t)},_l=kr,Tl=Qr,wl=Un,Rl=te,Pl=bi,El=an,kl=z,Al=pn,xl=pt,Ll=Ar,Il=Qn("slice"),Dl=xl("species"),Ml=Array,Ol=Math.max;_l({target:"Array",proto:!0,forced:!Il},{slice:function(e,t){var r,n,i,o=kl(this),a=El(o),s=Pl(e,a),c=Pl(void 0===t?a:t,a);if(Tl(o)&&(r=o.constructor,(wl(r)&&(r===Ml||Tl(r.prototype))||Rl(r)&&null===(r=r[Dl]))&&(r=void 0),r===Ml||void 0===r))return Ll(o,s,c);for(n=new(void 0===r?Ml:r)(Ol(c-s,0)),i=0;s<c;s++,i++)s in o&&Al(n,i,o[s]);return n.length=i,n}});var Nl=Gr("Array").slice,Vl=l,Fl=Nl,Ul=Array.prototype,jl=function(e){var t=e.slice;return e===Ul||Vl(Ul,e)&&t===Ul.slice?Fl:t},Bl={},Wl=No,Gl=Vo.concat("length","prototype");Bl.f=Object.getOwnPropertyNames||function(e){return Wl(e,Gl)};var Zl={},Yl=bi,Hl=an,Xl=pn,Jl=Array,Kl=Math.max,Ql=S,ql=z,zl=Bl.f,$l=function(e,t,r){for(var n=Hl(e),i=Yl(t,n),o=Yl(void 0===r?n:r,n),a=Jl(Kl(o-i,0)),s=0;i<o;i++,s++)Xl(a,s,e[i]);return a.length=s,a},ed="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];Zl.f=function(e){return ed&&"Window"===Ql(e)?function(e){try{return zl(e)}catch(e){return $l(ed)}}(e):zl(ql(e))};var td={};td.f=Object.getOwnPropertySymbols;var rd=qt,nd=function(e,t,r){return rd.f(e,t,r)},id={},od=pt;id.f=od;var ad=re,sd=qe,cd=id,ud=qt.f,ld=D,dd=se,pd=pt,fd=_a,hd=kr,md=p,vd=D,yd=u,gd=x,bd=be,Cd=n,Sd=qe,_d=l,Td=rr,wd=z,Rd=_t,Pd=Oi,Ed=j,kd=da,Ad=jo,xd=Bl,Ld=Zl,Id=td,Dd=A,Md=qt,Od=xo,Nd=M,Vd=_a,Fd=nd,Ud=Ue.exports,jd=co,Bd=rt,Wd=pt,Gd=id,Zd=function(e){var t=ad.Symbol||(ad.Symbol={});sd(t,e)||ud(t,e,{value:cd.f(e)})},Yd=function(){var e=dd("Symbol"),t=e&&e.prototype,r=t&&t.valueOf,n=pd("toPrimitive");t&&!t[n]&&fd(t,n,(function(e){return ld(r,this)}),{arity:1})},Hd=Ba,Xd=To,Jd=Hs.forEach,Kd=so("hidden"),Qd="Symbol",qd="prototype",zd=Xd.set,$d=Xd.getterFor(Qd),ep=Object[qd],tp=md.Symbol,rp=tp&&tp[qd],np=md.TypeError,ip=md.QObject,op=Dd.f,ap=Md.f,sp=Ld.f,cp=Nd.f,up=yd([].push),lp=Ud("symbols"),dp=Ud("op-symbols"),pp=Ud("wks"),fp=!ip||!ip[qd]||!ip[qd].findChild,hp=gd&&Cd((function(){return 7!==kd(ap({},"a",{get:function(){return ap(this,"a",{value:7}).a}})).a}))?function(e,t,r){var n=op(ep,t);n&&delete ep[t],ap(e,t,r),n&&e!==ep&&ap(ep,t,n)}:ap,mp=function(e,t){var r=lp[e]=kd(rp);return zd(r,{type:Qd,tag:e,description:t}),gd||(r.description=t),r},vp=function(e,t,r){e===ep&&vp(dp,t,r),Td(e);var n=Rd(t);return Td(r),Sd(lp,n)?(r.enumerable?(Sd(e,Kd)&&e[Kd][n]&&(e[Kd][n]=!1),r=kd(r,{enumerable:Ed(0,!1)})):(Sd(e,Kd)||ap(e,Kd,Ed(1,{})),e[Kd][n]=!0),hp(e,n,r)):ap(e,n,r)},yp=function(e,t){Td(e);var r=wd(t),n=Ad(r).concat(Sp(r));return Jd(n,(function(t){gd&&!vd(gp,r,t)||vp(e,t,r[t])})),e},gp=function(e){var t=Rd(e),r=vd(cp,this,t);return!(this===ep&&Sd(lp,t)&&!Sd(dp,t))&&(!(r||!Sd(this,t)||!Sd(lp,t)||Sd(this,Kd)&&this[Kd][t])||r)},bp=function(e,t){var r=wd(e),n=Rd(t);if(r!==ep||!Sd(lp,n)||Sd(dp,n)){var i=op(r,n);return!i||!Sd(lp,n)||Sd(r,Kd)&&r[Kd][n]||(i.enumerable=!0),i}},Cp=function(e){var t=sp(wd(e)),r=[];return Jd(t,(function(e){Sd(lp,e)||Sd(jd,e)||up(r,e)})),r},Sp=function(e){var t=e===ep,r=sp(t?dp:wd(e)),n=[];return Jd(r,(function(e){!Sd(lp,e)||t&&!Sd(ep,e)||up(n,lp[e])})),n};bd||(tp=function(){if(_d(rp,this))throw np("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?Pd(arguments[0]):void 0,t=Bd(e),r=function(e){this===ep&&vd(r,dp,e),Sd(this,Kd)&&Sd(this[Kd],t)&&(this[Kd][t]=!1),hp(this,t,Ed(1,e))};return gd&&fp&&hp(ep,t,{configurable:!0,set:r}),mp(t,e)},Vd(rp=tp[qd],"toString",(function(){return $d(this).tag})),Vd(tp,"withoutSetter",(function(e){return mp(Bd(e),e)})),Nd.f=gp,Md.f=vp,Od.f=yp,Dd.f=bp,xd.f=Ld.f=Cp,Id.f=Sp,Gd.f=function(e){return mp(Wd(e),e)},gd&&Fd(rp,"description",{configurable:!0,get:function(){return $d(this).description}})),hd({global:!0,constructor:!0,wrap:!0,forced:!bd,sham:!bd},{Symbol:tp}),Jd(Ad(pp),(function(e){Zd(e)})),hd({target:Qd,stat:!0,forced:!bd},{useSetter:function(){fp=!0},useSimple:function(){fp=!1}}),hd({target:"Object",stat:!0,forced:!bd,sham:!gd},{create:function(e,t){return void 0===t?kd(e):yp(kd(e),t)},defineProperty:vp,defineProperties:yp,getOwnPropertyDescriptor:bp}),hd({target:"Object",stat:!0,forced:!bd},{getOwnPropertyNames:Cp}),Yd(),Hd(tp,Qd),jd[Kd]=!0;var _p=be&&!!Symbol.for&&!!Symbol.keyFor,Tp=kr,wp=se,Rp=qe,Pp=Oi,Ep=Ue.exports,kp=_p,Ap=Ep("string-to-symbol-registry"),xp=Ep("symbol-to-string-registry");Tp({target:"Symbol",stat:!0,forced:!kp},{for:function(e){var t=Pp(e);if(Rp(Ap,t))return Ap[t];var r=wp("Symbol")(t);return Ap[t]=r,xp[r]=t,r}});var Lp=kr,Ip=qe,Dp=Re,Mp=Ee,Op=_p,Np=(0,Ue.exports)("symbol-to-string-registry");Lp({target:"Symbol",stat:!0,forced:!Op},{keyFor:function(e){if(!Dp(e))throw TypeError(Mp(e)+" is not a symbol");if(Ip(Np,e))return Np[e]}});var Vp=Qr,Fp=k,Up=S,jp=Oi,Bp=u([].push),Wp=kr,Gp=se,Zp=y,Yp=D,Hp=u,Xp=n,Jp=k,Kp=Re,Qp=Ar,qp=function(e){if(Fp(e))return e;if(Vp(e)){for(var t=e.length,r=[],n=0;n<t;n++){var i=e[n];"string"==typeof i?Bp(r,i):"number"!=typeof i&&"Number"!==Up(i)&&"String"!==Up(i)||Bp(r,jp(i))}var o=r.length,a=!0;return function(e,t){if(a)return a=!1,t;if(Vp(this))return t;for(var n=0;n<o;n++)if(r[n]===e)return t}}},zp=be,$p=String,ef=Gp("JSON","stringify"),tf=Hp(/./.exec),rf=Hp("".charAt),nf=Hp("".charCodeAt),of=Hp("".replace),af=Hp(1..toString),sf=/[\uD800-\uDFFF]/g,cf=/^[\uD800-\uDBFF]$/,uf=/^[\uDC00-\uDFFF]$/,lf=!zp||Xp((function(){var e=Gp("Symbol")("stringify detection");return"[null]"!==ef([e])||"{}"!==ef({a:e})||"{}"!==ef(Object(e))})),df=Xp((function(){return'"\\udf06\\ud834"'!==ef("\udf06\ud834")||'"\\udead"'!==ef("\udead")})),pf=function(e,t){var r=Qp(arguments),n=qp(t);if(Jp(n)||void 0!==e&&!Kp(e))return r[1]=function(e,t){if(Jp(n)&&(t=Yp(n,this,$p(e),t)),!Kp(t))return t},Zp(ef,null,r)},ff=function(e,t,r){var n=rf(r,t-1),i=rf(r,t+1);return tf(cf,e)&&!tf(uf,i)||tf(uf,e)&&!tf(cf,n)?"\\u"+af(nf(e,0),16):e};ef&&Wp({target:"JSON",stat:!0,arity:3,forced:lf||df},{stringify:function(e,t,r){var n=Qp(arguments),i=Zp(lf?pf:ef,null,n);return df&&"string"==typeof i?of(i,sf,ff):i}});var hf=td,mf=Je;kr({target:"Object",stat:!0,forced:!be||n((function(){hf.f(1)}))},{getOwnPropertySymbols:function(e){var t=hf.f;return t?t(mf(e)):[]}});var vf=re.Object.getOwnPropertySymbols,yf={exports:{}},gf=kr,bf=x,Cf=qt.f;gf({target:"Object",stat:!0,forced:Object.defineProperty!==Cf,sham:!bf},{defineProperty:Cf});var Sf=re.Object,_f=yf.exports=function(e,t,r){return Sf.defineProperty(e,t,r)};Sf.defineProperty.sham&&(_f.sham=!0);var Tf=yf.exports,wf=Je,Rf=jo;kr({target:"Object",stat:!0,forced:n((function(){Rf(1)}))},{keys:function(e){return Rf(wf(e))}});var Pf=re.Object.keys,Ef={exports:{}},kf=kr,Af=n,xf=z,Lf=A.f,If=x;kf({target:"Object",stat:!0,forced:!If||Af((function(){Lf(1)})),sham:!If},{getOwnPropertyDescriptor:function(e,t){return Lf(xf(e),t)}});var Df=re.Object,Mf=Ef.exports=function(e,t){return Df.getOwnPropertyDescriptor(e,t)};Df.getOwnPropertyDescriptor.sham&&(Mf.sham=!0);var Of=Ef.exports,Nf=se,Vf=Bl,Ff=td,Uf=rr,jf=u([].concat),Bf=Nf("Reflect","ownKeys")||function(e){var t=Vf.f(Uf(e)),r=Ff.f;return r?jf(t,r(e)):t},Wf=Bf,Gf=z,Zf=A,Yf=pn;kr({target:"Object",stat:!0,sham:!x},{getOwnPropertyDescriptors:function(e){for(var t,r,n=Gf(e),i=Zf.f,o=Wf(n),a={},s=0;o.length>s;)void 0!==(r=i(n,t=o[s++]))&&Yf(a,t,r);return a}});var Hf=re.Object.getOwnPropertyDescriptors,Xf={exports:{}},Jf=kr,Kf=x,Qf=xo.f;Jf({target:"Object",stat:!0,forced:Object.defineProperties!==Qf,sham:!Kf},{defineProperties:Qf});var qf=re.Object,zf=Xf.exports=function(e,t){return qf.defineProperties(e,t)};qf.defineProperties.sham&&(zf.sham=!0);var $f=Xf.exports,eh=y,th=z,rh=en,nh=an,ih=Js,oh=Math.min,ah=[].lastIndexOf,sh=!!ah&&1/[1].lastIndexOf(1,-0)<0,ch=ih("lastIndexOf"),uh=sh||!ch?function(e){if(sh)return eh(ah,this,arguments)||0;var t=th(this),r=nh(t),n=r-1;for(arguments.length>1&&(n=oh(n,rh(arguments[1]))),n<0&&(n=r+n);n>=0;n--)if(n in t&&t[n]===e)return n||0;return-1}:ah;kr({target:"Array",proto:!0,forced:uh!==[].lastIndexOf},{lastIndexOf:uh});var lh=Gr("Array").lastIndexOf,dh=l,ph=lh,fh=Array.prototype,hh=function(e){var t=e.lastIndexOf;return e===fh||dh(fh,e)&&t===fh.lastIndexOf?ph:t},mh=x,vh=u,yh=D,gh=n,bh=jo,Ch=td,Sh=M,_h=Je,Th=Y,wh=Object.assign,Rh=Object.defineProperty,Ph=vh([].concat),Eh=!wh||gh((function(){if(mh&&1!==wh({b:1},wh(Rh({},"a",{enumerable:!0,get:function(){Rh(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},r=Symbol("assign detection"),n="abcdefghijklmnopqrst";return e[r]=7,n.split("").forEach((function(e){t[e]=e})),7!==wh({},e)[r]||bh(wh({},t)).join("")!==n}))?function(e,t){for(var r=_h(e),n=arguments.length,i=1,o=Ch.f,a=Sh.f;n>i;)for(var s,c=Th(arguments[i++]),u=o?Ph(bh(c),o(c)):bh(c),l=u.length,d=0;l>d;)s=u[d++],mh&&!yh(a,c,s)||(r[s]=c[s]);return r}:wh,kh=Eh;kr({target:"Object",stat:!0,arity:2,forced:Object.assign!==kh},{assign:kh});var Ah=re.Object.assign,xh={exports:{}},Lh=n((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}})),Ih=n,Dh=te,Mh=S,Oh=Lh,Nh=Object.isExtensible,Vh=Ih((function(){Nh(1)}))||Oh?function(e){return!!Dh(e)&&((!Oh||"ArrayBuffer"!==Mh(e))&&(!Nh||Nh(e)))}:Nh,Fh=!n((function(){return Object.isExtensible(Object.preventExtensions({}))})),Uh=kr,jh=u,Bh=co,Wh=te,Gh=qe,Zh=qt.f,Yh=Bl,Hh=Zl,Xh=Vh,Jh=Fh,Kh=!1,Qh=rt("meta"),qh=0,zh=function(e){Zh(e,Qh,{value:{objectID:"O"+qh++,weakData:{}}})},$h=xh.exports={enable:function(){$h.enable=function(){},Kh=!0;var e=Yh.f,t=jh([].splice),r={};r[Qh]=1,e(r).length&&(Yh.f=function(r){for(var n=e(r),i=0,o=n.length;i<o;i++)if(n[i]===Qh){t(n,i,1);break}return n},Uh({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:Hh.f}))},fastKey:function(e,t){if(!Wh(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!Gh(e,Qh)){if(!Xh(e))return"F";if(!t)return"E";zh(e)}return e[Qh].objectID},getWeakData:function(e,t){if(!Gh(e,Qh)){if(!Xh(e))return!0;if(!t)return!1;zh(e)}return e[Qh].weakData},onFreeze:function(e){return Jh&&Kh&&Xh(e)&&!Gh(e,Qh)&&zh(e),e}};Bh[Qh]=!0;var em=eo,tm=pt("iterator"),rm=Array.prototype,nm=Sn,im=Me,om=H,am=eo,sm=pt("iterator"),cm=function(e){if(!om(e))return im(e,sm)||im(e,"@@iterator")||am[nm(e)]},um=D,lm=Le,dm=rr,pm=Ee,fm=cm,hm=TypeError,mm=D,vm=rr,ym=Me,gm=function(e,t,r){var n,i;vm(e);try{if(!(n=ym(e,"return"))){if("throw"===t)throw r;return r}n=mm(n,e)}catch(e){i=!0,n=e}if("throw"===t)throw r;if(i)throw n;return vm(n),r},bm=Qt,Cm=D,Sm=rr,_m=Ee,Tm=function(e){return void 0!==e&&(em.Array===e||rm[tm]===e)},wm=an,Rm=l,Pm=function(e,t){var r=arguments.length<2?fm(e):t;if(lm(r))return dm(um(r,e));throw hm(pm(e)+" is not iterable")},Em=cm,km=gm,Am=TypeError,xm=function(e,t){this.stopped=e,this.result=t},Lm=xm.prototype,Im=function(e,t,r){var n,i,o,a,s,c,u,l=r&&r.that,d=!(!r||!r.AS_ENTRIES),p=!(!r||!r.IS_RECORD),f=!(!r||!r.IS_ITERATOR),h=!(!r||!r.INTERRUPTED),m=bm(t,l),v=function(e){return n&&km(n,"normal",e),new xm(!0,e)},y=function(e){return d?(Sm(e),h?m(e[0],e[1],v):m(e[0],e[1])):h?m(e,v):m(e)};if(p)n=e.iterator;else if(f)n=e;else{if(!(i=Em(e)))throw Am(_m(e)+" is not iterable");if(Tm(i)){for(o=0,a=wm(e);a>o;o++)if((s=y(e[o]))&&Rm(Lm,s))return s;return new xm(!1)}n=Pm(e,i)}for(c=p?e.next:n.next;!(u=Cm(c,n)).done;){try{s=y(u.value)}catch(e){km(n,"throw",e)}if("object"==typeof s&&s&&Rm(Lm,s))return s}return new xm(!1)},Dm=l,Mm=TypeError,Om=function(e,t){if(Dm(t,e))return e;throw Mm("Incorrect invocation")},Nm=kr,Vm=p,Fm=xh.exports,Um=n,jm=vr,Bm=Im,Wm=Om,Gm=k,Zm=te,Ym=H,Hm=Ba,Xm=qt.f,Jm=Hs.forEach,Km=x,Qm=To.set,qm=To.getterFor,zm=function(e,t,r){var n,i=-1!==e.indexOf("Map"),o=-1!==e.indexOf("Weak"),a=i?"set":"add",s=Vm[e],c=s&&s.prototype,u={};if(Km&&Gm(s)&&(o||c.forEach&&!Um((function(){(new s).entries().next()})))){var l=(n=t((function(t,r){Qm(Wm(t,l),{type:e,collection:new s}),Ym(r)||Bm(r,t[a],{that:t,AS_ENTRIES:i})}))).prototype,d=qm(e);Jm(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(e){var t="add"===e||"set"===e;!(e in c)||o&&"clear"===e||jm(l,e,(function(r,n){var i=d(this).collection;if(!t&&o&&!Zm(r))return"get"===e&&void 0;var a=i[e](0===r?0:r,n);return t?this:a}))})),o||Xm(l,"size",{configurable:!0,get:function(){return d(this).collection.size}})}else n=r.getConstructor(t,e,i,a),Fm.enable();return Hm(n,e,!1,!0),u[e]=n,Nm({global:!0,forced:!0},u),o||r.setStrong(n,e,i),n},$m=_a,ev=se,tv=nd,rv=x,nv=pt("species"),iv=function(e){var t=ev(e);rv&&t&&!t[nv]&&tv(t,nv,{configurable:!0,get:function(){return this}})},ov=da,av=nd,sv=function(e,t,r){for(var n in t)r&&r.unsafe&&e[n]?e[n]=t[n]:$m(e,n,t[n],r);return e},cv=Qt,uv=Om,lv=H,dv=Im,pv=bs,fv=Cs,hv=iv,mv=x,vv=xh.exports.fastKey,yv=To.set,gv=To.getterFor,bv={getConstructor:function(e,t,r,n){var i=e((function(e,i){uv(e,o),yv(e,{type:t,index:ov(null),first:void 0,last:void 0,size:0}),mv||(e.size=0),lv(i)||dv(i,e[n],{that:e,AS_ENTRIES:r})})),o=i.prototype,a=gv(t),s=function(e,t,r){var n,i,o=a(e),s=c(e,t);return s?s.value=r:(o.last=s={index:i=vv(t,!0),key:t,value:r,previous:n=o.last,next:void 0,removed:!1},o.first||(o.first=s),n&&(n.next=s),mv?o.size++:e.size++,"F"!==i&&(o.index[i]=s)),e},c=function(e,t){var r,n=a(e),i=vv(t);if("F"!==i)return n.index[i];for(r=n.first;r;r=r.next)if(r.key===t)return r};return sv(o,{clear:function(){for(var e=a(this),t=e.index,r=e.first;r;)r.removed=!0,r.previous&&(r.previous=r.previous.next=void 0),delete t[r.index],r=r.next;e.first=e.last=void 0,mv?e.size=0:this.size=0},delete:function(e){var t=this,r=a(t),n=c(t,e);if(n){var i=n.next,o=n.previous;delete r.index[n.index],n.removed=!0,o&&(o.next=i),i&&(i.previous=o),r.first===n&&(r.first=i),r.last===n&&(r.last=o),mv?r.size--:t.size--}return!!n},forEach:function(e){for(var t,r=a(this),n=cv(e,arguments.length>1?arguments[1]:void 0);t=t?t.next:r.first;)for(n(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!c(this,e)}}),sv(o,r?{get:function(e){var t=c(this,e);return t&&t.value},set:function(e,t){return s(this,0===e?0:e,t)}}:{add:function(e){return s(this,e=0===e?0:e,e)}}),mv&&av(o,"size",{configurable:!0,get:function(){return a(this).size}}),i},setStrong:function(e,t,r){var n=t+" Iterator",i=gv(t),o=gv(n);pv(e,t,(function(e,t){yv(this,{type:n,target:e,state:i(e),kind:t,last:void 0})}),(function(){for(var e=o(this),t=e.kind,r=e.last;r&&r.removed;)r=r.previous;return e.target&&(e.last=r=r?r.next:e.state.first)?fv("keys"===t?r.key:"values"===t?r.value:[r.key,r.value],!1):(e.target=void 0,fv(void 0,!0))}),r?"entries":"values",!r,!0),hv(t)}};zm("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),bv);var Cv=u,Sv=en,_v=Oi,Tv=K,wv=Cv("".charAt),Rv=Cv("".charCodeAt),Pv=Cv("".slice),Ev=function(e){return function(t,r){var n,i,o=_v(Tv(t)),a=Sv(r),s=o.length;return a<0||a>=s?e?"":void 0:(n=Rv(o,a))<55296||n>56319||a+1===s||(i=Rv(o,a+1))<56320||i>57343?e?wv(o,a):n:e?Pv(o,a,a+2):i-56320+(n-55296<<10)+65536}},kv={codeAt:Ev(!1),charAt:Ev(!0)},Av=kv.charAt,xv=Oi,Lv=To,Iv=bs,Dv=Cs,Mv="String Iterator",Ov=Lv.set,Nv=Lv.getterFor(Mv);Iv(String,"String",(function(e){Ov(this,{type:Mv,string:xv(e),index:0})}),(function(){var e,t=Nv(this),r=t.string,n=t.index;return n>=r.length?Dv(void 0,!0):(e=Av(r,n),t.index+=e.length,Dv(e,!1))}));var Vv=re.Set,Fv=Ee,Uv=function(e){if("object"==typeof e&&"size"in e&&"has"in e&&"add"in e&&"delete"in e&&"keys"in e)return e;throw TypeError(Fv(e)+" is not a set")},jv=function(e,t){return 1===t?function(t,r){return t[e](r)}:function(t,r,n){return t[e](r,n)}},Bv=jv,Wv=se("Set"),Gv=Wv.prototype,Zv={Set:Wv,add:Bv("add",1),has:Bv("has",1),remove:Bv("delete",1),proto:Gv},Yv=D,Hv=function(e,t,r){for(var n,i,o=r?e:e.iterator,a=e.next;!(n=Yv(a,o)).done;)if(void 0!==(i=t(n.value)))return i},Xv=Hv,Jv=function(e,t,r){return r?Xv(e.keys(),t,!0):e.forEach(t)},Kv=Jv,Qv=Zv.Set,qv=Zv.add,zv=function(e){var t=new Qv;return Kv(e,(function(e){qv(t,e)})),t},$v=function(e){return e.size},ey=Le,ty=rr,ry=D,ny=en,iy=function(e){return{iterator:e,next:e.next,done:!1}},oy="Invalid size",ay=RangeError,sy=TypeError,cy=Math.max,uy=function(e,t,r,n){this.set=e,this.size=t,this.has=r,this.keys=n};uy.prototype={getIterator:function(){return iy(ty(ry(this.keys,this.set)))},includes:function(e){return ry(this.has,this.set,e)}};var ly=function(e){ty(e);var t=+e.size;if(t!=t)throw sy(oy);var r=ny(t);if(r<0)throw ay(oy);return new uy(e,cy(r,0),ey(e.has),ey(e.keys))},dy=Uv,py=zv,fy=$v,hy=ly,my=Jv,vy=Hv,yy=Zv.has,gy=Zv.remove,by=function(e){var t=dy(this),r=hy(e),n=py(t);return fy(t)<=r.size?my(t,(function(e){r.includes(e)&&gy(n,e)})):vy(r.getIterator(),(function(e){yy(t,e)&&gy(n,e)})),n},Cy=function(){return!1},Sy=by;kr({target:"Set",proto:!0,real:!0,forced:!Cy()},{difference:Sy});var _y=Uv,Ty=$v,wy=ly,Ry=Jv,Py=Hv,Ey=Zv.Set,ky=Zv.add,Ay=Zv.has,xy=function(e){var t=_y(this),r=wy(e),n=new Ey;return Ty(t)>r.size?Py(r.getIterator(),(function(e){Ay(t,e)&&ky(n,e)})):Ry(t,(function(e){r.includes(e)&&ky(n,e)})),n},Ly=n,Iy=xy;kr({target:"Set",proto:!0,real:!0,forced:!Cy()||Ly((function(){return"3,2"!==Array.from(new Set([1,2,3]).intersection(new Set([3,2])))}))},{intersection:Iy});var Dy=Uv,My=Zv.has,Oy=$v,Ny=ly,Vy=Jv,Fy=Hv,Uy=gm,jy=function(e){var t=Dy(this),r=Ny(e);if(Oy(t)<=r.size)return!1!==Vy(t,(function(e){if(r.includes(e))return!1}),!0);var n=r.getIterator();return!1!==Fy(n,(function(e){if(My(t,e))return Uy(n,"normal",!1)}))},By=jy;kr({target:"Set",proto:!0,real:!0,forced:!Cy()},{isDisjointFrom:By});var Wy=Uv,Gy=$v,Zy=Jv,Yy=ly,Hy=function(e){var t=Wy(this),r=Yy(e);return!(Gy(t)>r.size)&&!1!==Zy(t,(function(e){if(!r.includes(e))return!1}),!0)},Xy=Hy;kr({target:"Set",proto:!0,real:!0,forced:!Cy()},{isSubsetOf:Xy});var Jy=Uv,Ky=Zv.has,Qy=$v,qy=ly,zy=Hv,$y=gm,eg=function(e){var t=Jy(this),r=qy(e);if(Qy(t)<r.size)return!1;var n=r.getIterator();return!1!==zy(n,(function(e){if(!Ky(t,e))return $y(n,"normal",!1)}))},tg=eg;kr({target:"Set",proto:!0,real:!0,forced:!Cy()},{isSupersetOf:tg});var rg=Uv,ng=zv,ig=ly,og=Hv,ag=Zv.add,sg=Zv.has,cg=Zv.remove,ug=function(e){var t=rg(this),r=ig(e).getIterator(),n=ng(t);return og(r,(function(e){sg(t,e)?cg(n,e):ag(n,e)})),n},lg=ug;kr({target:"Set",proto:!0,real:!0,forced:!Cy()},{symmetricDifference:lg});var dg=Uv,pg=Zv.add,fg=zv,hg=ly,mg=Hv,vg=function(e){var t=dg(this),r=hg(e).getIterator(),n=fg(t);return mg(r,(function(e){pg(n,e)})),n},yg=vg;kr({target:"Set",proto:!0,real:!0,forced:!Cy()},{union:yg});var gg=Vv,bg=Un,Cg=Ee,Sg=TypeError,_g=function(e){if(bg(e))return e;throw Sg(Cg(e)+" is not a constructor")},Tg=Qt,wg=D,Rg=Le,Pg=_g,Eg=H,kg=Im,Ag=[].push,xg=function(e){var t,r,n,i,o=arguments.length,a=o>1?arguments[1]:void 0;return Pg(this),(t=void 0!==a)&&Rg(a),Eg(e)?new this:(r=[],t?(n=0,i=Tg(a,o>2?arguments[2]:void 0),kg(e,(function(e){wg(Ag,r,i(e,n++))}))):kg(e,Ag,{that:r}),new this(r))};kr({target:"Set",stat:!0,forced:!0},{from:xg});var Lg=Ar,Ig=function(){return new this(Lg(arguments))};kr({target:"Set",stat:!0,forced:!0},{of:Ig});var Dg=Uv,Mg=Zv.add;kr({target:"Set",proto:!0,real:!0,forced:!0},{addAll:function(){for(var e=Dg(this),t=0,r=arguments.length;t<r;t++)Mg(e,arguments[t]);return e}});var Og=Uv,Ng=Zv.remove;kr({target:"Set",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=Og(this),r=!0,n=0,i=arguments.length;n<i;n++)e=Ng(t,arguments[n]),r=r&&e;return!!r}});var Vg=Qt,Fg=Uv,Ug=Jv;kr({target:"Set",proto:!0,real:!0,forced:!0},{every:function(e){var t=Fg(this),r=Vg(e,arguments.length>1?arguments[1]:void 0);return!1!==Ug(t,(function(e){if(!r(e,e,t))return!1}),!0)}});var jg=Sn,Bg=qe,Wg=H,Gg=eo,Zg=pt("iterator"),Yg=Object,Hg=k,Xg=function(e){if(Wg(e))return!1;var t=Yg(e);return void 0!==t[Zg]||"@@iterator"in t||Bg(Gg,jg(t))},Jg=te,Kg=se("Set"),Qg=function(e){return function(e){return Jg(e)&&"number"==typeof e.size&&Hg(e.has)&&Hg(e.keys)}(e)?e:Xg(e)?new Kg(e):e},qg=D,zg=Qg,$g=by;kr({target:"Set",proto:!0,real:!0,forced:!0},{difference:function(e){return qg($g,this,zg(e))}});var eb=Qt,tb=Uv,rb=Jv,nb=Zv.Set,ib=Zv.add;kr({target:"Set",proto:!0,real:!0,forced:!0},{filter:function(e){var t=tb(this),r=eb(e,arguments.length>1?arguments[1]:void 0),n=new nb;return rb(t,(function(e){r(e,e,t)&&ib(n,e)})),n}});var ob=Qt,ab=Uv,sb=Jv;kr({target:"Set",proto:!0,real:!0,forced:!0},{find:function(e){var t=ab(this),r=ob(e,arguments.length>1?arguments[1]:void 0),n=sb(t,(function(e){if(r(e,e,t))return{value:e}}),!0);return n&&n.value}});var cb=D,ub=Qg,lb=xy;kr({target:"Set",proto:!0,real:!0,forced:!0},{intersection:function(e){return cb(lb,this,ub(e))}});var db=D,pb=Qg,fb=jy;kr({target:"Set",proto:!0,real:!0,forced:!0},{isDisjointFrom:function(e){return db(fb,this,pb(e))}});var hb=D,mb=Qg,vb=Hy;kr({target:"Set",proto:!0,real:!0,forced:!0},{isSubsetOf:function(e){return hb(vb,this,mb(e))}});var yb=D,gb=Qg,bb=eg;kr({target:"Set",proto:!0,real:!0,forced:!0},{isSupersetOf:function(e){return yb(bb,this,gb(e))}});var Cb=kr,Sb=u,_b=Uv,Tb=Jv,wb=Oi,Rb=Sb([].join),Pb=Sb([].push);Cb({target:"Set",proto:!0,real:!0,forced:!0},{join:function(e){var t=_b(this),r=void 0===e?",":wb(e),n=[];return Tb(t,(function(e){Pb(n,e)})),Rb(n,r)}});var Eb=Qt,kb=Uv,Ab=Jv,xb=Zv.Set,Lb=Zv.add;kr({target:"Set",proto:!0,real:!0,forced:!0},{map:function(e){var t=kb(this),r=Eb(e,arguments.length>1?arguments[1]:void 0),n=new xb;return Ab(t,(function(e){Lb(n,r(e,e,t))})),n}});var Ib=Le,Db=Uv,Mb=Jv,Ob=TypeError;kr({target:"Set",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=Db(this),r=arguments.length<2,n=r?void 0:arguments[1];if(Ib(e),Mb(t,(function(i){r?(r=!1,n=i):n=e(n,i,i,t)})),r)throw Ob("Reduce of empty set with no initial value");return n}});var Nb=Qt,Vb=Uv,Fb=Jv;kr({target:"Set",proto:!0,real:!0,forced:!0},{some:function(e){var t=Vb(this),r=Nb(e,arguments.length>1?arguments[1]:void 0);return!0===Fb(t,(function(e){if(r(e,e,t))return!0}),!0)}});var Ub=D,jb=Qg,Bb=ug;kr({target:"Set",proto:!0,real:!0,forced:!0},{symmetricDifference:function(e){return Ub(Bb,this,jb(e))}});var Wb=D,Gb=Qg,Zb=vg;kr({target:"Set",proto:!0,real:!0,forced:!0},{union:function(e){return Wb(Zb,this,Gb(e))}});var Yb=gg;zm("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),bv);var Hb=re.Map,Xb=jv,Jb=se("Map"),Kb={Map:Jb,set:Xb("set",2),get:Xb("get",1),has:Xb("has",1),remove:Xb("delete",1),proto:Jb.prototype},Qb=kr,qb=Le,zb=K,$b=Im,eC=Kb.Map,tC=Kb.has,rC=Kb.get,nC=Kb.set,iC=u([].push);Qb({target:"Map",stat:!0,forced:true},{groupBy:function(e,t){zb(e),qb(t);var r=new eC,n=0;return $b(e,(function(e){var i=t(e,n++);tC(r,i)?iC(rC(r,i),e):nC(r,i,[e])})),r}});var oC=Hb;kr({target:"Map",stat:!0,forced:!0},{from:xg}),kr({target:"Map",stat:!0,forced:!0},{of:Ig});var aC=Ee,sC=function(e){if("object"==typeof e&&"size"in e&&"has"in e&&"get"in e&&"set"in e&&"delete"in e&&"entries"in e)return e;throw TypeError(aC(e)+" is not a map")},cC=sC,uC=Kb.remove;kr({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=cC(this),r=!0,n=0,i=arguments.length;n<i;n++)e=uC(t,arguments[n]),r=r&&e;return!!r}});var lC=sC,dC=Kb.get,pC=Kb.has,fC=Kb.set;kr({target:"Map",proto:!0,real:!0,forced:!0},{emplace:function(e,t){var r,n,i=lC(this);return pC(i,e)?(r=dC(i,e),"update"in t&&(r=t.update(r,e,i),fC(i,e,r)),r):(n=t.insert(e,i),fC(i,e,n),n)}});var hC=Hv,mC=function(e,t,r){return r?hC(e.entries(),(function(e){return t(e[1],e[0])}),!0):e.forEach(t)},vC=Qt,yC=sC,gC=mC;kr({target:"Map",proto:!0,real:!0,forced:!0},{every:function(e){var t=yC(this),r=vC(e,arguments.length>1?arguments[1]:void 0);return!1!==gC(t,(function(e,n){if(!r(e,n,t))return!1}),!0)}});var bC=Qt,CC=sC,SC=mC,_C=Kb.Map,TC=Kb.set;kr({target:"Map",proto:!0,real:!0,forced:!0},{filter:function(e){var t=CC(this),r=bC(e,arguments.length>1?arguments[1]:void 0),n=new _C;return SC(t,(function(e,i){r(e,i,t)&&TC(n,i,e)})),n}});var wC=Qt,RC=sC,PC=mC;kr({target:"Map",proto:!0,real:!0,forced:!0},{find:function(e){var t=RC(this),r=wC(e,arguments.length>1?arguments[1]:void 0),n=PC(t,(function(e,n){if(r(e,n,t))return{value:e}}),!0);return n&&n.value}});var EC=Qt,kC=sC,AC=mC;kr({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function(e){var t=kC(this),r=EC(e,arguments.length>1?arguments[1]:void 0),n=AC(t,(function(e,n){if(r(e,n,t))return{key:n}}),!0);return n&&n.key}});var xC=function(e,t){return e===t||e!=e&&t!=t},LC=sC,IC=mC;kr({target:"Map",proto:!0,real:!0,forced:!0},{includes:function(e){return!0===IC(LC(this),(function(t){if(xC(t,e))return!0}),!0)}});var DC=D,MC=Im,OC=k,NC=Le,VC=Kb.Map;kr({target:"Map",stat:!0,forced:!0},{keyBy:function(e,t){var r=new(OC(this)?this:VC);NC(t);var n=NC(r.set);return MC(e,(function(e){DC(n,r,t(e),e)})),r}});var FC=sC,UC=mC;kr({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function(e){var t=UC(FC(this),(function(t,r){if(t===e)return{key:r}}),!0);return t&&t.key}});var jC=Qt,BC=sC,WC=mC,GC=Kb.Map,ZC=Kb.set;kr({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function(e){var t=BC(this),r=jC(e,arguments.length>1?arguments[1]:void 0),n=new GC;return WC(t,(function(e,i){ZC(n,r(e,i,t),e)})),n}});var YC=Qt,HC=sC,XC=mC,JC=Kb.Map,KC=Kb.set;kr({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function(e){var t=HC(this),r=YC(e,arguments.length>1?arguments[1]:void 0),n=new JC;return XC(t,(function(e,i){KC(n,i,r(e,i,t))})),n}});var QC=sC,qC=Im,zC=Kb.set;kr({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function(e){for(var t=QC(this),r=arguments.length,n=0;n<r;)qC(arguments[n++],(function(e,r){zC(t,e,r)}),{AS_ENTRIES:!0});return t}});var $C=Le,eS=sC,tS=mC,rS=TypeError;kr({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=eS(this),r=arguments.length<2,n=r?void 0:arguments[1];if($C(e),tS(t,(function(i,o){r?(r=!1,n=i):n=e(n,i,o,t)})),r)throw rS("Reduce of empty map with no initial value");return n}});var nS=Qt,iS=sC,oS=mC;kr({target:"Map",proto:!0,real:!0,forced:!0},{some:function(e){var t=iS(this),r=nS(e,arguments.length>1?arguments[1]:void 0);return!0===oS(t,(function(e,n){if(r(e,n,t))return!0}),!0)}});var aS=Le,sS=sC,cS=TypeError,uS=Kb.get,lS=Kb.has,dS=Kb.set;kr({target:"Map",proto:!0,real:!0,forced:!0},{update:function(e,t){var r=sS(this),n=arguments.length;aS(t);var i=lS(r,e);if(!i&&n<3)throw cS("Updating absent value");var o=i?uS(r,e):aS(n>2?arguments[2]:void 0)(e,r);return dS(r,e,t(o,e,r)),r}});var pS=D,fS=Le,hS=k,mS=rr,vS=TypeError,yS=function(e,t){var r,n=mS(this),i=fS(n.get),o=fS(n.has),a=fS(n.set),s=arguments.length>2?arguments[2]:void 0;if(!hS(t)&&!hS(s))throw vS("At least one callback required");return pS(o,n,e)?(r=pS(i,n,e),hS(t)&&(r=t(r),pS(a,n,e,r))):hS(s)&&(r=s(),pS(a,n,e,r)),r};kr({target:"Map",proto:!0,real:!0,forced:!0},{upsert:yS}),kr({target:"Map",proto:!0,real:!0,name:"upsert",forced:!0},{updateOrInsert:yS});var gS=oC,bS=kv.codeAt;kr({target:"String",proto:!0},{codePointAt:function(e){return bS(this,e)}});var CS=Gr("String").codePointAt,SS=l,_S=CS,TS=String.prototype,wS=function(e){var t=e.codePointAt;return"string"==typeof e||e===TS||SS(TS,e)&&t===TS.codePointAt?_S:t},RS=kr,PS=u,ES=bi,kS=RangeError,AS=String.fromCharCode,xS=String.fromCodePoint,LS=PS([].join);RS({target:"String",stat:!0,arity:1,forced:!!xS&&1!==xS.length},{fromCodePoint:function(e){for(var t,r=[],n=arguments.length,i=0;n>i;){if(t=+arguments[i++],ES(t,1114111)!==t)throw kS(t+" is not a valid code point");r[i]=t<65536?AS(t):AS(55296+((t-=65536)>>10),t%1024+56320)}return LS(r,"")}});var IS=re.String.fromCodePoint,DS=kr,MS=Date,OS=u(MS.prototype.getTime);DS({target:"Date",stat:!0},{now:function(){return OS(new MS)}});var NS=re.Date.now,VS=re,FS=y;VS.JSON||(VS.JSON={stringify:JSON.stringify});var US=function(e,t,r){return FS(VS.JSON.stringify,null,arguments)},jS=US,BS=qe,WS=Bf,GS=A,ZS=qt,YS=te,HS=vr,XS=Error,JS=u("".replace),KS=String(XS("zxcasd").stack),QS=/\n\s*at [^:]*:[^\n]*/,qS=QS.test(KS),zS=j,$S=!n((function(){var e=Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",zS(1,7)),7!==e.stack)})),e_=vr,t_=function(e,t){if(qS&&"string"==typeof e&&!XS.prepareStackTrace)for(;t--;)e=JS(e,QS,"");return e},r_=$S,n_=Error.captureStackTrace,i_=Oi,o_=kr,a_=l,s_=Ca,c_=rs,u_=function(e,t,r){for(var n=WS(t),i=ZS.f,o=GS.f,a=0;a<n.length;a++){var s=n[a];BS(e,s)||r&&BS(r,s)||i(e,s,o(t,s))}},l_=da,d_=vr,p_=j,f_=function(e,t){YS(t)&&"cause"in t&&HS(e,"cause",t.cause)},h_=function(e,t,r,n){r_&&(n_?n_(e,t):e_(e,"stack",t_(r,n)))},m_=Im,v_=function(e,t){return void 0===e?arguments.length<2?"":t:i_(e)},y_=pt("toStringTag"),g_=Error,b_=[].push,C_=function(e,t){var r,n=a_(S_,this);c_?r=c_(g_(),n?s_(this):S_):(r=n?this:l_(S_),d_(r,y_,"Error")),void 0!==t&&d_(r,"message",v_(t)),h_(r,C_,r.stack,1),arguments.length>2&&f_(r,arguments[2]);var i=[];return m_(e,b_,{that:i}),d_(r,"errors",i),r};c_?c_(C_,g_):u_(C_,g_,{name:!0});var S_=C_.prototype=l_(g_.prototype,{constructor:p_(1,C_),message:p_(1,""),name:p_(1,"AggregateError")});o_({global:!0,constructor:!0,arity:2},{AggregateError:C_});var __,T_,w_,R_,P_="process"===S(p.process),E_=rr,k_=_g,A_=H,x_=pt("species"),L_=function(e,t){var r,n=E_(e).constructor;return void 0===n||A_(r=E_(n)[x_])?t:k_(r)},I_=/(?:ipad|iphone|ipod).*applewebkit/i.test(ce),D_=p,M_=y,O_=Qt,N_=k,V_=qe,F_=n,U_=Jo,j_=Ar,B_=Pt,W_=sc,G_=I_,Z_=P_,Y_=D_.setImmediate,H_=D_.clearImmediate,X_=D_.process,J_=D_.Dispatch,K_=D_.Function,Q_=D_.MessageChannel,q_=D_.String,z_=0,$_={},eT="onreadystatechange";F_((function(){__=D_.location}));var tT=function(e){if(V_($_,e)){var t=$_[e];delete $_[e],t()}},rT=function(e){return function(){tT(e)}},nT=function(e){tT(e.data)},iT=function(e){D_.postMessage(q_(e),__.protocol+"//"+__.host)};Y_&&H_||(Y_=function(e){W_(arguments.length,1);var t=N_(e)?e:K_(e),r=j_(arguments,1);return $_[++z_]=function(){M_(t,void 0,r)},T_(z_),z_},H_=function(e){delete $_[e]},Z_?T_=function(e){X_.nextTick(rT(e))}:J_&&J_.now?T_=function(e){J_.now(rT(e))}:Q_&&!G_?(R_=(w_=new Q_).port2,w_.port1.onmessage=nT,T_=O_(R_.postMessage,R_)):D_.addEventListener&&N_(D_.postMessage)&&!D_.importScripts&&__&&"file:"!==__.protocol&&!F_(iT)?(T_=iT,D_.addEventListener("message",nT,!1)):T_=eT in B_("script")?function(e){U_.appendChild(B_("script"))[eT]=function(){U_.removeChild(this),tT(e)}}:function(e){setTimeout(rT(e),0)});var oT={set:Y_,clear:H_},aT=function(){this.head=null,this.tail=null};aT.prototype={add:function(e){var t={item:e,next:null},r=this.tail;r?r.next=t:this.head=t,this.tail=t},get:function(){var e=this.head;if(e)return null===(this.head=e.next)&&(this.tail=null),e.item}};var sT,cT,uT,lT,dT,pT=aT,fT=/ipad|iphone|ipod/i.test(ce)&&"undefined"!=typeof Pebble,hT=/web0s(?!.*chrome)/i.test(ce),mT=p,vT=Qt,yT=A.f,gT=oT.set,bT=pT,CT=I_,ST=fT,_T=hT,TT=P_,wT=mT.MutationObserver||mT.WebKitMutationObserver,RT=mT.document,PT=mT.process,ET=mT.Promise,kT=yT(mT,"queueMicrotask"),AT=kT&&kT.value;if(!AT){var xT=new bT,LT=function(){var e,t;for(TT&&(e=PT.domain)&&e.exit();t=xT.get();)try{t()}catch(e){throw xT.head&&sT(),e}e&&e.enter()};CT||TT||_T||!wT||!RT?!ST&&ET&&ET.resolve?((lT=ET.resolve(void 0)).constructor=ET,dT=vT(lT.then,lT),sT=function(){dT(LT)}):TT?sT=function(){PT.nextTick(LT)}:(gT=vT(gT,mT),sT=function(){gT(LT)}):(cT=!0,uT=RT.createTextNode(""),new wT(LT).observe(uT,{characterData:!0}),sT=function(){uT.data=cT=!cT}),AT=function(e){xT.head||sT(),xT.add(e)}}var IT=AT,DT=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}},MT=p.Promise,OT="object"==typeof Deno&&Deno&&"object"==typeof Deno.version,NT=!OT&&!P_&&"object"==typeof window&&"object"==typeof document,VT=p,FT=MT,UT=k,jT=Ht,BT=Rn,WT=pt,GT=NT,ZT=OT,YT=me,HT=FT&&FT.prototype,XT=WT("species"),JT=!1,KT=UT(VT.PromiseRejectionEvent),QT=jT("Promise",(function(){var e=BT(FT),t=e!==String(FT);if(!t&&66===YT)return!0;if(!HT.catch||!HT.finally)return!0;if(!YT||YT<51||!/native code/.test(e)){var r=new FT((function(e){e(1)})),n=function(e){e((function(){}),(function(){}))};if((r.constructor={})[XT]=n,!(JT=r.then((function(){}))instanceof n))return!0}return!t&&(GT||ZT)&&!KT})),qT={CONSTRUCTOR:QT,REJECTION_EVENT:KT,SUBCLASSING:JT},zT={},$T=Le,ew=TypeError,tw=function(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw ew("Bad Promise constructor");t=e,r=n})),this.resolve=$T(t),this.reject=$T(r)};zT.f=function(e){return new tw(e)};var rw,nw,iw=kr,ow=P_,aw=p,sw=D,cw=_a,uw=Ba,lw=iv,dw=Le,pw=k,fw=te,hw=Om,mw=L_,vw=oT.set,yw=IT,gw=function(e,t){try{1===arguments.length?console.error(e):console.error(e,t)}catch(e){}},bw=DT,Cw=pT,Sw=To,_w=MT,Tw=zT,ww="Promise",Rw=qT.CONSTRUCTOR,Pw=qT.REJECTION_EVENT,Ew=Sw.getterFor(ww),kw=Sw.set,Aw=_w&&_w.prototype,xw=_w,Lw=Aw,Iw=aw.TypeError,Dw=aw.document,Mw=aw.process,Ow=Tw.f,Nw=Ow,Vw=!!(Dw&&Dw.createEvent&&aw.dispatchEvent),Fw="unhandledrejection",Uw=function(e){var t;return!(!fw(e)||!pw(t=e.then))&&t},jw=function(e,t){var r,n,i,o=t.value,a=1===t.state,s=a?e.ok:e.fail,c=e.resolve,u=e.reject,l=e.domain;try{s?(a||(2===t.rejection&&Yw(t),t.rejection=1),!0===s?r=o:(l&&l.enter(),r=s(o),l&&(l.exit(),i=!0)),r===e.promise?u(Iw("Promise-chain cycle")):(n=Uw(r))?sw(n,r,c,u):c(r)):u(o)}catch(e){l&&!i&&l.exit(),u(e)}},Bw=function(e,t){e.notified||(e.notified=!0,yw((function(){for(var r,n=e.reactions;r=n.get();)jw(r,e);e.notified=!1,t&&!e.rejection&&Gw(e)})))},Ww=function(e,t,r){var n,i;Vw?((n=Dw.createEvent("Event")).promise=t,n.reason=r,n.initEvent(e,!1,!0),aw.dispatchEvent(n)):n={promise:t,reason:r},!Pw&&(i=aw["on"+e])?i(n):e===Fw&&gw("Unhandled promise rejection",r)},Gw=function(e){sw(vw,aw,(function(){var t,r=e.facade,n=e.value;if(Zw(e)&&(t=bw((function(){ow?Mw.emit("unhandledRejection",n,r):Ww(Fw,r,n)})),e.rejection=ow||Zw(e)?2:1,t.error))throw t.value}))},Zw=function(e){return 1!==e.rejection&&!e.parent},Yw=function(e){sw(vw,aw,(function(){var t=e.facade;ow?Mw.emit("rejectionHandled",t):Ww("rejectionhandled",t,e.value)}))},Hw=function(e,t,r){return function(n){e(t,n,r)}},Xw=function(e,t,r){e.done||(e.done=!0,r&&(e=r),e.value=t,e.state=2,Bw(e,!0))},Jw=function(e,t,r){if(!e.done){e.done=!0,r&&(e=r);try{if(e.facade===t)throw Iw("Promise can't be resolved itself");var n=Uw(t);n?yw((function(){var r={done:!1};try{sw(n,t,Hw(Jw,r,e),Hw(Xw,r,e))}catch(t){Xw(r,t,e)}})):(e.value=t,e.state=1,Bw(e,!1))}catch(t){Xw({done:!1},t,e)}}};Rw&&(Lw=(xw=function(e){hw(this,Lw),dw(e),sw(rw,this);var t=Ew(this);try{e(Hw(Jw,t),Hw(Xw,t))}catch(e){Xw(t,e)}}).prototype,(rw=function(e){kw(this,{type:ww,done:!1,notified:!1,parent:!1,reactions:new Cw,rejection:!1,state:0,value:void 0})}).prototype=cw(Lw,"then",(function(e,t){var r=Ew(this),n=Ow(mw(this,xw));return r.parent=!0,n.ok=!pw(e)||e,n.fail=pw(t)&&t,n.domain=ow?Mw.domain:void 0,0===r.state?r.reactions.add(n):yw((function(){jw(n,r)})),n.promise})),nw=function(){var e=new rw,t=Ew(e);this.promise=e,this.resolve=Hw(Jw,t),this.reject=Hw(Xw,t)},Tw.f=Ow=function(e){return e===xw||undefined===e?new nw(e):Nw(e)}),iw({global:!0,constructor:!0,wrap:!0,forced:Rw},{Promise:xw}),uw(xw,ww,!1,!0),lw(ww);var Kw=pt("iterator"),Qw=!1;try{var qw=0,zw={next:function(){return{done:!!qw++}},return:function(){Qw=!0}};zw[Kw]=function(){return this},Array.from(zw,(function(){throw 2}))}catch(e){}var $w=MT,eR=function(e,t){if(!t&&!Qw)return!1;var r=!1;try{var n={};n[Kw]=function(){return{next:function(){return{done:r=!0}}}},e(n)}catch(e){}return r},tR=qT.CONSTRUCTOR||!eR((function(e){$w.all(e).then(void 0,(function(){}))})),rR=D,nR=Le,iR=zT,oR=DT,aR=Im;kr({target:"Promise",stat:!0,forced:tR},{all:function(e){var t=this,r=iR.f(t),n=r.resolve,i=r.reject,o=oR((function(){var r=nR(t.resolve),o=[],a=0,s=1;aR(e,(function(e){var c=a++,u=!1;s++,rR(r,t,e).then((function(e){u||(u=!0,o[c]=e,--s||n(o))}),i)})),--s||n(o)}));return o.error&&i(o.value),r.promise}});var sR=kr,cR=qT.CONSTRUCTOR;MT&&MT.prototype,sR({target:"Promise",proto:!0,forced:cR,real:!0},{catch:function(e){return this.then(void 0,e)}});var uR=D,lR=Le,dR=zT,pR=DT,fR=Im;kr({target:"Promise",stat:!0,forced:tR},{race:function(e){var t=this,r=dR.f(t),n=r.reject,i=pR((function(){var i=lR(t.resolve);fR(e,(function(e){uR(i,t,e).then(r.resolve,n)}))}));return i.error&&n(i.value),r.promise}});var hR=D,mR=zT;kr({target:"Promise",stat:!0,forced:qT.CONSTRUCTOR},{reject:function(e){var t=mR.f(this);return hR(t.reject,void 0,e),t.promise}});var vR=rr,yR=te,gR=zT,bR=function(e,t){if(vR(e),yR(t)&&t.constructor===e)return t;var r=gR.f(e);return(0,r.resolve)(t),r.promise},CR=kr,SR=MT,_R=qT.CONSTRUCTOR,TR=bR,wR=se("Promise"),RR=!_R;CR({target:"Promise",stat:!0,forced:true},{resolve:function(e){return TR(RR&&this===wR?SR:this,e)}});var PR=D,ER=Le,kR=zT,AR=DT,xR=Im;kr({target:"Promise",stat:!0,forced:tR},{allSettled:function(e){var t=this,r=kR.f(t),n=r.resolve,i=r.reject,o=AR((function(){var r=ER(t.resolve),i=[],o=0,a=1;xR(e,(function(e){var s=o++,c=!1;a++,PR(r,t,e).then((function(e){c||(c=!0,i[s]={status:"fulfilled",value:e},--a||n(i))}),(function(e){c||(c=!0,i[s]={status:"rejected",reason:e},--a||n(i))}))})),--a||n(i)}));return o.error&&i(o.value),r.promise}});var LR=D,IR=Le,DR=se,MR=zT,OR=DT,NR=Im,VR="No one promise resolved";kr({target:"Promise",stat:!0,forced:tR},{any:function(e){var t=this,r=DR("AggregateError"),n=MR.f(t),i=n.resolve,o=n.reject,a=OR((function(){var n=IR(t.resolve),a=[],s=0,c=1,u=!1;NR(e,(function(e){var l=s++,d=!1;c++,LR(n,t,e).then((function(e){d||u||(u=!0,i(e))}),(function(e){d||u||(d=!0,a[l]=e,--c||o(new r(a,VR)))}))})),--c||o(new r(a,VR))}));return a.error&&o(a.value),n.promise}});var FR=kr,UR=MT,jR=n,BR=se,WR=k,GR=L_,ZR=bR,YR=UR&&UR.prototype;FR({target:"Promise",proto:!0,real:!0,forced:!!UR&&jR((function(){YR.finally.call({then:function(){}},(function(){}))}))},{finally:function(e){var t=GR(this,BR("Promise")),r=WR(e);return this.then(r?function(r){return ZR(t,e()).then((function(){return r}))}:e,r?function(r){return ZR(t,e()).then((function(){throw r}))}:e)}});var HR=re.Promise,XR=zT;kr({target:"Promise",stat:!0},{withResolvers:function(){var e=XR.f(this);return{promise:e.promise,resolve:e.resolve,reject:e.reject}}});var JR=HR,KR=zT,QR=DT;kr({target:"Promise",stat:!0,forced:!0},{try:function(e){var t=KR.f(this),r=QR(e);return(r.error?t.reject:t.resolve)(r.value),t.promise}});var qR=JR,zR="\t\n\v\f\r 聽釟€鈥€鈥佲€傗€冣€勨€呪€嗏€団€堚€夆€娾€仧銆€\u2028\u2029\ufeff",$R=K,eP=Oi,tP=zR,rP=u("".replace),nP=RegExp("^["+tP+"]+"),iP=RegExp("(^|[^"+tP+"])["+tP+"]+$"),oP=function(e){return function(t){var r=eP($R(t));return 1&e&&(r=rP(r,nP,"")),2&e&&(r=rP(r,iP,"$1")),r}},aP={start:oP(1),end:oP(2),trim:oP(3)},sP=p,cP=n,uP=u,lP=Oi,dP=aP.trim,pP=zR,fP=sP.parseInt,hP=sP.Symbol,mP=hP&&hP.iterator,vP=/^[+-]?0x/i,yP=uP(vP.exec),gP=8!==fP(pP+"08")||22!==fP(pP+"0x16")||mP&&!cP((function(){fP(Object(mP))}))?function(e,t){var r=dP(lP(e));return fP(r,t>>>0||(yP(vP,r)?16:10))}:fP;kr({global:!0,forced:parseInt!==gP},{parseInt:gP});var bP=re.parseInt,CP=Gr("Array").entries,SP=Sn,_P=qe,TP=l,wP=CP,RP=Array.prototype,PP={DOMTokenList:!0,NodeList:!0},EP=function(e){var t=e.entries;return e===RP||TP(RP,e)&&t===RP.entries||_P(PP,SP(e))?wP:t},kP=x,AP=Qr,xP=TypeError,LP=Object.getOwnPropertyDescriptor,IP=kP&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}(),DP=Ee,MP=TypeError,OP=kr,NP=Je,VP=bi,FP=en,UP=an,jP=IP?function(e,t){if(AP(e)&&!LP(e,"length").writable)throw xP("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t},BP=cn,WP=Hn,GP=pn,ZP=function(e,t){if(!delete e[t])throw MP("Cannot delete property "+DP(t)+" of "+DP(e))},YP=Qn("splice"),HP=Math.max,XP=Math.min;OP({target:"Array",proto:!0,forced:!YP},{splice:function(e,t){var r,n,i,o,a,s,c=NP(this),u=UP(c),l=VP(e,u),d=arguments.length;for(0===d?r=n=0:1===d?(r=0,n=u-l):(r=d-2,n=XP(HP(FP(t),0),u-l)),BP(u+r-n),i=WP(c,n),o=0;o<n;o++)(a=l+o)in c&&GP(i,o,c[a]);if(i.length=n,r<n){for(o=l;o<u-n;o++)s=o+r,(a=o+n)in c?c[s]=c[a]:ZP(c,s);for(o=u;o>u-n+r;o--)ZP(c,o-1)}else if(r>n)for(o=u-n;o>l;o--)s=o+r-1,(a=o+n-1)in c?c[s]=c[a]:ZP(c,s);for(o=0;o<r;o++)c[o+l]=arguments[o+2];return jP(c,u-n+r),i}});var JP=Gr("Array").splice,KP=l,QP=JP,qP=Array.prototype,zP=function(e){var t=e.splice;return e===qP||KP(qP,e)&&t===qP.splice?QP:t},$P=Le,eE=Je,tE=Y,rE=an,nE=TypeError,iE=function(e){return function(t,r,n,i){$P(r);var o=eE(t),a=tE(o),s=rE(o),c=e?s-1:0,u=e?-1:1;if(n<2)for(;;){if(c in a){i=a[c],c+=u;break}if(c+=u,e?c<0:s<=c)throw nE("Reduce of empty array with no initial value")}for(;e?c>=0:s>c;c+=u)c in a&&(i=r(i,a[c],c,o));return i}},oE={left:iE(!1),right:iE(!0)}.left;kr({target:"Array",proto:!0,forced:!P_&&me>79&&me<83||!Js("reduce")},{reduce:function(e){var t=arguments.length;return oE(this,e,t,t>1?arguments[1]:void 0)}});var aE=Gr("Array").reduce,sE=l,cE=aE,uE=Array.prototype,lE=function(e){var t=e.reduce;return e===uE||sE(uE,e)&&t===uE.reduce?cE:t},dE=kr,pE=Hs.find,fE="find",hE=!0;fE in[]&&Array(1)[fE]((function(){hE=!1})),dE({target:"Array",proto:!0,forced:hE},{find:function(e){return pE(this,e,arguments.length>1?arguments[1]:void 0)}});var mE=Gr("Array").find,vE=l,yE=mE,gE=Array.prototype,bE=function(e){var t=e.find;return e===gE||vE(gE,e)&&t===gE.find?yE:t},CE={exports:{}};!function(e,t){self,e.exports=function(){var e={729:function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function o(e,t,n,o,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var s=new i(n,o||e,a),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],s]:e._events[c].push(s):(e._events[c]=s,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function s(){this._events=new n,this._eventsCount=0}Sl&&(n.prototype=Sl(null),(new n).__proto__||(r=!1)),s.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?jl(n).call(n,1):n);return vf?mi(i).call(i,vf(e)):i},s.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=new Array(o);i<o;i++)a[i]=n[i].fn;return a},s.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},s.prototype.emit=function(e,t,n,i,o,a){var s=r?r+e:e;if(!this._events[s])return!1;var c,u,l=this._events[s],d=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),d){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,n),!0;case 4:return l.fn.call(l.context,t,n,i),!0;case 5:return l.fn.call(l.context,t,n,i,o),!0;case 6:return l.fn.call(l.context,t,n,i,o,a),!0}for(u=1,c=new Array(d-1);u<d;u++)c[u-1]=arguments[u];l.fn.apply(l.context,c)}else{var p,f=l.length;for(u=0;u<f;u++)switch(l[u].once&&this.removeListener(e,l[u].fn,void 0,!0),d){case 1:l[u].fn.call(l[u].context);break;case 2:l[u].fn.call(l[u].context,t);break;case 3:l[u].fn.call(l[u].context,t,n);break;case 4:l[u].fn.call(l[u].context,t,n,i);break;default:if(!c)for(p=1,c=new Array(d-1);p<d;p++)c[p-1]=arguments[p];l[u].fn.apply(l[u].context,c)}}return!0},s.prototype.on=function(e,t,r){return o(this,e,t,r,!1)},s.prototype.once=function(e,t,r){return o(this,e,t,r,!0)},s.prototype.removeListener=function(e,t,n,i){var o=r?r+e:e;if(!this._events[o])return this;if(!t)return a(this,o),this;var s=this._events[o];if(s.fn)s.fn!==t||i&&!s.once||n&&s.context!==n||a(this,o);else{for(var c=0,u=[],l=s.length;c<l;c++)(s[c].fn!==t||i&&!s[c].once||n&&s[c].context!==n)&&u.push(s[c]);u.length?this._events[o]=1===u.length?u[0]:u:a(this,o)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s},961:function(e,t,r){var n,i=function(){var e=String.fromCharCode,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",n={};function i(e,t){if(!n[e]){n[e]={};for(var r=0;r<e.length;r++)n[e][e.charAt(r)]=r}return n[e][t]}var o={compressToBase64:function(e){if(null==e)return"";var r=o._compress(e,6,(function(e){return t.charAt(e)}));switch(r.length%4){default:case 0:return r;case 1:return r+"===";case 2:return r+"==";case 3:return r+"="}},decompressFromBase64:function(e){return null==e?"":""==e?null:o._decompress(e.length,32,(function(r){return i(t,e.charAt(r))}))},compressToUTF16:function(t){return null==t?"":o._compress(t,15,(function(t){return e(t+32)}))+" "},decompressFromUTF16:function(e){return null==e?"":""==e?null:o._decompress(e.length,16384,(function(t){return e.charCodeAt(t)-32}))},compressToUint8Array:function(e){for(var t=o.compress(e),r=new Uint8Array(2*t.length),n=0,i=t.length;n<i;n++){var a=t.charCodeAt(n);r[2*n]=a>>>8,r[2*n+1]=a%256}return r},decompressFromUint8Array:function(t){if(null==t)return o.decompress(t);for(var r=new Array(t.length/2),n=0,i=r.length;n<i;n++)r[n]=256*t[2*n]+t[2*n+1];var a=[];return ic(r).call(r,(function(t){a.push(e(t))})),o.decompress(a.join(""))},compressToEncodedURIComponent:function(e){return null==e?"":o._compress(e,6,(function(e){return r.charAt(e)}))},decompressFromEncodedURIComponent:function(e){return null==e?"":""==e?null:(e=e.replace(/ /g,"+"),o._decompress(e.length,32,(function(t){return i(r,e.charAt(t))})))},compress:function(t){return o._compress(t,16,(function(t){return e(t)}))},_compress:function(e,t,r){if(null==e)return"";var n,i,o,a={},s={},c="",u="",l="",d=2,p=3,f=2,h=[],m=0,v=0;for(o=0;o<e.length;o+=1)if(c=e.charAt(o),Object.prototype.hasOwnProperty.call(a,c)||(a[c]=p++,s[c]=!0),u=l+c,Object.prototype.hasOwnProperty.call(a,u))l=u;else{if(Object.prototype.hasOwnProperty.call(s,l)){if(l.charCodeAt(0)<256){for(n=0;n<f;n++)m<<=1,v==t-1?(v=0,h.push(r(m)),m=0):v++;for(i=l.charCodeAt(0),n=0;n<8;n++)m=m<<1|1&i,v==t-1?(v=0,h.push(r(m)),m=0):v++,i>>=1}else{for(i=1,n=0;n<f;n++)m=m<<1|i,v==t-1?(v=0,h.push(r(m)),m=0):v++,i=0;for(i=l.charCodeAt(0),n=0;n<16;n++)m=m<<1|1&i,v==t-1?(v=0,h.push(r(m)),m=0):v++,i>>=1}0==--d&&(d=Math.pow(2,f),f++),delete s[l]}else for(i=a[l],n=0;n<f;n++)m=m<<1|1&i,v==t-1?(v=0,h.push(r(m)),m=0):v++,i>>=1;0==--d&&(d=Math.pow(2,f),f++),a[u]=p++,l=String(c)}if(""!==l){if(Object.prototype.hasOwnProperty.call(s,l)){if(l.charCodeAt(0)<256){for(n=0;n<f;n++)m<<=1,v==t-1?(v=0,h.push(r(m)),m=0):v++;for(i=l.charCodeAt(0),n=0;n<8;n++)m=m<<1|1&i,v==t-1?(v=0,h.push(r(m)),m=0):v++,i>>=1}else{for(i=1,n=0;n<f;n++)m=m<<1|i,v==t-1?(v=0,h.push(r(m)),m=0):v++,i=0;for(i=l.charCodeAt(0),n=0;n<16;n++)m=m<<1|1&i,v==t-1?(v=0,h.push(r(m)),m=0):v++,i>>=1}0==--d&&(d=Math.pow(2,f),f++),delete s[l]}else for(i=a[l],n=0;n<f;n++)m=m<<1|1&i,v==t-1?(v=0,h.push(r(m)),m=0):v++,i>>=1;0==--d&&(d=Math.pow(2,f),f++)}for(i=2,n=0;n<f;n++)m=m<<1|1&i,v==t-1?(v=0,h.push(r(m)),m=0):v++,i>>=1;for(;;){if(m<<=1,v==t-1){h.push(r(m));break}v++}return h.join("")},decompress:function(e){return null==e?"":""==e?null:o._decompress(e.length,32768,(function(t){return e.charCodeAt(t)}))},_decompress:function(t,r,n){var i,o,a,s,c,u,l,d=[],p=4,f=4,h=3,m="",v=[],y={val:n(0),position:r,index:1};for(i=0;i<3;i+=1)d[i]=i;for(a=0,c=Math.pow(2,2),u=1;u!=c;)s=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),a|=(s>0?1:0)*u,u<<=1;switch(a){case 0:for(a=0,c=Math.pow(2,8),u=1;u!=c;)s=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),a|=(s>0?1:0)*u,u<<=1;l=e(a);break;case 1:for(a=0,c=Math.pow(2,16),u=1;u!=c;)s=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),a|=(s>0?1:0)*u,u<<=1;l=e(a);break;case 2:return""}for(d[3]=l,o=l,v.push(l);;){if(y.index>t)return"";for(a=0,c=Math.pow(2,h),u=1;u!=c;)s=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),a|=(s>0?1:0)*u,u<<=1;switch(l=a){case 0:for(a=0,c=Math.pow(2,8),u=1;u!=c;)s=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),a|=(s>0?1:0)*u,u<<=1;d[f++]=e(a),l=f-1,p--;break;case 1:for(a=0,c=Math.pow(2,16),u=1;u!=c;)s=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),a|=(s>0?1:0)*u,u<<=1;d[f++]=e(a),l=f-1,p--;break;case 2:return v.join("")}if(0==p&&(p=Math.pow(2,h),h++),d[l])m=d[l];else{if(l!==f)return null;m=o+o.charAt(0)}v.push(m),d[f++]=o+m.charAt(0),o=m,0==--p&&(p=Math.pow(2,h),h++)}}};return o}();void 0===(n=function(){return i}.call(t,r,t,e))||(e.exports=n)}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var o=t[n]={exports:{}};return e[n](o,o.exports,r),o.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Tf(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var n={};return function(){r.d(n,{default:function(){return Jt}});var e=r(729),t=r.n(e),i="LEBP2PInnerEvent",o="LEBP2PEvent",a={ERR_LOAD_TIMEOUT:1003,ERR_LOAD_REACH_MAX_RETRY:1004,ERR_PLAY_WEBRTC_FAIL:1005,ERR_FETCH_REQUEST_FAIL:1006,ERR_PLAY_REQUEST_PULL_FAIL:1007,ERR_PLAY_REQUEST_STOP_FAIL:1008,INF_PLAY_EVT_SERVER_CONNECTED:3001,INF_PLAY_EVT_SERVER_RECONNECT:3002,INF_PLAY_EVT_REQUEST_PULL_BEGIN:3003,INF_PLAY_EVT_REQUEST_PULL_SUCCESS:3004,INF_PLAY_EVT_STREAM_FIRST_AUDIO_FRAME:3005,INF_PLAY_EVT_STREAM_FIRST_VIDEO_FRAME:3006,INF_PLAY_EVT_STREAM_CLOSED:3007,INF_PLAY_EVT_STREAM_SWITCH:3008};function s(e,t){var r=Pf(e);if(vf){var n=vf(e);t&&(n=ru(n).call(n,(function(t){return Of(e,t).enumerable}))),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r,n,i=null!=arguments[t]?arguments[t]:{};t%2?ic(r=s(Object(i),!0)).call(r,(function(t){u(e,t,i[t])})):Hf?$f(e,Hf(i)):ic(n=s(Object(i))).call(n,(function(t){Tf(e,t,Of(i,t))}))}return e}function u(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var l,d={streamUrl:"",streamId:function(){return function(e){var t=e.split("?")[0];return jl(t).call(t,hh(t).call(t,"/")+1,Oc(t).call(t,"?"))}(this.streamUrl)},pullStreamDomain:"https://overseas-webrtc.liveplay.myqcloud.com",stopStreamDomain:"https://overseas-webrtc.liveplay.myqcloud.com",get pullStreamUrl(){return"".concat(this.pullStreamDomain,"/webrtc/v1/pullstream")},get stopStreamUrl(){return"".concat(this.stopStreamDomain,"/webrtc/v1/stopstream")}},p=c(c(c(c(c(c(c(c({},{cloudAppid:0,xp2pAppid:0,token:"",xp2pDomain:""}),d),{confBaseUrl:"https://conf.qvb.qcloud.com/api/v3/live/h5/",reportUrl:"https://log.qvb.qcloud.com/reporter/vlive",signal:"wss://signal.qvb.qcloud.com/",stun:"127.0.0.1"}),{trackerUrl:"https://tracker-00.qvb.qcloud.com/api/tracker/v2/htbt",trackerInterval:5e3,trackerVersion:"v1"}),{maxCandidate:5,maxConnected:5,maxConnecting:5,tickInterval:1e4}),{stuckFrameRateRatio:.6}),{subscribeCoolingTime:2e4}),{connectRetryCount:3,connectRetryDelay:1e3}),f=function(){function e(){var e,t,r;e=this,t="config",r=Ah({},p),t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}return e.prototype.update=function(e){Ah(this.config,e)},e.prototype.get=function(e){return this.config[e]},e.prototype.set=function(e,t){this.config[e]=t},e}();function h(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e){e[e.base=0]="base",e[e.number=1]="number",e[e.boolean=2]="boolean",e[e.string=3]="string",e[e.array=4]="array",e[e.object=5]="object"}(l||(l={}));var m=function(){function e(e){var t=e.initVal,r=e.defaultVal;h(this,"type",l.base),h(this,"val",void 0),this.val=void 0===t?r:t}return e.prototype.set=function(e){return this.val=e,this},Tf(e.prototype,"value",{get:function(){return this.val},enumerable:!1,configurable:!0}),e.prototype.isString=function(){return this.type===l.string},e.prototype.isBoolean=function(){return this.type===l.boolean},e.prototype.isNumber=function(){return this.type===l.number},e}(),v=function(e){function t(t){var r=this;return h(r=e.call(this,{initVal:t,defaultVal:0})||this,"type",l.number),r}return iu(t,e),t.prototype.plus=function(e){this.val+=e},t.prototype.minus=function(e){this.val-=e},t}(m),y=function(e){function t(t){var r=this;return h(r=e.call(this,{initVal:t,defaultVal:!1})||this,"type",l.boolean),r}return iu(t,e),t}(m),g=function(e){function t(t){var r=this;return h(r=e.call(this,{initVal:t,defaultVal:""})||this,"type",l.string),r}return iu(t,e),t.prototype.append=function(e){this.val+=e},t}(m),b=function(e){function t(t){var r=this;return h(r=e.call(this,{initVal:t,defaultVal:[]})||this,"type",l.array),r}return iu(t,e),t}(m),C=function(e){function t(t){var r=this;return h(r=e.call(this,{initVal:t,defaultVal:{}})||this,"type",l.object),r}return iu(t,e),t}(m),S=function(){function e(){}return e.create=function(e,t){switch(e){case l.number:return new v(t);case l.boolean:return new y(t);case l.string:return new g(t);case l.array:return new b(t);case l.object:return new C(t)}},e}();function _(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var T=function(){function e(){_(this,"config",{deleteZeroNumber:!0,fieldsReportedWithZeroNumber:[],fieldsReservedValueAfterReport:[]}),_(this,"registeredReportCallback",new Yb),_(this,"storedFields",new gS),_(this,"preHooks",new gS)}return e.prototype.destroy=function(){this.config=null,this.reset()},e.prototype.configure=function(e){this.config=e},e.prototype.reset=function(){this.registeredReportCallback.clear(),this.preHooks.clear()},e.prototype.string=function(e){return this.findOrCreate(l.string,e)},e.prototype.number=function(e){return this.findOrCreate(l.number,e)},e.prototype.bool=function(e){return this.findOrCreate(l.boolean,e)},e.prototype.array=function(e){return this.findOrCreate(l.array,e)},e.prototype.object=function(e){return this.findOrCreate(l.object,e)},e.prototype.deleteField=function(e){this.storedFields.delete(e)},e.prototype.generate=function(){var e,t,r=this;ic(e=this.preHooks).call(e,(function(e){e()}));var n={};return ic(t=this.storedFields).call(t,(function(e,t){var i,o;r.config&&(!r.config.deleteZeroNumber||$i(i=r.config.fieldsReportedWithZeroNumber).call(i,t)||e.isBoolean()||e.isString()||e.value)&&(n[t]=e.value,$i(o=r.config.fieldsReservedValueAfterReport).call(o,t)||r.storedFields.delete(t))})),n},e.prototype.registerPreHook=function(e,t){this.preHooks.has(e)||this.preHooks.set(e,t)},e.prototype.deletePreHook=function(e){this.preHooks.delete(e)},e.prototype.findOrCreate=function(e,t,r){var n=this.storedFields.get(t);return n?(n.type!==e&&(n=S.create(e,r),this.storedFields.set(t,n)),n):(n=S.create(e,r),this.storedFields.set(t,n),n)},e}(),w=function(){function e(){_(this,"reportUnits",new gS)}return e.prototype.registerReportUint=function(e,t){this.reportUnits.has(e)||this.reportUnits.set(e,t)},e.prototype.deleteReportUint=function(e){this.reportUnits.delete(e)},e.prototype.generate=function(){var e,t={};return ic(e=this.reportUnits).call(e,(function(e){Ah(t,e.generate())})),t},e}();function R(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var P=0,E=function(e){function t(){var t=this;return R(t=e.call(this)||this,"tagName","Base"),R(t,"objId",0),R(t,"reporter",null),R(t,"bus",null),t.objId=P,P+=1,t}return iu(t,e),Tf(t.prototype,"objName",{get:function(){var e;return mi(e="".concat(this.tagName,"_")).call(e,this.objId)},enumerable:!1,configurable:!0}),t.prototype.init=function(){},t.prototype.destroy=function(){this.reporter=null,this.removeAllListeners()},t.prototype.setReporter=function(e){return e&&(this.reporter=e),this},t.prototype.setBus=function(e){return this.bus=e,this},t}(t());function k(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var A=function(e){function t(){var t=this;return k(t=e.call(this)||this,"tagName","MediaStreamProcessor"),k(t,"mediaStream",void 0),k(t,"trackGenerator",void 0),k(t,"vwriter",void 0),k(t,"destroyed",!1),t.mediaStream=new MediaStream,t.trackGenerator=new MediaStreamTrackGenerator({kind:"video"}),t.vwriter=t.trackGenerator.writable.getWriter(),t.mediaStream.addTrack(t.trackGenerator),t}return iu(t,e),t.prototype.destroy=function(){this.destroyed=!0,this.vwriter.abort(),this.stopTrack()},t.prototype.getMediaObj=function(){return this.mediaStream},t.prototype.addMediaStreamTrack=function(e){"video"===e.kind?this.onNewVideoTrack(e):"audio"===e.kind&&this.onNewAudioTrack(e)},t.prototype.onNewVideoTrack=function(e){var t=this,r=new MediaStreamTrackProcessor({track:e}),n=new MediaStreamTrackGenerator({kind:"video"}),i=new TransformStream({transform:function(e,r){t.destroyed||t.vwriter.write(e)}});r.readable.pipeThrough(i).pipeTo(n.writable)},t.prototype.onNewAudioTrack=function(e){this.replaceTrack(this.mediaStream,e)},t.prototype.replaceTrack=function(e,t){var r,n,i=e.getTracks();try{for(var o=uu(i),a=o.next();!a.done;a=o.next()){var s=a.value;t.kind===s.kind&&e.removeTrack(s)}}catch(e){r={error:e}}finally{try{a&&!a.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}e.addTrack(t)},t.prototype.stopTrack=function(){var e;ic(e=this.mediaStream.getTracks()).call(e,(function(e){e.stop()}))},t}(E);function x(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var L=function(){function e(e,t,r,n){x(this,"config",void 0),x(this,"requestInfo",void 0),x(this,"init",void 0),x(this,"callbacks",void 0),x(this,"retryTimes",0),x(this,"timeoutTimer",null),x(this,"abortController",null),x(this,"stats",{}),x(this,"res",null),this.config=e,this.requestInfo=t,this.init=r,this.callbacks=n,this.load()}return e.prototype.destroy=function(){this.clearCallbacks(),this.clearTimer(),this.abort()},e.prototype.stat=function(){},e.prototype.abort=function(){var e;this.clearTimer(),null===(e=this.abortController)||void 0===e||e.abort()},e.prototype.load=function(){this.abort(),this.setupRequest()},e.prototype.retry=function(){if(this.abort(),this.retryTimes>=this.config.retry)return this.callbacks.onTimeout(),void this.destroy();this.retryTimes+=1,this.load()},e.prototype.setupRequest=function(){var e=this;this.abortController=new AbortController,this.init.signal=this.abortController.signal,this.res=fetch(this.requestInfo,this.init),this.setupResponse(this.res),this.clearTimer(),this.timeoutTimer=Rc((function(){e.retry()}),this.config.timeout||5e3)},e.prototype.setupResponse=function(e){var t=this;e.then((function(e){return t.clearTimer(),e})).then((function(e){return e.ok?"text"===t.config.resType?e.text():"arraybuffer"===t.config.resType?e.arrayBuffer():e.json():(t.callbacks.onError({status:e.status}),void t.destroy())})).then((function(e){t.callbacks.onSuccess({resData:e}),t.destroy()})).catch((function(e){t.callbacks.onError({status:0}),t.destroy()}))},e.prototype.clearTimer=function(){this.timeoutTimer&&(clearTimeout(this.timeoutTimer),this.timeoutTimer=null)},e.prototype.clearCallbacks=function(){this.callbacks={onError:function(){},onSuccess:function(){},onTimeout:function(){}}},e}();function I(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var D=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return I(n=e.apply(this,du([],lu(t),!1))||this,"req",null),I(n,"config",null),n}return iu(t,e),t.prototype.configure=function(e){this.config=e},t.prototype.destroy=function(){this.abort()},t.prototype.load=function(){var e,t,r;if(this.abort(),this.config){var n=this.createUrl();this.req=new L({retry:2,timeout:5e3,resType:"text"},n,{method:"GET"},{onError:Jr(e=this.onError).call(e,this),onSuccess:Jr(t=this.onSuccess).call(t,this),onTimeout:Jr(r=this.onTimeout).call(r,this)})}},t.prototype.onTimeout=function(){this.emit("EVENT_CONF_LOADER_TIMEOUT")},t.prototype.onSuccess=function(e){var t=e.resData;this.emit("EVENT_CONF_LOADER_LOADED",function(e){for(var t="",r=[99,117,105],n=0;n<e.length;n++){var i=wS(e).call(e,n)^r[n%r.length];t+=IS(i)}return JSON.parse(t)}(t))},t.prototype.onError=function(e){e.status,this.emit("EVENT_CONF_LOADER_ERROR")},t.prototype.createUrl=function(){var e,t,r;if(!this.config)throw Error("[ConfLoader] [createUrl] empty conf");return mi(e=mi(t=mi(r="".concat(this.config.confBaseUrl)).call(r,this.config.streamId,"?domain=")).call(t,this.config.domain,"&d=0&timestamp=")).call(e,NS())},t.prototype.abort=function(){this.req&&(this.req.destroy(),this.req=null)},t}(e.EventEmitter);function M(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var O=function(){function e(){M(this,"config",null),M(this,"confLoader",new D),M(this,"confTimer",null)}return e.prototype.destroy=function(){this.stop(),this.config=null,this.confLoader.destroy()},e.prototype.configure=function(e){this.config=e,this.confLoader.configure(e)},e.prototype.start=function(){var e=this;if(this.config&&!this.confTimer&&this.config.confInterval){var t=Math.max(this.config.confInterval,6e4);this.confTimer=wc((function(){e.confLoader.load()}),t)}},e.prototype.stop=function(){this.clearTimer()},e.prototype.restart=function(){this.stop(),this.start()},Tf(e.prototype,"loader",{get:function(){return this.config?this.confLoader:null},enumerable:!1,configurable:!0}),e.prototype.clearTimer=function(){this.confTimer&&(clearTimeout(this.confTimer),this.confTimer=null)},e}(),N=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n,i,o,a=this;return n=a=e.apply(this,du([],lu(t),!1))||this,i="confService",o=new O,i in n?Tf(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o,a}return iu(t,e),t.prototype.destroy=function(){this.confService.destroy()},t.prototype.configure=function(e){this.confService.configure(e);var t=this.confService.loader;t&&this.setupLoader(t)},t.prototype.start=function(){this.confService.start()},t.prototype.load=function(){this.confService.loader&&this.confService.loader.load()},t.prototype.setupLoader=function(e){var t=this;e.on("EVENT_CONF_LOADER_LOADED",(function(e){t.emit("EVENT_CONF_LOADED",e)})),e.on("EVENT_CONF_LOADER_TIMEOUT",(function(){})),e.on("EVENT_CONF_LOADER_ERROR",(function(){}))},t}(e.EventEmitter);function V(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var F=function(){function e(){V(this,"config",null),V(this,"backgroundReportFlag",!1),V(this,"currentReportReq",null)}return e.prototype.destroy=function(){this.config=null,this.clearReportReq()},e.prototype.configure=function(e){this.config=e},e.prototype.enableBackgroundReport=function(){this.backgroundReportFlag=!0},e.prototype.disableBackgroundReport=function(){this.backgroundReportFlag=!1},e.prototype.reportString=function(e){var t=function(e){for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=[99,117,105],i=0;i<e.length;i++)r[i]=wS(e).call(e,i)^n[i%n.length];return r}(e);this.report(t)},e.prototype.report=function(e){this.config&&(this.backgroundReportFlag?this.sendBackground(this.config.reportUrl,e):this.sendWithFetch(this.config.reportUrl,e))},e.prototype.sendBackground=function(e,t){var r;null!==(r=navigator)&&void 0!==r&&r.sendBeacon&&navigator.sendBeacon(e,t)},e.prototype.sendWithFetch=function(e,t){this.currentReportReq=new L({retry:2,timeout:6e3,resType:"text"},e,{method:"POST",headers:{"Content-Type":"application/octet-stream"},body:t},{onError:function(){},onSuccess:function(){},onTimeout:function(){}})},e.prototype.clearReportReq=function(){this.currentReportReq&&(this.currentReportReq.destroy(),this.currentReportReq=null)},e}();function U(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var j=function(){function e(){U(this,"beforeUnloadListener",null),U(this,"beforeUnloadCallback",null),U(this,"collectCallback",null),U(this,"reportTimer",null),U(this,"reporter",new F),U(this,"config",null)}return e.prototype.destroy=function(){var e;this.stop(),this.collectCallback=null,this.config=null,this.removeBeforeUnloadReport(),null===(e=this.reporter)||void 0===e||e.destroy(),this.reporter=null},e.prototype.configure=function(e){var t;this.config=e,null===(t=this.reporter)||void 0===t||t.configure(e)},e.prototype.setCollectCallback=function(e){this.collectCallback=e},e.prototype.start=function(){var e,t=this;this.reportTimer||(this.reportTimer=wc((function(){t.autoCollectAndReport()}),Math.max((null===(e=this.config)||void 0===e?void 0:e.interval)||0,6e4)))},e.prototype.stop=function(){this.clearTimer()},e.prototype.restart=function(){this.stop(),this.start()},e.prototype.backgroundAutoReport=function(){var e,t;null===(e=this.reporter)||void 0===e||e.enableBackgroundReport(),this.autoCollectAndReport(),null===(t=this.reporter)||void 0===t||t.disableBackgroundReport()},e.prototype.autoCollectAndReport=function(){var e;if(this.collectCallback){var t=this.collectCallback(),r=jS(t);null===(e=this.reporter)||void 0===e||e.reportString(r)}},e.prototype.manualReport=function(e){var t,r=jS(e);null===(t=this.reporter)||void 0===t||t.reportString(r)},e.prototype.enableBeforeUnloadReport=function(e){var t=this;this.removeBeforeUnloadReport(),this.beforeUnloadCallback=e||null,this.beforeUnloadListener=function(){t.beforeUnloadCallback&&t.beforeUnloadCallback(),t.backgroundAutoReport()},window.addEventListener("beforeunload",this.beforeUnloadListener)},e.prototype.removeBeforeUnloadReport=function(){this.beforeUnloadListener&&(this.beforeUnloadCallback=null,window.removeEventListener("beforeunload",this.beforeUnloadListener),this.beforeUnloadListener=null)},e.prototype.clearTimer=function(){this.reportTimer&&(clearInterval(this.reportTimer),this.reportTimer=null)},e}(),B=function(){var e="qvb_leb_uuid",t=localStorage.getItem(e);return t||(t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})),localStorage.setItem(e,t)),t},W="",G=function(){return W};B();var Z="1.0.6";function Y(e,t){var r=Pf(e);if(vf){var n=vf(e);t&&(n=ru(n).call(n,(function(t){return Of(e,t).enumerable}))),r.push.apply(r,n)}return r}function H(e){for(var t=1;t<arguments.length;t++){var r,n,i=null!=arguments[t]?arguments[t]:{};t%2?ic(r=Y(Object(i),!0)).call(r,(function(t){X(e,t,i[t])})):Hf?$f(e,Hf(i)):ic(n=Y(Object(i))).call(n,(function(t){Tf(e,t,Of(i,t))}))}return e}function X(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var J,K=H(H(H(H({},{}),{}),{CDN_BYTES:"cdn_bytes",P2P_BYTES:"p2p_bytes"}),{appid:"appid",platform:"platform",type:"type",partner:"partner",uuid:"str_user_id",playid:"str_play_id"});function Q(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e){e[e.beforeunload=0]="beforeunload",e[e.destroy=1]="destroy",e[e.rollback=2]="rollback"}(J||(J={}));var q,z=function(){function e(){var e;Q(this,"reportService",new j),Q(this,"dataCollecor",new w),Q(this,"basicReportUnit",new T),Q(this,"config",null),Q(this,"startTime",NS()),this.basicReportUnit.configure({deleteZeroNumber:!0,fieldsReportedWithZeroNumber:[],fieldsReservedValueAfterReport:[]}),this.reportService.setCollectCallback(Jr(e=this.collectCallback).call(e,this))}return e.prototype.destroy=function(){this.tryToSetCloseReason(J.destroy),this.reportService.backgroundAutoReport(),this.reportService.destroy()},e.prototype.configure=function(e){return this.config=e,this.reportService.configure(e),this},e.prototype.init=function(){var e=this;this.reportService.enableBeforeUnloadReport((function(){e.tryToSetCloseReason(J.beforeunload)}))},e.prototype.start=function(){this.reportService.start()},e.prototype.registerReportUint=function(e,t){this.dataCollecor.registerReportUint(e,t)},e.prototype.deleteReportUint=function(e){this.dataCollecor.deleteReportUint(e)},e.prototype.tryToSetCloseReason=function(e){e!==J.beforeunload?e!==J.rollback?e===J.destroy&&(this.basicReportUnit.string("exit_reason").value||this.basicReportUnit.string("exit_reason").set("destroy")):this.basicReportUnit.string("exit_reason").set("rollback"):this.basicReportUnit.string("exit_reason").set("close")},e.prototype.initBasicReportField=function(){if(!this.config)throw Error("report missing config");var e=this.basicReportUnit;return e.number(K.appid).set(this.config.cloudAppid),e.number(K.partner).set(this.config.xp2pAppid),e.number(K.platform).set(31),e.string(K.uuid).set(B()),e.string(K.playid).set(G()),e.string("referer").set(window.location.href),e.string("user_agent").set(window.navigator.userAgent),e.number("cur_time").set(NS()),e.string("version").set(Z),e.number("data_time").set(Math.round(NS()/1e3)),e.number("data_type").set(2),e.string("channel").set(this.config.streamId()),e.string("url").set(this.config.streamUrl),e.string("str_video_type").set("webrtc"),e.generate()},e.prototype.collectCallback=function(){var e=this.initBasicReportField();return this.basicReportUnit.number("play_started").set(Math.floor(NS()-this.startTime)/1e3),e.i=this.dataCollecor.generate(),e},e}();function $(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e){e[e.new=0]="new",e[e.connecting=1]="connecting",e[e.connected=2]="connected",e[e.disconnected=3]="disconnected",e[e.closed=4]="closed",e[e.fail=5]="fail",e[e.timeout=6]="timeout"}(q||(q={}));var ee,te="closed",re="timeout",ne="disconnect",ie="track",oe="connected",ae="error",se="failed",ce="stuck",ue=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return $(n=e.apply(this,du([],lu(t),!1))||this,"videoEncodedFrameCache",null),$(n,"audioEncodedFrameCache",null),$(n,"sourceConfig",null),$(n,"peer",null),$(n,"statUnit",null),$(n,"reportUnit",new T),$(n,"timeoutTimer",null),$(n,"closed",!1),$(n,"peerManager",null),$(n,"syncController",null),$(n,"streamInfo",null),n}return iu(t,e),t.prototype.destroy=function(){this.peerManager=null,this.syncController=null,this.peer=null,this.streamInfo=null,e.prototype.destroy.call(this)},t.prototype.load=function(){this.setTimeout()},t.prototype.close=function(){var e,t=this;if(!this.sourceConfig)throw new Error;this.peerManager&&(this.closed=!0,this.clearTimeout(),null===(e=this.peerManager.getPeer(this.sourceConfig.srcPid))||void 0===e||e.getConnection().destroy(),Rc((function(){t.destroy()})))},t.prototype.isLeb=function(){return!1},t.prototype.setPeerManager=function(e){return this.peerManager=e,this},t.prototype.setStatUnit=function(e){return this.statUnit=e,this},Tf(t.prototype,"pid",{get:function(){var e;return null===(e=this.sourceConfig)||void 0===e?void 0:e.srcPid},enumerable:!1,configurable:!0}),t.prototype.setReporter=function(t){return null==t||t.registerReportUint(this.objName,this.reportUnit),e.prototype.setReporter.call(this,t)},t.prototype.setEncodedVideoChunkCache=function(e){return this.videoEncodedFrameCache=e,this},t.prototype.setEncodedAudioChunkCache=function(e){return this.audioEncodedFrameCache=e,this},t.prototype.setSyncController=function(e){return this.syncController=e,this},t.prototype.setStreamInfo=function(e){return this.streamInfo=e,this},t.prototype.setTimeout=function(){var e=this;this.timeoutTimer=Rc((function(){e.emit(re)}),5e3)},t.prototype.clearTimeout=function(){this.timeoutTimer&&(clearTimeout(this.timeoutTimer),this.timeoutTimer=null)},t.prototype.listenConnection=function(e){var t,r=this,n={srcPid:null===(t=this.sourceConfig)||void 0===t?void 0:t.srcPid};e.on("closed",(function(){r.emit(te,n)})),e.on("failed",(function(){r.emit(se,n)})),e.on("disconnected",(function(){r.emit(ne,n)})),e.on("connected",(function(){r.emit(oe,n)})),e.on("error",(function(e){r.emit(ae,e)})),e.on(i,(function(e){r.emit(i,e)})),e.pc.setOnTrackCallback((function(t){r.clearTimeout(),r.onTrack(t,e)}))},t.prototype.onTrack=function(e,t){},t.prototype.emitTrack=function(e,t){var r;this.emit(ie,{srcPid:null===(r=this.sourceConfig)||void 0===r?void 0:r.srcPid,track:e,rtcPeerConnction:t.getRTCPeerConnection()})},t}(E);function le(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e){e.P2P="P2P",e.CDN="CDN",e.None="None"}(ee||(ee={}));var de=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return le(n=e.apply(this,du([],lu(t),!1))||this,"tagName","Dispatch"),le(n,"mediaStreamProcessor",null),le(n,"streamUrl",""),le(n,"cdnPid",""),le(n,"p2pTimer",null),le(n,"subscriber",null),le(n,"config",null),le(n,"sourceType",ee.None),le(n,"reportUnit",new T),le(n,"statsVar",{loadok:!1,loadStartTime:0}),le(n,"cdnRetryCtrl",{totalRetryTimes:3,leftRetryTimes:3,retryDelay:0,reset:function(){this.leftRetryTimes=this.totalRetryTimes},retry:function(){this.leftRetryTimes-=1},allowedToRetry:function(){return this.leftRetryTimes>0}}),n}return iu(t,e),t.prototype.destroy=function(){this.clearP2PTimer()},t.prototype.setMediaStreamProcessor=function(e){this.mediaStreamProcessor=e},t.prototype.setSubscriber=function(e){this.subscriber=e},t.prototype.setReporter=function(t){return null==t||t.registerReportUint(this.objName,this.reportUnit),e.prototype.setReporter.call(this,t)},t.prototype.configure=function(e){this.config=e,this.configCdnRetryCtrl(e)},t.prototype.load=function(e){var t=this;this.streamUrl=e,this.reportUnit.number("request").set(1),this.statsVar.loadStartTime=NS();var r=this.loadFromCdn(this.streamUrl);return r&&this.statsStartStream(r),this.clearP2PTimer(),this.p2pTimer=wc((function(){t.loadFromP2P()}),5e3),qR.resolve()},t.prototype.configCdnRetryCtrl=function(e){this.cdnRetryCtrl.totalRetryTimes=e.connectRetryCount,this.cdnRetryCtrl.leftRetryTimes=e.connectRetryCount,this.cdnRetryCtrl.retryDelay=e.connectRetryDelay},t.prototype.loadFromCdn=function(e){var t=this;if(!this.subscriber)throw Error("missing subscriber");if(!this.config)throw Error("missing config");if(this.cdnRetryCtrl.allowedToRetry()){this.cdnRetryCtrl.retry();var r=function(){function e(){}return e.create=function(){return"lebcdn_".concat(NS())},e.isCdnPid=function(e){return Qc(e).call(e,"lebcdn_")},e}().create(),n=this.subscriber.subscribeLEB({pullStreamUrl:this.config.pullStreamUrl,stopStreamUrl:this.config.stopStreamUrl,streamUrl:e,srcPid:r,param:{uuid:B(),playid:G()}});return n.on(ie,(function(e){var r;"video"===e.track.kind&&t.cdnRetryCtrl.reset(),t.sourceType!==ee.CDN&&(t.sourceType=ee.CDN,null===(r=t.bus)||void 0===r||r.emit(o,{code:a.INF_PLAY_EVT_STREAM_SWITCH,msg:"鍒囨崲鍒癈DN鎷夋祦",data:{source:"CDN"}})),t.onNewSourceTrack(e)})).once(oe,(function(){var e;null===(e=t.bus)||void 0===e||e.emit(o,{code:a.INF_PLAY_EVT_SERVER_CONNECTED,msg:"宸茬粡杩炴帴鍒癱dn鏈嶅姟鍣�"})})).once(ne,(function(){var e;t.reloadCDNAsync({from:"cdn",reason:"".concat(r,"-DISCONNECT")}),null===(e=t.bus)||void 0===e||e.emit(o,{code:a.INF_PLAY_EVT_SERVER_RECONNECT,msg:"杩炴帴cdn鏈嶅姟鍣ㄥけ璐ワ紝宸插惎鍔ㄨ嚜鍔ㄩ噸杩炴仮澶�"})})).once(se,(function(){t.reloadCDNAsync({from:"cdn",reason:"".concat(r,"-FAILED")})})).once(ae,(function(e){t.reloadCDNAsync({from:"cdn",reason:"".concat(r,"-ERROR")})})).once(te,(function(){var e;null===(e=t.bus)||void 0===e||e.emit(o,{code:a.INF_PLAY_EVT_STREAM_CLOSED,msg:"娴佽鍏抽棴浜�"})})),n}Rc((function(){var e;null===(e=t.bus)||void 0===e||e.emit(o,{code:a.ERR_LOAD_REACH_MAX_RETRY,msg:"宸茶揪鏈€澶DN閲嶈瘯娆℃暟: ".concat(t.cdnRetryCtrl.totalRetryTimes)})}))},t.prototype.loadFromP2P=function(e){var t=this;if(this.subscriber){var r=this.subscriber.subscribeP2P(e);return r?(r.on(ie,(function(e){var r;t.sourceType!==ee.P2P&&(t.sourceType=ee.P2P,null===(r=t.bus)||void 0===r||r.emit(o,{code:a.INF_PLAY_EVT_STREAM_SWITCH,msg:"鍒囨崲鍒癙2P鎷夋祦",data:{source:"P2P"}})),t.onNewSourceTrack(e)})).once(ne,(function(){t.reloadCDNAsync({from:"p2p",reason:"p2p-DISCONNECT"})})).once(ce,(function(){t.reloadCDNAsync({from:"p2p",reason:"p2p-STUCK"})})).once(te,(function(){})),r):void 0}},t.prototype.onNewSourceTrack=function(e){var t=this;return new qR((function(t){t(e)})).then((function(){if(!t.mediaStreamProcessor)throw Error("miss mediaStreamProcessor");t.mediaStreamProcessor.addMediaStreamTrack(e.track)})).catch((function(e){e.fatal}))},t.prototype.reloadCDNAsync=function(e){var t=this,r=e.from;e.reason;var n="p2p"===r?0:this.cdnRetryCtrl.retryDelay;Rc((function(){t.loadFromCdn(t.streamUrl)}),n)},t.prototype.statsStartStream=function(e){var t=this;e.on(re,(function(){t.statsVar.loadok||t.reportUnit.string("exit_reason").set("play_timeout"),Rc((function(){var e;null===(e=t.bus)||void 0===e||e.emit(o,{code:a.ERR_LOAD_TIMEOUT,msg:"鎷夋祦瓒呮椂"})}))})).on(ie,(function(e){"video"===e.track.kind&&(t.statsVar.loadok||(t.reportUnit.number("loadok_t").set(NS()-t.statsVar.loadStartTime),t.statsVar.loadok=!0))})).on(i,(function(e){var r;null===(r=t.bus)||void 0===r||r.emit(o,e)}))},t.prototype.clearP2PTimer=function(){this.p2pTimer&&(clearInterval(this.p2pTimer),this.p2pTimer=null)},t}(E);function pe(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var fe=function(e){function t(){for(var t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];var i=this;return pe(i=e.apply(this,du([],lu(r),!1))||this,"serverName","DefaultServer"),pe(i,"transport",null),pe(i,"router",new gS),pe(i,"triggerHandler",Jr(t=i.triggerAPI).call(t,i)),i}return iu(t,e),t.prototype.destroy=function(){var t;null===(t=this.transport)||void 0===t||t.removeListener(this.serverName,this.triggerHandler),e.prototype.destroy.call(this)},t.prototype.setTransport=function(e){this.transport=e,e.on(this.serverName,this.triggerHandler)},t.prototype.registerAPI=function(e,t){this.router.set(e,t)},t.prototype.deleteAPI=function(e){this.router.delete(e)},t.prototype.triggerAPI=function(e){var t=this.router.get("".concat(e.path));t&&t(e)},t}(E),he=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n,i,o,a=this;return o="P2PServer",(i="serverName")in(n=a=e.apply(this,du([],lu(t),!1))||this)?Tf(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o,a}return iu(t,e),t}(fe);function me(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ve=0,ye=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return me(n=e.apply(this,du([],lu(t),!1))||this,"tagName","TempClient"),me(n,"transport",null),me(n,"localPid",""),n}return iu(t,e),t.prototype.setTransport=function(e){this.transport=e},t.prototype.setLocalPid=function(e){this.localPid=e},t.prototype.destroy=function(){this.transport=null,e.prototype.destroy.call(this)},t.prototype.request=function(e){var t,r;null===(r=this.transport)||void 0===r||r.send({host:e.host,path:"".concat(e.path),from:e.from||this.localPid,to:e.to,type:e.type||"app",payload:e.payload,requestId:mi(t="req_".concat(NS(),"_")).call(t,ve+=1)})},t}(E);function ge(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var be=function(e){function t(t,r){var n=this;return ge(n=e.call(this)||this,"tag","WS"),ge(n,"url",""),ge(n,"id",""),ge(n,"options",{retry:0}),ge(n,"ws",null),n.url=t,n.options=r,n.init(),n}return iu(t,e),t.prototype.destroy=function(){var t;null===(t=this.ws)||void 0===t||t.close(),this.ws=null,this.id="",e.prototype.destroy.call(this)},t.prototype.send=function(e,t){var r,n;null===(n=this.ws)||void 0===n||n.send(mi(r="S ".concat(e," ")).call(r,t))},t.prototype.init=function(){var e,t,r,n;WebSocket&&(this.ws=new WebSocket(this.url),this.ws.onopen=Jr(e=this.onOpen).call(e,this),this.ws.onmessage=Jr(t=this.onMessage).call(t,this),this.ws.onclose=Jr(r=this.onClose).call(r,this),this.ws.onerror=Jr(n=this.onError).call(n,this))},t.prototype.onOpen=function(){},t.prototype.onMessage=function(e){var t=e.data.substring(0,1);if("C"===t)this.id=e.data.substring(2),this.emit("EVENT_WS_ID",this.id);else if("S"===t){var r=e.data.substring(29);this.emit("EVENT_WS_MESSAGE",r)}},t.prototype.onClose=function(){},t.prototype.onError=function(){},t}(E),Ce=r(961),Se=r.n(Ce);function _e(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Te=function(){function e(){_e(this,"totalBlock",0),_e(this,"recvBlock",0),_e(this,"blocks",new gS)}return e.prototype.getFullContent=function(){if(!this.complete())return null;for(var e="",t=0;t<this.totalBlock;t+=1)e+=this.blocks.get(t);return e},e.prototype.set=function(e,t,r){this.blocks.has(e)||(this.blocks.set(e,t),this.totalBlock=r,this.recvBlock+=1)},e.prototype.complete=function(){return this.totalBlock===this.recvBlock},e}(),we=function(){function e(){_e(this,"messages",new gS)}return e.prototype.set=function(e,t){var r=t.curIndex,n=t.total,i=t.block,o=this.messages.get(e);o||(o=new Te,this.messages.set(e,o)),o.set(r,i,n)},e.prototype.get=function(e){var t;return null===(t=this.messages.get(e))||void 0===t?void 0:t.getFullContent()},e.prototype.delete=function(e){this.messages.delete(e)},e}(),Re=function(){function e(){}return e.split=function(e){for(var t=NS(),r=[],n=5e3,i=Math.ceil(e.length/n),o=0;o<i;o++){var a,s,c,u=mi(a=mi(s="".concat(t,"-")).call(s,o,"-")).call(a,i,"#");r.push(mi(c="".concat(u)).call(c,jl(e).call(e,o*n,(o+1)*n)))}return r},e.extract=function(e){var t=Oc(e).call(e,"#"),r=jl(e).call(e,0,t+1),n=jl(e).call(e,t+1),i=lu(r.split("-"),3),o=i[0],a=i[1],s=i[2];return{key:o,index:bP(a,10),total:bP(s,10),content:n}},e}();function Pe(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ee="EVENT_SIGNAL_ID",ke="EVENT_SIGNAL_MESSAGE",Ae=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return Pe(n=e.apply(this,du([],lu(t),!1))||this,"id",""),Pe(n,"tag","Signal"),Pe(n,"messageCache",new we),Pe(n,"ws",null),Pe(n,"url",""),n}return iu(t,e),t.prototype.connect=function(e){this.url=e,this.init()},t.prototype.destroy=function(){this.ws&&this.ws.destroy(),this.ws=null,this.id="",e.prototype.destroy.call(this)},t.prototype.ready=function(){return!!this.id},t.prototype.send=function(e,t){var r,n=this,i=Se().compressToBase64(t);ic(r=Re.split(i)).call(r,(function(t){var r;null===(r=n.ws)||void 0===r||r.send(e,t)}))},t.prototype.init=function(){var e=this;this.ws||(this.ws=new be(this.url,{retry:0}),this.ws.on("EVENT_WS_ID",(function(t){e.id=t,e.emit("EVENT_SIGNAL_ID",t)})),this.ws.on("EVENT_WS_MESSAGE",(function(t){var r=Re.extract(t);e.messageCache.set(r.key,{curIndex:r.index,total:r.total,block:r.content});var n=e.messageCache.get(r.key);if(n){e.messageCache.delete(r.key);var i=Se().decompressFromBase64(n);e.emit("EVENT_SIGNAL_MESSAGE",i)}})))},t}(E),xe=function(){function e(){var e,t,r;e=this,t="rules",r=new Yb,t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}return e.prototype.destroy=function(){this.rules.clear()},e.prototype.addRule=function(e){this.rules.add(e)},e.prototype.match=function(e){var t,r;try{for(var n=uu(this.rules),i=n.next();!i.done;i=n.next())if(i.value.match(e))return!0}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return!1},e}(),Le=function(){function e(){}return e.prototype.destroy=function(){},e.prototype.score=function(){return 0},e}();function Ie(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var De=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return Ie(n=e.apply(this,du([],lu(t),!1))||this,"tagName","Peer"),Ie(n,"pid",""),Ie(n,"refreshTime",NS()),Ie(n,"info",null),Ie(n,"scorer",new Le),Ie(n,"filter",new xe),Ie(n,"connection",null),n}return iu(t,e),t.prototype.setPid=function(e){this.pid=e},t.prototype.setConnection=function(e){this.connection=e},t.prototype.getConnection=function(){if(!this.connection)throw Error("empty conn");return this.connection},t.prototype.setFilter=function(e){this.filter=e},t.prototype.setScorer=function(e){this.scorer=e},t.prototype.destroy=function(){var t;this.scorer.destroy(),ru(this).destroy(),null===(t=this.connection)||void 0===t||t.destroy(),this.connection=null,e.prototype.destroy.call(this)},t.prototype.fresh=function(){return NS()-this.refreshTime<6e4},t}(E);function Me(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Oe=function(e){function t(t,r){var n,i,o=t.pid,a=this;return Me(a=e.call(this)||this,"rtcPC",void 0),Me(a,"pid",""),Me(a,"onIceCandidateCallback",null),a.pid=o,a.tagName="PeerConnection_".concat(o),a.rtcPC=new RTCPeerConnection(r),a.rtcPC.onicecandidate=Jr(n=a.onIceCandidate).call(n,a),a.rtcPC.oniceconnectionstatechange=Jr(i=a.onIceConnectionStateChange).call(i,a),a}return iu(t,e),t.prototype.destroy=function(){this.rtcPC.close(),this.onIceCandidateCallback=null,e.prototype.destroy.call(this)},t.prototype.setOnTrackCallback=function(e){this.rtcPC.ontrack=e},t.prototype.clearOnTrackCallbak=function(){this.rtcPC.ontrack=null},t.prototype.setOnIceCandidateCallback=function(e){this.onIceCandidateCallback=e},t.prototype.createOffer=function(e){var t=this;return this.rtcPC.createOffer(e).then((function(e){return t.rtcPC.setLocalDescription(e)})).then((function(){return t.rtcPC.localDescription})).catch((function(t){return qR.reject({code:a.ERR_PLAY_WEBRTC_FAIL,msg:"createOffer fail, offer: ".concat(e)})}))},t.prototype.recvOffer=function(e){return this.rtcPC.setRemoteDescription(new RTCSessionDescription(e))},t.prototype.createAnswer=function(){var e=this;return this.rtcPC.createAnswer().then((function(t){return e.rtcPC.setLocalDescription(t)})).then((function(){return e.rtcPC.localDescription}))},t.prototype.recvAnswer=function(e){return this.rtcPC.setRemoteDescription(new RTCSessionDescription(e)).catch((function(t){return qR.reject({code:a.ERR_PLAY_WEBRTC_FAIL,msg:"setRemoteDescription fail, answer: ".concat(e.sdp)})}))},t.prototype.recvCandidate=function(e){if(e)return this.rtcPC.addIceCandidate(new RTCIceCandidate(e))},t.prototype.onIceCandidate=function(e){var t=null==e?void 0:e.candidate;null!=t&&t.candidate&&this.onIceCandidateCallback&&this.onIceCandidateCallback(t.toJSON())},t.prototype.onIceConnectionStateChange=function(){this.rtcPC.iceConnectionState,"failed"===this.rtcPC.iceConnectionState&&this.rtcPC.restartIce()},t}(E);function Ne(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ve=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return Ne(n=e.apply(this,du([],lu(t),!1))||this,"tagName","DataChannel"),Ne(n,"rtcDataChannel",null),Ne(n,"config",null),Ne(n,"pc",null),n}return iu(t,e),t.prototype.configure=function(e){return this.config=e,this},t.prototype.destroy=function(){var e;null===(e=this.rtcDataChannel)||void 0===e||e.close(),this.rtcDataChannel=null,this.pc=null},t.prototype.setRTCPeerConnection=function(e){return this.pc=e,this},t.prototype.init=function(){if(!this.config||!this.pc)throw Error("missing param");return this.rtcDataChannel=this.pc.createDataChannel(this.config.label,{id:this.config.id,negotiated:!0}),this.setCallback(this.rtcDataChannel),this},t.prototype.setCallback=function(e){var t=this;e.onopen=function(){t.emit("open")},e.onmessage=function(e){t.emit(e.data)},e.onerror=function(e){t.emit("error")},e.onclosing=function(){t.emit("closing")},e.onclose=function(){t.emit("close")}},t}(E);function Fe(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ue=function(e){function t(t,r){var n=this;return Fe(n=e.call(this)||this,"tagName","P2PConnection"),Fe(n,"pc",void 0),Fe(n,"connInit",void 0),Fe(n,"makingOffer",!1),Fe(n,"isSettingRemoteAnswerPending",!1),Fe(n,"closed",!1),Fe(n,"connectTimer",null),Fe(n,"nego",!1),Fe(n,"dc",void 0),Fe(n,"destroyed",!1),n.connInit=t,n.tagName="P2PConnection_".concat(n.connInit.targetPid),n.pc=new Oe({pid:t.targetPid},r),n.dc=new Ve,n}return iu(t,e),t.prototype.destroy=function(){this.destroyed||(this.destroyed=!0,this.dc.destroy(),this.pc.destroy(),this.close(),e.prototype.destroy.call(this))},t.prototype.getRTCPeerConnection=function(){return this.pc.rtcPC},t.prototype.init=function(){var e,t=this;this.on("EVENT_P2P_SIGNAL_RECV",Jr(e=this.recvSignalMessage).call(e,this)),this.pc.setOnIceCandidateCallback((function(e){t.sendSignal({from:t.connInit.localPid,to:t.connInit.targetPid,type:"iceCandidate",payload:e})})),this.pc.rtcPC.onnegotiationneeded=function(){return su(t,void 0,void 0,(function(){return cu(this,(function(e){switch(e.label){case 0:return this.nego=!0,[4,this.connect()];case 1:return e.sent(),this.nego=!1,[2]}}))}))},this.pc.rtcPC.onconnectionstatechange=function(){var e=t.pc.rtcPC.connectionState;t.emit(e)},this.dc.setRTCPeerConnection(this.pc.rtcPC).configure({label:"base",id:0}).init().on("error",(function(){t.emit("disconnected")}))},t.prototype.recvSignalMessage=function(e){return su(this,void 0,void 0,(function(){var t;return cu(this,(function(r){switch(r.label){case 0:switch(e.type){case"answer":return[3,1];case"offer":return[3,5];case"iceCandidate":return[3,10]}return[3,13];case 1:return r.trys.push([1,3,,4]),this.isSettingRemoteAnswerPending=!0,[4,this.pc.recvAnswer(e.payload)];case 2:return r.sent(),this.isSettingRemoteAnswerPending=!1,[3,4];case 3:return r.sent(),[3,4];case 4:case 9:return[3,13];case 5:return r.trys.push([5,8,,9]),this.ignoreOffer?[2]:[4,this.pc.recvOffer(e.payload)];case 6:return r.sent(),[4,this.pc.rtcPC.setLocalDescription()];case 7:return r.sent(),(t=this.pc.rtcPC.localDescription)?(this.sendSignal({from:this.connInit.localPid,to:this.connInit.targetPid,type:"answer",payload:t}),[3,9]):[2];case 8:return r.sent(),[3,9];case 10:return r.trys.push([10,12,,13]),[4,this.pc.recvCandidate(e.payload)];case 11:return r.sent(),[3,13];case 12:return r.sent(),this.ignoreOffer,[3,13];case 13:return[2]}}))}))},t.prototype.connect=function(){return su(this,void 0,void 0,(function(){var e,t;return cu(this,(function(r){switch(r.label){case 0:if(!this.nego)return[2];e={offerToReceiveAudio:!0,offerToReceiveVideo:!0},this.makingOffer=!0,r.label=1;case 1:return r.trys.push([1,3,4,5]),[4,this.pc.createOffer(e)];case 2:return(t=r.sent())?(this.sendSignal({from:this.connInit.localPid,to:this.connInit.targetPid,type:"offer",payload:t}),[3,5]):[2];case 3:return r.sent(),[3,5];case 4:return this.makingOffer=!1,[7];case 5:return[2]}}))}))},t.prototype.close=function(){this.closed||(this.closed=!0,this.emit("closed"))},t.prototype.sendSignal=function(e){this.emit("EVENT_P2P_SIGNAL_SEND",e)},Tf(t.prototype,"politeRole",{get:function(){var e=this.connInit;return e.localPid>e.targetPid},enumerable:!1,configurable:!0}),Tf(t.prototype,"readyForOffer",{get:function(){return!this.makingOffer&&("stable"===this.pc.rtcPC.signalingState||this.isSettingRemoteAnswerPending)},enumerable:!1,configurable:!0}),Tf(t.prototype,"offerCollision",{get:function(){return!this.readyForOffer},enumerable:!1,configurable:!0}),Tf(t.prototype,"ignoreOffer",{get:function(){return!this.politeRole&&this.offerCollision},enumerable:!1,configurable:!0}),t}(E);function je(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Be=0,We=function(e){function t(t,r){var n,i=this;return je(i=e.call(this)||this,"tagName","LEBConnection"),je(i,"pc",void 0),je(i,"initConfig",void 0),je(i,"svrSig",""),je(i,"closed",!1),je(i,"sessionId",NS()),je(i,"destroyed",!1),i.initConfig=t,i.tagName=mi(n="".concat(i.tagName,"_")).call(n,i.initConfig.srcPid),i.pc=new Oe({pid:t.srcPid},r),i.pc.rtcPC.onconnectionstatechange=function(){var e=i.pc.rtcPC.connectionState;i.emit(e)},i.pc.rtcPC.oniceconnectionstatechange=function(){i.pc.rtcPC.connectionState},i}return iu(t,e),Tf(t.prototype,"isSdpStreamUrl",{get:function(){return/^https?:\/\/.+\.sdp/.test(this.initConfig.streamUrl)},enumerable:!1,configurable:!0}),t.prototype.destroy=function(){this.destroyed||(this.destroyed=!0,this.close(),this.pc.destroy(),e.prototype.destroy.call(this))},t.prototype.getRTCPeerConnection=function(){return this.pc.rtcPC},t.prototype.connect=function(){return su(this,void 0,void 0,(function(){var e,t,r,n,o;return cu(this,(function(s){switch(s.label){case 0:e={offerToReceiveAudio:!0,offerToReceiveVideo:!0,voiceActivityDetection:!1},s.label=1;case 1:return s.trys.push([1,7,,8]),[4,this.pc.createOffer(e)];case 2:return t=s.sent(),this.emit(i,{code:a.INF_PLAY_EVT_REQUEST_PULL_BEGIN,msg:"寮€濮嬫媺娴佹椂瑙﹀彂",data:{localsdp:null==t?void 0:t.sdp,sessionid:this.sessionId}}),this.isSdpStreamUrl?[4,this.fetchStreamSdp(t)]:[3,4];case 3:return n=s.sent(),[3,6];case 4:return[4,this.fetchStream(t)];case 5:n=s.sent(),s.label=6;case 6:return r=n,this.svrSig=r.svrsig,this.emit(i,{code:a.INF_PLAY_EVT_REQUEST_PULL_SUCCESS,msg:"鎷夋祦鎴愬姛鍚庤Е鍙�",data:{localsdp:null==t?void 0:t.sdp,remotesdp:r.remotesdp.sdp,sessionid:this.sessionId}}),this.pc.recvAnswer(r.remotesdp),[3,8];case 7:return o=s.sent(),this.emit(i,o),this.emit("error",{errcode:0,errmsg:"leb pull stream fetch error: ".concat(o)}),[3,8];case 8:return[2]}}))}))},t.prototype.fetchStream=function(e){return su(this,void 0,void 0,(function(){var t,r,n,i;return cu(this,(function(o){var s,c,u,l,d;switch(o.label){case 0:t={streamurl:"".concat(this.initConfig.streamUrl),sessionid:this.sessionId,clientinfo:this.initConfig.param.uuid,localsdp:e,seipass:1},r=mi(s=mi(c=mi(u=mi(l=mi(d="".concat(this.initConfig.pullStreamUrl,"?from=")).call(d,this.initConfig.srcPid,"&uuid=")).call(l,this.initConfig.param.uuid,"&playid=")).call(u,this.initConfig.param.playid,"&sessionid=")).call(c,this.sessionId,"&reqCounter=")).call(s,Be+=1),o.label=1;case 1:return o.trys.push([1,4,,5]),[4,fetch(r,{method:"POST",body:jS(t),headers:{"Content-Type":"application/json"}})];case 2:return[4,o.sent().json()];case 3:return[2,(n=o.sent()).errcode||!n.svrsig?qR.reject({code:a.ERR_PLAY_REQUEST_PULL_FAIL,msg:"鎷夋祦鎺モ紳鍚庡彴杩斿洖鎶ラ敊",data:n}):n];case 4:return i=o.sent(),[2,qR.reject({code:a.ERR_FETCH_REQUEST_FAIL,msg:"鎷夋祦鎺モ紳缃戠粶寮傚父",data:{from:"PULL_STREAM",url:r,error:i}})];case 5:return[2]}}))}))},t.prototype.fetchStreamSdp=function(e){return su(this,void 0,void 0,(function(){var t,r,n,i,o,s,c,u;return cu(this,(function(l){switch(l.label){case 0:return t={version:"v1.0",sessionid:this.sessionId,localSdp:e},[4,fetch(this.initConfig.streamUrl,{method:"POST",body:jS(t),headers:{"Content-Type":"application/json"}})];case 1:r=l.sent(),l.label=2;case 2:return l.trys.push([2,4,,5]),[4,r.json()];case 3:return n=l.sent(),i=n.code,o=n.message,s=n.remoteSdp,c=n.svrsig,200!==i?[2,qR.reject({code:a.ERR_PLAY_REQUEST_PULL_FAIL,msg:"鎷夋祦鎺モ紳鍚庡彴杩斿洖鎶ラ敊",data:n})]:[2,{remotesdp:s,svrsig:c,errcode:0,errmsg:o}];case 4:return u=l.sent(),[2,qR.reject({code:a.ERR_FETCH_REQUEST_FAIL,msg:"鎷夋祦鎺モ紳缃戠粶寮傚父",data:{from:"PULL_STREAM",url:this.initConfig.streamUrl,error:u}})];case 5:return[2]}}))}))},t.prototype.close=function(){return su(this,void 0,void 0,(function(){return cu(this,(function(e){switch(e.label){case 0:return this.closed?[3,2]:(this.closed=!0,this.emit("closed"),[4,this.closeLeb()]);case 1:e.sent(),e.label=2;case 2:return[2]}}))}))},t.prototype.closeLeb=function(){return su(this,void 0,void 0,(function(){var e,t,r;return cu(this,(function(n){var o,s;switch(n.label){case 0:if(this.isSdpStreamUrl&&!this.svrSig)return[2];e={streamurl:this.initConfig.streamUrl,svrsig:this.svrSig},t=mi(o="".concat(this.initConfig.stopStreamUrl,"?from=")).call(o,this.initConfig.srcPid),n.label=1;case 1:return n.trys.push([1,4,,5]),[4,fetch(t,{method:"post",body:jS(e),headers:{"Content-Type":"application/json"}})];case 2:return[4,n.sent().json()];case 3:return(r=n.sent()).errcode&&(this.emit("error",{errcode:1,errmsg:mi(s="leb stop stream response error: errcode: ".concat(r.errcode,", errmsg: ")).call(s,r.errmsg)}),this.emit(i,{code:a.ERR_PLAY_REQUEST_STOP_FAIL,msg:"鍋滄祦鎺モ紳鍚庡彴杩斿洖鎶ラ敊",data:{url:t}})),[3,5];case 4:return n.sent(),this.emit(i,{code:a.ERR_FETCH_REQUEST_FAIL,msg:"鍋滄祦鎺モ紳缃戠粶寮傚父",data:{from:"STOP_STREAM",url:t}}),[3,5];case 5:return[2]}}))}))},t}(E);function Ge(e,t){var r=Pf(e);if(vf){var n=vf(e);t&&(n=ru(n).call(n,(function(t){return Of(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ze(e){for(var t=1;t<arguments.length;t++){var r,n,i=null!=arguments[t]?arguments[t]:{};t%2?ic(r=Ge(Object(i),!0)).call(r,(function(t){Ye(e,t,i[t])})):Hf?$f(e,Hf(i)):ic(n=Ge(Object(i))).call(n,(function(t){Tf(e,t,Of(i,t))}))}return e}function Ye(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var He=function(){function e(){}return e.prototype.create=function(e){var t=this.createPeer({pid:e.srcPid}),r=this.createConnection(e);return t.setConnection(r),t},e.prototype.createConnection=function(e){return new We(e,{iceServers:[],encodedInsertableStreams:!0})},e.prototype.createPeer=function(e){var t=e.pid,r=new De;return r.setPid(t),r},e}(),Xe=function(){function e(){Ye(this,"client",null),Ye(this,"server",null),Ye(this,"localPid",""),Ye(this,"config",null)}return e.prototype.create=function(e){var t=this.createConnection(e),r=this.createPeer({pid:e.targetPid});return r.setConnection(t),r},e.prototype.setClient=function(e){return this.client=e,this},e.prototype.setServer=function(e){return this.server=e,this},e.prototype.setLocalPid=function(e){return this.localPid=e,this},e.prototype.configure=function(e){this.config=e},e.prototype.createConnection=function(e){var t=this;if(!this.server)throw Error("P2PPeerBuilder empty server");if(!this.config)throw Error("P2PPeerBuilder empty config");var r=new Ue({targetPid:e.targetPid,localPid:this.localPid},{iceServers:[{urls:["stun:".concat(this.config.stun)]}],encodedInsertableStreams:!0});return r.on("EVENT_P2P_SIGNAL_SEND",(function(e){if(!t.server||!t.client||!t.localPid)throw Error("P2PPeerBuilder param error");var r=Ze(Ze({},e),{},{from:t.localPid,path:"signal",host:t.server.serverName});t.client.request(r)})),r.init(),r},e.prototype.createPeer=function(e){var t=e.pid,r=new De;return r.setPid(t),r},e}();function Je(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ke=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return iu(t,e),t.prototype.send=function(e){},t.prototype.recv=function(e){},t}(t()),Qe=function(e){function t(){for(var t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];var i=this;return Je(i=e.apply(this,du([],lu(r),!1))||this,"signal",null),Je(i,"recvHandler",Jr(t=i.recv).call(t,i)),i}return iu(t,e),t.prototype.destroy=function(){var e;null===(e=this.signal)||void 0===e||e.removeListener(ke,this.recvHandler),this.signal=null,this.removeAllListeners()},t.prototype.setSignal=function(e){this.signal=e,e.on(ke,this.recvHandler)},t.prototype.send=function(e){var t;null===(t=this.signal)||void 0===t||t.send(e.to,jS(e))},t.prototype.recv=function(e){var t;try{t=JSON.parse(e)}catch(e){return}this.emit(t.host,t)},t}(Ke);function qe(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ze=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return qe(n=e.apply(this,du([],lu(t),!1))||this,"tagName","PeerPool"),qe(n,"candidate",new gS),qe(n,"connected",new gS),qe(n,"connecting",new gS),qe(n,"config",null),qe(n,"tickTimer",null),qe(n,"reportUnit",new T),n}return iu(t,e),t.prototype.configure=function(e){this.config=e},t.prototype.setReporter=function(t){var r=this;return null==t||t.registerReportUint(this.objName,this.reportUnit),this.reportUnit.registerPreHook(this.objName,(function(){var e=r.stats();r.reportUnit.number("connected").set(e.connected),r.reportUnit.number("connecting").set(e.connecting),r.reportUnit.number("candidate").set(e.candidate)})),e.prototype.setReporter.call(this,t)},t.prototype.init=function(){var e;if(!this.config)throw Error("missing config");this.clearTimer(),this.tickTimer=wc(Jr(e=this.tick).call(e,this),this.config.tickInterval)},t.prototype.destroy=function(){var t,r,n;this.clearTimer(),ic(t=this.connecting).call(t,(function(e){e.destroy()})),this.connecting.clear(),ic(r=this.candidate).call(r,(function(e){e.destroy()})),this.candidate.clear(),ic(n=this.connected).call(n,(function(e){e.destroy()})),this.connected.clear(),this.config=null,e.prototype.destroy.call(this)},t.prototype.stats=function(){return{connected:this.connected.size,connecting:this.connecting.size,candidate:this.candidate.size}},t.prototype.addCandidate=function(e){this.get(e.pid)||(this.reportUnit.number("candidate_total").plus(1),this.candidate.set(e.pid,e),this.tick())},t.prototype.addConnecting=function(e){this.get(e.pid)||(this.reportUnit.number("connecting_total").plus(1),this.connecting.set(e.pid,e),this.setupConnection(e))},t.prototype.tick=function(){if(this.config&&!(this.connecting.size>this.config.maxConnecting)){var e=this.config.maxConnected-this.connected.size;if(e)for(var t,r=EP(t=this.candidate).call(t),n=function(){var e=r.next();return e.done?null:e.value[1]},i=e;i>0;){var o=n();if(!o)return void this.emit("EVENT_LESS_CANDIDATE",{lackCnt:e});o.fresh()?this.connecting.has(o.pid)||this.connected.has(o.pid)||(this.reportUnit.number("connect_total").plus(1),this.candidate.delete(o.pid),this.prepareConnectCandiate(o),i-=1):this.remove(o.pid)}}},t.prototype.bestOne=function(e){var t,r;try{for(var n=uu(this.connected),i=n.next();!i.done;i=n.next()){var o=lu(i.value,2),a=o[0],s=o[1];if(!$i(e).call(e,a))return s}}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return null},t.prototype.get=function(e){return this.candidate.get(e)||this.connected.get(e)||this.connecting.get(e)},t.prototype.getConnected=function(e){return this.connected.get(e)},t.prototype.remove=function(e){var t=this.get(e);t&&(this.connecting.delete(e),this.connected.delete(e),this.candidate.delete(e),Rc((function(){t.destroy()})))},t.prototype.prepareConnectCandiate=function(e){this.setupConnection(e),this.connected.delete(e.pid),this.connecting.set(e.pid,e),e.getConnection().connect()},t.prototype.setupConnection=function(e){var t=this,r=function(r){t.remove(e.pid)},n=Rc((function(){r()}),1e4),i=e.getConnection();i.on("connected",(function(){t.reportUnit.number("conn_connected_total").plus(1),clearTimeout(n),t.connecting.delete(e.pid),t.connected.set(e.pid,e)})),i.on("closed",(function(){t.reportUnit.number("conn_closed_total").plus(1),clearTimeout(n),r()})),i.on("disconnected",(function(){t.reportUnit.number("conn_disconnected_total").plus(1),r()})),i.on("failed",(function(){t.reportUnit.number("conn_failed_total").plus(1),r()}))},t.prototype.clearTimer=function(){this.tickTimer&&(clearTimeout(this.tickTimer),this.tickTimer=null)},t}(E);function $e(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var et=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return $e(n=e.apply(this,du([],lu(t),!1))||this,"tagName","PeerManager"),$e(n,"peerPool",new ze),$e(n,"peerCache",null),$e(n,"peerBuilder",null),$e(n,"reportUnit",new T),$e(n,"config",null),n}return iu(t,e),t.prototype.destroy=function(){this.peerPool&&this.peerPool.destroy(),this.peerBuilder=null,e.prototype.destroy.call(this)},t.prototype.configure=function(e){this.config=e,this.peerPool.configure(e)},t.prototype.setReporter=function(t){return null==t||t.registerReportUint(this.objName,this.reportUnit),this.peerPool.setReporter(t),e.prototype.setReporter.call(this,t)},t.prototype.init=function(){var e=this;this.peerPool.on("EVENT_LESS_CANDIDATE",(function(t){if(t.lackCnt,!e.peerCache)throw Error("missing param");var r=e.peerCache.getPid();e.addPeerId(r)})),this.peerPool.init()},t.prototype.setPeerBuilder=function(e){return this.peerBuilder=e,this},t.prototype.setPeerCache=function(e){return this.peerCache=e,this},t.prototype.setSortStrategy=function(){},t.prototype.setKickStrategy=function(){},t.prototype.addPeerId=function(e){if(e&&!this.peerPool.get(e)&&this.peerBuilder&&this.config){var t=this.peerBuilder.create({targetPid:e});this.addPeer(t)}},t.prototype.addPeer=function(e){this.peerPool.addCandidate(e)},t.prototype.addConnectingPeer=function(e){this.peerPool.addConnecting(e)},t.prototype.getPeer=function(e){return this.peerPool.get(e)},t.prototype.getConnectedPeer=function(e){return this.peerPool.getConnected(e)},t.prototype.getBestPeer=function(e){var t=e.excludePeerId;return this.peerPool.bestOne(t)},t}(E);function tt(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var rt=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return tt(n=e.apply(this,du([],lu(t),!1))||this,"pid",""),tt(n,"streamId",""),tt(n,"timer",null),tt(n,"config",null),n}return iu(t,e),t.prototype.destroy=function(){this.timer&&clearInterval(this.timer),this.removeAllListeners(),this.timer=null},t.prototype.configure=function(e){this.config=e},t.prototype.setPid=function(e){return this.pid=e,this},t.prototype.setStreamId=function(e){return this.streamId=e,this},t.prototype.start=function(){var e=this;if(!this.config)throw Error("missing config");!this.timer&&this.config.trackerInterval&&(this.timer=wc((function(){e.request()}),this.config.trackerInterval))},t.prototype.ready=function(){return this.pid&&this.streamId},t.prototype.request=function(){var e,t,r,n=this;if(!this.config)throw Error("missing config");if(this.ready()){var i=mi(e=mi(t=mi(r="".concat(this.config.trackerUrl,"?pid=")).call(r,this.pid,"&channel=")).call(t,this.streamId,"_")).call(e,this.config.trackerVersion,"&streamId=0&mode=bat");fetch(i).then((function(e){return e.text()})).then((function(e){return JSON.parse(e)})).then((function(e){n.parsePeer(e)})).catch((function(e){}))}},t.prototype.parsePeer=function(e){var t,r=this;ic(t=e.peers).call(t,(function(e){var t=e.pid;r.emit("EVENT_NEW_PEER",{pid:t})}))},t}(t());function nt(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var it=function(){function e(){nt(this,"defaultMaxCandidateSize",100),nt(this,"localPid",""),nt(this,"peers",new Yb),nt(this,"tracker",new rt),nt(this,"config",null)}return e.prototype.destroy=function(){this.tracker.destroy(),this.peers.clear(),this.config=null},e.prototype.setStreamId=function(e){return this.tracker.setStreamId(e),this},e.prototype.setPeerId=function(e){return this.tracker.setPid(e),this},e.prototype.start=function(){this.tracker.start()},e.prototype.configure=function(e){this.config=e,this.tracker.configure(this.config)},e.prototype.init=function(){var e=this;if(!this.config)throw Error("miss config");this.tracker.on("EVENT_NEW_PEER",(function(t){var r=t.pid;e.addPid(r)}))},e.prototype.addPid=function(e){var t;if(this.peers.has(e)&&this.peers.delete(e),this.peers.add(e),this.peers.size>((null===(t=this.config)||void 0===t?void 0:t.maxCandidateSize)||this.defaultMaxCandidateSize)){var r=this.getOldestPid();r&&this.peers.delete(r)}},e.prototype.getPid=function(){var e=this.getFreshPid();return e&&this.peers.delete(e),e},e.prototype.getFreshPid=function(){return this.peers.size?du([],lu(this.peers),!1)[this.peers.size-1]:null},e.prototype.getOldestPid=function(){return this.peers.size?du([],lu(this.peers),!1)[0]:null},e}();function ot(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var at=function(){function e(){ot(this,"path","signal"),ot(this,"peerManager",null),ot(this,"peerBuilder",null),ot(this,"server",null)}return e.prototype.destroy=function(){this.server&&(this.server.deleteAPI(this.path),this.server=null)},e.prototype.setPeerManager=function(e){return this.peerManager=e,this},e.prototype.setPeerBuilder=function(e){return this.peerBuilder=e,this},e.prototype.setServer=function(e){var t;return this.server=e,this.server.registerAPI(this.path,Jr(t=this.handler).call(t,this)),this},e.prototype.handler=function(e){var t;if(this.peerBuilder&&this.peerManager){var r=null===(t=this.peerManager)||void 0===t?void 0:t.getPeer(e.from);"offer"===e.type&&(r||(r=this.peerBuilder.create({targetPid:e.from}),this.peerManager.addConnectingPeer(r))),r&&r.getConnection().emit("EVENT_P2P_SIGNAL_RECV",e)}},e}();function st(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ct=function(){function e(){st(this,"path","SubscribeTrack"),st(this,"server",null),st(this,"subscriber",null)}return e.prototype.destroy=function(){var e;null===(e=this.server)||void 0===e||e.deleteAPI(this.path),this.server=null,this.subscriber=null},e.prototype.setServer=function(e){var t;return this.server=e,this.server.registerAPI(this.path,Jr(t=this.handler).call(t,this)),this},e.prototype.setSubscriber=function(e){this.subscriber=e},e.prototype.handler=function(e){if(this.subscriber){var t=e.from;"subscribe"===e.payload?this.subscriber.onSubscribe(t):"cancelSubscribe"===e.payload&&this.subscriber.onCancelSubscribe(t)}},e}();function ut(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var lt,dt=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return ut(n=e.apply(this,du([],lu(t),!1))||this,"chunks",[]),ut(n,"rtcEncodedStreams",null),ut(n,"aborted",!1),ut(n,"lastTimestamp",0),ut(n,"maxCache",200),n}return iu(t,e),t.prototype.destroy=function(){this.lastTimestamp=0,this.chunks=[],e.prototype.removeAllListeners.call(this)},t.prototype.configure=function(e){var t=e.max;this.maxCache=t},t.prototype.reset=function(){this.chunks=[]},t.prototype.getHead=function(){return this.chunks[0]},t.prototype.shift=function(){return this.chunks.shift()},t.prototype.removeHead=function(){return this.chunks.shift()},Tf(t.prototype,"size",{get:function(){return this.chunks.length},enumerable:!1,configurable:!0}),t.prototype.cache=function(e){this.lastTimestamp=e.timestamp;var t={type:e.type,timestamp:e.timestamp,data:new Uint8Array(e.data).buffer,chunk:e.chunk};this.chunks.push(t),this.chunks.length>this.maxCache&&this.removeHead(),this.emit("new_chunk",t)},t.prototype.remove=function(e){for(var t;this.chunks.length>e;)this.chunks.shift();var r=this.chunks.length;return r<=e?0:zP(t=this.chunks).call(t,0,r-e)},t.prototype.dropOutdateChunk=function(e){for(var t=this.getHead();t&&t.timestamp<e;)this.removeHead(),t=this.getHead()},t}(e.EventEmitter);function pt(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e){e[e.key=31]="key",e[e.delta=32]="delta",e[e.empty=33]="empty"}(lt||(lt={}));var ft=function(){function e(){}return e.encode=function(e){var t=e.chunkData.byteLength+4+1,r=new mt({totalBytes:t});return r.setArraybuffer(e.chunkData),r.setUint32(e.timestamp),r.setUnit8(lt[e.type]),r.getArraybuffer()},e.decode=function(e){var t=new ht(e),r=t.getArraybuffer(e.byteLength-5);return{timestamp:t.getUint32(),type:lt[t.getUint8()],chunkData:r}},e}(),ht=function(){function e(e){pt(this,"u8a",void 0),pt(this,"dataview",void 0),pt(this,"bytesOffset",0),pt(this,"totalBytes",0),this.u8a=new Uint8Array(e),this.dataview=new DataView(e),this.totalBytes=e.byteLength}return e.prototype.getUint8=function(){var e=this.dataview.getUint8(this.bytesOffset);return this.bytesOffset+=1,e},e.prototype.getUint32=function(){var e=this.dataview.getUint32(this.bytesOffset);return this.bytesOffset+=4,e},e.prototype.getArraybuffer=function(e,t){var r;void 0===t&&(t=0);var n=jl(r=this.u8a).call(r,t,e);return this.bytesOffset+=t+e,n.buffer},e}(),mt=function(){function e(e){var t=e.totalBytes;pt(this,"totalBytes",0),pt(this,"u8a",void 0),pt(this,"dataview",void 0),pt(this,"bytesOffset",0),this.totalBytes=t,this.u8a=new Uint8Array(t),this.dataview=new DataView(this.u8a.buffer)}return e.prototype.setUnit8=function(e){this.dataview.setUint8(this.bytesOffset,e),this.bytesOffset+=1},e.prototype.setUint32=function(e){this.dataview.setUint32(this.bytesOffset,e),this.bytesOffset+=4},e.prototype.setArraybuffer=function(e){this.u8a.set(new Uint8Array(e),this.bytesOffset),this.bytesOffset+=e.byteLength},e.prototype.getArraybuffer=function(){return this.u8a.buffer},e}();function vt(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var yt=function(){function e(){vt(this,"videoTrack",new MediaStreamTrackGenerator({kind:"video"})),vt(this,"audioTrack",new MediaStreamTrackGenerator({kind:"audio"})),vt(this,"videoWriter",this.videoTrack.writable.getWriter()),vt(this,"audioWriter",this.audioTrack.writable.getWriter()),vt(this,"timer",null)}return e.prototype.destroy=function(){this.timer&&(clearInterval(this.timer),this.timer=null)},e.prototype.getVideoTrack=function(){return this.videoTrack},e.prototype.getAudioTrack=function(){return this.audioTrack},e.prototype.writeAudioDataToTrack=function(){var e=this.createAudioData();this.audioWriter.write(e).catch((function(e){}))},e.prototype.writeVideoFrameToTrack=function(e){var t=e.timestamp,r=this.createVideoFrame({timestamp:t});return this.videoWriter.write(r).catch((function(e){})),r},e.prototype.createAudioData=function(){var e={timestamp:NS(),data:new ArrayBuffer(10),sampleRate:48e3,format:"u8",numberOfFrames:1,numberOfChannels:1};return new AudioData(e)},e.prototype.createVideoFrame=function(e){for(var t=e.timestamp,r={format:"I420",codedHeight:16,codedWidth:16,colorSpace:new VideoColorSpace({matrix:"bt709",primaries:"bt709",transfer:"bt709"}),displayHeight:16,displayWidth:16,timestamp:t},n=new Uint8Array(r.codedWidth*r.codedHeight*4),i=0;i<r.codedWidth;i++)for(var o=0;o<r.codedHeight;o++){var a=4*(o*r.codedWidth+i);n[a]=127,n[a+1]=255,n[a+2]=212,n[a+3]=255}return new VideoFrame(n,r)},e}();function gt(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var bt=function(e,t){var r,n,i,o=null===(i=RTCRtpReceiver.getCapabilities(t))||void 0===i?void 0:i.codecs,a=[];if(!o)return a;try{for(var s=uu(o),c=s.next();!c.done;c=s.next()){var u=c.value;u.mimeType===e&&a.push(u)}}catch(e){r={error:e}}finally{try{c&&!c.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}return a},Ct=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return gt(n=e.apply(this,du([],lu(t),!1))||this,"tagName","StreamTrackProvider"),gt(n,"targetPid",""),gt(n,"srcPid",""),gt(n,"peerManager",null),gt(n,"driverTrack",new yt),gt(n,"videoEncodedFrameCache",null),gt(n,"audioEncodedFrameCache",null),gt(n,"statUnit",null),gt(n,"reportUnit",new T),gt(n,"audioAbortController",new AbortController),gt(n,"videoAbortController",new AbortController),gt(n,"newVideoChunkHandler",null),gt(n,"videoEmitChunkTimer",null),n}return iu(t,e),t.prototype.destroy=function(){var t;this.clearTimer(),this.onCancelSubscribe(),this.driverTrack.destroy(),this.peerManager=null,this.audioAbortController.abort("StreamTrackProvider_abort_audio"),this.videoAbortController.abort("StreamTrackProvider_abort_video"),this.newVideoChunkHandler&&(null===(t=this.videoEncodedFrameCache)||void 0===t||t.off("new_chunk",this.newVideoChunkHandler)),this.videoEncodedFrameCache=null,this.audioEncodedFrameCache=null,e.prototype.destroy.call(this)},t.prototype.setStatUnit=function(e){return this.statUnit=e,this},t.prototype.setPeerManager=function(e){return this.peerManager=e,this},t.prototype.setVideoEncodedFrameCache=function(e){return this.videoEncodedFrameCache=e,this},t.prototype.setAudioEncodedFrameCache=function(e){return this.audioEncodedFrameCache=e,this},t.prototype.setTargetPid=function(e){return this.targetPid=e,this},t.prototype.setSrcPid=function(e){return this.srcPid=e,this},t.prototype.setReporter=function(t){return null==t||t.registerReportUint(this.objName,this.reportUnit),e.prototype.setReporter.call(this,t)},t.prototype.onSubscribe=function(){var e,t,r=null===(e=this.peerManager)||void 0===e?void 0:e.getPeer(this.targetPid),n=null===(t=this.peerManager)||void 0===t?void 0:t.getPeer(this.srcPid);if(!r)return!1;if(!n)return!1;var i=r.getConnection().getRTCPeerConnection(),o=n.getConnection().getRTCPeerConnection();return"closed"!==i.signalingState&&(this.listenConnection(r.getConnection()),this.provideVideoTrack(o,i),this.provideAudioTrack(o,i),!0)},t.prototype.onCancelSubscribe=function(){var e,t=null===(e=this.peerManager)||void 0===e?void 0:e.getPeer(this.targetPid);if(!t)return!1;t.getConnection().destroy()},t.prototype.updateTrack=function(e,t){var r,n,i;this.srcPid=t;var o=null===(i=this.peerManager)||void 0===i?void 0:i.getPeer(this.targetPid);if(!o)return!1;if("video"!==e.kind){var a=o.getConnection().getRTCPeerConnection().getSenders();try{for(var s=uu(a),c=s.next();!c.done;c=s.next()){var u,l=c.value;if(e.kind===(null===(u=l.track)||void 0===u?void 0:u.kind)){l.replaceTrack(e);break}}}catch(e){r={error:e}}finally{try{c&&!c.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}return!0}},t.prototype.provideAudioTrack=function(e,t){var r,n,i,o,a,s,c=this,u=e.getReceivers();try{for(var l=uu(u),d=l.next();!d.done;d=l.next()){var p=d.value;if("audio"===p.track.kind){t.addTrack(p.track);break}}}catch(e){r={error:e}}finally{try{d&&!d.done&&(n=l.return)&&n.call(l)}finally{if(r)throw r.error}}var f=t.getTransceivers();try{for(var h=uu(f),m=h.next();!m.done;m=h.next()){var v,y=m.value;if("audio"===(null===(v=y.sender.track)||void 0===v?void 0:v.kind)){y.setCodecPreferences(bt("audio/opus","audio"));break}}}catch(e){i={error:e}}finally{try{m&&!m.done&&(o=h.return)&&o.call(h)}finally{if(i)throw i.error}}var g,b=t.getSenders(),C=function(e){if("audio"!==(null===(g=e.track)||void 0===g?void 0:g.kind))return"continue";var t=!1,r=e.createEncodedStreams();return r.readable.pipeThrough(new TransformStream({transform:function(e,r){if(c.audioEncodedFrameCache){if(!t){var n=1;c.audioEncodedFrameCache.remove(n),t=!0}var i=c.audioEncodedFrameCache.shift();i&&(e.data=ft.encode({chunkData:i.data,timestamp:i.timestamp,type:"key"}),r.enqueue(e))}}})).pipeThrough(new TransformStream({transform:function(e,t){var r,n;null===(r=c.statUnit)||void 0===r||r.number("uploadBytes").plus(e.data.byteLength),null===(n=c.statUnit)||void 0===n||n.number("uploadAuidoBytes").plus(e.data.byteLength),c.reportUnit.number("uploadBytes").plus(e.data.byteLength),c.reportUnit.number("uploadAuidoBytes").plus(e.data.byteLength),t.enqueue(e)}})).pipeTo(r.writable,{signal:S.audioAbortController.signal}).catch((function(e){})),"break"},S=this;try{for(var _=uu(b),T=_.next();!T.done&&"break"!==C(T.value);T=_.next());}catch(e){a={error:e}}finally{try{T&&!T.done&&(s=_.return)&&s.call(_)}finally{if(a)throw a.error}}},t.prototype.provideVideoTrack=function(e,t){var r,n,i,o,a,s=this,c=this.driverTrack.getVideoTrack();t.addTrack(c),this.newVideoChunkHandler=function(){s.driverTrack.writeVideoFrameToTrack({timestamp:0})},null===(a=this.videoEncodedFrameCache)||void 0===a||a.on("new_chunk",this.newVideoChunkHandler),this.clearTimer(),this.videoEmitChunkTimer=wc((function(){s.videoEncodedFrameCache&&s.videoEncodedFrameCache.size>1&&s.driverTrack.writeVideoFrameToTrack({timestamp:0}),s.driverTrack.writeVideoFrameToTrack({timestamp:0})}),30);var u=t.getTransceivers();try{for(var l=uu(u),d=l.next();!d.done;d=l.next()){var p,f=d.value;if("video"===(null===(p=f.sender.track)||void 0===p?void 0:p.kind)){f.setCodecPreferences(bt("video/H264","video"));break}}}catch(e){r={error:e}}finally{try{d&&!d.done&&(n=l.return)&&n.call(l)}finally{if(r)throw r.error}}var h,m=t.getSenders(),v=function(e){if("video"!==(null===(h=e.track)||void 0===h?void 0:h.kind))return"continue";var t=e.createEncodedStreams(),r=!1;return t.readable.pipeThrough(new TransformStream({transform:function(e,t){if(!s.videoEncodedFrameCache)throw Error("provideVideoTrack missing param");if(!r){var n=1;s.videoEncodedFrameCache.remove(n),r=!0}var i=s.videoEncodedFrameCache.shift();i&&(e.data=ft.encode({chunkData:i.data,timestamp:i.timestamp,type:i.type}),t.enqueue(e))}})).pipeThrough(new TransformStream({transform:function(e,t){var r,n;null===(r=s.statUnit)||void 0===r||r.number("uploadBytes").plus(e.data.byteLength),null===(n=s.statUnit)||void 0===n||n.number("uploadVideoBytes").plus(e.data.byteLength),s.reportUnit.number("uploadBytes").plus(e.data.byteLength),s.reportUnit.number("uploadVideoBytes").plus(e.data.byteLength),t.enqueue(e)}})).pipeTo(t.writable,{signal:y.videoAbortController.signal}).catch((function(e){})),"break"},y=this;try{for(var g=uu(m),b=g.next();!b.done&&"break"!==v(b.value);b=g.next());}catch(e){i={error:e}}finally{try{b&&!b.done&&(o=g.return)&&o.call(g)}finally{if(i)throw i.error}}},t.prototype.listenConnection=function(e){var t=this;e.on("disconnected",(function(){t.emit("EVENT_RPOVIDER_DISCONNECT")})),e.on("failed",(function(){t.emit("EVENT_RPOVIDER_FAILED")}))},t.prototype.clearTimer=function(){this.videoEmitChunkTimer&&(clearInterval(this.videoEmitChunkTimer),this.videoEmitChunkTimer=null)},t}(E);function St(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var _t=function(){function e(){St(this,"config",null),St(this,"coolingPeers",new Yb)}return e.prototype.configure=function(e){this.config=e},e.prototype.destroy=function(){this.coolingPeers.clear()},e.prototype.add=function(e){var t,r=this;if(!this.config)throw Error("PeerCooler missing param");this.coolingPeers.add(e),Rc((function(){r.coolingPeers.delete(e)}),null===(t=this.config)||void 0===t?void 0:t.coolingTime)},e.prototype.getList=function(){return du([],lu(this.coolingPeers),!1)},e}();function Tt(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var wt,Rt=function(){function e(){Tt(this,"lastWriteAudioTimestamp",0),Tt(this,"lastWriteVideoTimestamp",0),Tt(this,"p2pVideoRecvEnough",!1),Tt(this,"_prepareToStopCdn",!1),Tt(this,"_cdnStoppingReady",!1),Tt(this,"_lastestCdnKeyTimestamp",0),Tt(this,"_lastRecvCdnVideoTimestamp",0),Tt(this,"_lastRecvCdnAudioTimestamp",0)}return Tf(e.prototype,"lastRecvCdnVideoTimestamp",{get:function(){return this._lastRecvCdnVideoTimestamp},set:function(e){this._lastRecvCdnVideoTimestamp=e},enumerable:!1,configurable:!0}),Tf(e.prototype,"lastRecvCdnAudioTimestamp",{get:function(){return this._lastRecvCdnAudioTimestamp},set:function(e){this._lastRecvCdnAudioTimestamp=e},enumerable:!1,configurable:!0}),Tf(e.prototype,"prepareToStopCdn",{get:function(){return this._prepareToStopCdn},set:function(e){this._prepareToStopCdn=e},enumerable:!1,configurable:!0}),Tf(e.prototype,"cdnStoppingReady",{get:function(){return this._cdnStoppingReady},set:function(e){this._cdnStoppingReady=e},enumerable:!1,configurable:!0}),Tf(e.prototype,"lastestCdnKeyTimestamp",{get:function(){return this._lastestCdnKeyTimestamp},set:function(e){this._lastestCdnKeyTimestamp=e},enumerable:!1,configurable:!0}),e.prototype.reset=function(){this._prepareToStopCdn=!1,this._cdnStoppingReady=!1,this._lastestCdnKeyTimestamp=0,this._lastRecvCdnVideoTimestamp=0,this._lastRecvCdnAudioTimestamp=0,this.lastWriteAudioTimestamp=0,this.lastWriteVideoTimestamp=0,this.p2pVideoRecvEnough=!1},e}();function Pt(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e){e[e.smooth=0]="smooth",e[e.recentAvg=1]="recentAvg",e[e.latestOne=2]="latestOne"}(wt||(wt={}));var Et=function(){function e(){Pt(this,"cnt",0),Pt(this,"frameRateRecords",[]),Pt(this,"config",{maxFrameRateRecordsLen:10}),Pt(this,"timer",null)}return e.prototype.configure=function(e){this.config=e},e.prototype.destroy=function(){this.clearTimer()},e.prototype.reset=function(){this.frameRateRecords=[],this.clearTimer()},e.prototype.getFrameRate=function(e){return this.frameRateRecords.length?e===wt.smooth?this.smooth(this.frameRateRecords):e===wt.recentAvg?this.recentAvg(this.frameRateRecords):this.latestOne(this.frameRateRecords):null},e.prototype.sample=function(){this.start(),this.cnt+=1},e.prototype.start=function(){this.setTimer()},e.prototype.stop=function(){this.clearTimer()},e.prototype.setTimer=function(){var e=this;this.timer||(this.timer=wc((function(){e.frameRateRecords.push(e.cnt),e.frameRateRecords.length>e.config.maxFrameRateRecordsLen&&e.frameRateRecords.shift(),e.cnt=0}),1e3))},e.prototype.clearTimer=function(){this.timer&&(clearInterval(this.timer),this.timer=null)},e.prototype.smooth=function(e){if(e.length<5)return null;var t=jl(e).call(e,3);return Math.floor(lE(t).call(t,(function(e,t){return e+t}))/t.length)},e.prototype.recentAvg=function(e){if(e.length<3)return null;var t=jl(e).call(e,e.length-3);return Math.floor(lE(t).call(t,(function(e,t){return e+t}))/t.length)},e.prototype.latestOne=function(e){return e.length?e[this.frameRateRecords.length-1]:null},e}();function kt(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var At=function(){function e(){kt(this,"audioClockRate",0),kt(this,"videoClockRate",0),kt(this,"frameRate",new Et)}return e.prototype.reset=function(){this.audioClockRate=0,this.videoClockRate=0},e.prototype.ready=function(){return this.audioClockRate&&this.videoClockRate},e.prototype.sample=function(e){var t,r=this;ic(t=e.getReceivers()).call(t,(function(e){r.sampleClockRate(e)}))},e.prototype.sampleClockRate=function(e){var t=this;e.getStats().then((function(e){var r,n;try{for(var i=uu(e),o=i.next();!o.done;o=i.next()){var a,s,c=o.value,u=bE(c).call(c,(function(e){return"codec"===e.type}));u&&"number"==typeof u.clockRate&&(Qc(a=u.mimeType).call(a,"audio")?t.audioClockRate=u.clockRate:Qc(s=u.mimeType).call(s,"video")&&(t.videoClockRate=u.clockRate))}}catch(e){r={error:e}}finally{try{o&&!o.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}}))},e}();function xt(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Lt=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return xt(n=e.apply(this,du([],lu(t),!1))||this,"tagName","LEBSource"),xt(n,"sourceConfig",null),xt(n,"stopWriting",!1),xt(n,"videoAbortController",new AbortController),xt(n,"audioAbortController",new AbortController),n}return iu(t,e),t.prototype.configure=function(e){return this.sourceConfig=e,this},t.prototype.isLeb=function(){return!0},t.prototype.load=function(){if(!this.sourceConfig)throw new Error;if(!this.peerManager)throw new Error("miss peerManager");e.prototype.load.call(this);var t=(new He).create(Ah({targetPid:this.sourceConfig.srcPid},this.sourceConfig));this.peer=t,this.peerManager.addPeer(t),this.listenConnection(t.getConnection())},t.prototype.close=function(){this.closed||(this.audioAbortController.abort("LEBSource_abort_audio"),this.videoAbortController.abort("LEBSource_abort_video"),e.prototype.close.call(this))},t.prototype.onTrack=function(e,t){var r=this;if(!this.videoEncodedFrameCache||!this.audioEncodedFrameCache)throw Error("miss cache");var n=e.track,o=e.receiver.createEncodedStreams();if(o){if(!this.syncController)throw Error("missing param");if(!this.streamInfo)throw Error("missing param");var s=o.readable.pipeThrough(new TransformStream({transform:function(t,n){if(!r.closed){if(!r.syncController)throw Error("missing param");if(!r.stopWriting){var o,s,c;if(!r.stopWriting&&r.syncController.prepareToStopCdn&&"video"===e.track.kind&&"key"===t.type)return r.syncController.cdnStoppingReady=!0,r.syncController.lastestCdnKeyTimestamp=t.timestamp,r.stopWriting=!0,void(null===(o=r.streamInfo)||void 0===o||o.frameRate.stop());r.syncController&&("audio"===e.track.kind?(null!==(s=r.syncController)&&void 0!==s&&s.lastRecvCdnAudioTimestamp||r.emit(i,{code:a.INF_PLAY_EVT_STREAM_FIRST_AUDIO_FRAME,msg:"鏀跺埌浜嗙涓€涓煶棰戝抚"}),r.syncController.lastRecvCdnAudioTimestamp=t.timestamp):(null!==(c=r.syncController)&&void 0!==c&&c.lastRecvCdnVideoTimestamp||r.emit(i,{code:a.INF_PLAY_EVT_STREAM_FIRST_VIDEO_FRAME,msg:"鏀跺埌浜嗙涓€涓棰戝抚"}),r.syncController.lastRecvCdnVideoTimestamp=t.timestamp)),n.enqueue(t)}}}})).pipeThrough(new TransformStream({transform:function(e,t){var n,i;null===(n=r.statUnit)||void 0===n||n.number("cdnDownloadBytes").plus(e.data.byteLength),null===(i=r.reportUnit)||void 0===i||i.number("cdn_bytes").plus(e.data.byteLength),t.enqueue(e)}}));"video"===n.kind?s.pipeThrough(new TransformStream({transform:function(e,t){var n;r.reportUnit.number("cdnDownloadVideoBytes").plus(e.data.byteLength),null===(n=r.statUnit)||void 0===n||n.number("cdnDownloadVideoBytes").plus(e.data.byteLength),t.enqueue(e)}})).pipeThrough(new TransformStream({transform:function(e,t){var n,i;null===(n=r.streamInfo)||void 0===n||n.frameRate.sample(),null===(i=r.videoEncodedFrameCache)||void 0===i||i.cache(e),t.enqueue(e)}})).pipeTo(o.writable,{signal:this.videoAbortController.signal}).catch((function(e){})):s.pipeThrough(new TransformStream({transform:function(e,t){var n;r.reportUnit.number("cdnDownloadAuidoBytes").plus(e.data.byteLength),null===(n=r.statUnit)||void 0===n||n.number("cdnDownloadAuidoBytes").plus(e.data.byteLength),t.enqueue(e)}})).pipeThrough(new TransformStream({transform:function(e,t){var n;null===(n=r.audioEncodedFrameCache)||void 0===n||n.cache(e),t.enqueue(e)}})).pipeTo(o.writable,{signal:this.audioAbortController.signal}).catch((function(e){})),this.emitTrack(n,t)}},t}(ue);function It(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Dt=function(){function e(){It(this,"targetWritableStream",null),It(this,"targetWriter",null),It(this,"proxiedWritableStream",null),It(this,"getNextChunkCb",null)}return e.prototype.destroy=function(){var e;this.targetWritableStream=null,null===(e=this.targetWriter)||void 0===e||e.abort(),this.targetWriter=null,this.proxiedWritableStream=null,this.getNextChunkCb=null},e.prototype.setTargetWriableStream=function(e){return this.targetWritableStream=e,this.targetWriter=e.getWriter(),this},e.prototype.setGetNextChunkCb=function(e){this.getNextChunkCb=e},e.prototype.getStream=function(){var e=this;if(this.proxiedWritableStream)return this.proxiedWritableStream;var t=new CountQueuingStrategy({highWaterMark:10});return this.proxiedWritableStream=new WritableStream({write:function(t){e.writeChunk()},close:function(){},abort:function(e){}},t),this.proxiedWritableStream},e.prototype.write=function(){this.writeChunk()},e.prototype.writeChunk=function(){if(!this.getNextChunkCb)throw Error("missing param");for(var e=this.getNextChunkCb();e;){var t;null===(t=this.targetWriter)||void 0===t||t.write(e).catch((function(e){})),e=this.getNextChunkCb()}},e}();function Mt(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ot,Nt=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return Mt(n=e.apply(this,du([],lu(t),!1))||this,"tagName","P2PSource"),Mt(n,"sourceConfig",null),Mt(n,"p2pClient",null),Mt(n,"videoReady",!1),Mt(n,"p2pAudioCache",new dt),Mt(n,"p2pVideoCache",new dt),Mt(n,"audioProxyStream",new Dt),Mt(n,"videoProxyStream",new Dt),Mt(n,"recvFrameRate",new Et),Mt(n,"audioAbortController",new AbortController),Mt(n,"videoAbortController",new AbortController),n}return iu(t,e),t.prototype.configure=function(e){return this.sourceConfig=e,this},t.prototype.setClient=function(e){return this.p2pClient=e,this},t.prototype.setPeerManager=function(e){return this.peerManager=e,this},t.prototype.destroy=function(){this.p2pAudioCache.destroy(),this.p2pVideoCache.destroy(),this.audioProxyStream.destroy(),this.videoProxyStream.destroy(),this.audioAbortController.abort("P2PSource_abort_audio"),this.videoAbortController.abort("P2PSource_abort_video"),e.prototype.destroy.call(this)},t.prototype.isLeb=function(){return!1},t.prototype.load=function(){if(!this.sourceConfig)throw new Error("missing p2p source config");if(this.peerManager&&this.p2pClient){var t=this.peerManager.getPeer(this.sourceConfig.srcPid);if(t){this.peer=t;var r=t.getConnection();r&&(e.prototype.load.call(this),this.p2pClient.request({host:"P2PServer",to:this.sourceConfig.srcPid,path:"SubscribeTrack",payload:"subscribe",type:"app"}),this.listenConnection(r))}}},t.prototype.close=function(){if(!this.sourceConfig)throw new Error("missing p2p source config");this.p2pClient&&(this.closed||(this.p2pClient.request({host:"P2PServer",to:this.sourceConfig.srcPid,path:"SubscribeTrack",payload:"cancelSubscribe",type:"app"}),e.prototype.close.call(this)))},t.prototype.onTrack=function(e,t){var r=e.track;"video"===r.kind?this.onVideoTrack(e,t):"audio"===r.kind&&this.onAudioTrack(e,t)},t.prototype.onAudioTrack=function(e,t){var r=this,n=e.receiver.createEncodedStreams();this.audioProxyStream.setTargetWriableStream(n.writable).setGetNextChunkCb((function(){var e;if(r.syncController&&r.p2pAudioCache&&r.videoReady&&r.streamInfo&&r.streamInfo.ready()&&null!==(e=r.syncController)&&void 0!==e&&e.cdnStoppingReady&&r.syncController.p2pVideoRecvEnough){var t=r.p2pAudioCache.getHead();return t?(r.p2pAudioCache.removeHead(),r.syncController.lastWriteAudioTimestamp=t.timestamp,r.videoProxyStream.write(),t.chunk):void 0}})),n.readable.pipeThrough(new TransformStream({transform:function(e,t){r.closed||t.enqueue(e)}})).pipeThrough(new TransformStream({transform:function(e,t){var n,i,o;null===(n=r.reportUnit)||void 0===n||n.number("p2p_bytes").plus(e.data.byteLength),null===(i=r.statUnit)||void 0===i||i.number("p2pDownloadBytes").plus(e.data.byteLength),null===(o=r.statUnit)||void 0===o||o.number("p2pDownloadAuidoBytes").plus(e.data.byteLength),t.enqueue(e)}})).pipeThrough(new TransformStream({transform:function(e,t){if(0!==e.data.byteLength){if(r.syncController){var n=ft.decode(e.data);n.timestamp<r.syncController.lastRecvCdnAudioTimestamp||(e.data=n.chunkData,r.p2pAudioCache.cache({data:n.chunkData,timestamp:n.timestamp,type:n.type,chunk:e}),t.enqueue({}))}}else t.enqueue(e)}})).pipeTo(this.audioProxyStream.getStream(),{signal:this.audioAbortController.signal}).catch((function(e){})),this.emitTrack(e.track,t)},t.prototype.onVideoTrack=function(e,t){var r=this,n=e.receiver,i=1,o=0,a=n.createEncodedStreams();this.videoProxyStream.setTargetWriableStream(a.writable).setGetNextChunkCb((function(){if(r.syncController&&r.p2pVideoCache&&r.streamInfo&&r.streamInfo.ready()&&(r.p2pVideoCache.size>5&&!r.syncController.p2pVideoRecvEnough&&(r.syncController.p2pVideoRecvEnough=!0),r.syncController.lastWriteAudioTimestamp)){var e=r.p2pVideoCache.getHead();if(e){var t=r.syncController,n=t.lastRecvCdnVideoTimestamp,i=t.lastRecvCdnAudioTimestamp,o=r.streamInfo,a=o.videoClockRate,s=o.audioClockRate,c=i/s-n/a;return r.syncController.lastWriteAudioTimestamp/s-c<e.timestamp/a+.1?void 0:(r.p2pVideoCache.removeHead(),e.chunk)}}})),a.readable.pipeThrough(new TransformStream({transform:function(e,t){r.closed||t.enqueue(e)}})).pipeThrough(new TransformStream({transform:function(e,t){if(!r.streamInfo||!r.sourceConfig)throw Error("missing param");r.recvFrameRate.sample();var n=r.recvFrameRate.getFrameRate(wt.recentAvg),i=r.streamInfo.frameRate.getFrameRate(wt.smooth);null!==n&&null!==i&&n<i*r.sourceConfig.stuckFrameRateRatio&&2===(o+=1)?r.emit(ce):t.enqueue(e)}})).pipeThrough(new TransformStream({transform:function(e,t){var n,i,o;null===(n=r.reportUnit)||void 0===n||n.number("p2p_bytes").plus(e.data.byteLength),null===(i=r.statUnit)||void 0===i||i.number("p2pDownloadBytes").plus(e.data.byteLength),null===(o=r.statUnit)||void 0===o||o.number("p2pDownloadVideoBytes").plus(e.data.byteLength),t.enqueue(e)}})).pipeThrough(new TransformStream({transform:function(n,o){if(r.syncController){if(n.data.byteLength){var a=ft.decode(n.data);if("key"===a.type&&(i-=1),n.data=a.chunkData,r.syncController.prepareToStopCdn||"key"!==a.type||0!==i||(r.syncController.prepareToStopCdn=!0),!r.syncController.cdnStoppingReady)return;if(a.timestamp<r.syncController.lastestCdnKeyTimestamp)return;a.timestamp,r.syncController.lastestCdnKeyTimestamp,r.videoReady||(r.videoReady=!0,r.emitTrack(e.track,t)),r.p2pVideoCache.cache({data:a.chunkData,timestamp:a.timestamp,type:a.type,chunk:n})}o.enqueue(n)}}})).pipeTo(this.videoProxyStream.getStream(),{signal:this.videoAbortController.signal}).catch((function(e){}))},t}(ue);!function(e){e[e.LEB=0]="LEB",e[e.P2P=1]="P2P"}(Ot||(Ot={}));var Vt,Ft=function(){function e(){}return e.create=function(e){switch(e){case Ot.LEB:return new Lt;case Ot.P2P:return new Nt}},e}();function Ut(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e){e[e.IDEL=0]="IDEL",e[e.WAITING=1]="WAITING",e[e.COMPLETE=2]="COMPLETE"}(Vt||(Vt={}));var jt=function(e){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this;return Ut(n=e.apply(this,du([],lu(t),!1))||this,"tagName","Subscriber"),Ut(n,"parent",{pid:""}),Ut(n,"child",{pid:""}),Ut(n,"subscribeStatus",{status:Vt.IDEL,pid:""}),Ut(n,"config",null),Ut(n,"p2pClient",null),Ut(n,"peerManager",null),Ut(n,"videoEncodedFrameCache",new dt),Ut(n,"audioEncodedFrameCache",new dt),Ut(n,"statUnit",null),Ut(n,"peerCooler",new _t),Ut(n,"syncController",new Rt),Ut(n,"streamInfo",new At),Ut(n,"sampleTimer",null),Ut(n,"reportUnit",new T),Ut(n,"lebSource",null),Ut(n,"p2pSource",null),n}return iu(t,e),t.prototype.destroy=function(){this.peerManager=null,this.reporter=null,this.p2pClient=null,this.peerCooler.destroy(),this.cancelsubscribe(),this.removeChild(this.child.pid),this.clearSampleTimer(),this.closeSource(Ot.LEB),this.closeSource(Ot.P2P),e.prototype.destroy.call(this)},t.prototype.configure=function(e){return this.config=e,this.peerCooler.configure({coolingTime:this.config.subscribeCoolingTime}),this},t.prototype.setPeerManager=function(e){return this.peerManager=e,this},t.prototype.setReporter=function(t){return null==t||t.registerReportUint(this.objName,this.reportUnit),e.prototype.setReporter.call(this,t)},t.prototype.setClient=function(e){return this.p2pClient=e,this},t.prototype.setStatUnit=function(e){this.statUnit=e},Tf(t.prototype,"currentSourcePeerconnection",{get:function(){var e,t;return this.parent.pid&&(null===(e=this.peerManager)||void 0===e||null===(t=e.getPeer(this.parent.pid))||void 0===t?void 0:t.getConnection().getRTCPeerConnection())||null},enumerable:!1,configurable:!0}),t.prototype.subscribeLEB=function(e){var t=this;if(!this.peerManager||!this.statUnit)throw Error("subscribeLEB missing param");this.syncController.reset(),this.streamInfo.reset(),this.streamInfo.frameRate.reset(),this.reportUnit.number("src_cdn_sub").plus(1);var r=Ft.create(Ot.LEB);return this.lebSource=r,r.configure(e).setSyncController(this.syncController).setReporter(this.reporter).setPeerManager(this.peerManager).setEncodedVideoChunkCache(this.videoEncodedFrameCache).setEncodedAudioChunkCache(this.audioEncodedFrameCache).setStatUnit(this.statUnit).setStreamInfo(this.streamInfo).on(ie,(function(e){"video"===e.track.kind&&(t.reportUnit.number("src_cdn_succ").plus(1),t.updateParent({pid:e.srcPid,source:r}),t.updateChildTrack(e.track,{srcPid:e.srcPid}),t.sampleTimer||(t.sampleTimer=wc((function(){t.streamInfo.ready()?t.clearSampleTimer():t.streamInfo.sample(e.rtcPeerConnction)}),1e3)))})).once(re,(function(){t.reportUnit.number("src_cdn_timeout").plus(1),t.closeSource(Ot.LEB)})).once(se,(function(e){e.srcPid,t.reportUnit.number("src_cdn_failed").plus(1),t.closeSource(Ot.LEB)})).once(ne,(function(e){e.srcPid,t.reportUnit.number("src_cdn_disconnect").plus(1),t.closeSource(Ot.LEB)})).once(ae,(function(e){e.srcPid,t.reportUnit.number("src_cdn_error").plus(1),t.closeSource(Ot.LEB)})).load(),r},t.prototype.subscribeP2P=function(e){var t=this;if(!(this.p2pClient&&this.peerManager&&this.statUnit&&this.config))throw Error("[Subscriber] [subscribeP2P] missing param");if(!0===this.allowSubscribe()){var r=e||this.selectAvaliableSubscribePeer();if(r){var n=Ft.create(Ot.P2P);return this.p2pSource=n,this.parent.pid&&this.parent.source&&this.parent.source.setSyncController(this.syncController),this.reportUnit.number("src_p2p_sub").plus(1),n.configure({srcPid:r,stuckFrameRateRatio:this.config.stuckFrameRateRatio}).setReporter(this.reporter).setClient(this.p2pClient).setPeerManager(this.peerManager).setStatUnit(this.statUnit).setSyncController(this.syncController).setStreamInfo(this.streamInfo).on(ie,(function(e){"video"===e.track.kind&&t.updateParent({pid:e.srcPid,source:n})})).once(re,(function(){t.reportUnit.number("src_p2p_timeout").plus(1),t.peerCooler.add(r),t.closeSource(Ot.P2P),t.unmarkSubscribeP2P()})).once(se,(function(e){e.srcPid,t.reportUnit.number("src_p2p_failed").plus(1),t.closeSource(Ot.P2P),t.unmarkSubscribeP2P()})).once(ne,(function(){t.reportUnit.number("src_p2p_disconnect").plus(1),t.closeSource(Ot.P2P),t.unmarkSubscribeP2P()})).once(ae,(function(){t.reportUnit.number("src_p2p_error").plus(1),t.closeSource(Ot.P2P),t.unmarkSubscribeP2P()})).once(ce,(function(){t.reportUnit.number("src_p2p_stuck").plus(1),t.closeSource(Ot.P2P),t.unmarkSubscribeP2P()})).on(ie,(function(e){"video"===e.track.kind&&(t.reportUnit.number("src_p2p_succ").plus(1),t.subscribeStatus.status=Vt.COMPLETE)})).load(),this.markSubscribeP2P(),n}}},t.prototype.cancelsubscribe=function(){this.parent.pid&&this.parent.source&&(this.parent.source.close(),this.parent.pid="",this.parent.source=void 0)},t.prototype.onSubscribe=function(e){var t=this;if(this.reportUnit.number("on_sub").plus(1),!this.peerManager||!this.statUnit)throw Error("[Subscriber] [onSubscribe] missing param");if(!0===this.allowToBeSubscribed()){var r=new Ct,n=r.setSrcPid(this.parent.pid).setTargetPid(e).setReporter(this.reporter).setPeerManager(this.peerManager).setVideoEncodedFrameCache(this.videoEncodedFrameCache).setAudioEncodedFrameCache(this.audioEncodedFrameCache).setStatUnit(this.statUnit).onSubscribe();r.on("EVENT_RPOVIDER_DISCONNECT",(function(){t.removeChild(e)})).on("EVENT_RPOVIDER_FAILED",(function(){t.removeChild(e)})),n?(this.setChild(e,r),this.reportUnit.number("on_p2p_sub_succ").plus(1)):r.destroy()}},t.prototype.onCancelSubscribe=function(e){this.reportUnit.number("on_p2p_cancel_sub").plus(1),this.removeChild(e)},t.prototype.getParentId=function(){return this.parent.pid},t.prototype.getChildId=function(){return this.child.pid},t.prototype.allowToBeSubscribed=function(){var e;return this.parent.pid&&this.parent.source?""!==this.child.pid?"鏈夊瓙鑺傜偣":!!(null===(e=this.parent.source)||void 0===e?void 0:e.isLeb())||"鐖惰妭鐐逛笉鏄痗dn":"鏃犱笂娓歌妭鐐�"},t.prototype.allowSubscribe=function(){var e;return this.child.pid?"鏈夊瓙鑺傜偣":this.streamInfo.ready()?this.subscribeStatus.status===Vt.WAITING?"姝ｅ湪璁㈤槄涓�":this.subscribeStatus.status===Vt.COMPLETE?"宸茬粡璁㈤槄":!(this.parent.pid&&(null===(e=this.parent.source)||void 0===e||!e.isLeb()))||"鐖惰妭鐐逛笉鏄痗dn":"娴佷俊鎭湭閲囬泦鍒�"},t.prototype.selectAvaliableSubscribePeer=function(){var e,t="";if(this.parent.pid&&null!==(e=this.parent.source)&&void 0!==e&&e.isLeb()){var r,n=this.peerCooler.getList(),i=null===(r=this.peerManager)||void 0===r?void 0:r.getBestPeer({excludePeerId:du([this.parent.pid],lu(n),!1)});i&&(t=i.pid)}return t},t.prototype.closeSource=function(e){var t,r;e===Ot.LEB&&(null===(t=this.lebSource)||void 0===t||t.close(),this.lebSource=null),e===Ot.P2P&&(null===(r=this.p2pSource)||void 0===r||r.close(),this.p2pSource=null)},t.prototype.removeChild=function(e){var t;this.child.pid&&e!==this.child.pid||(this.child.pid="",null===(t=this.child.provider)||void 0===t||t.destroy(),this.child.provider=void 0)},t.prototype.setChild=function(e,t){this.child.pid=e,this.child.provider=t},t.prototype.updateParent=function(e){e.pid!==this.parent.pid&&this.cancelsubscribe(),e.source&&(this.parent.pid=e.pid,this.parent.source=e.source)},t.prototype.updateChildTrack=function(e,t){this.child.pid&&this.child.provider&&this.child.provider.updateTrack(e,t.srcPid)},t.prototype.markSubscribeP2P=function(){this.subscribeStatus.status=Vt.WAITING},t.prototype.unmarkSubscribeP2P=function(){this.subscribeStatus.status=Vt.IDEL},t.prototype.clearSampleTimer=function(){this.sampleTimer&&(clearTimeout(this.sampleTimer),this.sampleTimer=null)},t}(E);function Bt(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Wt=function(e){function t(){var t=this;return Bt(t=e.call(this)||this,"tagName","StreamSourceModule"),Bt(t,"config",null),Bt(t,"reporter",null),Bt(t,"dispatch",new de),Bt(t,"signal",new Ae),Bt(t,"p2pServer",new he),Bt(t,"p2pClient",new ye),Bt(t,"signalTransport",new Qe),Bt(t,"p2pPeerBuilder",new Xe),Bt(t,"peerManager",new et),Bt(t,"peerCache",new it),Bt(t,"signalApi",new at),Bt(t,"subscribeTrackApi",new ct),Bt(t,"subscriber",new jt),t.signal.on(Ee,(function(e){if(!t.config)throw Error("missing config");t.p2pClient.setLocalPid(e),t.p2pPeerBuilder.setLocalPid(e),t.peerCache.setPeerId(e).setStreamId(t.config.streamId()),t.peerCache.start()})),t.signalTransport.setSignal(t.signal),t.p2pClient.setTransport(t.signalTransport),t.p2pServer.setTransport(t.signalTransport),t.p2pPeerBuilder.setClient(t.p2pClient).setServer(t.p2pServer),t.peerManager.setPeerCache(t.peerCache).setPeerBuilder(t.p2pPeerBuilder),t.subscriber.setClient(t.p2pClient).setPeerManager(t.peerManager),t.subscribeTrackApi.setSubscriber(t.subscriber),t.dispatch.setSubscriber(t.subscriber),t.initApi(),t}return iu(t,e),Tf(t.prototype,"currentSourcePeerconnection",{get:function(){return this.subscriber.currentSourcePeerconnection},enumerable:!1,configurable:!0}),t.prototype.setMediaStreamProcessor=function(e){return this.dispatch.setMediaStreamProcessor(e),this},t.prototype.setReporter=function(e){return this.reporter=e,this.subscriber.setReporter(e),this.dispatch.setReporter(e),this.peerManager.setReporter(e),this},t.prototype.setStatUnit=function(e){return this.subscriber.setStatUnit(e),this},t.prototype.setBus=function(t){return e.prototype.setBus.call(this,t),this.dispatch.setBus(t),this},t.prototype.destroy=function(){this.dispatch.destroy(),this.peerCache.destroy(),this.peerManager.destroy(),this.p2pClient.destroy(),this.p2pServer.destroy(),this.signal.destroy(),this.signalApi.destroy(),this.subscribeTrackApi.destroy(),this.subscriber.destroy(),e.prototype.destroy.call(this)},t.prototype.configure=function(e){return this.config=e,this.peerCache.configure(this.config),this.peerManager.configure(this.config),this.p2pPeerBuilder.configure(this.config),this.dispatch.configure(e),this.subscriber.configure(e),this},t.prototype.init=function(){if(!this.config)throw Error("missing config");this.peerCache.init(),this.peerManager.init(),this.signal.connect(this.config.signal),this.dispatch.init()},t.prototype.load=function(e){var t=e.streamUrl;if(!this.config)throw Error("missing config");return this.dispatch.load(t)},t.prototype.initApi=function(){this.signalApi.setPeerBuilder(this.p2pPeerBuilder).setPeerManager(this.peerManager).setServer(this.p2pServer),this.subscribeTrackApi.setServer(this.p2pServer)},t}(E),Gt=function(){try{var e,t,r;return!!(window.MediaStreamTrack&&window.MediaStream&&window.MediaStreamTrack&&null!==(e=window.RTCPeerConnection)&&void 0!==e&&e.prototype.getReceivers&&null!==(t=window.RTCPeerConnection)&&void 0!==t&&t.prototype.getSenders&&null!==(r=window.RTCRtpReceiver)&&void 0!==r&&r.prototype.createEncodedStreams&&window.HTMLCanvasElement.prototype.captureStream&&window.MediaStreamTrackGenerator&&window.MediaStreamTrackProcessor)}catch(e){return!1}},Zt=function(){function e(){var e,t,r;r=null,(t="video")in(e=this)?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}return e.prototype.destroy=function(){this.video&&(this.video.srcObject=null,this.video=null)},e.prototype.setVideoSrcObj=function(e){if(!this.video)throw Error("miss video");var t=this.video;t.onwaiting=function(){},t.ontimeupdate=function(){},t.onplay=function(){},t.onplaying=function(){},t.onpause=function(){},t.srcObject=e,t.onseeking=function(e){},t.onseeked=function(e){}},e.prototype.play=function(){var e;null===(e=this.video)||void 0===e||e.play()},e.prototype.setVideo=function(e){this.video=e},e}();function Yt(e,t){var r=Pf(e);if(vf){var n=vf(e);t&&(n=ru(n).call(n,(function(t){return Of(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ht(e){for(var t=1;t<arguments.length;t++){var r,n,i=null!=arguments[t]?arguments[t]:{};t%2?ic(r=Yt(Object(i),!0)).call(r,(function(t){Xt(e,t,i[t])})):Hf?$f(e,Hf(i)):ic(n=Yt(Object(i))).call(n,(function(t){Tf(e,t,Of(i,t))}))}return e}function Xt(e,t,r){return t in e?Tf(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Jt=function(e){function t(){var t,r=this;return Xt(r=e.call(this)||this,"player",new Zt),Xt(r,"mediaStreamProcessor",new A),Xt(r,"source",new Wt),Xt(r,"reporter",new z),Xt(r,"stats",new T),Xt(r,"statsTotal",new T),Xt(r,"config",new f),Xt(r,"conf",new N),W=mi(t="".concat(NS(),"_")).call(t,B().split("-")[0]),r.stats.configure({deleteZeroNumber:!1,fieldsReportedWithZeroNumber:[],fieldsReservedValueAfterReport:[]}),r.statsTotal.configure({deleteZeroNumber:!1,fieldsReportedWithZeroNumber:[],fieldsReservedValueAfterReport:["uploadBytesTotal","uploadAuidoBytesTotal","uploadVideoBytesTotal","cdnDownloadBytesTotal","cdnDownloadAuidoBytesTotal","cdnDownloadVideoBytesTotal","p2pDownloadBytesTotal","p2pDownloadAuidoBytesTotal","p2pDownloadVideoBytesTotal"]}),r.source.setMediaStreamProcessor(r.mediaStreamProcessor).setReporter(r.reporter).setStatUnit(r.stats).setBus(r),r.source.configure(r.config.config),r.reporter.configure(r.config.config).init(),r.conf.on("EVENT_CONF_LOADED",(function(e){r.config.update(e)})).once("EVENT_CONF_LOADED",(function(){r.source.init()})),r.reporter.start(),r}return iu(t,e),t.support=function(){return Gt()},t.create=function(e){var r=new t;return r.configure(e),r},Tf(t,"Event",{get:function(){return o},enumerable:!1,configurable:!0}),Tf(t,"EventCode",{get:function(){return a},enumerable:!1,configurable:!0}),Tf(t,"version",{get:function(){return Z},enumerable:!1,configurable:!0}),Tf(t,"uuid",{get:function(){return B()},enumerable:!1,configurable:!0}),t.prototype.destroy=function(){this.removeAllListeners(),this.reporter.destroy(),this.conf.destroy(),this.source.destroy(),this.mediaStreamProcessor.destroy(),this.player.destroy()},t.prototype.load=function(e){return su(this,void 0,void 0,(function(){var t=this;return cu(this,(function(r){return[2,(this.config.update({streamUrl:e}),this.source.load({streamUrl:e}).then((function(){t.conf.configure({confBaseUrl:t.config.config.confBaseUrl,confInterval:0,domain:t.config.config.xp2pDomain,streamId:t.config.config.streamId()}),t.conf.load()})),{srcObject:this.mediaStreamProcessor.getMediaObj()})]}))}))},t.prototype.setVideoHtmlElement=function(e){this.player.setVideo(e),this.player.setVideoSrcObj(this.mediaStreamProcessor.getMediaObj())},t.prototype.getSDKStats=function(){return this.stats.number("uploadBytes").plus(0),this.stats.number("uploadAuidoBytes").plus(0),this.stats.number("uploadVideoBytes").plus(0),this.stats.number("cdnDownloadBytes").plus(0),this.stats.number("cdnDownloadAuidoBytes").plus(0),this.stats.number("cdnDownloadVideoBytes").plus(0),this.stats.number("p2pDownloadBytes").plus(0),this.stats.number("p2pDownloadAuidoBytes").plus(0),this.stats.number("p2pDownloadVideoBytes").plus(0),this.statsTotal.number("uploadBytesTotal").plus(this.stats.number("uploadBytes").value),this.statsTotal.number("uploadAuidoBytesTotal").plus(this.stats.number("uploadAuidoBytes").value),this.statsTotal.number("uploadVideoBytesTotal").plus(this.stats.number("uploadVideoBytes").value),this.statsTotal.number("cdnDownloadBytesTotal").plus(this.stats.number("cdnDownloadBytes").value),this.statsTotal.number("cdnDownloadAuidoBytesTotal").plus(this.stats.number("cdnDownloadAuidoBytes").value),this.statsTotal.number("cdnDownloadVideoBytesTotal").plus(this.stats.number("cdnDownloadVideoBytes").value),this.statsTotal.number("p2pDownloadBytesTotal").plus(this.stats.number("p2pDownloadBytes").value),this.statsTotal.number("p2pDownloadAuidoBytesTotal").plus(this.stats.number("p2pDownloadAuidoBytes").value),this.statsTotal.number("p2pDownloadVideoBytesTotal").plus(this.stats.number("p2pDownloadVideoBytes").value),Ht(Ht({},this.stats.generate()),this.statsTotal.generate())},t.prototype.getStats=function(){var e=this.source.currentSourcePeerconnection;return null==e?void 0:e.getStats()},Tf(t.prototype,"playId",{get:function(){return G()},enumerable:!1,configurable:!0}),t.prototype.configure=function(e){this.config.update(e)},t}(e.EventEmitter)}(),n.default}()}(CE);var SE,_E,TE,wE,RE,PE,EE,kE,AE=r(CE.exports),xE="1.3.4",LE=["overseas-webrtc.liveplay.myqcloud.com","oswebrtc-lint.liveplay.myqcloud.com"],IE="TX_LIVE_PLAYER_SIGNAL_DATA",DE=["webrtc-signal-scheduler.tlivesource.com","bak-webrtc-signal-scheduler.tlivesource.com"],ME="max-width:100%;max-height:100%;object-fit:contain;display:block;margin:0 auto;",OE="kdccmd",NE="kdccmdp",VE="RequestSignalError";!function(e){e[e.DISCONNECTED=0]="DISCONNECTED",e[e.CONNECTING=1]="CONNECTING",e[e.CONNECTED=2]="CONNECTED"}(SE||(SE={})),function(e){e[e.FAILURE=-1]="FAILURE",e[e.SUCCESS=0]="SUCCESS"}(_E||(_E={})),function(e){e[e.NEED_RECONNECT=-1]="NEED_RECONNECT",e[e.MANUAL_CLOSE=0]="MANUAL_CLOSE"}(TE||(TE={})),function(e){e[e.SEND=0]="SEND",e[e.RECEIVE=1]="RECEIVE"}(wE||(wE={})),function(e){e[e.INIT=0]="INIT",e[e.PLAYING=1]="PLAYING",e[e.WAITING=2]="WAITING"}(RE||(RE={})),function(e){e[e.PLAY_EVT_STREAM_BEGIN=1001]="PLAY_EVT_STREAM_BEGIN",e[e.PLAY_EVT_SERVER_CONNECTED=1002]="PLAY_EVT_SERVER_CONNECTED",e[e.PLAY_EVT_PLAY_BEGIN=1003]="PLAY_EVT_PLAY_BEGIN",e[e.PLAY_EVT_PLAY_STOP=1004]="PLAY_EVT_PLAY_STOP",e[e.PLAY_EVT_SERVER_RECONNECT=1005]="PLAY_EVT_SERVER_RECONNECT",e[e.PLAY_EVT_STREAM_EMPTY=1006]="PLAY_EVT_STREAM_EMPTY",e[e.PLAY_EVT_REQUEST_PULL_BEGIN=1007]="PLAY_EVT_REQUEST_PULL_BEGIN",e[e.PLAY_EVT_REQUEST_PULL_SUCCESS=1008]="PLAY_EVT_REQUEST_PULL_SUCCESS",e[e.PLAY_EVT_PLAY_WAITING_BEGIN=1009]="PLAY_EVT_PLAY_WAITING_BEGIN",e[e.PLAY_EVT_PLAY_WAITING_STOP=1010]="PLAY_EVT_PLAY_WAITING_STOP",e[e.PLAY_ERR_WEBRTC_FAIL=-2001]="PLAY_ERR_WEBRTC_FAIL",e[e.PLAY_ERR_REQUEST_PULL_FAIL=-2002]="PLAY_ERR_REQUEST_PULL_FAIL",e[e.PLAY_ERR_PLAY_FAIL=-2003]="PLAY_ERR_PLAY_FAIL",e[e.PLAY_ERR_SERVER_DISCONNECT=-2004]="PLAY_ERR_SERVER_DISCONNECT",e[e.PLAY_ERR_DECODE_FAIL=-2005]="PLAY_ERR_DECODE_FAIL",e[e.PLAY_ERR_REQUEST_ABR_FAIL=-2006]="PLAY_ERR_REQUEST_ABR_FAIL",e[e.PLAY_EVT_P2P_START_SUCCESS=3001]="PLAY_EVT_P2P_START_SUCCESS",e[e.PLAY_EVT_P2P_SOURCE_SWITCH=3002]="PLAY_EVT_P2P_SOURCE_SWITCH",e[e.PLAY_ERR_P2P_START_FAIL=-4001]="PLAY_ERR_P2P_START_FAIL"}(PE||(PE={}));var FE,UE,jE,BE=/tbs\/(\d+) /i,WE=/OS (\d+)_(\d+)_?(\d+)?/,GE=BE.test(navigator.userAgent),ZE=/firefox\/(\d+)\./i.test(navigator.userAgent),YE=ZE,HE=/UCBrowser\/(\d+)\./i.test(navigator.userAgent),XE=/safari\/(\d+)\./i.test(navigator.userAgent)&&!/chrome\/(\d+)\./i.test(navigator.userAgent),JE=/iPhone|iPad|iOS/i.test(navigator.userAgent),KE=JE,QE=function(){var e=navigator.userAgent.match(WE);return e&&[bP(e[1],10),bP(e[2],10),bP(e[3]||"0",10)]||[]},qE=!!(null===(EE=window.RTCRtpReceiver)||void 0===EE?void 0:EE.prototype.createEncodedStreams),zE=!!(null===(kE=window.RTCRtpReceiver)||void 0===kE?void 0:kE.prototype.getSynchronizationSources),$E=function(e,t,r){var n=e.match(t);return n&&n.length>=r&&bP(n[r],10)},ek=function(e,t){var r,n,i=/^(?:webrtc:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)(?:[^?#]*)(?:\?*)([^?#]*)/.exec(e);if(i){var o=i[1].split("&");try{for(var a=uu(o),s=a.next();!s.done;s=a.next()){var c=lu(s.value.split("="),2),u=c[0],l=c[1];if(u===t)return l}}catch(e){r={error:e}}finally{try{s&&!s.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}}return null},tk=function(e,t,r){var n,i,o=/^(?:webrtc:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)(?:[^?#]*)(?:\?*)([^?#]*)/.exec(e);if(o){var a,s,c=o[1],u=[],l=mi(a="".concat(t,"=")).call(a,r);try{for(var d=uu(c.split("&")),p=d.next();!p.done;p=d.next()){var f,h=lu(p.value.split("="),2),m=h[0],v=h[1];if(m===t)null!==r&&u.push(l);else u.push(mi(f="".concat(m,"=")).call(f,v))}}catch(e){n={error:e}}finally{try{p&&!p.done&&(i=d.return)&&i.call(d)}finally{if(n)throw n.error}}return null===r||$i(u).call(u,l)||u.push(l),mi(s="".concat(e.replace(c,""))).call(s,u.join("&"))}return e},rk={},nk=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],ik=nk[0];try{for(var ok=uu(nk),ak=ok.next();!ak.done;ak=ok.next()){var sk=ak.value;if(sk[1]in document){jE=sk;break}}}catch(e){FE={error:e}}finally{try{ak&&!ak.done&&(UE=ok.return)&&UE.call(ok)}finally{if(FE)throw FE.error}}if(jE)for(var ck=0;ck<jE.length;ck++)rk[ik[ck]]=jE[ck];var uk,lk=function(e){if("function"==typeof e.webkitEnterFullScreen){var t=navigator.userAgent||"";if(/Android/.test(t)||!/Chrome|Mac OS X 10.5/.test(t))return!0}return!1};!function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.EXCELLENT=1]="EXCELLENT",e[e.GOOD=2]="GOOD",e[e.POOR=3]="POOR",e[e.BAD=4]="BAD",e[e.VERY_BAD=5]="VERY_BAD"}(uk||(uk={}));var dk=Hs.map;kr({target:"Array",proto:!0,forced:!Qn("map")},{map:function(e){return dk(this,e,arguments.length>1?arguments[1]:void 0)}});var pk=Gr("Array").map,fk=l,hk=pk,mk=Array.prototype,vk=function(e){var t=e.map;return e===mk||fk(mk,e)&&t===mk.map?hk:t},yk=function(e){var t,r=e.split("\r\n"),n=[];ic(r).call(r,(function(e){var t=e.toLowerCase();$i(t).call(t,"a=rtpmap")&&$i(t).call(t,"h264")&&n.push(e)}));var i=ru(t=vk(n).call(n,(function(e){var t=/a=rtpmap:(\d+)\s/.exec(e);return t&&t.length>1?t[1]:null}))).call(t,(function(e){return null!==e})),o=[];return ic(r).call(r,(function(e){var t=e;if($i(e).call(e,"a=fmtp:111")&&(t="".concat(e,";stereo=1")),$i(e).call(e,"a=fmtp")){var r=/a=fmtp:(\d+)\s/.exec(e);r&&r.length>1&&$i(i).call(i,r[1])&&(t="".concat(e,";sps-pps-idr-in-keyframe=1"))}o.push(t)})),function(e){var t;if(!YE)return e;var r=e.split("\r\n"),n=[],i=[];ic(r).call(r,(function(e){var t=e.toLowerCase();$i(t).call(t,"a=rtpmap")&&$i(t).call(t,"h264")&&n.push(e)})),n.length>1&&i.push.apply(i,du([],lu(jl(n).call(n,1)),!1));var o=ru(t=vk(i).call(i,(function(e){var t=/a=rtpmap:(\d+)\s/.exec(e);return t&&t.length>1?t[1]:null}))).call(t,(function(e){return null!==e})),a=[];return ic(r).call(r,(function(e){var t,r=e;if($i(e).call(e,"a=setup")&&(r="a=setup:passive"),($i(e).call(e,"m=audio")||$i(e).call(e,"m=video"))&&(r=ru(t=e.split(" ")).call(t,(function(e,t){return t<3||!$i(o).call(o,e)})).join(" ")),$i(e).call(e,"a=fmtp")||$i(e).call(e,"a=rtcp-fb")||$i(e).call(e,"a=rtpmap")){var n=/a=(?:fmtp|rtcp-fb|rtpmap):(\d+)\s/.exec(e);if(n&&n.length>1&&$i(o).call(o,n[1]))return}a.push(r)})),a.join("\r\n")}(o.join("\r\n"))},gk=function(e){var t,r,n="",i="";return ic(t=e.split("\r\n")).call(t,(function(e){var t=/(?:a=ice-ufrag:)(.+)/.exec(e);if(t)n=t[1];else{var r=/(?:a=ice-pwd:)(.+)/.exec(e);r&&(i=r[1])}})),mi(r="".concat(n,"_")).call(r,i)};kr({target:"Array",stat:!0},{isArray:Qr});var bk=re.Array.isArray,Ck=Gr("Array").keys,Sk=Sn,_k=qe,Tk=l,wk=Ck,Rk=Array.prototype,Pk={DOMTokenList:!0,NodeList:!0},Ek=function(e){var t=e.keys;return e===Rk||Tk(Rk,e)&&t===Rk.keys||_k(Pk,Sk(e))?wk:t},kk={exports:{}},Ak={};Object.defineProperty(Ak,"__esModule",{value:!0});var xk=function(e){this.bitReservoir=e,this.originalBitReservoir=e};Ak.ExpGolombDecoder=xk,xk.prototype.byteAlign=function(){var e=8*Math.ceil((this.originalBitReservoir.length-this.bitReservoir.length)/8),t=this.originalBitReservoir.length-e,r=this.bitReservoir.length-t;return this.readRawBits(r)},xk.prototype.countLeadingZeros=function(){for(var e=0;e<this.bitReservoir.length;e++)if("1"===this.bitReservoir[e])return e;return-1},xk.prototype.readUnsignedExpGolomb=function(){var e=this.countLeadingZeros(),t=e+1;if(-1===e)throw new Error("Error reading exp-golomb value.");this.readBits(e);var r=this.readBits(t);return r-=1},xk.prototype.readExpGolomb=function(){var e=this.readUnsignedExpGolomb();return 0!==e&&(e=1&e?(e+1)/2:-e/2),e},xk.prototype.readBits=function(e){if(this.bitReservoir.length<e)throw new Error("Error reading bit stream value expected ("+e+") bits remaining but found ("+this.bitReservoir.length+")");var t=parseInt(this.bitReservoir.slice(0,e),2);return this.bitReservoir=this.bitReservoir.slice(e),t},xk.prototype.readRawBits=function(e){if(this.bitReservoir.length<e)throw new Error("Error reading bit stream value expected ("+e+") bits remaining but found ("+this.bitReservoir.length+")");var t=this.bitReservoir.slice(0,e);return this.bitReservoir=this.bitReservoir.slice(e),t},xk.prototype.readUnsignedByte=function(){return this.readBits(8)};var Lk=function(e){this.bitReservoir=e||""};Ak.ExpGolombEncoder=Lk,Lk.prototype.writeUnsignedExpGolomb=function(e){for(var t="",r=(e+1).toString(2),n=r.length-1,i=0;i<n;i++)t+="0";this.bitReservoir+=t+r},Lk.prototype.writeExpGolomb=function(e){e=e<=0?2*-e:2*e-1,this.writeUnsignedExpGolomb(e)},Lk.prototype.writeBits=function(e,t){for(var r="",n=(t&(1<<e)-1).toString(2),i=e-n.length,o=0;o<i;o++)r+="0";this.bitReservoir+=r+n},Lk.prototype.writeRawBits=function(e,t){for(var r="",n=e-t.length,i=0;i<n;i++)r+="0";this.bitReservoir+=r+t},Lk.prototype.writeUnsignedByte=function(e){this.writeBits(8,e)};var Ik={},Dk={};Object.defineProperty(Dk,"__esModule",{value:!0});Dk.typedArrayToBitString=function(e){for(var t=[],r=e.BYTES_PER_ELEMENT||1,n="",i=0;i<e.length;i++)t.push(e[i]);for(i=0;i<r;i++)n+="00000000";return t.map((function(e){return(n+e.toString(2)).slice(8*-r)})).join("")};Dk.bitStringToTypedArray=function(e){for(var t=8-e.length%8,r=0;8!==t&&r<t;r++)e+="0";var n=e.match(/(.{8})/g).map((function(e){return parseInt(e,2)}));return new Uint8Array(n)};Dk.removeRBSPTrailingBits=function(e){return e.split(/10*$/)[0]};Dk.appendRBSPTrailingBits=function(e){var t=e+"10000000",r=t.length%8;return 0===r?t:t.slice(0,-r)};var Mk={};Object.defineProperty(Mk,"__esModule",{value:!0});Mk.mergeObj=function(e,t){var r={};return e&&Object.keys(e).forEach((function(t){r[t]=e[t]})),t&&Object.keys(t).forEach((function(e){r[e]=t[e]})),r};var Ok={};Object.defineProperty(Ok,"__esModule",{value:!0});Ok.list=function(e,t){return{decode:function(r){var n=r.expGolomb,i=r.output,o=r.options,a=r.indexes,s=r.path,c=t?s:s.concat("[list]");return e.forEach((function(e){i=e.decode({expGolomb:n,output:i,options:o,indexes:a,path:c})||i})),i},encode:function(t,r,n,i){e.forEach((function(e){e.encode(t,r,n,i)}))}}};var Nk={};Object.defineProperty(Nk,"__esModule",{value:!0});Nk.propertyHandler=function(e){var t=e.split("["),r=t[0],n=null;return t.length>1&&(n=t.slice(1).map(parseFloat)),{propertyName:r,indexArray:n}};var Vk={};Object.defineProperty(Vk,"__esModule",{value:!0});Vk.getProperty=function(e,t,r,n){var i=e[r]||t[r];return n.length?n.reduce((function(e,t){return Array.isArray(e)||Object.isObject(e)?e[t]:e}),i):i};Vk.writeProperty=function(e,t,r,n,i){var o=e[r]||t[r],a=n[n.length-1];if(!o){if(!n.length)return void(e[r]=i);o=e[r]=[]}n.slice(0,-1).reduce((function(e,t){return e[t],Array.isArray(e[t])||(e[t]=[]),e[t]}),o)[a]=i};Vk.indexArrayMerge=function(e,t){return e.slice(-t.length).map((function(e,r){return isNaN(t[r])?e:t[r]}))},Object.defineProperty(Ik,"__esModule",{value:!0});var Fk=Ak,Uk=Dk,jk=Ok,Bk=Nk,Wk=Vk,Gk=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=(0,jk.list)(r,!0);return{decode:function(t,r,n){var o=(0,Uk.typedArrayToBitString)(t),a=o;n=n||{},(r=r||{}).no_trailer_bits||(a=(0,Uk.removeRBSPTrailingBits)(o));var s=new Fk.ExpGolombDecoder(a);try{return i.decode({options:r,output:n,expGolomb:s,indexes:[],path:[e]})}catch(e){return n}},encode:function(t,r){var n=new Fk.ExpGolombEncoder;r=r||{},i.encode({options:r,input:t,expGolomb:n,indexes:[],path:[e]});var o=n.bitReservoir,a=(0,Uk.appendRBSPTrailingBits)(o);return(0,Uk.bitStringToTypedArray)(a)}}};Ik.start=Gk;Ik.startArray=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=Gk.apply(void 0,[e].concat(r));return{decode:function(e,t){return i.decode(e,t,[])},encode:i.encode}};Ik.data=function(e,t){var r=(0,Bk.propertyHandler)(e),n=r.propertyName,i=r.indexArray;return{name:e,decode:function(e){var r=e.expGolomb,o=e.output,a=e.options,s=e.indexes,c=e.path,u=void 0;try{u=t.read(r,o,a,s)}catch(e){throw o["Parse Error:"]=e.message+" at "+c.join("/"),e}return i?(0,Wk.writeProperty)(o,a,n,(0,Wk.indexArrayMerge)(s,i),u):o[n]=u,o},encode:function(e){var r=e.expGolomb,o=e.input,a=e.options,s=e.indexes;e.path;var c=void 0;"number"==typeof(c=i?(0,Wk.getProperty)(o,a,n,(0,Wk.indexArrayMerge)(s,i)):o[n])&&(c=t.write(r,o,a,s,c))}}};Ik.debug=function(e){return{decode:function(t){var r=t.expGolomb,n=t.output,i=t.options,o=t.indexes,a=t.path;console.log(e,a.join(","),r.bitReservoir,n,i,o)},encode:function(t){var r=t.expGolomb,n=t.input,i=t.options,o=t.indexes,a=t.path;console.log(e,a.join(","),r.bitReservoir,n,i,o)}}};Ik.newObj=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=(0,Bk.propertyHandler)(e),o=i.propertyName,a=i.indexArray,s=(0,jk.list)(r,!0);return{name:e,decode:function(t){var r=t.expGolomb,n=t.output,i=t.options,c=t.indexes,u=t.path.concat(e),l=s.decode({expGolomb:r,output:Object.create(n),options:i,indexes:c,path:u});return a?(0,Wk.writeProperty)(n,i,o,(0,Wk.indexArrayMerge)(c,a),l):n[o]=l,n},encode:function(t){var r=t.expGolomb,n=t.input,i=t.options,c=t.indexes,u=void 0;if("number"==typeof(u=nameArray?(0,Wk.getProperty)(n,i,o,(0,Wk.indexArrayMerge)(c,a)):n[o])){var l=path.concat(e);s.encode({expGolomb:r,input:u,options:i,indexes:c,path:l})}}}};Ik.verify=function(e){return{decode:function(t){var r=t.expGolomb,n=t.output;t.options,t.indexes;var i=r.bitReservoir.length;0!==i&&(n["Validation Error:"]=e+" was not completely parsed - there were ("+i+") bits remaining")},encode:function(e){e.expGolomb,e.input,e.options,e.indexes}}};var Zk={};Object.defineProperty(Zk,"__esModule",{value:!0});var Yk=Ok,Hk=Nk,Xk=Vk;Zk.when=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=(0,Yk.list)(r,!0);return{decode:function(t){var r=t.expGolomb,n=t.output,o=t.options,a=t.indexes,s=t.path.concat("[when]");return e(n,o,a)?i.decode({expGolomb:r,output:n,options:o,indexes:a,path:s}):n},encode:function(t){var r=t.expGolomb,n=t.input,o=t.options,a=t.indexes,s=t.path.concat("[when]");e(n,o,a)&&i.encode({expGolomb:r,input:n,options:o,indexes:a,path:s})}}};Zk.each=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=(0,Yk.list)(r,!0);return{decode:function(t){var r=t.expGolomb,n=t.output,o=t.options,a=t.indexes,s=t.path.concat("[each]"),c=a.length;for(a[c]=0;e(a[c],n,o);)i.decode({expGolomb:r,output:n,options:o,indexes:a,path:s}),a[c]++;return a.length=c,n},encode:function(t){var r=t.expGolomb,n=t.input,o=t.options,a=t.indexes,s=t.path.concat("[each]"),c=a.length;for(a[c]=0;e(a[c],n,o);)i.encode({expGolomb:r,input:n,options:o,indexes:a,path:s}),a[c]++;a.length=c}}};Zk.inArray=function(e,t){var r=(0,Hk.propertyHandler)(e),n=r.propertyName,i=r.indexArray;return function(e,r,o){return i?-1!==t.indexOf((0,Xk.getProperty)(e,r,n,(0,Xk.indexArrayMerge)(o,i))):-1!==t.indexOf(e[n])||-1!==t.indexOf(r[n])}};Zk.equals=function(e,t){var r=(0,Hk.propertyHandler)(e),n=r.propertyName,i=r.indexArray;return function(e,r,o){return i?(0,Xk.getProperty)(e,r,n,(0,Xk.indexArrayMerge)(o,i))===t:e[n]===t||r[n]===t}};Zk.gt=function(e,t){var r=(0,Hk.propertyHandler)(e),n=r.propertyName,i=r.indexArray;return function(e,r,o){return i?(0,Xk.getProperty)(e,r,n,(0,Xk.indexArrayMerge)(o,i))===t:e[n]>t||r[n]>t}};Zk.not=function(e){return function(t,r,n){return!e(t,r,n)}};Zk.some=function(e){return function(t,r,n){return e.some((function(e){return e(t,r,n)}))}};Zk.every=function(e){return function(t,r,n){return e.every((function(e){return e(t,r,n)}))}};Zk.whenMoreData=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=(0,Yk.list)(t,!0);return{decode:function(e){var t=e.expGolomb,r=e.output,i=e.options,o=e.indexes,a=e.path.concat("[whenMoreData]");return t.bitReservoir.length?n.decode({expGolomb:t,output:r,options:i,indexes:o,path:a}):r},encode:function(e){var t=e.expGolomb,r=e.input,i=e.options,o=e.indexes,a=e.path.concat("[whenMoreData]");n.encode({expGolomb:t,input:r,options:i,indexes:o,path:a})}}};Zk.whileMoreData=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=(0,Yk.list)(t,!0);return{decode:function(e){var t=e.expGolomb,r=e.output,i=e.options,o=e.indexes,a=e.path.concat("[whileMoreData]"),s=o.length;for(o[s]=0;t.bitReservoir.length;)n.decode({expGolomb:t,output:r,options:i,indexes:o,path:a}),o[s]++;return o.length=s,r},encode:function(e){var t=e.expGolomb,r=e.input,i=e.options,o=e.indexes,a=e.path.concat("[whenMoreData]"),s=o.length;o[s]=0;var c=0;for(Array.isArray(r)&&(c=r.length);index<c;)n.encode({expGolomb:t,input:r,options:i,indexes:o,path:a}),o[s]++;o.length=s}}};var Jk={exports:{}};!function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var r=function(e,t,r,n,i){return"function"==typeof e?e(t,r,n,i):e},n={u:function(e){return{read:function(t,n,i,o){var a=r(e,t,n,i,o);return t.readBits(a)},write:function(t,n,i,o,a){var s=r(e,t,n,i,o);t.writeBits(s,a)}}},f:function(e){return{read:function(t,n,i,o){var a=r(e,t,n,i,o);return t.readBits(a)},write:function(t,n,i,o,a){var s=r(e,t,n,i,o);t.writeBits(s,a)}}},ue:function(){return{read:function(e,t,r,n){return e.readUnsignedExpGolomb()},write:function(e,t,r,n,i){e.writeUnsignedExpGolomb(i)}}},se:function(){return{read:function(e,t,r,n){return e.readExpGolomb()},write:function(e,t,r,n,i){e.writeExpGolomb(i)}}},b:function(){return{read:function(e,t,r,n){return e.readUnsignedByte()},write:function(e,t,r,n,i){e.writeUnsignedByte(i)}}},val:function(e){return{read:function(t,r,n,i){return"function"==typeof e?e(t,r,n,i):e},write:function(t,r,n,i,o){"function"==typeof e&&e(ExpGolomb,output,n,i)}}},byteAlign:function(){return{read:function(e,t,r,n){return e.byteAlign()},write:function(e,t,r,n,i){}}}};t.default=n,e.exports=t.default}(Jk,Jk.exports),function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var r=Ak,n=Ik,i=Ok,o=Zk,a=Jk.exports,s=Dk;(0,i.list)([]);var c=function(e,t,r,n){return r.initial_cpb_removal_delay_length_minus1+1},u={0:{name:"buffering_period",codec:(0,i.list)([(0,n.data)("seq_parameter_set_id",(0,a.ue)(null)),(0,o.when)((0,o.equals)("nal_hrd_parameters_present_flag",1),(0,o.each)((function(e,t,r){return e<=r.cpb_cnt_minus1}),(0,n.data)("initial_cpb_removal_delay[]",(0,a.u)(c)),(0,n.data)("initial_cpb_removal_delay_offset[]",(0,a.u)(c)))),(0,o.when)((0,o.equals)("vcl_hrd_parameters_present_flag",1),(0,o.each)((function(e,t,r){return e<=r.cpb_cnt_minus1}),(0,n.data)("initial_cpb_removal_delay[]",(0,a.u)(c)),(0,n.data)("initial_cpb_removal_delay_offset[]",(0,a.u)(c))))])},1:{name:"pic_timing",codec:(0,i.list)([(0,o.when)((0,o.some)([(0,o.equals)("nal_hrd_parameters_present_flag",1),(0,o.equals)("vcl_hrd_parameters_present_flag",1)]),(0,n.data)("cpb_removal_delay",(0,a.u)((function(e,t,r,n){return r.cpb_removal_delay_length_minus1+1}))),(0,n.data)("dpb_output_delay",(0,a.u)((function(e,t,r,n){return r.dpb_output_delay_length_minus1+1})))),(0,o.when)((0,o.equals)("pic_struct_present_flag",1),(0,n.data)("pic_struct",(0,a.u)(4)),(0,o.when)((0,o.equals)("pic_struct",0),(0,n.data)("NumClockTS",(0,a.val)(1))),(0,o.when)((0,o.equals)("pic_struct",1),(0,n.data)("NumClockTS",(0,a.val)(1))),(0,o.when)((0,o.equals)("pic_struct",2),(0,n.data)("NumClockTS",(0,a.val)(1))),(0,o.when)((0,o.equals)("pic_struct",3),(0,n.data)("NumClockTS",(0,a.val)(2))),(0,o.when)((0,o.equals)("pic_struct",4),(0,n.data)("NumClockTS",(0,a.val)(2))),(0,o.when)((0,o.equals)("pic_struct",5),(0,n.data)("NumClockTS",(0,a.val)(3))),(0,o.when)((0,o.equals)("pic_struct",6),(0,n.data)("NumClockTS",(0,a.val)(3))),(0,o.when)((0,o.equals)("pic_struct",7),(0,n.data)("NumClockTS",(0,a.val)(2))),(0,o.when)((0,o.equals)("pic_struct",8),(0,n.data)("NumClockTS",(0,a.val)(2))),(0,o.each)((function(e,t){return e<t.NumClockTS}),(0,n.data)("clock_timestamp_flag[]",(0,a.u)(1)),(0,o.when)((0,o.equals)("clock_timestamp_flag[]",1),(0,n.data)("ct_type[]",(0,a.u)(2)),(0,n.data)("nuit_field_based_flag[]",(0,a.u)(1)),(0,n.data)("counting_type[]",(0,a.u)(5)),(0,n.data)("full_timestamp_flag[]",(0,a.u)(1)),(0,n.data)("discontinuity_flag[]",(0,a.u)(1)),(0,n.data)("cnt_dropped_flag[]",(0,a.u)(1)),(0,n.data)("n_frames[]",(0,a.u)(8)),(0,o.when)((0,o.equals)("full_timestamp_flag[]",1),(0,n.data)("seconds_value[]",(0,a.u)(6)),(0,n.data)("minutes_value[]",(0,a.u)(6)),(0,n.data)("hours_value[]",(0,a.u)(5))),(0,o.when)((0,o.equals)("full_timestamp_flag[]",0),(0,n.data)("seconds_flag[]",(0,a.u)(1)),(0,o.when)((0,o.equals)("seconds_flag[]",1),(0,n.data)("seconds_value[]",(0,a.u)(6)),(0,n.data)("minutes_flag[]",(0,a.u)(1)),(0,o.when)((0,o.equals)("minutes_flag[]",1),(0,n.data)("minutes_value[]",(0,a.u)(6)),(0,n.data)("hours_flag[]",(0,a.u)(1)),(0,o.when)((0,o.equals)("hours_flag[]",1),(0,n.data)("hours_value[]",(0,a.u)(5)))))),(0,o.when)((0,o.gt)("time_offset_length",0),(0,n.data)("time_offset",(0,a.u)((function(e,t,r,n){return r.time_offset_length})))))))])},2:{name:"pan_scan_rect"},3:{name:"filler_payload"},4:{name:"user_data_registered_itu_t_t35",codec:(0,i.list)([(0,n.data)("itu_t_t35_country_code",(0,a.u)(8)),(0,n.data)("itu_t_t35_provider_code",(0,a.u)(16)),(0,o.when)((0,o.equals)("itu_t_t35_provider_code",49),(0,n.data)("ATSC_user_identifier",(0,a.u)(32))),(0,o.when)((0,o.inArray)("itu_t_t35_provider_code",[47,49]),(0,n.data)("ATSC1_data_user_data_type_code",(0,a.u)(8))),(0,o.when)((0,o.equals)("itu_t_t35_provider_code",47),(0,n.data)("DIRECTV_user_data_length",(0,a.u)(8))),(0,n.data)("process_em_data_flag",(0,a.u)(1)),(0,n.data)("process_cc_data_flag",(0,a.u)(1)),(0,n.data)("additional_data_flag",(0,a.u)(1)),(0,n.data)("cc_count",(0,a.u)(5)),(0,n.data)("em_data",(0,a.u)(8)),(0,o.each)((function(e,t){return e<t.cc_count}),(0,n.newObj)("cc_data_pkts[]",(0,n.data)("type",(0,a.val)("cc_data_pkt")),(0,n.data)("marker_bits",(0,a.u)(5)),(0,n.data)("cc_valid",(0,a.u)(1)),(0,n.data)("cc_type",(0,a.u)(2)),(0,n.data)("cc_data_1",(0,a.u)(8)),(0,n.data)("cc_data_2",(0,a.u)(8)))),(0,n.data)("marker_bits",(0,a.u)(8))])},5:{name:"user_data_unregistered"},6:{name:"recovery_point",codec:(0,i.list)([(0,n.data)("recovery_frame_cnt",(0,a.ue)(null)),(0,n.data)("exact_match_flag",(0,a.u)(1)),(0,n.data)("broken_link_flag",(0,a.u)(1)),(0,n.data)("changing_slice_group_idc",(0,a.u)(2))])},7:{name:"dec_ref_pic_marking_repetition"},8:{name:"spare_pic"},9:{name:"scene_info"},10:{name:"sub_seq_info"},11:{name:"sub_seq_layer_characteristics"},12:{name:"sub_seq_characteristics"},13:{name:"full_frame_freeze"},14:{name:"full_frame_freeze_release"},15:{name:"full_frame_snapshot"},16:{name:"progressive_refinement_segment_start"},17:{name:"progressive_refinement_segment_end"},18:{name:"motion_constrained_slice_group_set"},19:{name:"film_grain_characteristics"},20:{name:"deblocking_filter_display_preference"},21:{name:"stereo_video_info"},22:{name:"post_filter_hint"},23:{name:"tone_mapping_info"},24:{name:"scalability_info"},25:{name:"sub_pic_scalable_layer"},26:{name:"non_required_layer_rep"},27:{name:"priority_layer_info"},28:{name:"layers_not_present"},29:{name:"layer_dependency_change"},30:{name:"scalable_nesting"},31:{name:"base_layer_temporal_hrd"},32:{name:"quality_layer_integrity_check"},33:{name:"redundant_pic_property"},34:{name:"tl"},35:{name:"tl_switching_point"},36:{name:"parallel_decoding_info"},37:{name:"mvc_scalable_nesting"},38:{name:"view_scalability_info"},39:{name:"multiview_scene_info"},40:{name:"multiview_acquisition_info"},41:{name:"non_required_view_component"},42:{name:"view_dependency_change"},43:{name:"operation_points_not_present"},44:{name:"base_view_temporal_hrd"},45:{name:"frame_packing_arrangement"}},l={decode:function(e){var t=e.expGolomb,n=e.output,i=e.options,o=e.indexes,a=e.path,c=o[0],l={payloadType:0,payloadSize:0},d=void 0;do{d=t.readUnsignedByte(),l.payloadType+=d}while(255===d);do{d=t.readUnsignedByte(),l.payloadSize+=d}while(255===d);var p=u[l.payloadType],f=t.readRawBits(8*l.payloadSize);if(p)if(l.type=p.name,p.codec){var h=new r.ExpGolombDecoder(f);p.codec.decode({expGolomb:h,output:l,options:i,path:a,indexes:o})}else l.data=(0,s.bitStringToTypedArray)(f);else l.type="unknown type",l.data=(0,s.bitStringToTypedArray)(f);return n[c]=l,n},encode:function(e){for(var t=e.expGolomb,n=e.input,i=e.options,o=e.indexes,a=e.path,c=n[o[0]],l=c.payloadType;l>255;)l-=255,t.writeUnsignedByte(255);t.writeUnsignedByte(l);for(var d=c.payloadSize;d>255;)d-=255,t.writeUnsignedByte(255);t.writeUnsignedByte(d);var p=u[c.payloadType];if(p&&p.codec){var f=new r.ExpGolombEncoder;p.codec.encode({expGolomb:f,input:c,options:i,path:a,indexes:o});var h=f.bitReservoir;h.length%8!=0&&(h=(0,s.appendRBSPTrailingBits)(h)),t.writeRawBits(8*c.payloadSize,h)}else c.data?t.writeRawBits(8*c.payloadSize,(0,s.typedArrayToBitString)(c.data)):t.writeRawBits(8*c.payloadSize,"")}},d=(0,n.startArray)("sei_message",(0,o.whileMoreData)(l));t.default=d,e.exports=t.default}(kk,kk.exports);var Kk=r(kk.exports),Qk=function(e){return Kk.decode(e)},qk=function(){function e(){this.consoleEnabled=!1}return e.prototype.log=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.consoleEnabled&&console.log.apply(console,du([],lu(e),!1))},e.prototype.enableConsole=function(e){this.consoleEnabled=e},e}(),zk=new qk;function $k(e,t,r){var n=void 0===t?null:t,i=function(e,t){var r=atob(e);if(t){for(var n=new Uint8Array(r.length),i=0,o=r.length;i<o;++i)n[i]=r.charCodeAt(i);return String.fromCharCode.apply(null,new Uint16Array(n.buffer))}return r}(e,void 0!==r&&r),o=i.indexOf("\n",10)+1,a=i.substring(o)+(n?"//# sourceMappingURL="+n:""),s=new Blob([a],{type:"application/javascript"});return URL.createObjectURL(s)}var eA,tA,rA,nA,iA=(eA="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",tA=null,rA=!1,function(e){return nA=nA||$k(eA,tA,rA),new Worker(nA,e)}),oA=function(){function e(e){void 0===e&&(e=60),this.times=60,this.worker=null,this.times=e,this.worker=new iA}return e.prototype.tick=function(e){this.requestAnimationFrameWrapper(e)},e.prototype.destroy=function(){var e;null===(e=this.worker)||void 0===e||e.terminate(),this.worker=null},e.prototype.setWorkerInterval=function(e,t){var r,n,i=this;null===(r=this.worker)||void 0===r||r.addEventListener("message",e),null===(n=this.worker)||void 0===n||n.postMessage({type:"start",data:{time:t}});return function(){var t,r;null===(t=i.worker)||void 0===t||t.postMessage({type:"stop"}),null===(r=i.worker)||void 0===r||r.removeEventListener("message",e)}},e.prototype.requestAnimationFrameWrapper=function(e){var t=!1,r=this.setWorkerInterval((function(){!t&&document.hidden&&(t=!0,r(),e())}),1e3/this.times);requestAnimationFrame((function(){t||(t=!0,r(),e())}))},e}(),aA=function(){function e(){var e,t,r,n,i,o,a,s;this.peerConnection=null,this.dataChannel=null,this.clientSideDescription=null,this.serverSideDescription=null,this.connectStatus=SE.DISCONNECTED,this.connectDirection=wE.RECEIVE,this.connectConfig=null,this.negotiating=!1,this.ticker=null,this.seiDataMap=null,this.onAddTrack=null,this.onConnect=null,this.onDisconnect=null,this.onSetLocalDescription=null,this.onError=null,this.onReceiveSEI=null,this.onTrack=Jr(e=this.onTrack).call(e,this),this.onIceConnectionStateChange=Jr(t=this.onIceConnectionStateChange).call(t,this),this.onSignalingStateChange=Jr(r=this.onSignalingStateChange).call(r,this),this.onConnectionStateChange=Jr(n=this.onConnectionStateChange).call(n,this),this.onNegotiationNeeded=Jr(i=this.onNegotiationNeeded).call(i,this),this.onDataChannelOpen=Jr(o=this.onDataChannelOpen).call(o,this),this.onDataChannelMessage=Jr(a=this.onDataChannelMessage).call(a,this),this.handleSeiData=Jr(s=this.handleSeiData).call(s,this)}return e.prototype.init=function(e){var t=void 0===e?{}:e,r=t.onAddTrack,n=void 0===r?null:r,i=t.onConnect,o=void 0===i?null:i,a=t.onDisconnect,s=void 0===a?null:a,c=t.onSetLocalDescription,u=void 0===c?null:c,l=t.onError,d=void 0===l?null:l,p=t.onReceiveSEI,f=void 0===p?null:p;this.onAddTrack=n,this.onConnect=o,this.onDisconnect=s,this.onSetLocalDescription=u,this.onError=d,this.onReceiveSEI=f},e.prototype.initWebRTCConnect=function(e){var t,r=void 0===e?{}:e,n=r.config,i=void 0===n?{}:n,o=r.direction,a=void 0===o?wE.RECEIVE:o,s=r.stream;zk.log("WebRTC init"),this.peerConnection?zk.log("peerConnection is existed"):(this.connectDirection=a,this.connectConfig=null!==(t=i.connection)&&void 0!==t?t:{},this.createWebRTCConnect(i,s))},e.prototype.connect=function(e){e&&this.onAnswer(e)},e.prototype.disconnect=function(e){var t,r=void 0===e?{}:e,n=r.msg,i=void 0===n?"":n,o=r.code,a=void 0===o?TE.MANUAL_CLOSE:o;zk.log("connection close".concat(a===TE.NEED_RECONNECT?", needs to reconnect":"")),this.connectStatus!==SE.DISCONNECTED&&(this.connectStatus=SE.DISCONNECTED,this.negotiating=!1,this.peerConnection&&(this.peerConnection.removeEventListener("track",this.onTrack),this.peerConnection.removeEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.peerConnection.removeEventListener("signalingstatechange",this.onSignalingStateChange),this.peerConnection.removeEventListener("connectionstatechange",this.onConnectionStateChange),this.peerConnection.removeEventListener("negotiationneeded",this.onNegotiationNeeded),this.peerConnection.close(),this.peerConnection=null),this.dataChannel&&(this.dataChannel.removeEventListener("open",this.onDataChannelOpen),this.dataChannel.removeEventListener("message",this.onDataChannelMessage),this.dataChannel.close(),this.dataChannel=null),this.seiDataMap&&(this.seiDataMap.clear(),this.seiDataMap=null),this.ticker&&(this.ticker.destroy(),this.ticker=null),this.clientSideDescription=null,this.serverSideDescription=null,null===(t=this.onDisconnect)||void 0===t||t.call(this,{code:a,msg:i}))},e.prototype.getClientSideDescription=function(){return this.clientSideDescription||zk.log("WebRTC is not initialized"),this.clientSideDescription},e.prototype.getConnectStatus=function(){return this.connectStatus},e.prototype.getStats=function(){return su(this,void 0,void 0,(function(){return cu(this,(function(e){switch(e.label){case 0:return this.peerConnection?[4,this.peerConnection.getStats(null)]:[2];case 1:return[2,e.sent()]}}))}))},e.prototype.createWebRTCConnect=function(e,t){var r,n,i,o=this,a=null!==(r=e.connection)&&void 0!==r?r:{},s=a.isReceiveSEI,c=void 0!==s&&s,u=a.isCreateSEIDataChannel,l=void 0!==u&&u;try{var d={iceServers:[],sdpSemantics:"unified-plan",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",tcpCandidatePolicy:"disable",IceTransportsType:"nohost"};c&&(qE?(zk.log("enable insertable streams"),d.encodedInsertableStreams=!0):zk.log("insertable streams is not supported")),this.peerConnection=new RTCPeerConnection(d)}catch(e){var p;return zk.log(e),void("ReferenceError"===e.name&&$i(p=e.message).call(p,"RTCPeerConnection")&&(zk.log("WebRTC is not supported"),null===(n=this.onError)||void 0===n||n.call(this,"WebRTC is not supported")))}if(this.connectStatus=SE.CONNECTING,this.negotiating=!1,this.peerConnection.addEventListener("track",this.onTrack),this.peerConnection.addEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.peerConnection.addEventListener("signalingstatechange",this.onSignalingStateChange),this.peerConnection.addEventListener("connectionstatechange",this.onConnectionStateChange),this.peerConnection.addEventListener("negotiationneeded",this.onNegotiationNeeded),c&&!qE&&l)if(zE){zk.log("create data channel to receive sei messages");var f=OE,h=NE;this.dataChannel=this.peerConnection.createDataChannel(f,{protocol:h,ordered:!1,maxPacketLifeTime:100}),this.dataChannel.binaryType="arraybuffer",this.ticker=new oA(40),this.seiDataMap=new gS,this.dataChannel.addEventListener("open",this.onDataChannelOpen),this.dataChannel.addEventListener("message",this.onDataChannelMessage)}else zk.log("RTCRtpReceiver getSynchronizationSources is not supported");this.connectDirection===wE.SEND&&t instanceof MediaStream&&ic(i=t.getTracks()).call(i,(function(e){o.peerConnection.addTrack(e,t)}));this.connectDirection===wE.RECEIVE&&this.createOffer(e.offer)},e.prototype.onTrack=function(e){var t,r,n,i=this;zk.log("".concat(e.track.kind," track added to peerConnection")),null===(t=this.onAddTrack)||void 0===t||t.call(this,e.track);var o=this.connectConfig.isReceiveSEI;if(void 0!==o&&o&&qE)if("video"===e.track.kind){zk.log("insertable streams for video is started");try{var a=e.receiver.createEncodedStreams(),s=new TransformStream({transform:function(e,t){var r,n,o=function(e){var t=new DataView(e);if(t.byteLength<3)return[];for(var r=[],n=0;n<t.byteLength-2;n++)if(0===t.getUint8(n)&&0===t.getUint8(n+1)&&1===t.getUint8(n+2)){var i=3==(c=n>0&&0===t.getUint8(n-1)?4:3)?n:n-1;r.push({startOffset:i,startCodeSize:c}),n+=2}for(var o=[],a=0;a<r.length;a++){for(var s=r[a],c=(i=s.startOffset,s.startCodeSize),u=i,l=r[a+1]?r[a+1].startOffset:t.byteLength,d=31&t.getUint8(u+c),p=l-u-c-1,f=new Uint8Array(p),h=0;h<p;h++)f[h]=t.getUint8(u+c+1+h);o.push({nalUnitType:d,rbspData:f})}return o}(e.data);try{for(var a=uu(o),s=a.next();!s.done;s=a.next()){var c=s.value,u=c.nalUnitType,l=c.rbspData;if(6===u){var d=Qk(l);ic(d).call(d,(function(e){var t;null===(t=i.onReceiveSEI)||void 0===t||t.call(i,e)}))}}}catch(e){r={error:e}}finally{try{s&&!s.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}t.enqueue(e)}});a.readable.pipeThrough(s).pipeTo(a.writable)}catch(e){zk.log("insertable streams for video has error,",e),null===(r=this.onError)||void 0===r||r.call(this,"parse video data is failed")}}else{zk.log("insertable streams for audio is started");try{(a=e.receiver.createEncodedStreams()).readable.pipeTo(a.writable)}catch(e){zk.log("insertable streams for audio has error,",e),null===(n=this.onError)||void 0===n||n.call(this,"parse audio data is failed")}}},e.prototype.onIceConnectionStateChange=function(){var e;switch(zk.log("onIceConnectionStateChange",this.peerConnection.iceConnectionState),this.peerConnection.iceConnectionState){case"failed":case"disconnected":zk.log("iceConnection disconnected"),this.disconnect({code:TE.NEED_RECONNECT,msg:"Connection disconnected, please try again"});break;case"connected":case"completed":if(!YE)return;if(this.connectStatus===SE.CONNECTED)return;zk.log("iceConnection connected"),this.connectStatus=SE.CONNECTED,null===(e=this.onConnect)||void 0===e||e.call(this,{code:_E.SUCCESS,msg:"Connection connected"})}},e.prototype.onSignalingStateChange=function(){zk.log("onSignalingStateChange",this.peerConnection.signalingState)},e.prototype.onConnectionStateChange=function(){var e;switch(zk.log("onConnectionStateChange",this.peerConnection.connectionState),this.peerConnection.connectionState){case"failed":case"disconnected":zk.log("connection disconnected"),this.disconnect({code:TE.NEED_RECONNECT,msg:"Connection disconnected, please try again"});break;case"connected":if(this.connectStatus===SE.CONNECTED)return;zk.log("connection connected"),this.connectStatus=SE.CONNECTED,null===(e=this.onConnect)||void 0===e||e.call(this,{code:_E.SUCCESS,msg:"Connection connected"})}},e.prototype.onNegotiationNeeded=function(){if(zk.log("peerConnection need negotiation"),this.connectDirection!==wE.RECEIVE)try{if(this.negotiating||"stable"!==this.peerConnection.signalingState)return;this.negotiating=!0,this.createOffer()}catch(e){zk.log("onNegotiationNeeded error,",e)}finally{this.negotiating=!1}},e.prototype.createOffer=function(e){var t;return void 0===e&&(e={}),su(this,void 0,void 0,(function(){var r,n,i,o,a,s,c;return cu(this,(function(u){switch(u.label){case 0:r=e.offerToReceiveVideo,n=void 0===r||r,i=e.offerToReceiveAudio,o=void 0===i||i,a={voiceActivityDetection:!1},this.connectDirection===wE.RECEIVE&&(!!this.peerConnection.addTransceiver?(zk.log("peerConnection addTransceiver"),o&&this.peerConnection.addTransceiver("audio",{direction:"recvonly"}),n&&this.peerConnection.addTransceiver("video",{direction:"recvonly"})):(zk.log("init createOffer options"),a=ou(ou({},a),{offerToReceiveVideo:n,offerToReceiveAudio:o}))),u.label=1;case 1:return u.trys.push([1,3,,4]),zk.log("createOffer start"),[4,this.peerConnection.createOffer(a)];case 2:return s=u.sent(),zk.log("createOffer success"),this.onOffer(s),[3,4];case 3:return c=u.sent(),zk.log("create offer error,",c),this.disconnect({code:TE.NEED_RECONNECT,msg:"create offer is failed"}),null===(t=this.onError)||void 0===t||t.call(this,"create offer is failed"),[3,4];case 4:return[2]}}))}))},e.prototype.onOffer=function(e){var t,r;return su(this,void 0,void 0,(function(){var n,i;return cu(this,(function(o){switch(o.label){case 0:n=e.sdp,n=this.connectDirection===wE.RECEIVE?yk(n):vk(a=n.split("\r\n")).call(a,(function(e){return $i(e).call(e,"a=fmtp:111")?"".concat(e,";stereo=1"):e})).join("\r\n"),e.sdp=n,o.label=1;case 1:return o.trys.push([1,3,,4]),zk.log("setLocalDescription start"),[4,this.peerConnection.setLocalDescription(e)];case 2:return o.sent(),zk.log("setLocalDescription success"),this.clientSideDescription=this.peerConnection.localDescription,null===(t=this.onSetLocalDescription)||void 0===t||t.call(this,this.clientSideDescription),[3,4];case 3:return i=o.sent(),zk.log("setLocalDescription error,",i),null===(r=this.onError)||void 0===r||r.call(this,"set local sdp is failed"),[3,4];case 4:return[2]}var a}))}))},e.prototype.onAnswer=function(e){var t;return su(this,void 0,void 0,(function(){var r;return cu(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),zk.log("setRemoteDescription start"),[4,this.peerConnection.setRemoteDescription(new RTCSessionDescription(e))];case 1:return n.sent(),zk.log("setRemoteDescription success"),this.serverSideDescription=this.peerConnection.remoteDescription,[3,3];case 2:return r=n.sent(),zk.log("setRemoteDescription error,",r),null===(t=this.onError)||void 0===t||t.call(this,"set remote sdp is failed"),[3,3];case 3:return[2]}}))}))},e.prototype.onDataChannelOpen=function(){zk.log("data channel open"),this.handleSeiData()},e.prototype.onDataChannelMessage=function(e){var t=this;if(e.data&&this.seiDataMap)try{var r=String.fromCharCode.apply(null,new Uint8Array(e.data)),n=JSON.parse(r),i=bk(n)?n:[n];ic(i).call(i,(function(e){var r,n=e.type,i=e.rtp_time,o=e.data;if("sync_sei"===n){var a=new Uint8Array(vk(r=o.match(/[\da-f]{2}/gi)).call(r,(function(e){return bP(e,16)}))),s=Qk(a);ic(s).call(s,(function(e){var r=t.seiDataMap.get(i)||[];t.seiDataMap.set(i,du(du([],lu(r),!1),[e],!1))}))}}))}catch(t){zk.log("data channel error: data parsing failed",t,e.data)}},e.prototype.handleSeiData=function(){var e,t=this;if(this.ticker&&this.seiDataMap&&this.peerConnection){var r=this.connectConfig.maxSEIDelayTime,n=void 0===r?100:r,i=bE(e=this.peerConnection.getReceivers()).call(e,(function(e){return e.track&&"video"===e.track.kind}));if(i){var o=i.getSynchronizationSources();if(o.length>0){var a,s=o[0].rtpTimestamp,c=du([],lu(Ek(a=this.seiDataMap).call(a)),!1);ic(c).call(c,(function(e){var r=90*n,i=s-e;if(i>=0&&i<=r){var o=t.seiDataMap.get(e)||[];ic(o).call(o,(function(e){var r;null===(r=t.onReceiveSEI)||void 0===r||r.call(t,e)})),t.seiDataMap.delete(e)}else i>r&&t.seiDataMap.delete(e)}))}}this.ticker.tick(this.handleSeiData)}else zk.log("sei data handler stop")},e}(),sA=Ao.PROPER,cA=n,uA=zR,lA=aP.trim;kr({target:"String",proto:!0,forced:function(e){return cA((function(){return!!uA[e]()||"鈥嬄呩爭"!=="鈥嬄呩爭"[e]()||sA&&uA[e].name!==e}))}("trim")},{trim:function(){return lA(this)}});var dA=Gr("String").trim,pA=l,fA=dA,hA=String.prototype,mA=function(e){var t=e.trim;return"string"==typeof e||e===hA||pA(hA,e)&&t===hA.trim?fA:t},vA=function(){function e(){this.timeMark=null,this.commonData=null,this.reportData=null,this.tempData=null,this.onReport=null,this.timeMark=new gS,this.commonData={},this.initCommonData(),this.reportData=ou({},this.commonData),this.tempData={}}return e.prototype.setData=function(e,t,r){void 0===r&&(r=!1),this.reportData[e]=t,r&&(this.commonData[e]=t)},e.prototype.setTempData=function(e,t){this.tempData[e]=t},e.prototype.getTempData=function(e){return this.tempData[e]},e.prototype.startReport=function(e){var t;switch(e){case"start":this.setData("uint32_data_type",1),this.setData("uint32_command",40101);break;case"interval":this.setData("uint32_data_type",2),this.setData("uint32_command",40100);break;case"stop":this.setData("uint32_data_type",1),this.setData("uint32_command",40102)}var r=(new Date).getTime();this.setData("uint64_data_time",Math.round(r/1e3)),null===(t=this.onReport)||void 0===t||t.call(this,this.reportData),this.reportData=ou({},this.commonData)},e.prototype.setHandler=function(e){this.onReport=e},e.prototype.markTime=function(e){this.timeMark.set(e,(new Date).getTime())},e.prototype.measureTime=function(e,t,r,n){if(void 0===n&&(n=!1),this.timeMark.has(t)&&this.timeMark.has(r)){var i=Math.round(this.timeMark.get(r)-this.timeMark.get(t));this.setData(e,i),n&&this.setTempData(e,i)}},e.prototype.clearData=function(){this.timeMark?this.timeMark.clear():this.timeMark=new gS,this.commonData={},this.initCommonData(),this.reportData=ou({},this.commonData),this.tempData={}},e.prototype.destroy=function(){var e;null===(e=this.timeMark)||void 0===e||e.clear(),this.timeMark=null,this.commonData=null,this.reportData=null,this.tempData=null,this.onReport=null},e.prototype.initCommonData=function(){var e,t;this.commonData.str_app_version="TXLivePlayer-".concat(xE),this.commonData.bytes_version="TXLivePlayer-".concat(xE);var r=window.navigator.userAgent,n=bl.parse(r),i=n.platform,o=n.os,a=n.browser;this.commonData.str_device_type=i.model||i.vendor||"",this.commonData.str_os_info=mA(e=mi(t="".concat(o.name||""," ")).call(t,o.version||"")).call(e),this.commonData.str_browser_model=a.name||"",this.commonData.str_browser_version=a.version||"",this.commonData.str_user_agent=r,this.commonData.u32_link_type=4,this.commonData.u32_channel_type=3},e}(),yA=function(e,t,r){return void 0===r&&(r={}),su(void 0,void 0,void 0,(function(){var n,i,o,a,s,c;return cu(this,(function(u){switch(u.label){case 0:return n=r.timeout,i=void 0===n?10:n,o=null,a=null,s={},window.AbortController&&(o=new window.AbortController,s={signal:o.signal},a=Rc((function(){return o.abort()}),1e3*i)),[4,fetch(e,ou({body:jS(t),cache:"no-cache",credentials:"same-origin",headers:{"content-type":"application/json"},method:"POST",mode:"cors"},s))];case 1:if(c=u.sent(),a&&window.clearTimeout(a),200!==c.status)throw new Error("Network Error, status code:".concat(c.status));return[2,c.json()]}}))}))},gA=function(){function e(){this.baseUrl="",this.signalConfig={protocol:"https",domain:"",query:!0,timeout:5}}return e.prototype.setSignalConfig=function(e){this.signalConfig=ou(ou({},this.signalConfig),e)},e.prototype.fetchPullStream=function(e){var t;return su(this,void 0,void 0,(function(){var r,n,i,o,a,s,c,u,l,d,p=this;return cu(this,(function(f){switch(f.label){case 0:return r=function(e,t,r){return su(p,void 0,void 0,(function(){var n,i,o,a,s,c,u;return cu(this,(function(l){switch(l.label){case 0:return n="".concat(e,"/webrtc/v1/pullstream"),[4,yA(n,t,{timeout:r})];case 1:var d;if(i=l.sent(),o=i.errcode,a=i.errmsg,s=i.remotesdp,c=i.svrsig,0!==o)throw(u=new Error(mi(d="errCode:".concat(o,", errMsg:")).call(d,a))).name=VE,u;return[2,{url:e,remoteSdp:s,svrSig:c}]}}))}))},this.baseUrl?[4,r(this.baseUrl,e,this.signalConfig.timeout)]:[3,2];case 1:return n=f.sent(),u=n.url,[2,l=au(n,["url"])];case 2:i=this.signalConfig,o=i.protocol,a=i.timeout,f.label=3;case 3:return f.trys.push([3,5,,6]),s=vk(LE).call(LE,(function(t){var n;return r(mi(n="".concat(o,"://")).call(n,t),e,a)})),[4,qR.any(s)];case 4:return c=f.sent(),u=c.url,l=au(c,["url"]),this.baseUrl=u,[2,l];case 5:throw d=f.sent(),(null===(t=null==d?void 0:d.errors)||void 0===t?void 0:t[0])||d;case 6:return[2]}}))}))},e.prototype.fetchStopStream=function(e){return su(this,void 0,void 0,(function(){var t,r,n,i;return cu(this,(function(o){switch(o.label){case 0:if(!this.baseUrl)throw new Error("signal domain name is empty");return t="".concat(this.baseUrl,"/webrtc/v1/stopstream"),[4,yA(t,e,{timeout:this.signalConfig.timeout})];case 1:var a;if(r=o.sent(),n=r.errcode,i=r.errmsg,0!==n)throw new Error(mi(a="errCode:".concat(n,", errmsg:")).call(a,i));return[2,r]}}))}))},e.prototype.fetchStreamSdp=function(e){return su(this,void 0,void 0,(function(){var t,r,n,i,o,a,s,c;return cu(this,(function(u){switch(u.label){case 0:return t=e.streamurl,r=e.sessionid,n=e.localsdp,[4,yA(t,{version:"v1.0",sessionId:r,localSdp:n},{timeout:this.signalConfig.timeout})];case 1:var l;if(i=u.sent(),o=i.code,a=i.message,s=i.remoteSdp,200!==o)throw(c=new Error(mi(l="errCode:".concat(o,", errMsg:")).call(l,a))).name=VE,c;return[2,{remoteSdp:s,svrSig:null}]}}))}))},e.prototype.fetchAbrControl=function(e){return su(this,void 0,void 0,(function(){var t,r,n,i;return cu(this,(function(o){switch(o.label){case 0:if(!this.baseUrl)throw new Error("signal domain name is empty");return t="".concat(this.baseUrl,"/webrtc/v1/tabrctl"),[4,yA(t,e,{timeout:this.signalConfig.timeout})];case 1:var a;if(r=o.sent(),n=r.errcode,i=r.seq,0!==n)throw new Error(mi(a="errCode:".concat(n,", errMsg:")).call(a,403===n?"not allowed error":"error"));return[2,{seq:i}]}}))}))},e.prototype.updateSignalDomain=function(e){var t,r=this.signalConfig,n=r.protocol,i=r.domain,o=r.query;if(e&&o){var a=window.localStorage.getItem(IE);if(a){var s=JSON.parse(a)[e];if(s){var c,u=s.signal,l=s.expire;return(new Date).getTime()>l&&this.fetchSignalDomain(e,DE[0]),void(this.baseUrl=mi(c="".concat(n,"://")).call(c,u))}}this.baseUrl=i?mi(t="".concat(n,"://")).call(t,i):"",this.fetchSignalDomain(e,DE[0])}else{var d;this.baseUrl=i?mi(d="".concat(n,"://")).call(d,i):""}},e.prototype.getSignalDomain=function(){return this.baseUrl},e.prototype.fetchSignalDomain=function(e,t){return su(this,void 0,void 0,(function(){var r,n,i,o,a,s,c,u,l;return cu(this,(function(d){switch(d.label){case 0:if(!e||!t)return[2];r="https://".concat(t,"/signal_query"),d.label=1;case 1:return d.trys.push([1,3,,4]),[4,yA(r,{domain:e,requestid:yl(16),client_type:"Web",client_info:window.navigator.userAgent})];case 2:if(n=d.sent(),i=n.errcode,o=n.data,0===i){a=o.signal_domain,s=o.cache_time,c={},(u=window.localStorage.getItem(IE))&&(c=JSON.parse(u)),c[e]={signal:a,expire:(new Date).getTime()+1e3*s};try{window.localStorage.setItem(IE,jS(c))}catch(e){}}return[3,4];case 3:return d.sent(),-1!==(l=Oc(DE).call(DE,t))&&this.fetchSignalDomain(e,DE[l+1]),[3,4];case 4:return[2]}}))}))},e}(),bA=window.LEBP2P||AE,CA=function(){function e(){var e,t,r,n,i,o,a,s,c,u,l;this.playerView=null,this.playUrl=null,this.rawUrl=null,this.isVideoExisted=!1,this.webrtcConnection=null,this.svrSig=null,this.stream=null,this.timer={retryTimeout:null,disconnectTimeout:null,statsInterval:null,reportInterval:null},this.connectTimeout=5,this.connectRetry={maxNum:3,curNum:0,delay:1,playing:!1},this.streamDecodeFail={maxNum:3,curNum:0},this.streamReceiveFail={maxNum:10,curNum:0},this.streamPlaying={threshold:5,status:RE.INIT},this.docOrigOverflow=null,this.receiveConfig={video:!0,audio:!0},this.seiConfig={enable:!1,fallback:!1,maxDelayTime:100},this.listener={onPlayEvent:null,onPlayStats:null,onPlayReport:null,onPlaySEI:null},this.lastStatsReport=null,this.report=null,this.signal=null,this.abrClient={curBitrate:"",seq:0},this.p2pMode={enable:!1,config:{cloudAppid:0,xp2pAppid:"",appKey:"",appSecret:"",xp2pDomain:""},source:""},this.p2pConnection=null,this.report=new vA,this.signal=new gA,this.onAddTrack=Jr(e=this.onAddTrack).call(e,this),this.onSetLocalDescription=Jr(t=this.onSetLocalDescription).call(t,this),this.onConnect=Jr(r=this.onConnect).call(r,this),this.onDisconnect=Jr(n=this.onDisconnect).call(n,this),this.onError=Jr(i=this.onError).call(i,this),this.onStats=Jr(o=this.onStats).call(o,this),this.onBeforeUnload=Jr(a=this.onBeforeUnload).call(a,this),this.onPagehide=Jr(s=this.onPagehide).call(s,this),this.onP2PEvent=Jr(c=this.onP2PEvent).call(c,this),this.onReceiveSEI=Jr(u=this.onReceiveSEI).call(u,this),window.addEventListener("beforeunload",this.onBeforeUnload),window.addEventListener("pagehide",this.onPagehide);var d=vl.browserDetails,p=d.browser,f=d.version;zk.log(mi(l="browser is ".concat(p,", version is ")).call(l,f))}return e.checkSupport=function(e){su(void 0,void 0,void 0,(function(){var e,t,r;return cu(this,(function(n){var i;switch(n.label){case 0:return e=!1,ic(i=["RTCPeerConnection","webkitRTCPeerConnection"]).call(i,(function(t){e||t in window&&(e=!0)})),GE||(HE&&JE||XE&&JE&&(0===(t=QE()).length||t[0]<11||11===t[0]&&t[1]<1||11===t[0]&&1===t[1]&&t[2]<2))&&(e=!1),[4,su(void 0,void 0,void 0,(function(){var e,t,r,n;return cu(this,(function(i){var o;switch(i.label){case 0:return i.trys.push([0,2,,3]),e=new RTCPeerConnection({iceServers:[],sdpSemantics:"unified-plan"}),t={},e.addTransceiver?(e.addTransceiver("audio",{direction:"recvonly"}),e.addTransceiver("video",{direction:"recvonly"})):t={offerToReceiveVideo:!0,offerToReceiveAudio:!0},[4,e.createOffer(t)];case 1:return r=i.sent(),n=Oc(o=r.sdp.toLowerCase()).call(o,"h264")>-1,e.close(),[2,n];case 2:return i.sent(),[2,!1];case 3:return[2]}}))}))];case 1:return r=n.sent(),[2,{support:e,isTbs:GE,tbsVersion:GE?$E(navigator.userAgent,BE,1):null,isFirefox:ZE,isSafari:XE,isIOS:JE,iOSVersion:JE?QE().join("."):null,h264Support:r}]}}))})).then((function(t){var r=t.support,n=t.h264Support;null==e||e({support:r,h264Support:n,p2pSupport:bA.support()})}))},e.prototype.setConfig=function(e){var t=e.connectRetryCount,r=e.connectRetryDelay,n=e.connectTimeout,i=e.receiveVideo,o=e.receiveAudio,a=e.receiveSEI,s=e.showLog,c=e.p2pEnable,u=e.p2pConfig,l=e.signalDomain,d=e.signalTimeout,p=e.maxDecodeFailCount,f=e.maxReceiveFailCount,h=e.minDecodeFramerate;if(void 0!==t&&("number"==typeof t&&t>=0?this.connectRetry.maxNum=t:zk.log("connectRetryCount must be a number greater than or equal to 0")),void 0!==r&&("number"==typeof r&&r>=0?this.connectRetry.delay=r:zk.log("connectRetryDelay must be a number greater than or equal to 0")),void 0!==n&&("number"==typeof n&&n>=0?this.connectTimeout=n:zk.log("connectTimeout must be a number greater than or equal to 0")),void 0!==i&&(this.receiveConfig.video=!!i),void 0!==o&&(this.receiveConfig.audio=!!o),void 0!==a)if("boolean"==typeof a)this.seiConfig={enable:a,fallback:!1,maxDelayTime:100};else{var m=null!=a?a:{},v=m.fallback,y=void 0!==v&&v,g=m.maxDelayTime,b=void 0===g?100:g;this.seiConfig={enable:!0,fallback:y,maxDelayTime:b}}if(void 0!==s&&zk.enableConsole(!!s),void 0!==c&&(this.p2pMode.enable=!!c),void 0!==u){var C=null!=u?u:{},S=C.cloudAppid,_=C.appKey,T=C.appSecret,w=C.xp2pAppid,R=C.xp2pDomain;this.p2pMode.config=ou(ou({},this.p2pMode.config),{cloudAppid:S,appKey:_,appSecret:T,xp2pAppid:w,xp2pDomain:R})}if(void 0!==l)if("string"==typeof l)this.signal.setSignalConfig({domain:l});else{var P,E=null!=l?l:{},k=E.protocol,A=E.domain,x=E.query;this.signal.setSignalConfig({protocol:$i(P=["https","http"]).call(P,k)?k:"https",domain:A,query:x})}void 0!==d&&("number"==typeof d&&d>=0?this.signal.setSignalConfig({timeout:d}):zk.log("signalTimeout must be a number greater than or equal to 0")),void 0!==p&&("number"==typeof p&&p>=0?this.streamDecodeFail.maxNum=p:zk.log("maxDecodeFailCount must be a number greater than or equal to 0")),void 0!==f&&("number"==typeof f&&f>=0?this.streamReceiveFail.maxNum=f:zk.log("maxReceiveFailCount must be a number greater than or equal to 0")),void 0!==h&&("number"==typeof h&&h>=0?this.streamPlaying.threshold=h:zk.log("minDecodeFramerate must be a number greater than or equal to 0"))},e.prototype.setPlayListener=function(e){var t=this,r=["onPlayEvent","onPlayStats","onPlayReport","onPlaySEI"];ic(r).call(r,(function(r){var n=e[r];void 0!==n&&("function"==typeof n?(t.listener[r]=n,"onPlayReport"===r&&t.report.setHandler(n)):zk.log("".concat(r," must be function")))}))},e.prototype.setPlayerView=function(e){if(this.playerView)zk.log("player view is existed");else{var t="string"==typeof e?document.getElementById(e):e;if(t&&(t instanceof HTMLDivElement||t instanceof HTMLVideoElement))if(t instanceof HTMLVideoElement)this.playerView=t;else{var r=document.createElement("video");r.autoplay=!0,r.muted=!0,r.controls=!0,r.playsInline=!0,r.setAttribute("webkit-playsinline",""),r.setAttribute("x5-playsinline",""),r.setAttribute("style",ME),t.appendChild(r),this.playerView=r}else zk.log("Require container element id or HTMLDivElement")}},e.prototype.startPlay=function(e){var t,r,n,i,o=this;if(this.playerView){var a=function(e){var t=/^(?:webrtc:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)([^?#]*)(?:\?*)(?:[^?#]*)/.exec(e);if(t)return t[1];var r=/^(?:https?:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)([^?#]*)(?:\.sdp)(?:\?*)(?:[^?#]*)/.exec(e);return r?r[1]:null}(e);if(null!==a)if(this.isVideoExisted)zk.log("Video is existed, please stop playing first");else if(this.receiveConfig.video||this.receiveConfig.audio){var s=(new Date).getTime();this.report.clearData(),this.report.setData("u64_timestamp",s),this.report.setTempData("pull_start_time",s),this.report.markTime("pull_start"),this.report.setData("bytes_stream_id",a,!0),this.report.setData("str_stream_url",e,!0);var c=function(e){var t=/^(?:webrtc:\/\/)([0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)(?:[^?#]*)(?:\?*)(?:[^?#]*)/.exec(e);return t?t[1]:null}(e);if(this.signal.updateSignalDomain(c),this.rawUrl=e,this.playUrl=function(e){var t=ek(e,"tabr_bitrates"),r=ek(e,"tabr_start_bitrate"),n=ek(e,"tabr_control"),i=e;return t&&r&&(i=tk(i,"tabr_control",null),i=tk(i,"webrtc_tabr_level","auto"===n?"server_control":"client_control")),i}(e),this.streamReceiveFail.curNum=0,this.streamDecodeFail.curNum=0,this.streamPlaying.status=RE.INIT,this.abrClient.seq=0,this.p2pMode.source="",this.p2pMode.enable){if(this.p2pConnection)return void zk.log("p2p connection is existed, please stop playing first");if(bA.support()&&this.receiveConfig.video&&this.receiveConfig.audio&&!function(e){var t=ek(e,"tabr_bitrates"),r=ek(e,"tabr_start_bitrate");return!(!t||!r)}(e)){this.report.setData("u32_isp2p",1,!0),null===(r=(t=this.listener).onPlayEvent)||void 0===r||r.call(t,PE.PLAY_EVT_P2P_START_SUCCESS);var u=this.signal.getSignalDomain();return this.p2pConnection=bA.create(ou(ou({},this.p2pMode.config),{connectRetryCount:this.connectRetry.maxNum,connectRetryDelay:1e3*this.connectRetry.delay,pullStreamDomain:u,stopStreamDomain:u})),this.p2pConnection.on(bA.Event,this.onP2PEvent),void this.p2pConnection.load(this.playUrl).then((function(e){var t=e.srcObject;o.stream=t,o.onAddStream(o.stream)}))}zk.log("p2p mode start failed"),null===(i=(n=this.listener).onPlayEvent)||void 0===i||i.call(n,PE.PLAY_ERR_P2P_START_FAIL)}this.report.setData("u32_isp2p",0,!0),this.webrtcConnection||(this.webrtcConnection=new aA,this.webrtcConnection.init({onAddTrack:this.onAddTrack,onSetLocalDescription:this.onSetLocalDescription,onConnect:this.onConnect,onDisconnect:this.onDisconnect,onError:this.onError,onReceiveSEI:this.onReceiveSEI})),this.webrtcConnection.initWebRTCConnect({config:{connection:{isReceiveSEI:this.seiConfig.enable&&this.receiveConfig.video,isCreateSEIDataChannel:this.seiConfig.fallback,maxSEIDelayTime:this.seiConfig.maxDelayTime},offer:{offerToReceiveVideo:this.receiveConfig.video,offerToReceiveAudio:this.receiveConfig.audio}}})}else zk.log("Both receiveVideo and receiveAudio are false");else zk.log("Play url is not correct")}else zk.log("Please set player view first")},e.prototype.stopPlay=function(e){var t,r,n;return void 0===e&&(e=!0),su(this,void 0,void 0,(function(){return cu(this,(function(i){switch(i.label){case 0:return this.isVideoExisted?(this.isVideoExisted=!1,this.timer.disconnectTimeout&&(window.clearTimeout(this.timer.disconnectTimeout),this.timer.disconnectTimeout=null),this.timer.retryTimeout&&(window.clearTimeout(this.timer.retryTimeout),this.timer.retryTimeout=null),this.abrClient.curBitrate="",this.connectRetry.curNum=0,this.connectRetry.playing=!1,this.timer.statsInterval&&(window.clearInterval(this.timer.statsInterval),this.timer.statsInterval=null),this.timer.reportInterval&&(window.clearInterval(this.timer.reportInterval),this.timer.reportInterval=null),this.playUrl&&this.svrSig&&(this.signal.fetchStopStream({streamurl:this.playUrl,svrsig:this.svrSig}).catch((function(e){zk.log("request stopStream error,",e)})),this.playUrl=null,this.svrSig=null),[4,this.startReport("stop")]):(zk.log("Video is not existed"),[2]);case 1:var o;if(i.sent(),this.p2pConnection?(this.p2pConnection.destroy(),this.p2pConnection=null):this.webrtcConnection.disconnect(),e)null===(t=this.stream)||void 0===t||ic(o=t.getTracks()).call(o,(function(e){e.stop()}));return this.stream=null,this.playerView&&(this.playerView.pause(),e&&(this.playerView.srcObject=null,this.playerView.load())),null===(n=(r=this.listener).onPlayEvent)||void 0===n||n.call(r,PE.PLAY_EVT_PLAY_STOP),[2]}}))}))},e.prototype.switchStream=function(e){var t,r;return su(this,void 0,void 0,(function(){var n,i,o;return cu(this,(function(a){switch(a.label){case 0:if(s=this.rawUrl,c=e,u=function(e){var t=ek(e,"tabr_bitrates"),r=ek(e,"tabr_start_bitrate"),n=ek(e,"tabr_control");return!(!t||!r||"auto"===n)},!u(s)||!u(c)||tk(s,"tabr_start_bitrate",null)!==tk(c,"tabr_start_bitrate",null))return[3,5];if(!this.svrSig)return[3,4];a.label=1;case 1:return a.trys.push([1,3,,4]),n=ek(e,"tabr_start_bitrate"),zk.log("abr control: switch ".concat(n)),this.abrClient.seq=this.abrClient.seq+1,[4,this.signal.fetchAbrControl({svrsig:this.svrSig,seq:this.abrClient.seq,bitrate_name:n})];case 2:return i=a.sent().seq,this.abrClient.seq=Math.max(this.abrClient.seq,i),this.abrClient.curBitrate=n,[3,4];case 3:return o=a.sent(),zk.log(o.message),null===(r=(t=this.listener).onPlayEvent)||void 0===r||r.call(t,PE.PLAY_ERR_REQUEST_ABR_FAIL,{message:o.message}),[3,4];case 4:return[3,7];case 5:return[4,this.stopPlay()];case 6:a.sent(),this.startPlay(e),a.label=7;case 7:return[2]}var s,c,u}))}))},e.prototype.isPlaying=function(){return this.isVideoExisted&&this.isVideoPlaying()},e.prototype.pause=function(){this.playerView&&this.isVideoExisted&&this.isVideoPlaying()&&this.playerView.pause()},e.prototype.resume=function(){return su(this,void 0,void 0,(function(){var e;return cu(this,(function(t){switch(t.label){case 0:if(!this.playerView||!this.isVideoExisted||this.isVideoPlaying())return[3,4];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.playerView.play()];case 2:return t.sent(),[3,4];case 3:return e=t.sent(),zk.log("resume failed",e),[3,4];case 4:return[2]}}))}))},e.prototype.setMute=function(e){this.playerView&&(this.playerView.muted=!!e)},e.prototype.setVolume=function(e){"number"==typeof e&&e>=0&&e<=100?this.playerView&&(this.playerView.volume=e/100):zk.log("volume must be a number between 0 and 100")},e.prototype.setControls=function(e){this.playerView&&(this.playerView.controls=!!e)},e.prototype.setFullscreen=function(e){this.playerView&&(e?this.requestFullscreen():this.exitFullscreen())},e.prototype.getVideoElement=function(){return this.playerView},e.prototype.destroy=function(){return su(this,void 0,void 0,(function(){return cu(this,(function(e){switch(e.label){case 0:return window.removeEventListener("pagehide",this.onPagehide),window.removeEventListener("beforeunload",this.onBeforeUnload),this.timer.disconnectTimeout&&(window.clearTimeout(this.timer.disconnectTimeout),this.timer.disconnectTimeout=null),this.timer.retryTimeout&&(window.clearTimeout(this.timer.retryTimeout),this.timer.retryTimeout=null),this.timer.statsInterval&&(window.clearInterval(this.timer.statsInterval),this.timer.statsInterval=null),this.timer.reportInterval&&(window.clearInterval(this.timer.reportInterval),this.timer.reportInterval=null),this.isVideoExisted&&(this.webrtcConnection||this.p2pConnection)?[4,this.startReport("stop")]:[3,2];case 1:e.sent(),e.label=2;case 2:var t;if(this.stream)ic(t=this.stream.getTracks()).call(t,(function(e){e.stop()})),this.stream=null;return this.playerView&&(this.playerView.pause(),this.playerView.srcObject=null,this.playerView.parentElement.removeChild(this.playerView),this.playerView=null),this.p2pConnection&&(this.p2pConnection.destroy(),this.p2pConnection=null),this.webrtcConnection&&(this.webrtcConnection.disconnect(),this.webrtcConnection=null),this.lastStatsReport=null,this.report&&(this.report.destroy(),this.report=null),this.signal=null,[2]}}))}))},e.prototype.isVideoPlaying=function(){return!(!this.playerView||!1!==this.playerView.paused)},e.prototype.requestFullscreen=function(){var e=rk;try{e.requestFullscreen?this.playerView[e.requestFullscreen]({navigationUI:"hide"}):lk(this.playerView)?this.playerView.webkitEnterFullScreen():this.enterFullWindow()}catch(e){zk.log("enter full screen failed, ",e)}},e.prototype.exitFullscreen=function(){var e,t=rk;try{if(t.requestFullscreen){var r=document[t.exitFullscreen]();null===(e=null==r?void 0:r.catch)||void 0===e||e.call(r,(function(e){return zk.log("exit full screen failed, ",null==e?void 0:e.message)}))}else lk(this.playerView)?this.playerView.webkitExitFullScreen():this.exitFullWindow()}catch(e){zk.log("exit full screen failed, ",e)}},e.prototype.enterFullWindow=function(){this.docOrigOverflow=document.documentElement.style.overflow,document.documentElement.style.overflow="hidden",this.playerView.setAttribute("style","position:fixed;overflow:hidden;z-index:9999;left:0;top:0;bottom:0;right:0;width:100% !important;height:100% !important;padding-top:0 !important;background-color:#000;")},e.prototype.exitFullWindow=function(){document.documentElement.style.overflow=this.docOrigOverflow,this.playerView.setAttribute("style",ME)},e.prototype.onAddStream=function(e){var t,r,n,i;return su(this,void 0,void 0,(function(){var o,a,s,c,u,l=this;return cu(this,(function(d){switch(d.label){case 0:if(null===(r=(t=this.listener).onPlayEvent)||void 0===r||r.call(t,PE.PLAY_EVT_STREAM_BEGIN),!this.playerView)return[2];if(o=function(){l.playerView.removeEventListener("loadedmetadata",o),l.playerView.removeEventListener("canplay",o),l.report.markTime("video_play"),l.receiveConfig.video?(l.report.measureTime("u32_first_video_decode_time","pull_start","video_play"),l.report.measureTime("u32_first_i_frame","pull_start","video_play",!0)):(l.report.setData("u32_first_video_decode_time",0),l.report.setData("u32_first_i_frame",0),l.report.setTempData("u32_first_i_frame",0)),l.receiveConfig.audio?l.report.measureTime("u32_first_audio_render_time","pull_start","video_play"):l.report.setData("u32_first_audio_render_time",0),l.startReport("start"),l.timer.reportInterval&&(window.clearInterval(l.timer.reportInterval),l.timer.reportInterval=null),l.timer.reportInterval=wc((function(){l.startReport("interval")}),5e3)},this.playerView.addEventListener("loadedmetadata",o),this.playerView.addEventListener("canplay",o),this.playerView.srcObject=e,this.isVideoExisted=!0,!this.playerView.autoplay&&!this.connectRetry.playing)return[2];a=function(e){var t,r,n;zk.log("play failed,",e),l.isVideoExisted&&($i(t=e.toString()).call(t,"NotAllowedError")?Rc((function(){var t;null===(t=l.getConnectionStats())||void 0===t||t.then((function(t){var r,n,i,o,a=function(e){if(e){var t=null,r=null;ic(e).call(e,(function(e){"inbound-rtp"===e.type&&("video"===e.kind||"video"===e.mediaType?t=e.id:"audio"!==e.kind&&"audio"!==e.mediaType||(r=e.id))}));var n=e.get(t),i=e.get(r);return{video:{bytesReceived:null==n?void 0:n.bytesReceived},audio:{bytesReceived:null==i?void 0:i.bytesReceived}}}}(t);null===(n=(r=l.listener).onPlayEvent)||void 0===n||n.call(r,PE.PLAY_ERR_PLAY_FAIL,{message:e.toString(),videoActive:(null===(i=null==a?void 0:a.video)||void 0===i?void 0:i.bytesReceived)>0,audioActive:(null===(o=null==a?void 0:a.audio)||void 0===o?void 0:o.bytesReceived)>0})}))}),1e3):null===(n=(r=l.listener).onPlayEvent)||void 0===n||n.call(r,PE.PLAY_ERR_PLAY_FAIL,{message:e.toString()}))},d.label=1;case 1:return d.trys.push([1,3,,4]),[4,this.playerView.play()];case 2:return d.sent(),s=QE(),KE&&s.length>0&&13===s[0]?(this.playerView.pause(),c=function(){return su(l,void 0,void 0,(function(){var e,t,r;return cu(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,this.playerView.play()];case 1:return n.sent(),zk.log("play ok ios"),null===(r=(t=this.listener).onPlayEvent)||void 0===r||r.call(t,PE.PLAY_EVT_PLAY_BEGIN),[3,3];case 2:return e=n.sent(),a(e),[3,3];case 3:return[2]}}))}))},Rc((function(){c()}),200)):(zk.log("play ok"),null===(i=(n=this.listener).onPlayEvent)||void 0===i||i.call(n,PE.PLAY_EVT_PLAY_BEGIN)),[3,4];case 3:return u=d.sent(),a(u),[3,4];case 4:return[2]}}))}))},e.prototype.onAddTrack=function(e){"video"===e.kind?(this.report.markTime("video_down"),this.report.measureTime("u32_first_frame_down","pull_start","video_down")):"audio"===e.kind&&(this.report.markTime("audio_down"),this.report.measureTime("u32_first_audio_frame_down","pull_start","audio_down")),this.stream||(this.stream=new MediaStream),this.stream.addTrack(e)},e.prototype.onSetLocalDescription=function(e){var t,r,n,i;return su(this,void 0,void 0,(function(){var o,a,s,c,u,l,d,p,f,h,m,v,y=this;return cu(this,(function(g){var b,C,S,_,T;switch(g.label){case 0:return g.trys.push([0,5,,6]),o=yl(),a=gk(e.sdp),s=Oc(b=e.sdp.toLowerCase()).call(b,"h264")>-1,c=bl.parse(window.navigator.userAgent),u=c.browser,l=c.os,d=mi(C=mi(S=mi(_="".concat(l.name||"other"," ")).call(_,l.version,";")).call(S,u.name||"other"," ")).call(C,u.version),null===(r=(t=this.listener).onPlayEvent)||void 0===r||r.call(t,PE.PLAY_EVT_REQUEST_PULL_BEGIN,{localSdp:e}),this.report.setData("str_session_id",o,!0),this.report.setData("bytes_token",o,!0),this.report.setData("str_ice_info",a,!0),this.report.markTime("request_start"),zk.log("H264 is ".concat(s?"":"not ","supported")),zk.log("traceId is webrtc_user=".concat(a)),zk.log("request pullStream start"),p=null,Qc(T=this.playUrl).call(T,"webrtc")?[4,this.signal.fetchPullStream({streamurl:this.playUrl,sessionid:o,clientinfo:d,localsdp:e})]:[3,2];case 1:return p=g.sent(),[3,4];case 2:return[4,this.signal.fetchStreamSdp({streamurl:this.playUrl,sessionid:o,localsdp:e})];case 3:p=g.sent(),g.label=4;case 4:return zk.log("request pullStream success"),this.report.markTime("request_end"),this.report.measureTime("u32_signal_time","request_start","request_end"),f=p.remoteSdp,h=p.svrSig,m=function(e){var t,r,n=e.split("\r\n");try{for(var i=uu(n),o=i.next();!o.done;o=i.next()){var a=o.value;if(Qc(a).call(a,"i="))return a.replace("i=","")}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return""}(f.sdp),null===(i=(n=this.listener).onPlayEvent)||void 0===i||i.call(n,PE.PLAY_EVT_REQUEST_PULL_SUCCESS,{remoteSdp:f}),this.report.setData("bytes_server_label",m,!0),this.webrtcConnection.connect(f),this.svrSig=h,this.timer.disconnectTimeout&&window.clearTimeout(this.timer.disconnectTimeout),this.timer.disconnectTimeout=Rc((function(){zk.log("connect timeout, try reconnect"),y.webrtcConnection.disconnect({code:TE.NEED_RECONNECT,msg:"Connection disconnected, please try again"})}),1e3*this.connectTimeout),[3,6];case 5:return v=g.sent(),zk.log("request pullStream error,",v),v.name===VE?(this.report.markTime("request_end"),this.report.measureTime("u32_signal_time","request_start","request_end"),this.webrtcConnection.disconnect(),this.onPlayError(PE.PLAY_ERR_REQUEST_PULL_FAIL,{message:v.message})):(zk.log("request signal ".concat("AbortError"===v.name?"timeout":"failed",", try reconnect")),this.webrtcConnection.disconnect({code:TE.NEED_RECONNECT,msg:"request signal failed, please try again"})),[3,6];case 6:return[2]}}))}))},e.prototype.onConnect=function(e){var t,r,n=this;e.code===_E.SUCCESS&&(this.stream&&this.onAddStream(this.stream),this.timer.disconnectTimeout&&(window.clearTimeout(this.timer.disconnectTimeout),this.timer.disconnectTimeout=null),this.startStat(),null===(r=(t=this.listener).onPlayEvent)||void 0===r||r.call(t,PE.PLAY_EVT_SERVER_CONNECTED),this.abrClient.curBitrate&&this.svrSig&&(zk.log("abr control: reconnect ".concat(this.abrClient.curBitrate)),this.abrClient.seq=this.abrClient.seq+1,this.signal.fetchAbrControl({svrsig:this.svrSig,seq:this.abrClient.seq,bitrate_name:this.abrClient.curBitrate}).then((function(e){var t=e.seq;n.abrClient.seq=Math.max(n.abrClient.seq,t)})).catch((function(e){zk.log(e.message)}))))},e.prototype.onDisconnect=function(e){var t=this;if(this.lastStatsReport=null,this.streamReceiveFail.curNum=0,this.streamDecodeFail.curNum=0,this.streamPlaying.status=RE.INIT,this.timer.disconnectTimeout&&(window.clearTimeout(this.timer.disconnectTimeout),this.timer.disconnectTimeout=null),this.timer.statsInterval&&(window.clearInterval(this.timer.statsInterval),this.timer.statsInterval=null),this.timer.reportInterval&&(window.clearInterval(this.timer.reportInterval),this.timer.reportInterval=null),e.code===TE.NEED_RECONNECT){var r;if(0===this.connectRetry.curNum&&(this.connectRetry.playing=this.isPlaying()),this.playUrl&&this.svrSig&&(this.signal.fetchStopStream({streamurl:this.playUrl,svrsig:this.svrSig}).catch((function(e){zk.log("request stopStream error,",e)})),this.svrSig=null),this.stream)ic(r=this.stream.getTracks()).call(r,(function(e){e.stop()})),this.stream=null;this.isVideoExisted&&this.playerView&&(this.playerView.pause(),this.playerView.srcObject=null,this.playerView.load(),this.isVideoExisted=!1),this.timer.retryTimeout&&(window.clearTimeout(this.timer.retryTimeout),this.timer.retryTimeout=null),this.timer.retryTimeout=Rc((function(){var e,r,n;t.connectRetry.curNum+=1;var i=t.connectRetry,o=i.curNum,a=i.maxNum;zk.log(mi(e="current retry num: ".concat(o,", max retry num: ")).call(e,a)),o<=a?(zk.log("start connection retry"),null===(n=(r=t.listener).onPlayEvent)||void 0===n||n.call(r,PE.PLAY_EVT_SERVER_RECONNECT),t.startPlay(t.rawUrl)):(zk.log("stop connection retry"),t.onPlayError(PE.PLAY_ERR_SERVER_DISCONNECT),t.connectRetry.curNum=0,t.connectRetry.playing=!1),t.timer.retryTimeout=null}),1e3*this.connectRetry.delay)}},e.prototype.onError=function(e){this.onPlayError(PE.PLAY_ERR_WEBRTC_FAIL,{message:e})},e.prototype.startStat=function(){return su(this,void 0,void 0,(function(){var e,t=this;return cu(this,(function(r){switch(r.label){case 0:return this.report.markTime("server_connected"),this.report.measureTime("u32_connect_server_time","pull_start","server_connected"),this.timer.statsInterval&&(window.clearInterval(this.timer.statsInterval),this.timer.statsInterval=null),[4,this.getConnectionStats()];case 1:return(e=r.sent())&&(this.report.setTempData("start_stats",e),this.report.setTempData("last_stats",e),this.report.setTempData("last_time",(new Date).getTime()),this.onStats(e)),this.timer.statsInterval=wc((function(){var e;null===(e=t.getConnectionStats())||void 0===e||e.then((function(e){e&&t.onStats(e)}))}),1e3),[2]}}))}))},e.prototype.onStats=function(e){var t,r,n,i,o,a,s,c,u,l,d=function(e,t){var r,n,i,o,a;if(void 0===t&&(t=null),t){var s=null,c=null,u=null,l=null,d=null;ic(e).call(e,(function(e){"track"===e.type&&("video"===e.kind||e.frameWidth?s=e.id:("audio"===e.kind||e.audioLevel)&&(u=e.id)),"inbound-rtp"===e.type&&("video"===e.kind||"video"===e.mediaType?c=e.id:"audio"!==e.kind&&"audio"!==e.mediaType||(l=e.id)),"candidate-pair"===e.type&&e.selected?d=e.id:"transport"===e.type&&e.selectedCandidatePairId&&(d=e.selectedCandidatePairId)}));var p=e.get(s),f=e.get(c),h=t.get(s),m=t.get(c),v=0;void 0!==(null==f?void 0:f.timestamp)&&void 0!==(null==m?void 0:m.timestamp)&&(v=(f.timestamp-m.timestamp)/1e3);var y=void 0;if((null==f?void 0:f.codecId)&&(L=e.get(f.codecId))){var g,b,C=L.mimeType,S=L.payloadType,_=L.sdpFmtpLine;y=(null==C?void 0:C.replace("video/",""))||"",S&&_&&(y=mi(g=mi(b="".concat(y," (")).call(b,S,", ")).call(g,_,")"))}var T=void 0;void 0!==(null==f?void 0:f.framesPerSecond)?T=f.framesPerSecond:void 0!==(null==f?void 0:f.framerateMean)?T=f.framerateMean:void 0!==(null==f?void 0:f.framesReceived)&&void 0!==(null==m?void 0:m.framesReceived)&&v?T=(f.framesReceived-m.framesReceived)/v:void 0!==(null==p?void 0:p.framesReceived)&&void 0!==(null==h?void 0:h.framesReceived)&&v&&(T=(p.framesReceived-h.framesReceived)/v);var w=void 0;void 0!==(null==f?void 0:f.framesDecoded)&&void 0!==(null==m?void 0:m.framesDecoded)&&v&&(w=(f.framesDecoded-m.framesDecoded)/v);var R=void 0;void 0!==(null==f?void 0:f.bytesReceived)&&void 0!==(null==m?void 0:m.bytesReceived)&&v&&(R=8*(f.bytesReceived-m.bytesReceived)/v);var P=void 0;void 0!==(null==f?void 0:f.jitterBufferDelay)&&void 0!==(null==f?void 0:f.jitterBufferEmittedCount)&&void 0!==(null==m?void 0:m.jitterBufferDelay)&&void 0!==(null==m?void 0:m.jitterBufferEmittedCount)&&(f.jitterBufferEmittedCount-m.jitterBufferEmittedCount?P=(f.jitterBufferDelay-m.jitterBufferDelay)/(f.jitterBufferEmittedCount-m.jitterBufferEmittedCount)*1e3:f.jitterBufferEmittedCount&&(P=f.jitterBufferDelay/f.jitterBufferEmittedCount*1e3));var E=void 0;void 0!==(null==f?void 0:f.totalDecodeTime)&&void 0!==(null==f?void 0:f.framesDecoded)&&void 0!==(null==m?void 0:m.totalDecodeTime)&&void 0!==(null==m?void 0:m.framesDecoded)&&(f.framesDecoded-m.framesDecoded?E=(f.totalDecodeTime-m.totalDecodeTime)/(f.framesDecoded-m.framesDecoded)*1e3:f.framesDecoded&&(E=f.totalDecodeTime/f.framesDecoded*1e3));var k=e.get(u),A=e.get(l),x=t.get(l);void 0!==(null==A?void 0:A.timestamp)&&void 0!==(null==x?void 0:x.timestamp)&&(v=(A.timestamp-x.timestamp)/1e3);var L,I,D,M=void 0;if((null==A?void 0:A.codecId)&&(L=e.get(A.codecId)))C=L.mimeType,S=L.payloadType,_=L.sdpFmtpLine,M=(null==C?void 0:C.replace("audio/",""))||"",S&&_&&(M=mi(I=mi(D="".concat(M," (")).call(D,S,", ")).call(I,_,")"));var O=void 0;void 0!==(null==A?void 0:A.audioLevel)?O=null==A?void 0:A.audioLevel:void 0!==(null==k?void 0:k.audioLevel)&&(O=null==k?void 0:k.audioLevel);var N=void 0;void 0!==(null==A?void 0:A.bytesReceived)&&void 0!==(null==x?void 0:x.bytesReceived)&&v&&(N=8*(A.bytesReceived-x.bytesReceived)/v);var V=void 0;void 0!==(null==A?void 0:A.jitterBufferDelay)&&void 0!==(null==A?void 0:A.jitterBufferEmittedCount)&&void 0!==(null==x?void 0:x.jitterBufferDelay)&&void 0!==(null==x?void 0:x.jitterBufferEmittedCount)&&(A.jitterBufferEmittedCount-x.jitterBufferEmittedCount?V=(A.jitterBufferDelay-x.jitterBufferDelay)/(A.jitterBufferEmittedCount-x.jitterBufferEmittedCount)*1e3:A.jitterBufferEmittedCount&&(V=A.jitterBufferDelay/A.jitterBufferEmittedCount*1e3));var F=e.get(d),U=void 0;void 0!==(null==F?void 0:F.currentRoundTripTime)&&(U=1e3*F.currentRoundTripTime);var j=void 0,B=void 0,W=void 0,G=void 0;void 0===(null==f?void 0:f.packetsLost)&&void 0===(null==A?void 0:A.packetsLost)||(j=((null==f?void 0:f.packetsLost)||0)+((null==A?void 0:A.packetsLost)||0)),void 0===(null==f?void 0:f.packetsReceived)&&void 0===(null==A?void 0:A.packetsReceived)||(B=((null==f?void 0:f.packetsReceived)||0)+((null==A?void 0:A.packetsReceived)||0)),void 0===(null==m?void 0:m.packetsLost)&&void 0===(null==x?void 0:x.packetsLost)||(W=((null==m?void 0:m.packetsLost)||0)+((null==x?void 0:x.packetsLost)||0)),void 0===(null==m?void 0:m.packetsReceived)&&void 0===(null==x?void 0:x.packetsReceived)||(G=((null==m?void 0:m.packetsReceived)||0)+((null==x?void 0:x.packetsReceived)||0));var Z=void 0;if(void 0!==j&&void 0!==B&&void 0!==W&&void 0!==G){var Y=j-W,H=B-G;Z=Y<=0||H<0?0:Y/(Y+H)*100}var X=uk.UNKNOWN;return void 0===U&&void 0===Z||(Z>50||U>500?X=uk.VERY_BAD:Z>30||U>350?X=uk.BAD:Z>20||U>200?X=uk.POOR:Z>10||U>100?X=uk.GOOD:(Z>=0||U>=0)&&(X=uk.EXCELLENT)),{timestamp:(null==f?void 0:f.timestamp)||(null==A?void 0:A.timestamp),video:{codec:y,bitrate:R&&Number(R.toFixed(2)),framesPerSecond:T&&Math.round(T),framesDecodedPerSecond:w&&Math.round(w),frameWidth:null!==(r=null==f?void 0:f.frameWidth)&&void 0!==r?r:null==p?void 0:p.frameWidth,frameHeight:null!==(n=null==f?void 0:f.frameHeight)&&void 0!==n?n:null==p?void 0:p.frameHeight,framesDecoded:null!==(i=null==f?void 0:f.framesDecoded)&&void 0!==i?i:null==p?void 0:p.framesDecoded,framesDropped:null!==(o=null==f?void 0:f.framesDropped)&&void 0!==o?o:null==p?void 0:p.framesDropped,framesReceived:null!==(a=null==f?void 0:f.framesReceived)&&void 0!==a?a:null==p?void 0:p.framesReceived,packetsLost:null==f?void 0:f.packetsLost,packetsReceived:null==f?void 0:f.packetsReceived,nackCount:null==f?void 0:f.nackCount,firCount:null==f?void 0:f.firCount,pliCount:null==f?void 0:f.pliCount,jitterBufferDelay:P&&Number(P.toFixed(2)),frameDecodeAvgTime:E&&Number(E.toFixed(2))},audio:{codec:M,audioLevel:O,bitrate:N&&Number(N.toFixed(2)),packetsLost:null==A?void 0:A.packetsLost,packetsReceived:null==A?void 0:A.packetsReceived,jitterBufferDelay:V&&Number(V.toFixed(2))},network:{roundTripTime:U,quality:X}}}}(e,this.lastStatsReport);if(this.lastStatsReport=e,d){d.video.frameWidth=d.video.frameWidth||(null===(t=this.playerView)||void 0===t?void 0:t.videoWidth)||void 0,d.video.frameHeight=d.video.frameHeight||(null===(r=this.playerView)||void 0===r?void 0:r.videoHeight)||void 0;var p={};if(this.p2pConnection){var f=this.p2pConnection.getSDKStats(),h=f.uploadBytesTotal,m=f.uploadAuidoBytesTotal,v=f.uploadVideoBytesTotal,y=f.cdnDownloadBytesTotal,g=f.cdnDownloadAuidoBytesTotal,b=f.cdnDownloadVideoBytesTotal,C=f.p2pDownloadBytesTotal,S=f.p2pDownloadAuidoBytesTotal,_=f.p2pDownloadVideoBytesTotal;p.p2p={uploadBytes:h,uploadAuidoBytes:m,uploadVideoBytes:v,cdnDownloadBytes:y,cdnDownloadAuidoBytes:g,cdnDownloadVideoBytes:b,p2pDownloadBytes:C,p2pDownloadAuidoBytes:S,p2pDownloadVideoBytes:_}}if(null===(i=(n=this.listener).onPlayStats)||void 0===i||i.call(n,ou(ou({},d),p)),d.video.bitrate||d.audio.bitrate)this.streamReceiveFail.curNum=0,this.connectRetry.curNum=0;else if(this.streamReceiveFail.curNum+=1,this.streamReceiveFail.curNum>=this.streamReceiveFail.maxNum)return null===(a=(o=this.listener).onPlayEvent)||void 0===a||a.call(o,PE.PLAY_EVT_STREAM_EMPTY),zk.log("stream is empty, try reconnect"),void this.webrtcConnection.disconnect({code:TE.NEED_RECONNECT,msg:"stream is empty, please try again"})}if((null==d?void 0:d.video)&&this.receiveConfig.video&&this.isVideoExisted){var T=d.video,w=T.bitrate,R=T.framesPerSecond,P=T.framesDecoded,E=T.framesDecodedPerSecond;if(w&&R&&!P?(this.streamDecodeFail.curNum+=1,this.streamDecodeFail.curNum===this.streamDecodeFail.maxNum&&this.onPlayError(PE.PLAY_ERR_DECODE_FAIL,{message:"decode failed",bitrate:w,framesPerSecond:R})):this.streamDecodeFail.curNum=0,E>=0||R>=0)if(E<=this.streamPlaying.threshold||R<=this.streamPlaying.threshold){var k=this.report.getTempData("freeze_interval_count")||0;this.report.setTempData("freeze_interval_count",k+1),this.streamPlaying.status!==RE.WAITING&&(this.streamPlaying.status=RE.WAITING,this.report.setTempData("freeze_start_time",(new Date).getTime()),null===(c=(s=this.listener).onPlayEvent)||void 0===c||c.call(s,PE.PLAY_EVT_PLAY_WAITING_BEGIN))}else{if(this.streamPlaying.status===RE.WAITING){var A=this.report.getTempData("freeze_start_time"),x=(new Date).getTime(),L=x-A,I=this.report.getTempData("video_freeze")||{},D=I.count,M=void 0===D?0:D,O=I.totalTime,N=void 0===O?0:O,V=I.maxTime,F=void 0===V?0:V,U=I.endTimes,j=void 0===U?[]:U;M+=1,N+=L,F=Math.max(L,F),j=du(du([],lu(j),!1),[x],!1),this.report.setTempData("video_freeze",{count:M,totalTime:N,maxTime:F,endTimes:j}),null===(l=(u=this.listener).onPlayEvent)||void 0===l||l.call(u,PE.PLAY_EVT_PLAY_WAITING_STOP)}this.streamPlaying.status=RE.PLAYING}}},e.prototype.startReport=function(e,t){var r,n;return void 0===t&&(t=""),su(this,void 0,void 0,(function(){var i,o,a,s,c,u,l,d,p,f,h,m,v,y,g,b,C,S,_,T,w,R,P,E,k,A,x,L,I,D,M,O,N,V;return cu(this,(function(F){switch(F.label){case 0:return i=(new Date).getTime(),o=this.report.getTempData("pull_start_time"),"stop"===e?(a=this.report.getTempData("u32_first_i_frame")||0,this.report.setData("u64_timestamp",i),this.report.setData("u64_begin_timestamp",o),this.report.setData("u32_result",Math.round((i-o)/1e3)),this.report.setData("u32_first_i_frame",a),this.report.setData("u32_first_frame_black",a>1e4?1:0),s=this.report.getTempData("video_freeze")||{},c=s.count,v=void 0===c?0:c,u=s.totalTime,l=void 0===u?0:u,d=s.maxTime,p=void 0===d?0:d,this.report.setData("u64_block_count",v),this.report.setData("u32_video_block_time",l),this.report.setData("u64_block_duration_max",p),this.report.setData("u32_avg_block_time",l&&v?Math.round(l/v):0),this.report.setData("u32_delay_report",t?1:0)):"interval"===e&&(this.report.setData("u64_timestamp",i),this.report.setData("u64_playtime",i-o),f=(this.report.getTempData("video_freeze")||{}).endTimes,h=void 0===f?[]:f,m=this.report.getTempData("last_time")||0,v=ru(h).call(h,(function(e){return e>=m&&e<i})).length,this.report.setData("u32_video_block_count",v),this.report.setTempData("last_time",i),y=this.report.getTempData("freeze_interval_count")||0,this.report.setData("u32_block_usage",y/5*1e3),this.report.setTempData("freeze_interval_count",0)),g=null,"stop"!==e||"pagehide"!==t?[3,1]:(g=this.report.getTempData("last_stats"),[3,3]);case 1:return[4,this.getConnectionStats()];case 2:g=F.sent(),F.label=3;case 3:var U,j;if(g)b=function(e){var t=null;ic(e).call(e,(function(r){var n;"candidate-pair"===r.type&&r.selected?t=r.remoteCandidateId:"transport"===r.type&&r.selectedCandidatePairId&&(t=null===(n=e.get(r.selectedCandidatePairId))||void 0===n?void 0:n.remoteCandidateId)}));var r=e.get(t)||{},n=r.address,i=void 0===n?"":n,o=r.port;return{address:i,port:void 0===o?"":o}}(g),C=b.address,S=b.port,this.report.setData("u32_server_ip",mi(U=mi(j="".concat(C)).call(j,S?":":"")).call(U,S)),"stop"===e?(_=this.report.getTempData("start_stats"),T=function(e,t){var r,n,i=null,o=null,a=null;ic(e).call(e,(function(e){"track"!==e.type||"video"!==e.kind&&!e.frameWidth||(i=e.id),"inbound-rtp"===e.type&&("video"===e.kind||"video"===e.mediaType?o=e.id:"audio"!==e.kind&&"audio"!==e.mediaType||(a=e.id))}));var s=e.get(i),c=e.get(o),u=e.get(a),l=null==t?void 0:t.get(o),d=0;void 0!==(null==c?void 0:c.timestamp)&&void 0!==(null==l?void 0:l.timestamp)&&(d=(c.timestamp-l.timestamp)/1e3);var p=0;void 0!==(null==c?void 0:c.packetsLost)&&void 0!==(null==c?void 0:c.packetsReceived)&&(p=c.packetsLost/(c.packetsLost+c.packetsReceived)*1e3);var f=0;void 0!==(null==u?void 0:u.packetsLost)&&void 0!==(null==u?void 0:u.packetsReceived)&&(f=u.packetsLost/(u.packetsLost+u.packetsReceived)*1e3);var h=0;return void 0!==(null==c?void 0:c.framesDecoded)&&void 0!==(null==l?void 0:l.framesDecoded)&&d&&(h=Math.round((c.framesDecoded-l.framesDecoded)/d)),{width:(null!==(r=null==c?void 0:c.frameWidth)&&void 0!==r?r:null==s?void 0:s.frameWidth)||0,height:(null!==(n=null==c?void 0:c.frameHeight)&&void 0!==n?n:null==s?void 0:s.frameHeight)||0,videoFractionLost:Number(p.toFixed(2)),audioFractionLost:Number(f.toFixed(2)),framerateMean:h}}(g,_),w=T.width,R=T.height,N=T.videoFractionLost,L=T.audioFractionLost,P=T.framerateMean,this.report.setData("u32_video_width",w||(null===(r=this.playerView)||void 0===r?void 0:r.videoWidth)),this.report.setData("u32_video_height",R||(null===(n=this.playerView)||void 0===n?void 0:n.videoHeight)),this.report.setData("u32_video_drop_usage",N),this.report.setData("u32_audio_drop_usage",L),this.report.setData("u32_video_avg_fps",P)):"interval"===e&&(E=this.report.getTempData("last_stats"),k=function(e,t){var r=null,n=null,i=null,o=null;ic(e).call(e,(function(e){"track"===e.type&&("video"===e.kind||e.frameWidth)&&(r=e.id),"inbound-rtp"===e.type&&("video"===e.kind||"video"===e.mediaType?n=e.id:"audio"!==e.kind&&"audio"!==e.mediaType||(i=e.id)),"candidate-pair"===e.type&&e.selected?o=e.id:"transport"===e.type&&e.selectedCandidatePairId&&(o=e.selectedCandidatePairId)}));var a=e.get(r),s=e.get(n),c=null==t?void 0:t.get(r),u=null==t?void 0:t.get(n),l=0;void 0!==(null==s?void 0:s.timestamp)&&void 0!==(null==u?void 0:u.timestamp)&&(l=(s.timestamp-u.timestamp)/1e3);var d=0;void 0!==(null==s?void 0:s.packetsLost)&&void 0!==(null==u?void 0:u.packetsLost)&&(d=s.packetsLost-u.packetsLost);var p=0;void 0!==(null==s?void 0:s.framesReceived)&&void 0!==(null==u?void 0:u.framesReceived)&&l?p=(s.framesReceived-u.framesReceived)/l:void 0!==(null==a?void 0:a.framesReceived)&&void 0!==(null==c?void 0:c.framesReceived)&&l?p=(a.framesReceived-c.framesReceived)/l:void 0!==(null==s?void 0:s.framerateMean)&&void 0!==(null==u?void 0:u.framerateMean)&&(p=(s.framerateMean+u.framerateMean)/2);var f=0;void 0!==(null==s?void 0:s.framesDecoded)&&void 0!==(null==u?void 0:u.framesDecoded)&&l&&(f=(s.framesDecoded-u.framesDecoded)/l);var h=0;void 0!==(null==s?void 0:s.bytesReceived)&&void 0!==(null==u?void 0:u.bytesReceived)&&l&&(h=8*(s.bytesReceived-u.bytesReceived)/l/1024);var m=0;void 0!==(null==s?void 0:s.packetsLost)&&void 0!==(null==s?void 0:s.packetsReceived)&&void 0!==(null==u?void 0:u.packetsLost)&&void 0!==(null==u?void 0:u.packetsReceived)&&(m=(C=s.packetsLost-u.packetsLost)/(C+(s.packetsReceived-u.packetsReceived))*1e3);var v=e.get(i),y=null==t?void 0:t.get(i);void 0!==(null==v?void 0:v.timestamp)&&void 0!==(null==y?void 0:y.timestamp)&&(l=(v.timestamp-y.timestamp)/1e3);var g=0;void 0!==(null==v?void 0:v.packetsLost)&&void 0!==(null==y?void 0:y.packetsLost)&&(g=v.packetsLost-y.packetsLost);var b=0;void 0!==(null==v?void 0:v.bytesReceived)&&void 0!==(null==y?void 0:y.bytesReceived)&&l&&(b=8*(v.bytesReceived-y.bytesReceived)/l/1024);var C,S=0;void 0!==(null==v?void 0:v.packetsLost)&&void 0!==(null==v?void 0:v.packetsReceived)&&void 0!==(null==y?void 0:y.packetsLost)&&void 0!==(null==y?void 0:y.packetsReceived)&&(S=(C=v.packetsLost-y.packetsLost)/(C+(v.packetsReceived-y.packetsReceived))*1e3);var _=e.get(o),T=null==t?void 0:t.get(o),w=0;return void 0!==(null==_?void 0:_.totalRoundTripTime)&&void 0!==(null==_?void 0:_.responsesReceived)&&void 0!==(null==T?void 0:T.totalRoundTripTime)&&void 0!==(null==T?void 0:T.responsesReceived)&&(w=(_.totalRoundTripTime-T.totalRoundTripTime)/(_.responsesReceived-T.responsesReceived)*1e3),{videoPacketsLost:Math.max(d,0),videoReceiveFps:Math.round(p),videoDecodeFps:Math.round(f),videoBitrate:Number(h.toFixed(2)),videoFractionLost:Math.max(Math.round(m),0),audioPacketsLost:Math.max(g,0),audioBitrate:Number(b.toFixed(2)),audioFractionLost:Math.max(Math.round(S),0),roundTripTime:Math.round(w)}}(g,E),A=k.audioPacketsLost,x=k.audioBitrate,L=k.audioFractionLost,I=k.videoReceiveFps,D=k.videoDecodeFps,M=k.videoBitrate,O=k.videoPacketsLost,N=k.videoFractionLost,V=k.roundTripTime,this.report.setData("u32_audio_drop",A),this.report.setData("u32_audio_drop_usage",L),this.report.setData("u32_avg_audio_bitrate",x),this.report.setData("u32_video_drop",O),this.report.setData("u32_video_drop_usage",N),this.report.setData("u32_video_recv_fps",I),this.report.setData("u32_fps",D),this.report.setData("u32_avg_video_bitrate",M),this.report.setData("u32_avg_net_speed",Number((M+x).toFixed(2))),this.report.setData("u64_rtt",V),this.report.setTempData("last_stats",g));return this.report.startReport(e),[2]}}))}))},e.prototype.onPlayError=function(e,t){var r,n,i=(t||{}).message;null===(n=(r=this.listener).onPlayEvent)||void 0===n||n.call(r,e,t),this.report.setData("u64_err_code",e),this.report.setData("str_err_info",i||""),e===PE.PLAY_ERR_SERVER_DISCONNECT?this.startReport("stop"):this.startReport("start")},e.prototype.onBeforeUnload=function(){window.removeEventListener("pagehide",this.onPagehide),this.isVideoExisted&&(this.webrtcConnection||this.p2pConnection)&&this.report&&this.startReport("stop","beforeunload")},e.prototype.onPagehide=function(){this.isVideoExisted&&(this.webrtcConnection||this.p2pConnection)&&this.report&&this.startReport("stop","pagehide")},e.prototype.getConnectionStats=function(){return this.p2pConnection?this.p2pConnection.getStats():this.webrtcConnection?this.webrtcConnection.getStats():void 0},e.prototype.onP2PEvent=function(e){var t,r,n,i,o,a,s,c,u,l,d,p,f=e.code,h=e.msg,m=e.data;switch(zk.log(mi(t="P2P Event, code: ".concat(f,", msg: ")).call(t,h,", data:"),m),f){case bA.EventCode.INF_PLAY_EVT_STREAM_FIRST_AUDIO_FRAME:this.report.markTime("audio_down"),this.report.measureTime("u32_first_audio_frame_down","pull_start","audio_down");break;case bA.EventCode.INF_PLAY_EVT_STREAM_FIRST_VIDEO_FRAME:this.report.markTime("video_down"),this.report.measureTime("u32_first_frame_down","pull_start","video_down");break;case bA.EventCode.INF_PLAY_EVT_REQUEST_PULL_BEGIN:var v={type:"offer",sdp:m.localsdp},y=m.sessionid;null===(n=(r=this.listener).onPlayEvent)||void 0===n||n.call(r,PE.PLAY_EVT_REQUEST_PULL_BEGIN,{localSdp:v}),this.report.setData("str_session_id",y,!0),this.report.setData("bytes_token",y,!0),this.report.setData("str_ice_info",gk(v.sdp),!0),this.report.markTime("request_start");break;case bA.EventCode.INF_PLAY_EVT_REQUEST_PULL_SUCCESS:var g={type:"answer",sdp:m.remotesdp};null===(o=(i=this.listener).onPlayEvent)||void 0===o||o.call(i,PE.PLAY_EVT_REQUEST_PULL_SUCCESS,{remoteSdp:g}),this.report.markTime("request_end"),this.report.measureTime("u32_signal_time","request_start","request_end");break;case bA.EventCode.ERR_PLAY_REQUEST_PULL_FAIL:this.onPlayError(PE.PLAY_ERR_REQUEST_PULL_FAIL,{message:h});break;case bA.EventCode.ERR_FETCH_REQUEST_FAIL:"PULL_STREAM"===m.from&&this.onPlayError(PE.PLAY_ERR_REQUEST_PULL_FAIL,{message:h});break;case bA.EventCode.INF_PLAY_EVT_SERVER_CONNECTED:this.startStat(),null===(s=(a=this.listener).onPlayEvent)||void 0===s||s.call(a,PE.PLAY_EVT_SERVER_CONNECTED);break;case bA.EventCode.INF_PLAY_EVT_SERVER_RECONNECT:this.connectRetry.playing=this.isPlaying(),null===(u=(c=this.listener).onPlayEvent)||void 0===u||u.call(c,PE.PLAY_EVT_SERVER_RECONNECT);break;case bA.EventCode.ERR_LOAD_REACH_MAX_RETRY:this.onPlayError(PE.PLAY_ERR_SERVER_DISCONNECT);break;case bA.EventCode.ERR_PLAY_WEBRTC_FAIL:this.onPlayError(PE.PLAY_ERR_WEBRTC_FAIL,{message:h});break;case bA.EventCode.INF_PLAY_EVT_STREAM_SWITCH:var b=null===(l=m.source)||void 0===l?void 0:l.toLowerCase();this.p2pMode.source=b,null===(p=(d=this.listener).onPlayEvent)||void 0===p||p.call(d,PE.PLAY_EVT_P2P_SOURCE_SWITCH,{source:b})}if(f>1e3&&f<2e3){var C;if(this.lastStatsReport=null,this.timer.statsInterval&&(window.clearInterval(this.timer.statsInterval),this.timer.statsInterval=null),this.timer.reportInterval&&(window.clearInterval(this.timer.reportInterval),this.timer.reportInterval=null),this.stream)ic(C=this.stream.getTracks()).call(C,(function(e){e.stop()})),this.stream=null;this.isVideoExisted&&this.playerView&&(this.playerView.pause(),this.playerView.srcObject=null,this.playerView.load(),this.isVideoExisted=!1),this.p2pConnection.destroy(),this.p2pConnection=null}},e.prototype.onReceiveSEI=function(e){var t,r;null===(r=(t=this.listener).onPlaySEI)||void 0===r||r.call(t,e)},e.version=xE,e}();return CA}));