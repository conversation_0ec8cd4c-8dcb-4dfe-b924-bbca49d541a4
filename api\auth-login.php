<?php
/**
 * 用户登录API
 * 支持传统用户名/邮箱密码登录
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

$auth = new AuthAPI();

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    $auth->jsonResponse(400, '无效的请求数据');
}

$credential = trim($input['credential'] ?? ''); // 用户名或邮箱
$password = $input['password'] ?? '';
$device_info = $input['device_info'] ?? null;

// 验证必填字段
if (empty($credential) || empty($password)) {
    $auth->jsonResponse(400, '请填写用户名/邮箱和密码');
}

try {
    // 查找用户
    $user = $auth->getUserByCredential($credential);
    
    if (!$user) {
        $auth->logLogin(null, 'traditional', strpos($credential, '@') ? 'email' : 'username', 'failed', '用户不存在');
        $auth->jsonResponse(401, '用户名或密码错误');
    }
    
    // 检查用户状态
    if ($user['status'] === 'banned') {
        $auth->logLogin($user['id'], 'traditional', strpos($credential, '@') ? 'email' : 'username', 'failed', '账户已被禁用');
        $auth->jsonResponse(403, '账户已被禁用，请联系管理员');
    }
    
    if ($user['status'] === 'inactive') {
        $auth->logLogin($user['id'], 'traditional', strpos($credential, '@') ? 'email' : 'username', 'failed', '账户未激活');
        $auth->jsonResponse(403, '账户未激活，请先激活账户');
    }
    
    // 检查登录类型和密码
    if ($user['login_type'] === 'wechat') {
        // 纯微信登录用户，检查是否设置了密码
        if (!$user['password']) {
            $auth->logLogin($user['id'], 'traditional', strpos($credential, '@') ? 'email' : 'username', 'failed', '该账户仅支持微信登录，未设置密码');
            $auth->jsonResponse(400, '该账户仅支持微信登录，请使用微信登录或联系管理员设置密码');
        }
    }

    // 验证密码（所有有密码的用户都可以使用密码登录）
    if (!$user['password'] || !$auth->verifyPassword($password, $user['password'])) {
        $auth->logLogin($user['id'], 'traditional', strpos($credential, '@') ? 'email' : 'username', 'failed', '密码错误');
        $auth->jsonResponse(401, '用户名或密码错误');
    }

    // 如果是微信用户首次使用密码登录，更新登录类型为both
    if ($user['login_type'] === 'wechat') {
        $update_stmt = $conn->prepare("UPDATE users SET login_type = 'both', updated_at = NOW() WHERE id = ?");
        $update_stmt->bind_param("i", $user['id']);
        $update_stmt->execute();
    }
    
    // 生成令牌
    $access_token = $auth->generateJWT($user['id'], 'access');
    $refresh_token = $auth->generateJWT($user['id'], 'refresh');
    
    // 保存令牌
    $auth->saveToken($user['id'], $access_token, 'access', $device_info);
    $auth->saveToken($user['id'], $refresh_token, 'refresh', $device_info);
    
    // 更新最后登录信息
    $auth->updateLastLogin($user['id']);
    
    // 记录登录日志
    $auth->logLogin($user['id'], 'traditional', strpos($credential, '@') ? 'email' : 'username', 'success');
    
    // 获取完整用户信息
    $user_info = $auth->getUserById($user['id']);
    
    // 返回成功响应
    $auth->jsonResponse(200, '登录成功', [
        'access_token' => $access_token,
        'refresh_token' => $refresh_token,
        'token_type' => 'Bearer',
        'expires_in' => 7200,
        'user' => [
            'id' => $user_info['id'],
            'name' => $user_info['name'],
            'email' => $user_info['email'],
            'username' => $user_info['username'],
            'nickname' => $user_info['nickname'],
            'avatar' => $user_info['avatar'],
            'phone' => $user_info['phone'],
            'gender' => $user_info['gender'],
            'login_type' => $user_info['login_type'],
            'last_login_at' => $user_info['last_login_at'],
            'login_count' => $user_info['login_count']
        ]
    ]);
    
} catch (Exception $e) {
    error_log('登录错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}