<?php
/**
 * 视频数据修复脚本
 * 修复lessons表中的视频配置问题
 */

require_once '../includes/db.php';

header('Content-Type: application/json; charset=utf-8');

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => '仅支持POST请求'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 开始事务
    $conn->begin_transaction();
    
    $fixes = [];
    $errors = [];
    
    // 1. 修复无效的sample-videos.com URL
    $sample_video_fixes = [
        2 => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
        3 => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
        4 => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
        7 => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4'
    ];
    
    foreach ($sample_video_fixes as $lesson_id => $new_url) {
        $update_sql = "UPDATE lessons SET video_url = ? WHERE id = ?";
        $stmt = $conn->prepare($update_sql);
        $stmt->bind_param("si", $new_url, $lesson_id);
        
        if ($stmt->execute()) {
            $fixes[] = "课时{$lesson_id}: 更新视频URL为有效的测试视频";
        } else {
            $errors[] = "课时{$lesson_id}: 更新视频URL失败 - " . $stmt->error;
        }
    }
    
    // 2. 修复video_type字段不一致问题
    // 将腾讯云m3u8链接的课时改为vod类型
    $vod_lessons = [5, 6, 8, 9]; // 这些课时使用腾讯云视频但类型标记为url
    
    foreach ($vod_lessons as $lesson_id) {
        // 检查当前视频URL是否为腾讯云链接
        $check_sql = "SELECT video_url FROM lessons WHERE id = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("i", $lesson_id);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        $lesson = $result->fetch_assoc();
        
        if ($lesson && strpos($lesson['video_url'], 'vod-qcloud.com') !== false) {
            // 这是腾讯云视频，应该设置为vod类型
            $update_sql = "UPDATE lessons SET 
                          video_type = 'vod',
                          vod_video_url = video_url,
                          video_url = ''
                          WHERE id = ?";
            $stmt = $conn->prepare($update_sql);
            $stmt->bind_param("i", $lesson_id);
            
            if ($stmt->execute()) {
                $fixes[] = "课时{$lesson_id}: 修正为VOD类型，移动URL到vod_video_url字段";
            } else {
                $errors[] = "课时{$lesson_id}: 修正VOD类型失败 - " . $stmt->error;
            }
        }
    }
    
    // 3. 修复缩略图问题
    $thumbnail_sql = "UPDATE lessons SET thumbnail = NULL WHERE thumbnail = '0' OR thumbnail = ''";
    if ($conn->query($thumbnail_sql)) {
        $affected = $conn->affected_rows;
        if ($affected > 0) {
            $fixes[] = "修复了{$affected}个课时的无效缩略图";
        }
    } else {
        $errors[] = "修复缩略图失败 - " . $conn->error;
    }
    
    // 4. 为VOD视频生成假的file_id（如果需要）
    $vod_without_fileid_sql = "SELECT id FROM lessons WHERE video_type = 'vod' AND (vod_file_id IS NULL OR vod_file_id = '')";
    $result = $conn->query($vod_without_fileid_sql);
    
    while ($row = $result->fetch_assoc()) {
        // 生成一个假的file_id用于测试
        $fake_file_id = '5145403692137145' . str_pad($row['id'], 3, '0', STR_PAD_LEFT);
        $update_sql = "UPDATE lessons SET vod_file_id = ? WHERE id = ?";
        $stmt = $conn->prepare($update_sql);
        $stmt->bind_param("si", $fake_file_id, $row['id']);
        
        if ($stmt->execute()) {
            $fixes[] = "课时{$row['id']}: 生成测试用的vod_file_id";
        } else {
            $errors[] = "课时{$row['id']}: 生成vod_file_id失败 - " . $stmt->error;
        }
    }
    
    // 如果有错误，回滚事务
    if (!empty($errors)) {
        $conn->rollback();
        echo json_encode([
            'success' => false,
            'message' => '修复过程中出现错误，已回滚所有更改',
            'errors' => $errors,
            'fixes' => $fixes
        ], JSON_UNESCAPED_UNICODE);
    } else {
        // 提交事务
        $conn->commit();
        echo json_encode([
            'success' => true,
            'message' => '视频数据修复完成',
            'fixes' => $fixes,
            'total_fixes' => count($fixes)
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    $conn->rollback();
    echo json_encode([
        'success' => false,
        'message' => '修复过程中发生异常',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
