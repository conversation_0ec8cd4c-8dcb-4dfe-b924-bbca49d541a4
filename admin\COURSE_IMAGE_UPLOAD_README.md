# 课程图片上传功能说明

## 功能概述

为后台课程管理系统新增了本地图片上传功能，管理员可以直接上传课程缩略图，无需手动输入图片链接。

## 功能特性

### 1. 支持的文件格式
- JPG/JPEG
- PNG
- GIF
- WebP

### 2. 文件大小限制
- 最大文件大小：5MB
- 系统会自动验证文件大小并提示用户

### 3. 上传功能
- **实时进度显示**：上传过程中显示进度条和百分比
- **图片预览**：上传成功后立即显示预览图
- **自动填充**：上传成功后自动填充缩略图链接字段
- **错误处理**：完善的错误提示和处理机制

### 4. 安全特性
- **文件类型验证**：使用MIME类型检测，防止恶意文件上传
- **管理员权限验证**：只有登录的管理员才能上传
- **文件名随机化**：防止文件名冲突和猜测
- **上传日志记录**：记录所有上传操作，便于审计

## 文件结构

```
admin/
├── courses.php                 # 课程管理页面（已增强）
├── upload_course_image.php     # 图片上传API
├── test_upload.php            # 上传功能测试页面
└── COURSE_IMAGE_UPLOAD_README.md  # 本说明文档

uploads/
└── course_thumbnails/         # 课程缩略图存储目录
    └── YYYY/MM/              # 按年月分目录存储
```

## 使用方法

### 1. 添加新课程时上传图片
1. 点击"添加课程"按钮
2. 填写课程基本信息
3. 在"缩略图"字段旁点击"上传图片"按钮
4. 选择本地图片文件
5. 等待上传完成，系统自动填充图片链接
6. 保存课程信息

### 2. 编辑现有课程时更换图片
1. 点击课程列表中的"编辑"按钮
2. 如果已有缩略图，会显示预览
3. 点击"上传图片"按钮选择新图片
4. 上传成功后会替换原有图片链接
5. 点击"删除"按钮可清除图片
6. 更新课程信息

### 3. 测试上传功能
- 访问 `admin/test_upload.php` 页面
- 可以测试上传功能是否正常工作
- 支持点击选择和拖拽上传两种方式

## 技术实现

### 1. 前端功能
- **文件选择**：HTML5 file input
- **拖拽上传**：支持拖拽文件到指定区域
- **进度显示**：XMLHttpRequest upload progress事件
- **预览功能**：动态创建img元素显示预览
- **错误处理**：友好的错误提示消息

### 2. 后端API
- **文件验证**：MIME类型检测和文件大小验证
- **安全上传**：随机文件名生成，防止路径遍历
- **目录管理**：按年月自动创建存储目录
- **日志记录**：记录上传操作到数据库

### 3. 数据库表
```sql
-- 管理员上传日志表（自动创建）
CREATE TABLE admin_upload_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    upload_type VARCHAR(50) NOT NULL DEFAULT 'course_thumbnail',
    file_name VARCHAR(255) NOT NULL,
    file_size INT NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_id (admin_id),
    INDEX idx_upload_type (upload_type),
    INDEX idx_created_at (created_at)
);
```

## 配置说明

### 1. 上传目录权限
确保 `uploads/course_thumbnails/` 目录具有写入权限：
```bash
chmod 755 uploads/course_thumbnails/
```

### 2. PHP配置
确保PHP配置支持文件上传：
```ini
file_uploads = On
upload_max_filesize = 5M
post_max_size = 5M
max_execution_time = 30
```

### 3. Web服务器配置
确保Web服务器允许访问uploads目录中的文件。

## 错误处理

### 常见错误及解决方案

1. **"没有上传文件或上传失败"**
   - 检查文件是否正确选择
   - 确认网络连接正常

2. **"只支持 JPG、PNG、GIF、WebP 格式的图片"**
   - 确认文件格式正确
   - 某些文件可能格式不标准

3. **"图片大小不能超过5MB"**
   - 使用图片压缩工具减小文件大小
   - 或联系管理员调整大小限制

4. **"创建上传目录失败"**
   - 检查uploads目录权限
   - 确认Web服务器有写入权限

5. **"文件保存失败"**
   - 检查磁盘空间是否充足
   - 确认目录权限设置正确

## 维护建议

### 1. 定期清理
- 定期清理未使用的图片文件
- 可以通过数据库查询找出未被课程引用的图片

### 2. 备份策略
- 定期备份uploads目录
- 备份上传日志数据库表

### 3. 监控建议
- 监控uploads目录大小
- 定期检查上传日志，发现异常操作

## 扩展功能

### 可能的未来增强
1. **图片压缩**：自动压缩上传的图片
2. **多尺寸生成**：自动生成不同尺寸的缩略图
3. **云存储支持**：支持上传到云存储服务
4. **批量上传**：支持一次上传多张图片
5. **图片编辑**：在线裁剪和编辑功能

---

**更新时间**: 2025-07-16  
**版本**: v1.0  
**开发者**: 系统管理员
