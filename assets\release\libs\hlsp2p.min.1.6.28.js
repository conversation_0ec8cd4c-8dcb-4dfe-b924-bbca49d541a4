/*! For license information <NAME_EMAIL> */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.HLSP2P=t():e.HLSP2P=t()}(window,(function(){return function(e){var t={};function r(i){if(t[i])return t[i].exports;var n=t[i]={i:i,l:!1,exports:{}};return e[i].call(n.exports,n,n.exports,r),n.l=!0,n.exports}return r.m=e,r.c=t,r.d=function(e,t,i){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)r.d(i,n,function(t){return e[t]}.bind(null,n));return i},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=27)}([function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));var i={SERVICE:"service",PLATFORM:"platform",APPID:"appid",STREAM_ID:"stream_id",MODULE_ID:"module_id",COMMAND:"command",DATA_TIME:"data_time",VERSION:"version",VIDEO_TYPE:"video_type",DATA_TYPE:"data_type",CHANNEL:"channel",PARTNER:"partner",UUID:"uuid",CODE:"code",STR_VIDEO_TYPE:"str_video_type",SRC_TYPE:"src_type",HOST:"host",LIFE:"life",URL:"url",REFERER:"referer",PRO:"pro",USER_AGENT:"user_agent",STR_USER_ID:"str_user_id",STR_PLAY_ID:"str_play_id",CONNECTED:"connected",CONN_TRY:"conn_try",CONN_TRY_TOTAL:"conn_try_total",CONN_SUC:"conn_suc",CONN_SUC_TOTAL:"conn_suc_total",CONN_REJECT:"conn_reject",CONN_REJECT_TOTAL:"conn_reject_total",CONN_KICK:"conn_kick",CONN_DC_ERR:"conn_dc_err",CONN_PC_OPEN_CNT:"conn_dc_open_cnt",CONN_DC_CLOSED:"conn_dc_closed",CONN_INIT_TIMEOUT:"conn_init_timeout",CONN_HTBT_TIMEOUT:"conn_htbt_timeout",CONN_CONFLICT_IGNORE:"conn_conflict_ignore",CONN_CONFLICT_RESET:"conn_conflict_reset",PEER_RTT:"peer_rtt",REQUEST:"request",LOAD_OK_TIME:"loadok_t",CONF_OK_TIME:"confok_t",STUCK_COUNT:"stuck_count",CDN_BYTES:"cdn_bytes",P2P_BYTES:"p2p_bytes",EXIT_REASON:"exit_reason",PLAY_STARTED:"play_started",PLAY_TIME:"play_time",SESSION_TIME:"session_time",TS_CDN_INFO:"ts_cdn_info",PLAYED_BYTES:"played_bytes",P2P_UPLOADED_BYTES:"p2p_upload_bytes",P2P_DOWNLOAD_BYTES:"p2p_download_bytes",BITRATE_CHANGE:"bitrate_change",BITRART:"bitrate",BUFFER_LENGTH:"buffer_length",RANGE_OK:"range_ok",PAUSED:"paused",CDN_DOMAIN:"cdn_domain",REQ_LEVEL_0:"req_level_0",REQ_LEVEL_1:"req_level_1",REQ_LEVEL_2:"req_level_2",REQ_LEVEL_3:"req_level_3",REQ_LEVEL_4:"req_level_4",CDN_COST_MS:"cdn_cost_ms",P2P_FIRST_MS:"p2p_first_ms",P2P_COST_MS:"p2p_costs",P2P_REQ_COST_MS:"p2p_req_costs",P2P_FIRST_RECV_DEPTH:"p2p_first_recv_depth",P2P_TIMEOUT_DEPTH:"p2p_timeout_depth",P2P_FIRST_TIMEOUT_CNT:"p2p_first_timeout_cnt",P2P_DWN_TIMEOUT_CNT:"p2p_dwn_timeout_cnt",CDN_COST_AVG:"cdn_cost_avg",PLAYER_REQ_CNT:"player_req_cnt",P2P_PRE_CNT:"p2p_pre_cnt",CDN_NODE_REQ_CNT:"cdn_node_req_cnt",PRE_REQ_CNT:"pre_req_cnt",ABORT_PRE_CNT:"abort_pre_cnt",CANCEL_PRE_CNT:"cancel_pre_cnt",CDN_REQ_CNT:"cdn_req_cnt",HIT_P2P_CNT:"hit_p2p_cnt",HIT_CDN_CNT:"hit_cdn_cnt",CDN_SUCC_CNT:"cdn_succ_cnt",CDN_TIMEOUT_CNT:"cdn_timeout_cnt",CDN_ERR_CNT:"cdn_err_cnt",CDN_RANGE_CNT:"cdn_range_cnt",CDN_RANGE_BYTES:"cdn_range_bytes",PARENT_CNT:"parent_c",CHILDREN_CNT:"children_c",SUBSCRIBE_CNT:"subscribe_request_c",SUBSCRIBE_TIMEOUT_CNT:"subscribe_timeout_c",SUBSCRIBE_SUCCESS_CNT:"subscribe_success_c",SUBSCRIBE_FAIL_CNT:"subscribe_failure_c",NOP2P_BY_DEPTH:"nop2p_depth",NOP2P_BY_NO_PEER:"nop2p_peer",P2P_SUB_CNT:"p2p_sub_cnt",DROP_PEER_BY_CYCLE:"drop_peer_by_cycle",DROP_PEER_BY_BLACKLIST:"drop_peer_by_blacklist",DROP_PEER_BY_BITRATE:"drop_peer_by_bitrate",SUBSCRIBE_RANGE_CNT:"subscribe_range_c",P2P_RETRY_CNT:"p2p_retry_cnt",P2P_RETRY_MISS_CNT:"p2p_retry_miss_cnt",P2P_RETRY_BY_DISCONNECT:"p2p_retry_by_disconnect",P2P_RETRY_BY_CIRCLE:"p2p_retry_by_circle",P2P_RETRY_BY_REFUSE:"p2p_retry_by_refuse",REFUSED_BY_CYCLE:"refused_by_cycle",REFUSED_BY_BITRATE:"refused_by_diff_bitrate",REFUSED_BY_LOAD:"refused_by_full_load",REFUSED_BY_TOO_DEPTH:"refused_by_too_depth",REFUSED_BY_SN_RANGE:"refused_by_sn_range",BACK_CDN_PEER_DISCONNECT:"back_cdn_peer_disconn",BACK_CDN_RES_PARENT_CIRCLE:"back_cdn_res_circle",BACK_CDN_RES_DEPTH_LIMIT:"back_cdn_depth_limit",BACK_CDN_REQ_REFUSED:"back_cdn_req_refused",BACK_CDN_REQ_PROXY:"back_cdn_req_proxy",BACK_CDN_UPWARD:"back_cdn_upward",BACK_CDN_REQ_TOTAL:"back_cdn_req_total",BACK_CDN_LOW_BUFFER:"back_cdn_low_buffer",LLS_TRIGGER_UPWARD:"lls_trigger_upward",IGNORE_CDN_HAS_BUFFER:"ignore_cdn_has_buffer",IGNORE_CDN_SMALL_SN:"ignore_cdn_small_sn",IGNORE_CDN_MISS_LEVEL:"ignore_cdn_miss_level",BACK_CDN_REQ_EXIST:"back_cdn_req_exist",BACK_CDN_MAKE_REQ:"back_cdn_make_req",SEND_PARENT_TOTAL:"send_parent_total",SEND_PARENT_NEW_CHILD:"send_parent_new_child",SEND_PARENT_ACCEPT:"send_parent_accept",SEND_PARENT_PASS:"send_parent_pass",SEND_PARENT_RESET:"send_parent_reset",PLAYER_REQ_DELAY:"player_req_delay",PLAYER_REQ_DELAY_SIZE:"player_req_delay_size",PLAYER_REQ_LOW_BUFFER:"player_req_low_buffer",WS_STATE:"ws_state",DUPLICATE_BYTES:"duplicate_bytes",CDN_NODE:"cdn_node"}},function(e,t,r){"use strict";function i(){return{publicIP:"",randomPlayId:`${Date.now()}-${Math.floor(Math.random()*(Math.floor(999999999)-Math.ceil(1)))+Math.ceil(1)}`,loadTime:Date.now(),channelId:"",originalUrl:"",cdnDomain:"",startTime:Date.now(),videoId:"video",isLive:!1,signalServer:"signal.qvb.qcloud.com",vodTrackerServer:"https://tracker-00.qvb.qcloud.com/api/tracker-flash/v1",liveTrackerServer:"https://tracker-00.qvb.qcloud.com/api/tracker/v2",reportServer:"https://log.qvb.qcloud.com/reporter/vlive",stunServer:"",PCHeartInterval:6e3,maxPeerCache:20,maxPCConnecting:10,maxPCConnected:12,minPCConnected:10,maxConnectRequestEachPeriod:2,checkPeerInterval:8e3,bufferCount:10,sliceCount:1,DCChunkSize:64512,videoType:"VOD",peerConnWaWeight:.75,bitmapInterval:12e3,channelIdIncludeHost:!0,channelIdIncludeSearch:!1,confInterval:0,enableRTLog:!1,rtLogLevel:3,enableAutoConfig:!0,targetDurationTemplate:[1,2,3,4,5,6,7,8,9,10],enableLLS:!1,p2pReqTimeout:3e3,maxP2PDepth:6,streamP2PChunkLength:61440,maxSubscribeSize:5,syncNodeInterval:1e3,levelForTracker:"default",lowBufferThreshold:0,maxP2PRetryTimes:20,llsAutoConfig:{p2pReqTimeout:[500,1e3,2e3,3e3,4e3,5e3,6e3,7e3,8e3,9e3,1e4]},enableP2pAutoTimeout:!0,cdnEstimateRatio:1.5,enableRange:!0,enableSubscribeReport:!1,enableLocalNetworkShare:!1,enableMultiArea:!0,LNSRttThreshold:40,bucketCount:6,clearBucketInterval:6e4,enableExtraPCCnt:0,p2pLimit:20,p2pStartDelay:15e3,enableCDNDetailReport:!1,cdnBWRatio:.9,cdnCostRatio:.5,liveMaxBuffer:16,vodMaxBuffer:30,p2pEstimateExtra:1e3,checkBufferInterval:1e3,p2pSliceRequestCount:2,peerConnWaInitSendBytes:0,peerConnWaInitReceiveBytes:0,PCCheckAliveInterval:1e3,PCHeartTimeout:1e4,initConnTimeout:1e4,fixPCConflict:!1,trackerInterval:16e3,liveTrackerVersion:"v3",liveModel:"",vodTrackerVersion:"v1",bufferedAmountLimit:209715200,preloadProbability:.2,cdnNodeRatio:.02,confBaseUrl:"https://conf.qvb.qcloud.com/api/v1/vod/h5/",sourceUrl:"",liveDelayPreset:0,enableAegis:!1,service:1,platform:39,moduleId:1080,command:81e4,reportInterval:6e4,domain:"",cloudAppId:0,xp2pAppId:"",aegisId:""}}r.d(t,"b",(function(){return s}));var n=i();function s(){Object.keys(n).forEach((e=>{delete n[e]})),Object.assign(n,i())}s(),t.a=n},function(e,t,r){"use strict";r.d(t,"b",(function(){return s}));var i={curReqSn:void 0,curReqLevel:void 0,hlsjs:void 0,exitReason:void 0,hlsp2p:void 0,cdnEstimate:void 0,p2pEstimate:void 0,curPlayerAdapter:void 0,isCdnNode:!1,targetDuration:0},n={};function s(){Object.keys(n).forEach((e=>{delete n[e]})),Object.assign(n,i)}s(),t.a=n},function(e,t,r){"use strict";t.a={ERROR:"error",LEVEL_LOADED:"hlsLevelLoaded",SIGNAL_SENDING:"signalSending",SIGNAL_BEFORE_SENDING:"signalBeforeSending",SIGNAL_RECEIVING:"signalReceiving",SIGNAL_RECEIVED:"signalReceived",SIGNAL_READY:"signalReady",HlsManifestParsed:"hlsManifestParsed",TRACKER_LOADED:"trackerLoader",TRACKER_STARTING:"trackerStarting",TRACKER_LOADING:"trackerLoading",PEER_REMOVED:"peerRemoved",PEER_CONNECTED:"peerConnected",FRAG_LOADED:"10001",FRAG_LOADED_P2P:"10002",REPORT_START:"reportStart",REPORT_LOADED:"reportLoaded",REPORT_LOADING:"reportLoading",CONF_STARTING:"confStaring",CONF_LOADING:"confLoading",CONF_LOADED:"confLoaded",CONF_PARSED:"confParsed",ABNORMAL_REPORT:"abnormalRequest",NOR_REPORT_START:"norReportStart",FRAGMENT_UPDATED:"fragmentUpdated",LOCK_RESP:"lockRes",LOCK_TO_PEER:"lockToPeer",P2P_FRAG_REQ:"p2pFragReq",RTT_GOT:"rtt_got",PEER_FILTER_PASSED:"peerFilterPassed",PEER_FILTER_BLOCKED:"peerFilterBlocked",PEER_BLACKLIST:"peerBlacklist",CDN_REQUEST:"cdnRequest"}},function(e,t,r){"use strict";r.d(t,"a",(function(){return h}));var i=0,n=1,s=2,a=3,o=4;class h{static createLogHeader(e,t){return`[${r=new Date,i=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return`${e}`.padStart(t,"0")},`${r.getFullYear()}-${i(r.getMonth()+1)}-${i(r.getDate())} ${i(r.getHours())}:${i(r.getMinutes())}:${i(r.getSeconds())}.${i(r.getMilliseconds(),3)}`}] [${e}] [${t}]`;var r,i}constructor(){this.logs=[],this.level=a}destroy(){this.disable(),this.stop(),this.logs=[]}init(e){this._config=e,this.stop(),this.logs=[]}setLevel(e){this.level=e}start(e){var t=e.reportInterval;this._reportInterval=t,this._startUpload()}stop(){this._clearTimer()}enable(){this._enableCollect=!0}disable(){this._enableCollect=!1}trace(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=`${h.createLogHeader(this._config.logPrefix,"TRC")} ${t.join(" ")}`;return this._enableCollect&&i>=this.level&&this._cacheLog(n),n}log(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var i=`${h.createLogHeader(this._config.logPrefix,"LOG")} ${t.join(" ")}`;return this._enableCollect&&n>=this.level&&this._cacheLog(i),i}info(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var i=`${h.createLogHeader(this._config.logPrefix,"INF")} ${t.join(" ")}`;return this._enableCollect&&s>=this.level&&this._cacheLog(i),i}warn(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var i=`${h.createLogHeader(this._config.logPrefix,"WRN")} ${t.join(" ")}`;return this._enableCollect&&a>=this.level&&this._cacheLog(i),i}error(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var i=`${h.createLogHeader(this._config.logPrefix,"ERR")} ${t.join(" ")}`;return this._enableCollect&&o>=this.level&&this._cacheLog(i),i}_cacheLog(e){this.logs.push(e),this.logs.length>5e3&&this.logs.shift()}_startUpload(){this._reportInterval&&this._config&&(this._clearTimer(),this._uploadTimer=setInterval((()=>{if(this._enableCollect&&this.logs.length){var e=`https://rtlog.qvb.qcloud.com/upload/p2p.log?uuid=${this._config.uuid}&version=${this._config.version}&platform=${this._config.platform}&type=data`;fetch(e,{method:"POST",body:this.logs.join("\r\n")}).catch((e=>{})),this.logs=[]}}),this._reportInterval))}_clearTimer(){this._uploadTimer&&(clearInterval(this._uploadTimer),this._uploadTimer=null)}}var l=new h;t.b=l},function(e,t,r){"use strict";t.a={BITMAP:1,SLICE:2,PACKET:3,PING:4,PONG:41,BUFFER_INFO:5,SLICE_REQUEST:6,LOCK_REQ:7,LOCK_RES:8,RTT_REQ:9,RTT_RES:10,P2P_REQ_CHUNK:11,P2P_RES_ERR:12,P2P_RES_CHUNK:13,P2P_REQ_CANCEL:14,P2P_RES_PARENT:15,P2P_REQ_ACCEPT:16,NODE_SYNC:17,P2P_UPWARD:18}},function(e,t,r){var i,n;!function(s,a){"use strict";i=function(){var e=function(){},t="undefined",r=typeof window!==t&&typeof window.navigator!==t&&/Trident\/|MSIE /.test(window.navigator.userAgent),i=["trace","debug","info","warn","error"];function n(e,t){var r=e[t];if("function"==typeof r.bind)return r.bind(e);try{return Function.prototype.bind.call(r,e)}catch(t){return function(){return Function.prototype.apply.apply(r,[e,arguments])}}}function s(){console.log&&(console.log.apply||Function.prototype.apply.apply(console.log,[console,arguments])),console.trace}function a(i){return"debug"===i&&(i="log"),typeof console!==t&&("trace"===i&&r?s:void 0!==console[i]?n(console,i):void 0!==console.log?n(console,"log"):e)}function o(t,r){for(var n=0;n<i.length;n++){var s=i[n];this[s]=n<t?e:this.methodFactory(s,t,r)}this.log=this.debug}function h(e,r,i){return function(){typeof console!==t&&(o.call(this,r,i),this[e].apply(this,arguments))}}function l(e,t,r){return a(e)||h.apply(this,arguments)}function c(e,r,n){var s,a=this,h="loglevel";function c(e){var r=(i[e]||"silent").toUpperCase();if(typeof window!==t&&h){try{return void(window.localStorage[h]=r)}catch(e){}try{window.document.cookie=encodeURIComponent(h)+"="+r+";"}catch(e){}}}function u(){var e;if(typeof window!==t&&h){try{e=window.localStorage[h]}catch(e){}if(typeof e===t)try{var r=window.document.cookie,i=r.indexOf(encodeURIComponent(h)+"=");-1!==i&&(e=/^([^;]+)/.exec(r.slice(i))[1])}catch(e){}return void 0===a.levels[e]&&(e=void 0),e}}"string"==typeof e?h+=":"+e:"symbol"==typeof e&&(h=void 0),a.name=e,a.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},a.methodFactory=n||l,a.getLevel=function(){return s},a.setLevel=function(r,i){if("string"==typeof r&&void 0!==a.levels[r.toUpperCase()]&&(r=a.levels[r.toUpperCase()]),!("number"==typeof r&&r>=0&&r<=a.levels.SILENT))throw"log.setLevel() called with invalid level: "+r;if(s=r,!1!==i&&c(r),o.call(a,r,e),typeof console===t&&r<a.levels.SILENT)return"No console available for logging"},a.setDefaultLevel=function(e){u()||a.setLevel(e,!1)},a.enableAll=function(e){a.setLevel(a.levels.TRACE,e)},a.disableAll=function(e){a.setLevel(a.levels.SILENT,e)};var d=u();null==d&&(d=null==r?"WARN":r),a.setLevel(d,!1)}var u=new c,d={};u.getLogger=function(e){if("symbol"!=typeof e&&"string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");var t=d[e];return t||(t=d[e]=new c(e,u.getLevel(),u.methodFactory)),t};var p=typeof window!==t?window.log:void 0;return u.noConflict=function(){return typeof window!==t&&window.log===u&&(window.log=p),u},u.getLoggers=function(){return d},u.default=u,u},void 0===(n="function"==typeof i?i.call(t,r,t,e):i)||(e.exports=n)}()},function(e,t,r){"use strict";var i=r(4),n="log",s="log",a="info",o="warn",h="error",l={[s]:1,[a]:2,[o]:3,[h]:4};class c{constructor(e,t){this.level=e,this.message=t}}var u=new class{init(e){return this.hlsp2p=e,this._enable=!0,this._logLevel=l[h],this}setLevel(e){return e&&(this._logLevel=l[e]),this}enable(){this._enable=!0}disable(){this._enable=!1}setNext(e){this._nextLogger=e}destroy(){this._nextLogger=null}log(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var a=`${i.a.createLogHeader("HLSP2P","LOG")}${[...t].join(" ")}`;this._enable&&l[s]>=this._logLevel&&this.hlsp2p.trigger(n,new c(s,a)),this._nextLogger&&this._nextLogger.log(...t)}info(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var s=`${i.a.createLogHeader("HLSP2P","INF")}${[...t].join(" ")}`;this._enable&&l[a]>=this._logLevel&&this.hlsp2p.trigger(n,new c(a,s)),this._nextLogger&&this._nextLogger.info(...t)}warn(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var s=`${i.a.createLogHeader("HLSP2P","WRN")}${[...t].join(" ")}`;this._enable&&l[o]>=this._logLevel&&this.hlsp2p.trigger(n,new c(o,s)),this._nextLogger&&this._nextLogger.warn(...t)}error(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var s=`${i.a.createLogHeader("HLSP2P","ERR")}${[...t].join(" ")}`;this._enable&&l[h]>=this._logLevel&&this.hlsp2p.trigger(n,new c(h,s)),this._nextLogger&&this._nextLogger.error(...t)}};t.a=u},function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"b",(function(){return n}));class i{static GetResTemplate(){return{url:"",data:"",statusText:"",dataLen:0,timeRequestPerformance:0,timeRequest:0,TTFBPerformance:0,TTFB:0,timeLoadedPerformance:0,timeLoaded:0,retry:0,httpCode:0,responseURL:"",responseType:"",aborted:!1,__type:"CDN",config:{}}}static GetRequestConfig(){return{timeout:0,maxRetry:0,retryDelay:0,maxRetryDelay:0,url:"",responseType:"",headers:{},useFetch:!1,useRange:!1,range:{start:0,end:0}}}}var n={SUCCESS:1,ERROR:2,TIMEOUT:3,PROGRESS:4,STATUS_CODE:5}},function(e,t,r){"use strict";t.a=class{constructor(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.hasHeader=e,this.segments=[],this.data=null,this.totalLen=0,this.uri=0,this.hasHeader&&(this.pushUInt32(10),this.pushUInt32(this.uri),this.pushUInt16(200))}marshall(){if(0===this.segments.length)return null;this.data=new Uint8Array(this.totalLen);var e=0;return this.segments.forEach((t=>{this.data.set(t,e),e+=t.length})),this.hasHeader&&(this.replaceUInt32(0,this.totalLen),this.replaceUInt32(4,this.uri)),this.data}setUri(e){this.uri=e}replaceUInt32(e,t){new DataView(this.data.buffer).setUint32(e,t,!0)}pushBool(e){this.pushUInt8(e?1:0)}pushUInt8(e){var t=new Uint8Array(1);new DataView(t.buffer).setUint8(0,e),this.segments.push(t),this.totalLen+=1}pushUInt16(e){var t=new Uint8Array(2);new DataView(t.buffer).setUint16(0,e,!0),this.segments.push(t),this.totalLen+=2}pushUInt32(e){var t=new Uint8Array(4);new DataView(t.buffer).setUint32(0,e,!0),this.segments.push(t),this.totalLen+=4}pushUInt64(e){var t=new Uint8Array(8),r=new DataView(t.buffer),i=e%4294967296,n=e/4294967296>>0;r.setUint32(0,i,!0),r.setUint32(4,n,!0),this.segments.push(t),this.totalLen+=8}pushUint8Array(e){this.pushUInt16(e.length),this.segments.push(e),this.totalLen+=e.length}pushUint8ArrayWithoutLen(e){this.segments.push(e),this.totalLen+=e.length}pushUint8Array32(e){this.pushUInt32(e.length),this.segments.push(e),this.totalLen+=e.length}pushUInt32Vector(e){this.pushUInt32(e.length),e.forEach((e=>{this.pushUInt32(e)}))}pushUInt16Vector(e){this.pushUInt32(e.length),e.forEach((e=>{this.pushUInt16(e)}))}pushString(e){var t=e.length;this.pushUInt16(t);for(var r=new Uint8Array(t),i=new DataView(r.buffer),n=0;n<t;n++)i.setUint8(n,e.charCodeAt(n));this.segments.push(r),this.totalLen+=t}pushString32(e){var t=e.length;this.pushUInt32(t);for(var r=new Uint8Array(t),i=new DataView(r.buffer),n=0;n<t;n++)i.setUint8(n,e.charCodeAt(n));this.segments.push(r),this.totalLen+=t}}},function(e,t,r){"use strict";var i=r(28),n=r(35);var s=class{constructor(e,t){this.TAG="ProtoBuffer",this.id=e,this.sn=parseInt(e.split("-")[0],10),this.level=parseInt(e.split("-")[1],10),this.dataLen=0,this.avgLen=0,this.sliceCount=t,this.slicesMap=new Map,this.downloadFrom="cdn"}destroy(){this.slicesMap=null}setArrayBufferData(e){this.dataLen=e.byteLength,this.avgLen=Math.floor(this.dataLen/this.sliceCount);for(var t=this.sliceCount-1,r=0;r<t;r+=1){var n=e.slice(r*this.avgLen,(r+1)*this.avgLen),s=new i.a(this.sn,this.level,r,n);this.slicesMap.set(r,s)}var a=this.sliceCount-1,o=a*this.avgLen,h=e.slice(o),l=new i.a(this.sn,this.level,a,h);this.slicesMap.set(a,l)}setSlice(e,t){this.slicesMap.get(e)||(this.dataLen+=t.dataByteLength,this.slicesMap.set(e,t))}getSlice(e){return this.slicesMap.get(e)}complete(){return this.slicesMap.size===this.sliceCount}arrayBuffer(){if(this.complete()){this.avgLen=Math.floor(this.dataLen/this.sliceCount);var e=new Uint8Array(this.dataLen);return this.slicesMap.forEach((t=>{e.set(new Uint8Array(t.arrayBufferData),t.index*this.avgLen)})),e.buffer}return!1}bufferInfo(){var e=new n.a;return e.sn=this.sn,e.level=this.level,this.slicesMap.forEach((t=>{e.subIndexes.push(t.index)})),e}},a=r(3),o=r(36),h=r(8),l=r(1),c=r(14),u=r(0),d=r(6),p=r.n(d),f=r(2),g=r(7);class _{constructor(){this._cdnBytesTotal=0,this._p2pBytesTotal=0,this._cdnBytes=0,this._p2pBytes=0}reset(){this._cdnBytesTotal=0,this._p2pBytesTotal=0,this._cdnBytes=0,this._p2pBytes=0}addCdnBytes(e){this._cdnBytes+=e,this._cdnBytesTotal+=e}addP2PBytes(e){this._p2pBytes+=e,this._p2pBytesTotal+=e}getStats(){var e={cdnBytesTotal:this._cdnBytesTotal,p2pBytesTotal:this._p2pBytesTotal,cdnBytes:this._cdnBytes,p2pBytes:this._p2pBytes};return this._cdnBytes=0,this._p2pBytes=0,e}}class v extends o.a{constructor(){super(),this.TAG="BufferController",this.map=new Map,this.stat=new _,this.TSInfoFromCDN=[],this._downloadBytes={},this._loadOkTime=0,this._llsDuplicateBytes=0}init(e){this.hlsp2p=e,this.receiveBufferHandler=this.onReceiveBuffer.bind(this),e.on(a.a.FRAG_LOADED,this.receiveBufferHandler),this.receiveChunkHandler=this.onReceiveChunk.bind(this),e.on("chunk_buffer_complete",this.receiveChunkHandler),this._downloadBytes.cdnBytes=0,this._downloadBytes.p2pBytes=0,this.stat.reset(),c.a.registerModule(this.TAG,this.reportCallback.bind(this)),e.once(a.a.FRAG_LOADED,(()=>{this._loadOkTime=Date.now()-l.a.loadTime,p.a.debug("LOAD OK TIME",this._loadOkTime)})),this.onKeepSize=this.keepSize.bind(this),this.keepSizeTimer=setInterval(this.onKeepSize,l.a.checkBufferInterval)}destroy(){this.reset(),this._clearTimer(),this.hlsp2p.off(a.a.FRAG_LOADED,this.receiveBufferHandler),this.hlsp2p=null}_clearTimer(){this.keepSizeTimer||(clearInterval(this.keepSizeTimer),this.keepSizeTimer=null)}has(e){var t=this.map.get(e);return!!t&&t.complete()}add(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"cdn";if(!this.has(e)){var i=new s(e,l.a.sliceCount);i.downloadFrom=r,i.setArrayBufferData(t),this.map.set(e,i)}}get(e){return this.map.get(e)}reset(){this.TSInfoFromCDN=[],this._downloadBytes={},this.map.forEach((e=>e.destroy())),this.map.clear()}size(){return this.map.size}delete(e){var t=this.get(e);t&&(t.destroy(),this.map.delete(e))}keepSize(){var e=this.size()-l.a.bufferCount;if(e>0)for(var t=[...this.map.keys()],r=0;r<e;r+=1){var i=t.shift();this.delete(i),this.hlsp2p.trigger("delete_buffer",{id:i})}}getBufferInfo(e){if(!Array.isArray(e))throw Error("bufferController.getSpecifiedBitmap 参数需为数组");var t=[];return 0===e.length?this.map.forEach((e=>{t.push(e.bufferInfo())})):e.forEach((e=>{var r=this.get(`${e.sn}-${e.level}`);r&&t.push(r.bufferInfo())})),t}onReceiveSlice(e){var t=e.id.slice(0,e.id.lastIndexOf("-")),r=this.map.get(t);r&&r.complete()?p.a.debug(`[${this.TAG} p2p receive slice, buffer已完整, 直接返回]`):(r||((r=new s(t,l.a.sliceCount)).downloadFrom="p2p",this.map.set(t,r)),r.setSlice(e.index,e.slice),r.complete()&&(p.a.debug(`[${this.TAG}] [onReceiveSlice] p2p receive slice, buffer已完整 id: ${t}`),g.a.log(`${t} p2p下载完成, byteLength: ${r.dataLen}`),this.hlsp2p.trigger(a.a.FRAG_LOADED_P2P,{id:e.id})))}getSlices(e,t,r){var i=this.map.get(`${e}-${t}`);return!!i&&i.getSlice(r)}onReceiveBuffer(e){var t=e.id,r=e.binaryData,i=e.resp;this.recordResp(t,r,i),this._downloadBytes.cdnBytes+=r.byteLength,this.stat.addCdnBytes(r.byteLength),g.a.log(`${t} CDN下载ts完成, byteLength: ${r.byteLength}`),this.add(t,r)}onReceiveChunk(e){var t=e.id,r=e.payload,i=e.stats,n=(i.p2pBytes,i.cdnBytes),s=i.duplicateBytes;if(this._downloadBytes.cdnBytes+=n,this.stat.addCdnBytes(n),!this.map.has(t)){var a=Math.max(r.byteLength-n,0);this._downloadBytes.p2pBytes+=a,this.stat.addP2PBytes(a);var o="mix";0===a?o="cdn":0===n&&(o="p2p"),this._llsDuplicateBytes+=s,this.add(t,r.buffer,o),this.hlsp2p.trigger("new_buffer",{id:t})}}genRespFromBuffer(e,t){var r=h.a.GetResTemplate(),i=this.get(e);if(!i)return!1;r.data=i.arrayBuffer(),r.responseType=t.responseType,r.httpCode=200,r.dataLen=r.data.byteLength,r.timeRequestPerformance=performance.now();var n=f.a.cdnEstimate.estimateTTFB()||100;r.TTFBPerformance=n+r.timeRequestPerformance;var s=f.a.cdnEstimate.estimateBandwidth(),a=s?r.dataLen/s:100;return r.timeLoadedPerformance=r.TTFBPerformance+a,r.responseURL=t.url,r.__type="INNER","p2p"!==i.downloadFrom||l.a.enableLLS||(this._downloadBytes.p2pBytes+=r.dataLen,this.stat.addP2PBytes(r.dataLen)),r}recordResp(e,t,r){if(!(this.TSInfoFromCDN.length>100)){var i,n,s;this.TSInfoFromCDN.push(`v1|${i=r.url,n=new URL(i),n.pathname,s=n.pathname.split("/"),s[s.length-1]}|${e}|${t.byteLength}`)}}reportCallback(){var e={};return this._downloadBytes&&(0!==this._downloadBytes.p2pBytes&&(e[u.a.P2P_BYTES]=this._downloadBytes.p2pBytes,this._downloadBytes.p2pBytes=0),0!==this._downloadBytes.cdnBytes&&(e[u.a.CDN_BYTES]=this._downloadBytes.cdnBytes,this._downloadBytes.cdnBytes=0),this._llsDuplicateBytes&&(e[u.a.DUPLICATE_BYTES]=this._llsDuplicateBytes,this._llsDuplicateBytes=0)),this.TSInfoFromCDN.length>0&&(l.a.enableCDNDetailReport?e[u.a.TS_CDN_INFO]=this.TSInfoFromCDN:delete e[u.a.TS_CDN_INFO],this.TSInfoFromCDN=[]),this._loadOkTime&&(e[u.a.LOAD_OK_TIME]=this._loadOkTime,this._loadOkTime=0),e}get downloadBytes(){return this._downloadBytes}}var m=new v;t.a=m},function(e,t,r){"use strict";var i=r(15),n=r.n(i);class s extends n.a{constructor(){super(),this.TAG="VideoProxy",this._videoElement=null,this._listenEvent=["waiting","canplay","loadedmetadata","loadeddata","stalled","canplaythrough"],this._handler=new Map}init(e){var t=e.videoId;this._genHandler(),this.setVideoById(t)}destroy(){this._videoElement&&(this.releaseVideoElement(),this._videoElement=null),this.removeAllListeners()}setVideoByElement(e){this._videoElement&&this.releaseVideoElement(),this._videoElement=e,this.attachVideoElement()}setVideoById(e){var t=document.getElementById(e);this.setVideoByElement(t)}attachVideoElement(){this._handler.forEach(((e,t)=>{this._videoElement.addEventListener(t,e)}))}releaseVideoElement(){this._handler.forEach(((e,t)=>{this._videoElement.removeEventListener(t,e)})),this._handler.clear()}onWaiting(){this.emit("waiting")}onCanplay(){this.emit("canplay")}onLoadedmetadata(){this.emit("loadedmetadata")}onLoadeddata(){this.emit("loadeddata")}onStalled(){this.emit("stalled")}onCanplaythrough(){this.emit("canplaythrough")}get currentTime(){return this._videoElement.currentTime}set currentTime(e){this._videoElement.currentTime=e}get seeking(){return this._videoElement.seeking}get ended(){return this._videoElement.ended}get video(){return this._videoElement}get buffered(){return this._videoElement.buffered}get bufferLength(){var e=this.buffered,t=0;if(e){var r=e.length;r&&(t=e.end(r-1)-a.currentTime)}return t}get paused(){return this._videoElement.paused}set playbackRate(e){this._videoElement.playbackRate=e}get playbackRate(){return this._videoElement.playbackRate}get readyState(){return this._videoElement.readyState}static transEventToFunc(e){if(!e)throw new TypeError("Video-Proxy: param event must be a non-empty string");if(e.length<4)throw new Error("Video-Proxy: no such event");return`on${e[0].toUpperCase()}${e.slice(1)}`}_genHandler(){this._listenEvent.forEach((e=>{var t=s.transEventToFunc(e);if(!this[t])throw new Error("Video-Proxy: no such function");this._handler.set(e,this[t].bind(this))}))}}var a=new s;t.a=a},function(e,t,r){"use strict";var i=r(6),n=r.n(i),s=r(2),a=r(1);var o=new class{constructor(){this.TAG="TaskScheduler",this.map=new Map,this.curTaskId=null,this.p2pReqRef=0,this.p2pTimers=new Set}init(){this.reset()}destroy(){n.a.debug(`${this.TAG} destroy`),this.reset()}has(e){return this.map.has(e)}abort(e){var t=this.get(e);t&&t.abort()}add(e,t){this.map.set(e,t);var r=s.a.p2pEstimate.estimateTimeNeeded(`${e}-0`);if(n.a.debug(`${this.TAG} 预估P2P结束时间 ${e}, ${r}`),r>1e4&&(r=0),r){this.p2pReqRef+=1;var i=setTimeout((()=>{this.p2pReqRef-=1,this.p2pTimers.delete(i),this.tick()}),r+a.a.p2pEstimateExtra)}this.tick()}tick(){if(this.curTaskId)n.a.debug(`[${this.TAG}] [tick] 存在cdn请求 ${this.curTaskId}, 需要排队. 已存在的任务[${[...this.map.keys()]}]`);else if(this.curTaskId=this.pickNextTask(),this.curTaskId){var e=this.map.get(this.curTaskId);n.a.debug(`${this.TAG} tick load ${this.curTaskId}`),e.load()}}pickNextTask(){var e=null;if(0!==this.p2pReqRef)return n.a.debug(`${this.TAG} P2P请求不为空, 不请求CDN`),e;var t=[...this.map.keys()];return t.length&&(e=t[0]),e}get(e){return this.map.get(e)}reset(){this.map.forEach((e=>{e.destroy()})),this.map.clear(),this.p2pTimers.forEach((e=>{clearTimeout(e)})),this.p2pTimers.clear(),this.p2pReqRef=0,this.curTaskId=null}delete(e){var t=this.map.get(e);t&&(t.destroy(),this.map.delete(e)),e===this.curTaskId&&(this.curTaskId=null),s.a.p2pEstimate&&s.a.p2pEstimate.delete(`${e}-0`),this.tick()}size(){return this.map.size}};t.a=o},function(e,t,r){"use strict";r.d(t,"a",(function(){return s}));var i=r(29),n=r.n(i);class s{static fromId(e){var t=e.split("-"),r=n()(t,2),i=r[0],a=r[1];return new s({sn:parseInt(i,10),level:parseInt(a,10)})}static fromObj(e){var t=e.sn,r=e.level;return new s({sn:t,level:r})}constructor(e){var t=e.sn,r=e.level;this.sn=t,this.level=r}toStringId(){return`${this.sn}-${this.level}`}}},function(e,t,r){"use strict";var i=r(1),n=r(3),s=r(22),a=r(21),o=r(0),h=r(18),l=r(8),c=r(37),u=r(23);var d=new class{constructor(){this.TAG="ReportLoader",this.reportCount=null,this._loaderManager=new u.a}init(e){this.hlsp2p=e,this.reportCount=0,this._loaderManager.init()}destroy(){this.hlsp2p=null,this.reportCount=0,this._loaderManager.destroy()}onReportLoading(e){var t=e.data,r=e.lastReport,n=c.a.encrypt(JSON.stringify(t));if(r)navigator&&navigator.sendBeacon&&navigator.sendBeacon(i.a.reportServer,n);else{var s=`report-${this.reportCount}`,a=l.a.GetRequestConfig();a.method="POST",a.url=i.a.reportServer,a.postData=n;var o=new h.a(s,a,this.receiveResp.bind(this));this._loaderManager.add(s,o),o.load()}}receiveResp(e,t,r){t===l.b.SUCCESS&&this.hlsp2p.trigger(n.a.REPORT_LOADED,{Data:r.data}),this._loaderManager.delete(e)}},p=r(2),f=r(25),g=r(50),_="destroy",v="close";var m=new class{constructor(){this.TAG="ReportController",this._callbacks=[]}init(e){this.hlsp2p=e,d.init(e),this.hlsp2p.on(n.a.NOR_REPORT_START,this._norReportStart.bind(this)),this.onCollectReport=this._collectReport.bind(this),window.addEventListener("beforeunload",this.beforeUnload.bind(this))}registerModule(e,t){this._callbacks.push(t)}destroy(){this._timer&&(clearInterval(this._timer),this._timer=null);var e=p.a.exitReason||_;this._collectReport({[o.a.EXIT_REASON]:e},!0),d.destroy(),this._callbacks=[],this.hlsp2p=null}beforeUnload(){this._collectReport({[o.a.EXIT_REASON]:v},!0)}_collectReport(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.template;this._callbacks.forEach((e=>{var t=e();t&&Object.assign(r.i,t)})),e&&Object.assign(r.i,e),t&&(r.i[o.a.PLAY_TIME]=Date.now()-i.a.loadTime),d.onReportLoading({data:r,lastReport:t})}_norReportStart(){this._timer=setInterval(this._collectReport.bind(this),i.a.reportInterval)}get template(){var e=new g.UAParser(navigator.userAgent).getResult();return{service:i.a.service,platform:i.a.platform,appid:i.a.cloudAppId,stream_id:i.a.channelId,module_id:i.a.moduleId,command:i.a.command,data_time:Math.round(s.a.timestamp()/1e3),version:f.c,build_time:f.a,commit_id:f.b,video_type:"LIVE"===i.a.videoType?"live":"vod",data_type:2,channel:i.a.channelId,partner:i.a.xp2pAppId,code:"000",str_video_type:"LIVE"===i.a.videoType?"live":"vod",src_type:"auto",host:window.location.host,life:Math.round(s.a.timestamp()-i.a.startTime),url:i.a.originalUrl,referer:window.location.href,user_agent:window.navigator.userAgent,str_user_id:a.a.myUUID().UUID,str_play_id:i.a.randomPlayId,os_name:e.os.name||"",os_version:e.os.version||"",browser_name:e.browser.name||"",browser_version:e.browser.version||"",browser_major:e.browser.major||"",device_vendor:e.device.vendor||"",device_type:e.device.type||"",i:{[o.a.CDN_DOMAIN]:i.a.cdnDomain}}}};t.a=m},function(e,t,r){"use strict";var i=Object.prototype.hasOwnProperty,n="~";function s(){}function a(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function o(e,t,r,i,s){if("function"!=typeof r)throw new TypeError("The listener must be a function");var o=new a(r,i||e,s),h=n?n+t:t;return e._events[h]?e._events[h].fn?e._events[h]=[e._events[h],o]:e._events[h].push(o):(e._events[h]=o,e._eventsCount++),e}function h(e,t){0==--e._eventsCount?e._events=new s:delete e._events[t]}function l(){this._events=new s,this._eventsCount=0}Object.create&&(s.prototype=Object.create(null),(new s).__proto__||(n=!1)),l.prototype.eventNames=function(){var e,t,r=[];if(0===this._eventsCount)return r;for(t in e=this._events)i.call(e,t)&&r.push(n?t.slice(1):t);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(e)):r},l.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,s=r.length,a=new Array(s);i<s;i++)a[i]=r[i].fn;return a},l.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},l.prototype.emit=function(e,t,r,i,s,a){var o=n?n+e:e;if(!this._events[o])return!1;var h,l,c=this._events[o],u=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),u){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,r),!0;case 4:return c.fn.call(c.context,t,r,i),!0;case 5:return c.fn.call(c.context,t,r,i,s),!0;case 6:return c.fn.call(c.context,t,r,i,s,a),!0}for(l=1,h=new Array(u-1);l<u;l++)h[l-1]=arguments[l];c.fn.apply(c.context,h)}else{var d,p=c.length;for(l=0;l<p;l++)switch(c[l].once&&this.removeListener(e,c[l].fn,void 0,!0),u){case 1:c[l].fn.call(c[l].context);break;case 2:c[l].fn.call(c[l].context,t);break;case 3:c[l].fn.call(c[l].context,t,r);break;case 4:c[l].fn.call(c[l].context,t,r,i);break;default:if(!h)for(d=1,h=new Array(u-1);d<u;d++)h[d-1]=arguments[d];c[l].fn.apply(c[l].context,h)}}return!0},l.prototype.on=function(e,t,r){return o(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return o(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,r,i){var s=n?n+e:e;if(!this._events[s])return this;if(!t)return h(this,s),this;var a=this._events[s];if(a.fn)a.fn!==t||i&&!a.once||r&&a.context!==r||h(this,s);else{for(var o=0,l=[],c=a.length;o<c;o++)(a[o].fn!==t||i&&!a[o].once||r&&a[o].context!==r)&&l.push(a[o]);l.length?this._events[s]=1===l.length?l[0]:l:h(this,s)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&h(this,t)):(this._events=new s,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=n,l.EventEmitter=l,e.exports=l},function(e,t,r){"use strict";var i=r(3),n=r(2);var s=new class{constructor(){this.TAG="PlayList",this.hlsp2p=null}init(e){this.hlsp2p=e,this._reset(),this.LevelLoaded=this.onLevelLoaded.bind(this),this.hlsp2p.on(i.a.LEVEL_LOADED,this.LevelLoaded)}destroy(){this._reset(),this.hlsp2p.off(i.a.LEVEL_LOADED,this.LevelLoaded),this.hlsp2p=null}_reset(){this.levels=[]}findTSInfo(e){for(var t=0;t<this.levels.length;t+=1){var r=this.levels[t];if(!r)return;if(r.fragments){var i=this.findInfo(r.fragments,e);if(void 0!==i)return i}}}findInfo(e,t){return e.find((e=>e.url===t))}hasTS(e,t){var r=this.levels[e];return r&&r.endList>=t&&r.mediaSequence<=t}getTSInfo(e,t){return this.levels[e].fragments.find((e=>e.sn===t))}onLevelLoaded(e){var t=e.playlistType,r=e.mediaSequence,s=e.targetDuration,a=e.endList,o=e.fragments,h=e.level,l={};l.playlistType=t,l.mediaSequence=r,l.targetDuration=s,l.endList=a,l.fragments=o,n.a.targetDuration=s,this.levels[h]=l,this.hlsp2p.trigger(i.a.FRAGMENT_UPDATED)}};t.a=s},function(e,t,r){"use strict";var i=new class{constructor(){this.TAG="TsReqMgr",this._reqMap=new Map,this._respMap=new Map}init(){this.reset()}destroy(){this.reset()}reset(){this._reqMap.clear(),this._respMap.clear()}addReq(e,t,r){this._reqMap.set(e,t),this._respMap.set(e,r)}getReq(e){return this._reqMap.get(e)}getResp(e){return this._respMap.get(e)}has(e){return this._reqMap.has(e)}getRespMap(){return this._respMap}delete(e){this._reqMap.delete(e),this._respMap.delete(e)}size(){return this._reqMap.size}},n=r(12),s=r(18),a=r(10),o=r(8),h=r(3),l=r(36),c=r(6),u=r.n(c),d=r(2),p=r(14),f=r(0),g=r(7),_=r(1),v=r(13),m=r(11),y=r(4);class b{constructor(e,t){this.TAG="P2PRequestLoader",this.id=e,this.config=t,this.p2pReqTimeout=t.p2pReqTimeout||3e3,this.enableP2pAutoTimeout=t.enableP2pAutoTimeout||!1,this._aborted=!1,this._succed=!1}init(e,t){this.hlsp2p=e,this._reportData=t}setCDNRequestCallback(e){this.cdnReqCb=e}load(){this._load()}backToCDN(){y.b.warn(`[P2PRequestLoader] backToCDN ${this.id}`),clearTimeout(this.timeoutTimer),n.a.delete(this.id),this.cdnReqCb&&this.cdnReqCb()}destroy(){this._aborted||(this.cb=()=>{},this.abort(),this.hlsp2p=null,this._reportData=null)}abort(){this._aborted=!0,clearTimeout(this.timeoutTimer),this._succed||this.hlsp2p.subscriber.cancelChunkRequest(this.id)}_load(){u.a.debug(`[${this.TAG}] start download ${this.id}`);var e=v.a.fromId(this.id);this._reportData&&(this._reportData[f.a.P2P_SUB_CNT]+=1);var t=this.p2pReqTimeout,r=this.p2pReqTimeout;if(this.enableP2pAutoTimeout&&d.a.targetDuration){var i=.1*d.a.targetDuration;if((r=1e3*Math.max(i,m.a.bufferLength-i))===1e3*i)return this._reportData&&(this._reportData[f.a.BACK_CDN_LOW_BUFFER]+=1),void this.backToCDN()}this.hlsp2p.subscriber.tryToMakeRequest({sn:e.sn,level:e.level,hop:0,url:this.config.url})?(t=Math.min(r,t),this.actualP2PTimeout=t,y.b.info(`[P2PRequestLoader] [_load] 设置动态超时:${t} ${this.id}`),this.timeoutTimer=setTimeout(this._timeout.bind(this),t)):y.b.info(`[P2PRequestLoader] [_load] ${this.id} 未找到节点`)}_timeout(){y.b.warn(`[P2PRequestLoader] [_timeout] P2P请求超时: ${this.actualP2PTimeout}, 直接回cdn ${this.id}`),this.hlsp2p.subscriber.cancelChunkRequest(this.id,{reason:"cdn_timeout"}),this._reportData&&(this._reportData[f.a.SUBSCRIBE_TIMEOUT_CNT]+=1),this.backToCDN()}}var T=r(19),C=r(20);class R extends l.a{constructor(){super(),this.TAG="ProcessTSReq",this.preLevel=0}destroy(){i.getRespMap().forEach(((e,t)=>{u.a.info("return error to player for ",t),e.setRespAndSend(o.b.ERROR,{httpCode:0,statusText:""})})),n.a.destroy(),i.destroy(),this.hlsp2p=null}init(e){this.hlsp2p=e,e.on("chunk_stat",((e,t)=>{var r=i.getResp(e);r&&(r.stats.loaded=t.totalLength)})),e.on("new_buffer",(e=>{var t=e.id,r=i.getResp(t);if(!r||r.inner)return i.delete(t),void n.a.delete(t);var s=i.getReq(t);this.setRespFromBuffer(t,s.genRequestLoaderConfig())})),e.on("imt_load_cdn",(e=>{var t=e.sn,r=e.level,i=e.url;if(this.hlsp2p){var s=v.a.fromObj({sn:t,level:r}).toStringId();if(a.a.has(s))this._reportData[f.a.IGNORE_CDN_HAS_BUFFER]+=1;else if(r===d.a.curReqLevel&&t<d.a.curReqSn)this._reportData[f.a.IGNORE_CDN_SMALL_SN]+=1;else if(r===d.a.curReqLevel)if(n.a.curTaskId!==s)this._reportData[f.a.BACK_CDN_MAKE_REQ]+=1,this._makeLLSCDNRequest(s,{url:i});else{var o=n.a.get(s);o.backToCDN&&(this._reportData[f.a.BACK_CDN_REQ_EXIST]+=1,o.backToCDN())}else this._reportData[f.a.IGNORE_CDN_MISS_LEVEL]+=1}})),p.a.registerModule(this.TAG,this.reportCallback.bind(this)),this.initReport(),n.a.init(),i.init()}load(e,t,r){if(this.hlsp2p.trigger("player_level",{level:t.level}),g.a.log(`播放器请求ts id: ${e} pid_${this.hlsp2p.pid} uuid_${localStorage.myUUID} url: ${t.url} bufferLen: ${m.a.bufferLength.toFixed(3)}`),void 0!==this._reportData[`req_level_${t.level}`]&&(this._reportData[`req_level_${t.level}`]+=1),t.level!==this.preLevel&&(this.preLevel=t.level,this._reportData[f.a.BITRATE_CHANGE]+=1),this._reportData[f.a.PLAYER_REQ_CNT]+=1,_.a.enableLLS)this.llsLoad(e,t,r);else{var n=i.getResp(e);n&&n.inner&&(this._reportData[f.a.ABORT_PRE_CNT]+=1,u.a.debug(`${this.TAG} 存在内部请求, 终止 ${e}`),this.abort(e)),i.addReq(e,t,r),this.receiveReq(e)}}llsLoad(e,t,r){var s=i.getResp(e);s&&s.abort(),i.addReq(e,t,r),n.a.has(e)||this.receiveReq(e)}loadInnerReq(e,t,r){if(u.a.debug(`[${this.TAG}] [loadInnerReq] request ${e}`),i.has(e))return this._reportData[f.a.CANCEL_PRE_CNT]+=1,void u.a.debug(`[${this.TAG}] [loadInnerReq], 请求已存在 ${e} 返回`);this._reportData[f.a.PRE_REQ_CNT]+=1,g.a.log(`${e} 内部预请求ts`),r.inner=!0,i.addReq(e,t,r),this.receiveReq(e)}receiveReq(e){var t=i.getReq(e);u.a.debug(`[${this.TAG}] [receiveReq] id: ${e}, url: ${t.url}`);var r=t.genRequestLoaderConfig();a.a.has(e)?this.setRespFromBuffer(e,r):(u.a.debug(`[${this.TAG}] [receiveReq] 未命中缓存 ${e}`),n.a.has(e)?u.a.debug(`[${this.TAG}] [receiveReq] 请求队列中已存在 ${e}, 返回`):_.a.enableLLS?this._requestLLS(e,r):this._requestNormalCDN(e,r))}_requestNormalCDN(e,t){var r=new s.a(e,t,this.onNormalRecvCDNResp.bind(this));this._reportData[f.a.CDN_REQ_CNT]+=1,this.hlsp2p.trigger(h.a.CDN_REQUEST),n.a.add(e,r)}_requestLLS(e,t){if(_.a.lowBufferThreshold&&m.a.bufferLength<_.a.lowBufferThreshold)return this._reportData[f.a.PLAYER_REQ_LOW_BUFFER]+=1,void this._makeLLSCDNRequest(e,{url:t.url});t.p2pReqTimeout=_.a.p2pReqTimeout,t.useFetch=!0,t.enableP2pAutoTimeout=_.a.enableP2pAutoTimeout;var r=new b(e,t);r.init(this.hlsp2p,this._reportData),r.setCDNRequestCallback(this._makeLLSCDNRequest.bind(this,e,{url:t.url})),n.a.curTaskId!==e&&(this._reportData[f.a.PLAYER_REQ_DELAY]+=1),i.getResp(e).enableDelayHLSJSAbandonCheck(),n.a.add(e,r)}_makeLLSCDNRequest(e,t){var r=t.url,a=v.a.fromId(e),o={onSuccess:()=>{},onError:()=>{},onTimeout:()=>{},onProgress:()=>{}},h={maxRetry:0,maxRetryDelay:64e3,retryDelay:0,timeout:2e4},l={url:r,responseType:"arraybuffer",frag:{sn:a.sn,level:a.level}};if(i.has(e)){i.getResp(e).stopDelayHLSJSAbandonCheck()}else{var c=new T.a(l,h,o),u=new C.a(l,h,o);u.inner=!0,i.addReq(c.id,c,u)}var d=i.getReq(e).genRequestLoaderConfig();if(d.useFetch=!0,this.hlsp2p.chunkMgr){var p=this.hlsp2p.chunkMgr.getRange(e),g=this.hlsp2p.chunkMgr.getChunks(e)||new Map,m=this.hlsp2p.chunkMgr.getRangeSeq(e);y.b.log(`[_makeLLSCDNRequest] ${e} 查找range header bytes: ${JSON.stringify(p)}, chunkCnt: ${g.size}, chunkRange: ${JSON.stringify(m)}`),p&&0!==p.end&&_.a.enableRange&&(d.useRange=!0,d.range=p,d.headers.Range=`bytes=${p.end}-`,this._reportData[f.a.CDN_RANGE_CNT]+=1)}this.hlsp2p.trigger("subscribe_report",{seq:e,target:"CDN"});var b=new s.a(e,d,this.onLLSRecvCDNResp.bind(this));n.a.add(e,b)}onLLSRecvCDNResp(e,t,r){this.onRecvLLSTSRespData(e,t,r),this.statResp(e,t,r),[o.b.ERROR,o.b.TIMEOUT].includes(t)&&this.sendRespToPlayer(e,t,r)}onNormalRecvCDNResp(e,t,r){this.statResp(e,t,r);var n=i.getReq(e),s=r.data?r.data.byteLength:void 0;u.a.debug(`[${this.TAG}] [receiveResp] from cdn, id: ${e}, status: ${{1:"SUCCESS",2:"ERROR",3:"TIMEOUT"}[t]}, len: ${s}, url: ${n.url}`),t===o.b.SUCCESS&&this.hlsp2p&&this.hlsp2p.trigger(h.a.FRAG_LOADED,{id:e,binaryData:r.data,resp:r}),this.sendRespToPlayer(e,t,r)}statResp(e,t,r){t!==o.b.PROGRESS&&(t===o.b.SUCCESS&&(d.a.cdnEstimate.recordTTFB(r.TTFBPerformance-r.timeRequestPerformance),d.a.cdnEstimate.recordBandwidth(r.dataLen,r.timeLoadedPerformance-r.TTFBPerformance),y.b.warn(`[processTSReq] [statResp] CDN下载耗时 ${r.timeLoadedPerformance-r.timeRequestPerformance} ${e} ${localStorage.myUUID} ${this.hlsp2p.pid}`),this.recordDownloadCost(r.timeLoadedPerformance-r.timeRequestPerformance),this._reportData[f.a.CDN_SUCC_CNT]+=1,r.config.useRange&&(this._reportData[f.a.CDN_RANGE_BYTES]+=r.dataLen)),t===o.b.ERROR&&(this._reportData[f.a.CDN_ERR_CNT]+=1),t===o.b.TIMEOUT&&(this._reportData[f.a.CDN_TIMEOUT_CNT]+=1))}onRecvLLSTSRespData(e,t,r){if(_.a.enableLLS&&this.hlsp2p.chunkMgr){if(t===o.b.STATUS_CODE){var i=r;if(i.config.useRange){if(200===i.statusCode)this._reportData[f.a.RANGE_OK]=0,this.hlsp2p.chunkMgr.deleteBufFrag(e),this.hlsp2p.chunkMgr.createBufFrag(e,{startByteOffset:0});else if(206===i.statusCode){var n=i.config.range.end;this.hlsp2p.chunkMgr.deleteBufFrag(e),this.hlsp2p.chunkMgr.createBufFrag(e,{startByteOffset:n})}}else 200===i.statusCode&&i.statusCode<=299&&(this.hlsp2p.chunkMgr.deleteBufFrag(e),this.hlsp2p.chunkMgr.createBufFrag(e,{startByteOffset:0}))}var s=r;if(t===o.b.PROGRESS)return void this.hlsp2p.chunkMgr.addBuf(e,new Uint8Array(s.value));t===o.b.SUCCESS&&(this.hlsp2p.chunkMgr.completeBufFrag(e),a.a.recordResp(e,r.data,r))}}setRespFromBuffer(e,t){if(a.a.has(e)){var r=a.a.get(e);"cdn"===r.downloadFrom?this._reportData[f.a.HIT_CDN_CNT]+=1:"p2p"===r.downloadFrom&&(this._reportData[f.a.HIT_P2P_CNT]+=1);var i=a.a.genRespFromBuffer(e,t);if(!i)return;u.a.debug(`[${this.TAG}] [setRespFromBuffer] 返回res给播放器, id: ${e}, size ${i.data.byteLength}} from: ${a.a.get(e).downloadFrom}`),g.a.log(`${e} 命中缓存, download from ${a.a.get(e).downloadFrom} len: ${i.data.byteLength}`),this.sendRespToPlayer(e,o.b.SUCCESS,i)}}sendRespToPlayer(e,t,r){var s=i.getResp(e);s&&(s.inner&&u.a.debug(`${this.TAG} [receiveResp] 内部请求, 响应空回调`),s.inner||(this._reportData[f.a.PLAYED_BYTES]+=r.dataLen),s.setRespAndSend(t,r)),i.delete(e),n.a.delete(e),n.a.tick()}abort(e){(n.a.delete(e),i.has(e))&&i.getResp(e).abort();i.delete(e)}recordDownloadCost(e){this._reportData[f.a.CDN_COST_MS].length>100||this._reportData[f.a.CDN_COST_MS].push(Math.floor(e))}initReport(){this._reportData={[f.a.REQ_LEVEL_0]:0,[f.a.REQ_LEVEL_1]:0,[f.a.REQ_LEVEL_2]:0,[f.a.REQ_LEVEL_3]:0,[f.a.REQ_LEVEL_4]:0,[f.a.CDN_COST_MS]:[],[f.a.PLAYER_REQ_CNT]:0,[f.a.PRE_REQ_CNT]:0,[f.a.ABORT_PRE_CNT]:0,[f.a.CANCEL_PRE_CNT]:0,[f.a.CDN_REQ_CNT]:0,[f.a.HIT_CDN_CNT]:0,[f.a.HIT_P2P_CNT]:0,[f.a.CDN_SUCC_CNT]:0,[f.a.CDN_RANGE_BYTES]:0,[f.a.CDN_ERR_CNT]:0,[f.a.CDN_TIMEOUT_CNT]:0,[f.a.PLAYED_BYTES]:0,[f.a.RANGE_OK]:1,[f.a.BITRATE_CHANGE]:0,[f.a.BITRART]:"0",[f.a.IGNORE_CDN_HAS_BUFFER]:0,[f.a.IGNORE_CDN_SMALL_SN]:0,[f.a.IGNORE_CDN_MISS_LEVEL]:0,[f.a.BACK_CDN_REQ_EXIST]:0,[f.a.BACK_CDN_MAKE_REQ]:0,[f.a.PLAYER_REQ_DELAY]:0,[f.a.PLAYER_REQ_LOW_BUFFER]:0,[f.a.SUBSCRIBE_TIMEOUT_CNT]:0,[f.a.BACK_CDN_LOW_BUFFER]:0,[f.a.P2P_SUB_CNT]:0,[f.a.CDN_RANGE_CNT]:0}}reportCallback(){var e=this._reportData,t=this._reportData[f.a.CDN_COST_MS];return t&&t.length&&(e[f.a.CDN_COST_AVG]=Math.floor(t.reduce(((e,t)=>e+t))/t.length)),e[f.a.BITRART]=`${d.a.curReqLevel}`,this.initReport(),e}}var E=new R;t.a=E},function(e,t,r){"use strict";var i=class{constructor(e,t,r){this.TAG="XHRLoader",this.context={url:e.url,method:e.method||"GET",async:e.async||!0,responseType:e.responseType||"text",postData:e.postData||"",headers:e.headers||[]},r.timeRequestPerformance=performance.now(),this.callbacks=t;var i=this.context,n=i.url,s=i.method,a=i.async,o=i.responseType,h=i.postData,l=i.headers,c=new XMLHttpRequest;this.loader=c,c.open(s,n,a),l.length&&l.forEach((e=>{c.setRequestHeader(e.header,e.value)})),c.responseType=o,c.onreadystatechange=this.readyStateChange.bind(this),"POST"===s&&h?c.send(h):c.send(),this.response=r,this.response.responseType=o}readyStateChange(e){var t=e.currentTarget,r=this.response,i=t.readyState;if(!r.aborted&&i>=2&&(0===r.TTFBPerformance&&(r.TTFBPerformance=performance.now()),4===i)){var n=t.status;r.httpCode=n,n>=200&&n<300?(r.responseURL=t.responseURL,r.timeLoadedPerformance=performance.now(),"arraybuffer"===this.context.responseType?(r.data=t.response,r.dataLen=r.data.byteLength):(r.data=t.responseText,r.dataLen=r.data.length),this.callbacks.onSuccess(r)):(r.statusText=t.statusText,this.callbacks.onError(r))}}abort(){var e=this.loader;e&&4!==e.readyState&&(this.response.aborted=!0,e.abort(),this.loader=null)}},n=r(8),s=r(6),a=r.n(s);class o{constructor(e,t,r){this.TAG="FetchStreamLoader",this.config=e,this.callbacks=t,this.response=r,this._abort=!1,this._abortController=null,this._loadedBytelength=0,this._buffers=[],this._done=!1,this._counter=0,this._open(e.url)}abort(){this._abort||(this._done||(this.response.aborted=!0),this._abort=!0,this._abortController&&this._abortController.abort(),this._readerInstance&&(this._readerInstance.cancel().catch((e=>{})),this._readerInstance=null),this.callbacks={onStatus:()=>{},onError:()=>{},onSuccess:()=>{},onProgress:()=>{}},this._buffers=[])}_open(e){if(!this._readerInstance){var t=this.response;t.timeRequestPerformance=performance.now(),t.responseType=this.config.responseType;var r={method:"GET",mode:"cors",referrerPolicy:"no-referrer-when-downgrade",headers:this.config.headers};window.AbortController&&(this._abortController=new window.AbortController,r.signal=this._abortController.signal),window.fetch(e,r).then((e=>{this._abort||(this.response.httpCode=e.status,this.response.statusText=e.statusText,this.callbacks.onStatus({statusCode:e.status,config:this.config}),e.ok&&e.status>=200&&e.status<=209?(this._readerInstance=e.body.getReader(),this._pump(this._readerInstance),this.response.responseURL=e.url,this.response.TTFBPerformance=performance.now()):404===e.status&&this.callbacks.onError(this.response))})).catch((e=>{this._abort||(this.response.statusText=e.message,this.callbacks.onError(this.response))}))}}_pump(e){this._abort||e.read().then((t=>{var r=t.done,i=t.value;this._abort||(r?this._done||(this._done=!0,this.response.timeLoadedPerformance=performance.now(),this.response.dataLen=this._loadedBytelength,this.response.data=o.combine(this._buffers,this._loadedBytelength),this.callbacks.onSuccess(this.response)):(this._loadedBytelength+=i.byteLength,this._buffers.push(i.slice()),this.callbacks.onProgress({value:i,cnt:this._counter}),this._counter+=1,this._pump(e)))})).catch((e=>{this._abort||(this.response.statusText=e.message,this.callbacks.onError(this.response))}))}static combine(e,t){var r=new Uint8Array(t),i=0;return e.forEach((e=>{r.set(e,i),i+=e.byteLength})),r.buffer}}var h=r(7);t.a=class{constructor(e,t,r){this.TAG="RequestLoader",this.id=e,this.config=t,this.cb=r,this.timeout=t.timeout||16e3,this.retryTimes=t.maxRetry||1,this._retryCounter=0,this.response=n.a.GetResTemplate(),this.response.config=t,this.response.url=this.config.url}load(){this._load()}destroy(){this.cb=()=>{},a.a.debug(`${this.TAG} destroy ${this.id}`),this.abort()}abort(){clearTimeout(this.timeoutTimer),this.xhrLoader&&this.xhrLoader.abort()}_load(){var e=`[http] 开始请求 ${this.id} ${this.config.url}`;this.config.useRange&&(e+=` useRange, rangeHeader: ${this.config.headers.Range}`),h.a.info(e);var t=i;this.config.useFetch&&(t=o),this.xhrLoader=new t(this.config,{onStatus:this._status.bind(this),onSuccess:this._success.bind(this),onError:this._error.bind(this),onProgress:this._progress.bind(this)},this.response),this.timeoutTimer=setTimeout(this._timeout.bind(this),this.timeout)}_retry(){return this._retryCounter<this.retryTimes&&(this._retryCounter+=1,h.a.info(`[http] 请求重试 ${this.id} ${this.config.url}, 重试次数 ${this._retryCounter}`),this.xhrLoader.abort(),this._load(),!0)}_status(e){this.cb(this.id,n.b.STATUS_CODE,e)}_timeout(){var e=(this.response.timeLoadedPerformance-this.response.timeRequestPerformance).toFixed(3),t=(this.response.TTFBPerformance-this.response.timeRequestPerformance).toFixed(3);h.a.error(`[http] 请求超时 ${this.id} ${this.config.url} 总耗时: ${e} ms, 首字节耗时: ${t} ms,`),this.xhrLoader.abort(),this._retry()||(this.response.retry=this._retryCounter,this.cb(this.id,n.b.TIMEOUT,this.response))}_error(){h.a.error(`[http] 请求失败 ${this.id} ${this.config.url}. statusCode: ${this.response.httpCode}, statusText: ${this.response.statusText}`),this._retry()||(clearTimeout(this.timeoutTimer),this.response.retry=this._retryCounter,this.cb(this.id,n.b.ERROR,this.response))}_success(){var e=(this.response.timeLoadedPerformance-this.response.timeRequestPerformance).toFixed(3),t=(this.response.TTFBPerformance-this.response.timeRequestPerformance).toFixed(3);h.a.info(`[http] 请求成功 ${this.id} ${this.config.url}, 总耗时: ${e} ms, 首字节耗时: ${t} ms, bytesLen: ${this.response.dataLen}`),clearTimeout(this.timeoutTimer),this.response.retry=this._retryCounter,this.cb(this.id,n.b.SUCCESS,this.response)}_progress(e){this.cb(this.id,n.b.PROGRESS,e)}}},function(e,t,r){"use strict";var i=r(8);t.a=class{constructor(e,t,r){this.id="",this.url="",this.sequenceNumber=0,this.level=0,this.loaderConfig=i.a.GetRequestConfig(),this.parseConfig(e,t,r)}parseConfig(e,t,r){this.url=e.url;var i=e.frag;this.sequenceNumber=i.sn,this.level=i.level,this.id=e.id||`${this.sequenceNumber}-${this.level}`,this.loaderConfig.url=e.url,this.loaderConfig.responseType=e.responseType,Object.assign(this.loaderConfig,t)}genRequestLoaderConfig(){return this.loaderConfig}}},function(e,t,r){"use strict";var i=r(8),n=r(6),s=r.n(n),a=r(2);class o{constructor(){this.trequest=performance.now(),this.retry=0,this.tfirst=0,this.loaded=0,this.tload=0,this.total=0,this.aborted=!1,this.retry=0,this.chunkCount=0,this.bwEstimate=0,this.loading={start:performance.now(),first:0,end:0},this.parsing={start:0,end:0},this.buffering={start:0,first:0,end:0}}}t.a=class{constructor(e,t,r){this.TAG="ProtoTSResp",this.id="",this.inner=!1,this._context=e,this._callbacks=r,this._response={url:"",data:""},this._httpStatus={code:0,text:""},this._stats=new o,this._delayHlsjsAbandonTimer=null}enableDelayHLSJSAbandonCheck(){this._delayHlsjsAbandonTimer||(this._delayHlsjsAbandonTimer=setInterval((()=>{this._stats.trequest=performance.now(),this._stats.loading.start=performance.now()}),100))}stopDelayHLSJSAbandonCheck(){this._delayHlsjsAbandonTimer&&(clearInterval(this._delayHlsjsAbandonTimer),this._delayHlsjsAbandonTimer=null)}update(e,t,r){this._context=e,this._callbacks=r,this.id=e.id||`${e.frag.sn}-${e.frag.level}`}set stats(e){return this._stats=e}get stats(){return this._stats}abort(){this._stats.aborted=!0,this.stopDelayHLSJSAbandonCheck()}setResponse(e){this._response.url=e.url,this._response.data=e.data}setStats(e){this._stats.trequest=e.timeRequestPerformance,this._stats.retry=e.retry,this._stats.aborted=e.aborted,this._stats.tfirst=Math.max(e.TTFBPerformance,this._stats.trequest),this._stats.loaded=e.dataLen,this._stats.tload=Math.max(e.timeLoadedPerformance,this._stats.tfirst),this._stats.total=e.dataLen,this._stats.__type=e.__type,this.type=e.__type,s.a.debug(`${this.TAG} set resp cdn      [${this.type}] ttfb: ${this._stats.tfirst-this._stats.trequest}, cost: ${this._stats.tload-this._stats.tfirst}`);var t=performance.now();this._stats.tload=t,this._stats.tfirst=t-this._stats.total/a.a.cdnEstimate.estimateBandwidth(),this._stats.trequest=this._stats.tfirst-a.a.cdnEstimate.estimateTTFB(),this._stats.loading.start=this._stats.trequest,this._stats.loading.first=this._stats.tfirst,this._stats.loading.end=this._stats.tload,s.a.debug(`${this.TAG} set resp estimate [${this.type}] ttfb: ${a.a.cdnEstimate.estimateTTFB()}, cost: ${this._stats.total/a.a.cdnEstimate.estimateBandwidth()}`)}setHttpStatus(e){this._httpStatus.code=e.httpCode,this._httpStatus.text=e.statusText}setRespAndSend(e,t){switch(e){case i.b.SUCCESS:this.setResponse(t),this.setStats(t),this.progress(),this.success();break;case i.b.ERROR:this.setHttpStatus(t),this.error();break;case i.b.TIMEOUT:this.setStats(t),this.timeout()}this.stopDelayHLSJSAbandonCheck()}success(){setTimeout((()=>{this._callbacks.onSuccess(this._response,this._stats,this._context)}),0)}error(){this._callbacks.onError({code:this._httpStatus.code,text:this._httpStatus.statusText},this._context)}timeout(){this._callbacks.onTimeout(this._stats,this._context,null)}progress(){this._callbacks.onProgress&&this._callbacks.onProgress(this._stats,this._context,this._response.data,null)}}},function(e,t,r){"use strict";class i{static UUID(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(e=>{var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}static myUUID(){if(window.localStorage){var e,t=parseInt(localStorage.getItem("loadTimes")||0,10),r=localStorage.getItem("myUUID");return r?e=r:(e=i.UUID(),localStorage.setItem("myUUID",e)),36!==e.length&&(e=i.UUID(),localStorage.setItem("myUUID",e)),localStorage.setItem("loadTimes",t+1),{UUID:e,loadTimes:t+1}}return{UUID:i.UUID(),loadTimes:-1}}}t.a=i},function(e,t,r){"use strict";t.a=class{static getLogTime(){var e=new Date;return`${e.getHours()}:${e.getMinutes()}:${e.getSeconds()}.${e.getMilliseconds()}`}static timestamp(){return Date.now()}}},function(e,t,r){"use strict";t.a=class{constructor(){this._list=new Map}init(){this._reset()}destroy(){this._reset()}delete(e){this._list.delete(e)}add(e,t){this._list.has(e),this._list.set(e,t)}_reset(){this._list.forEach((e=>{e.destroy()})),this._list.clear()}}},function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));class i{setMaxBufferLength(e){}}},function(e,t,r){"use strict";r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return s}));var i="1.6.28",n="2023-03-29T07:45:21.113Z",s="d94268d"},function(e,t,r){"use strict";var i=r(19),n=r(20),s=r(17),a=r(16),o=r(2),h=r(24);class l extends h.a{loadTs(e,t){if(this._checkParam({requestContext:e,requestCallbacks:t})){var r={url:e.url,responseType:"arraybuffer",frag:{sn:void 0,level:void 0}},i={onSuccess:(e,r)=>{t.onSuccess(e.data,r)},onError:e=>{t.onError(e)},onTimeout:e=>{t.onTimeout(e)}},n=a.a.findTSInfo(r.url);n?(r.frag.sn=n.sn,r.frag.level=n.level):r.id=`ts_id_${Date.now()}`,this._load(r,{maxRetry:0,maxRetryDelay:64e3,retryDelay:0,timeout:2e4},i)}}_load(e,t,r){var a=e.frag,h=a.sn,l=a.level;o.a.curReqSn=h,o.a.curReqLevel=l;var c=new i.a(e,t,r),u=new n.a(e,t,r),d=c.id;u.id=d,s.a.load(d,c,u)}_checkParam(e){var t=e.requestContext,r=e.requestCallbacks,i=!0;if(t&&t.url||(i=!1,window.console.error("[hlsp2p] ts请求缺少url")),!r)return window.console.error("[hlsp2p] ts请求缺少callbacks参数"),!1;return["onError","onSuccess","onTimeout"].forEach((e=>{r[e]&&"function"==typeof r[e]||(i=!1,window.console.error(`[hlsp2p] ts请求的callbacks参数缺少${e}回调方法`))})),i}}var c=new l;t.a=c},function(e,t,r){"use strict";r.r(t),function(e){var i=r(49),n=r(57),s=r(47),a=r(10),o=r(17),h=r(40),l=r(16),c=r(31),u=r(14),d=r(46),p=r(1),f=r(2),g=r(3),_=r(39),v=r(26),m=r(53),y=r.n(m),b=r(21),T=r(6),C=r.n(T),R=r(54),E=r(55),w=r(48),S=r(56),P=r(7),I=r(25),L=r(4);r(42);class k{static get version(){return I.c}static isSupported(){return _.a.supportMSEH264Playback()&&_.a.supportP2P()&&_.a.supportES()}static get Events(){return{Rollback:"rollback",Log:"log"}}static get uuid(){return b.a.myUUID().UUID}static create(t,r){var i=Object.assign({},r||{});i.url=t.url,e.hlsp2p&&e.hlsp2p.destroy();var n=new k(t,i);return e.hlsp2p=n,n}static createCommon(t){var r=Object.assign({},t);e.hlsp2p&&e.hlsp2p.destroy();var i=new k(null,r);return e.hlsp2p=i,i}constructor(e,t){this.TAG="HLSP2P",this.initLogger(t.logLevel),C.a.setLevel(t.loglevel||"warn"),C.a.info(`[${this.TAG}] init`),Object(p.b)(),Object(f.b)(),f.a.hlsjs=e,k.initConfig(t),this.initCore(),this.initModules(),this.p2pStats=new S.a,this.trigger(g.a.NOR_REPORT_START),setTimeout((()=>{P.a.info(`[初始化], uuid: ${k.uuid}, version: ${k.version}`)}))}destroy(){C.a.debug(`${this.TAG} ### destroy`),P.a.info(`[destroy], uuid: ${k.uuid}`),this._destroyed?C.a.warn(`${this.TAG} Repeated  destroy`):(P.a.destroy(),L.b.destroy(),this._destroyed=!0,this.modules.forEach((e=>{e.destroy()})),this.modules=[],this.core.forEach((e=>{e.destroy()})),this.core=[],Object(p.b)(),Object(f.b)())}static initConfig(e){new n.a(e).process()}initLogger(e){P.a.init(this).setLevel(e||""),L.b.init({uuid:k.uuid,version:k.version,logPrefix:"HLSP2P",platform:"H5-HLS"}),L.b.enable(),P.a.setNext(L.b)}initCore(){this.core=[];var e=new i.a;this.on=e.on.bind(e),this.off=e.off.bind(e),this.once=e.once.bind(e),this.trigger=e.trigger.bind(e),this.core.push(e)}initModules(){if(this.modules=[],u.a.init(this),this.modules.push(u.a),this.modules.push(P.a),l.a.init(this),f.a.hlsjs){var e=this.hlsjsAdapter;e.init(this,f.a.hlsjs),this.modules.push(e)}else h.a.init(this),h.a.load(p.a.url),this.modules.push(h.a);a.a.init(this),o.a.init(this),d.a.init(this),this.modules.push(a.a,o.a,l.a,d.a),setTimeout((()=>{this.modules.length&&(C.a.info(`[${this.TAG}] init p2p`),c.a.init(this),this.initRTLogger(),this.modules.push(c.a))}),p.a.p2pStartDelay),f.a.cdnEstimate=new R.a,f.a.p2pEstimate=new E.a}initRTLogger(){this.on(g.a.CONF_LOADED,(()=>{p.a.enableRTLog?(L.b.setLevel(p.a.rtLogLevel),L.b.start({reportInterval:5e3})):L.b.disable()})),this.on(g.a.CONF_LOADED,(()=>{p.a.enableAegis&&(k.aegis||p.a.aegisId&&(k.aegis=new y.a({id:p.a.aegisId,uin:b.a.myUUID().UUID,version:k.version,onError:!0,webVitals:!1,speedSample:!1,reportApiSpeed:!1,reportAssetSpeed:!1,pagePerformance:!1})))}))}get hlsjsAdapter(){return this._destroyed?(window.console.error("[hlsp2p] sdk已调用destory, 不可再使用"),null):(f.a.curPlayerAdapter=s.a,s.a)}get commonLoader(){return f.a.hlsjs?(window.console.error("[hlsp2p] 初始化已经绑定了hls.js, 不可使用通用loader"),null):this._destroyed?(window.console.error("[hlsp2p] sdk已调用destory, 不可再使用"),null):(f.a.curPlayerAdapter=v.a,v.a)}get vhsXhr(){return f.a.hlsjs?(window.console.error("[hlsp2p] 初始化已经绑定了hls.js, 不可使用通用loader"),null):this._destroyed?(window.console.error("[hlsp2p] sdk已调用destory, 不可再使用"),null):(f.a.curPlayerAdapter=w.a,w.a.p2pXhrFactory())}get hconfig(){return p.a}get peerConnected(){return c.a.peerConnected}get downloadBytes(){return a.a.downloadBytes}getStats(){return Object.assign(this.p2pStats.getStats(),a.a.stat.getStats())}get trackerConnected(){return c.a.trackerConnected}}t.default=k}.call(this,r(41))},function(e,t,r){"use strict";var i=r(5),n=r(1),s=r(34),a=r(6),o=r.n(a);t.a=class{constructor(e,t,r,n){this.sn=e,this.level=t,this.id="",this.index=r,this.arrayBufferData=n,this.dataByteLength=n?n.byteLength:0,this.uri=i.a.SLICE,this.packetAmount=-1,this.packetArray=[],this.receivedPacketCount=0,this.reveivedPacketBytes=0,this.combineCallback=void 0,this.calledCallback=!1,this.createTime=Date.now()}addPacket(e){if(this.calledCallback)o.a.warn(`[ProtoSlice] [addPacket] repeat packet ${e.sn}-${e.level} pktIndex: ${e.packetIndex}`);else{var t=e.uint8ArrayPayload.length,r=e.packetIndex;o.a.debug(`[ProtoSlice] [addPacket] ${e.sn}-${e.level} sliceIndex: ${e.sliceIndex} pktIndex: ${e.packetIndex} payloadlen : ${t}, pkt.payloadLength: ${e.payloadLength}`),this.packetArray[r]||t!==e.payloadLength||(this.packetArray[r]=e,this.receivedPacketCount+=1,this.reveivedPacketBytes+=t),this._ensureReceiveAllPacket()&&this._combinePacketToSlice()}}_ensureReceiveAllPacket(){if(this.packetAmount>this.receivedPacketCount)return!1;for(var e=0;e<this.packetAmount;e++)if(!this.packetArray[e])return!1;return!0}_combinePacketToSlice(){var e=new Uint8Array(this.reveivedPacketBytes),t=0;this.packetArray.forEach((r=>{e.set(r.uint8ArrayPayload,t),t+=r.payloadLength})),this.packetArray=[],this.arrayBufferData=e.buffer,this.dataByteLength=this.reveivedPacketBytes,o.a.debug(`[ProtoSlice]  [_combinePacketToSlice]. sn : ${this.sn}, level: ${this.level}, sliceByteLength : ${this.dataByteLength}`),this.calledCallback=!0,this.combineCallback(this.id)}segmentSliceToPacket(){for(var e=[],t=n.a.DCChunkSize,r=Math.ceil(this.dataByteLength/t),i=this.dataByteLength,a=this.arrayBufferData,o=0;o<r;o+=1){var h=o*t,l=i-o*t>t?(o+1)*t:i,c=a.slice(h,l),u=new s.a;u.sn=this.sn,u.level=this.level,u.sliceIndex=this.index,u.packetIndex=o,u.totalPacket=r,u.uint8ArrayPayload=new Uint8Array(c),u.payloadLength=u.uint8ArrayPayload.length,e.push(u.marshall())}return e}}},function(e,t,r){var i=r(59),n=r(60),s=r(61),a=r(63);e.exports=function(e,t){return i(e)||n(e,t)||s(e,t)||a()},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,r){e.exports=r(64)},function(e,t,r){"use strict";var i=class{constructor(e,t){this.id=null,this.events={},this.uri=(document.location.protocol,`wss://${e}`),this.options=t,this.reconnect=0,this.timer=null,this.initWebSocket()}initWebSocket(){WebSocket&&(this.reconnect+=1,this._ws=new WebSocket(this.uri),this._ws.onopen=this.onopen.bind(this),this._ws.onmessage=this.onmessage.bind(this),this._ws.onclose=this.onclose.bind(this),this._ws.onerror=this.onerror.bind(this))}get readyState(){return this._ws?this._ws.readyState:4}on(e,t){"connect"!==e&&"message"!==e&&"open"!==e&&"error"!==e||(this.events[e]=t)}emit(e,t){"message"===e&&this._ws&&1===this._ws.readyState&&this._ws.send(`S ${t.to} ${JSON.stringify(t)}`)}disconnect(){this.timer&&(clearTimeout(this.timer),this.timer=null),this.options.reconnection=!1,this._ws.close()}onopen(e){this.reconnect=0,this.events.open()}onmessage(e){var t=e.data.substring(0,1);if("C"===t)this.id=e.data.substring(2),this.events.connect();else if("S"===t){var r=JSON.parse(e.data.substring(29));this.events.message(r)}}onerror(e){this.events.error(e.toString())}onclose(e){this.options.reconnection&&this.options.reconnectionAttempts>this.reconnect&&(this.timer=setTimeout((()=>{this.initWebSocket()}),this.options.reconnectionDelay+1e3*this.reconnect))}};var n=class{constructor(e,t){this._socket=new i(e.signalServer,{reconnection:!0,reconnectionAttempts:10,reconnectionDelay:2e3,randomizationFactor:.5,timeout:2e4,autoConnect:!0}),this.callback=t,this._initListening()}destroy(){this._disconnect(),this._socket=null,this.callback=null}on(e,t){this._socket.on(e,t)}emit(e,t){this._socket.emit(e,t)}_disconnect(){return this._socket.disconnect()}get socketId(){return this._socket.id}get connection(){return this._socket}set connection(e){this._socket=e}get logTime(){var e=new Date;return`${e.getHours()}:${e.getMinutes()}:${e.getSeconds()}.${e.getMilliseconds()}`}_initListening(){var e=this._socket;e.on("connect",(()=>{this.callback&&this.callback({event:"connect"})})),e.on("open",(()=>{this.callback&&this.callback({event:"open"})})),e.on("error",(e=>{this.callback&&this.callback({event:"error",message:e})})),e.on("connect_timeout",(e=>{})),e.on("disconnect",(e=>{})),e.on("reconnect",(e=>{})),e.on("reconnect_attempt",(e=>{})),e.on("reconnecting",(e=>{})),e.on("reconnect_error",(e=>{})),e.on("reconnect_failed",(()=>{})),e.on("ping",(()=>{})),e.on("pong",(e=>{}))}},s=r(3),a=r(1),o=r(14),h=r(0),l=r(7);var c=class{constructor(e){this.TAG="SignalClient",this.hlsp2p=e,o.a.registerModule(this.TAG,this.reportCallback.bind(this)),this.signalSending=this.onSignalSending.bind(this),this.hlsp2p.on(s.a.SIGNAL_SENDING,this.signalSending),this.config=a.a,this._socketIOConnection=new n({signalServer:this.config.signalServer},this.onEvents.bind(this)),this._socketIOConnection.on("message",(e=>{this.hlsp2p.trigger(s.a.SIGNAL_RECEIVING,e)}))}reportCallback(){var e={};return e[h.a.WS_STATE]=this._socketIOConnection.connection.readyState,e}onSignalSending(e){this.send(e)}destroy(){this._socketIOConnection.destroy(),this.hlsp2p.off(s.a.SIGNAL_SENDING,this.signalSending),this.hlsp2p=null}onEvents(e){var t=e.event,r=e.message;switch(t){case"connect":l.a.info("[signal] connect"),this.hlsp2p.pid=this._socketIOConnection.socketId,this.hlsp2p.trigger(s.a.SIGNAL_READY,{pid:this._socketIOConnection.socketId});break;case"open":l.a.info("[signal] open");break;case"error":l.a.error(`[signal] error: ${r}`)}}send(e){this._socketIOConnection.emit("message",e)}},u="offer",d="answer",p="iceCandidate",f="justStart",g="connReject",_="open",v="close",m="error",y="bufferAmountLow",b="report",T=r(4);var C=class{constructor(e,t,r){this.TAG=`DataChannel_${t.label}`,this.callbacks=r,this.config=t,this.dataChannel=e.createDataChannel(t.label,t),this.dataChannel.binaryType="arraybuffer",this._bindHandler(),this._bufferedAmountLimit=t.bufferedAmountLimit}destroy(){this._removeHandler(),this.dataChannel.close(),this.dataChannel=null,this.callbacks=null}_bindHandler(){var e=this.dataChannel;e.onopen=this.onOpen.bind(this),e.onerror=this.onError.bind(this),e.onclose=this.onClose.bind(this),e.onmessage=this.onMessage.bind(this),e.onbufferedamountlow&&(e.onbufferedamountlow=this.onBufferedAmountLow.bind(this))}_removeHandler(){var e=this.dataChannel;e.onopen=null,e.onerror=null,e.onclose=null,e.onmessage=null,e.onbufferedamountlow=null}send(e){var t=this.dataChannel;t&&"open"===t.readyState&&t.bufferedAmount<=this._bufferedAmountLimit&&t.send(e)}onMessage(e){this.callbacks&&this.callbacks.onDataChannelMessage(e.data)}onOpen(){T.b.log(`${this.TAG}, Data channel open, id:${this.id}`),this.callbacks&&this.callbacks.onDataChannelStateChange({state:_})}onClose(){T.b.log(`${this.TAG}, Data channel close`),this.callbacks&&this.callbacks.onDataChannelStateChange({state:v})}onError(e){var t=e.error;T.b.error(`${this.TAG}, Data channel error, id:${this.id} ${t.message}`),this.callbacks&&this.callbacks.onDataChannelStateChange({state:m})}onBufferedAmountLow(){this.callbacks.onDataChannelStateChange({state:y})}get readyState(){return this.dataChannel.readyState}get bufferedAmount(){return this.dataChannel.bufferedAmount}get label(){return this.dataChannel.label}get id(){return this.dataChannel.id}get bufferedAmountLowThreshold(){return this.dataChannel.bufferedAmountLowThreshold}};var R=class{constructor(e,t,r){var i=e.id;this.TAG=`WebRTCPeerConnection_${i}`,this.localId=t.localId,this.remoteId=t.remoteId,this.id=i,this.callbacks=r,this._rtcPeerConnection=new RTCPeerConnection(t.ice),this._bindHandler();var n={label:`${this.remoteId}_${this.id}`,id:0,negotiated:!0,bufferedAmountLimit:t.bufferedAmountLimit},s={onDataChannelMessage:this.onDataChannelMessage.bind(this),onDataChannelStateChange:this.onDataChannelStateChange.bind(this)};this._dataChannel=new C(this._rtcPeerConnection,n,s)}destroy(){this._dataChannel.destroy(),this._dataChannel=null,this._rtcPeerConnection.close(),this._rtcPeerConnection=null,this.callbacks=null}send(e){this._dataChannel.send(e)}_bindHandler(){this._rtcPeerConnection.onicecandidate=this.onIceCandidate.bind(this),this._rtcPeerConnection.oniceconnectionstatechange=this.onIceConnectionStateChange.bind(this),this._rtcPeerConnection.onsignalingstatechange=this.onSignalingStateChange.bind(this)}receiveSignal(e){switch(e.type){case f:this._createOffer();break;case u:this._receiveOffer(e.msg);break;case d:this._receiveAnswer(e.msg);break;case p:this._receiveCandidate(e.msg)}}onIceCandidate(e){if(this.callbacks){if(!e||!e.candidate)return;this.callbacks.onSendBySignalChannel({type:p,msg:e.candidate,id:this.id})}}onIceConnectionStateChange(e){this.callbacks&&this.callbacks.onStateChange({context:"peerConnection",type:"IceConnectionState",state:e.target.iceConnectionState})}onSignalingStateChange(){this.callbacks&&this.callbacks.onStateChange({context:"peerConnection",type:"SignalingState",state:this._rtcPeerConnection.signalingState})}onDataChannelMessage(e){this.callbacks&&this.callbacks.onMessage(e)}onDataChannelStateChange(e){this.callbacks&&this.callbacks.onStateChange({context:"dataChannel",type:"State",state:e.state,originData:e})}_receiveOffer(e){this._receiveDescription(e,(()=>{this._createAnswer()}))}_receiveAnswer(e){this._receiveDescription(e)}_receiveCandidate(e){var t;try{t=new RTCIceCandidate(e)}catch(e){return}this._rtcPeerConnection.addIceCandidate(t).then((()=>{})).catch((e=>{}))}_createAnswer(){this._rtcPeerConnection.createAnswer().then((e=>this._rtcPeerConnection.setLocalDescription(e))).then((()=>{this.callbacks&&this.callbacks.onSendBySignalChannel({type:d,msg:this._rtcPeerConnection.localDescription,id:this.id})})).catch((e=>{}))}_receiveDescription(e,t){this._rtcPeerConnection.setRemoteDescription(new RTCSessionDescription(e)).then((e=>{t&&t()})).catch((e=>{t&&t()}))}_createOffer(){this._rtcPeerConnection.createOffer().then((e=>this._rtcPeerConnection.setLocalDescription(e))).then((()=>{this.callbacks&&this.callbacks.onSendBySignalChannel({type:u,msg:this._rtcPeerConnection.localDescription,streamId:this._dataChannel.id,id:this.id})})).catch((e=>{}))}get iceGatheringState(){return this._rtcPeerConnection.iceGatheringState}get open(){return"open"===this._dataChannel.readyState}},E="fail",w="open",S="ice",P="report",I="INIT_TIMEOUT",L="HEART_TIMEOUT",k="DC_CLOSED",A="DC_ERROR",D=r(9),N=r(5);var O=class{constructor(){this.uri=N.a.PING,this.time=0,this.sendBytes=0,this.receiveBytes=0}init(e){this.time=e.time,this.sendBytes=e.sendBytes,this.receiveBytes=e.receiveBytes}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt32(this.time),e.pushUInt32(this.sendBytes),e.pushUInt32(this.receiveBytes),e.marshall()}unmarshall(e){this.time=e.popUInt32(),this.sendBytes=e.popUInt32(),this.receiveBytes=e.popUInt32()}};var x=class{constructor(e,t,r){this.alpha=e,this._sendBytesAvg=t,this._receiveBytesAvg=r}sendBytesAvg(e){this._sendBytesAvg=e*(1-this.alpha)+this.alpha*this._sendBytesAvg}receiveBytesAvg(e){this._receiveBytesAvg=e*(1-this.alpha)+this.alpha*this._receiveBytesAvg}getSendBytesAvg(){return this._sendBytesAvg}getReceiveBytesAvg(){return this._receiveBytesAvg}getBytesAvg(){return(this._receiveBytesAvg+this._sendBytesAvg)/2}};var U=class{constructor(){this.uri=N.a.SLICE_REQUEST,this.ReqSn=0,this.ReqLevel=0,this.idx=0}init(e){this.ReqSn=e.ReqSn,this.ReqLevel=e.ReqLevel,this.idx=e.idx}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt32(this.ReqSn),e.pushUInt32(this.ReqLevel),e.pushUInt32(this.idx),e.marshall()}unmarshall(e){this.ReqSn=e.popUInt32(),this.ReqLevel=e.popUInt32(),this.idx=e.popUInt32()}};var B=class{constructor(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.view=e,this.pos=0,this.len=0,this.uri=0,this.resCode=0,!0===t&&(this.len=this.popUInt32(),this.uri=this.popUInt32(),this.resCode=this.popUInt16())}bytesAvailable(){return this.view.byteLength-this.pos}popBool(){return 1===this.popUInt8()}popUInt8(){if(this.pos+1>this.view.byteLength)return 0;var e=this.view.getUint8(this.pos);return this.pos+=1,e}popUInt16(){if(this.pos+2>this.view.byteLength)return 0;var e=this.view.getUint16(this.pos,!0);return this.pos+=2,e}popUInt32(){if(this.pos+4>this.view.byteLength)return 0;var e=this.view.getUint32(this.pos,!0);return this.pos+=4,e}popUInt64(){if(this.pos+8>this.view.byteLength)return 0;var e=this.view.getUint32(this.pos,!0);this.pos+=4;var t=this.view.getUint32(this.pos,!0);return this.pos+=4,4294967296*t+e}popUint8Array(){var e=this.popUInt16();if(this.pos+e>this.view.byteLength)return null;var t=new Uint8Array(this.view.buffer,this.pos,e);return this.pos+=e,t.slice()}popUint8Array32(){var e=this.popUInt32();if(this.pos+e>this.view.byteLength)return null;var t=new Uint8Array(this.view.buffer,this.pos,e);return this.pos+=e,t.slice()}popUInt32Vector(){var e=this.popUInt32();if(this.pos+4*e>this.view.byteLength)return null;for(var t=[],r=0;r<e;r+=1)t.push(this.popUInt32());return t}popUInt16Vector(){var e=this.popUInt32();if(this.pos+2*e>this.view.byteLength)return null;for(var t=[],r=0;r<e;r+=1)t.push(this.popUInt16());return t}popString(){var e=this.popUInt16();if(this.pos+e>this.view.byteLength)return null;for(var t=[],r=0;r<e;r+=1)t[r]=String.fromCharCode(this.popUInt8());return t.join("")}},M=r(22),q=r(35);var $=class{constructor(){this.uri=N.a.BITMAP,this.curLevel=255,this.bufferInfo=[]}add(e){this.bufferInfo.push(e)}hasSlice(e){for(var t=0;t<this.bufferInfo.length;t+=1)if(this.bufferInfo[t].sn===e.ReqSn&&this.bufferInfo[t].level===e.ReqLevel&&-1!==this.bufferInfo[t].subIndexes.indexOf(e.idx))return!0;return!1}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt8(this.curLevel),e.pushUInt16(this.bufferInfo.length),this.bufferInfo.forEach((t=>{t.marshall(e)})),e.marshall()}unmarshall(e){this.curLevel=e.popUInt8();for(var t=e.popUInt16(),r=0;r<t;r+=1){var i=new q.a;i.unmarshall(e),this.bufferInfo.push(i)}}},F=r(34),G=r(28),H=r(10),j=r(6),Y=r.n(j),V=r(2),K=r(12);var z=class{constructor(){this.fragMap=new Map,this._timer=null}init(){this._setTimer()}destroy(){this._clearTimer(),this.fragMap.clear()}addPacket(e){var t=new F.a;t.unmarshall(e);var r=`${t.sn}-${t.level}-${t.sliceIndex}`;if(Y.a.debug(`[PacketMgr] [addPacket] ${r}`),H.a.getSlices(t.sn,t.level,t.sliceIndex))return Y.a.debug(`[PacketMgr] [addPacket] ${r} slice已完整, return`),void this.fragMap.delete(r);var i=this.fragMap.get(r);i||((i=new G.a(t.sn,t.level,t.sliceIndex)).id=r,i.packetAmount=t.totalPacket,i.combineCallback=this.combineFinish.bind(this),this.fragMap.set(r,i)),i.addPacket(t),V.a.p2pEstimate&&V.a.p2pEstimate.addPacket(r,{totalPacket:t.totalPacket})}static genPacketsFromSlice(e,t,r){var i=H.a.getSlices(e,t,r);if(i){var n=i.segmentSliceToPacket();return Y.a.debug(`切割slice sn ${e}, level ${t} index ${r}  packetsLen: ${n.length}`),n}return!1}combineFinish(e){var t=this.fragMap.get(e);H.a.onReceiveSlice({id:e,index:t.index,slice:t}),this.fragMap.delete(e),K.a.delete(e),Y.a.debug(`P2P接收完全, taskScheduler任务队列删除${e}`),K.a.tick(),V.a.p2pEstimate&&V.a.p2pEstimate.delete(e)}_clearOutDateFrag(){this.fragMap.forEach(((e,t)=>{Date.now()-e.createTime>12e4&&this.fragMap.delete(t)}))}_setTimer(){this._clearTimer(),this._timer=setInterval(this._clearOutDateFrag.bind(this),3e4)}_clearTimer(){this._timer&&(clearInterval(this._timer),this._timer=null)}};var X=class{constructor(){this.uri=N.a.PONG,this.time=0}init(e){this.time=e.time}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt32(this.time),e.marshall()}unmarshall(e){this.time=e.popUInt32()}};var W=class{constructor(e,t,r){this.TAG="PeerConnection",this.hlsp2p=e,this.passtive=null,this.localId=t.localId,this.remoteId=t.remoteId,this.callbacks=r,this._receiveBytes=0,this._sendBytes=0,this.nodeInfo=null,this.rtt=100,this._wa=new x(a.a.peerConnWaWeight,a.a.peerConnWaInitSendBytes,a.a.peerConnWaInitReceiveBytes),this._initConnTimer=setTimeout(this._checkInitConnected.bind(this),a.a.initConnTimeout),this.pcGroup=this.createPC(),this.sendIndex=0,this.totalPCNum=this.pcGroup.length}createPC(){var e=[],t={localId:this.localId,remoteId:this.remoteId,ice:{iceServers:[]},bufferedAmountLimit:a.a.bufferedAmountLimit};a.a.stunServer&&t.ice.iceServers.push({urls:`stun:${a.a.stunServer}`});var r=new R({id:0},t,{onMessage:this.onMessage.bind(this),onStateChange:this.onStateChange.bind(this),onSendBySignalChannel:this.onSendBySignalChannel.bind(this)});if(e.push(r),a.a.enableExtraPCCnt)for(var i=0;i<a.a.enableExtraPCCnt;i++){var n=new R({id:i+1},t,{onMessage:this.onMessage.bind(this),onStateChange:()=>{},onSendBySignalChannel:this.onSendBySignalChannel.bind(this)});e.push(n)}return e}destroy(){this._clearTimer(),this._clearInitConnTimer(),this.pcGroup.forEach((e=>{e.destroy()})),this.pcGroup=[],this.hlsp2p=null,this.passtive=null,this._wa=null,this.bitmap=null,this.router=null,this.nodeInfo=null}get score(){return this.nodeInfo?this.nodeInfo.score():this._wa?this._wa.getBytesAvg():0}get numOfOpeningPC(){return this.pcGroup.filter((e=>e.open)).length}setStats(e){this.p2pStats=e}setRouter(e){this.router=e}onStateChange(e){var t=e.context;if("peerConnection"===t){var r=e.type;"IceConnectionState"===r&&this.callbacks.onPeerConnectionState({type:S,state:e.state})}else if("dataChannel"===t){switch(e.state){case b:this.callbacks.onPeerConnectionState({remoteId:this.remoteId,type:P,reason:"report",originData:e.originData});break;case v:this.callbacks.onPeerConnectionState({remoteId:this.remoteId,type:E,reason:k});break;case m:this.callbacks.onPeerConnectionState({remoteId:this.remoteId,type:E,reason:A});break;case _:this.callbacks.onPeerConnectionState({remoteId:this.remoteId,type:w,reason:"dataChannelOpen"}),this._clearInitConnTimer(),this._sendHeart(),this._startHeart(),a.a.enableLocalNetworkShare||a.a.enableLLS||(this._exchangeBitmap(),this._startBitmap())}}}receiveSignal(e){var t=e.type,r=e.pcId,i=this.pcGroup[r];switch(t){case d:i.receiveSignal({type:d,msg:e.payload});break;case u:this.passtive=!0,i.receiveSignal({type:u,msg:e.payload});break;case p:i.receiveSignal({type:p,msg:e.payload});break;default:this.passtive=!1,this.pcGroup.forEach((e=>{e.receiveSignal({type:f})}))}}onSendBySignalChannel(e){var t;switch(e.type){case u:t={from:this.localId,to:this.remoteId,type:u,payload:e.msg,pcId:e.id},this._sendBySignaling(t);break;case p:t={from:this.localId,to:this.remoteId,type:p,payload:e.msg,pcId:e.id},this._sendBySignaling(t);break;case d:t={from:this.localId,to:this.remoteId,type:d,payload:e.msg,pcId:e.id},this._sendBySignaling(t)}}_sendBySignaling(e){this.callbacks.onSignaling(e)}sendSliceRequest(e){Y.a.debug(`${this.TAG} 发送slice请求 ${e.ReqSn}`);var t=new U;t.init(e),this.send(t.marshall())}_sendHeart(){var e=new O;this._lastPingTime=Math.floor(performance.now()),e.init({time:this._lastPingTime,receiveBytes:this._receiveBytes,sendBytes:this._sendBytes}),this.send(e.marshall())}_startHeart(){this._heartTimer&&(clearInterval(this._heartTimer),this._heartTimer=null),this._lastSyncTime=M.a.timestamp(),this.sendHeart=this._sendHeart.bind(this),this._heartTimer=setInterval(this.sendHeart,a.a.PCHeartInterval),this._checkAliveTimer=setInterval(this._checkAlive.bind(this),a.a.PCCheckAliveInterval)}_exchangeBitmap(){this.send(Ft.bitmapMgr.getBitmap([]))}_startBitmap(){this._bitmapTimer&&(clearInterval(this._bitmapTimer),this._bitmapTimer=null),this.exchangeBitmap=this._exchangeBitmap.bind(this),this._bitmapTimer=setInterval(this.exchangeBitmap,a.a.bitmapInterval)}_clearTimer(){this._heartTimer&&(clearInterval(this._heartTimer),this._heartTimer=null),this._checkAliveTimer&&(clearInterval(this._checkAliveTimer),this._checkAliveTimer=null),this._bitmapTimer&&(clearInterval(this._bitmapTimer),this._bitmapTimer=null)}onMessage(e){var t=new DataView(e),r=new B(t);if(a.a.enableLocalNetworkShare)switch(r.uri){case N.a.PING:this._onPing(r);break;case N.a.PONG:this._onPong(r);break;case N.a.SLICE:this._receiveBytes+=r.len;break;case N.a.SLICE_REQUEST:this._onSliceRequest(r);break;default:this.router.onRecv(this.remoteId,r)}else if(a.a.enableLLS)switch(r.uri){case N.a.PING:this._onPing(r);break;case N.a.PONG:this._onPong(r);break;default:this.router.onRecv(this.remoteId,r)}else switch(r.uri){case N.a.PING:this._onPing(r);break;case N.a.PONG:this._onPong(r);break;case N.a.BITMAP:this._onBitmap(r);break;case N.a.SLICE:this._receiveBytes+=r.len;break;case N.a.SLICE_REQUEST:this._onSliceRequest(r);break;case N.a.PACKET:this._onPacket(r)}}_onPacket(e){Ft.packetMgr.addPacket(e)}_onSliceRequest(e){var t=new U;t.unmarshall(e),this._sendPacket(t)}_sendPacket(e){var t=z.genPacketsFromSlice(e.ReqSn,e.ReqLevel,e.idx);t&&t.forEach(((e,t)=>{this.send(e)}))}_onPing(e){var t=new O;t.unmarshall(e),this._lastSyncTime=M.a.timestamp(),this._wa.sendBytesAvg(t.receiveBytes),this._wa.receiveBytesAvg(this._receiveBytes),this._receiveBytes=0,this._sendPong(t.time)}_sendPong(e){var t=new X;t.time=e,this.send(t.marshall())}_onPong(e){var t=new X;t.unmarshall(e),this._lastPingTime===t.time&&(this.rtt=Math.floor(performance.now()-t.time))}_onBitmap(e){var t=new $;t.unmarshall(e),this.bitmap=t}send(e){if(this.p2pStats&&this.p2pStats.addUploadBytes(e.byteLength),e[4]===N.a.P2P_RES_CHUNK){var t=this.sendIndex%this.totalPCNum;this.sendIndex+=1;var r=this.pcGroup[t];r.open||(r=this.pcGroup[0]),r.send(e),this.sendIndex>1e7&&(this.sendIndex=0)}else this.pcGroup[0].send(e)}_checkAlive(){M.a.timestamp()-this._lastSyncTime>a.a.PCHeartTimeout&&this.callbacks.onPeerConnectionState({remoteId:this.remoteId,type:E,reason:L})}_checkInitConnected(){this.callbacks.onPeerConnectionState({remoteId:this.remoteId,type:E,reason:I})}_clearInitConnTimer(){this._initConnTimer&&(clearTimeout(this._initConnTimer),this._initConnTimer=null)}};class Q{constructor(){this.cache=new Set}destroy(){this.clear()}next(){if(!this.cache.size)return null;var e=this.cache.entries().next();return e.done?null:e.value[0]}removeNext(){var e=this.next();return null===e?null:(this.cache.delete(e),e)}clear(){this.cache.clear()}has(e){return this.cache.has(e)}add(e){return this.cache.add(e)}delete(e){return this.cache.delete(e)}get size(){return this.cache.size}}var J=new class{constructor(){this.TAG="TrackerController",this.hlsp2p=null,this.peerCache=null,this._timer=null,this._trackerConnected=!1,this._preTrackerId=""}init(e){this.hlsp2p=e,this.onTrackerLoaded=this._onTrackerLoaded.bind(this),this.onTrackerStarting=this._onTrackerStarting.bind(this),this.onRequestTracker=this._onRequestTracker.bind(this),this.hlsp2p.on(s.a.TRACKER_LOADED,this.onTrackerLoaded),this.hlsp2p.on(s.a.TRACKER_STARTING,this.onTrackerStarting),this.peerCache=new Q}destroy(){this.peerCache.destroy(),this.hlsp2p.off(s.a.TRACKER_STARTING,this.onTrackerStarting),this.hlsp2p.off(s.a.TRACKER_LOADED,this.onTrackerLoaded),this.hlsp2p=null,this._clearTimer()}_clearTimer(){this._timer&&(clearInterval(this._timer),this._timer=null)}nextPeer(){return this.peerCache.removeNext()}_onTrackerLoaded(e){for(var t=e.Data,r=e.trackerChannelId;this.peerCache.size>a.a.maxPeerCache;)this.peerCache.removeNext();this._preTrackerId&&this._preTrackerId!==r&&(this._preTrackerId=r,this.peerCache.clear());try{var i=JSON.parse(t);if(0===i.ret||"0"===i.ret)this._trackerConnected=!0,i.peers.forEach((e=>{var t=e.pid||e;this.hlsp2p.trigger("find_peer",{pid:t}),this.peerCache.has(t)||this.peerCache.add(t)}))}catch(e){}}_onTrackerStarting(){this._timer=setInterval(this.onRequestTracker,a.a.trackerInterval),this._onRequestTracker()}_onRequestTracker(){this.hlsp2p.trigger(s.a.TRACKER_LOADING)}get trackerConnected(){return this._trackerConnected}};var Z=class{constructor(e){this.TAG="PeerManager",this.hlsp2p=e}localPid(){return this.hlsp2p.localPeerId}init(){o.a.registerModule(this.TAG,this.reportCallback.bind(this)),this._connecting=new Map,this._connected=new Map,this.checkPeers=this._checkPeers.bind(this),this._checkPeerTimer=setInterval(this.checkPeers,a.a.checkPeerInterval),this._initConnect=!0,this.initReportData()}destroy(){this._connecting.forEach((e=>{e.destroy()})),this._connecting.clear(),this._connecting=null,this._connected.forEach((e=>{e.destroy()})),this._connected.clear(),this._connected=null,this._clearTimer(),this.hlsp2p=null,this.router=null,this.transport=null}setRouter(e){return this.router=e,this}setTransport(e){return this.transport=e,this}setP2PStats(e){this.p2pStats=e}get peerConnected(){return this._connected.size}getConnectedPeers(){return this._connected}getConnectedPeer(e){return this._connected.get(e)}getCandidatesPid(e){e.id;return[...this._connected.keys()]}getComPeer(e){return this._connected.get(e)}_clearTimer(){this._checkPeerTimer&&(clearInterval(this._checkPeerTimer),this._checkPeerTimer=null)}receiveSignal(e){l.a.log(`[p2p] [接收信令] ${JSON.stringify(e)}`);var t=e.type,r=e.from,i=e.to;if(t===g)return this._reportData[h.a.CONN_REJECT]+=1,void(this._reportData[h.a.CONN_REJECT_TOTAL]+=1);if(t===u&&this._connecting.has(r)&&a.a.fixPCConflict){if(i>r)return void(this._reportData[h.a.CONN_CONFLICT_IGNORE]+=1);var n=this._connecting.get(r);this._connecting.delete(r),n.destroy(),this._reportData[h.a.CONN_CONFLICT_RESET]+=1,this.createPeerConnection({localId:i,remoteId:r})}if(t===u&&!this._checkPCExist(r)){if(!this.canConnect(e))return l.a.log(`[p2p] [receiveSignal] [reject] ${r}`),void this.sendSignaling({from:e.to,to:e.from,pcId:e.pcId,type:g,payload:""});this.createPeerConnection({localId:i,remoteId:r})}var o=this._connecting.get(r);o&&(this.hlsp2p.trigger(s.a.SIGNAL_RECEIVED,e),o.receiveSignal(e))}createPeerConnection(e){this._reportData[h.a.CONN_TRY]+=1,this._reportData[h.a.CONN_TRY_TOTAL]+=1;var t,r={onPeerConnectionMessage:this.onReceiveData.bind(this),onPeerConnectionState:this.onPeerConnectionState.bind(this),onSignaling:this.sendSignaling.bind(this)},i=e.localId,n=e.remoteId;try{t=new W(this.hlsp2p,{localId:i,remoteId:n},r),l.a.log(`[detect][createPeerConnection]?pid=${n}`),this._preparePC(t)}catch(e){l.a.error(` create PeerConnection Error for the reason ${e.reason}`)}return this._connecting.set(n,t),t}_preparePC(e){e.setStats(this.p2pStats),this.router&&e.setRouter(this.router)}canConnect(e){return this._connected.size<a.a.maxPCConnected&&this._connecting.size<a.a.maxPCConnecting}_PCConnected(e){this._reportData[h.a.CONN_SUC]+=1,this._reportData[h.a.CONN_SUC_TOTAL]+=1;var t=this._getReadyPCFromCache(e);l.a.log(`[detect][p2p][peerConnected]?pid=${e}`),t&&(this._connected.set(e,t),this.hlsp2p.trigger(s.a.PEER_CONNECTED,{pid:e}))}_getReadyPCFromCache(e){var t=this._connecting.get(e);return this._connecting.delete(e),t}_handlePCFail(e){switch(this._deletePC(e.remoteId),e.reason){case I:this._reportData[h.a.CONN_INIT_TIMEOUT]+=1,l.a.log(`[detect][connInitTimeout]?pid=${e.remoteId}`);break;case k:this._reportData[h.a.CONN_DC_CLOSED]+=1;break;case A:this._reportData[h.a.CONN_DC_ERR]+=1;break;case L:this._reportData[h.a.CONN_HTBT_TIMEOUT]+=1}}_deletePC(e){var t=this._connected.get(e);t&&(t.destroy(),this._connected.delete(e)),(t=this._connecting.get(e))&&(t.destroy(),this._connecting.delete(e)),this.hlsp2p.trigger(s.a.PEER_REMOVED,{pid:e})}onReceiveData(){throw Error("需要實現")}onPeerConnectionState(e){switch(e.type){case E:this._handlePCFail(e);break;case w:this._PCConnected(e.remoteId)}}sendSignaling(e){this.hlsp2p.trigger(s.a.SIGNAL_BEFORE_SENDING,e),this.hlsp2p.trigger(s.a.SIGNAL_SENDING,e)}_checkPCExist(e){return this._connecting.has(e)||this._connected.has(e)}keepEnoughPeers(){var e=0,t=this.hlsp2p.localPeerId;if(!this.canConnect())return!0;this._initConnect&&(this._initConnect=!1);for(var r=this._initConnect?a.a.maxPCConnected:a.a.maxConnectRequestEachPeriod;e<r;){var i=this._getNextPeerForConnecting();if(!i)return!1;if(!this._checkPCExist(i)){var n=this.createPeerConnection({localId:t,remoteId:i});n&&n.receiveSignal({type:"init"})}e+=1}return!1}_getNextPeerForConnecting(){return J.nextPeer()}kickUselessPeer(){var e;this._connected.size>a.a.minPCConnected&&this._connected.forEach(((t,r)=>{this._connected.get(e)?this._connected.get(e).score>t.score&&(e=r):e=r})),e&&this._kickPeer(e)}_kickPeer(e){this._reportData[h.a.CONN_KICK]+=1,this._deletePC(e)}_checkPeers(){this.keepEnoughPeers(),this.kickUselessPeer()}_allPcRtt(){var e=this._connected,t=[];return e.forEach((e=>{e&&e.rtt&&(l.a.log(`[p2p] [rtt] local: ${this.localPid()} remote: ${e.remoteId}, ${e.rtt}`),t.push(e.rtt))})),t}initReportData(){this._reportData={[h.a.CONNECTED]:0,[h.a.CONN_TRY]:0,[h.a.CONN_SUC]:0,[h.a.CONN_TRY_TOTAL]:0,[h.a.CONN_SUC_TOTAL]:0,[h.a.CONN_REJECT]:0,[h.a.CONN_REJECT_TOTAL]:0,[h.a.CONN_KICK]:0,[h.a.CONN_DC_CLOSED]:0,[h.a.CONN_DC_ERR]:0,[h.a.CONN_PC_OPEN_CNT]:[],[h.a.CONN_HTBT_TIMEOUT]:0,[h.a.CONN_INIT_TIMEOUT]:0,[h.a.CONN_CONFLICT_IGNORE]:0,[h.a.CONN_CONFLICT_RESET]:0,[h.a.PEER_RTT]:this._allPcRtt()}}reportCallback(){var e=this._reportData;return e[h.a.CONNECTED]=this._connected.size,this.initReportData(),this._reportData[h.a.CONN_TRY_TOTAL]=e[h.a.CONN_TRY_TOTAL],this._reportData[h.a.CONN_SUC_TOTAL]=e[h.a.CONN_SUC_TOTAL],this._reportData[h.a.CONN_REJECT_TOTAL]=e[h.a.CONN_REJECT_TOTAL],this._connected.forEach((e=>{this._reportData[h.a.CONN_PC_OPEN_CNT].push(e.numOfOpeningPC)})),e}},ee=r(16),te=r(13);class re{constructor(e){var t=e.ReqSn,r=e.ReqLevel,i=e.idx;this.ReqSn=t,this.ReqLevel=r,this.idx=i}}var ie=class extends Z{constructor(e){super(e),this.TAG="VodPeerManager"}init(){super.init(),this.checkBuffer=this._checkBuffer.bind(this),this._checkBufferTimer=setInterval(this.checkBuffer,a.a.checkBufferInterval),this.requestSn=new Set}destroy(){super.destroy(),this._checkBufferTimer&&(clearInterval(this._checkBufferTimer),this._checkBufferTimer=null)}_checkBuffer(){if(K.a.size())Y.a.debug(`${this.TAG} 存在cdn请求, 不进行P2P请求, 返回`);else{if(0===this._connected.size)return!1;var e=this._findUnfilledSlice();if(e.length){Y.a.debug("P2P检查缺片",e);var t=this._filterExistP2PReq(e);this._sendSliceRequest(t)}}}_filterExistP2PReq(e){var t=[];e.forEach((e=>{this.requestSn.has(e.ReqSn)?Y.a.debug(`sn ${e.ReqSn} 已经请求过, 忽略P2P请求`):t.push(e)}));var r=e[0].ReqSn;return this.requestSn.forEach((e=>{e<r-20&&this.requestSn.delete(e)})),t}_sendSliceRequest(e){var t=[...this._connected.keys()];if(t.length)for(;e.length;){var r=e.shift(),i=this._findPeerForRequestingSlice(t,r);i&&(this.requestSn.add(r.ReqSn),this._connected.get(i).sendSliceRequest(r))}}_findPeerForRequestingSlice(e,t){for(var r=0;r<e.length;r++){var i=e[r];if(this._ensurePeerHasSlice(i,t))return e.push(e.splice(r,1)[0]),i}return null}_ensurePeerHasSlice(e,t){var r=this._connected.get(e);if(!r)return!1;var i=r.bitmap;return!!i&&i.hasSlice(t)}_findUnfilledSlice(){var e=V.a.curReqSn,t=V.a.curReqLevel;if(void 0===e||void 0===t)return!1;for(var r=e+a.a.p2pLimit,i=e+1,n=[];n.length<a.a.p2pSliceRequestCount&&i<=r&&ee.a.hasTS(t,i);){var s=H.a.get(new te.a({sn:i,level:t}).toStringId());if(s)for(var o=0;o<a.a.sliceCount;o+=1)!s.getSlice(o)&&n.length<a.a.p2pSliceRequestCount&&n.push(new re({ReqSn:i,ReqLevel:t,idx:o}));else for(var h=0;h<a.a.sliceCount;h+=1)n.length<a.a.p2pSliceRequestCount&&n.push(new re({ReqSn:i,ReqLevel:t,idx:h}));i+=1}return n}};var ne=class{constructor(){this.TAG="BitmapMgr"}init(){}destroy(){}getBitmap(e){if(!Array.isArray(e))throw Error(`${this.TAG}.getSpecifiedBitmap 参数需为数组`);var t=new $;return H.a.getBufferInfo(e).forEach((e=>{t.add(e)})),t.marshall()}},se=r(18),ae=r(8),oe=r(11),he=r(23);var le=new class{constructor(){this.TAG="TrackerLoader",this.trackerCount=null,this._loaderManager=new he.a,this._trackerChannelId=""}init(e){this._loaderManager.init(),this.hlsp2p=e,this.onTrackerLoading=this._onTrackerLoading.bind(this),this.hlsp2p.on(s.a.TRACKER_LOADING,this.onTrackerLoading),this.trackerCount=0}destroy(){this._loaderManager.destroy(),this.hlsp2p.off(s.a.TRACKER_LOADING,this.onTrackerLoading),this.hlsp2p=null,this.trackerCount=null}_onTrackerLoading(){var e=`tracker-${this.trackerCount}`,t=ae.a.GetRequestConfig(),r=Math.floor(oe.a.currentTime||0);if(a.a.channelId&&this.hlsp2p.localPeerId&&("LIVE"!==a.a.videoType||!oe.a.paused)){"LIVE"===a.a.videoType?(this._trackerChannelId=`h5_live_hls_${a.a.cloudAppId}_${a.a.levelForTracker}_${a.a.liveTrackerVersion}_${a.a.liveModel}_${a.a.channelId}`,t.url=`${a.a.liveTrackerServer}/htbt?channel=${this._trackerChannelId}&pid=${this.hlsp2p.localPeerId}&mode=bat`):(this._trackerChannelId=`h5_vod_hls_${a.a.cloudAppId}_${a.a.vodTrackerVersion}_${a.a.channelId}`,t.url=`${a.a.vodTrackerServer}/htbt?channel=${this._trackerChannelId}&pid=${this.hlsp2p.localPeerId}&maxpos=${r}`);var i=new se.a(e,t,this.receiveResp.bind(this));this._loaderManager.add(e,i),i.load()}}receiveResp(e,t,r){t===ae.b.SUCCESS&&this.hlsp2p.trigger(s.a.TRACKER_LOADED,{trackerChannelId:this._trackerChannelId,Data:r.data}),this._loaderManager.delete(e)}};var ce=new class{constructor(){this.TAG="ConfLoader",this.confCount=null,this._confOkTime=0,this._loaderManager=new he.a}init(e){this.hlsp2p=e,o.a.registerModule(this.TAG,this.reportCallback.bind(this)),this.onConfLoading=this._onConfLoading.bind(this),this.hlsp2p.on(s.a.CONF_LOADING,this.onConfLoading),this.confCount=0,this._loaderManager.init()}destroy(){this.hlsp2p.off(s.a.CONF_LOADING,this.onConfLoading),this._loaderManager.destroy(),this.hlsp2p=null,this.confCount=null}reportCallback(){var e={};return this._confOkTime&&(e[h.a.CONF_OK_TIME]=this._confOkTime,this._confOkTime=0),e}_onConfLoading(){var e=`conf-${this.confCount}`,t=ae.a.GetRequestConfig();t.url=`${a.a.confBaseUrl}${a.a.channelId}?domain=${a.a.domain}&timestamp=${parseInt(Date.now(),10)}`;var r=new se.a(e,t,this.receiveResp.bind(this));this._loaderManager.add(e,r),r.load()}receiveResp(e,t,r){t===ae.b.SUCCESS&&(this._confOkTime=Date.now()-a.a.loadTime,this.hlsp2p.trigger(s.a.CONF_LOADED,{Data:r.data})),this._loaderManager.delete(e)}},ue=r(37),de=r(27);class pe{static selectNearestTargetDuration(e,t){for(var r=0,i=0;i<e.length;i++){if(t<=e[i])return r;r+=1}return r}static selectPropValue(e,t){return e.length<t?0:e[t]}}class fe{destroy(){this.hlsp2p&&this.hlsp2p.off(s.a.LEVEL_LOADED,this.handler),this.hlsp2p=null}init(e){return this.handler||(this.handler=e=>{this._updateConfigByTargetDuration({targetDuration:e.targetDuration})},e.on(s.a.LEVEL_LOADED,this.handler),this.hlsp2p=e),this}setAutoConfigFields(e,t){this._autoConfigFields=e,this._autoConfigObj=t}_updateConfigByTargetDuration(e){var t=e.targetDuration;if(a.a.enableAutoConfig){var r=pe.selectNearestTargetDuration(a.a.targetDurationTemplate,t);r&&this._autoConfigFields.forEach((e=>{var t=pe.selectPropValue(this._autoConfigObj[e],r);t&&(a.a[e]=t)}))}}}class ge{static config(){a.a.preloadProbability=0,a.a.liveMaxBuffer=30,a.a.liveModel="lls"}}class _e{static config(){_e.updateTrackerVersionWithPublicIP()}static updateTrackerVersionWithPublicIP(){a.a.publicIP&&a.a.enableMultiArea&&l.a.log(`[detect]外网ip?ip=${a.a.publicIP}`)}static checkDomain(e){return!!e.configured}}class ve{static assignDomainConfig(e,t,r,i){var n=ve.getConfigByDomain(e,t);n&&ve.assignConfig(n,r,i)}static getConfigByDomain(e,t){var r=t.domain_conf;return r?r[e]:null}static assignConfig(e,t,r){r.forEach((r=>{var i=r[0],n=r[1];new Set(Object.keys(e)).has(n)&&(t[i]=e[n])}))}}class me{constructor(e){this.hlsp2p=e,this._timer=null}destroy(){this._clearTimer(),this.hlsp2p=null}tryToStartOnce(e){if(0!==e&&!this.started){T.b.log(`[PeriodConf] 启动 ${e}`);var t=Math.max(e,3e4);this._timer=setInterval((()=>{this._reqConf()}),t)}}get started(){return null!==this._timer}_reqConf(){this.hlsp2p.trigger(s.a.CONF_LOADING)}_clearTimer(){this._timer&&(clearInterval(this._timer),this._timer=null)}}var ye=new class{constructor(){this.TAG="ConfController"}init(e){this.hlsp2p=e,this.autoConfig=new fe,this.periodicConf=new me(e),this.onConfLoaded=this._onConfLoaded.bind(this),this.onConfStarting=this._onConfStarting.bind(this),this.onRequestConf=this._onRequestConf.bind(this),this.hlsp2p.on(s.a.CONF_LOADED,this.onConfLoaded),this.hlsp2p.on(s.a.CONF_STARTING,this.onConfStarting)}destroy(){this.periodicConf.destroy(),this.periodicConf=null,this.autoConfig.destroy(),this.autoConfig=null,this.hlsp2p.off(s.a.CONF_LOADED,this.onConfLoaded),this.hlsp2p.off(s.a.CONF_STARTING,this.onConfStarting),this.hlsp2p=null}_onConfLoaded(e){var t=[["peerCacheSize","peer_cache_size"],["trackerInterval","tracker_interval"],["signalServer","signal_server"],["liveTrackerServer","live_tracker_server"],["vodTrackerServer","vod_tracker_server"],["liveTrackerVersion","live_tracker_ver"],["vodTrackerVersion","vod_tracker_ver"],["reportServer","report_server"],["stunServer","h5_stun_server"],["PCHeartInterval","pc_heart_interval"],["maxPCConnecting","max_pc_connecting"],["maxPCConnected","max_pc_connected"],["sliceCount","slice_count"],["p2pSliceRequestCount","p2p_slice_req_cnt"],["checkBufferInterval","check_buffer_interval"],["minPCConnected","min_pc_connected"],["maxConnectRequestEachPeriod","max_connect_request_each_period"],["checkPeerInterval","check_peer_interval"],["peerConnWaWeight","peer_conn_wa_weight"],["bitmapInterval","bitmap_interval"],["p2pLimit","p2p_limit"],["preloadProbability","preload_probability"],["enableAegis","enable_aegis"],["aegisId","aegis_id"],["bufferCount","buffer_cnt"],["cdnBWRatio","cdn_bw_ratio"],["cdnCostRatio","cdn_cost_ratio"],["liveMaxBuffer","live_max_buffer"],["vodMaxBuffer","vod_max_buffer"],["p2pEstimateExtra","p2p_estimate_extra"],["cloudAppId","app_id"],["configured","configured"],["cdnNodeRatio","cdn_node_ratio"],["enableCDNDetailReport","en_cdn_detail_report"],["enableLLS","enable_lls"],["p2pReqTimeout","p2p_req_timeout"],["enableP2pAutoTimeout","enable_p2p_auto_timeout"],["cdnEstimateRatio","cdn_estimate_ratio"],["maxP2PDepth","max_p2p_depth"],["streamP2PChunkLength","stream_p2p_chunk_len"],["maxSubscribeSize","max_subscribe_size"],["syncNodeInterval","sync_node_interval"],["lowBufferThreshold","low_buf_threshold"],["maxP2PRetryTimes","max_p2p_retry_times"],["enableAutoConfig","enable_auto_config"],["llsAutoConfig","lls_auto_config"],["enableRange","enable_range"],["fixPCConflict","fix_pc_conflict"],["enableExtraPCCnt","enable_extra_pc_cnt"],["enableRTLog","enable_rtlog"],["rtLogLevel","rtlog_level"],["publicIP","ip"],["confInterval","conf_interval"]];try{var r=ue.a.decrypt(e.Data).pconf;if(t.forEach((e=>{var t=e[1],i=e[0];t in r&&(a.a[i]=r[t])})),r.p2p||r.emit_rollback)return l.a.warn("[hlsp2p] 配置下发回退命令, 触发 HLSP2P.Events.Rollback 事件"),window.console.warn("[hlsp2p] 配置下发回退命令, 触发 HLSP2P.Events.Rollback 事件"),V.a.exitReason="config_rollback",void this.hlsp2p.trigger(de.default.Events.Rollback,{reason:V.a.exitReason});if(a.a.confInterval&&this.periodicConf.tryToStartOnce(a.a.confInterval),ve.assignDomainConfig(a.a.cdnDomain,r,a.a,t),a.a.enableLocalNetworkShare){if(!_e.checkDomain(r))return l.a.warn("[hlsp2p] 未配置的的域名, 回退"),V.a.exitReason="unknown_domain",void this.hlsp2p.trigger(de.default.Events.Rollback,{reason:V.a.exitReason});a.a.enableLLS=!0,_e.config()}a.a.enableLLS&&ge.config(),a.a.enableAutoConfig&&(this.autoConfig.init(this.hlsp2p),this.autoConfig.setAutoConfigFields(["p2pReqTimeout"],a.a.llsAutoConfig));var i="LIVE"===a.a.videoType?a.a.liveMaxBuffer:a.a.vodMaxBuffer;V.a.curPlayerAdapter&&V.a.curPlayerAdapter.setMaxBufferLength(i),this.hlsp2p.trigger(s.a.CONF_PARSED)}catch(e){V.a.exitReason="conf_parse_error",this.hlsp2p.trigger(de.default.Events.Rollback,{reason:V.a.exitReason})}}_onConfStarting(){this.onRequestConf()}_onRequestConf(){this.hlsp2p.trigger(s.a.CONF_LOADING)}},be=r(19),Te=r(20),Ce=r(17);var Re=new class{constructor(){this.TAG="P2PPreload"}init(e){this.hlsp2p=e,this.endList=-1,this.hlsp2p.on(s.a.FRAGMENT_UPDATED,this.preload.bind(this)),o.a.registerModule(this.TAG,this.reportCallback.bind(this)),this.initReport()}destroy(){this.hlsp2p=null,this.endList=-1}preload(){if("VOD"!==a.a.videoType&&!(Math.random()>a.a.preloadProbability)){var e=V.a.curReqLevel,t=ee.a.levels[e];if(t){var r=t.endList;if(this.endList!==r){var i=ee.a.getTSInfo(e,r);if(i){var n={onSuccess:()=>{},onError:()=>{},onTimeout:()=>{}},s={maxRetry:0,maxRetryDelay:64e3,retryDelay:0,timeout:2e4},o={url:"",responseType:"arraybuffer",frag:{sn:void 0,level:void 0}};o.frag.sn=r,o.frag.level=e,o.url=i.url;var l=new be.a(o,s,n),c=new Te.a(o,s,n),u=l.id;c.id=u,Ce.a.loadInnerReq(u,l,c),this._reportData[h.a.P2P_PRE_CNT]+=1,this.endList=r}}}}}initReport(){this._reportData={[h.a.P2P_PRE_CNT]:0}}reportCallback(){var e=this._reportData;return this.initReport(),e}};class Ee{constructor(){this._callbacks=new Map}destroy(){this._callbacks.clear()}onRecv(e,t){var r=this._callbacks.get(t.uri);r&&r(e,t)}registerCb(e,t){this._callbacks.set(e,t)}}class we{destroy(){this.peerManager=null}setPeerManager(e){this.peerManager=e}sendBinary(e,t){var r=this.peerManager.getComPeer(e);r&&r.send(t)}}var Se=r(15),Pe=r.n(Se);class Ie{constructor(e){this.length=e||0,this.array=[],this.offset=0}push(e){this.array.push(e),this.length+=e.length}shift(e){var t=e;if(this.array.length<1)return new Uint8Array(0);if(this.offset+t===this.array[0].length){var r=this.array[0].slice(this.offset,this.offset+t);return this.offset=0,this.array.shift(),this.length-=t,r}if(this.offset+t<this.array[0].length){var i=this.array[0].slice(this.offset,this.offset+t);return this.offset+=t,this.length-=t,i}for(var n=new Uint8Array(t),s=0;this.array.length>0&&t>0;){if(this.offset+t<this.array[0].length){var a=this.array[0].slice(this.offset,this.offset+t);n.set(a,s),this.offset+=t,this.length-=t,t=0;break}var o=this.array[0].length-this.offset;n.set(this.array[0].slice(this.offset,this.array[0].length),s),this.array.shift(),this.offset=0,s+=o,this.length-=o,t-=o}return n}clear(){this.array=[],this.length=0,this.offset=0}shiftBuffer(){this.array.length>0&&(this.length-=this.array[0].length,this.array.shift(),this.offset=0)}toInt(e,t){for(var r=0,i=this.offset+e;i<this.offset+t+e;)i<this.array[0].length?r=256*r+this.array[0][i]:this.array[1]&&(r=256*r+this.array[1][i-this.array[0].length]),i+=1;return r}}class Le{constructor(){this.uri=N.a.P2P_RES_CHUNK,this.sn=0,this.level=0,this.seq=0,this.flag=0,this.payload=new Uint8Array(0)}set id(e){var t=te.a.fromId(e);this.sn=t.sn,this.level=t.level}get id(){return te.a.fromObj({sn:this.sn,level:this.level}).toStringId()}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt32(this.sn),e.pushUInt8(this.level),e.pushUInt16(this.seq),e.pushUInt8(this.flag),e.pushUint8Array(this.payload),e.marshall()}unmarshall(e){this.sn=e.popUInt32(),this.level=e.popUInt8(),this.seq=e.popUInt16(),this.flag=e.popUInt8(),this.payload=e.popUint8Array()}}var ke=1,Ae=4;class De extends Pe.a{constructor(e){var t=e.id,r=e.chunkLength,i=e.startByteOffset;super(),this.id=t,this._buffer=new Ie,this._currentSeq=0,this._targetChunkLength=r,this._startByteOffset=i,this.lastUpdateTime=Date.now(),this._updateSeqByStartByteOffset()}destroy(){super.removeAllListeners(),this._buffer.clear(),this._currentSeq=0,this.lastUpdateTime=0}_updateSeqByStartByteOffset(){this._currentSeq=this._startByteOffset/this._targetChunkLength}write(e){if(0!==e.byteLength)for(this._buffer.push(e),this.lastUpdateTime=Date.now();this._buffer.length>this._targetChunkLength;)this._createNewChunk()}flush(e){e&&this.write(e);for(var t=Math.ceil(this._buffer.length/this._targetChunkLength),r=0;r<t-1;r++)this._createNewChunk();this._createNewChunk(!0),this.emit("complete",{id:this.id})}_createNewChunk(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this._buffer.length<this._targetChunkLength&&!e)return null;var t=new Le,r=te.a.fromId(this.id);t.sn=r.sn,t.level=r.level,t.seq=this._currentSeq;var i=e?this._buffer.length:this._targetChunkLength;t.payload=this._buffer.shift(i),e&&(t.flag|=Ae),0===this._currentSeq&&(t.flag|=ke),this._currentSeq+=1,this.emit("new_chunk",t)}}var Ne=1,Oe=2;class xe extends Pe.a{constructor(e){var t=e.id;super(),this.id=t,this.chunks=new Map,this.endFlag=!1,this.endChunkSeq=0,this.totalLength=0,this.lastUpdateTime=Date.now(),this.stats={p2pBytes:0,cdnBytes:0,duplicateBytes:0}}destroy(){this.chunks.clear(),this.lastUpdateTime=0,super.removeAllListeners()}getChunks(){return this.chunks}getRanges(){for(var e={start:0,end:0},t=0;t<this.chunks.size&&this.chunks.has(t);t++)e.end+=this.chunks.get(t).payload.byteLength;return e}getRangeSeq(){for(var e={start:0,end:-1},t=0;t<this.chunks.size&&this.chunks.has(t);t++)e.end=t;return e}addChunk(e,t){this._statChunk(e,t),this.chunks.has(e.seq)||(this.lastUpdateTime=Date.now(),this.chunks.set(e.seq,e),this.totalLength+=e.payload.byteLength,e.flag&Ae&&(this.endFlag=!0,this.endChunkSeq=e.seq),this.emit("new_chunk",e,{totalLength:this.totalLength}),this.ifComplete()&&this.emit("complete",{id:this.id}))}_statChunk(e,t){var r=e.payload.byteLength;t===Oe&&(this.stats.cdnBytes+=r),this.chunks.has(e.seq)&&(this.stats.duplicateBytes+=r),this.chunks.has(e.seq)||t!==Ne||(this.stats.p2pBytes+=r)}ifComplete(){return this.endFlag&&this.chunks.size===this.endChunkSeq+1}almostComplete(e){return e.flag&Ae?this.chunks.size===e.seq:this.endFlag?this.chunks.size===this.endChunkSeq:void 0}binary(){if(!this.ifComplete())return null;for(var e=new Uint8Array(this.totalLength),t=0,r=0;r<=this.endChunkSeq;r++){var i=this.chunks.get(r);if(!i)return null;e.set(i.payload,t),t+=i.payload.byteLength}return e}}class Ue extends Pe.a{constructor(){super(),this.chunkMap=new Map,this.bufMap=new Map,this._createDeleteTimer()}destroy(){this.chunkMap.forEach((e=>{e.destroy()})),this.chunkMap.clear(),this.bufMap.forEach((e=>{e.destroy()})),this.bufMap.clear(),this._clearTimer(),super.removeAllListeners()}getChunkFrag(e){return this.chunkMap.get(e)}getRange(e){return this.chunkMap.has(e)?this.chunkMap.get(e).getRanges():null}getRangeSeq(e){return this.chunkMap.has(e)?this.chunkMap.get(e).getRangeSeq():null}getChunks(e){var t=this.chunkMap.get(e);return t?t.getChunks():null}createNewChunkFrag(e){if(!this.chunkMap.has(e)){var t=new xe({id:e});t.on("new_chunk",this._onNewChunk.bind(this)),t.on("complete",this._onChunkComplete.bind(this)),this.chunkMap.set(e,t)}}addChunk(e,t){var r=`${e.sn}-${e.level}`;this.chunkMap.has(r)||this.createNewChunkFrag(r);var i=this.chunkMap.get(r);i&&i.addChunk(e,t)}deleteChunkFrag(e){var t=this.chunkMap.get(e);t&&(t.destroy(),this.chunkMap.delete(e))}addP2PChunk(e){this.addChunk(e,Ne)}addCDNChunk(e){this.addChunk(e,Oe)}createBufFrag(e,t){var r=t.startByteOffset;if(!this.bufMap.has(e)){var i=new De({id:e,chunkLength:a.a.streamP2PChunkLength,startByteOffset:r});return i.on("new_chunk",this._onNewChunkFromStream.bind(this)),i.on("complete",this._onStreamChunkComplete.bind(this)),this.bufMap.set(e,i),i}}addBuf(e,t){var r=this.bufMap.get(e);r&&r.write(t)}completeBufFrag(e,t){var r=this.bufMap.get(e);r&&r.flush(t)}deleteBufFrag(e){var t=this.bufMap.get(e);t&&(t.destroy(),this.bufMap.delete(e))}delete(e){this.deleteBufFrag(e),this.deleteChunkFrag(e)}_createDeleteTimer(){this._clearTimer(),this._deleteTimer=setInterval(this._deleteOutDate.bind(this),2e3)}_clearTimer(){this._deleteTimer&&(clearInterval(this._deleteTimer),this._deleteTimer=null)}_deleteOutDate(){this.chunkMap.forEach(((e,t)=>{Date.now()-e.lastUpdateTime>12e4&&this.deleteChunkFrag(t)})),this.bufMap.forEach(((e,t)=>{Date.now()-e.lastUpdateTime>12e4&&this.deleteBufFrag(t)}))}_onNewChunk(e,t){this.emit("chunk",e,t)}_onChunkComplete(e){var t=e.id,r=this.chunkMap.get(t);if(r){var i=r.binary();this.emit("complete",{id:t,payload:i,stats:r.stats}),this.deleteChunkFrag(t)}}_onNewChunkFromStream(e){this.addCDNChunk(e)}_onStreamChunkComplete(e){var t=e.id;this.deleteBufFrag(t)}}var Be={circle:1,levelMismatching:2,tempParent:3,overLoad:4,disableUpload:5,reachMaxDepth:6,tooMuchDiffSn:7,disconnect:8};class Me{constructor(){this.uri=N.a.P2P_RES_ERR,this.sn=0,this.level=0,this.code=0}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt32(this.sn),e.pushUInt8(this.level),e.pushUInt8(this.code),e.marshall()}unmarshall(e){this.sn=e.popUInt32(),this.level=e.popUInt8(),this.code=e.popUInt8()}}class qe{constructor(){this.uri=N.a.P2P_REQ_CHUNK,this.sn=0,this.level=0,this.hop=0,this.url="",this.startSeq=0}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt32(this.sn),e.pushUInt8(this.level),e.pushUInt8(this.hop),e.pushString(this.url),e.pushUInt32(this.startSeq),e.marshall()}unmarshall(e){this.sn=e.popUInt32(),this.level=e.popUInt8(),this.hop=e.popUInt8(),this.url=e.popString(),this.startSeq=e.popUInt32()}}class $e{constructor(){this.uri=N.a.P2P_REQ_CANCEL,this.sn=0,this.level=0}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt32(this.sn),e.pushUInt8(this.level),e.marshall()}unmarshall(e){this.sn=e.popUInt32(),this.level=e.popUInt8()}}class Fe{constructor(){this.uri=N.a.P2P_REQ_ACCEPT,this.sn=0,this.level=0,this.parents=[]}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt32(this.sn),e.pushUInt8(this.level),e.pushUInt8(this.parents.length),this.parents.forEach((t=>{e.pushString(t)})),e.marshall()}unmarshall(e){this.sn=e.popUInt32(),this.level=e.popUInt8();for(var t=e.popUInt8(),r=0;r<t;r++)this.parents.push(e.popString())}}class Ge{constructor(){this.uri=N.a.P2P_RES_PARENT,this.sn=0,this.level=0,this.parents=[]}include(e){return this.parents.indexOf(e)>=0}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt32(this.sn),e.pushUInt8(this.level),e.pushUInt8(this.parents.length),this.parents.forEach((t=>{e.pushString(t)})),e.marshall()}unmarshall(e){this.sn=e.popUInt32(),this.level=e.popUInt8();for(var t=e.popUInt8(),r=0;r<t;r++)this.parents.push(e.popString())}}var He=2,je=1,Ye=0;class Ve{constructor(){this.uri=N.a.NODE_SYNC,this.currentSn=0,this.currentLevel=0,this.load=0,this.rtt=100,this.currentDepth=100,this.progress=0}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt32(this.currentSn),e.pushUInt8(this.currentLevel),e.pushUInt8(this.load),e.pushUInt8(this.currentDepth),e.pushUInt8(this.progress),e.marshall()}unmarshall(e){this.currentSn=e.popUInt32(),this.currentLevel=e.popUInt8(),this.load=e.popUInt8(),this.currentDepth=e.popUInt8(),this.progress=e.popUInt8()}score(){return V.a.curReqLevel!==this.currentLevel||this.currentSn<V.a.curReqSn-50?0:a.a.enableLocalNetworkShare?100/(this.rtt||100)+5/this.currentDepth+this.progress:100/this.rtt/(this.load+1)}}var Ke=1;class ze{constructor(){this.uri=N.a.P2P_UPWARD,this.sn=0,this.level=0,this.action=0,this.ttl=0}clone(){var e=new ze;return e.sn=this.sn,e.level=this.level,e.action=this.action,e.ttl=this.ttl,e}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt32(this.sn),e.pushUInt8(this.level),e.pushUInt8(this.action),e.pushUInt8(this.ttl),e.marshall()}unmarshall(e){this.sn=e.popUInt32(),this.level=e.popUInt8(),this.action=e.popUInt8(),this.ttl=e.popUInt8()}}class Xe{constructor(){this.router=null}destroy(){this.router=null}setRouter(e){return this.router=e,this}setSubscriber(e){return this.subscriber=e,this}setPeerManager(e){return this.peerManager=e,this}setSyncWorker(e){return this.syncWorker=e,this}init(){this.router.registerCb(N.a.P2P_REQ_CHUNK,this._onChunkRequest.bind(this)),this.router.registerCb(N.a.P2P_REQ_CANCEL,this._onCancelRequestChunk.bind(this)),this.router.registerCb(N.a.P2P_REQ_ACCEPT,this._onRequestAccept.bind(this)),this.router.registerCb(N.a.P2P_RES_CHUNK,this._onResChunk.bind(this)),this.router.registerCb(N.a.P2P_RES_ERR,this._onResErr.bind(this)),this.router.registerCb(N.a.P2P_RES_PARENT,this._onResParent.bind(this)),this.router.registerCb(N.a.NODE_SYNC,this._onNodeSync.bind(this)),this.router.registerCb(N.a.P2P_UPWARD,this._onUpward.bind(this))}_onChunkRequest(e,t){var r=new qe;r.unmarshall(t),this.subscriber.onChunkRequest(e,r)}_onCancelRequestChunk(e,t){var r=new $e;r.unmarshall(t),this.subscriber.onReqCancel(e,r)}_onRequestAccept(e,t){var r=new Fe;r.unmarshall(t),this.subscriber.onReqAccept(e,r)}_onResChunk(e,t){var r=new Le;r.unmarshall(t),this.subscriber.onResponseChunk(e,r)}_onResErr(e,t){var r=new Me;r.unmarshall(t),this.subscriber.onRefused(e,r)}_onResParent(e,t){var r=new Ge;r.unmarshall(t),this.subscriber.onResponseParent(e,r)}_onNodeSync(e,t){var r=new Ve;r.unmarshall(t),this.syncWorker.onRecvNodeInfo(e,r)}_onUpward(e,t){var r=new ze;r.unmarshall(t),this.subscriber.onUpward(e,r)}}class We{constructor(){this.pid=""}}var Qe=e=>e?[...e.keys()]:[];class Je extends Map{constructor(e){var t=e.timeout;super(),this.timeout=t,this.deleteTimer=new Map}set(e,t){return this._deleteTimer(e),this._setDeleteTimer(e),super.set(e,t)}delete(e){return this._deleteTimer(e),super.delete(e)}clear(){return this.deleteTimer.forEach(((e,t)=>{this._deleteTimer(t)})),super.clear()}_setDeleteTimer(e){var t=setTimeout((()=>{this.delete(e),this.deleteTimer.delete(e)}),this.timeout);this.deleteTimer.set(e,t)}_deleteTimer(e){var t=this.deleteTimer.get(e);t&&clearTimeout(t),this.deleteTimer.delete(e)}}class Ze{constructor(){this.TAG="LLSSubscriber",this.child=new Je({timeout:12e4}),this.parent=new Je({timeout:12e4}),this.myRequest=new Je({timeout:12e4}),this.backToCDNRequest=new Je({timeout:12e4}),this.requsetUrl=new Je({timeout:12e4}),this.subscribeBlackList=new Je({timeout:12e4}),this.requestStartSeq=new Je({timeout:12e4}),this.retryTimes=new Je({timeout:12e4}),this._peerRecvRecord=new Je({timeout:12e4}),this._peerReqRecord=new Je({timeout:12e4}),this._firstRecvRecord=new Je({timeout:12e4}),o.a.registerModule(this.TAG,this.reportCallback.bind(this)),this.initReport()}setChunkMgr(e){return this.chunkMgr=e,this}init(e){this.hlsp2p=e,e.on("delete_buffer",(e=>{var t=e.id;this.child.delete(t),this.parent.delete(t),this.myRequest.delete(t),this.backToCDNRequest.delete(t),this.requsetUrl.delete(t),this.subscribeBlackList.delete(t),this.chunkMgr&&this.chunkMgr.delete(t),this.requestStartSeq.delete(t),this.retryTimes.delete(t),this._peerReqRecord.delete(t),this._peerRecvRecord.delete(t),this._firstRecvRecord.delete(t)})),e.on(s.a.PEER_REMOVED,(e=>{var t=e.pid;this.child.forEach((e=>{e.delete(t)})),this.myRequest.forEach(((e,r)=>{if(e===t){this.myRequest.delete(r);var i=te.a.fromId(r),n=this.requsetUrl.get(r);if(n){if(this._reportData[h.a.P2P_RETRY_BY_DISCONNECT]+=1,this.cancelChunkRequest(r),this._tryToRetryP2PRequest({sn:i.sn,level:i.level},{errCode:Be.disconnect,pid:e}))return;T.b.info(`[PEER_REMOVED] 节点断开 ${t}, 更新链路节点并走cdn ${r}`),this._reportData[h.a.BACK_CDN_PEER_DISCONNECT]+=1,this._clearAndRequestCDN({sn:i.sn,level:i.level,url:n})}}}))})),this.chunkMgr.on("chunk",this._sendChunkToChild.bind(this)),this.chunkMgr.on("complete",(e=>{var t=e.id,r=e.payload,i=e.stats;this.child.delete(t),this.parent.delete(t),this.hlsp2p.trigger("chunk_buffer_complete",{id:t,payload:r,stats:i}),this.myRequest.delete(t),this.backToCDNRequest.has(t)||this._resetParent(t)}))}destroy(){this.hlsp2p&&(this.child.clear(),this.parent.clear(),this.myRequest.clear(),this.backToCDNRequest.clear(),this.requsetUrl.clear(),this.subscribeBlackList.clear(),this._firstRecvRecord.clear(),this._peerRecvRecord.clear(),this._peerReqRecord.clear(),this.chunkMgr=null,this.hlsp2p=null)}setTransport(e){return this.transport=e,this}setPeerManager(e){return this.peerManager=e,this}setSyncWorker(e){return this.syncWorker=e,this.syncWorker.collectInfoCb=()=>{var e=new Ve;return e.currentSn=V.a.curReqSn,e.currentLevel=V.a.curReqLevel,e.load=this._childSize(),H.a.getSlices(V.a.curReqSn,V.a.curReqLevel,0)?e.progress=He:this.chunkMgr.getChunks(`${V.a.curReqSn}-${V.a.curReqLevel}`)?e.progress=je:e.progress=Ye,e},this}requestChunk(e){var t=e.sn,r=e.level,i=e.hop,n=e.url,s=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{retry:!1}).retry,a=`${t}-${r}`;if(this.requsetUrl.set(a,n),T.b.info(`[requestChunk] 本地尝试发出P2P请求 ${a}, hop: ${i} ${s}`),this.myRequest.has(a))return T.b.info(`[requestChunk] 本地已有P2P请求 id: ${a} parent: ${this.myRequest.get(a)}`),this.myRequest.get(a);var o=this.findParentToRequest(a);if(o){T.b.info(`[requestChunk] 发送p2p请求 ${a} to ${o}`);var l=new qe;l.sn=t,l.level=r,l.hop=i,l.url=n;var c=this.chunkMgr.getRangeSeq(a);c&&(l.startSeq=c.end+1,c.end>0&&(this._reportData[h.a.SUBSCRIBE_RANGE_CNT]+=1)),this._send(o,l),this._peerReqRecord.set(`${a}`,performance.now()),this.myRequest.set(a,o),this._reportData[h.a.SUBSCRIBE_CNT]+=1}else T.b.info(`[requestChunk] P2P请求未找到节点: id: ${a} ${localStorage.myUUID} ${this.hlsp2p.pid}`);return o}addParent(e,t){var r=this.parent.get(e);r||(r=new Set,this.parent.set(e,r)),r.add(t)}updateParents(e,t){this.backToCDNRequest.has(e)||(T.b.trace(`[updateParents] ${e} ${t}`),this.parent.set(e,new Set(t)))}findParentToRequest(e){var t=this.child.get(e),r=0,i="";return this.peerManager.getCandidatesPid({id:e}).forEach((n=>{var s=this.peerManager.getConnectedPeer(n);if(t&&t.has(n))return T.b.warn(`[findParentToRequest] ${e} ${n} 是自己的子节点, 忽略`),void(this._reportData[h.a.DROP_PEER_BY_CYCLE]+=1);if(this.subscribeBlackList.has(e)&&this.subscribeBlackList.get(e).has(n))return T.b.warn(`[findParentToRequest] ${n} 在黑名单中 ${e}, 忽略`),void(this._reportData[h.a.DROP_PEER_BY_BLACKLIST]+=1);var a=te.a.fromId(e);if(s.nodeInfo&&s.nodeInfo.currentLevel!==a.level)return T.b.warn(`[findParentToRequest] ${e}  ${n} 码率不同 need: ${a.level}, curPeer: ${s.nodeInfo.currentLevel}, 忽略`),void(this._reportData[h.a.DROP_PEER_BY_BITRATE]+=1);r||(i=n,r=s.score),s.score>r&&(i=n,r=s.score)})),i}cancelChunkRequest(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this.myRequest.get(e);if(T.b.log(`[cancelChunkRequest] 取消P2P请求 id: ${e}, parent: ${r}`),t&&"cdn_timeout"===t.reason&&this._statP2PWhenTimeout(e),r){var i=new $e,n=te.a.fromId(e);i.sn=n.sn,i.level=n.level,this._send(r,i),this.myRequest.delete(e),this.parent.has(e)&&this.parent.get(e).delete(r)}}onChunkRequest(e,t){var r=`${t.sn}-${t.level}`;if(this.requsetUrl.set(r,t.url),T.b.info(`[onChunkRequest] 接收P2P请求 from: ${e}, id: ${r} cdn: ${this.backToCDNRequest.has(r)}`),this._childSize()>a.a.maxSubscribeSize)return T.b.warn(`[onChunkRequest] ${r} 超负载拒绝 curChildSize: ${this._childSize()}, max config: ${a.a.maxSubscribeSize}`),this.sendRefuse(e,t,Be.overLoad),void(this._reportData[h.a.REFUSED_BY_LOAD]+=1);if(this._alreadyPartialData({sn:t.sn,level:t.level}))this._createChunkProvider(e,t);else{if(!this.levelCheck(t))return T.b.warn(`[onChunkRequest] 检查码率不同,拒绝 ${r} ${e}`),this.sendRefuse(e,t,Be.levelMismatching),void(this._reportData[h.a.REFUSED_BY_BITRATE]+=1);if(t.sn<V.a.curReqSn-a.a.bufferCount-1||t.sn>V.a.curReqSn+5)return T.b.warn(`[onChunkRequest] 检查sn相差太多,拒绝 req: ${t.sn} self: ${V.a.curReqSn}`),this.sendRefuse(e,t,Be.tooMuchDiffSn),void(this._reportData[h.a.REFUSED_BY_SN_RANGE]+=1);if(this.circleCheck(e,t))return T.b.warn(`[onChunkRequest] 成环拒绝 ${r} ${e}`),this.sendRefuse(e,t,Be.circle),void(this._reportData[h.a.REFUSED_BY_CYCLE]+=1);if(parent&&parent.size>=a.a.maxP2PDepth-1&&!this.backToCDNRequest.has(r)){T.b.warn(`[onChunkRequest] ${r} ${e} 通知父节点回cdn config: ${a.a.maxP2PDepth}`);var i=new ze;i.sn=t.sn,i.level=t.level,i.ttl=a.a.maxP2PDepth-1,i.action|=Ke,this._reportData[h.a.LLS_TRIGGER_UPWARD]+=1,this.sendUpward(i)}var n=this._createChunkProvider(e,t);n?T.b.warn(`[onChunkRequest] 本地有数据, 无需转发请求 ret 1=buffer, 2=chunkMgr, 0=miss, ret: ${n}, id: ${r}`):(this.acceptChunkRequest(e,t),this.tryToMakeRequest(t))}}acceptChunkRequest(e,t){this.addChild(e,t),this.sendReqAccept(e,{sn:t.sn,level:t.level})}tryToMakeRequest(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{retry:!1}).retry,r=`${e.sn}-${e.level}`;if(this.backToCDNRequest.has(r))return T.b.info(`[onChunkRequest] 自己已经回CDN请求 ${r}`),"";if(this.myRequest.has(r))return T.b.info(`[onChunkRequest] 自己也在请求中,无需转发 ${r}`),this.myRequest.get(r);var i=this.requestChunk({sn:e.sn,level:e.level,hop:e.hop+1,url:e.url},{retry:t});return T.b.info(`[onChunkRequest] 转发请求 ${r} to ${i}`),i||(T.b.warn(`[onChunkRequest] 接收请求, 无节点可以P2P请求, 代理走cdn ${r}`),this._reportData[h.a.NOP2P_BY_NO_PEER]+=1,this._clearAndRequestCDN({sn:e.sn,level:e.level,url:e.url})),i}addChild(e,t){var r=t.sn,i=t.level,n=t.hop,s=`${r}-${i}`,a=this.child.get(s);a||(a=new Map,this.child.set(s,a));var o=new We;o.hop=n,o.pid=e,a.set(e,o)}sendUpward(e){var t=this._getParent(e);t?this._send(t,e):T.b.warn(`[sendUpward] ${e.sn}-${e.level} 找不到parent`)}_getParent(e){var t=e.sn,r=e.level;return this.myRequest.get(`${t}-${r}`)||""}sendRefuse(e,t,r){var i=t.sn,n=t.level,s=new Me;s.sn=i,s.level=n,s.code=r,this._send(e,s)}sendReqAccept(e,t){var r=t.sn,i=t.level,n=new Fe;n.sn=r,n.level=i;var s=Qe(this.parent.get(te.a.fromObj({sn:r,level:i}).toStringId()));s.push(this.peerManager.localPid()),this._reportData[h.a.SEND_PARENT_TOTAL]+=1,n.parents=s,this._send(e,n)}circleCheck(e,t){var r=t.sn,i=t.level,n=this.parent.get(`${r}-${i}`);return!!n&&n.has(e)}levelCheck(e){return e.level===V.a.curReqLevel}depthCheck(e){return e<=a.a.maxP2PDepth}onUpward(e,t){var r=`${t.sn}-${t.level}`;if(T.b.log(`[onUpward] 接收到upward ${e} id: ${r} ttl: ${t.ttl}`),t.action&Ke){if(0===t.ttl){var i=this.requsetUrl.get(r);return this._reportData[h.a.BACK_CDN_UPWARD]+=1,T.b.info(`[onUpward] 接收到upward, 回cdn ${e} ${r}`),void this._clearAndRequestCDN({sn:t.sn,level:t.level,url:i})}var n=t.clone();n.ttl=t.ttl-1,this.sendUpward(n)}}onReqCancel(e,t){T.b.log(`[onReqCancel] 接受${e} 取消请求 ${t.sn}-${t.level}, 删除子节点`);var r=this.child.get(`${t.sn}-${t.level}`);r&&r.delete(e)}onReqAccept(e,t){var r=`${t.sn}-${t.level}`;this.hlsp2p.trigger("subscribe_report",{seq:r,target:e}),this._reportData[h.a.SUBSCRIBE_SUCCESS_CNT]+=1,this.updateParents(r,t.parents);var i=Qe(this.child.get(r));this._reportData[h.a.SEND_PARENT_ACCEPT]+=1,this._sendAllParentToChild(i,{sn:t.sn,level:t.level})}onRefused(e,t){var r=`${t.sn}-${t.level}`;T.b.warn(`[onRefused] ${r} 请求被拒绝 for ${t.code}`,Be),this.myRequest.get(r)===e&&this.myRequest.delete(r),this._reportData[h.a.SUBSCRIBE_FAIL_CNT]+=1,this._reportData[h.a.P2P_RETRY_BY_REFUSE]+=1,this.cancelChunkRequest(r),this._tryToRetryP2PRequest({sn:t.sn,level:t.level},{errCode:t.code,pid:e})||(T.b.info(`[onRefused] 订阅被拒绝 ${e}, 更新链路节点并走cdn ${r}`),this._reportData[h.a.BACK_CDN_REQ_REFUSED]+=1,this._clearAndRequestCDN({sn:t.sn,level:t.level,url:this.requsetUrl.get(r)}))}_addSubscribeBlackList(e,t){var r=this.subscribeBlackList.get(e);r||(r=new Set,this.subscribeBlackList.set(e,r)),r.add(t)}_tryToRetryP2PRequest(e,t){var r=e.sn,i=e.level,n=t.errCode,s=t.pid,a=`${r}-${i}`;if(this.myRequest.delete(a),T.b.info(`[_tryToRetryP2PRequest] ${a} retry for ${n}`),this._addSubscribeBlackList(a,s),this._allowRetry(a,n)){var o=this.retryTimes.get(a)||0;this.retryTimes.set(a,o+1);var l=this.tryToMakeRequest({sn:r,level:i,hop:0,url:this.requsetUrl.get(a)},{retry:!0});return l?(this._reportData[h.a.P2P_RETRY_CNT]+=1,T.b.info(`[_tryToRetryP2PRequest] ${a} oldParent: ${s}, 重试 newParent: ${l}`)):(this._reportData[h.a.P2P_RETRY_MISS_CNT]+=1,T.b.warn(`[_tryToRetryP2PRequest] ${a} oldParent: ${s}, 重试未找到parentId`)),l}return T.b.warn(`[_tryToRetryP2PRequest] ${a} 不允许重试`),""}_allowRetry(e,t){var r=this.retryTimes.get(e)||0,i=-1!==[Be.circle,Be.levelMismatching,Be.overLoad,Be.reachMaxDepth,Be.disconnect,Be.tooMuchDiffSn].indexOf(t);return r<a.a.maxP2PRetryTimes&&i}onResponseParent(e,t){var r=`${t.sn}-${t.level}`,i=this.myRequest.get(r);if(e===i)if(this.backToCDNRequest.has(r))T.b.warn(`[onResponseParent] id:${r} 已回CDN,忽略 from: ${e}`);else{T.b.trace(`[onResponseParent] id:${r} parent层数: ${t.parents.length} parent: ${t.parents}  currentParent: ${i}, from: ${e}`);var n=Qe(this.child.get(r)),s=Qe(this.parent.get(r)),a=!1;if(t.include(this.peerManager.localPid()))T.b.warn(`[onResponseParent] ${r} 检查上游parent和本节点成环: ${e}`),Array.from(t.parents).sort()[0]===this.peerManager.localPid()&&(T.b.warn(`[onResponseParent] ${r} 检查上游parent和本节点成环, 自己断开: ${this.peerManager.localPid()}`),a=!0);if(a){if(!this.depthCheck(t.parents.length))return this._reportData[h.a.BACK_CDN_RES_PARENT_CIRCLE]+=1,void this._clearAndRequestCDN({sn:t.sn,level:t.level,url:this.requsetUrl.get(r)});if(this._reportData[h.a.P2P_RETRY_BY_CIRCLE]+=1,this.cancelChunkRequest(r),this._tryToRetryP2PRequest({sn:t.sn,level:t.level},{pid:i,errCode:Be.circle}))return;return this._reportData[h.a.BACK_CDN_RES_PARENT_CIRCLE]+=1,void this._clearAndRequestCDN({sn:t.sn,level:t.level,url:this.requsetUrl.get(r)})}s.toString()!==t.parents.toString()&&(this.updateParents(r,t.parents),this._reportData[h.a.SEND_PARENT_PASS]+=1,this._sendAllParentToChild(n,t))}else T.b.warn(`[onResponseParent] id:${r} parent不一致,忽略 parent: ${i} from: ${e}`)}_resetParent(e){this._clearParent(e);var t=Qe(this.child.get(e));this._reportData[h.a.SEND_PARENT_RESET]+=1,this._sendAllParentToChild(t,te.a.fromId(e))}_clearParent(e){var t=this.parent.get(e);t&&t.clear()}_deleteChild(e,t){var r=t.sn,i=t.level,n=this.child.get(`${r}-${i}`);n&&n.delete(e)}onResponseChunk(e,t){var r=`${t.sn}-${t.level}`;if(this._reportData[h.a.P2P_DOWNLOAD_BYTES]+=t.payload.byteLength,!this._firstRecvRecord.has(r)&&this._peerReqRecord.has(r)){this._firstRecvRecord.set(r,performance.now());var i=performance.now()-this._peerReqRecord.get(r);this._recordP2PFirstDepth(r),T.b.warn(`[onResponseChunk] ${r} P2P首包耗时 ${i} ${localStorage.myUUID} local:${this.hlsp2p.pid} from: ${e}`),this._recordP2PFirstCost(i)}var n=this.chunkMgr.getChunkFrag(r);if(n&&n.almostComplete(t)){var s=Math.floor(performance.now()-this._firstRecvRecord.get(r)),a=Math.floor(performance.now()-this._peerReqRecord.get(r));T.b.warn(`[onResponseChunk] ${r} P2P尾包耗时 cost ${s}, req耗时: ${a} ${localStorage.myUUID} local:${this.hlsp2p.pid} from: ${e}`),this._recordP2PCost(s,a)}this._peerRecvRecord.set(r,!0),this.chunkMgr.addP2PChunk(t)}_alreadyPartialData(e){var t=e.sn,r=e.level,i=`${t}-${r}`;return H.a.getSlices(t,r,0)?1:this.chunkMgr.getChunks(i)?2:0}_createChunkProvider(e,t){var r=t.sn,i=t.level,n=t.startSeq,s=`${r}-${i}`,o=this.requestStartSeq.get(s);o||(o=new Map,this.requestStartSeq.set(s,o)),o.set(e,n);var h=H.a.getSlices(r,i,0);if(h){this.addChild(e,{sn:r,level:i,hop:0}),this._clearParent(s),this.sendReqAccept(e,{sn:r,level:i});var l=h.arrayBufferData,c=new De({id:`${r}-${i}`,chunkLength:a.a.streamP2PChunkLength,startByteOffset:0});return c.on("new_chunk",(t=>{this._necessaryChunk(e,t)&&((t.flag&ke||t.flag&Ae)&&T.b.log(`[_createChunkProvider] 发送buffer中的chunk ${t.sn}-${t.level} ${t.seq} pid: ${e} flag: ${t.flag}`),this._send(e,t))})),c.flush(new Uint8Array(l)),this.child.has(s)&&this.child.get(s).delete(e),1}var u=this.chunkMgr.getChunks(s);return u?(this.addChild(e,{sn:r,level:i,hop:0}),this.sendReqAccept(e,{sn:r,level:i}),u.forEach((t=>{this._necessaryChunk(e,t)&&this._send(e,t)})),2):0}_necessaryChunk(e,t){var r=`${t.sn}-${t.level}`,i=this.requestStartSeq.get(r),n=0;return i&&i.has(e)&&(n=i.get(e)),t.seq>=n}_sendAllParentToChild(e,t){var r=t.sn,i=t.level,n=`${r}-${i}`,s=new Ge;s.sn=r,s.level=i,s.parents=Qe(this.parent.get(n)),s.parents.push(this.peerManager.localPid()),this._reportData[h.a.SEND_PARENT_TOTAL]+=1,e.forEach((e=>{this._send(e,s)}))}_sendChunkToChild(e,t){var r=`${e.sn}-${e.level}`;this.hlsp2p.trigger("chunk_stat",r,t);var i=this.child.get(r);if(i){var n=e.marshall();i.forEach(((t,r)=>{this._necessaryChunk(r,e)&&(this._sendBinary(r,n),(e.flag&ke||e.flag&Ae)&&T.b.log(`[_sendChunkToChild] 发送刚接收的chunk给子节点 id:${e.sn}-${e.level} seq:${e.seq} flag: ${e.flag} pid: ${r}`))}))}}_clearAndRequestCDN(e){var t=e.sn,r=e.level,i=e.url,n=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{resetParent:!0}).resetParent,s=`${t}-${r}`;this.cancelChunkRequest(s),n&&this._resetParent(s),this.backToCDNRequest.has(s)?T.b.warn(`[_clearAndRequestCDN] 已经回了cdn, 不再重复请求 ${s}`):(this.backToCDNRequest.set(s,s),this._reportData[h.a.BACK_CDN_REQ_TOTAL]+=1,this.hlsp2p.trigger("imt_load_cdn",{sn:t,level:r,url:i}))}_send(e,t){this.transport&&(t.uri===N.a.P2P_RES_CHUNK&&(this._reportData[h.a.P2P_UPLOADED_BYTES]+=t.payload.byteLength),this.transport.sendBinary(e,t.marshall()))}_sendBinary(e,t){this.transport&&(this._reportData[h.a.P2P_UPLOADED_BYTES]+=t.byteLength,this.transport.sendBinary(e,t))}_childSize(){var e=0;return this.child.forEach((t=>{e+=t.size})),e}_recordP2PFirstCost(e){this._reportData[h.a.P2P_FIRST_MS].length>10||this._reportData[h.a.P2P_FIRST_MS].push(Math.floor(e))}_recordP2PFirstDepth(e){var t=this.parent.get(e);t&&this._reportData[h.a.P2P_FIRST_RECV_DEPTH].length<20&&this._reportData[h.a.P2P_FIRST_RECV_DEPTH].push(t.size)}_recordP2PCost(e,t){this._reportData[h.a.P2P_COST_MS].length>10||(this._reportData[h.a.P2P_COST_MS].push(e),this._reportData[h.a.P2P_REQ_COST_MS].push(t))}_statP2PWhenTimeout(e){T.b.warn(`P2P超时统计 ${e} 首帧: ${(this._firstRecvRecord.get(e)||this._peerReqRecord.get(e))-this._peerReqRecord.get(e)}`);var t=this.parent.get(e);t&&this._reportData[h.a.P2P_TIMEOUT_DEPTH].length<20&&this._reportData[h.a.P2P_TIMEOUT_DEPTH].push(t.size),this._firstRecvRecord.has(e)||(this._reportData[h.a.P2P_FIRST_TIMEOUT_CNT]+=1),this._reportData[h.a.P2P_DWN_TIMEOUT_CNT]+=1}initReport(){this._reportData={[h.a.CHILDREN_CNT]:0,[h.a.PARENT_CNT]:0,[h.a.SUBSCRIBE_SUCCESS_CNT]:0,[h.a.SUBSCRIBE_FAIL_CNT]:0,[h.a.REFUSED_BY_CYCLE]:0,[h.a.REFUSED_BY_BITRATE]:0,[h.a.REFUSED_BY_LOAD]:0,[h.a.REFUSED_BY_TOO_DEPTH]:0,[h.a.REFUSED_BY_SN_RANGE]:0,[h.a.DROP_PEER_BY_CYCLE]:0,[h.a.DROP_PEER_BY_BLACKLIST]:0,[h.a.DROP_PEER_BY_BITRATE]:0,[h.a.P2P_DOWNLOAD_BYTES]:0,[h.a.P2P_UPLOADED_BYTES]:0,[h.a.BACK_CDN_PEER_DISCONNECT]:0,[h.a.BACK_CDN_RES_PARENT_CIRCLE]:0,[h.a.BACK_CDN_RES_DEPTH_LIMIT]:0,[h.a.BACK_CDN_REQ_REFUSED]:0,[h.a.NOP2P_BY_NO_PEER]:0,[h.a.BACK_CDN_REQ_TOTAL]:0,[h.a.BACK_CDN_UPWARD]:0,[h.a.LLS_TRIGGER_UPWARD]:0,[h.a.SEND_PARENT_TOTAL]:0,[h.a.SEND_PARENT_NEW_CHILD]:0,[h.a.SEND_PARENT_ACCEPT]:0,[h.a.SEND_PARENT_PASS]:0,[h.a.SEND_PARENT_RESET]:0,[h.a.SUBSCRIBE_CNT]:0,[h.a.SUBSCRIBE_RANGE_CNT]:0,[h.a.P2P_RETRY_CNT]:0,[h.a.P2P_RETRY_MISS_CNT]:0,[h.a.P2P_RETRY_BY_REFUSE]:0,[h.a.P2P_RETRY_BY_CIRCLE]:0,[h.a.P2P_RETRY_BY_DISCONNECT]:0,[h.a.P2P_FIRST_MS]:[],[h.a.P2P_COST_MS]:[],[h.a.P2P_REQ_COST_MS]:[],[h.a.P2P_FIRST_RECV_DEPTH]:[],[h.a.P2P_FIRST_TIMEOUT_CNT]:0,[h.a.P2P_DWN_TIMEOUT_CNT]:0,[h.a.P2P_TIMEOUT_DEPTH]:[]}}reportCallback(){var e=this._reportData;return this.initReport(),e[h.a.CHILDREN_CNT]=this._childSize(),this.myRequest.forEach((()=>{e[h.a.PARENT_CNT]+=1})),e}}class et{constructor(){this.collectInfoCb=null}setPeerManager(e){return this.peerManager=e,this}setTransport(e){return this.transport=e,this}destroy(){this._clearTimer(),this.peerManager=null,this.transport=null,this.collectInfoCb=null}init(){this._syncNodeTimer=setInterval(this._syncNodeInfo.bind(this),a.a.syncNodeInterval)}onRecvNodeInfo(e,t){var r=this.peerManager.getConnectedPeer(e);r&&(t.rtt=r.rtt,r.nodeInfo=t)}_syncNodeInfo(){if(this.collectInfoCb){var e=this.collectInfoCb().marshall();this.peerManager.getConnectedPeers().forEach(((t,r)=>{this.transport.sendBinary(r,e)}))}}_clearTimer(){this._syncNodeTimer&&(clearInterval(this._syncNodeTimer),this._syncNodeTimer=null)}}class tt{constructor(){this.chunkMgr=new Ue,this.llsServer=new Xe,this.subscriber=new Ze,this.syncWorker=new et}setRouter(e){return this.router=e,this}setTransport(e){return this.transport=e,this}setPeerManager(e){return this.peerManager=e,this}init(e){this.hlsp2p=e,this.syncWorker.setTransport(this.transport).setPeerManager(this.peerManager).init(),this.subscriber.setChunkMgr(this.chunkMgr).setTransport(this.transport).setPeerManager(this.peerManager).setSyncWorker(this.syncWorker).init(e),this.llsServer.setSubscriber(this.subscriber).setRouter(this.router).setPeerManager(this.peerManager).setSyncWorker(this.syncWorker).init()}destroy(){this.chunkMgr.destroy(),this.subscriber.destroy(),this.llsServer.destroy(),this.syncWorker.destroy(),this.router=null,this.peerManager=null,this.transport=null,this.hlsp2p=null}}class rt extends Z{constructor(e){super(e),e.on("player_level",(e=>{var t=e.level;a.a.levelForTracker=t}))}}var it=r(21);class nt{constructor(){this.uri=N.a.RTT_REQ,this.roundId=0}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt8(this.roundId),e.marshall()}unmarshall(e){this.roundId=e.popUInt8()}}class st{constructor(){this.tasks=new Set}destroy(){this.flushAllTask()}addTask(e){e.setExecCb((e=>{this.tasks.delete(e)})),this.tasks.add(e)}tick(){}getNextTask(){}flushAllTask(){this.tasks.forEach((e=>{e.destroy()})),this.tasks.clear()}}class at{constructor(e){var t=e.cb;this._done=!1,this._action=t,this._execCb=null}destroy(){this.done(),this._action=null}setExecCb(e){this._execCb=e}done(){this._done=!0}exec(){this._action&&this._action(),this._execCb&&this._execCb(this)}}class ot extends at{constructor(e){var t=e.cb,r=e.timeout;super({cb:t}),this._setTimeout(r)}destroy(){super.destroy(),this._clearTimeout()}_setTimeout(e){"number"==typeof e&&(this._timer=setTimeout((()=>{this._timer=null,this._done||this.exec()}),e))}_clearTimeout(){this._timer&&(clearTimeout(this._timer),this._timer=null)}}class ht{constructor(){this.uri=N.a.RTT_RES,this.roundId=0}marshall(){var e=new D.a;return e.setUri(this.uri),e.pushUInt8(this.roundId),e.marshall()}unmarshall(e){this.roundId=e.popUInt8()}}class lt{constructor(e){var t=e.id,r=e.sampleCount,i=e.finishedCb;this._id=t,this._sampleCount=r,this._cnt=0,this._minRTT=1/0,this._finishedCb=i,this._round=new Map}setMin(e){e<this._minRTT&&(this._minRTT=e),this._cnt+=1,this._cnt===this._sampleCount&&this._finishedCb({id:this._id,rtt:this._minRTT})}start(e){this._round.set(e,performance.now())}end(e,t){if(this._round.has(e)){var r=performance.now()-this._round.get(e)-t;this.setMin(r)}}}class ct{constructor(e){this.TAG="RTTDetector",this.hlsp2p=e,this.taskScheduler=new st,this.rttRecords=new Map}destroy(){this.taskScheduler.destroy(),this.hlsp2p=null,this._transport=null,this._router=null}setTransport(e){return this._transport=e,this}setRouter(e){return this._router=e,this}init(){this._router.registerCb(N.a.RTT_REQ,this._recvPingReq.bind(this)),this._router.registerCb(N.a.RTT_RES,this._recvPingRes.bind(this))}ping(e){this.rttRecords.has(e)||this._createPingTask(e).forEach((e=>{this.taskScheduler.addTask(e)}))}_createPingTask(e){for(var t=this,r=[],i=function(i){var n=new ot({timeout:1e3*i,cb:()=>{t._sendPingReq(e,{pingIndex:i+1})}});r.push(n)},n=0;n<4;n++)i(n);return r}_sendPingReq(e,t){var r=t.pingIndex,i=new nt;this._getRecordByPid(e).start(r),i.roundId=r,this._transport&&this._transport.sendBinary(e,i.marshall())}_recvPingReq(e,t){var r=new nt;r.unmarshall(t),this._sendPingRes(e,{roundId:r.roundId})}_sendPingRes(e,t){var r=t.roundId,i=new ht;i.roundId=r,this._transport&&this._transport.sendBinary(e,i.marshall())}_recvPingRes(e,t){var r=new ht;r.unmarshall(t),this._getRecordByPid(e).end(r.roundId,0)}_getRecordByPid(e){var t=this.rttRecords.get(e);return t||(t=new lt({id:e,sampleCount:4,finishedCb:t=>{var r=t.id,i=t.rtt;this.hlsp2p.trigger(s.a.RTT_GOT,{pid:r,rtt:i}),this.rttRecords.delete(e)}}),this.rttRecords.set(e,t)),t}}class ut{constructor(e){this.hlsp2p=e,this.minRTT=new Map,this.rttDetector=new ct(this.hlsp2p)}destroy(){this.rttDetector.destroy(),this._transport=null,this._router=null,this.hlsp2p=null}setTransport(e){return this._transport=e,this}setRouter(e){return this._router=e,this}init(){this.rttDetector.setRouter(this._router).setTransport(this._transport).init(),this.hlsp2p.on(s.a.RTT_GOT,(e=>{var t=e.pid,r=e.rtt;this.minRTT.set(t,r),this._filterByRTT({pid:t,rtt:r})}))}addPeer(e){this.rttDetector.ping(e)}_filterByRTT(e){var t=e.pid,r=e.rtt;this.hlsp2p&&(r<=a.a.LNSRttThreshold?(l.a.log(`[p2p] [rtt filter] 节点: ${t} rtt: ${r} 满足局域网rtt筛选`),this.hlsp2p.trigger(s.a.PEER_FILTER_PASSED,{pid:t})):(l.a.log(`[p2p] [rtt filter] 节点: ${t} rtt: ${r} 未满足局域网rtt筛选`),this.hlsp2p.trigger(s.a.PEER_FILTER_BLOCKED,{pid:t})))}}function dt(e,t){var r;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return pt(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pt(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var i=0,n=function(){};return{s:n,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,o=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return a=e.done,e},e:function(e){o=!0,s=e},f:function(){try{a||null==r.return||r.return()}finally{if(o)throw s}}}}function pt(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=new Array(t);r<t;r++)i[r]=e[r];return i}class ft{static create(e){return e?new ft(e):null}constructor(e){this._strAddr=e}addrArray(){return this._strAddr.split(".").map((e=>parseInt(e,10)))}isValid(){return/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(this._strAddr)}isPrivate(){this._privateAddrs=[[new ft("10.0.0.0"),new ft("10.255.255.255")],[new ft("100.64.0.0"),new ft("100.127.255.255")],[new ft("172.16.0.0"),new ft("172.31.255.255")],[new ft("192.0.0.0"),new ft("192.0.0.255")],[new ft("192.168.0.0"),new ft("192.168.255.255")],[new ft("198.18.0.0"),new ft("198.19.255.255")]];var e,t=dt(this._privateAddrs);try{for(t.s();!(e=t.n()).done;){var r=e.value,i=r[0],n=r[1];if(this.gte(i)&&this.lte(n))return!0}}catch(e){t.e(e)}finally{t.f()}return!1}sameNetworkSegment(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this._categoryDefaultCIDR={A:8,B:16,C:24};var r=this.category();if(r!==e.category())return!1;for(var i=this.addrArray(),n=e.addrArray(),s=0;s<(t||this._categoryDefaultCIDR[r])/8;s++)if(i[s]!==n[s])return!1;return!0}gte(e){return this.compare(e)>=0}lte(e){return this.compare(e)<=0}category(){this._category=[{type:"A",range:[new ft("1.0.0.1"),new ft("127.255.255.254")]},{type:"B",range:[new ft("128.0.0.1"),new ft("191.255.255.254")]},{type:"C",range:[new ft("*********"),new ft("***************")]}];var e,t=dt(this._category);try{for(t.s();!(e=t.n()).done;){var r=e.value,i=r.range[0],n=r.range[1];if(this.gte(i)&&this.lte(n))return r.type}}catch(e){t.e(e)}finally{t.f()}return""}compare(e){for(var t=this.addrArray(),r=e.addrArray(),i=0;i<4;i++){if(t[i]>r[i])return 1;if(t[i]<r[i])return-1}return 0}}class gt{constructor(e){this.candidate=new RTCIceCandidate({candidate:e,sdpMLineIndex:0,sdpMid:"0"})}type(){var e=this.candidate.address;return-1!==e.indexOf("local")?"mdns":-1!==e.indexOf(":")?"IPv6":"IPv4"}address(){return this.candidate.address}}var _t=r(29),vt=r.n(_t);function mt(e,t){var r;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return yt(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yt(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var i=0,n=function(){};return{s:n,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,o=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return a=e.done,e},e:function(e){o=!0,s=e},f:function(){try{a||null==r.return||r.return()}finally{if(o)throw s}}}}function yt(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=new Array(t);r<t;r++)i[r]=e[r];return i}class bt{constructor(){this.local=new Map,this.remote=new Map}destroy(){this.local.clear(),this.remote.clear()}addLocal(e){this.hasLocal(e)||this.local.set(e.address(),e)}addRemote(e){this.hasRemote(e)||this.remote.set(e.address(),e)}hasLocal(e){return this.local.has(e.address())}hasRemote(e){return this.remote.has(e.address())}localAddrType(){var e,t=mt(this.local);try{for(t.s();!(e=t.n()).done;){var r=vt()(e.value,2)[1];if(r.type())return r.type()}}catch(e){t.e(e)}finally{t.f()}}remoteAddrType(){var e,t=mt(this.remote);try{for(t.s();!(e=t.n()).done;){var r=vt()(e.value,2)[1];if(r.type())return r.type()}}catch(e){t.e(e)}finally{t.f()}}}var Tt="OK",Ct="REJECT",Rt="NEED_MORE_CHECK",Et="MORE_CHECK_UNKNOWN";class wt{constructor(){this.tag="AddressFilter",this._candidatePairs=new Map}destroy(){this._candidatePairs.forEach((e=>{e.destroy()})),this._candidatePairs.clear()}addPeerCandidate(e,t,r){var i=new gt(r),n=this._candidatePairs.get(e);n||(n=new bt,this._candidatePairs.set(e,n)),"local"===t?n.addLocal(i):n.addRemote(i)}deletePeerCandidate(e){this._candidatePairs.delete(e)}check(e){var t=this._candidatePairs.get(e);return t?this.bothMdns(t)?Tt:this.bothIP(t)?this._checkPairInSameNetworkSegment(t)?Tt:Ct:this.mixedAddr(t)?Rt:Et:Tt}bothMdns(e){return"mdns"===e.localAddrType()&&"mdns"===e.remoteAddrType()}bothIP(e){return"IPv4"===e.localAddrType()&&"IPv4"===e.remoteAddrType()}mixedAddr(e){var t=e.localAddrType(),r=e.remoteAddrType();return"mdns"===t&&"IPv4"===r||"IPv4"===t&&"mdns"===r}_checkPairInSameNetworkSegment(e){var t=!1;return e.local.forEach((r=>{e.remote.forEach((e=>{var i=ft.create(r.address()),n=ft.create(e.address());if(!i.isValid()||!n.isValid())return t;i.sameNetworkSegment(n)&&(t=!0)}))})),t}}function St(e,t){var r;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return Pt(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Pt(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var i=0,n=function(){};return{s:n,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,o=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return a=e.done,e},e:function(e){o=!0,s=e},f:function(){try{a||null==r.return||r.return()}finally{if(o)throw s}}}}function Pt(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=new Array(t);r<t;r++)i[r]=e[r];return i}class It{constructor(e){this._bucketCount=e.bucketCount||6,this._clearBucketInterval=e.clearBucketInterval||6e4,this._buckets=[];for(var t=0;t<this._bucketCount;t++)this._buckets.push(new Set);this._curBucketIndex=0,this._switchTimer=setInterval(this._switch.bind(this),this._clearBucketInterval/this._bucketCount)}destroy(){this._clearTimer(),this._buckets.forEach((e=>{e.clear()}))}get size(){return this._buckets.reduce(((e,t)=>e+t.size),0)}add(e){this._curBucket().add(e)}delete(e){this._curBucket().delete(e)}has(e){var t,r=St(this._buckets);try{for(r.s();!(t=r.n()).done;){if(t.value.has(e))return!0}}catch(e){r.e(e)}finally{r.f()}return!1}_switch(){this._curBucketIndex=(this._curBucketIndex+1)%this._buckets.length,this._curBucket().clear()}_curBucket(){return this._buckets[this._curBucketIndex]}_clearTimer(){this._switchTimer&&(clearInterval(this._switchTimer),this._switchTimer=null)}}var Lt="filter_addr_reject",kt="filter_addr_ok",At="filter_addr_more",Dt="filter_rtt_ok",Nt="filter_rtt_reject",Ot="inner_ip",xt="public_ip",Ut="black_list_cnt";class Bt{constructor(e,t){this.peerManager=t,this.hlsp2p=e,e.on("subscribe_report",this.onSubscribe.bind(this))}destroy(){this.hlsp2p&&(this.hlsp2p.off("subscribe_report"),this.hlsp2p=null,this.peerManager=null)}onSubscribe(e){var t=e.seq,r=e.target;if(T.b.log("subscribe_report",t,r),a.a.enableSubscribeReport){var i={pid:this.peerManager.localPid(),partners:[...this.peerManager.getConnectedPeers().keys()],seq:t,req:[r]};fetch("https://dev.ad.qvb.qcloud.com/graph/node",{method:"POST",mode:"cors",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)}).catch((e=>{}))}}}class Mt extends Z{constructor(e){super(e),this.TAG="LocalLivePeerManager",this.hlsp2p=e}init(){super.init(),this._candidates=new Map,this.reqReport=new Bt(this.hlsp2p,this),this._blackList=new It({bucketCount:a.a.bucketCount,clearBucketInterval:a.a.clearBucketInterval}),this.hlsp2p.on(s.a.CDN_REQUEST,(()=>{V.a.isCdnNode&&(this._localReportData[h.a.CDN_NODE_REQ_CNT]+=1)})),this._initAddressFilter(),this.initLocalReportData()}_initAddressFilter(){this._addressFilter=new wt,this.hlsp2p.on(s.a.PEER_REMOVED,(e=>{var t=e.pid;this._addressFilter.deletePeerCandidate(t)})),this.hlsp2p.on(s.a.SIGNAL_BEFORE_SENDING,(e=>{if(e.type===p){var t=new gt(e.payload.candidate);this._reportData[Ot]=t.address(),this._addressFilter.addPeerCandidate(e.to,"local",e.payload.candidate)}})),this.hlsp2p.on(s.a.SIGNAL_RECEIVED,(e=>{this._addressFilter&&e.type===p&&this._addressFilter.addPeerCandidate(e.from,"remote",e.payload.candidate)}))}_initPeerFilter(){this._peerFilter=new ut(this.hlsp2p),this._peerFilter.setRouter(this.router).setTransport(this.transport).init(),this.hlsp2p.on(s.a.PEER_FILTER_PASSED,(e=>{var t=e.pid;super._PCConnected(t),this._localReportData[Dt]+=1})),this.hlsp2p.on(s.a.PEER_FILTER_BLOCKED,(e=>{var t=e.pid;this._localReportData[Nt]+=1,l.a.info(`[p2p] [peer] [_blackList] rtt reject, 加入黑名单 ${t}`),this._blackList.add(t),setTimeout((()=>{this.hlsp2p&&this._kickPeer(t)}),1e3)}))}destroy(){super.destroy(),this.reqReport&&(this.reqReport.destroy(),this.reqReport=null),this._blackList.destroy(),this._candidates.clear(),this._addressFilter&&(this._addressFilter.destroy(),this._addressFilter=null),this._peerFilter&&(this._peerFilter.destroy(),this._peerFilter=null)}getComPeer(e){return this._connected.get(e)||this._candidates.get(e)}receiveSignal(e){return e.type===g&&this._blackList.delete(e.from),super.receiveSignal(e)}canConnect(e){return e&&this._tryToKickForNewPeer(e),this._connected.size<a.a.maxPCConnected&&this._connecting.size<a.a.maxPCConnecting&&this._candidates.size<30}sendSignaling(e){e.type===u&&(e.peer={},e.peer.connected=this._connected.size),l.a.log(`[p2p] [发送信令] ${JSON.stringify(e)}`),super.sendSignaling(e)}_tryToKickForNewPeer(e){if(!(this._connected.size<a.a.maxPCConnected)&&e.type===u&&0===e.peer.connected){var t=this._pickPidToKick();t&&(l.a.warn(`[p2p] [peer] [kick] 满连接接收offer from: ${e.from}, 踢掉: ${t}`),this._kickPeer(t))}}_pickPidToKick(){var e=[...this._connected.keys()],t=e[Math.floor(e.length/2)];return t||""}_PCConnected(e){if(l.a.log(`[p2p] [preConnected] ${e}`),!a.a.enableMultiArea)return super._PCConnected(e);var t=this._connecting.get(e);if(this._connecting.delete(e),this._candidates.set(e,t),this._addressFilter){var r=this._addressFilter.check(e);if(l.a.info(`[p2p] [address filter] pid: ${e} ret: ${r}`),r===Tt)return this._localReportData[kt]+=1,super._PCConnected(e);if(r===Ct)return this._localReportData[Lt]+=1,l.a.info(`[p2p] [peer] [_blackList] [address filter] 加入黑名单 pid: ${e} ret: ${r}`),void this._blackList.add(e);this._localReportData[At]+=1}return this._peerFilter?(l.a.info(`[p2p] [rtt filter] add pid: ${e}`),this._peerFilter.addPeer(e)):super._PCConnected(e)}_handlePCFail(e){super._handlePCFail(e),e.reason===I&&(l.a.info(`[p2p] [peer] [_blackList]  连接超时, 加入黑名单 pid: ${e.remoteId}`),this._blackList.add(e.remoteId))}_getReadyPCFromCache(e){if(this._candidates.has(e)){var t=this._candidates.get(e);return this._candidates.delete(e),t}return super._getReadyPCFromCache(e)}_getNextPeerForConnecting(){for(var e=J.nextPeer();e&&this._blackList.has(e);)l.a.log(`[p2p] [peer] [_blackList] ${e} 在黑名单中, 不主动连接`),e=J.nextPeer();return e}initLocalReportData(){this._localReportData={[h.a.CDN_NODE_REQ_CNT]:0,[kt]:0,[Lt]:0,[At]:0,[Dt]:0,[Nt]:0,[Ot]:"",[xt]:a.a.publicIP||"",[Ut]:this._blackList.size}}reportCallback(){var e=super.reportCallback();return e[h.a.CDN_NODE]=V.a.isCdnNode,Object.assign(e,this._localReportData),this.initLocalReportData(),e}}class qt extends Mt{constructor(e){super(e)}getCandidatesPid(e){var t=e.id,r=te.a.fromId(t),i=[];return this._connected.forEach((e=>{var t=e?e.nodeInfo:null;t&&r.level===t.currentLevel&&(r.sn<t.currentSn||r.sn===t.currentSn&&0!==t.progress)&&i.push(e.remoteId)})),i.length?(T.b.log(`[getCandidatesPid] ${t} 命中缓存节点  size: ${i.length}`),i):super.getCandidatesPid({id:t})}}var $t=new class{constructor(){this.TAG="P2pController",this.hlsp2p=null}init(e){this.hlsp2p=e,this.initGlobalModule(),this._initedInnterModule=!1,this.hlsp2p.trigger(s.a.CONF_STARTING),this.initModuleRelyOnConfHandler=this.initModuleRelyOnConf.bind(this),this.hlsp2p.once(s.a.CONF_PARSED,this.initModuleRelyOnConfHandler)}destroy(){this.destroyGlobalModule(),this.destroyInnerModule(),this.destroyModuleRelyOnConf(),this.hlsp2p=null}initModuleRelyOnConf(){this.signalClient||(this.initInnerModule(),this.hlsp2p.trigger(s.a.TRACKER_STARTING),this.signalClient=new c(this.hlsp2p),this.hlsp2p.on(s.a.SIGNAL_READY,(e=>{var t=e.pid;l.a.info(`[p2p] [signal] ready: pid: ${t} uuid: ${it.a.myUUID().UUID} playId: ${a.a.randomPlayId}`),this.hlsp2p.localPeerId=t,this.hlsp2p.trigger(s.a.TRACKER_LOADING)})),this.hlsp2p.on(s.a.SIGNAL_RECEIVING,this.onSignalReceiving.bind(this)))}destroyModuleRelyOnConf(){this.hlsp2p.off(s.a.CONF_PARSED,this.initModuleRelyOnConfHandler),this.signalClient&&(this.signalClient.destroy(),this.signalClient=null)}initGlobalModule(){le.init(this.hlsp2p),J.init(this.hlsp2p),ce.init(this.hlsp2p),ye.init(this.hlsp2p),Re.init(this.hlsp2p)}destroyGlobalModule(){le.destroy(),J.destroy(),ce.destroy(),ye.destroy(),Re.destroy()}initInnerModule(){this._initedInnterModule||(this._initedInnterModule=!0,a.a.enableLocalNetworkShare||a.a.enableLLS?(this.router=new Ee,this.peerManager=a.a.enableLocalNetworkShare?new qt(this.hlsp2p):new rt(this.hlsp2p),this.transport=new we,this.peerManager.setRouter(this.router).setTransport(this.transport).init(),this.transport.setPeerManager(this.peerManager),this.llsController=new tt,this.llsController.setRouter(this.router).setTransport(this.transport).setPeerManager(this.peerManager).init(this.hlsp2p),this.hlsp2p.chunkMgr=this.llsController.chunkMgr,this.hlsp2p.subscriber=this.llsController.subscriber):(this.peerManager=new ie(this.hlsp2p),this.peerManager.init(),this.bitmapMgr=new ne,this.bitmapMgr.init(),this.packetMgr=new z,this.packetMgr.init()),this.peerManager.setP2PStats(this.hlsp2p.p2pStats))}destroyInnerModule(){this._initedInnterModule&&(this._initedInnterModule=!1,a.a.enableLocalNetworkShare||a.a.enableLLS?(this.router.destroy(),this.router=null,this.transport.destroy(),this.transport=null,this.llsController.destroy(),this.llsController=null,this.hlsp2p.chunkMgr=null,this.hlsp2p.subscriber=null):(this.bitmapMgr.destroy(),this.bitmapMgr=null,this.packetMgr.destroy(),this.packetMgr=null),this.peerManager.destroy(),this.peerManager=null)}onSignalReceiving(e){this.peerManager.receiveSignal(e)}get peerConnected(){return this.peerManager?this.peerManager.peerConnected:0}get trackerConnected(){return J.trackerConnected}},Ft=t.a=$t},function(e,t,r){"use strict";var i=r(33);t.a=class{constructor(){this.sn=0,this.level=0,this.uri="",this.baseUrl="",this.duration=0}get url(){return this._url||(this._url=Object(i.buildAbsoluteURL)(this.baseUrl,this.uri,{alwaysNormalize:!0})),this._url}}},function(e,t,r){var i,n,s,a,o;i=/^((?:[a-zA-Z0-9+\-.]+:)?)(\/\/[^\/?#]*)?((?:[^\/?#]*\/)*[^;?#]*)?(;[^?#]*)?(\?[^#]*)?(#.*)?$/,n=/^([^\/?#]*)(.*)$/,s=/(?:\/|^)\.(?=\/)/g,a=/(?:\/|^)\.\.\/(?!\.\.\/)[^\/]*(?=\/)/g,o={buildAbsoluteURL:function(e,t,r){if(r=r||{},e=e.trim(),!(t=t.trim())){if(!r.alwaysNormalize)return e;var i=o.parseURL(e);if(!i)throw new Error("Error trying to parse base URL.");return i.path=o.normalizePath(i.path),o.buildURLFromParts(i)}var s=o.parseURL(t);if(!s)throw new Error("Error trying to parse relative URL.");if(s.scheme)return r.alwaysNormalize?(s.path=o.normalizePath(s.path),o.buildURLFromParts(s)):t;var a=o.parseURL(e);if(!a)throw new Error("Error trying to parse base URL.");if(!a.netLoc&&a.path&&"/"!==a.path[0]){var h=n.exec(a.path);a.netLoc=h[1],a.path=h[2]}a.netLoc&&!a.path&&(a.path="/");var l={scheme:a.scheme,netLoc:s.netLoc,path:null,params:s.params,query:s.query,fragment:s.fragment};if(!s.netLoc&&(l.netLoc=a.netLoc,"/"!==s.path[0]))if(s.path){var c=a.path,u=c.substring(0,c.lastIndexOf("/")+1)+s.path;l.path=o.normalizePath(u)}else l.path=a.path,s.params||(l.params=a.params,s.query||(l.query=a.query));return null===l.path&&(l.path=r.alwaysNormalize?o.normalizePath(s.path):s.path),o.buildURLFromParts(l)},parseURL:function(e){var t=i.exec(e);return t?{scheme:t[1]||"",netLoc:t[2]||"",path:t[3]||"",params:t[4]||"",query:t[5]||"",fragment:t[6]||""}:null},normalizePath:function(e){for(e=e.split("").reverse().join("").replace(s,"");e.length!==(e=e.replace(a,"")).length;);return e.split("").reverse().join("")},buildURLFromParts:function(e){return e.scheme+e.netLoc+e.path+e.params+e.query+e.fragment}},e.exports=o},function(e,t,r){"use strict";var i=r(9),n=r(5);t.a=class{constructor(){this.uri=n.a.PACKET,this.sn=0,this.level=0,this.sliceIndex=0,this.packetIndex=0,this.totalPacket=0,this.payloadLength=0,this.uint8ArrayPayload=0}marshall(){var e=new i.a;return e.setUri(this.uri),e.pushUInt32(this.sn),e.pushUInt8(this.level),e.pushUInt8(this.sliceIndex),e.pushUInt8(this.packetIndex),e.pushUInt8(this.totalPacket),e.pushUInt32(this.payloadLength),e.pushUint8Array(this.uint8ArrayPayload),e.marshall()}unmarshall(e){this.sn=e.popUInt32(),this.level=e.popUInt8(),this.sliceIndex=e.popUInt8(),this.packetIndex=e.popUInt8(),this.totalPacket=e.popUInt8(),this.payloadLength=e.popUInt32(),this.uint8ArrayPayload=e.popUint8Array()}}},function(e,t,r){"use strict";var i=r(5);t.a=class{constructor(){this.sn=0,this.level=0,this.uri=i.a.BUFFER_INFO,this.subIndexes=[]}marshall(e){e.pushUInt32(this.sn),e.pushUInt8(this.level),e.pushUInt8(this.subIndexes.length),this.subIndexes.forEach((t=>{e.pushUInt8(t)}))}unmarshall(e){this.sn=e.popUInt32(),this.level=e.popUInt8();for(var t=e.popUInt8(),r=0;r<t;r+=1){var i=e.popUInt8();this.subIndexes.push(i)}}}},function(e,t,r){"use strict";t.a=class{constructor(){this.TAG="BaseModule",this.registerEvents=""}destroy(){throw Error("需要实现")}init(){throw Error("需要实现")}}},function(e,t,r){"use strict";t.a=class{static encrypt(e){for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),i=[99,117,105],n=0;n<e.length;n+=1)r[n]=e.codePointAt(n)^i[n%i.length];return r}static decrypt(e){for(var t="",r=[99,117,105],i=0;i<e.length;i+=1){var n=e[i].codePointAt(0)^r[i%r.length];t+=String.fromCodePoint(n)}return JSON.parse(t)}}},function(e,t,r){var i=r(65);e.exports=function(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,i(e,t)},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,r){"use strict";class i{static supportES(){try{var e=[1,2,3];[...e].forEach(((t,r)=>t===e[r]))}catch(e){return!1}return!(!window.Array.prototype.find||!window.Array.prototype.includes)}static supportMSEH264Playback(){return!(!window.MediaSource||!window.MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"'))}static supportP2P(){return i.supportWebSocket()&&i.supportDataType()&&i.supportObjectURL()&&i.supportRTCPeerConnection()&&i.supportDataChannel()}static supportDataType(){return!("undefined"==typeof ArrayBuffer||"undefined"==typeof Uint8Array||"undefined"==typeof Uint16Array||"undefined"==typeof Uint32Array||"undefined"==typeof DataView)}static supportWebSocket(){return!(!("WebSocket"in window)&&!("MozWebSocket"in window))}static supportRTCPeerConnection(){return!!window.RTCPeerConnection||!!(window.webkitRTCPeerConnection||window.mozRTCPeerConnection||window.msRTCPeerConnection||window.oRTCPeerConnection)}static supportDataChannel(){var e=window.RTCPeerConnection||window.msRTCPeerConnection||window.mozRTCPeerConnection||window.webkitRTCPeerConnection;return function(){var t=!1;try{var r=new e(null);t="createDataChannel"in r,r.close(),r=null}catch(e){}return t}()}static supportObjectURL(){return"URL"in window&&"createObjectURL"in URL}static pageVisibility(){var e={hidden:"",visibilityChange:"",pageVisible:"",support:!0};void 0!==document.hidden?(e.hidden="hidden",e.visibilityChange="visibilitychange"):void 0!==document.msHidden?(e.hidden="msHidden",e.visibilityChange="msvisibilitychange"):void 0!==document.webkitHidden?(e.hidden="webkitHidden",e.visibilityChange="webkitvisibilitychange"):e.support=!1;return e.pageVisible=e.support?window.document[e.hidden]?"hidden":"visible":"notSupport",e}static memory(){var e="";if(performance&&performance.memory){var t=performance.memory;e=`${t.jsHeapSizeLimit} | ${t.totalJSHeapSize} | ${t.usedJSHeapSize}`}return e}static isFullScreen(){return!!(document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement)}}t.a=i},function(e,t,r){"use strict";var i=r(43),n=r.n(i),s=r(30),a=r.n(s),o=r(38),h=r.n(o),l=function(){function e(){this.listeners={}}var t=e.prototype;return t.on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},t.off=function(e,t){if(!this.listeners[e])return!1;var r=this.listeners[e].indexOf(t);return this.listeners[e]=this.listeners[e].slice(0),this.listeners[e].splice(r,1),r>-1},t.trigger=function(e){var t=this.listeners[e];if(t)if(2===arguments.length)for(var r=t.length,i=0;i<r;++i)t[i].call(this,arguments[1]);else for(var n=Array.prototype.slice.call(arguments,1),s=t.length,a=0;a<s;++a)t[a].apply(this,n)},t.dispose=function(){this.listeners={}},t.pipe=function(e){this.on("data",(function(t){e.push(t)}))},e}(),c=r(44),u=r.n(c),d=r(51),p=r.n(d),f=r(52),g=function(e){function t(){var t;return(t=e.call(this)||this).buffer="",t}return h()(t,e),t.prototype.push=function(e){var t;for(this.buffer+=e,t=this.buffer.indexOf("\n");t>-1;t=this.buffer.indexOf("\n"))this.trigger("data",this.buffer.substring(0,t)),this.buffer=this.buffer.substring(t+1)},t}(l),_=String.fromCharCode(9),v=function(e){var t=/([0-9.]*)?@?([0-9.]*)?/.exec(e||""),r={};return t[1]&&(r.length=parseInt(t[1],10)),t[2]&&(r.offset=parseInt(t[2],10)),r},m=function(e){for(var t,r=e.split(new RegExp('(?:^|,)((?:[^=]*)=(?:"[^"]*"|[^,]*))')),i={},n=r.length;n--;)""!==r[n]&&((t=/([^=]*)=(.*)/.exec(r[n]).slice(1))[0]=t[0].replace(/^\s+|\s+$/g,""),t[1]=t[1].replace(/^\s+|\s+$/g,""),t[1]=t[1].replace(/^['"](.*)['"]$/g,"$1"),i[t[0]]=t[1]);return i},y=function(e){function t(){var t;return(t=e.call(this)||this).customParsers=[],t.tagMappers=[],t}h()(t,e);var r=t.prototype;return r.push=function(e){var t,r,i=this;0!==(e=e.trim()).length&&("#"===e[0]?this.tagMappers.reduce((function(t,r){var i=r(e);return i===e?t:t.concat([i])}),[e]).forEach((function(e){for(var n=0;n<i.customParsers.length;n++)if(i.customParsers[n].call(i,e))return;if(0===e.indexOf("#EXT"))if(e=e.replace("\r",""),t=/^#EXTM3U/.exec(e))i.trigger("data",{type:"tag",tagType:"m3u"});else{if(t=/^#EXTINF:?([0-9\.]*)?,?(.*)?$/.exec(e))return r={type:"tag",tagType:"inf"},t[1]&&(r.duration=parseFloat(t[1])),t[2]&&(r.title=t[2]),void i.trigger("data",r);if(t=/^#EXT-X-TARGETDURATION:?([0-9.]*)?/.exec(e))return r={type:"tag",tagType:"targetduration"},t[1]&&(r.duration=parseInt(t[1],10)),void i.trigger("data",r);if(t=/^#EXT-X-VERSION:?([0-9.]*)?/.exec(e))return r={type:"tag",tagType:"version"},t[1]&&(r.version=parseInt(t[1],10)),void i.trigger("data",r);if(t=/^#EXT-X-MEDIA-SEQUENCE:?(\-?[0-9.]*)?/.exec(e))return r={type:"tag",tagType:"media-sequence"},t[1]&&(r.number=parseInt(t[1],10)),void i.trigger("data",r);if(t=/^#EXT-X-DISCONTINUITY-SEQUENCE:?(\-?[0-9.]*)?/.exec(e))return r={type:"tag",tagType:"discontinuity-sequence"},t[1]&&(r.number=parseInt(t[1],10)),void i.trigger("data",r);if(t=/^#EXT-X-PLAYLIST-TYPE:?(.*)?$/.exec(e))return r={type:"tag",tagType:"playlist-type"},t[1]&&(r.playlistType=t[1]),void i.trigger("data",r);if(t=/^#EXT-X-BYTERANGE:?(.*)?$/.exec(e))return r=u()(v(t[1]),{type:"tag",tagType:"byterange"}),void i.trigger("data",r);if(t=/^#EXT-X-ALLOW-CACHE:?(YES|NO)?/.exec(e))return r={type:"tag",tagType:"allow-cache"},t[1]&&(r.allowed=!/NO/.test(t[1])),void i.trigger("data",r);if(t=/^#EXT-X-MAP:?(.*)$/.exec(e)){if(r={type:"tag",tagType:"map"},t[1]){var s=m(t[1]);s.URI&&(r.uri=s.URI),s.BYTERANGE&&(r.byterange=v(s.BYTERANGE))}i.trigger("data",r)}else if(t=/^#EXT-X-STREAM-INF:?(.*)$/.exec(e)){if(r={type:"tag",tagType:"stream-inf"},t[1]){if(r.attributes=m(t[1]),r.attributes.RESOLUTION){var a=r.attributes.RESOLUTION.split("x"),o={};a[0]&&(o.width=parseInt(a[0],10)),a[1]&&(o.height=parseInt(a[1],10)),r.attributes.RESOLUTION=o}r.attributes.BANDWIDTH&&(r.attributes.BANDWIDTH=parseInt(r.attributes.BANDWIDTH,10)),r.attributes["PROGRAM-ID"]&&(r.attributes["PROGRAM-ID"]=parseInt(r.attributes["PROGRAM-ID"],10))}i.trigger("data",r)}else{if(t=/^#EXT-X-MEDIA:?(.*)$/.exec(e))return r={type:"tag",tagType:"media"},t[1]&&(r.attributes=m(t[1])),void i.trigger("data",r);if(t=/^#EXT-X-ENDLIST/.exec(e))i.trigger("data",{type:"tag",tagType:"endlist"});else if(t=/^#EXT-X-DISCONTINUITY/.exec(e))i.trigger("data",{type:"tag",tagType:"discontinuity"});else{if(t=/^#EXT-X-PROGRAM-DATE-TIME:?(.*)$/.exec(e))return r={type:"tag",tagType:"program-date-time"},t[1]&&(r.dateTimeString=t[1],r.dateTimeObject=new Date(t[1])),void i.trigger("data",r);if(t=/^#EXT-X-KEY:?(.*)$/.exec(e))return r={type:"tag",tagType:"key"},t[1]&&(r.attributes=m(t[1]),r.attributes.IV&&("0x"===r.attributes.IV.substring(0,2).toLowerCase()&&(r.attributes.IV=r.attributes.IV.substring(2)),r.attributes.IV=r.attributes.IV.match(/.{8}/g),r.attributes.IV[0]=parseInt(r.attributes.IV[0],16),r.attributes.IV[1]=parseInt(r.attributes.IV[1],16),r.attributes.IV[2]=parseInt(r.attributes.IV[2],16),r.attributes.IV[3]=parseInt(r.attributes.IV[3],16),r.attributes.IV=new Uint32Array(r.attributes.IV))),void i.trigger("data",r);if(t=/^#EXT-X-START:?(.*)$/.exec(e))return r={type:"tag",tagType:"start"},t[1]&&(r.attributes=m(t[1]),r.attributes["TIME-OFFSET"]=parseFloat(r.attributes["TIME-OFFSET"]),r.attributes.PRECISE=/YES/.test(r.attributes.PRECISE)),void i.trigger("data",r);if(t=/^#EXT-X-CUE-OUT-CONT:?(.*)?$/.exec(e))return r={type:"tag",tagType:"cue-out-cont"},t[1]?r.data=t[1]:r.data="",void i.trigger("data",r);if(t=/^#EXT-X-CUE-OUT:?(.*)?$/.exec(e))return r={type:"tag",tagType:"cue-out"},t[1]?r.data=t[1]:r.data="",void i.trigger("data",r);if(t=/^#EXT-X-CUE-IN:?(.*)?$/.exec(e))return r={type:"tag",tagType:"cue-in"},t[1]?r.data=t[1]:r.data="",void i.trigger("data",r);if((t=/^#EXT-X-SKIP:(.*)$/.exec(e))&&t[1])return(r={type:"tag",tagType:"skip"}).attributes=m(t[1]),r.attributes.hasOwnProperty("SKIPPED-SEGMENTS")&&(r.attributes["SKIPPED-SEGMENTS"]=parseInt(r.attributes["SKIPPED-SEGMENTS"],10)),r.attributes.hasOwnProperty("RECENTLY-REMOVED-DATERANGES")&&(r.attributes["RECENTLY-REMOVED-DATERANGES"]=r.attributes["RECENTLY-REMOVED-DATERANGES"].split(_)),void i.trigger("data",r);if((t=/^#EXT-X-PART:(.*)$/.exec(e))&&t[1])return(r={type:"tag",tagType:"part"}).attributes=m(t[1]),["DURATION"].forEach((function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=parseFloat(r.attributes[e]))})),["INDEPENDENT","GAP"].forEach((function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=/YES/.test(r.attributes[e]))})),r.attributes.hasOwnProperty("BYTERANGE")&&(r.attributes.byterange=v(r.attributes.BYTERANGE)),void i.trigger("data",r);if((t=/^#EXT-X-SERVER-CONTROL:(.*)$/.exec(e))&&t[1])return(r={type:"tag",tagType:"server-control"}).attributes=m(t[1]),["CAN-SKIP-UNTIL","PART-HOLD-BACK","HOLD-BACK"].forEach((function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=parseFloat(r.attributes[e]))})),["CAN-SKIP-DATERANGES","CAN-BLOCK-RELOAD"].forEach((function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=/YES/.test(r.attributes[e]))})),void i.trigger("data",r);if((t=/^#EXT-X-PART-INF:(.*)$/.exec(e))&&t[1])return(r={type:"tag",tagType:"part-inf"}).attributes=m(t[1]),["PART-TARGET"].forEach((function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=parseFloat(r.attributes[e]))})),void i.trigger("data",r);if((t=/^#EXT-X-PRELOAD-HINT:(.*)$/.exec(e))&&t[1])return(r={type:"tag",tagType:"preload-hint"}).attributes=m(t[1]),["BYTERANGE-START","BYTERANGE-LENGTH"].forEach((function(e){if(r.attributes.hasOwnProperty(e)){r.attributes[e]=parseInt(r.attributes[e],10);var t="BYTERANGE-LENGTH"===e?"length":"offset";r.attributes.byterange=r.attributes.byterange||{},r.attributes.byterange[t]=r.attributes[e],delete r.attributes[e]}})),void i.trigger("data",r);if((t=/^#EXT-X-RENDITION-REPORT:(.*)$/.exec(e))&&t[1])return(r={type:"tag",tagType:"rendition-report"}).attributes=m(t[1]),["LAST-MSN","LAST-PART"].forEach((function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=parseInt(r.attributes[e],10))})),void i.trigger("data",r);i.trigger("data",{type:"tag",data:e.slice(4)})}}}else i.trigger("data",{type:"comment",text:e.slice(1)})})):this.trigger("data",{type:"uri",uri:e}))},r.addParser=function(e){var t=this,r=e.expression,i=e.customType,n=e.dataParser,s=e.segment;"function"!=typeof n&&(n=function(e){return e}),this.customParsers.push((function(e){if(r.exec(e))return t.trigger("data",{type:"custom",data:n(e),customType:i,segment:s}),!0}))},r.addTagMapper=function(e){var t=e.expression,r=e.map;this.tagMappers.push((function(e){return t.test(e)?r(e):e}))},t}(l),b=function(e){var t={};return Object.keys(e).forEach((function(r){var i;t[(i=r,i.toLowerCase().replace(/-(\w)/g,(function(e){return e[1].toUpperCase()})))]=e[r]})),t},T=function(e){var t=e.serverControl,r=e.targetDuration,i=e.partTargetDuration;if(t){var n="#EXT-X-SERVER-CONTROL",s="holdBack",a="partHoldBack",o=r&&3*r,h=i&&2*i;r&&!t.hasOwnProperty(s)&&(t[s]=o,this.trigger("info",{message:n+" defaulting HOLD-BACK to targetDuration * 3 ("+o+")."})),o&&t[s]<o&&(this.trigger("warn",{message:n+" clamping HOLD-BACK ("+t[s]+") to targetDuration * 3 ("+o+")"}),t[s]=o),i&&!t.hasOwnProperty(a)&&(t[a]=3*i,this.trigger("info",{message:n+" defaulting PART-HOLD-BACK to partTargetDuration * 3 ("+t[a]+")."})),i&&t[a]<h&&(this.trigger("warn",{message:n+" clamping PART-HOLD-BACK ("+t[a]+") to partTargetDuration * 2 ("+h+")."}),t[a]=h)}},C=function(e){function t(){var t;(t=e.call(this)||this).lineStream=new g,t.parseStream=new y,t.lineStream.pipe(t.parseStream);var r,i,n=p()(t),s=[],a={},o=!1,h=function(){},l={AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},c=0;t.manifest={allowCache:!0,discontinuityStarts:[],segments:[]};var d=0,_=0;return t.on("end",(function(){a.uri||!a.parts&&!a.preloadHints||(!a.map&&r&&(a.map=r),!a.key&&i&&(a.key=i),a.timeline||"number"!=typeof c||(a.timeline=c),t.manifest.preloadSegment=a)})),t.parseStream.on("data",(function(e){var t,p;({tag:function(){({version:function(){e.version&&(this.manifest.version=e.version)},"allow-cache":function(){this.manifest.allowCache=e.allowed,"allowed"in e||(this.trigger("info",{message:"defaulting allowCache to YES"}),this.manifest.allowCache=!0)},byterange:function(){var t={};"length"in e&&(a.byterange=t,t.length=e.length,"offset"in e||(e.offset=d)),"offset"in e&&(a.byterange=t,t.offset=e.offset),d=t.offset+t.length},endlist:function(){this.manifest.endList=!0},inf:function(){"mediaSequence"in this.manifest||(this.manifest.mediaSequence=0,this.trigger("info",{message:"defaulting media sequence to zero"})),"discontinuitySequence"in this.manifest||(this.manifest.discontinuitySequence=0,this.trigger("info",{message:"defaulting discontinuity sequence to zero"})),e.duration>0&&(a.duration=e.duration),0===e.duration&&(a.duration=.01,this.trigger("info",{message:"updating zero segment duration to a small value"})),this.manifest.segments=s},key:function(){if(e.attributes)if("NONE"!==e.attributes.METHOD)if(e.attributes.URI){if("urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed"===e.attributes.KEYFORMAT){return-1===["SAMPLE-AES","SAMPLE-AES-CTR","SAMPLE-AES-CENC"].indexOf(e.attributes.METHOD)?void this.trigger("warn",{message:"invalid key method provided for Widevine"}):("SAMPLE-AES-CENC"===e.attributes.METHOD&&this.trigger("warn",{message:"SAMPLE-AES-CENC is deprecated, please use SAMPLE-AES-CTR instead"}),"data:text/plain;base64,"!==e.attributes.URI.substring(0,23)?void this.trigger("warn",{message:"invalid key URI provided for Widevine"}):e.attributes.KEYID&&"0x"===e.attributes.KEYID.substring(0,2)?void(this.manifest.contentProtection={"com.widevine.alpha":{attributes:{schemeIdUri:e.attributes.KEYFORMAT,keyId:e.attributes.KEYID.substring(2)},pssh:Object(f.a)(e.attributes.URI.split(",")[1])}}):void this.trigger("warn",{message:"invalid key ID provided for Widevine"}))}e.attributes.METHOD||this.trigger("warn",{message:"defaulting key method to AES-128"}),i={method:e.attributes.METHOD||"AES-128",uri:e.attributes.URI},void 0!==e.attributes.IV&&(i.iv=e.attributes.IV)}else this.trigger("warn",{message:"ignoring key declaration without URI"});else i=null;else this.trigger("warn",{message:"ignoring key declaration without attribute list"})},"media-sequence":function(){isFinite(e.number)?this.manifest.mediaSequence=e.number:this.trigger("warn",{message:"ignoring invalid media sequence: "+e.number})},"discontinuity-sequence":function(){isFinite(e.number)?(this.manifest.discontinuitySequence=e.number,c=e.number):this.trigger("warn",{message:"ignoring invalid discontinuity sequence: "+e.number})},"playlist-type":function(){/VOD|EVENT/.test(e.playlistType)?this.manifest.playlistType=e.playlistType:this.trigger("warn",{message:"ignoring unknown playlist type: "+e.playlist})},map:function(){r={},e.uri&&(r.uri=e.uri),e.byterange&&(r.byterange=e.byterange)},"stream-inf":function(){this.manifest.playlists=s,this.manifest.mediaGroups=this.manifest.mediaGroups||l,e.attributes?(a.attributes||(a.attributes={}),u()(a.attributes,e.attributes)):this.trigger("warn",{message:"ignoring empty stream-inf attributes"})},media:function(){if(this.manifest.mediaGroups=this.manifest.mediaGroups||l,e.attributes&&e.attributes.TYPE&&e.attributes["GROUP-ID"]&&e.attributes.NAME){var r=this.manifest.mediaGroups[e.attributes.TYPE];r[e.attributes["GROUP-ID"]]=r[e.attributes["GROUP-ID"]]||{},t=r[e.attributes["GROUP-ID"]],(p={default:/yes/i.test(e.attributes.DEFAULT)}).default?p.autoselect=!0:p.autoselect=/yes/i.test(e.attributes.AUTOSELECT),e.attributes.LANGUAGE&&(p.language=e.attributes.LANGUAGE),e.attributes.URI&&(p.uri=e.attributes.URI),e.attributes["INSTREAM-ID"]&&(p.instreamId=e.attributes["INSTREAM-ID"]),e.attributes.CHARACTERISTICS&&(p.characteristics=e.attributes.CHARACTERISTICS),e.attributes.FORCED&&(p.forced=/yes/i.test(e.attributes.FORCED)),t[e.attributes.NAME]=p}else this.trigger("warn",{message:"ignoring incomplete or missing media group"})},discontinuity:function(){c+=1,a.discontinuity=!0,this.manifest.discontinuityStarts.push(s.length)},"program-date-time":function(){void 0===this.manifest.dateTimeString&&(this.manifest.dateTimeString=e.dateTimeString,this.manifest.dateTimeObject=e.dateTimeObject),a.dateTimeString=e.dateTimeString,a.dateTimeObject=e.dateTimeObject},targetduration:function(){!isFinite(e.duration)||e.duration<0?this.trigger("warn",{message:"ignoring invalid target duration: "+e.duration}):(this.manifest.targetDuration=e.duration,T.call(this,this.manifest))},start:function(){e.attributes&&!isNaN(e.attributes["TIME-OFFSET"])?this.manifest.start={timeOffset:e.attributes["TIME-OFFSET"],precise:e.attributes.PRECISE}:this.trigger("warn",{message:"ignoring start declaration without appropriate attribute list"})},"cue-out":function(){a.cueOut=e.data},"cue-out-cont":function(){a.cueOutCont=e.data},"cue-in":function(){a.cueIn=e.data},skip:function(){this.manifest.skip=b(e.attributes),this.warnOnMissingAttributes_("#EXT-X-SKIP",e.attributes,["SKIPPED-SEGMENTS"])},part:function(){var t=this;o=!0;var r=this.manifest.segments.length,i=b(e.attributes);a.parts=a.parts||[],a.parts.push(i),i.byterange&&(i.byterange.hasOwnProperty("offset")||(i.byterange.offset=_),_=i.byterange.offset+i.byterange.length);var n=a.parts.length-1;this.warnOnMissingAttributes_("#EXT-X-PART #"+n+" for segment #"+r,e.attributes,["URI","DURATION"]),this.manifest.renditionReports&&this.manifest.renditionReports.forEach((function(e,r){e.hasOwnProperty("lastPart")||t.trigger("warn",{message:"#EXT-X-RENDITION-REPORT #"+r+" lacks required attribute(s): LAST-PART"})}))},"server-control":function(){var t=this.manifest.serverControl=b(e.attributes);t.hasOwnProperty("canBlockReload")||(t.canBlockReload=!1,this.trigger("info",{message:"#EXT-X-SERVER-CONTROL defaulting CAN-BLOCK-RELOAD to false"})),T.call(this,this.manifest),t.canSkipDateranges&&!t.hasOwnProperty("canSkipUntil")&&this.trigger("warn",{message:"#EXT-X-SERVER-CONTROL lacks required attribute CAN-SKIP-UNTIL which is required when CAN-SKIP-DATERANGES is set"})},"preload-hint":function(){var t=this.manifest.segments.length,r=b(e.attributes),i=r.type&&"PART"===r.type;a.preloadHints=a.preloadHints||[],a.preloadHints.push(r),r.byterange&&(r.byterange.hasOwnProperty("offset")||(r.byterange.offset=i?_:0,i&&(_=r.byterange.offset+r.byterange.length)));var n=a.preloadHints.length-1;if(this.warnOnMissingAttributes_("#EXT-X-PRELOAD-HINT #"+n+" for segment #"+t,e.attributes,["TYPE","URI"]),r.type)for(var s=0;s<a.preloadHints.length-1;s++){var o=a.preloadHints[s];o.type&&(o.type===r.type&&this.trigger("warn",{message:"#EXT-X-PRELOAD-HINT #"+n+" for segment #"+t+" has the same TYPE "+r.type+" as preload hint #"+s}))}},"rendition-report":function(){var t=b(e.attributes);this.manifest.renditionReports=this.manifest.renditionReports||[],this.manifest.renditionReports.push(t);var r=this.manifest.renditionReports.length-1,i=["LAST-MSN","URI"];o&&i.push("LAST-PART"),this.warnOnMissingAttributes_("#EXT-X-RENDITION-REPORT #"+r,e.attributes,i)},"part-inf":function(){this.manifest.partInf=b(e.attributes),this.warnOnMissingAttributes_("#EXT-X-PART-INF",e.attributes,["PART-TARGET"]),this.manifest.partInf.partTarget&&(this.manifest.partTargetDuration=this.manifest.partInf.partTarget),T.call(this,this.manifest)}}[e.tagType]||h).call(n)},uri:function(){a.uri=e.uri,s.push(a),this.manifest.targetDuration&&!("duration"in a)&&(this.trigger("warn",{message:"defaulting segment duration to the target duration"}),a.duration=this.manifest.targetDuration),i&&(a.key=i),a.timeline=c,r&&(a.map=r),_=0,a={}},comment:function(){},custom:function(){e.segment?(a.custom=a.custom||{},a.custom[e.customType]=e.data):(this.manifest.custom=this.manifest.custom||{},this.manifest.custom[e.customType]=e.data)}})[e.type].call(n)})),t}h()(t,e);var r=t.prototype;return r.warnOnMissingAttributes_=function(e,t,r){var i=[];r.forEach((function(e){t.hasOwnProperty(e)||i.push(e)})),i.length&&this.trigger("warn",{message:e+" lacks required attribute(s): "+i.join(", ")})},r.push=function(e){this.lineStream.push(e)},r.end=function(){this.lineStream.push("\n"),this.trigger("end")},r.addParser=function(e){this.parseStream.addParser(e)},r.addTagMapper=function(e){this.parseStream.addTagMapper(e)},t}(l),R=r(32),E=r(33);class w{constructor(){this.manifest=null}parse(e){var t=new C;return t.push(e),t.end(),this.manifest=t.manifest,this}getPlaylist(e,t){var r=this.manifest,i=r.playlistType,n=r.mediaSequence,s=r.targetDuration,a=w.getFragments(r,e,t);return{playlistType:i?i.toUpperCase():"LIVE",mediaSequence:n,targetDuration:s,endList:n+a.length,fragments:a,level:t}}getPlaylists(e){var t=this.manifest.playlists;if(!t)return[];var r=[];return t.forEach(((t,i)=>{r.push({url:Object(E.buildAbsoluteURL)(e,t.uri),curLevel:i})})),r}static getFragments(e,t,r){var i=e.segments,n=e.mediaSequence,s=[],a=n;return i.forEach((e=>{var i=new R.a;i.baseUrl=t,i.uri=e.uri,i.level=r,i.sn=a,a+=1,s.push(i)})),s}}var S=w,P=r(3);var I=new class{constructor(){this.hlsp2p=null}init(e){this.hlsp2p=e}destroy(){this.hlsp2p=null}load(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return n()(a.a.mark((function i(){var n,s,o,h;return a.a.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=3,t.download(e);case 3:if(n=i.sent){i.next=6;break}return i.abrupt("return");case 6:if((s=new S).parse(n),!(o=s.getPlaylists(e)).length){i.next=12;break}return o.forEach((e=>{var r=e.url,i=e.curLevel;t.load(r,i)})),i.abrupt("return");case 12:"VOD"!==(h=s.parse(n).getPlaylist(e,r)).playlistType&&window.console.error("[hlsp2p] 当前通用loader仅支持HLS点播"),t.hlsp2p.trigger(P.a.LEVEL_LOADED,h);case 15:case"end":return i.stop()}}),i)})))()}download(e){return n()(a.a.mark((function t(){var r;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch(e);case 3:if(!(r=t.sent).ok){t.next=6;break}return t.abrupt("return",r.text());case 6:t.next=12;break;case 9:t.prev=9,t.t0=t.catch(0);case 12:return t.abrupt("return","");case 13:case"end":return t.stop()}}),t,null,[[0,9]])})))()}};t.a=I},function(e,t){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(e){"object"==typeof window&&(r=window)}e.exports=r},function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));class i{constructor(){this._dataLength=0,this._bufferLength=0,this._state=new Int32Array(4),this._buffer=new ArrayBuffer(68),this._buffer8=new Uint8Array(this._buffer,0,68),this._buffer32=new Uint32Array(this._buffer,0,17),this.start()}static hashStr(e,t=!1){return this.onePassHasher.start().appendStr(e).end(t)}static hashAsciiStr(e,t=!1){return this.onePassHasher.start().appendAsciiStr(e).end(t)}static _hex(e){const t=i.hexChars,r=i.hexOut;let n,s,a,o;for(o=0;o<4;o+=1)for(s=8*o,n=e[o],a=0;a<8;a+=2)r[s+1+a]=t.charAt(15&n),n>>>=4,r[s+0+a]=t.charAt(15&n),n>>>=4;return r.join("")}static _md5cycle(e,t){let r=e[0],i=e[1],n=e[2],s=e[3];r+=(i&n|~i&s)+t[0]-680876936|0,r=(r<<7|r>>>25)+i|0,s+=(r&i|~r&n)+t[1]-389564586|0,s=(s<<12|s>>>20)+r|0,n+=(s&r|~s&i)+t[2]+606105819|0,n=(n<<17|n>>>15)+s|0,i+=(n&s|~n&r)+t[3]-1044525330|0,i=(i<<22|i>>>10)+n|0,r+=(i&n|~i&s)+t[4]-176418897|0,r=(r<<7|r>>>25)+i|0,s+=(r&i|~r&n)+t[5]+1200080426|0,s=(s<<12|s>>>20)+r|0,n+=(s&r|~s&i)+t[6]-1473231341|0,n=(n<<17|n>>>15)+s|0,i+=(n&s|~n&r)+t[7]-45705983|0,i=(i<<22|i>>>10)+n|0,r+=(i&n|~i&s)+t[8]+1770035416|0,r=(r<<7|r>>>25)+i|0,s+=(r&i|~r&n)+t[9]-1958414417|0,s=(s<<12|s>>>20)+r|0,n+=(s&r|~s&i)+t[10]-42063|0,n=(n<<17|n>>>15)+s|0,i+=(n&s|~n&r)+t[11]-1990404162|0,i=(i<<22|i>>>10)+n|0,r+=(i&n|~i&s)+t[12]+1804603682|0,r=(r<<7|r>>>25)+i|0,s+=(r&i|~r&n)+t[13]-40341101|0,s=(s<<12|s>>>20)+r|0,n+=(s&r|~s&i)+t[14]-1502002290|0,n=(n<<17|n>>>15)+s|0,i+=(n&s|~n&r)+t[15]+1236535329|0,i=(i<<22|i>>>10)+n|0,r+=(i&s|n&~s)+t[1]-165796510|0,r=(r<<5|r>>>27)+i|0,s+=(r&n|i&~n)+t[6]-1069501632|0,s=(s<<9|s>>>23)+r|0,n+=(s&i|r&~i)+t[11]+643717713|0,n=(n<<14|n>>>18)+s|0,i+=(n&r|s&~r)+t[0]-373897302|0,i=(i<<20|i>>>12)+n|0,r+=(i&s|n&~s)+t[5]-701558691|0,r=(r<<5|r>>>27)+i|0,s+=(r&n|i&~n)+t[10]+38016083|0,s=(s<<9|s>>>23)+r|0,n+=(s&i|r&~i)+t[15]-660478335|0,n=(n<<14|n>>>18)+s|0,i+=(n&r|s&~r)+t[4]-405537848|0,i=(i<<20|i>>>12)+n|0,r+=(i&s|n&~s)+t[9]+568446438|0,r=(r<<5|r>>>27)+i|0,s+=(r&n|i&~n)+t[14]-1019803690|0,s=(s<<9|s>>>23)+r|0,n+=(s&i|r&~i)+t[3]-187363961|0,n=(n<<14|n>>>18)+s|0,i+=(n&r|s&~r)+t[8]+1163531501|0,i=(i<<20|i>>>12)+n|0,r+=(i&s|n&~s)+t[13]-1444681467|0,r=(r<<5|r>>>27)+i|0,s+=(r&n|i&~n)+t[2]-51403784|0,s=(s<<9|s>>>23)+r|0,n+=(s&i|r&~i)+t[7]+1735328473|0,n=(n<<14|n>>>18)+s|0,i+=(n&r|s&~r)+t[12]-1926607734|0,i=(i<<20|i>>>12)+n|0,r+=(i^n^s)+t[5]-378558|0,r=(r<<4|r>>>28)+i|0,s+=(r^i^n)+t[8]-2022574463|0,s=(s<<11|s>>>21)+r|0,n+=(s^r^i)+t[11]+1839030562|0,n=(n<<16|n>>>16)+s|0,i+=(n^s^r)+t[14]-35309556|0,i=(i<<23|i>>>9)+n|0,r+=(i^n^s)+t[1]-1530992060|0,r=(r<<4|r>>>28)+i|0,s+=(r^i^n)+t[4]+1272893353|0,s=(s<<11|s>>>21)+r|0,n+=(s^r^i)+t[7]-155497632|0,n=(n<<16|n>>>16)+s|0,i+=(n^s^r)+t[10]-1094730640|0,i=(i<<23|i>>>9)+n|0,r+=(i^n^s)+t[13]+681279174|0,r=(r<<4|r>>>28)+i|0,s+=(r^i^n)+t[0]-358537222|0,s=(s<<11|s>>>21)+r|0,n+=(s^r^i)+t[3]-722521979|0,n=(n<<16|n>>>16)+s|0,i+=(n^s^r)+t[6]+76029189|0,i=(i<<23|i>>>9)+n|0,r+=(i^n^s)+t[9]-640364487|0,r=(r<<4|r>>>28)+i|0,s+=(r^i^n)+t[12]-421815835|0,s=(s<<11|s>>>21)+r|0,n+=(s^r^i)+t[15]+530742520|0,n=(n<<16|n>>>16)+s|0,i+=(n^s^r)+t[2]-995338651|0,i=(i<<23|i>>>9)+n|0,r+=(n^(i|~s))+t[0]-198630844|0,r=(r<<6|r>>>26)+i|0,s+=(i^(r|~n))+t[7]+1126891415|0,s=(s<<10|s>>>22)+r|0,n+=(r^(s|~i))+t[14]-1416354905|0,n=(n<<15|n>>>17)+s|0,i+=(s^(n|~r))+t[5]-57434055|0,i=(i<<21|i>>>11)+n|0,r+=(n^(i|~s))+t[12]+1700485571|0,r=(r<<6|r>>>26)+i|0,s+=(i^(r|~n))+t[3]-1894986606|0,s=(s<<10|s>>>22)+r|0,n+=(r^(s|~i))+t[10]-1051523|0,n=(n<<15|n>>>17)+s|0,i+=(s^(n|~r))+t[1]-2054922799|0,i=(i<<21|i>>>11)+n|0,r+=(n^(i|~s))+t[8]+1873313359|0,r=(r<<6|r>>>26)+i|0,s+=(i^(r|~n))+t[15]-30611744|0,s=(s<<10|s>>>22)+r|0,n+=(r^(s|~i))+t[6]-1560198380|0,n=(n<<15|n>>>17)+s|0,i+=(s^(n|~r))+t[13]+1309151649|0,i=(i<<21|i>>>11)+n|0,r+=(n^(i|~s))+t[4]-145523070|0,r=(r<<6|r>>>26)+i|0,s+=(i^(r|~n))+t[11]-1120210379|0,s=(s<<10|s>>>22)+r|0,n+=(r^(s|~i))+t[2]+718787259|0,n=(n<<15|n>>>17)+s|0,i+=(s^(n|~r))+t[9]-343485551|0,i=(i<<21|i>>>11)+n|0,e[0]=r+e[0]|0,e[1]=i+e[1]|0,e[2]=n+e[2]|0,e[3]=s+e[3]|0}start(){return this._dataLength=0,this._bufferLength=0,this._state.set(i.stateIdentity),this}appendStr(e){const t=this._buffer8,r=this._buffer32;let n,s,a=this._bufferLength;for(s=0;s<e.length;s+=1){if(n=e.charCodeAt(s),n<128)t[a++]=n;else if(n<2048)t[a++]=192+(n>>>6),t[a++]=63&n|128;else if(n<55296||n>56319)t[a++]=224+(n>>>12),t[a++]=n>>>6&63|128,t[a++]=63&n|128;else{if(n=1024*(n-55296)+(e.charCodeAt(++s)-56320)+65536,n>1114111)throw new Error("Unicode standard supports code points up to U+10FFFF");t[a++]=240+(n>>>18),t[a++]=n>>>12&63|128,t[a++]=n>>>6&63|128,t[a++]=63&n|128}a>=64&&(this._dataLength+=64,i._md5cycle(this._state,r),a-=64,r[0]=r[16])}return this._bufferLength=a,this}appendAsciiStr(e){const t=this._buffer8,r=this._buffer32;let n,s=this._bufferLength,a=0;for(;;){for(n=Math.min(e.length-a,64-s);n--;)t[s++]=e.charCodeAt(a++);if(s<64)break;this._dataLength+=64,i._md5cycle(this._state,r),s=0}return this._bufferLength=s,this}appendByteArray(e){const t=this._buffer8,r=this._buffer32;let n,s=this._bufferLength,a=0;for(;;){for(n=Math.min(e.length-a,64-s);n--;)t[s++]=e[a++];if(s<64)break;this._dataLength+=64,i._md5cycle(this._state,r),s=0}return this._bufferLength=s,this}getState(){const e=this._state;return{buffer:String.fromCharCode.apply(null,Array.from(this._buffer8)),buflen:this._bufferLength,length:this._dataLength,state:[e[0],e[1],e[2],e[3]]}}setState(e){const t=e.buffer,r=e.state,i=this._state;let n;for(this._dataLength=e.length,this._bufferLength=e.buflen,i[0]=r[0],i[1]=r[1],i[2]=r[2],i[3]=r[3],n=0;n<t.length;n+=1)this._buffer8[n]=t.charCodeAt(n)}end(e=!1){const t=this._bufferLength,r=this._buffer8,n=this._buffer32,s=1+(t>>2);this._dataLength+=t;const a=8*this._dataLength;if(r[t]=128,r[t+1]=r[t+2]=r[t+3]=0,n.set(i.buffer32Identity.subarray(s),s),t>55&&(i._md5cycle(this._state,n),n.set(i.buffer32Identity)),a<=4294967295)n[14]=a;else{const e=a.toString(16).match(/(.*?)(.{0,8})$/);if(null===e)return;const t=parseInt(e[2],16),r=parseInt(e[1],16)||0;n[14]=t,n[15]=r}return i._md5cycle(this._state,n),e?this._state:i._hex(this._state)}}if(i.stateIdentity=new Int32Array([1732584193,-271733879,-1732584194,271733878]),i.buffer32Identity=new Int32Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),i.hexChars="0123456789abcdef",i.hexOut=[],i.onePassHasher=new i,"5d41402abc4b2a76b9719d911017c592"!==i.hashStr("hello"))throw new Error("Md5 self test failed.");function n(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{channelIdIncludeHost:!1,channelIdIncludeSearch:!1},r=t.channelIdIncludeHost,n=t.channelIdIncludeSearch;if(!e)return null;var s=new URL(e),a="";return r&&(a+=s.host),a+=s.pathname,n&&(a+=s.search),i.hashStr(a)}},function(e,t){function r(e,t,r,i,n,s,a){try{var o=e[s](a),h=o.value}catch(e){return void r(e)}o.done?t(h):Promise.resolve(h).then(i,n)}e.exports=function(e){return function(){var t=this,i=arguments;return new Promise((function(n,s){var a=e.apply(t,i);function o(e){r(a,n,s,o,h,"next",e)}function h(e){r(a,n,s,o,h,"throw",e)}o(void 0)}))}},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t){function r(){return e.exports=r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e},e.exports.default=e.exports,e.exports.__esModule=!0,r.apply(this,arguments)}e.exports=r,e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,r){(function(t){var r;r="undefined"!=typeof window?window:void 0!==t?t:"undefined"!=typeof self?self:{},e.exports=r}).call(this,r(41))},function(e,t,r){"use strict";var i=r(14),n=r(11),s=r(0),a=r(3),o=r(2),h=r(7);var l=new class{constructor(){this.TAG="StallDetector"}init(e){this._resetReportData(),this._played=!1,e.once(a.a.FRAG_LOADED,(()=>{n.a.on("waiting",this._onWaiting.bind(this))})),n.a.on("canplaythrough",this._onCanplaythrough.bind(this))}destroy(){this._resetReportData()}_onWaiting(){this._played?(h.a.warn(`stuck occur buffer:${n.a.bufferLength} ${o.a.curReqSn}-${o.a.curReqLevel}`),this._reportData.stuckCount+=1):h.a.warn(`播放未开始, 忽略 stuck occur buffer:${n.a.bufferLength} ${o.a.curReqSn}-${o.a.curReqLevel}`)}_onCanplaythrough(){h.a.warn(`Canplaythrough buffer:${n.a.bufferLength} ${o.a.curReqSn}-${o.a.curReqLevel}`),this._played=!0}_resetReportData(){this._reportData={stuckCount:0}}get reportData(){var e={[s.a.STUCK_COUNT]:this._reportData.stuckCount};return this._resetReportData(),e}},c=r(1);var u=new class{constructor(){this._lastReport=Date.now()}init(){this.resetReportData(),this._lastReport=Date.now(),this._reportData[s.a.REQUEST]=1}destroy(){this.resetReportData()}resetReportData(){this._reportData={[s.a.PLAY_STARTED]:Math.floor((Date.now()-c.a.loadTime)/1e3)}}get reportData(){var e=this._reportData;return e[s.a.SESSION_TIME]=Math.floor((Date.now()-this._lastReport)/1e3),e[s.a.BUFFER_LENGTH]=n.a.bufferLength,e[s.a.PAUSED]=n.a.paused,this._lastReport=Date.now(),this.resetReportData(),e}};var d=new class{constructor(){this.TAG="MediaController"}init(e){i.a.registerModule(this.TAG,this.reportCallback.bind(this)),n.a.init({videoId:c.a.videoId}),u.init(),l.init(e)}destroy(){n.a.destroy(),u.destroy(),l.destroy()}reportCallback(){var e={},t=l.reportData,r=u.reportData;return Object.assign(e,t),Object.assign(e,r),e}};t.a=d},function(e,t,r){"use strict";var i=r(3),n=r(32),s=r(19),a=r(17),o=r(20),h=r(2),l=r(6),c=r.n(l);var u=class{constructor(){this.TAG="HlsjsFLoader",this.id="",this.resp=new o.a,this.stats=this.resp._stats}destroy(){this.abort()}abort(){a.a.abort(this.id)}load(e,t,r){c.a.info(`[${this.TAG}] [load] request from hls.js ${this.id}`);var i=new s.a(e,t,r);this.resp.update(e,t,r),this.id=i.id,this.resp.id=i.id,this.resp.from="player";var n=e.frag,o=n.sn,l=n.level;h.a.curReqSn=o,h.a.curReqLevel=l,c.a.info(`[${this.TAG}] [load] request from hls.js ${this.id}`),setTimeout((()=>{a.a.load(this.id,i,this.resp)}))}},d=r(24),p=r(1),f=r(7);class g extends d.a{constructor(){super(),this.TAG="HlsjsAdapter",this.hlsp2p=null,this.hlsjs=null,this.playerOriginalConfig={maxBufferLength:30,maxMaxBufferLength:600}}destroy(){this._detach(),this.hlsp2p=null,this.hlsjs=null}init(e,t){this.hlsp2p=e,this.hlsjs=t,this.onHlsManifestParsed=this.hlsManifestParsed.bind(this),this.onHlsLevelLoaded=this.hlsLevelLoaded.bind(this),this._attach()}reset(){this._attach(),this._detach()}_attach(){this._setVODTimer(),this.playerOriginalConfig.maxBufferLength=this.hlsjs.maxBufferLength,this.playerOriginalConfig.maxMaxBufferLength=this.hlsjs.maxMaxBufferLength,this.hlsjs.config.fLoader=u,this.hlsjs.on("hlsManifestParsed",this.onHlsManifestParsed),this.hlsjs.on("hlsLevelLoaded",this.onHlsLevelLoaded)}hlsManifestParsed(e,t){this.hlsp2p.trigger(i.a.HlsManifestParsed,t)}hlsLevelLoaded(e,t){var r=t.details.fragments,s=t.details,a=[];r.forEach((e=>{f.a.log(`[Hlsjs LevelLoaded] ${e.sn}-${e.level} duration: ${e.duration}`);var t=new n.a;t.sn=e.sn,t.level=e.level,t.uri=e.relurl,t.baseUrl=e.baseurl,t.duration=Math.round(e.duration),a.push(t)})),this.hlsp2p.trigger(i.a.LEVEL_LOADED,{level:t.level,playlistType:!0===s.live?"LIVE":"VOD",mediaSequence:s.startSN,targetDuration:s.targetduration,endList:s.endSN,fragments:a})}setMaxBufferLength(e){h.a.hlsjs.config.maxBufferLength=e,h.a.hlsjs.config.maxMaxBufferLength=e}_detach(){this._clearVODTimer(),this.hlsjs.config.maxBufferLength=this.playerOriginalConfig.maxBufferLength,this.hlsjs.config.maxMaxBufferLength=this.playerOriginalConfig.maxMaxBufferLength,this.hlsjs.config.fLoader=void 0,this.hlsjs.off("hlsManifestParsed",this.onHlsManifestParsed),this.hlsjs.off("hlsLevelLoaded",this.onHlsLevelLoaded)}_setVODTimer(){"VOD"===p.a.videoType&&(this._clearVODTimer(),this._vodTimer=setTimeout((()=>{this._copyVODPlaylistFromHlsjs()}),1e4))}_clearVODTimer(){this._vodTimer&&(clearInterval(this._vodTimer),this._vodTimer=null)}_copyVODPlaylistFromHlsjs(){if(this.hlsjs){var e=this.hlsjs.levels;if(e&&e.length)try{e.forEach((e=>{e.details&&this.hlsLevelLoaded("hlsLevelLoaded",e)}))}catch(e){c.a.error("error in _copyVODPlaylistFromHlsjs",e)}}}}var _=new g;t.a=_},function(e,t,r){"use strict";class i extends EventTarget{static create(){return new i}constructor(e){super(),this.responseType="arraybuffer",this.response=new ArrayBuffer,this.responseText=void 0,this.responseURL=e,this.status=0}dispatchLoadOK(e){var t=e.loaded,r=e.total;this.status=200,this.dispatchEvent(new ProgressEvent("loadstart",{loaded:t,total:r,lengthComputable:!0})),this.dispatchEvent(new ProgressEvent("progress",{loaded:t,total:r,lengthComputable:!0})),this.dispatchEvent(new ProgressEvent("load",{loaded:t,total:r,lengthComputable:!0})),this.dispatchEvent(new ProgressEvent("loadend",{loaded:t,total:r,lengthComputable:!0}))}dispatchTimeout(){this.dispatchEvent(new ProgressEvent("loadstart",{loaded:0,total:0})),this.dispatchEvent(new ProgressEvent("loadend",{loaded:0,total:0}))}dispatchError(){this.dispatchEvent(new ProgressEvent("loadstart",{loaded:0,total:0})),this.dispatchEvent(new ProgressEvent("error",{loaded:0,total:0})),this.dispatchEvent(new ProgressEvent("loadend",{loaded:0,total:0}))}}var n=i,s=r(26),a=r(24),o=function(e,t,r,i){var n,s="arraybuffer"===e.responseType?e.response:e.responseText;!t&&s&&(e.responseTime=Date.now(),e.roundTripTime=e.responseTime-e.requestTime,e.bytesReceived=s.byteLength||s.length,e.bandwidth||(e.bandwidth=Math.floor(e.bytesReceived/e.roundTripTime*8*1e3))),r.headers&&(e.responseHeaders=r.headers),t&&"ETIMEDOUT"===t.code&&(e.timedout=!0),t||e.aborted||200===r.statusCode||206===r.statusCode||0===r.statusCode||(n=new Error(`XHR Failed with a response of: ${e&&(s||e.responseText)}`)),i(n,e)};class h extends a.a{constructor(){super(),this.TAG="VideojsHttpStreamingAdapter"}p2pXhrFactory(){return function(e,t){var r=n.create();r.abort=function(){r.aborted=!0},r.uri=e.uri,r.requestTime=Date.now();var i={url:e.uri},a={onSuccess:(e,i)=>{r.response=e.slice(0),r.status=200,r.dispatchLoadOK({loaded:e.byteLength,total:e.byteLength}),i.trequest&&(r.requestTime=i.trequest),o(r,null,{statusCode:200},t)},onError:()=>{r.dispatchError(),o(r,{},{},t)},onTimeout:()=>{r.dispatchTimeout(),o(r,{code:"ETIMEDOUT"},{},t)}};if(s.a)return s.a.loadTs(i,a),r}}}var l=new h;t.a=l},function(e,t,r){"use strict";var i=r(15),n=r.n(i);t.a=class{constructor(){this.TAG="Observer",this.observer=new n.a}destroy(){this.observer.removeAllListeners()}on(e,t){this.observer.on(e,t)}once(e,t){this.observer.once(e,t)}trigger(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];this.observer.emit(e,...r)}off(e,t){this.observer.off(e,t)}eventNames(){var e=this.observer._events;return e?Object.keys(e):[]}}},function(e,t,r){var i;!function(n,s){"use strict";var a="function",o="undefined",h="object",l="string",c="model",u="name",d="type",p="vendor",f="version",g="architecture",_="console",v="mobile",m="tablet",y="smarttv",b="wearable",T="embedded",C="Amazon",R="Apple",E="ASUS",w="BlackBerry",S="Firefox",P="Google",I="Huawei",L="LG",k="Microsoft",A="Motorola",D="Opera",N="Samsung",O="Sharp",x="Sony",U="Xiaomi",B="Zebra",M="Facebook",q=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},$=function(e,t){return typeof e===l&&-1!==F(t).indexOf(F(e))},F=function(e){return e.toLowerCase()},G=function(e,t){if(typeof e===l)return e=e.replace(/^\s\s*/,""),typeof t===o?e:e.substring(0,350)},H=function(e,t){for(var r,i,n,o,l,c,u=0;u<t.length&&!l;){var d=t[u],p=t[u+1];for(r=i=0;r<d.length&&!l;)if(l=d[r++].exec(e))for(n=0;n<p.length;n++)c=l[++i],typeof(o=p[n])===h&&o.length>0?2===o.length?typeof o[1]==a?this[o[0]]=o[1].call(this,c):this[o[0]]=o[1]:3===o.length?typeof o[1]!==a||o[1].exec&&o[1].test?this[o[0]]=c?c.replace(o[1],o[2]):s:this[o[0]]=c?o[1].call(this,c,o[2]):s:4===o.length&&(this[o[0]]=c?o[3].call(this,c.replace(o[1],o[2])):s):this[o]=c||s;u+=2}},j=function(e,t){for(var r in t)if(typeof t[r]===h&&t[r].length>0){for(var i=0;i<t[r].length;i++)if($(t[r][i],e))return"?"===r?s:r}else if($(t[r],e))return"?"===r?s:r;return e},Y={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},V={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[f,[u,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[f,[u,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[u,f],[/opios[\/ ]+([\w\.]+)/i],[f,[u,"Opera Mini"]],[/\bopr\/([\w\.]+)/i],[f,[u,D]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(weibo)__([\d\.]+)/i],[u,f],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[f,[u,"UCBrowser"]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[f,[u,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[f,[u,"WeChat"]],[/konqueror\/([\w\.]+)/i],[f,[u,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[f,[u,"IE"]],[/yabrowser\/([\w\.]+)/i],[f,[u,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[u,/(.+)/,"$1 Secure Browser"],f],[/\bfocus\/([\w\.]+)/i],[f,[u,"Firefox Focus"]],[/\bopt\/([\w\.]+)/i],[f,[u,"Opera Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[f,[u,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[f,[u,"Dolphin"]],[/coast\/([\w\.]+)/i],[f,[u,"Opera Coast"]],[/miuibrowser\/([\w\.]+)/i],[f,[u,"MIUI Browser"]],[/fxios\/([-\w\.]+)/i],[f,[u,S]],[/\bqihu|(qi?ho?o?|360)browser/i],[[u,"360 Browser"]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[u,/(.+)/,"$1 Browser"],f],[/(comodo_dragon)\/([\w\.]+)/i],[[u,/_/g," "],f],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[u,f],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[u],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[u,M],f],[/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[u,f],[/\bgsa\/([\w\.]+) .*safari\//i],[f,[u,"GSA"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[f,[u,"Chrome Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[u,"Chrome WebView"],f],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[f,[u,"Android Browser"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[u,f],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[f,[u,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[f,u],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[u,[f,j,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[u,f],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[u,"Netscape"],f],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[f,[u,"Firefox Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[u,f],[/(cobalt)\/([\w\.]+)/i],[u,[f,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,F]],[/((?:i[346]|x)86)[;\)]/i],[[g,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[g,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[g,/ower/,"",F]],[/(sun4\w)[;\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[g,F]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[c,[p,N],[d,m]],[/\b((?:s[cgp]h|gt|sm)-\w+|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[c,[p,N],[d,v]],[/\((ip(?:hone|od)[\w ]*);/i],[c,[p,R],[d,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[c,[p,R],[d,m]],[/(macintosh);/i],[c,[p,R]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[c,[p,I],[d,m]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[c,[p,I],[d,v]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[c,/_/g," "],[p,U],[d,v]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[c,/_/g," "],[p,U],[d,m]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[c,[p,"OPPO"],[d,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[c,[p,"Vivo"],[d,v]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[c,[p,"Realme"],[d,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[c,[p,A],[d,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[c,[p,A],[d,m]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[c,[p,L],[d,m]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[c,[p,L],[d,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[c,[p,"Lenovo"],[d,m]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[c,/_/g," "],[p,"Nokia"],[d,v]],[/(pixel c)\b/i],[c,[p,P],[d,m]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[c,[p,P],[d,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[c,[p,x],[d,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[c,"Xperia Tablet"],[p,x],[d,m]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[c,[p,"OnePlus"],[d,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[c,[p,C],[d,m]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[c,/(.+)/g,"Fire Phone $1"],[p,C],[d,v]],[/(playbook);[-\w\),; ]+(rim)/i],[c,p,[d,m]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[c,[p,w],[d,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[c,[p,E],[d,m]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[c,[p,E],[d,v]],[/(nexus 9)/i],[c,[p,"HTC"],[d,m]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic|sony(?!-bra))[-_ ]?([-\w]*)/i],[p,[c,/_/g," "],[d,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[c,[p,"Acer"],[d,m]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[c,[p,"Meizu"],[d,v]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[c,[p,O],[d,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[p,c,[d,v]],[/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[p,c,[d,m]],[/(surface duo)/i],[c,[p,k],[d,m]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[c,[p,"Fairphone"],[d,v]],[/(u304aa)/i],[c,[p,"AT&T"],[d,v]],[/\bsie-(\w*)/i],[c,[p,"Siemens"],[d,v]],[/\b(rct\w+) b/i],[c,[p,"RCA"],[d,m]],[/\b(venue[\d ]{2,7}) b/i],[c,[p,"Dell"],[d,m]],[/\b(q(?:mv|ta)\w+) b/i],[c,[p,"Verizon"],[d,m]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[c,[p,"Barnes & Noble"],[d,m]],[/\b(tm\d{3}\w+) b/i],[c,[p,"NuVision"],[d,m]],[/\b(k88) b/i],[c,[p,"ZTE"],[d,m]],[/\b(nx\d{3}j) b/i],[c,[p,"ZTE"],[d,v]],[/\b(gen\d{3}) b.+49h/i],[c,[p,"Swiss"],[d,v]],[/\b(zur\d{3}) b/i],[c,[p,"Swiss"],[d,m]],[/\b((zeki)?tb.*\b) b/i],[c,[p,"Zeki"],[d,m]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[p,"Dragon Touch"],c,[d,m]],[/\b(ns-?\w{0,9}) b/i],[c,[p,"Insignia"],[d,m]],[/\b((nxa|next)-?\w{0,9}) b/i],[c,[p,"NextBook"],[d,m]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[p,"Voice"],c,[d,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[p,"LvTel"],c,[d,v]],[/\b(ph-1) /i],[c,[p,"Essential"],[d,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[c,[p,"Envizen"],[d,m]],[/\b(trio[-\w\. ]+) b/i],[c,[p,"MachSpeed"],[d,m]],[/\btu_(1491) b/i],[c,[p,"Rotor"],[d,m]],[/(shield[\w ]+) b/i],[c,[p,"Nvidia"],[d,m]],[/(sprint) (\w+)/i],[p,c,[d,v]],[/(kin\.[onetw]{3})/i],[[c,/\./g," "],[p,k],[d,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[c,[p,B],[d,m]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[c,[p,B],[d,v]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[p,c,[d,_]],[/droid.+; (shield) bui/i],[c,[p,"Nvidia"],[d,_]],[/(playstation [345portablevi]+)/i],[c,[p,x],[d,_]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[c,[p,k],[d,_]],[/smart-tv.+(samsung)/i],[p,[d,y]],[/hbbtv.+maple;(\d+)/i],[[c,/^/,"SmartTV"],[p,N],[d,y]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[p,L],[d,y]],[/(apple) ?tv/i],[p,[c,"Apple TV"],[d,y]],[/crkey/i],[[c,"Chromecast"],[p,P],[d,y]],[/droid.+aft(\w)( bui|\))/i],[c,[p,C],[d,y]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[c,[p,O],[d,y]],[/(bravia[\w ]+)( bui|\))/i],[c,[p,x],[d,y]],[/(mitv-\w{5}) bui/i],[c,[p,U],[d,y]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w ]*; *(\w[^;]*);([^;]*)/i],[[p,G],[c,G],[d,y]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[d,y]],[/((pebble))app/i],[p,c,[d,b]],[/droid.+; (glass) \d/i],[c,[p,P],[d,b]],[/droid.+; (wt63?0{2,3})\)/i],[c,[p,B],[d,b]],[/(quest( 2)?)/i],[c,[p,M],[d,b]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[p,[d,T]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[c,[d,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[c,[d,m]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[d,m]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[d,v]],[/(android[-\w\. ]{0,9});.+buil/i],[c,[p,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[f,[u,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[f,[u,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i],[u,f],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[f,u]],os:[[/microsoft (windows) (vista|xp)/i],[u,f],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[u,[f,j,Y]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[u,"Windows"],[f,j,Y]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/cfnetwork\/.+darwin/i],[[f,/_/g,"."],[u,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[u,"Mac OS"],[f,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[f,u],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[u,f],[/\(bb(10);/i],[f,[u,w]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[f,[u,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[f,[u,"Firefox OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[f,[u,"webOS"]],[/crkey\/([\d\.]+)/i],[f,[u,"Chromecast"]],[/(cros) [\w]+ ([\w\.]+\w)/i],[[u,"Chromium OS"],f],[/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[u,f],[/(sunos) ?([\w\.\d]*)/i],[[u,"Solaris"],f],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux)/i,/(unix) ?([\w\.]*)/i],[u,f]]},K=function(e,t){if(typeof e===h&&(t=e,e=s),!(this instanceof K))return new K(e,t).getResult();var r=e||(typeof n!==o&&n.navigator&&n.navigator.userAgent?n.navigator.userAgent:""),i=t?function(e,t){var r={};for(var i in e)t[i]&&t[i].length%2==0?r[i]=t[i].concat(e[i]):r[i]=e[i];return r}(V,t):V;return this.getBrowser=function(){var e,t={};return t.name=s,t.version=s,H.call(t,r,i.browser),t.major=typeof(e=t.version)===l?e.replace(/[^\d\.]/g,"").split(".")[0]:s,t},this.getCPU=function(){var e={};return e.architecture=s,H.call(e,r,i.cpu),e},this.getDevice=function(){var e={};return e.vendor=s,e.model=s,e.type=s,H.call(e,r,i.device),e},this.getEngine=function(){var e={};return e.name=s,e.version=s,H.call(e,r,i.engine),e},this.getOS=function(){var e={};return e.name=s,e.version=s,H.call(e,r,i.os),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(e){return r=typeof e===l&&e.length>350?G(e,350):e,this},this.setUA(r),this};K.VERSION="1.0.33",K.BROWSER=q([u,f,"major"]),K.CPU=q([g]),K.DEVICE=q([c,p,d,_,v,y,m,b,T]),K.ENGINE=K.OS=q([u,f]),typeof t!==o?(typeof e!==o&&e.exports&&(t=e.exports=K),t.UAParser=K):r(58)?(i=function(){return K}.call(t,r,t,e))===s||(e.exports=i):typeof n!==o&&(n.UAParser=K);var z=typeof n!==o&&(n.jQuery||n.Zepto);if(z&&!z.ua){var X=new K;z.ua=X.getResult(),z.ua.get=function(){return X.getUA()},z.ua.set=function(e){X.setUA(e);var t=X.getResult();for(var r in t)z.ua[r]=t[r]}}}("object"==typeof window?window:this)},function(e,t){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return s}));var i=r(45),n=r.n(i);function s(t){for(var r,i=(r=t,n.a.atob?n.a.atob(r):e.from(r,"base64").toString("binary")),s=new Uint8Array(i.length),a=0;a<i.length;a++)s[a]=i.charCodeAt(a);return s}}).call(this,r(66).Buffer)},function(e,t,r){e.exports=function(){"use strict";function e(e){return(e=Array.isArray(e)?e:[e]).map((function(e,t){return Object.getOwnPropertyNames(e).map((function(r){return i(r)+"["+t+"]="+(void 0===e[r]?"":i(e[r]))})).join("&")})).join("&")+(e.length?"&count="+e.length:"")}var t,r=(n.prototype.indexOf=function(e,t){for(var r=0;r<e.length;r++)if(e[r].callback===t)return r;return-1},n.prototype.on=function(e,t,r){if(void 0===r&&(r=0),this){var i=this.EventsList[e];return i||(this.EventsList[e]=[],i=this.EventsList[e]),-1!==this.indexOf(i,t)||(t={name:e,type:r||0,callback:t},i.push(t)),this}},n.prototype.one=function(e,t){this.on(e,t,1)},n.prototype.remove=function(e,t){if(this){var r=this.EventsList[e];if(!r)return null;if(t)return r.length&&(t=this.indexOf(r,t),r.splice(t,1)),this;try{delete this.EventsList[e]}catch(e){}return null}},n),i=function(e){try{return encodeURIComponent(decodeURIComponent(e))}catch(t){return e}};function n(){var e=this;this.emit=function(t,r){if(e){var i;if(null!=(n=e.EventsList[t])&&n.length)for(var n=n.slice(),s=0;s<n.length;s++){i=n[s];try{var a=i.callback.apply(e,[r]);if(1===i.type&&e.remove(t,i.callback),!1===a)break}catch(t){throw t}}return e}},this.EventsList={}}function s(e,r){return"number"==typeof e||"string"==typeof e?e:r?t.string:t.number}function a(e,t){return"string"==typeof e?e.split("?")[t?1:0]||"":e}function o(e){return/^https/.test(e)}function h(){return void 0!==window.performance&&"function"==typeof performance.clearResourceTimings&&"function"==typeof performance.getEntriesByType&&"function"==typeof performance.now}function l(e){return p.some((function(t){return-1!==e.indexOf(t)}))}function c(e){if("string"==typeof e)return e;try{return(JSON.stringify(e,(t=[],r=[],function(e,i){if(i instanceof Error)return"Error.message: "+i.message+" \n  Error.stack: "+i.stack;if("object"==typeof i&&null!==i){var n=t.indexOf(i);if(-1!==n)return"[Circular "+r[n]+"]";t.push(i),r.push(e||"root")}return i}),4)||"undefined").replace(/"/gim,"")}catch(e){return"error happen when aegis stringify: \n "+e.message+" \n "+e.stack}var t,r}(Z=t=t||{})[Z.number=-1]="number",Z.string="";var u,d,p=["application/octet-stream","application/xhtml+xml","application/xml","application/pdf","application/pkcs12","application/javascript","application/ecmascript","application/vnd.mspowerpoint","application/ogg","text/html","text/css","text/javascript","image","audio","video"],f=["ret","retcode","code"],g=function(e,t){var r;try{"string"==typeof e&&(e=JSON.parse(e)),"function"==typeof(null===(r=null==t?void 0:t.ret)||void 0===r?void 0:r.join)&&(f=[].concat(null===(i=null==t?void 0:t.ret)||void 0===i?void 0:i.map((function(e){return e.toLowerCase()}))));var i=Object.getOwnPropertyNames(e).filter((function(e){return-1!==f.indexOf(e.toLowerCase())}));return i.length?""+e[i[0]]:"unknown"}catch(e){return"unknown"}},_=(y.prototype.sourceURL=function(){return this.data.responseURL},y.prototype.status=function(){return Number(this.data.status)},y.prototype.headers=function(){var e=this.data.getAllResponseHeaders().split("\n"),t={};return e.forEach((function(e){var r;e&&(e=(r=e.split(": "))[0],r=r[1].trim(),t[e]=r)})),t},y),v=(m.prototype.sourceURL=function(){return this.data.url},m.prototype.status=function(){return Number(this.data.status)},m.prototype.headers=function(){var e={};return this.data.headers.forEach((function(t,r){e[r]=t})),e},m);function m(e,t){this.type="fetch",this.data=e||{},this.data.response=t}function y(e){this.type="xhr",this.data=e}function b(e,t){return Array.isArray(e)?t(e.map((function(e){return{msg:"string"==typeof e.msg?e.msg:[].concat(e.msg).map(c).join(" "),level:e.level,trace:e.trace}}))):t({msg:"string"==typeof e.msg?e.msg:c(e.msg),level:e.level,trace:e.trace})}function T(){}(te=u=u||{}).INFO_ALL="-1",te.API_RESPONSE="1",te.INFO="2",te.ERROR="4",te.PROMISE_ERROR="8",te.AJAX_ERROR="16",te.SCRIPT_ERROR="32",te.IMAGE_ERROR="64",te.CSS_ERROR="128",te.CONSOLE_ERROR="256",te.MEDIA_ERROR="512",te.RET_ERROR="1024",te.REPORT="2048",(re=d=d||{}).LOG="log",re.SPEED="speed",re.PERFORMANCE="performance",re.OFFLINE="offline",re.WHITE_LIST="whiteList",re.VITALS="vitals",re.PV="pv",re.EVENT="event",re.CUSTOM="custom",re.SDK_ERROR="sdkError";var C=function(e,t){var r,i=[];return function(n,s){if(i.push(n),t&&i.length>=t)return s(i.splice(0,i.length)),void(r&&clearTimeout(r));r&&clearTimeout(r),r=setTimeout((function(){r=null,s(i.splice(0,i.length))}),e.delay||1e3)}},R=function(e){var t={};return function(r,i){var n;e.speedSample?Array.isArray(r)?(n=r.filter((function(e){var r=!t[e.url];return t[e.url]=!0,r}))).length&&i(n):(t[r.url]||i(r),t[r.url]=!0):i(r)}},E=function(e){if(!e||!e.reduce||!e.length)throw new TypeError("createPipeline need at least one function param");return 1===e.length?function(t,r){e[0](t,r||T)}:e.reduce((function(e,t){return function(r,i){return void 0===i&&(i=T),e(r,(function(e){return null==t?void 0:t(e,i)}))}}))},w=(A.prototype.init=function(e){this.setConfig(e);for(var t=0;t<A.installedPlugins.length;t++)try{A.installedPlugins[t].patch(this)}catch(e){this.sendSDKError(e)}this.lifeCycle.emit("onInited")},A.prototype.setConfig=function(e){Object.assign(this.config,e);var t=(n=this.config).id,r=n.uin,i=n.version,n=this.bean.id!==t||this.bean.uin!==r;return this.bean.id=t||"",this.bean.uin=r||"",this.bean.version=i||"1.32.18",n&&this.lifeCycle.emit("onConfigChange",e),this.config},A.use=function(e){-1===A.installedPlugins.indexOf(e)&&e.aegisPlugin&&A.installedPlugins.push(e)},A.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.normalLogPipeline({msg:e,level:u.INFO})},A.prototype.infoAll=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.normalLogPipeline({msg:e,level:u.INFO_ALL})},A.prototype.report=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.normalLogPipeline({msg:e,level:u.REPORT})},A.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.normalLogPipeline({msg:e,level:u.ERROR})},A.prototype.speedLogPipeline=function(e){throw new Error('You need to override "speedLogPipeline" method')},A.prototype.reportPv=function(e){var t,r=this;e&&(t=""+Object.getOwnPropertyNames(this.bean).filter((function(e){return"id"!==e})).map((function(e){return e+"="+r.bean[e]})).join("&"),this.send({url:this.config.url+"/"+e+"?"+t,addBean:!1,type:d.PV}))},A.prototype.reportEvent=function(e){e&&this.eventPipeline(e)},A.prototype.reportTime=function(e,t){"string"==typeof e&&"number"==typeof t&&(t<0||6e4<t||this.submitCustomTime(e,t))},A.prototype.reportT=function(e){var t=e.name,r=e.duration,i=void 0===(n=e.ext1)?"":n,n=void 0===(s=e.ext2)?"":s,s=void 0===(s=e.ext3)?"":s;if(e=void 0===(e=e.from)?"":e,"string"==typeof t&&"number"==typeof r&&"string"==typeof i&&"string"==typeof n&&"string"==typeof s&&!(r<0||6e4<r))return this.submitCustomTime(t,r,i,n,s,e)},A.prototype.time=function(e){"string"==typeof e&&(this.timeMap[e]||(this.timeMap[e]=Date.now()))},A.prototype.timeEnd=function(e){"string"==typeof e&&this.timeMap[e]&&(this.submitCustomTime(e,Date.now()-this.timeMap[e]),delete this.timeMap[e])},A.prototype.submitCustomTime=function(e,t,r,i,n,s){this.customTimePipeline({name:e,duration:t,ext1:r||this.config.ext1,ext2:i||this.config.ext2,ext3:n||this.config.ext3,from:s||void 0})},A.prototype.extendBean=function(e,t){this.bean[e]=t},A.prototype.send=function(e,t,r){throw new Error('You need to override "send" method')},A.prototype.sendSDKError=function(e){this.send({url:"https://aegis.qq.com/collect?id=1085&msg[0]="+encodeURIComponent(c(e))+"&level[0]=2&from="+this.config.id+"&count=1&version="+this.config.id+"(1.32.18)",addBean:!1,method:"get",type:d.SDK_ERROR})},A.LOG_TYPE=u,A.installedPlugins=[],A),S=(k.prototype.patch=function(e){this.canUse(e)&&this.exist(e)&&(this.instances.push(e),this.triggerInit(e),this.triggerOnNewAegis(e))},k.prototype.walk=function(e){var t=this;this.instances.forEach((function(r){var i=t.canUse(r);i&&e(r,i)}))},k.prototype.canUse=function(e){return!(!(e=this.getConfig(e))||"object"!=typeof e)||!!e},k.prototype.getConfig=function(e){return e.config[this.name]},k.prototype.exist=function(e){return-1===this.instances.indexOf(e)},k.prototype.triggerInit=function(e){var t;this.inited||(this.inited=!0,null===(t=null===(t=this.option)||void 0===t?void 0:t.init)||void 0===t||t.call(this.option,this.getConfig(e)))},k.prototype.triggerOnNewAegis=function(e){var t;null===(t=null===(t=this.option)||void 0===t?void 0:t.onNewAegis)||void 0===t||t.call(this.option,e,this.getConfig(e))},k),P=new S({name:"aid",aid:"",init:function(){try{var e=window.localStorage.getItem("AEGIS_ID");e||(e="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})),window.localStorage.setItem("AEGIS_ID",e)),this.aid=e}catch(e){}},onNewAegis:function(e){e.bean.aid=this.aid}}),I=(new S({name:"reportAssetSpeed"}),new S({name:"reportAssetSpeed",collectCur:0,ASSETS_INITIATOR_TYPE:["img","css","script","link","audio","video"],onNewAegis:function(e){var t=this;h()&&(this.collectSuccLog(e.config),this.collectFailLog(e.config),performance.onresourcetimingbufferfull=function(){t.collectCur=0,performance.clearResourceTimings()})},publish:function(e){this.$walk((function(t){t.speedLogPipeline(e)}))},collectSuccLog:function(e){function t(t){for(var i=0,n=t.length;i<n;i++){var s=t[i];-1!==r.ASSETS_INITIATOR_TYPE.indexOf(s.initiatorType)&&-1===s.name.indexOf("cdn-go.cn/aegis/aegis-sdk")&&r.publish(r.generateLog(s,e))}}var r=this;"function"==typeof window.PerformanceObserver?(t(performance.getEntriesByType("resource")),new window.PerformanceObserver((function(e){t(e.getEntries())})).observe({entryTypes:["resource"]})):setInterval((function(){var e=performance.getEntriesByType("resource"),i=e.slice(r.collectCur);r.collectCur=e.length,t(i)}),3e3)},collectFailLog:function(e){var r=this;window.document.addEventListener("error",(function(i){var n,s;i&&(i=(null==(n=i.target||i.srcElement)?void 0:n.src)||(null==n?void 0:n.href),s="function"==typeof(null===(n=null==e?void 0:e.api)||void 0===n?void 0:n.resourceTypeHandler)?null===(s=null==e?void 0:e.api)||void 0===s?void 0:s.resourceTypeHandler(i):"",i&&(i={url:a(i),status:400,method:"get",type:s||"static",isHttps:o(i),urlQuery:a(i,!0),x5ContentType:t.string,x5HttpStatusCode:t.number,x5ImgDecodeStatus:t.number,x5ErrorCode:t.number,x5LoadFromLocalCache:t.number,x5ContentLength:t.number,domainLookup:t.number,connectTime:t.number},r.publish(i)))}),!0)},generateLog:function(e,r){var i,n="function"==typeof(null===(i=null==r?void 0:r.api)||void 0===i?void 0:i.resourceTypeHandler)?null===(n=null==r?void 0:r.api)||void 0===n?void 0:n.resourceTypeHandler(e.name):"";return{url:a(e.name),method:"get",duration:Number(e.duration.toFixed(2)),status:200,type:n||"static",isHttps:o(e.name),urlQuery:a(e.name,!0),x5ContentType:s(e.x5ContentType,!0),x5HttpStatusCode:s(e.x5HttpStatusCode),x5ImgDecodeStatus:s(e.x5ImgDecodeStatus),x5ErrorCode:s(e.x5ErrorCode),x5LoadFromLocalCache:void 0===e.x5LoadFromLocalCache?t.number:0|e.x5LoadFromLocalCache,x5ContentLength:e.encodedBodySize||s(e.x5ContentLength),domainLookup:s(e.domainLookupEnd-e.domainLookupStart),connectTime:s(e.connectEnd-e.connectStart)}}})),L=function(e,t){return(L=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function k(e){this.aegisPlugin=!0,this.name="",this.instances=[],this.inited=!1,e.$walk=this.walk.bind(this),e.$getConfig=this.getConfig.bind(this),this.option=e,this.name=e.name}function A(t){var i,n,s,a,o=this;this.config={version:0,delay:1500,onError:!0,repeat:5,random:1,aid:!0,device:!0,pagePerformance:!0,webVitals:!0,speedSample:!0,url:"https://aegis.qq.com/collect",speedUrl:"https://aegis.qq.com/speed",customTimeUrl:"https://aegis.qq.com/speed/custom",whiteListUrl:"https://aegis.qq.com/aegis/whitelist",performanceUrl:"https://aegis.qq.com/speed/performance",webVitalsUrl:"https://aegis.qq.com/speed/webvitals",eventUrl:"https://aegis.qq.com/collect/events",listenOnerror:!0},this.isWhiteList=!1,this.lifeCycle=new r,this.bean={},this.normalLogPipeline=E([C(this.config,5),b,(s=this.config,a={},function(e,t){var r="number"==typeof s.repeat?s.repeat:5;if(0===r)return t(e);t(e.filter((function(e){return e.level!==u.ERROR&&e.level!==u.PROMISE_ERROR&&e.level!==u.AJAX_ERROR&&e.level!==u.SCRIPT_ERROR&&e.level!==u.IMAGE_ERROR&&e.level!==u.CSS_ERROR&&e.level!==u.MEDIA_ERROR||(a[e.msg]=a[e.msg]||0,a[e.msg]+=1,!(a[e.msg]>r))})))}),(i=this.lifeCycle.emit,n=this.config,function(e,t){var r=n.logCreated;return"function"!=typeof r?(i("beforeWrite",e),t(e)):(e=e.filter((function(e){return!1!==r(e)})),i("beforeWrite",e),t(e))}),function(e){var t,r=!1,i=!1,n=!1;e.lifeCycle.on("onConfigChange",(function(){t&&clearTimeout(t),t=setTimeout((function(){n||(n=!0,e.send({url:e.config.whiteListUrl||"",type:d.WHITE_LIST},(function(t){i=!0;try{0===(t=JSON.parse(t)||{}).retcode&&(r=t.result.is_in_white_list,e.isWhiteList=r);var n=e.config.onWhitelist;"function"==typeof n&&n(r)}catch(t){}}),(function(){i=!0})))}),e.config.uin?50:500)}));var s=[],a=!1;return function(e,t){!a&&e.some((function(e){return e.level===u.ERROR}))&&(a=!0),r||a?t(e.concat(s.splice(0)).map((function(e){return e.level===u.INFO_ALL&&(e.level=u.INFO),e}))):(e=e.filter((function(e){return e.level!==u.INFO&&e.level!==u.API_RESPONSE?(e.level===u.INFO_ALL&&(e.level=u.INFO),!0):(i||(s.push(e),200<=s.length&&(s.length=200)),!1)}))).length&&t(e)}}(this),function(e,t){var r=JSON.parse(JSON.stringify(e));o.lifeCycle.emit("beforeReport",r);var i=o.config.beforeReport;if((e="function"==typeof i?e.filter((function(e){return!1!==i(e)})):e).length)return t(e)},function(t){o.send({url:o.config.url||"",data:e(t),method:"post",contentType:"application/x-www-form-urlencoded",type:d.LOG},(function(){var e=o.config.onReport;"function"==typeof e&&t.forEach((function(t){e(t)}))}))}]),this.eventPipeline=E([C(this.config,5),function(e){o.send({url:o.config.eventUrl+"?"+e.map((function(e,t){return"event["+t+"]="+encodeURIComponent(e)})).join("&"),type:d.EVENT})}]),this.timeMap={},this.customTimePipeline=E([C(this.config,10),function(e){o.send({url:o.config.customTimeUrl+"?payload="+encodeURIComponent(JSON.stringify({custom:e})),type:d.CUSTOM})}])}var D=function(){return(D=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)};function N(e){var t,r;q.push(e),!M&&window.XMLHttpRequest&&(M=!0,t=window.XMLHttpRequest.prototype.send,r=window.XMLHttpRequest.prototype.open,window.XMLHttpRequest.prototype.open=function(){if(this.aegisMethod=arguments[0],this.aegisUrl=arguments[1],!this.sendByAegis)for(var e=0;e<q.length;e++){var t=q[e];try{"function"==typeof t.open&&t.open(this)}catch(e){}}return r.apply(this,arguments)},window.XMLHttpRequest.prototype.send=function(){if(!this.sendByAegis)for(var e=0;e<q.length;e++){var r=q[e];try{"function"==typeof r.send&&r.send(this,arguments[0])}catch(e){}}return t.apply(this,arguments)})}function O(e){var t;F.push(e),!$&&window.fetch&&($=!0,t=window.fetch,window.fetch=function(e,r){void 0===r&&(r={});for(var i=0;i<F.length;i++){var n=F[i];try{"function"==typeof n.beforeFetch&&n.beforeFetch(e,r)}catch(e){}}var s=Date.now();return t(e,r).then((function(t){for(var i=0;i<F.length;i++){var n=F[i];try{"function"==typeof n.then&&n.then(t,Date.now()-s,e,r)}catch(t){}}return t})).catch((function(t){for(var i=0;i<F.length;i++){var n=F[i];try{"function"==typeof n.catch&&n.catch(t,Date.now()-s,e,r)}catch(t){}}throw t}))})}var x,U,B,M=!1,q=[],$=!1,F=[],G=(new S({name:"reportApiSpeed"}),new S({name:"reportApiSpeed",override:!1,onNewAegis:function(e,t){this.override||(this.override=!0,this.overrideFetch(e.config),this.overrideXhr(e.config))},overrideFetch:function(e){var r=this;O({then:function(i,n,s,h){var d,p,f,_={url:i.url,isHttps:o(i.url),method:(null==h?void 0:h.method)||"get",duration:n,type:"fetch",status:i.status};"fetch"!==(f="function"==typeof(null===(d=null==e?void 0:e.api)||void 0===d?void 0:d.resourceTypeHandler)?null===(p=null==e?void 0:e.api)||void 0===p?void 0:p.resourceTypeHandler(i.url):f)&&"static"!==f&&(p=i.headers?i.headers.get("content-type"):"",f=i.ok&&"string"==typeof p&&l(p)?"static":"fetch"),"fetch"===f?i.clone().text().then((function(t){var a,o,l,d,p="req url: "+s+" \n                                \nreq method: "+((null==h?void 0:h.method)||"get")+" \n                                \nreq param: "+c(null==h?void 0:h.body)+" \n                                \nres duration: "+n+" \n                                \nres status: "+i.status+" \n                                \nres data: "+t;r.publishNormalLog({msg:p,level:u.API_RESPONSE}),_.payload=new v(i,t),("function"==typeof(null===(a=null==e?void 0:e.api)||void 0===a?void 0:a.retCodeHandler)?(d=(l=e.api.retCodeHandler(t,i.url,i)).code,o=l.isErr,_.ret=d,o):(l=g(t,e.api),_.ret=l,t=(t=null===(o=e.api)||void 0===o?void 0:o.errCode)&&[].concat(t),d=(d=null===(o=e.api)||void 0===o?void 0:o.code)&&[].concat(d),t&&-1!==t.indexOf(l)||d&&-1===d.indexOf(l)||!t&&!d&&"0"!==l&&"unknown"!==l))&&r.publishNormalLog({msg:p,level:u.RET_ERROR})})):Object.assign(_,{type:"static",urlQuery:a(i.url,!0),x5ContentType:t.string,x5HttpStatusCode:t.number,x5ImgDecodeStatus:t.number,x5ErrorCode:t.number,x5LoadFromLocalCache:t.number,x5ContentLength:t.number,domainLookup:t.number,connectTime:t.number}),r.publishSpeed(_)},catch:function(e,t,i,n){t={url:i,isHttps:o(i),method:(null==n?void 0:n.method)||"get",duration:t,type:"fetch",status:600},r.publishSpeed(t)}})},getRequestType:function(e,t,r){var i,n="function"==typeof(null===(i=null==e?void 0:e.api)||void 0===i?void 0:i.resourceTypeHandler)?null===(n=null==e?void 0:e.api)||void 0===n?void 0:n.resourceTypeHandler(r):"";return"fetch"!==n&&"static"!==n&&(r=t.getResponseHeader("content-type"),n=400<=t.status||"string"!=typeof r||!l(r)?"fetch":"static"),n},overrideXhr:function(e){var r=this;N({send:function(i,n){var s=Date.now();i.addEventListener("loadend",(function(){var h=i.aegisUrl;if(h){var l=Date.now()-s,d={url:h,isHttps:o(h),status:i.status,method:i.aegisMethod||"get",type:"fetch",duration:l,payload:new _(i)};if("fetch"===r.getRequestType(e,i,h))try{var p,f,v,m,y="req url: "+h+" \n                                \nreq method: "+(i.aegisMethod||"get")+" \n                                \nreq param: "+c(n)+" \n                                \nres duration: "+l+" \n                                \nres status: "+i.status+" \n                                \nres data: "+c(i.response);r.publishNormalLog({msg:y,level:u.API_RESPONSE,trace:i.aegisTjgTrace}),("function"==typeof(null===(f=null==e?void 0:e.api)||void 0===f?void 0:f.retCodeHandler)?(m=(p=e.api.retCodeHandler(i.response,h,i)).code,v=p.isErr,d.ret=m,v):(f=g(i.response,e.api),d.ret=f,v=(v=null===(p=e.api)||void 0===p?void 0:p.errCode)&&[].concat(v),m=(m=null===(p=e.api)||void 0===p?void 0:p.code)&&[].concat(m),v&&-1!==v.indexOf(f)||m&&-1===m.indexOf(f)||!v&&!m&&"0"!==f&&"unknown"!==f))&&r.publishNormalLog({msg:y,level:u.RET_ERROR})}catch(h){d.ret="unknown"}else Object.assign(d,{type:"static",urlQuery:a(h,!0),x5ContentType:t.string,x5HttpStatusCode:t.number,x5ImgDecodeStatus:t.number,x5ErrorCode:t.number,x5LoadFromLocalCache:t.number,x5ContentLength:t.number,domainLookup:t.number,connectTime:t.number});r.publishSpeed(d)}}))}})},publishSpeed:function(e){var t=this;e.url=a(e.url),this.$walk((function(r){var i=t.$getConfig(r);"fetch"===e.type&&i&&"function"==typeof i.urlHandler?r.speedLogPipeline(D(D({},e),{url:encodeURIComponent(i.urlHandler(e.url,e.payload))})):r.speedLogPipeline(e)}))},publishNormalLog:function(e){this.$walk((function(t){t.normalLogPipeline(e)}))}}));function H(e){return{name:e,value:1<arguments.length&&void 0!==arguments[1]?arguments[1]:-1,delta:0,entries:[],id:"".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),isFinal:!1}}function j(e,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){return e.getEntries().map(t)}));return r.observe({type:e,buffered:!0}),r}}catch(e){}}function Y(e){ie=!e.persisted}function V(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];ne||(addEventListener("pagehide",Y),addEventListener("beforeunload",(function(){})),ne=!0),addEventListener("visibilitychange",(function(t){t=t.timeStamp,"hidden"===document.visibilityState&&e({timeStamp:t,isUnloading:ie})}),{capture:!0,once:t})}function K(e,t,r,i){var n;return function(){r&&t.isFinal&&r.disconnect(),0<=t.value&&(i||t.isFinal||"hidden"===document.visibilityState)&&(t.delta=t.value-(n||0),(t.delta||t.isFinal||void 0===n)&&(e(t),n=t.value))}}function z(){return void 0===Q&&(Q="hidden"===document.visibilityState?0:1/0,V((function(e){return e=e.timeStamp,Q=e}),!0)),{get timeStamp(){return Q}}}function X(e){if(e.payload){var t={};return Object.keys(e).forEach((function(r){"payload"!==r&&(t[r]=e[r])})),t}return e}(se=x=x||{})[se.unknown=100]="unknown",se[se.wifi=1]="wifi",se[se.net2g=2]="net2g",se[se.net3g=3]="net3g",se[se.net4g=4]="net4g",se[se.net5g=5]="net5g",se[se.net6g=6]="net6g",(oe=U=U||{})[oe.android=1]="android",oe[oe.ios=2]="ios",oe[oe.windows=3]="windows",oe[oe.macos=4]="macos",oe[oe.linux=5]="linux",oe[oe.other=100]="other",(pe=B=B||{})[pe.oldX5=1]="oldX5",pe[pe.newX5=2]="newX5",pe[pe.other=3]="other";var W,Q,J,Z=new S({name:"device",onNewAegis:function(e){e.extendBean("platform",this.getPlatform()),e.extendBean("x5Type",this.getX5Type()),e.extendBean("netType",x.unknown),this.refreshNetworkTypeToBean(e)},getPlatform:function(){var e={android:/\bAndroid\s*([^;]+)/,ios:/\b(iPad|iPhone|iPod)\b.*? OS ([\d_]+)/,windows:/\b(Windows NT)/,macos:/\b(Mac OS)/,linux:/\b(Linux)/i},t=Object.keys(e).find((function(t){return e[t].test(navigator.userAgent)}));return t?U[t]:U.other},getX5Type:function(){var e,t=B.other;return 0<=navigator.userAgent.toLowerCase().indexOf("tbs")?null!=(e=navigator.userAgent.match(/tbs\/([\d.]+)/i))&&e[1]&&36541<=parseInt(e[1],10)?B.newX5:B.oldX5:t},refreshNetworkTypeToBean:function(e){var t=this,r=e.config;("function"==typeof r.getNetworkType?r.getNetworkType:ee)((function(r){x[r]||(r=x.unknown),e.extendBean("netType",r),t.NetworkRefreshTimer=setTimeout((function(){t.refreshNetworkTypeToBean(e),clearTimeout(t.NetworkRefreshTimer)}),1e4)}))}}),ee=function(e){var t="",r=navigator.userAgent.match(/NetType\/(\w+)/);r?t=r[1]:navigator.connection&&(t=navigator.connection.effectiveType||navigator.connection.type),e((t=t=t||"unknown",0<=(t=String(t).toLowerCase()).indexOf("4g")?x.net4g:0<=t.indexOf("wifi")?x.wifi:0<=t.indexOf("5g")?x.net5g:0<=t.indexOf("6g")?x.net6g:0<=t.indexOf("3g")?x.net3g:0<=t.indexOf("2g")?x.net2g:x.unknown))},te=(new S({name:"onError"}),new S({name:"onError",init:function(){this.startListen()},startListen:function(){var e=this,t=window.onerror;window.onerror=function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];e.publishErrorLog({msg:(c(r[0])||"")+" @ ("+(c(r[1])||"")+":"+(r[2]||0)+":"+(r[3]||0)+")",level:u.ERROR}),null==t||t.call.apply(t,function(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;var i=Array(e),n=0;for(t=0;t<r;t++)for(var s=arguments[t],a=0,o=s.length;a<o;a++,n++)i[n]=s[a];return i}([window],r))},window.addEventListener("unhandledrejection",(function(t){t=t&&c(t.reason),e.publishErrorLog({msg:"PROMISE_ERROR: "+t,level:u.PROMISE_ERROR})})),window.document.addEventListener("error",(function(t){if(t&&t.target&&t.srcElement){t=(r=t.target||t.srcElement).src||r.href;var r=r.tagName;if("string"==typeof t&&-1===(null==t?void 0:t.indexOf("aegis.qq.com"))&&r){var i={msg:r+" load fail: "+t,level:u.INFO};switch(r.toLowerCase()){case"script":i.level=u.SCRIPT_ERROR;break;case"link":i.level=u.CSS_ERROR;break;case"img":i.level=u.IMAGE_ERROR;break;case"audio":case"video":i.level=u.MEDIA_ERROR;break;default:return}e.publishErrorLog(i)}}}),!0);var r=this;N({send:function(e,t){e.addEventListener("loadend",(function(){var e="";this.aegisTimeout?e="timeout":this.status<=0?e="failed":400<=this.status&&(e="error"),e&&r.publishErrorLog({msg:"AJAX_ERROR: request "+e+". status: "+this.status+". \n \n                    url: "+this.aegisUrl+". \n \n                    method: "+this.aegisMethod+". \n\n                    request body: "+c(t)+".",level:u.AJAX_ERROR})})),e.addEventListener("timeout",(function(){e.aegisTimeout=!0}))}})},publishErrorLog:function(e){this.$walk((function(t){t.normalLogPipeline(e)}))}})),re=(new S({name:"pagePerformance"}),new S({name:"pagePerformance",onNewAegis:function(e){var t=this;if(h())if(W)this.publish(W,e);else try{this.getFirstScreenTiming(e,(function(r){var i,n=performance.timing;n&&((i=n.loadEventStart-n.domInteractive)<0&&(i=1070),W={dnsLookup:n.domainLookupEnd-n.domainLookupStart,tcp:n.connectEnd-n.connectStart,ssl:0===n.secureConnectionStart?0:n.requestStart-n.secureConnectionStart,ttfb:n.responseStart-n.requestStart,contentDownload:n.responseEnd-n.responseStart,domParse:n.domInteractive-n.domLoading,resourceDownload:i,firstScreenTiming:Math.floor(r)},t.publish(W,e))}))}catch(e){}},publish:function(e,t){var r,i=[];for(r in e)i.push(r+"="+e[r]);var n=this.$getConfig(t),s=-1===t.config.performanceUrl.indexOf("?")?"?":"&";"function"==typeof n.urlHandler?t.send({url:t.config.performanceUrl+s+i.join("&")+"&from="+(encodeURIComponent(n.urlHandler())||window.location.href),beanFilter:["from"],type:d.PERFORMANCE}):t.send({url:t.config.performanceUrl+s+i.join("&"),type:d.PERFORMANCE})},getFirstScreenTiming:function(e,t){var r=["script","style","link","br"],i=[],n=this,s={},a=new MutationObserver((function(e){var t={roots:[],rootsDomNum:[],time:performance.now()};e.forEach((function(e){e&&e.addedNodes&&e.addedNodes.forEach&&e.addedNodes.forEach((function(e){1===e.nodeType&&(e.hasAttribute("AEGIS-FIRST-SCREEN-TIMING")||e.querySelector("[AEGIS-FIRST-SCREEN-TIMING]"))?(Object.prototype.hasOwnProperty.apply(s,[t.time])||(s[t.time]=[]),s[t.time].push(e)):1!==e.nodeType||-1!==r.indexOf(e.nodeName.toLocaleLowerCase())||n.isEleInArray(e,t.roots)||e.hasAttribute("AEGIS-IGNORE-FIRST-SCREEN-TIMING")||(t.roots.push(e),t.rootsDomNum.push(n.walkAndCount(e)||0))}))})),t.roots.length&&i.push(t)}));a.observe(document,{childList:!0,subtree:!0}),setTimeout((function(){a.disconnect();var r=0,o=0,h=!1,l=Object.keys(s).filter((function(e){return s[+e].find((function(e){return n.isInFirstScreen(e)}))}));l.length?(o=Math.max.apply(null,l),e.firstScreenInfo={element:null===(l=s[o])||void 0===l?void 0:l[0],timing:o,markDoms:s}):i.forEach((function(t){for(var i=0;i<t.roots.length;i++)t.rootsDomNum[i]>r&&n.isInFirstScreen(t.roots[i])&&(r=t.rootsDomNum[i],o=t.time,e.firstScreenInfo={element:t.roots[i],timing:o})}));for(var c,u=document.querySelectorAll(".fp-check"),d=[],p=0;p<u.length;p++)"img"===u[p].tagName.toLowerCase()&&""!==u[p].src?d.push(u[p].src):(c=window.getComputedStyle(u[p]),(c=/url\(['"](.+)['"]\)/gi.exec(c.backgroundImage))&&2===c.length&&d.push(c[1]));var f=0;window.performance.getEntriesByType("resource").some((function(e){return d.some((function(t,r){return t===e.name&&(e.responseEnd>f&&(f=e.responseEnd),(e.fetchStart<o||e.startTime<o)&&e.responseEnd>o&&(h=!0,o=e.responseEnd),d.splice(r,1),!0)})),0===d.length})),0===o&&(o=f),null==t||t(h?o:o+25)}),3e3)},isEleInArray:function(e,t){return!(!e||e===document.documentElement)&&(-1!==t.indexOf(e)||this.isEleInArray(e.parentElement,t))},isInFirstScreen:function(e){if(!e||"function"!=typeof e.getBoundingClientRect)return!1;var t=e.getBoundingClientRect(),r=window.innerHeight;return e=window.innerWidth,0<=t.left&&t.left<e&&0<=t.top&&t.top<r},walkAndCount:function(e){var t=0;if(e&&1===e.nodeType){t+=1;var r=e.children;if(null!=r&&r.length)for(var i=0;i<r.length;i++)t+=this.walkAndCount(r[i])}return t}})),ie=!1,ne=!1,se=(new S({name:"webVitals"}),new S({name:"webVitals",onNewAegis:function(e){if(h())try{!function(e,t){function r(e){var t=e.startTime;t<s.timeStamp?(n.value=t,n.entries.push(e)):n.isFinal=!0,i()}t=1<arguments.length&&void 0!==t&&t;var i,n=H("LCP"),s=z(),a=j("largest-contentful-paint",r);a&&(i=K(e,n,a,t),t=function(){n.isFinal||(a.takeRecords().map(r),n.isFinal=!0,i())},(J=J||new Promise((function(e){return["scroll","keydown","pointerdown"].map((function(t){addEventListener(t,e,{once:!0,passive:!0,capture:!0})}))}))).then(t),V(t,!0))}(this.publish.bind(this,e)),r=this.publish.bind(this,e),i=H("FID"),n=z(),s=j("first-input",t),a=K(r,i,s),s?V((function(){s.takeRecords().map(t),s.disconnect()}),!0):window.perfMetrics&&window.perfMetrics.onFirstInputDelay&&window.perfMetrics.onFirstInputDelay((function(e,t){t.timeStamp<n.timeStamp&&(i.value=e,i.isFinal=!0,i.entries=[{entryType:"first-input",name:t.type,target:t.target,cancelable:t.cancelable,startTime:t.timeStamp,processingStart:t.timeStamp+e}],a())})),function(e,t){function r(e){e.hadRecentInput||(n.value+=e.value,n.entries.push(e),i())}t=1<arguments.length&&void 0!==t&&t;var i,n=H("CLS",0),s=j("layout-shift",r);s&&(i=K(e,n,s,t),V((function(e){e=e.isUnloading,s.takeRecords().map(r),e&&(n.isFinal=!0),i()})))}(this.publish.bind(this,e))}catch(e){}function t(e){e.startTime<n.timeStamp&&(i.value=e.processingStart-e.startTime,i.entries.push(e),i.isFinal=!0,a())}var r,i,n,s,a},publish:function(e,t){var r,i=t.name,n=t.value,s={LCP:-1,FID:-1,CLS:-1},a=[];for(r in s)a.push(r+"="+(r===i?n:s[r]));var o=-1===e.config.performanceUrl.indexOf("?")?"?":"&";setTimeout((function(){e.send({url:e.config.webVitalsUrl+o+a.join("&"),type:d.VITALS})}),0)}})),ae=(new S({name:"tjg"}),!1),oe=new S({name:"tjg",onNewAegis:function(e){this.setTjgHeader(e)},setTjgHeader:function(e){ae||(ae=!0,this.overrideFetch(e),this.overrideXHR(e))},overrideFetch:function(e){var t=this;O({beforeFetch:function(r,i){t.isSameOrigin(r)&&(i.headers?i.headers instanceof Headers&&i.headers.append("X-Tjg-Json-Span-Context",t.getTjgHeaderValue(e)):i.headers=new Headers({"X-Tjg-Json-Span-Context":t.getTjgHeaderValue(e)}))}})},overrideXHR:function(e){var t=this;N({send:function(r,i){var n;t.isSameOrigin(r.aegisUrl)&&(n=t.getTjgHeaderValue(e),r.aegisTjgTrace=n,r.setRequestHeader("X-Tjg-Json-Span-Context",n))}})},getTjgHeaderValue:function(e){return window.btoa&&window.btoa(JSON.stringify({ids:{trace_id:{high:this.rand53(),low:this.rand53()},span_id:this.rand53(),parent_id:0,flag:2},baggages:{aegis_session_id:e.sessionID}}))},rand53:function(){return parseInt(new Array(53).fill(1).map((function(){return.5<Math.random()?1:0})).join(""),2)},isSameOrigin:function(e){var t=this,r=!1;if(this.$walk((function(e){!0===t.$getConfig(e).crossOrigin&&(r=!0)})),r)return!0;var i=document.createElement("a");return i.href=e,location.origin===i.origin}}),he=function(e,t){var r,i={fetch:[],static:[]},n=new FormData;return Array.isArray(e)?e.forEach((function(e){var t=X(e);i[e.type].push(t)})):(r=X(e),i[e.type].push(r)),n.append("payload",JSON.stringify(D({duration:i},t))),n};Object.assign||Object.defineProperty(Object,"assign",{enumerable:!1,configurable:!0,writable:!0,value:function(e){if(null==e)throw new TypeError("Cannot convert first argument to object");for(var t=Object(e),r=1;r<arguments.length;r++)if(null!=(i=arguments[r]))for(var i=Object(i),n=Object.keys(Object(i)),s=0,a=n.length;s<a;s++){var o=n[s],h=Object.getOwnPropertyDescriptor(i,o);null!=h&&h.enumerable&&(t[o]=i[o])}return t}});var le,ce,ue=0,de=(L(ce=fe,pe=le=w),ce.prototype=null===pe?Object.create(pe):(ge.prototype=pe.prototype,new ge),fe.prototype.getBean=function(e){var t=this;return void 0===e&&(e=[]),""+Object.getOwnPropertyNames(this.bean).filter((function(t){return-1===e.indexOf(t)})).map((function(e){return e+"="+t.bean[e]})).join("&")},fe.prototype.send=function(e,t,r){var i=this;if(e&&"string"==typeof e.url&&""!==e.url&&this.bean.id)if(this.requesting)this.requestQueue.push({options:e,success:t,fail:r});else{this.requesting=!0;var n=e.url;!1!==e.addBean&&(n=n+(-1===n.indexOf("?")?"?":"&")+this.getBean(e.beanFilter)),e.url=n;var s=e.method||"get";if(!(e=(n=this.config.onBeforeRequest)?n(e,this):e)||!e.url)return;var a=new XMLHttpRequest;a.sendByAegis=!0,a.addEventListener("readystatechange",(function(){var e;4===a.readyState&&(i.requesting=!1,(e=i.requestQueue.shift())&&i.send(e.options,e.success,e.fail),400<=a.status?null==r||r(a.response):null==t||t(a.response))})),"get"===s.toLocaleLowerCase()?(a.open("get",function(e,t){if("string"!=typeof e)return"";if("object"==typeof t&&t){var r=Object.getOwnPropertyNames(t).map((function(e){var r=t[e];return e+"="+("string"==typeof r?encodeURIComponent(r):encodeURIComponent(JSON.stringify(r)))})).join("&").replace(/eval/gi,"evaI");return e+(-1===e.indexOf("?")?"?":"&")+r}return e}(e.url,e.data)),a.send()):(a.open("post",e.url),e.contentType&&a.setRequestHeader("Content-Type",e.contentType),"string"==typeof e.data&&(e.data=e.data.replace(/eval/gi,"evaI")),a.send(e.data))}},fe.useAsyncPlugin=function(e,t){var r,i=void 0===(r=(t=void 0===t?{}:t).exportsConstructor)?"aegis-plugin-"+ue:r,n=void 0===(r=t.onAegisInit)?function(){}:r,s=void 0===(t=t.onAegisInitAndPluginLoaded)?function(){}:t;if(ue+=1,"string"!=typeof e)throw new TypeError("useAsyncPlugin first param must be string");if("function"!=typeof n||"function"!=typeof s)throw new TypeError("onAegisInit and onAegisInitAndPluginLoaded must be function");this.use(new S({name:"asyncPlugin",onNewAegis:function(t){try{n(t),fe.asyncPlugin[e]?s(t,window[fe.asyncPlugin[e]]):(r=e,a=i,o=function(r){r||(fe.asyncPlugin[e]=i,r=window[i],s(t,r))},h=document.createElement("script"),l=document.head,"function"==typeof a&&(o=a,a=""),h.src=r,h.setAttribute("name",a),h.name=a,h.setAttribute("crossorigin","anonymous"),h.crossorigin="anonymous",h.async=!0,h.hasLoaded=!1,h.onreadystatechange=function(){h.hasLoaded||h.readyState&&"loaded"!==h.readyState&&"complete"!==h.readyState||(h.hasLoaded=!0,"function"==typeof o&&o(!1),setTimeout((function(){l.contains(h)&&l.removeChild(h)})))},h.onload=h.onreadystatechange,h.onerror=function(){"function"==typeof o&&o(!0),setTimeout((function(){l.contains(h)&&l.removeChild(h)}))},l.appendChild(h))}catch(r){}var r,a,o,h,l}}))},fe.prototype.uploadLogs=function(e,t){void 0===e&&(e={}),void 0===t&&(t={}),this.lifeCycle.emit("uploadLogs",e,t)},fe.__version__="1.32.18",fe.sessionID="session-"+Date.now(),fe.asyncPlugin={},fe.urls={aegisCollect:"https://aegis.qq.com/collect",flog:"https://cdn-go.cn/vasdev/web_webpersistance_v2/v1.7.2/flog.core.min.js",shadowLog:""},fe),pe=(new S({name:"offlineLog"}),w=new S({name:"offlineLog",onNewAegis:function(t){var r=de.urls.flog,i=t.config,n=[],s=null,a=null,o=function(e){a=e},h=function(e){n=n.concat(e)},l=function(e,t){s={conds:e=void 0===e?{}:e,params:t=void 0===t?{}:t}};de.useAsyncPlugin(r,{exportsConstructor:"Flog",onAegisInit:function(e){e.lifeCycle.on("beforeWrite",h),e.lifeCycle.on("uploadLogs",l),e.lifeCycle.on("onConfigChange",o)},onAegisInitAndPluginLoaded:function(t,r){var p=void 0===(g=i.dbConfig)?{}:g,f=void 0===(_=i.url)?de.urls.aegisCollect:_,g=void 0===(v=i.offlineLogExp)?3:v,_=i.id,v=i.uin;t.lifeCycle.remove("beforeWrite",h),t.lifeCycle.remove("uploadLogs",l),t.lifeCycle.remove("onConfigChange",o);var m=Object.assign({lookupUrl:f+"/offlineAuto?id="+_+"&uin="+(v||""),preservedDay:g,id:_,uin:v},p,{sessionId:de.sessionID});try{var y=new r(m);n.forEach((function(e){y.add(D(D({},e),{level:u.INFO_ALL?u.INFO:e.level}))})),t.lifeCycle.on("beforeWrite",(function(e){(e=void 0===e?[]:e).forEach((function(e){y.add(D(D({},e),{level:u.INFO_ALL?u.INFO:e.level}))}))})),t.lifeCycle.on("uploadLogs",(function(e,t){void 0===e&&(e={}),void 0===t&&(t={}),y.uploadLogs(Object.assign({id:i.id,uin:i.uin},e),t)})),t.lifeCycle.on("onConfigChange",(function(e){y.setConfig(e)})),y.on("PERREVENT",(function(r){t.send({type:d.OFFLINE,data:e({msg:c(r),level:u.INFO}),contentType:"application/x-www-form-urlencoded",method:"post",addBean:!1,url:i.url+"?id=893&sessionId="+de.sessionID+"&uin="+i.uin+"&from="+i.id+"&count=1&version=1.32.18"})})),a&&(y.setConfig(a),a=null),s&&(t.lifeCycle.emit("uploadLogs",s.conds,s.params),s=null),t.flog=y}catch(t){}}})}}),new S({name:"spa"}),new S({name:"spa",init:function(){history.pushState=this.wr("pushState")||history.pushState,history.replaceState=this.wr("replaceState")||history.replaceState;var e=(null===(e=location.href)||void 0===e?void 0:e.split("?"))[0];this.$fireUrl=e,this.sendPv=this.sendPv.bind(this),this.onPageChange()},onPageChange:function(){window.addEventListener("replaceState",this.sendPv),window.addEventListener("pushState",this.sendPv),window.addEventListener("popstate",this.sendPv)},wr:function(e){var t=history[e];return!("function"!=typeof t||!/native code/.test(t.toString()))&&function(){var r=t.apply(this,arguments),i=new Event(e);return window.dispatchEvent(i),r}},sendPv:function(){var e=this;setTimeout((function(){var t=location.href,r=(null===(r=location.href)||void 0===r?void 0:r.split("?"))[0];r&&r!==e.$fireUrl&&(e.$walk((function(e){e.send({url:e.config.whiteListUrl+"?from="+encodeURIComponent(t),beanFilter:["from"],type:d.WHITE_LIST})})),e.$fireUrl=r)}),0)}}));function fe(e){var t,r=le.call(this,e)||this;r.requestQueue=[],r.requesting=!1,r.speedLogPipeline=E([R(r.config),C(r.config),(t=r,function(e,r){ee((function(i){t.extendBean("netType",i),r(e)}))}),function(e,t){r.lifeCycle.emit("beforeReportSpeed",e);var i=r.config.beforeReportSpeed;if((e="function"==typeof i?e.filter((function(e){var t=!1!==i(e);return"fetch"===e.type&&void 0===e.ret&&e.payload&&(e.ret=g(e.payload.data.response,r.config.api)),t})):e).length)return t(e)},function(e){r.send({type:d.SPEED,url:""+r.config.speedUrl,method:"post",data:he(e,r.bean)})}]),e.asyncPlugin=!0;try{e.uin=e.uin||parseInt((document.cookie.match(/\buin=\D+(\d*)/)||[])[1],10)||parseInt((document.cookie.match(/\bilive_uin=\D*(\d+)/)||[])[1],10)||"",r.init(e),r.extendBean("sessionId",fe.sessionID),r.extendBean("from",encodeURIComponent(e.pageUrl||location.href)),r.extendBean("referer",encodeURIComponent(document.referrer||""))}catch(e){r.sendSDKError(e)}return r}function ge(){this.constructor=ce}return new S({name:"ie"}),de.use(te),de.use(G),de.use(I),de.use(re),de.use(se),de.use(P),de.use(oe),de.use(Z),de.use(w),de.use(pe),de}()},function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));var i=r(1);class n{constructor(){this.ttfbCost=0,this.bandwidth=0,this.costMs=0}recordTTFB(e){this.ttfbCost=this.ttfbCost*(1-i.a.cdnBWRatio)+e*i.a.cdnBWRatio}estimateTTFB(){return this.ttfbCost}recordBandwidth(e,t){if(t){var r=e/t;this.bandwidth=this.bandwidth*(1-i.a.cdnBWRatio)+r*i.a.cdnBWRatio,this.costMs=this.costMs*(1-i.a.cdnCostRatio)+t*i.a.cdnCostRatio}}estimateBandwidth(){return this.bandwidth}estimateCostMs(){return this.costMs*i.a.cdnEstimateRatio}}},function(e,t,r){"use strict";r.d(t,"a",(function(){return a}));var i=r(6),n=r.n(i);class s{constructor(e){var t=e.id,r=e.totalPacket;this.totalPacket=r,this.receivedPacket=0,this.costTime=0,this.startTime=Date.now(),this.id=t}recordPacket(){this.receivedPacket+=1,this.costTime=Date.now()-this.startTime}estimateTimeNeeded(){var e=(this.totalPacket-this.receivedPacket)*this.costTime/this.receivedPacket;return n.a.debug(`[estimateTimeNeeded] id: ${this.id} totalPacket: ${this.totalPacket} receivedPacket: ${this.receivedPacket} costTime: ${this.costTime}, needed: ${e}`),e}}class a{constructor(){this.records=new Map}destroy(){this.records.clear()}addPacket(e,t){var r=t.totalPacket,i=this.records.get(e);i||(i=new s({id:e,totalPacket:r}),this.records.set(e,i)),n.a.debug(`[P2pEstimate] add packet ${e}, totalPacket: ${r}`),i.recordPacket()}estimateTimeNeeded(e){var t=this.records.get(e);return t?t.estimateTimeNeeded():(n.a.debug(`[estimateTimeNeeded] slice not found ${e}`),0)}delete(e){this.records.delete(e)}}},function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));class i{constructor(){this._uploadBytesTotal=0,this.uploadBytes=0}addUploadBytes(e){this._uploadBytesTotal+=e,this._uploadBytes+=e}getStats(){var e={uploadBytesTotal:this._uploadBytesTotal,uploadBytes:this._uploadBytes};return this._uploadBytes=0,e}}},function(e,t,r){"use strict";var i=r(1),n=r(6),s=r.n(n);function a(e,t){var r;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return o(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return o(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var i=0,n=function(){};return{s:n,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,h=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return a=e.done,e},e:function(e){h=!0,s=e},f:function(){try{a||null==r.return||r.return()}finally{if(h)throw s}}}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=new Array(t);r<t;r++)i[r]=e[r];return i}class h extends Error{constructor(e){super(`[HLSP2P] [字段检查错误] ${e}`),s.a.error(this.message)}}var l=(e,t)=>{var r,i=a(t);try{for(i.s();!(r=i.n()).done;){var n=r.value;if(!(n in e))throw new h(`${n} 未传入`)}}catch(e){i.e(e)}finally{i.f()}},c=(e,t)=>{var r,i=a(t);try{for(i.s();!(r=i.n()).done;){var n=r.value;if(n in e&&!e[n])throw new h(`字段为空 ${n}`)}}catch(e){i.e(e)}finally{i.f()}},u=[{name:"checkVideoType",matchRule:()=>!0,check(e){var t=["videoType"];if(l(e,t),c(e,t),-1===["LIVE","VOD"].indexOf(e.videoType))throw new h("字段取值不对 videoType取值为LIVE / VOD")}},{name:"checkChannelId",matchRule:()=>!0,check(e){var t=["channelId"];l(e,t),c(e,t)}},{name:"checkPlayConfig",matchRule:()=>!0,check(e,t){var r=["videoId","url"];l(t,r),c(t,r)}},{name:"checkLiveServiceId",matchRule:e=>"LIVE"===e.videoType,check(e,t){var r=["domain","xp2pAppId"];c(t,r),l(t,r)}},{name:"checkVodServiceId",matchRule:e=>"VOD"===e.videoType,check(e,t){var r=["domain","xp2pAppId"];c(t,r),l(t,r)}},{name:"checkCloudAppId",matchRule:()=>!0,check(e,t){l(t,["cloudAppId"])}}],d=r(42);class p{constructor(e){this.TAG="ConfigParser",this.userConfig=e;var t=e.url,r=e.videoType;i.a.originalUrl=t,i.a.videoType=r||"VOD";var n=i.a.channelIdIncludeHost,a=i.a.channelIdIncludeSearch,o=e.channelId||Object(d.a)(t,{channelIdIncludeHost:n,channelIdIncludeSearch:a});s.a.info(`channelId: ${o}`),e.channelId=o,i.a.channelId=o,i.a.cdnDomain=p.cdnDomain(t)}static cdnDomain(e){return new URL(e).host}static check(e){((e,t)=>{var r,i=a(u);try{for(i.s();!(r=i.n()).done;){var n=r.value;n.matchRule(e,t)&&n.check(e,t)}}catch(e){i.e(e)}finally{i.f()}})(i.a,e)}static merge(e){Object.assign(i.a,e)}process(){p.check(this.userConfig),p.merge(this.userConfig)}}t.a=p},function(e,t){(function(t){e.exports=t}).call(this,{})},function(e,t){e.exports=function(e){if(Array.isArray(e))return e},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t){e.exports=function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],i=!0,n=!1,s=void 0;try{for(var a,o=e[Symbol.iterator]();!(i=(a=o.next()).done)&&(r.push(a.value),!t||r.length!==t);i=!0);}catch(e){n=!0,s=e}finally{try{i||null==o.return||o.return()}finally{if(n)throw s}}return r}},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,r){var i=r(62);e.exports=function(e,t){if(e){if("string"==typeof e)return i(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(e,t):void 0}},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=new Array(t);r<t;r++)i[r]=e[r];return i},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,r){var i=function(e){"use strict";var t,r=Object.prototype,i=r.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",o=n.toStringTag||"@@toStringTag";function h(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{h({},"")}catch(e){h=function(e,t,r){return e[t]=r}}function l(e,t,r,i){var n=t&&t.prototype instanceof _?t:_,s=Object.create(n.prototype),a=new I(i||[]);return s._invoke=function(e,t,r){var i=u;return function(n,s){if(i===p)throw new Error("Generator is already running");if(i===f){if("throw"===n)throw s;return k()}for(r.method=n,r.arg=s;;){var a=r.delegate;if(a){var o=w(a,r);if(o){if(o===g)continue;return o}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===u)throw i=f,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=p;var h=c(e,t,r);if("normal"===h.type){if(i=r.done?f:d,h.arg===g)continue;return{value:h.arg,done:r.done}}"throw"===h.type&&(i=f,r.method="throw",r.arg=h.arg)}}}(e,r,a),s}function c(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=l;var u="suspendedStart",d="suspendedYield",p="executing",f="completed",g={};function _(){}function v(){}function m(){}var y={};y[s]=function(){return this};var b=Object.getPrototypeOf,T=b&&b(b(L([])));T&&T!==r&&i.call(T,s)&&(y=T);var C=m.prototype=_.prototype=Object.create(y);function R(e){["next","throw","return"].forEach((function(t){h(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function r(n,s,a,o){var h=c(e[n],e,s);if("throw"!==h.type){var l=h.arg,u=l.value;return u&&"object"==typeof u&&i.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,a,o)}),(function(e){r("throw",e,a,o)})):t.resolve(u).then((function(e){l.value=e,a(l)}),(function(e){return r("throw",e,a,o)}))}o(h.arg)}var n;this._invoke=function(e,i){function s(){return new t((function(t,n){r(e,i,t,n)}))}return n=n?n.then(s,s):s()}}function w(e,r){var i=e.iterator[r.method];if(i===t){if(r.delegate=null,"throw"===r.method){if(e.iterator.return&&(r.method="return",r.arg=t,w(e,r),"throw"===r.method))return g;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return g}var n=c(i,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,g;var s=n.arg;return s?s.done?(r[e.resultName]=s.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):s:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function I(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function L(e){if(e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function r(){for(;++n<e.length;)if(i.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}return{next:k}}function k(){return{value:t,done:!0}}return v.prototype=C.constructor=m,m.constructor=v,v.displayName=h(m,o,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,h(e,o,"GeneratorFunction")),e.prototype=Object.create(C),e},e.awrap=function(e){return{__await:e}},R(E.prototype),E.prototype[a]=function(){return this},e.AsyncIterator=E,e.async=function(t,r,i,n,s){void 0===s&&(s=Promise);var a=new E(l(t,r,i,n),s);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},R(C),h(C,o,"Generator"),C[s]=function(){return this},C.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var i=t.pop();if(i in e)return r.value=i,r.done=!1,r}return r.done=!0,r}},e.values=L,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(i,n){return o.type="throw",o.arg=e,r.next=i,n&&(r.method="next",r.arg=t),!!n}for(var s=this.tryEntries.length-1;s>=0;--s){var a=this.tryEntries[s],o=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var h=i.call(a,"catchLoc"),l=i.call(a,"finallyLoc");if(h&&l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(h){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var s=n;break}}s&&("break"===e||"continue"===e)&&s.tryLoc<=t&&t<=s.finallyLoc&&(s=null);var a=s?s.completion:{};return a.type=e,a.arg=t,s?(this.method="next",this.next=s.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),P(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var i=r.completion;if("throw"===i.type){var n=i.arg;P(r)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,i){return this.delegate={iterator:L(e),resultName:r,nextLoc:i},"next"===this.method&&(this.arg=t),g}},e}(e.exports);try{regeneratorRuntime=i}catch(e){Function("r","regeneratorRuntime = r")(i)}},function(e,t){function r(t,i){return e.exports=r=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},e.exports.default=e.exports,e.exports.__esModule=!0,r(t,i)}e.exports=r,e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,r){"use strict";(function(e){var i=r(67),n=r(68),s=r(69);function a(){return h.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function o(e,t){if(a()<t)throw new RangeError("Invalid typed array length");return h.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=h.prototype:(null===e&&(e=new h(t)),e.length=t),e}function h(e,t,r){if(!(h.TYPED_ARRAY_SUPPORT||this instanceof h))return new h(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return u(this,e)}return l(this,e,t,r)}function l(e,t,r,i){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,i){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(i||0))throw new RangeError("'length' is out of bounds");t=void 0===r&&void 0===i?new Uint8Array(t):void 0===i?new Uint8Array(t,r):new Uint8Array(t,r,i);h.TYPED_ARRAY_SUPPORT?(e=t).__proto__=h.prototype:e=d(e,t);return e}(e,t,r,i):"string"==typeof t?function(e,t,r){"string"==typeof r&&""!==r||(r="utf8");if(!h.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var i=0|f(t,r),n=(e=o(e,i)).write(t,r);n!==i&&(e=e.slice(0,n));return e}(e,t,r):function(e,t){if(h.isBuffer(t)){var r=0|p(t.length);return 0===(e=o(e,r)).length||t.copy(e,0,0,r),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(i=t.length)!=i?o(e,0):d(e,t);if("Buffer"===t.type&&s(t.data))return d(e,t.data)}var i;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function c(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function u(e,t){if(c(t),e=o(e,t<0?0:0|p(t)),!h.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function d(e,t){var r=t.length<0?0:0|p(t.length);e=o(e,r);for(var i=0;i<r;i+=1)e[i]=255&t[i];return e}function p(e){if(e>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|e}function f(e,t){if(h.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return F(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return G(e).length;default:if(i)return F(e).length;t=(""+t).toLowerCase(),i=!0}}function g(e,t,r){var i=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return k(this,t,r);case"utf8":case"utf-8":return S(this,t,r);case"ascii":return I(this,t,r);case"latin1":case"binary":return L(this,t,r);case"base64":return w(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return A(this,t,r);default:if(i)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),i=!0}}function _(e,t,r){var i=e[t];e[t]=e[r],e[r]=i}function v(e,t,r,i,n){if(0===e.length)return-1;if("string"==typeof r?(i=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=n?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(n)return-1;r=e.length-1}else if(r<0){if(!n)return-1;r=0}if("string"==typeof t&&(t=h.from(t,i)),h.isBuffer(t))return 0===t.length?-1:m(e,t,r,i,n);if("number"==typeof t)return t&=255,h.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):m(e,[t],r,i,n);throw new TypeError("val must be string, number or Buffer")}function m(e,t,r,i,n){var s,a=1,o=e.length,h=t.length;if(void 0!==i&&("ucs2"===(i=String(i).toLowerCase())||"ucs-2"===i||"utf16le"===i||"utf-16le"===i)){if(e.length<2||t.length<2)return-1;a=2,o/=2,h/=2,r/=2}function l(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(n){var c=-1;for(s=r;s<o;s++)if(l(e,s)===l(t,-1===c?0:s-c)){if(-1===c&&(c=s),s-c+1===h)return c*a}else-1!==c&&(s-=s-c),c=-1}else for(r+h>o&&(r=o-h),s=r;s>=0;s--){for(var u=!0,d=0;d<h;d++)if(l(e,s+d)!==l(t,d)){u=!1;break}if(u)return s}return-1}function y(e,t,r,i){r=Number(r)||0;var n=e.length-r;i?(i=Number(i))>n&&(i=n):i=n;var s=t.length;if(s%2!=0)throw new TypeError("Invalid hex string");i>s/2&&(i=s/2);for(var a=0;a<i;++a){var o=parseInt(t.substr(2*a,2),16);if(isNaN(o))return a;e[r+a]=o}return a}function b(e,t,r,i){return H(F(t,e.length-r),e,r,i)}function T(e,t,r,i){return H(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,i)}function C(e,t,r,i){return T(e,t,r,i)}function R(e,t,r,i){return H(G(t),e,r,i)}function E(e,t,r,i){return H(function(e,t){for(var r,i,n,s=[],a=0;a<e.length&&!((t-=2)<0);++a)i=(r=e.charCodeAt(a))>>8,n=r%256,s.push(n),s.push(i);return s}(t,e.length-r),e,r,i)}function w(e,t,r){return 0===t&&r===e.length?i.fromByteArray(e):i.fromByteArray(e.slice(t,r))}function S(e,t,r){r=Math.min(e.length,r);for(var i=[],n=t;n<r;){var s,a,o,h,l=e[n],c=null,u=l>239?4:l>223?3:l>191?2:1;if(n+u<=r)switch(u){case 1:l<128&&(c=l);break;case 2:128==(192&(s=e[n+1]))&&(h=(31&l)<<6|63&s)>127&&(c=h);break;case 3:s=e[n+1],a=e[n+2],128==(192&s)&&128==(192&a)&&(h=(15&l)<<12|(63&s)<<6|63&a)>2047&&(h<55296||h>57343)&&(c=h);break;case 4:s=e[n+1],a=e[n+2],o=e[n+3],128==(192&s)&&128==(192&a)&&128==(192&o)&&(h=(15&l)<<18|(63&s)<<12|(63&a)<<6|63&o)>65535&&h<1114112&&(c=h)}null===c?(c=65533,u=1):c>65535&&(c-=65536,i.push(c>>>10&1023|55296),c=56320|1023&c),i.push(c),n+=u}return function(e){var t=e.length;if(t<=P)return String.fromCharCode.apply(String,e);var r="",i=0;for(;i<t;)r+=String.fromCharCode.apply(String,e.slice(i,i+=P));return r}(i)}t.Buffer=h,t.SlowBuffer=function(e){+e!=e&&(e=0);return h.alloc(+e)},t.INSPECT_MAX_BYTES=50,h.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=a(),h.poolSize=8192,h._augment=function(e){return e.__proto__=h.prototype,e},h.from=function(e,t,r){return l(null,e,t,r)},h.TYPED_ARRAY_SUPPORT&&(h.prototype.__proto__=Uint8Array.prototype,h.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&h[Symbol.species]===h&&Object.defineProperty(h,Symbol.species,{value:null,configurable:!0})),h.alloc=function(e,t,r){return function(e,t,r,i){return c(t),t<=0?o(e,t):void 0!==r?"string"==typeof i?o(e,t).fill(r,i):o(e,t).fill(r):o(e,t)}(null,e,t,r)},h.allocUnsafe=function(e){return u(null,e)},h.allocUnsafeSlow=function(e){return u(null,e)},h.isBuffer=function(e){return!(null==e||!e._isBuffer)},h.compare=function(e,t){if(!h.isBuffer(e)||!h.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,i=t.length,n=0,s=Math.min(r,i);n<s;++n)if(e[n]!==t[n]){r=e[n],i=t[n];break}return r<i?-1:i<r?1:0},h.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},h.concat=function(e,t){if(!s(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return h.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var i=h.allocUnsafe(t),n=0;for(r=0;r<e.length;++r){var a=e[r];if(!h.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(i,n),n+=a.length}return i},h.byteLength=f,h.prototype._isBuffer=!0,h.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)_(this,t,t+1);return this},h.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)_(this,t,t+3),_(this,t+1,t+2);return this},h.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)_(this,t,t+7),_(this,t+1,t+6),_(this,t+2,t+5),_(this,t+3,t+4);return this},h.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?S(this,0,e):g.apply(this,arguments)},h.prototype.equals=function(e){if(!h.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===h.compare(this,e)},h.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"},h.prototype.compare=function(e,t,r,i,n){if(!h.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===i&&(i=0),void 0===n&&(n=this.length),t<0||r>e.length||i<0||n>this.length)throw new RangeError("out of range index");if(i>=n&&t>=r)return 0;if(i>=n)return-1;if(t>=r)return 1;if(this===e)return 0;for(var s=(n>>>=0)-(i>>>=0),a=(r>>>=0)-(t>>>=0),o=Math.min(s,a),l=this.slice(i,n),c=e.slice(t,r),u=0;u<o;++u)if(l[u]!==c[u]){s=l[u],a=c[u];break}return s<a?-1:a<s?1:0},h.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},h.prototype.indexOf=function(e,t,r){return v(this,e,t,r,!0)},h.prototype.lastIndexOf=function(e,t,r){return v(this,e,t,r,!1)},h.prototype.write=function(e,t,r,i){if(void 0===t)i="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)i=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===i&&(i="utf8")):(i=r,r=void 0)}var n=this.length-t;if((void 0===r||r>n)&&(r=n),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");for(var s=!1;;)switch(i){case"hex":return y(this,e,t,r);case"utf8":case"utf-8":return b(this,e,t,r);case"ascii":return T(this,e,t,r);case"latin1":case"binary":return C(this,e,t,r);case"base64":return R(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,e,t,r);default:if(s)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),s=!0}},h.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var P=4096;function I(e,t,r){var i="";r=Math.min(e.length,r);for(var n=t;n<r;++n)i+=String.fromCharCode(127&e[n]);return i}function L(e,t,r){var i="";r=Math.min(e.length,r);for(var n=t;n<r;++n)i+=String.fromCharCode(e[n]);return i}function k(e,t,r){var i=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>i)&&(r=i);for(var n="",s=t;s<r;++s)n+=$(e[s]);return n}function A(e,t,r){for(var i=e.slice(t,r),n="",s=0;s<i.length;s+=2)n+=String.fromCharCode(i[s]+256*i[s+1]);return n}function D(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function N(e,t,r,i,n,s){if(!h.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>n||t<s)throw new RangeError('"value" argument is out of bounds');if(r+i>e.length)throw new RangeError("Index out of range")}function O(e,t,r,i){t<0&&(t=65535+t+1);for(var n=0,s=Math.min(e.length-r,2);n<s;++n)e[r+n]=(t&255<<8*(i?n:1-n))>>>8*(i?n:1-n)}function x(e,t,r,i){t<0&&(t=4294967295+t+1);for(var n=0,s=Math.min(e.length-r,4);n<s;++n)e[r+n]=t>>>8*(i?n:3-n)&255}function U(e,t,r,i,n,s){if(r+i>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function B(e,t,r,i,s){return s||U(e,0,r,4),n.write(e,t,r,i,23,4),r+4}function M(e,t,r,i,s){return s||U(e,0,r,8),n.write(e,t,r,i,52,8),r+8}h.prototype.slice=function(e,t){var r,i=this.length;if((e=~~e)<0?(e+=i)<0&&(e=0):e>i&&(e=i),(t=void 0===t?i:~~t)<0?(t+=i)<0&&(t=0):t>i&&(t=i),t<e&&(t=e),h.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=h.prototype;else{var n=t-e;r=new h(n,void 0);for(var s=0;s<n;++s)r[s]=this[s+e]}return r},h.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||D(e,t,this.length);for(var i=this[e],n=1,s=0;++s<t&&(n*=256);)i+=this[e+s]*n;return i},h.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||D(e,t,this.length);for(var i=this[e+--t],n=1;t>0&&(n*=256);)i+=this[e+--t]*n;return i},h.prototype.readUInt8=function(e,t){return t||D(e,1,this.length),this[e]},h.prototype.readUInt16LE=function(e,t){return t||D(e,2,this.length),this[e]|this[e+1]<<8},h.prototype.readUInt16BE=function(e,t){return t||D(e,2,this.length),this[e]<<8|this[e+1]},h.prototype.readUInt32LE=function(e,t){return t||D(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},h.prototype.readUInt32BE=function(e,t){return t||D(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},h.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||D(e,t,this.length);for(var i=this[e],n=1,s=0;++s<t&&(n*=256);)i+=this[e+s]*n;return i>=(n*=128)&&(i-=Math.pow(2,8*t)),i},h.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||D(e,t,this.length);for(var i=t,n=1,s=this[e+--i];i>0&&(n*=256);)s+=this[e+--i]*n;return s>=(n*=128)&&(s-=Math.pow(2,8*t)),s},h.prototype.readInt8=function(e,t){return t||D(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},h.prototype.readInt16LE=function(e,t){t||D(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},h.prototype.readInt16BE=function(e,t){t||D(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},h.prototype.readInt32LE=function(e,t){return t||D(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},h.prototype.readInt32BE=function(e,t){return t||D(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},h.prototype.readFloatLE=function(e,t){return t||D(e,4,this.length),n.read(this,e,!0,23,4)},h.prototype.readFloatBE=function(e,t){return t||D(e,4,this.length),n.read(this,e,!1,23,4)},h.prototype.readDoubleLE=function(e,t){return t||D(e,8,this.length),n.read(this,e,!0,52,8)},h.prototype.readDoubleBE=function(e,t){return t||D(e,8,this.length),n.read(this,e,!1,52,8)},h.prototype.writeUIntLE=function(e,t,r,i){(e=+e,t|=0,r|=0,i)||N(this,e,t,r,Math.pow(2,8*r)-1,0);var n=1,s=0;for(this[t]=255&e;++s<r&&(n*=256);)this[t+s]=e/n&255;return t+r},h.prototype.writeUIntBE=function(e,t,r,i){(e=+e,t|=0,r|=0,i)||N(this,e,t,r,Math.pow(2,8*r)-1,0);var n=r-1,s=1;for(this[t+n]=255&e;--n>=0&&(s*=256);)this[t+n]=e/s&255;return t+r},h.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,1,255,0),h.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},h.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,2,65535,0),h.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):O(this,e,t,!0),t+2},h.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,2,65535,0),h.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):O(this,e,t,!1),t+2},h.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,4,4294967295,0),h.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):x(this,e,t,!0),t+4},h.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,4,4294967295,0),h.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):x(this,e,t,!1),t+4},h.prototype.writeIntLE=function(e,t,r,i){if(e=+e,t|=0,!i){var n=Math.pow(2,8*r-1);N(this,e,t,r,n-1,-n)}var s=0,a=1,o=0;for(this[t]=255&e;++s<r&&(a*=256);)e<0&&0===o&&0!==this[t+s-1]&&(o=1),this[t+s]=(e/a>>0)-o&255;return t+r},h.prototype.writeIntBE=function(e,t,r,i){if(e=+e,t|=0,!i){var n=Math.pow(2,8*r-1);N(this,e,t,r,n-1,-n)}var s=r-1,a=1,o=0;for(this[t+s]=255&e;--s>=0&&(a*=256);)e<0&&0===o&&0!==this[t+s+1]&&(o=1),this[t+s]=(e/a>>0)-o&255;return t+r},h.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,1,127,-128),h.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},h.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,2,32767,-32768),h.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):O(this,e,t,!0),t+2},h.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,2,32767,-32768),h.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):O(this,e,t,!1),t+2},h.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,4,2147483647,-2147483648),h.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):x(this,e,t,!0),t+4},h.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),h.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):x(this,e,t,!1),t+4},h.prototype.writeFloatLE=function(e,t,r){return B(this,e,t,!0,r)},h.prototype.writeFloatBE=function(e,t,r){return B(this,e,t,!1,r)},h.prototype.writeDoubleLE=function(e,t,r){return M(this,e,t,!0,r)},h.prototype.writeDoubleBE=function(e,t,r){return M(this,e,t,!1,r)},h.prototype.copy=function(e,t,r,i){if(r||(r=0),i||0===i||(i=this.length),t>=e.length&&(t=e.length),t||(t=0),i>0&&i<r&&(i=r),i===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),e.length-t<i-r&&(i=e.length-t+r);var n,s=i-r;if(this===e&&r<t&&t<i)for(n=s-1;n>=0;--n)e[n+t]=this[n+r];else if(s<1e3||!h.TYPED_ARRAY_SUPPORT)for(n=0;n<s;++n)e[n+t]=this[n+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+s),t);return s},h.prototype.fill=function(e,t,r,i){if("string"==typeof e){if("string"==typeof t?(i=t,t=0,r=this.length):"string"==typeof r&&(i=r,r=this.length),1===e.length){var n=e.charCodeAt(0);n<256&&(e=n)}if(void 0!==i&&"string"!=typeof i)throw new TypeError("encoding must be a string");if("string"==typeof i&&!h.isEncoding(i))throw new TypeError("Unknown encoding: "+i)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var s;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(s=t;s<r;++s)this[s]=e;else{var a=h.isBuffer(e)?e:F(new h(e,i).toString()),o=a.length;for(s=0;s<r-t;++s)this[s+t]=a[s%o]}return this};var q=/[^+\/0-9A-Za-z-_]/g;function $(e){return e<16?"0"+e.toString(16):e.toString(16)}function F(e,t){var r;t=t||1/0;for(var i=e.length,n=null,s=[],a=0;a<i;++a){if((r=e.charCodeAt(a))>55295&&r<57344){if(!n){if(r>56319){(t-=3)>-1&&s.push(239,191,189);continue}if(a+1===i){(t-=3)>-1&&s.push(239,191,189);continue}n=r;continue}if(r<56320){(t-=3)>-1&&s.push(239,191,189),n=r;continue}r=65536+(n-55296<<10|r-56320)}else n&&(t-=3)>-1&&s.push(239,191,189);if(n=null,r<128){if((t-=1)<0)break;s.push(r)}else if(r<2048){if((t-=2)<0)break;s.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;s.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;s.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return s}function G(e){return i.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(q,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function H(e,t,r,i){for(var n=0;n<i&&!(n+r>=t.length||n>=e.length);++n)t[n+r]=e[n];return n}}).call(this,r(41))},function(e,t,r){"use strict";t.byteLength=function(e){var t=l(e),r=t[0],i=t[1];return 3*(r+i)/4-i},t.toByteArray=function(e){var t,r,i=l(e),a=i[0],o=i[1],h=new s(function(e,t,r){return 3*(t+r)/4-r}(0,a,o)),c=0,u=o>0?a-4:a;for(r=0;r<u;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],h[c++]=t>>16&255,h[c++]=t>>8&255,h[c++]=255&t;2===o&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,h[c++]=255&t);1===o&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,h[c++]=t>>8&255,h[c++]=255&t);return h},t.fromByteArray=function(e){for(var t,r=e.length,n=r%3,s=[],a=16383,o=0,h=r-n;o<h;o+=a)s.push(c(e,o,o+a>h?h:o+a));1===n?(t=e[r-1],s.push(i[t>>2]+i[t<<4&63]+"==")):2===n&&(t=(e[r-2]<<8)+e[r-1],s.push(i[t>>10]+i[t>>4&63]+i[t<<2&63]+"="));return s.join("")};for(var i=[],n=[],s="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,h=a.length;o<h;++o)i[o]=a[o],n[a.charCodeAt(o)]=o;function l(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function c(e,t,r){for(var n,s,a=[],o=t;o<r;o+=3)n=(e[o]<<16&16711680)+(e[o+1]<<8&65280)+(255&e[o+2]),a.push(i[(s=n)>>18&63]+i[s>>12&63]+i[s>>6&63]+i[63&s]);return a.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},function(e,t){t.read=function(e,t,r,i,n){var s,a,o=8*n-i-1,h=(1<<o)-1,l=h>>1,c=-7,u=r?n-1:0,d=r?-1:1,p=e[t+u];for(u+=d,s=p&(1<<-c)-1,p>>=-c,c+=o;c>0;s=256*s+e[t+u],u+=d,c-=8);for(a=s&(1<<-c)-1,s>>=-c,c+=i;c>0;a=256*a+e[t+u],u+=d,c-=8);if(0===s)s=1-l;else{if(s===h)return a?NaN:1/0*(p?-1:1);a+=Math.pow(2,i),s-=l}return(p?-1:1)*a*Math.pow(2,s-i)},t.write=function(e,t,r,i,n,s){var a,o,h,l=8*s-n-1,c=(1<<l)-1,u=c>>1,d=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,p=i?0:s-1,f=i?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(o=isNaN(t)?1:0,a=c):(a=Math.floor(Math.log(t)/Math.LN2),t*(h=Math.pow(2,-a))<1&&(a--,h*=2),(t+=a+u>=1?d/h:d*Math.pow(2,1-u))*h>=2&&(a++,h/=2),a+u>=c?(o=0,a=c):a+u>=1?(o=(t*h-1)*Math.pow(2,n),a+=u):(o=t*Math.pow(2,u-1)*Math.pow(2,n),a=0));n>=8;e[r+p]=255&o,p+=f,o/=256,n-=8);for(a=a<<n|o,l+=n;l>0;e[r+p]=255&a,p+=f,a/=256,l-=8);e[r+p-f]|=128*g}},function(e,t){var r={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==r.call(e)}}]).default}));
//# sourceMappingURL=<EMAIL>