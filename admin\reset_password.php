<?php
require_once '../includes/db.php';

$title = '一键重置密码';
$message = '';
$error = '';

// 处理重置密码请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'reset_admin') {
        // 重置管理员密码
        $username = $_POST['username'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        
        if (empty($username) || empty($new_password)) {
            $error = '用户名和新密码不能为空！';
        } else {
            // 加密新密码
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            
            // 更新数据库
            $sql = "UPDATE admins SET password = ? WHERE username = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ss", $hashed_password, $username);
            
            if ($stmt->execute()) {
                if ($stmt->affected_rows > 0) {
                    $message = "管理员 '{$username}' 的密码已成功重置为: {$new_password}";
                } else {
                    $error = "用户名 '{$username}' 不存在！";
                }
            } else {
                $error = "重置密码失败: " . $conn->error;
            }
        }
    } elseif ($action === 'reset_default') {
        // 重置为默认管理员账户
        $default_username = 'admin';
        $default_password = 'admin123';
        $hashed_password = password_hash($default_password, PASSWORD_DEFAULT);
        
        // 先删除现有的admin用户，再插入新的
        $conn->query("DELETE FROM admins WHERE username = 'admin'");
        
        $sql = "INSERT INTO admins (username, password) VALUES (?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $default_username, $hashed_password);
        
        if ($stmt->execute()) {
            $message = "默认管理员账户已创建！<br>用户名: admin<br>密码: admin123";
        } else {
            $error = "创建默认账户失败: " . $conn->error;
        }
    }
}

// 获取所有管理员账户
$admins_result = $conn->query("SELECT id, username FROM admins ORDER BY id");
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .container { margin-top: 50px; }
        .alert { margin-bottom: 20px; }
        .card { margin-bottom: 20px; }
        .btn-danger { margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <h2 class="text-center mb-4"><?php echo $title; ?></h2>
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- 当前管理员列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5>当前管理员账户</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($admins_result && $admins_result->num_rows > 0): ?>
                            <ul class="list-group">
                                <?php while ($admin = $admins_result->fetch_assoc()): ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>ID: <?php echo $admin['id']; ?> - 用户名: <?php echo htmlspecialchars($admin['username']); ?></span>
                                    </li>
                                <?php endwhile; ?>
                            </ul>
                        <?php else: ?>
                            <p class="text-warning">没有找到管理员账户！</p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 重置指定用户密码 -->
                <div class="card">
                    <div class="card-header">
                        <h5>重置指定用户密码</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="reset_admin">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" name="username" required placeholder="输入要重置密码的用户名">
                            </div>
                            <div class="mb-3">
                                <label for="new_password" class="form-label">新密码</label>
                                <input type="text" class="form-control" id="new_password" name="new_password" required placeholder="输入新密码">
                                <div class="form-text">建议使用强密码，包含字母、数字和特殊字符</div>
                            </div>
                            <button type="submit" class="btn btn-warning">重置密码</button>
                        </form>
                    </div>
                </div>

                <!-- 一键创建默认管理员 -->
                <div class="card">
                    <div class="card-header">
                        <h5>紧急重置 - 创建默认管理员</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">如果忘记了所有管理员密码，可以使用此功能创建默认管理员账户</p>
                        <p><strong>默认账户信息：</strong></p>
                        <ul>
                            <li>用户名: admin</li>
                            <li>密码: admin123</li>
                        </ul>
                        <form method="POST" onsubmit="return confirm('确定要创建默认管理员账户吗？这将覆盖现有的admin用户！')">
                            <input type="hidden" name="action" value="reset_default">
                            <button type="submit" class="btn btn-danger">创建默认管理员</button>
                        </form>
                    </div>
                </div>

                <!-- 快速密码生成器 -->
                <div class="card">
                    <div class="card-header">
                        <h5>密码生成器</h5>
                    </div>
                    <div class="card-body">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="generated_password" readonly placeholder="点击生成密码">
                            <button class="btn btn-outline-secondary" type="button" onclick="generatePassword()">生成密码</button>
                            <button class="btn btn-outline-primary" type="button" onclick="copyPassword()">复制</button>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-secondary">返回管理后台</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 生成随机密码
        function generatePassword() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
            let password = '';
            for (let i = 0; i < 12; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            document.getElementById('generated_password').value = password;
        }

        // 复制密码到剪贴板
        function copyPassword() {
            const passwordField = document.getElementById('generated_password');
            if (passwordField.value) {
                passwordField.select();
                document.execCommand('copy');
                alert('密码已复制到剪贴板！');
            } else {
                alert('请先生成密码！');
            }
        }

        // 自动填充生成的密码
        document.getElementById('generated_password').addEventListener('input', function() {
            document.getElementById('new_password').value = this.value;
        });
    </script>
</body>
</html>