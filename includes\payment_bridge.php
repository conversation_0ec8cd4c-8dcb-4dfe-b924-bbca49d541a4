<?php
/**
 * 支付系统桥接器
 * 统一管理传统换钱系统和新API系统的支付配置
 */

class PaymentBridge {
    private $legacy_db; // 传统系统数据库
    private $api_db;    // API系统数据库
    
    public function __construct() {
        // 传统系统数据库连接
        global $DB;
        $this->legacy_db = $DB;
        
        // API系统数据库连接
        require_once __DIR__ . '/../includes/db.php';
        global $conn;
        $this->api_db = $conn;
    }
    
    /**
     * 获取统一的支付配置
     */
    public function getPaymentConfig() {
        $config = [];
        
        // 从传统系统获取码支付配置
        $legacy_config = $this->getLegacyConfig();
        
        // 从API系统获取新配置
        $api_config = $this->getApiConfig();
        
        // 合并配置
        $config = array_merge($legacy_config, $api_config);
        
        return $config;
    }
    
    /**
     * 获取传统系统配置
     */
    private function getLegacyConfig() {
        $config = [];
        
        if ($this->legacy_db) {
            $result = $this->legacy_db->query("SELECT k, v FROM ds_config WHERE k IN ('yzf_url', 'yzf_id', 'yzf_key', 'on')");
            while ($row = $this->legacy_db->fetch($result)) {
                switch ($row['k']) {
                    case 'yzf_url':
                        $config['legacy_epay_api_url'] = $row['v'];
                        break;
                    case 'yzf_id':
                        $config['legacy_epay_partner_id'] = $row['v'];
                        break;
                    case 'yzf_key':
                        $config['legacy_epay_partner_key'] = $row['v'];
                        break;
                    case 'on':
                        $config['legacy_system_enabled'] = $row['v'];
                        break;
                }
            }
        }
        
        return $config;
    }
    
    /**
     * 获取API系统配置
     */
    private function getApiConfig() {
        $config = [];
        
        if ($this->api_db) {
            $stmt = $this->api_db->prepare("
                SELECT setting_key, setting_value 
                FROM settings 
                WHERE setting_key LIKE 'epay_%' OR setting_key LIKE 'wechat_pay_%'
            ");
            $stmt->execute();
            $result = $stmt->get_result();
            
            while ($row = $result->fetch_assoc()) {
                $config[$row['setting_key']] = $row['setting_value'];
            }
        }
        
        return $config;
    }
    
    /**
     * 同步配置到两个系统
     */
    public function syncConfig($config_data) {
        // 同步到传统系统
        $this->syncToLegacySystem($config_data);
        
        // 同步到API系统
        $this->syncToApiSystem($config_data);
    }
    
    /**
     * 同步到传统系统
     */
    private function syncToLegacySystem($config_data) {
        if (!$this->legacy_db) return;
        
        $legacy_mapping = [
            'epay_api_url' => 'yzf_url',
            'epay_partner_id' => 'yzf_id',
            'epay_partner_key' => 'yzf_key'
        ];
        
        foreach ($legacy_mapping as $new_key => $legacy_key) {
            if (isset($config_data[$new_key])) {
                $value = $this->legacy_db->escape($config_data[$new_key]);
                $this->legacy_db->query("REPLACE INTO ds_config SET k='$legacy_key', v='$value'");
            }
        }
    }
    
    /**
     * 同步到API系统
     */
    private function syncToApiSystem($config_data) {
        if (!$this->api_db) return;
        
        foreach ($config_data as $key => $value) {
            if (strpos($key, 'epay_') === 0 || strpos($key, 'wechat_pay_') === 0) {
                $stmt = $this->api_db->prepare("
                    INSERT INTO settings (setting_key, setting_value, setting_type, description, group_name) 
                    VALUES (?, ?, 'string', '支付配置项', 'payment') 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->bind_param("ss", $key, $value);
                $stmt->execute();
            }
        }
    }
    
    /**
     * 创建传统系统订单
     */
    public function createLegacyOrder($order_data) {
        if (!$this->legacy_db) return false;
        
        $trade_no = $order_data['trade_no'];
        $name = $this->legacy_db->escape($order_data['name']);
        $money = $this->legacy_db->escape($order_data['money']);
        $userid = $this->legacy_db->escape($order_data['userid'] ?? '');
        $ip = $this->legacy_db->escape($order_data['ip'] ?? $_SERVER['REMOTE_ADDR']);
        
        $sql = "INSERT INTO ds_pay (trade_no, name, money, userid, ip, addtime, status) 
                VALUES ('$trade_no', '$name', '$money', '$userid', '$ip', NOW(), 0)";
        
        return $this->legacy_db->query($sql);
    }
    
    /**
     * 获取传统系统订单状态
     */
    public function getLegacyOrderStatus($trade_no) {
        if (!$this->legacy_db) return null;
        
        $trade_no = $this->legacy_db->escape($trade_no);
        $row = $this->legacy_db->get_row("SELECT * FROM ds_pay WHERE trade_no='$trade_no'");
        
        return $row;
    }
    
    /**
     * 更新传统系统订单状态
     */
    public function updateLegacyOrderStatus($trade_no, $status) {
        if (!$this->legacy_db) return false;
        
        $trade_no = $this->legacy_db->escape($trade_no);
        $status = intval($status);
        $endtime = $status == 1 ? ", endtime=NOW()" : "";
        
        return $this->legacy_db->query("UPDATE ds_pay SET status='$status' $endtime WHERE trade_no='$trade_no'");
    }
    
    /**
     * 获取可用的支付方式（整合两个系统）
     */
    public function getAvailablePaymentMethods() {
        $config = $this->getPaymentConfig();
        $methods = [];
        
        // 检查传统系统是否启用
        $legacy_enabled = isset($config['legacy_system_enabled']) && $config['legacy_system_enabled'] == '1';
        
        // 检查API系统微信支付
        $wechat_enabled = isset($config['wechat_pay_enabled']) && $config['wechat_pay_enabled'] == '1';
        
        // 检查API系统码支付易支付
        $epay_enabled = isset($config['epay_enabled']) && $config['epay_enabled'] == '1';
        
        if ($wechat_enabled) {
            $methods[] = [
                'id' => 'wechat',
                'name' => '微信支付',
                'type' => 'wechat',
                'system' => 'api',
                'enabled' => true
            ];
        }
        
        if ($epay_enabled || $legacy_enabled) {
            $supported_methods = explode(',', $config['epay_supported_methods'] ?? 'alipay,wxpay,qqpay');
            
            foreach ($supported_methods as $method) {
                $method = trim($method);
                if ($method) {
                    $methods[] = [
                        'id' => 'epay_' . $method,
                        'name' => $this->getPaymentMethodName($method),
                        'type' => 'epay',
                        'epay_type' => $method,
                        'system' => $epay_enabled ? 'api' : 'legacy',
                        'enabled' => true
                    ];
                }
            }
        }
        
        return $methods;
    }
    
    /**
     * 获取支付方式名称
     */
    private function getPaymentMethodName($method) {
        $names = [
            'alipay' => '支付宝',
            'wxpay' => '微信支付（扫码）',
            'qqpay' => 'QQ钱包'
        ];
        
        return $names[$method] ?? $method;
    }
    
    /**
     * 生成传统系统支付URL
     */
    public function generateLegacyPaymentUrl($trade_no, $payment_type) {
        global $siteurl;
        return $siteurl . "other/submit.php?type={$payment_type}&orderid={$trade_no}";
    }
}

// 全局实例
$GLOBALS['payment_bridge'] = new PaymentBridge();

/**
 * 获取支付桥接器实例
 */
function getPaymentBridge() {
    return $GLOBALS['payment_bridge'];
}
?>
