<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    switch ($_POST['action']) {
        case 'create':
            createCategory();
            break;
        case 'update':
            updateCategory();
            break;
        case 'delete':
            deleteCategory();
            break;
        case 'get':
            getCategory();
            break;
        default:
            echo json_encode(['success' => false, 'message' => '未知操作']);
    }
    exit;
}

function createCategory() {
    global $conn;
    
    $name = $_POST['name'] ?? '';
    $description = $_POST['description'] ?? '';
    $sort_order = (int)($_POST['sort_order'] ?? 0);
    $is_active = (int)($_POST['is_active'] ?? 1);
    
    if (empty($name)) {
        echo json_encode(['success' => false, 'message' => '分类名称不能为空']);
        return;
    }
    
    $sql = "INSERT INTO announcement_categories (name, description, sort_order, is_active) VALUES (?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssii", $name, $description, $sort_order, $is_active);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => '创建成功']);
    } else {
        echo json_encode(['success' => false, 'message' => '创建失败：' . $conn->error]);
    }
}

function updateCategory() {
    global $conn;
    
    $id = (int)$_POST['id'];
    $name = $_POST['name'] ?? '';
    $description = $_POST['description'] ?? '';
    $sort_order = (int)($_POST['sort_order'] ?? 0);
    $is_active = (int)($_POST['is_active'] ?? 1);
    
    if (empty($name)) {
        echo json_encode(['success' => false, 'message' => '分类名称不能为空']);
        return;
    }
    
    $sql = "UPDATE announcement_categories SET name=?, description=?, sort_order=?, is_active=? WHERE id=?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssiii", $name, $description, $sort_order, $is_active, $id);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => '更新成功']);
    } else {
        echo json_encode(['success' => false, 'message' => '更新失败：' . $conn->error]);
    }
}

function deleteCategory() {
    global $conn;
    
    $id = (int)$_POST['id'];
    
    // 检查是否有公告使用此分类
    $check_sql = "SELECT COUNT(*) as count FROM announcements WHERE category_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("i", $id);
    $check_stmt->execute();
    $count = $check_stmt->get_result()->fetch_assoc()['count'];
    
    if ($count > 0) {
        echo json_encode(['success' => false, 'message' => "无法删除，还有 {$count} 个公告使用此分类"]);
        return;
    }
    
    $sql = "DELETE FROM announcement_categories WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => '删除成功']);
    } else {
        echo json_encode(['success' => false, 'message' => '删除失败：' . $conn->error]);
    }
}

function getCategory() {
    global $conn;
    
    $id = (int)$_POST['id'];
    
    $sql = "SELECT * FROM announcement_categories WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        echo json_encode(['success' => true, 'data' => $row]);
    } else {
        echo json_encode(['success' => false, 'message' => '分类不存在']);
    }
}

// 获取分类列表
$categories_result = $conn->query("SELECT c.*, 
    (SELECT COUNT(*) FROM announcements WHERE category_id = c.id) as announcement_count 
    FROM announcement_categories c 
    ORDER BY c.sort_order ASC, c.id ASC");

$categories = [];
while ($row = $categories_result->fetch_assoc()) {
    $categories[] = $row;
}

render_admin_header('公告分类管理', 'announcements');
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>公告分类管理</h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal" onclick="openCreateModal()">
                    <i class="fas fa-plus"></i> 新建分类
                </button>
            </div>
            
            <!-- 分类列表 -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>名称</th>
                                    <th>描述</th>
                                    <th>排序</th>
                                    <th>状态</th>
                                    <th>公告数量</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($categories as $category): ?>
                                <tr>
                                    <td><?= $category['id'] ?></td>
                                    <td><?= htmlspecialchars($category['name']) ?></td>
                                    <td>
                                        <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                            <?= htmlspecialchars($category['description']) ?>
                                        </div>
                                    </td>
                                    <td><?= $category['sort_order'] ?></td>
                                    <td>
                                        <span class="badge <?= $category['is_active'] ? 'bg-success' : 'bg-secondary' ?>">
                                            <?= $category['is_active'] ? '启用' : '禁用' ?>
                                        </span>
                                    </td>
                                    <td><?= $category['announcement_count'] ?></td>
                                    <td><?= date('Y-m-d H:i', strtotime($category['created_at'])) ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="editCategory(<?= $category['id'] ?>)">编辑</button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteCategoryConfirm(<?= $category['id'] ?>)">删除</button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分类编辑模态框 -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">新建分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="categoryForm">
                <div class="modal-body">
                    <input type="hidden" id="categoryId">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">分类名称 *</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">描述</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">排序</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" value="0">
                                <div class="form-text">数字越小越靠前</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="is_active" class="form-label">状态</label>
                                <select class="form-select" id="is_active" name="is_active">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let isEditing = false;

function openCreateModal() {
    isEditing = false;
    document.getElementById('modalTitle').textContent = '新建分类';
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryId').value = '';
    document.getElementById('is_active').value = '1';
}

function editCategory(id) {
    isEditing = true;
    document.getElementById('modalTitle').textContent = '编辑分类';
    
    // 获取分类数据
    fetch('announcement_categories.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=get&id=' + id
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const category = data.data;
            document.getElementById('categoryId').value = category.id;
            document.getElementById('name').value = category.name;
            document.getElementById('description').value = category.description || '';
            document.getElementById('sort_order').value = category.sort_order;
            document.getElementById('is_active').value = category.is_active;
            
            new bootstrap.Modal(document.getElementById('categoryModal')).show();
        } else {
            alert('获取分类数据失败：' + data.message);
        }
    });
}

function deleteCategoryConfirm(id) {
    if (confirm('确定要删除这个分类吗？')) {
        fetch('announcement_categories.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=delete&id=' + id
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('删除成功');
                location.reload();
            } else {
                alert('删除失败：' + data.message);
            }
        });
    }
}

document.getElementById('categoryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append('action', isEditing ? 'update' : 'create');
    
    if (isEditing) {
        formData.append('id', document.getElementById('categoryId').value);
    }
    
    fetch('announcement_categories.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            bootstrap.Modal.getInstance(document.getElementById('categoryModal')).hide();
            location.reload();
        } else {
            alert('操作失败：' + data.message);
        }
    });
});
</script>

<?php render_admin_footer(); ?>