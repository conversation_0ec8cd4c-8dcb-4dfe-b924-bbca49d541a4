<?php
/**
 * 获取用户收藏的公告列表API
 * 支持JWT认证，分页查询用户收藏的公告
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    // 验证用户认证
    $user = $auth->requireAuth();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        getFavoriteAnnouncements($auth, $user);
    } else {
        $auth->jsonResponse(405, '不支持的请求方法');
    }
} catch (Exception $e) {
    error_log('获取收藏公告列表API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 获取用户收藏的公告列表
 */
function getFavoriteAnnouncements($auth, $user) {
    $conn = $auth->getConn();
    
    $user_id = $user['id'];
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    // 验证分页参数
    if ($page < 1) $page = 1;
    if ($limit < 1 || $limit > 100) $limit = 10;
    
    $offset = ($page - 1) * $limit;
    
    // 构建查询条件
    $where_conditions = ["af.user_id = ?", "a.status = 'published'"];
    $params = [$user_id];
    $param_types = "i";
    
    if (!empty($search)) {
        $where_conditions[] = "(a.title LIKE ? OR a.content LIKE ?)";
        $search_param = "%{$search}%";
        $params[] = $search_param;
        $params[] = $search_param;
        $param_types .= "ss";
    }
    
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
    
    // 获取总数
    $count_sql = "SELECT COUNT(*) as total 
                  FROM announcement_favorites af 
                  INNER JOIN announcements a ON af.announcement_id = a.id 
                  $where_clause";
    
    $count_stmt = $conn->prepare($count_sql);
    if (!empty($params)) {
        $count_stmt->bind_param($param_types, ...$params);
    }
    $count_stmt->execute();
    $total = $count_stmt->get_result()->fetch_assoc()['total'];
    
    // 获取数据
    $sql = "SELECT 
                a.id, a.title, a.content, a.type, a.priority, a.is_pinned,
                a.category_id, a.publish_time, a.expire_time, a.view_count,
                a.created_at, a.updated_at,
                c.name as category_name,
                ad.username as author_name,
                af.created_at as favorite_time
            FROM announcement_favorites af
            INNER JOIN announcements a ON af.announcement_id = a.id
            LEFT JOIN announcement_categories c ON a.category_id = c.id
            LEFT JOIN admins ad ON a.author_id = ad.id
            $where_clause
            ORDER BY af.created_at DESC 
            LIMIT ? OFFSET ?";
    
    // 添加分页参数
    $params[] = $limit;
    $params[] = $offset;
    $param_types .= "ii";
    
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $announcements = [];
    while ($row = $result->fetch_assoc()) {
        // 处理内容长度
        $content_preview = mb_strlen($row['content']) > 200 
            ? mb_substr($row['content'], 0, 200) . '...' 
            : $row['content'];
        
        // 格式化时间
        $publish_time = $row['publish_time'] ? date('Y-m-d H:i:s', strtotime($row['publish_time'])) : null;
        $expire_time = $row['expire_time'] ? date('Y-m-d H:i:s', strtotime($row['expire_time'])) : null;
        $favorite_time = date('Y-m-d H:i:s', strtotime($row['favorite_time']));
        
        // 检查是否过期
        $is_expired = $row['expire_time'] && strtotime($row['expire_time']) < time();
        
        $announcements[] = [
            'id' => (int)$row['id'],
            'title' => $row['title'],
            'content' => $row['content'],
            'content_preview' => $content_preview,
            'type' => $row['type'],
            'priority' => (int)$row['priority'],
            'is_pinned' => (bool)$row['is_pinned'],
            'category_id' => $row['category_id'] ? (int)$row['category_id'] : null,
            'category_name' => $row['category_name'],
            'publish_time' => $publish_time,
            'expire_time' => $expire_time,
            'is_expired' => $is_expired,
            'view_count' => (int)$row['view_count'],
            'author_name' => $row['author_name'],
            'created_at' => date('Y-m-d H:i:s', strtotime($row['created_at'])),
            'updated_at' => date('Y-m-d H:i:s', strtotime($row['updated_at'])),
            'favorite_time' => $favorite_time
        ];
    }
    
    // 计算分页信息
    $total_pages = ceil($total / $limit);
    $has_next = $page < $total_pages;
    $has_prev = $page > 1;
    
    $auth->jsonResponse(200, '获取收藏公告列表成功', [
        'announcements' => $announcements,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => (int)$total,
            'total_pages' => $total_pages,
            'has_next' => $has_next,
            'has_prev' => $has_prev
        ],
        'search' => $search
    ]);
}
?>
