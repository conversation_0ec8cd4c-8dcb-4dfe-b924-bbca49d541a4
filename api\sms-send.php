<?php
/**
 * 短信验证码发送API
 * 支持手机号绑定、登录等场景的验证码发送
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

$auth = new AuthAPI();

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    $auth->jsonResponse(400, '无效的请求数据');
}

$phone = trim($input['phone'] ?? '');
$type = $input['type'] ?? 'bind'; // bind, login, reset
$user_id = null;

// 验证手机号格式
if (empty($phone)) {
    $auth->jsonResponse(400, '请输入手机号');
}

if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    $auth->jsonResponse(400, '手机号格式不正确');
}

try {
    // 获取系统配置
    $settings_result = $auth->getConn()->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('sms_code_length', 'sms_code_expire_minutes', 'sms_daily_limit', 'sms_provider', 'phone_bind_required')");
    $settings = [];
    while ($row = $settings_result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    
    $code_length = intval($settings['sms_code_length'] ?? 6);
    $expire_minutes = intval($settings['sms_code_expire_minutes'] ?? 5);
    $daily_limit = intval($settings['sms_daily_limit'] ?? 10);
    $sms_provider = $settings['sms_provider'] ?? 'mock';
    $phone_bind_required = $settings['phone_bind_required'] ?? '1';
    
    // 如果手机号绑定功能未开启，拒绝请求
    if ($phone_bind_required !== '1' && $type === 'bind') {
        $auth->jsonResponse(400, '手机号绑定功能未开启');
    }
    
    // 检查今日发送次数限制
    $today_count_stmt = $auth->getConn()->prepare("SELECT COUNT(*) as count FROM sms_codes WHERE phone = ? AND DATE(created_at) = CURDATE()");
    $today_count_stmt->bind_param("s", $phone);
    $today_count_stmt->execute();
    $today_count = $today_count_stmt->get_result()->fetch_assoc()['count'];
    
    if ($today_count >= $daily_limit) {
        $auth->jsonResponse(429, "今日发送次数已达上限（{$daily_limit}次），请明天再试");
    }
    
    // 检查1分钟内是否已发送
    $recent_stmt = $auth->getConn()->prepare("SELECT id FROM sms_codes WHERE phone = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE) ORDER BY created_at DESC LIMIT 1");
    $recent_stmt->bind_param("s", $phone);
    $recent_stmt->execute();
    $recent_result = $recent_stmt->get_result();
    
    if ($recent_result->num_rows > 0) {
        $auth->jsonResponse(429, '发送过于频繁，请1分钟后再试');
    }
    
    // 根据类型进行不同的验证
    switch ($type) {
        case 'bind':
            // 绑定验证：需要用户登录
            $user = $auth->getCurrentUser();
            if (!$user) {
                $auth->jsonResponse(401, '请先登录');
            }
            $user_id = $user['id'];
            
            // 检查手机号是否已被其他用户绑定
            $check_stmt = $auth->getConn()->prepare("SELECT id, name FROM users WHERE phone = ? AND id != ? AND phone_verified = 1");
            $check_stmt->bind_param("si", $phone, $user_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $auth->jsonResponse(400, '该手机号已被其他用户绑定');
            }
            break;
            
        case 'login':
            // 登录验证：检查手机号是否已注册
            $check_stmt = $auth->getConn()->prepare("SELECT id FROM users WHERE phone = ? AND phone_verified = 1 AND status = 'active'");
            $check_stmt->bind_param("s", $phone);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows === 0) {
                $auth->jsonResponse(400, '该手机号未注册或未验证');
            }
            break;
            
        case 'reset':
            // 重置验证：检查手机号是否存在
            $check_stmt = $auth->getConn()->prepare("SELECT id FROM users WHERE phone = ? AND phone_verified = 1");
            $check_stmt->bind_param("s", $phone);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows === 0) {
                $auth->jsonResponse(400, '该手机号未绑定任何账户');
            }
            break;
            
        default:
            $auth->jsonResponse(400, '无效的验证码类型');
    }
    
    // 生成验证码
    $code = '';
    for ($i = 0; $i < $code_length; $i++) {
        $code .= mt_rand(0, 9);
    }
    
    // 计算过期时间
    $expires_at = date('Y-m-d H:i:s', time() + ($expire_minutes * 60));
    
    // 获取客户端信息
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
    
    // 保存验证码到数据库
    $stmt = $auth->getConn()->prepare("INSERT INTO sms_codes (phone, code, type, user_id, ip_address, expires_at) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("sssiss", $phone, $code, $type, $user_id, $ip_address, $expires_at);
    
    if (!$stmt->execute()) {
        $auth->jsonResponse(500, '验证码生成失败');
    }
    
    // 发送短信
    $sms_result = sendSMS($phone, $code, $expire_minutes, $sms_provider, $settings);
    
    if (!$sms_result['success']) {
        // 如果发送失败，删除刚才插入的验证码记录
        $delete_stmt = $auth->getConn()->prepare("DELETE FROM sms_codes WHERE phone = ? AND code = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)");
        $delete_stmt->bind_param("ss", $phone, $code);
        $delete_stmt->execute();
        
        $auth->jsonResponse(500, '短信发送失败：' . $sms_result['message']);
    }
    
    // 返回成功响应
    $auth->jsonResponse(200, '验证码发送成功', [
        'phone' => $phone,
        'expire_minutes' => $expire_minutes,
        'daily_remaining' => $daily_limit - $today_count - 1
    ]);
    
} catch (Exception $e) {
    error_log('短信发送错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 发送短信验证码
 */
function sendSMS($phone, $code, $expire_minutes, $provider, $settings) {
    switch ($provider) {
        case 'mock':
            // 模拟发送（开发测试用）
            error_log("Mock SMS: 发送验证码 {$code} 到手机号 {$phone}，有效期 {$expire_minutes} 分钟");
            return ['success' => true, 'message' => '模拟发送成功'];
            
        case 'aliyun':
            // 阿里云短信服务
            return sendAliyunSMS($phone, $code, $expire_minutes, $settings);
            
        case 'tencent':
            // 腾讯云短信服务
            return sendTencentSMS($phone, $code, $expire_minutes, $settings);
            
        default:
            return ['success' => false, 'message' => '未配置短信服务商'];
    }
}

/**
 * 阿里云短信发送（示例）
 */
function sendAliyunSMS($phone, $code, $expire_minutes, $settings) {
    // 这里需要集成阿里云短信SDK
    // 示例代码，实际使用时需要安装阿里云SDK
    return ['success' => false, 'message' => '阿里云短信服务未配置'];
}

/**
 * 腾讯云短信发送（示例）
 */
function sendTencentSMS($phone, $code, $expire_minutes, $settings) {
    // 这里需要集成腾讯云短信SDK
    // 示例代码，实际使用时需要安装腾讯云SDK
    return ['success' => false, 'message' => '腾讯云短信服务未配置'];
}
