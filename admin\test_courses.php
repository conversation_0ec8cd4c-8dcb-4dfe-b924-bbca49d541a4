<?php
session_start();
require_once '../includes/db.php';

// 简单的测试页面，检查课程数据
echo "<h1>课程管理测试页面</h1>";

// 检查数据库连接
if ($conn->connect_error) {
    die("数据库连接失败: " . $conn->connect_error);
}

echo "<h2>数据库连接成功</h2>";

// 检查courses表结构
echo "<h2>Courses表结构:</h2>";
$result = $conn->query("DESCRIBE courses");
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// 检查课程数据
echo "<h2>课程数据:</h2>";
$result = $conn->query("SELECT id, title, price, original_price, is_free, tags, difficulty, status FROM courses LIMIT 5");
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>标题</th><th>价格</th><th>原价</th><th>是否免费</th><th>标签</th><th>难度</th><th>状态</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['title']) . "</td>";
        echo "<td>" . $row['price'] . "</td>";
        echo "<td>" . $row['original_price'] . "</td>";
        echo "<td>" . $row['is_free'] . "</td>";
        echo "<td>" . htmlspecialchars($row['tags'] ?? '') . "</td>";
        echo "<td>" . ($row['difficulty'] ?? 'beginner') . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<br><a href='courses.php'>返回课程管理</a>";
?>
