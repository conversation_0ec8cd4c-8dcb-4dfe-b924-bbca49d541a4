<?php
/**
 * 后台管理系统通用模板
 * 提供标准化的UI布局和组件
 */

// 确保已登录
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header("Location: login.php");
    exit;
}

/**
 * 获取管理面板配置
 * @return array 包含标题配置的数组
 */
function get_admin_panel_config() {
    // 默认配置
    $default_config = [
        'admin_panel_title' => '管理中心',
        'admin_panel_subtitle' => 'Admin Panel'
    ];

    // 尝试从数据库获取配置
    try {
        // 检查是否有数据库连接
        global $conn;
        if (isset($conn) && $conn instanceof mysqli) {
            $result = $conn->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('admin_panel_title', 'admin_panel_subtitle')");
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $default_config[$row['setting_key']] = $row['setting_value'];
                }
            }
        }
    } catch (Exception $e) {
        // 如果数据库查询失败，使用默认配置
        error_log("获取管理面板配置失败: " . $e->getMessage());
    }

    return $default_config;
}

/**
 * 渲染管理页面头部
 * @param string $title 页面标题
 * @param string $current_page 当前页面标识
 */
function render_admin_header($title = '后台管理', $current_page = '') {
    // 获取管理面板配置
    $panel_config = get_admin_panel_config();
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo htmlspecialchars($title); ?></title>
        <link rel="stylesheet" href="../assets/css/admin.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    </head>
    <body>
        <div class="admin-layout">
            <!-- 侧边栏 -->
            <nav class="admin-sidebar">
                <div class="admin-sidebar-header">
                    <div class="admin-sidebar-title"><?php echo htmlspecialchars($panel_config['admin_panel_title']); ?></div>
                    <div class="admin-sidebar-subtitle"><?php echo htmlspecialchars($panel_config['admin_panel_subtitle']); ?></div>
                </div>

                <div class="admin-nav">
                    <?php render_nav_items($current_page); ?>
                </div>
            </nav>
            
            <!-- 主内容区 -->
            <main class="admin-main">
                <!-- 顶部导航 -->
                <header class="admin-header">
                    <h1 class="admin-header-title"><?php echo htmlspecialchars($title); ?></h1>
                    <div class="admin-header-user">
                        <div class="admin-user-info">
                            <i class="fas fa-user"></i>
                            <span><?php echo htmlspecialchars($_SESSION['username']); ?></span>
                        </div>
                        <a href="logout.php" class="admin-logout-btn">
                            <i class="fas fa-sign-out-alt"></i> 退出登录
                        </a>
                    </div>
                </header>
                
                <!-- 内容区域 -->
                <div class="admin-content">
    <?php
}

/**
 * 渲染导航菜单项
 * @param string $current_page 当前页面标识
 */
function render_nav_items($current_page) {
    $nav_items = [
        'dashboard' => [
            'url' => 'index.php',
            'icon' => 'fas fa-tachometer-alt',
            'title' => '仪表盘'
        ],
        'users' => [
            'url' => 'users.php',
            'icon' => 'fas fa-users',
            'title' => '用户管理'

                    ],
        'announcements' => [
            'url' => 'announcements.php',
            'icon' => 'fas fa-bullhorn',
            'title' => '公告管理'
        ],
        'banners' => [
            'url' => 'banners.php',
            'icon' => 'fas fa-images',
            'title' => '轮播图管理'
        ],

        'courses' => [
            'url' => 'courses.php',
            'icon' => 'fas fa-play-circle',
            'title' => '课程管理'
        ],
        'user_courses' => [
            'url' => 'user_courses.php',
            'icon' => 'fas fa-user-graduate',
            'title' => '用户课程'
        ],
        'orders' => [
            'url' => 'orders.php',
            'icon' => 'fas fa-shopping-cart',
            'title' => '订单管理'
        ],
        'admins' => [
            'url' => 'admins.php',
            'icon' => 'fas fa-user-shield',
            'title' => '管理员管理'
        ],
        'system_status' => [
            'url' => 'system_status.php',
            'icon' => 'fas fa-heartbeat',
            'title' => '系统状态'
        ],
        'settings' => [
            'url' => 'settings.php',
            'icon' => 'fas fa-cog',
            'title' => '系统设置'
        ]
    ];
    
    foreach ($nav_items as $key => $item) {
        $active_class = ($current_page === $key) ? ' active' : '';
        echo '<a href="' . $item['url'] . '" class="admin-nav-item' . $active_class . '">';
        echo '<i class="' . $item['icon'] . '"></i>';
        echo $item['title'];
        echo '</a>';
    }
}

/**
 * 渲染管理页面尾部
 */
function render_admin_footer() {
    ?>
                </div>
            </main>
        </div>
        
        <script>
        // 确认删除对话框
        function confirmDelete(message = '您确定要删除吗？') {
            return confirm(message);
        }
        
        // 移动端侧边栏切换
        function toggleSidebar() {
            const sidebar = document.querySelector('.admin-sidebar');
            sidebar.classList.toggle('active');
        }
        
        // 表单验证
        function validateForm(formId) {
            const form = document.getElementById(formId);
            const inputs = form.querySelectorAll('input[required]');
            let isValid = true;
            
            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.style.borderColor = '#e74c3c';
                    isValid = false;
                } else {
                    input.style.borderColor = '#e1e8ed';
                }
            });
            
            return isValid;
        }
        </script>
    </body>
    </html>
    <?php
}

/**
 * 渲染卡片容器开始
 * @param string $title 卡片标题
 */
function render_card_start($title = '') {
    echo '<div class="admin-card">';
    if ($title) {
        echo '<div class="admin-card-header">';
        echo '<h3 class="admin-card-title">' . htmlspecialchars($title) . '</h3>';
        echo '</div>';
    }
    echo '<div class="admin-card-body">';
}

/**
 * 渲染卡片容器结束
 */
function render_card_end() {
    echo '</div></div>';
}

/**
 * 渲染表单开始
 * @param string $action 表单提交地址
 * @param string $method 提交方法
 * @param string $id 表单ID
 */
function render_form_start($action = '', $method = 'post', $id = '') {
    echo '<form class="admin-form" method="' . $method . '"';
    if ($action) echo ' action="' . htmlspecialchars($action) . '"';
    if ($id) echo ' id="' . htmlspecialchars($id) . '"';
    echo '>';
}

/**
 * 渲染表单结束
 */
function render_form_end() {
    echo '</form>';
}

/**
 * 渲染表单输入组
 * @param string $label 标签文本
 * @param string $name 输入框名称
 * @param string $type 输入框类型
 * @param string $value 默认值
 * @param bool $required 是否必填
 * @param string $placeholder 占位符
 */
function render_form_input($label, $name, $type = 'text', $value = '', $required = false, $placeholder = '') {
    echo '<div class="admin-form-group">';
    echo '<label class="admin-form-label" for="' . htmlspecialchars($name) . '">' . htmlspecialchars($label) . '</label>';
    echo '<input class="admin-form-input" type="' . $type . '" name="' . htmlspecialchars($name) . '" id="' . htmlspecialchars($name) . '"';
    if ($value) echo ' value="' . htmlspecialchars($value) . '"';
    if ($placeholder) echo ' placeholder="' . htmlspecialchars($placeholder) . '"';
    if ($required) echo ' required';
    echo '>';
    echo '</div>';
}

/**
 * 渲染按钮
 * @param string $text 按钮文本
 * @param string $type 按钮类型
 * @param string $class 额外CSS类
 * @param string $onclick 点击事件
 */
function render_button($text, $type = 'submit', $class = 'admin-btn-primary', $onclick = '') {
    echo '<button type="' . $type . '" class="admin-btn ' . $class . '"';
    if ($onclick) echo ' onclick="' . htmlspecialchars($onclick) . '"';
    echo '>' . htmlspecialchars($text) . '</button>';
}

/**
 * 渲染链接按钮
 * @param string $text 按钮文本
 * @param string $url 链接地址
 * @param string $class 按钮样式类
 * @param string $onclick 点击事件
 */
function render_link_button($text, $url, $class = 'admin-btn-secondary', $onclick = '') {
    echo '<a href="' . htmlspecialchars($url) . '" class="admin-btn ' . $class . '"';
    if ($onclick) echo ' onclick="' . htmlspecialchars($onclick) . '"';
    echo '>' . htmlspecialchars($text) . '</a>';
}

/**
 * 渲染表格开始
 * @param array $headers 表头数组
 */
function render_table_start($headers = []) {
    echo '<table class="admin-table">';
    if (!empty($headers)) {
        echo '<thead><tr>';
        foreach ($headers as $header) {
            echo '<th>' . htmlspecialchars($header) . '</th>';
        }
        echo '</tr></thead>';
    }
    echo '<tbody>';
}

/**
 * 渲染表格结束
 */
function render_table_end() {
    echo '</tbody></table>';
}

/**
 * 渲染状态标签
 * @param string $text 标签文本
 * @param string $type 标签类型 (success, danger, warning, info)
 */
function render_badge($text, $type = 'info') {
    echo '<span class="admin-badge admin-badge-' . $type . '">' . htmlspecialchars($text) . '</span>';
}

/**
 * 显示成功消息
 * @param string $message 消息内容
 */
function show_success_message($message) {
    echo '<div class="alert alert-success" style="background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin-bottom: 20px;">';
    echo '<i class="fas fa-check-circle"></i> ' . htmlspecialchars($message);
    echo '</div>';
}

/**
 * 显示错误消息
 * @param string $message 消息内容
 */
function show_error_message($message) {
    echo '<div class="alert alert-danger" style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin-bottom: 20px;">';
    echo '<i class="fas fa-exclamation-circle"></i> ' . htmlspecialchars($message);
    echo '</div>';
}

/**
 * 渲染分页组件
 * @param int $current_page 当前页码
 * @param int $total_pages 总页数
 * @param string $base_url 基础URL
 * @param array $params 额外的URL参数
 */
function render_pagination($current_page, $total_pages, $base_url, $params = []) {
    if ($total_pages <= 1) {
        return;
    }

    // 移除page参数，避免重复
    unset($params['page']);

    // 构建基础查询字符串
    $query_string = '';
    if (!empty($params)) {
        $query_string = '&' . http_build_query($params);
    }

    echo '<div class="pagination-container" style="margin: 20px 0; text-align: center;">';
    echo '<nav aria-label="分页导航">';
    echo '<ul class="pagination" style="display: inline-flex; list-style: none; padding: 0; margin: 0; gap: 5px;">';

    // 上一页
    if ($current_page > 1) {
        $prev_page = $current_page - 1;
        echo '<li class="page-item">';
        echo '<a class="page-link" href="' . $base_url . '?page=' . $prev_page . $query_string . '" style="padding: 8px 12px; text-decoration: none; border: 1px solid #ddd; border-radius: 4px; color: #007bff;">';
        echo '<i class="fas fa-chevron-left"></i> 上一页';
        echo '</a>';
        echo '</li>';
    }

    // 页码显示逻辑
    $start_page = max(1, $current_page - 2);
    $end_page = min($total_pages, $current_page + 2);

    // 如果开始页码大于1，显示第一页和省略号
    if ($start_page > 1) {
        echo '<li class="page-item">';
        echo '<a class="page-link" href="' . $base_url . '?page=1' . $query_string . '" style="padding: 8px 12px; text-decoration: none; border: 1px solid #ddd; border-radius: 4px; color: #007bff;">1</a>';
        echo '</li>';

        if ($start_page > 2) {
            echo '<li class="page-item disabled">';
            echo '<span class="page-link" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; color: #6c757d;">...</span>';
            echo '</li>';
        }
    }

    // 显示页码
    for ($i = $start_page; $i <= $end_page; $i++) {
        echo '<li class="page-item' . ($i == $current_page ? ' active' : '') . '">';
        if ($i == $current_page) {
            echo '<span class="page-link" style="padding: 8px 12px; border: 1px solid #007bff; border-radius: 4px; background-color: #007bff; color: white;">' . $i . '</span>';
        } else {
            echo '<a class="page-link" href="' . $base_url . '?page=' . $i . $query_string . '" style="padding: 8px 12px; text-decoration: none; border: 1px solid #ddd; border-radius: 4px; color: #007bff;">' . $i . '</a>';
        }
        echo '</li>';
    }

    // 如果结束页码小于总页数，显示省略号和最后一页
    if ($end_page < $total_pages) {
        if ($end_page < $total_pages - 1) {
            echo '<li class="page-item disabled">';
            echo '<span class="page-link" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; color: #6c757d;">...</span>';
            echo '</li>';
        }

        echo '<li class="page-item">';
        echo '<a class="page-link" href="' . $base_url . '?page=' . $total_pages . $query_string . '" style="padding: 8px 12px; text-decoration: none; border: 1px solid #ddd; border-radius: 4px; color: #007bff;">' . $total_pages . '</a>';
        echo '</li>';
    }

    // 下一页
    if ($current_page < $total_pages) {
        $next_page = $current_page + 1;
        echo '<li class="page-item">';
        echo '<a class="page-link" href="' . $base_url . '?page=' . $next_page . $query_string . '" style="padding: 8px 12px; text-decoration: none; border: 1px solid #ddd; border-radius: 4px; color: #007bff;">';
        echo '下一页 <i class="fas fa-chevron-right"></i>';
        echo '</a>';
        echo '</li>';
    }

    echo '</ul>';
    echo '</nav>';

    // 显示分页信息
    echo '<div class="pagination-info" style="margin-top: 10px; color: #6c757d; font-size: 14px;">';
    echo '第 ' . $current_page . ' 页，共 ' . $total_pages . ' 页';
    echo '</div>';

    echo '</div>';
}
?>
