<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';
require_once '../includes/lesson_db_setup.php';

// 确保数据库表结构正确
$setup_result = setupLessonTables($conn);
if (!$setup_result['success']) {
    $error_message = '数据库表结构初始化失败: ' . implode(', ', $setup_result['messages']);
}

$success_message = '';
$error_message = isset($error_message) ? $error_message : '';

// 获取课程ID
$course_id = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;
if ($course_id <= 0) {
    header('Location: courses.php');
    exit;
}

// 获取课程信息
$course_query = "SELECT * FROM courses WHERE id = ?";
$course_stmt = $conn->prepare($course_query);
$course_stmt->bind_param("i", $course_id);
$course_stmt->execute();
$course_result = $course_stmt->get_result();
$course = $course_result->fetch_assoc();

if (!$course) {
    header('Location: courses.php');
    exit;
}

// 处理课时操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_lesson':
                $title = trim($_POST['title']);
                $description = trim($_POST['description']);
                $video_url = trim($_POST['video_url']);
                $duration = intval($_POST['duration']);
                $sort_order = intval($_POST['sort_order']);
                $is_free = isset($_POST['is_free']) ? 1 : 0;

                // 处理视频类型和VOD数据
                $video_type = 'url';
                $vod_file_id = '';
                $vod_video_url = '';
                $vod_duration = 0;

                if (!empty($_POST['vod_file_id'])) {
                    $video_type = 'vod';
                    $vod_file_id = trim($_POST['vod_file_id']);
                    $vod_video_url = trim($_POST['vod_video_url']);
                    $vod_duration = intval($_POST['vod_duration']);

                    // 如果使用VOD，优先使用VOD的时长
                    if ($vod_duration > 0) {
                        $duration = $vod_duration;
                    }
                }

                // 处理缩略图
                $thumbnail = '';
                if (!empty($_POST['uploaded_thumbnail_url'])) {
                    $thumbnail = trim($_POST['uploaded_thumbnail_url']);
                } elseif (!empty($_POST['thumbnail'])) {
                    $thumbnail = trim($_POST['thumbnail']);
                }

                // 调试信息（仅在需要时启用）
                if (isset($_GET['debug']) && $_GET['debug'] === '1') {
                    error_log("=== 添加课时调试 ===");
                    error_log("上传的缩略图URL: " . ($_POST['uploaded_thumbnail_url'] ?? 'empty'));
                    error_log("链接缩略图URL: " . ($_POST['thumbnail'] ?? 'empty'));
                    error_log("最终缩略图URL: " . $thumbnail);
                    error_log("==================");
                }
                
                if (empty($title)) {
                    $error_message = '请填写课时标题';
                } else {
                    $insert_sql = "INSERT INTO lessons (course_id, title, description, video_url, vod_file_id, vod_video_url, video_type, duration, sort_order, is_free, thumbnail, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    $insert_stmt = $conn->prepare($insert_sql);
                    $admin_id = $_SESSION['admin_id'] ?? 1;
                    $insert_stmt->bind_param("issssssiiiis", $course_id, $title, $description, $video_url, $vod_file_id, $vod_video_url, $video_type, $duration, $sort_order, $is_free, $thumbnail, $admin_id);

                    if ($insert_stmt->execute()) {
                        $success_message = '课时添加成功';
                        // 更新课程的课时数量和总时长
                        updateCourseStats($conn, $course_id);
                    } else {
                        $error_message = '课时添加失败: ' . $conn->error;
                    }
                }
                break;
                
            case 'edit_lesson':
                $lesson_id = intval($_POST['lesson_id']);
                $title = trim($_POST['title']);
                $description = trim($_POST['description']);
                $video_url = trim($_POST['video_url']);
                $duration = intval($_POST['duration']);
                $sort_order = intval($_POST['sort_order']);
                $is_free = isset($_POST['is_free']) ? 1 : 0;
                $status = intval($_POST['status']);

                // 处理视频类型和VOD数据
                $video_type = 'url';
                $vod_file_id = '';
                $vod_video_url = '';
                $vod_duration = 0;

                if (!empty($_POST['edit_vod_file_id'])) {
                    $video_type = 'vod';
                    $vod_file_id = trim($_POST['edit_vod_file_id']);
                    $vod_video_url = trim($_POST['edit_vod_video_url']);
                    $vod_duration = intval($_POST['edit_vod_duration']);

                    // 如果使用VOD，优先使用VOD的时长
                    if ($vod_duration > 0) {
                        $duration = $vod_duration;
                    }
                }

                // 处理缩略图
                $thumbnail = '';
                if (!empty($_POST['edit_uploaded_thumbnail_url'])) {
                    $thumbnail = trim($_POST['edit_uploaded_thumbnail_url']);
                } elseif (!empty($_POST['thumbnail'])) {
                    $thumbnail = trim($_POST['thumbnail']);
                }

                // 调试信息（仅在需要时启用）
                if (isset($_GET['debug']) && $_GET['debug'] === '1') {
                    error_log("=== 编辑课时调试 ===");
                    error_log("上传的缩略图URL: " . ($_POST['edit_uploaded_thumbnail_url'] ?? 'empty'));
                    error_log("链接缩略图URL: " . ($_POST['thumbnail'] ?? 'empty'));
                    error_log("最终缩略图URL: " . $thumbnail);
                    error_log("==================");
                }
                
                if (empty($title)) {
                    $error_message = '请填写课时标题';
                } else {
                    $update_sql = "UPDATE lessons SET title = ?, description = ?, video_url = ?, vod_file_id = ?, vod_video_url = ?, video_type = ?, duration = ?, sort_order = ?, is_free = ?, thumbnail = ?, status = ? WHERE id = ? AND course_id = ?";
                    $update_stmt = $conn->prepare($update_sql);
                    $update_stmt->bind_param("ssssssiiiisii", $title, $description, $video_url, $vod_file_id, $vod_video_url, $video_type, $duration, $sort_order, $is_free, $thumbnail, $status, $lesson_id, $course_id);

                    if ($update_stmt->execute()) {
                        $success_message = '课时更新成功';
                        // 更新课程的课时数量和总时长
                        updateCourseStats($conn, $course_id);
                    } else {
                        $error_message = '课时更新失败: ' . $conn->error;
                    }
                }
                break;
                
            case 'delete_lesson':
                $lesson_id = intval($_POST['lesson_id']);
                
                // 先删除相关的观看记录
                $delete_logs_sql = "DELETE FROM lesson_watch_logs WHERE lesson_id = ?";
                $delete_logs_stmt = $conn->prepare($delete_logs_sql);
                $delete_logs_stmt->bind_param("i", $lesson_id);
                $delete_logs_stmt->execute();
                
                // 删除课时
                $delete_sql = "DELETE FROM lessons WHERE id = ? AND course_id = ?";
                $delete_stmt = $conn->prepare($delete_sql);
                $delete_stmt->bind_param("ii", $lesson_id, $course_id);
                
                if ($delete_stmt->execute()) {
                    $success_message = '课时删除成功';
                    // 更新课程的课时数量和总时长
                    updateCourseStats($conn, $course_id);
                } else {
                    $error_message = '课时删除失败: ' . $conn->error;
                }
                break;
                
            case 'update_sort':
                $lesson_orders = json_decode($_POST['lesson_orders'], true);
                if ($lesson_orders) {
                    foreach ($lesson_orders as $order_data) {
                        $lesson_id = intval($order_data['id']);
                        $sort_order = intval($order_data['sort_order']);
                        
                        $update_sort_sql = "UPDATE lessons SET sort_order = ? WHERE id = ? AND course_id = ?";
                        $update_sort_stmt = $conn->prepare($update_sort_sql);
                        $update_sort_stmt->bind_param("iii", $sort_order, $lesson_id, $course_id);
                        $update_sort_stmt->execute();
                    }
                    $success_message = '课时排序更新成功';
                }
                break;
        }
    }
}

// 更新课程统计信息的函数
function updateCourseStats($conn, $course_id) {
    $stats_sql = "SELECT COUNT(*) as lesson_count, COALESCE(SUM(duration), 0) as total_duration FROM lessons WHERE course_id = ? AND status = 1";
    $stats_stmt = $conn->prepare($stats_sql);
    $stats_stmt->bind_param("i", $course_id);
    $stats_stmt->execute();
    $stats_result = $stats_stmt->get_result();
    $stats = $stats_result->fetch_assoc();
    
    $update_course_sql = "UPDATE courses SET lesson_count = ?, total_duration = ? WHERE id = ?";
    $update_course_stmt = $conn->prepare($update_course_sql);
    $update_course_stmt->bind_param("iii", $stats['lesson_count'], $stats['total_duration'], $course_id);
    $update_course_stmt->execute();
}

// 获取课时列表
$lessons_query = "SELECT * FROM lessons WHERE course_id = ? ORDER BY sort_order ASC, id ASC";
$lessons_stmt = $conn->prepare($lessons_query);
$lessons_stmt->bind_param("i", $course_id);
$lessons_stmt->execute();
$lessons_result = $lessons_stmt->get_result();
$lessons = $lessons_result->fetch_all(MYSQLI_ASSOC);

render_admin_header('课时管理 - ' . $course['title']);
?>

<style>
.lesson-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.lesson-header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.lesson-title h2 {
    margin: 0;
    color: #333;
    font-size: 24px;
}

.lesson-subtitle {
    color: #666;
    margin: 5px 0 0 0;
    font-size: 14px;
}

.lesson-actions {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.lesson-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.alert {
    padding: 12px 16px;
    margin-bottom: 20px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-close {
    margin-left: auto;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: inherit;
}

.stats-row {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stats-left {
    display: flex;
    gap: 10px;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.badge-info {
    background: #d1ecf1;
    color: #0c5460;
}

.badge-success {
    background: #d4edda;
    color: #155724;
}

.btn-outline {
    background: white;
    border: 1px solid #007bff;
    color: #007bff;
}

.btn-outline:hover {
    background: #007bff;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    display: block;
}

.table-container {
    overflow-x: auto;
}

.lesson-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.lesson-table th,
.lesson-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.lesson-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.lesson-table tbody tr:hover {
    background: #f8f9fa;
}

.lesson-title-cell strong {
    color: #333;
    font-size: 14px;
}

.lesson-description {
    color: #6c757d;
    font-size: 12px;
    margin-top: 4px;
}

.video-type-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.vod-badge {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.url-badge {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #e1bee7;
}

.vod-status-check {
    margin-top: 4px;
}

.vod-status-check .btn-xs {
    padding: 1px 4px;
    font-size: 10px;
    line-height: 1.2;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.free-badge {
    background: #d4edda;
    color: #155724;
}

.paid-badge {
    background: #fff3cd;
    color: #856404;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
}

.modal-dialog {
    position: relative;
    width: 90%;
    max-width: 600px;
    margin: 50px auto;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-check {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-check-input {
    margin: 0;
}

.row {
    display: flex;
    margin: 0 -10px;
}

.col {
    flex: 1;
    padding: 0 10px;
}

.col-6 {
    flex: 0 0 50%;
    padding: 0 10px;
}

.text-danger {
    color: #dc3545;
}

.sort-handle {
    cursor: move;
    color: #6c757d;
}

.sort-order {
    font-weight: 500;
    color: #495057;
}

/* 缩略图上传样式 */
.thumbnail-upload-container {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    background: #f8f9fa;
}

/* 视频上传样式 */
.video-upload-container {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    background: #f8f9fa;
}

.upload-options {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.thumbnail-input-group,
.video-input-group {
    margin-top: 10px;
}

.video-upload-area {
    border: 2px dashed #ced4da;
    border-radius: 4px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    background: white;
}

.video-upload-area:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.video-upload-area.dragover {
    border-color: #007bff;
    background: #e3f2fd;
}

.video-upload-progress {
    padding: 20px;
    background: white;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.file-name {
    font-weight: 500;
    color: #333;
}

.progress-percent {
    font-weight: 500;
    color: #007bff;
}

.upload-actions {
    margin-top: 10px;
    text-align: center;
}

.video-upload-success {
    padding: 20px;
    background: white;
    border-radius: 4px;
    border: 1px solid #d4edda;
    background-color: #f8fff9;
}

.success-info {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.video-info p {
    margin: 5px 0;
    color: #495057;
}

.upload-area {
    border: 2px dashed #ced4da;
    border-radius: 4px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    background: white;
}

.upload-area:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.upload-area.dragover {
    border-color: #007bff;
    background: #e3f2fd;
}

.upload-content i {
    font-size: 32px;
    color: #6c757d;
    margin-bottom: 10px;
}

.upload-content p {
    margin: 10px 0 5px 0;
    color: #495057;
    font-weight: 500;
}

.upload-content small {
    color: #6c757d;
}

.upload-preview {
    position: relative;
    display: inline-block;
    max-width: 200px;
}

.upload-preview img {
    width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-actions {
    position: absolute;
    top: 5px;
    right: 5px;
}

.upload-progress {
    margin-top: 10px;
}

.progress {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: #007bff;
    transition: width 0.3s;
}
</style>

<div class="lesson-container">
    <div class="lesson-header">
        <div class="lesson-title">
            <h2>课时管理</h2>
            <p class="lesson-subtitle">课程：<?php echo htmlspecialchars($course['title']); ?></p>
        </div>
        <div class="lesson-actions">
            <a href="courses.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回课程列表
            </a>
            <button type="button" class="btn btn-primary" onclick="showAddLessonModal()">
                <i class="fas fa-plus"></i> 添加课时
            </button>
        </div>
    </div>

    <div class="lesson-content">
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="alert-close" onclick="this.parentElement.style.display='none'">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="alert-close" onclick="this.parentElement.style.display='none'">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <div class="stats-row">
            <div class="stats-left">
                <span class="badge badge-info">总课时: <?php echo count($lessons); ?></span>
                <span class="badge badge-success">总时长: <?php echo gmdate("H:i:s", array_sum(array_column($lessons, 'duration'))); ?></span>
            </div>
            <div class="stats-right">
                <button type="button" class="btn btn-outline" onclick="enableSortMode()">
                    <i class="fas fa-sort"></i> 排序模式
                </button>
            </div>
        </div>
                    
        <?php if (empty($lessons)): ?>
            <div class="empty-state">
                <i class="fas fa-video"></i>
                <h3>暂无课时</h3>
                <p>点击上方"添加课时"按钮开始添加课时内容</p>
            </div>
        <?php else: ?>
            <div class="table-container">
                <table class="lesson-table" id="lessonsTable">
                    <thead>
                        <tr>
                            <th width="60">排序</th>
                            <th>课时标题</th>
                            <th width="80">视频类型</th>
                            <th width="100">时长</th>
                            <th width="80">状态</th>
                            <th width="80">免费</th>
                            <th width="120">创建时间</th>
                            <th width="180">操作</th>
                        </tr>
                    </thead>
                    <tbody id="lessonsList">
                        <?php foreach ($lessons as $lesson): ?>
                            <tr data-lesson-id="<?php echo $lesson['id']; ?>">
                                <td>
                                    <span class="sort-handle" style="cursor: move; display: none;">
                                        <i class="fas fa-grip-vertical"></i>
                                    </span>
                                    <span class="sort-order"><?php echo $lesson['sort_order']; ?></span>
                                </td>
                                <td>
                                    <div class="lesson-title-cell">
                                        <strong><?php echo htmlspecialchars($lesson['title']); ?></strong>
                                        <?php if ($lesson['description']): ?>
                                            <div class="lesson-description"><?php echo htmlspecialchars(mb_substr($lesson['description'], 0, 50)) . (mb_strlen($lesson['description']) > 50 ? '...' : ''); ?></div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if (($lesson['video_type'] ?? 'url') === 'vod'): ?>
                                        <span class="video-type-badge vod-badge" title="腾讯云点播">
                                            <i class="fas fa-cloud"></i> VOD
                                        </span>
                                        <?php if (!empty($lesson['vod_file_id']) && empty($lesson['vod_video_url'])): ?>
                                            <div class="vod-status-check">
                                                <small class="text-warning">转码中...</small>
                                                <button type="button" class="btn btn-xs btn-outline-primary" onclick="checkVodStatus(<?php echo $lesson['id']; ?>, '<?php echo htmlspecialchars($lesson['vod_file_id']); ?>')" title="检查转码状态">
                                                    <i class="fas fa-sync"></i>
                                                </button>
                                            </div>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="video-type-badge url-badge" title="外部链接">
                                            <i class="fas fa-link"></i> URL
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $lesson['duration'] ? gmdate("H:i:s", $lesson['duration']) : '-'; ?></td>
                                <td>
                                    <span class="status-badge <?php echo $lesson['status'] ? 'status-active' : 'status-inactive'; ?>">
                                        <?php echo $lesson['status'] ? '启用' : '禁用'; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge <?php echo $lesson['is_free'] ? 'free-badge' : 'paid-badge'; ?>">
                                        <?php echo $lesson['is_free'] ? '免费' : '付费'; ?>
                                    </span>
                                </td>
                                <td><?php echo date('m-d H:i', strtotime($lesson['created_at'])); ?></td>
                                <td>
                                    <button type="button" class="btn btn-info btn-sm" onclick="editLesson(<?php echo htmlspecialchars(json_encode($lesson)); ?>)">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteLesson(<?php echo $lesson['id']; ?>, '<?php echo htmlspecialchars($lesson['title']); ?>')">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 添加课时模态框 -->
<div class="modal" id="addLessonModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加课时</h5>
                <button type="button" class="modal-close" onclick="hideAddLessonModal()">&times;</button>
            </div>
            <form method="POST" onsubmit="return validateAddForm()">
                <input type="hidden" name="action" value="add_lesson">
                <div class="modal-body">
                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label for="title" class="form-label">课时标题 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label for="sort_order" class="form-label">排序</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" value="<?php echo count($lessons) + 1; ?>">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description" class="form-label">课时描述</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">视频设置</label>
                        <div class="video-upload-container">
                            <div class="upload-options">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="video_type" id="video_url_option" value="url" checked>
                                    <label class="form-check-label" for="video_url_option">
                                        使用视频链接
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="video_type" id="video_upload_option" value="upload">
                                    <label class="form-check-label" for="video_upload_option">
                                        上传到腾讯云点播
                                    </label>
                                </div>
                            </div>

                            <!-- 视频链接输入 -->
                            <div id="video_url_input" class="video-input-group">
                                <div class="row">
                                    <div class="col">
                                        <div class="form-group">
                                            <label for="video_url" class="form-label">视频链接</label>
                                            <input type="url" class="form-control" id="video_url" name="video_url" placeholder="请输入视频链接">
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label for="duration" class="form-label">时长（秒）</label>
                                            <input type="number" class="form-control" id="duration" name="duration" min="0">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 视频上传 -->
                            <div id="video_upload_input" class="video-input-group" style="display: none;">
                                <div class="video-upload-area" id="video_upload_area">
                                    <div class="upload-content">
                                        <i class="fas fa-video"></i>
                                        <p>点击选择视频文件或拖拽视频到此处</p>
                                        <small>支持 MP4、AVI、MOV、WMV、FLV、MKV 等格式，最大5GB</small>
                                    </div>
                                    <input type="file" id="video_file" accept="video/*" style="display: none;">
                                </div>

                                <!-- 上传进度 -->
                                <div class="video-upload-progress" id="video_upload_progress" style="display: none;">
                                    <div class="progress-info">
                                        <span class="file-name" id="video_file_name"></span>
                                        <span class="progress-percent" id="video_progress_percent">0%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" id="video_progress_bar" style="width: 0%"></div>
                                    </div>
                                    <div class="upload-actions">
                                        <button type="button" class="btn btn-sm btn-danger" id="video_cancel_btn">
                                            <i class="fas fa-times"></i> 取消上传
                                        </button>
                                    </div>
                                </div>

                                <!-- 上传成功 -->
                                <div class="video-upload-success" id="video_upload_success" style="display: none;">
                                    <div class="success-info">
                                        <i class="fas fa-check-circle text-success"></i>
                                        <span>视频上传成功</span>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="clearVideoUpload()">
                                            <i class="fas fa-redo"></i> 重新上传
                                        </button>
                                    </div>
                                    <div class="video-info">
                                        <p><strong>文件ID:</strong> <span id="video_file_id"></span></p>
                                        <p><strong>时长:</strong> <span id="video_duration_display"></span></p>
                                    </div>
                                </div>

                                <input type="hidden" id="vod_file_id" name="vod_file_id">
                                <input type="hidden" id="vod_video_url" name="vod_video_url">
                                <input type="hidden" id="vod_duration" name="vod_duration">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="thumbnail" class="form-label">视频播放器缩略图</label>
                        <div class="thumbnail-upload-container">
                            <div class="upload-options">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="thumbnail_type" id="thumbnail_url" value="url" checked>
                                    <label class="form-check-label" for="thumbnail_url">
                                        使用链接
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="thumbnail_type" id="thumbnail_upload" value="upload">
                                    <label class="form-check-label" for="thumbnail_upload">
                                        上传图片
                                    </label>
                                </div>
                            </div>

                            <div id="thumbnail_url_input" class="thumbnail-input-group">
                                <input type="url" class="form-control" id="thumbnail" name="thumbnail" placeholder="请输入图片链接">
                            </div>

                            <div id="thumbnail_upload_input" class="thumbnail-input-group" style="display: none;">
                                <div class="upload-area" id="upload_area">
                                    <div class="upload-content">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <p>点击选择图片或拖拽图片到此处</p>
                                        <small>支持 JPG、PNG、GIF、WebP 格式，最大5MB</small>
                                    </div>
                                    <input type="file" id="thumbnail_file" accept="image/*" style="display: none;">
                                </div>
                                <div class="upload-preview" id="upload_preview" style="display: none;">
                                    <img id="preview_image" src="" alt="预览">
                                    <div class="preview-actions">
                                        <button type="button" class="btn btn-sm btn-danger" onclick="clearUpload()">
                                            <i class="fas fa-times"></i> 删除
                                        </button>
                                    </div>
                                </div>
                                <input type="hidden" id="uploaded_thumbnail_url" name="uploaded_thumbnail_url">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_free" name="is_free" checked>
                            <label class="form-check-label" for="is_free">
                                免费课时
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="hideAddLessonModal()">取消</button>
                    <button type="submit" class="btn btn-primary">添加课时</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑课时模态框 -->
<div class="modal" id="editLessonModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑课时</h5>
                <button type="button" class="modal-close" onclick="hideEditLessonModal()">&times;</button>
            </div>
            <form method="POST" id="editLessonForm" onsubmit="return validateEditForm()">
                <input type="hidden" name="action" value="edit_lesson">
                <input type="hidden" name="lesson_id" id="edit_lesson_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label for="edit_title" class="form-label">课时标题 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_title" name="title" required>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label for="edit_sort_order" class="form-label">排序</label>
                                <input type="number" class="form-control" id="edit_sort_order" name="sort_order">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_description" class="form-label">课时描述</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">视频设置</label>
                        <div class="video-upload-container">
                            <div class="upload-options">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="edit_video_type" id="edit_video_url_option" value="url" checked>
                                    <label class="form-check-label" for="edit_video_url_option">
                                        使用视频链接
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="edit_video_type" id="edit_video_upload_option" value="upload">
                                    <label class="form-check-label" for="edit_video_upload_option">
                                        上传到腾讯云点播
                                    </label>
                                </div>
                            </div>

                            <!-- 视频链接输入 -->
                            <div id="edit_video_url_input" class="video-input-group">
                                <div class="row">
                                    <div class="col">
                                        <div class="form-group">
                                            <label for="edit_video_url" class="form-label">视频链接</label>
                                            <input type="url" class="form-control" id="edit_video_url" name="video_url" placeholder="请输入视频链接">
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label for="edit_duration" class="form-label">时长（秒）</label>
                                            <input type="number" class="form-control" id="edit_duration" name="duration" min="0">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 视频上传 -->
                            <div id="edit_video_upload_input" class="video-input-group" style="display: none;">
                                <div class="video-upload-area" id="edit_video_upload_area">
                                    <div class="upload-content">
                                        <i class="fas fa-video"></i>
                                        <p>点击选择视频文件或拖拽视频到此处</p>
                                        <small>支持 MP4、AVI、MOV、WMV、FLV、MKV 等格式，最大5GB</small>
                                    </div>
                                    <input type="file" id="edit_video_file" accept="video/*" style="display: none;">
                                </div>

                                <!-- 上传进度 -->
                                <div class="video-upload-progress" id="edit_video_upload_progress" style="display: none;">
                                    <div class="progress-info">
                                        <span class="file-name" id="edit_video_file_name"></span>
                                        <span class="progress-percent" id="edit_video_progress_percent">0%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" id="edit_video_progress_bar" style="width: 0%"></div>
                                    </div>
                                    <div class="upload-actions">
                                        <button type="button" class="btn btn-sm btn-danger" id="edit_video_cancel_btn">
                                            <i class="fas fa-times"></i> 取消上传
                                        </button>
                                    </div>
                                </div>

                                <!-- 上传成功 -->
                                <div class="video-upload-success" id="edit_video_upload_success" style="display: none;">
                                    <div class="success-info">
                                        <i class="fas fa-check-circle text-success"></i>
                                        <span>视频上传成功</span>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="clearEditVideoUpload()">
                                            <i class="fas fa-redo"></i> 重新上传
                                        </button>
                                    </div>
                                    <div class="video-info">
                                        <p><strong>文件ID:</strong> <span id="edit_video_file_id"></span></p>
                                        <p><strong>时长:</strong> <span id="edit_video_duration_display"></span></p>
                                    </div>
                                </div>

                                <input type="hidden" id="edit_vod_file_id" name="edit_vod_file_id">
                                <input type="hidden" id="edit_vod_video_url" name="edit_vod_video_url">
                                <input type="hidden" id="edit_vod_duration" name="edit_vod_duration">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_thumbnail" class="form-label">视频播放器缩略图</label>
                        <div class="thumbnail-upload-container">
                            <div class="upload-options">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="edit_thumbnail_type" id="edit_thumbnail_url" value="url" checked>
                                    <label class="form-check-label" for="edit_thumbnail_url">
                                        使用链接
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="edit_thumbnail_type" id="edit_thumbnail_upload" value="upload">
                                    <label class="form-check-label" for="edit_thumbnail_upload">
                                        上传图片
                                    </label>
                                </div>
                            </div>

                            <div id="edit_thumbnail_url_input" class="thumbnail-input-group">
                                <input type="url" class="form-control" id="edit_thumbnail" name="thumbnail" placeholder="请输入图片链接">
                            </div>

                            <div id="edit_thumbnail_upload_input" class="thumbnail-input-group" style="display: none;">
                                <div class="upload-area" id="edit_upload_area">
                                    <div class="upload-content">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <p>点击选择图片或拖拽图片到此处</p>
                                        <small>支持 JPG、PNG、GIF、WebP 格式，最大5MB</small>
                                    </div>
                                    <input type="file" id="edit_thumbnail_file" accept="image/*" style="display: none;">
                                </div>
                                <div class="upload-preview" id="edit_upload_preview" style="display: none;">
                                    <img id="edit_preview_image" src="" alt="预览">
                                    <div class="preview-actions">
                                        <button type="button" class="btn btn-sm btn-danger" onclick="clearEditUpload()">
                                            <i class="fas fa-times"></i> 删除
                                        </button>
                                    </div>
                                </div>
                                <input type="hidden" id="edit_uploaded_thumbnail_url" name="edit_uploaded_thumbnail_url">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_is_free" name="is_free">
                                    <label class="form-check-label" for="edit_is_free">
                                        免费课时
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label for="edit_status" class="form-label">状态</label>
                                <select class="form-control" id="edit_status" name="status">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="hideEditLessonModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存更改</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 腾讯云VOD SDK -->
<script src="../assets/vod-js-sdk-v6-master/dist/vod-js-sdk-v6.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
let sortMode = false;
let sortable = null;

// 显示添加课时模态框
function showAddLessonModal() {
    document.getElementById('addLessonModal').style.display = 'block';
}

// 隐藏添加课时模态框
function hideAddLessonModal() {
    document.getElementById('addLessonModal').style.display = 'none';

    // 重置表单状态
    resetAddModalState();
}

// 重置添加模态框状态
function resetAddModalState() {
    // 重置表单
    const form = document.querySelector('#addLessonModal form');
    if (form) {
        form.reset();
        // 重新设置默认排序值
        const sortOrderInput = document.getElementById('sort_order');
        if (sortOrderInput) {
            sortOrderInput.value = <?php echo count($lessons) + 1; ?>;
        }
        // 重新勾选免费课时
        const isFreeCheckbox = document.getElementById('is_free');
        if (isFreeCheckbox) {
            isFreeCheckbox.checked = true;
        }
    }

    // 重置缩略图上传状态
    clearUpload();

    // 重置单选按钮状态
    const urlRadio = document.getElementById('thumbnail_url');
    const uploadRadio = document.getElementById('thumbnail_upload');
    const urlInput = document.getElementById('thumbnail_url_input');
    const uploadInput = document.getElementById('thumbnail_upload_input');

    if (urlRadio && uploadRadio && urlInput && uploadInput) {
        urlRadio.checked = true;
        uploadRadio.checked = false;
        urlInput.style.display = 'block';
        uploadInput.style.display = 'none';
    }
}

// 显示编辑课时模态框
function showEditLessonModal() {
    document.getElementById('editLessonModal').style.display = 'block';
}

// 隐藏编辑课时模态框
function hideEditLessonModal() {
    document.getElementById('editLessonModal').style.display = 'none';

    // 重置表单状态
    resetEditModalState();
}

// 重置编辑模态框状态
function resetEditModalState() {
    // 重置表单
    const form = document.getElementById('editLessonForm');
    if (form) {
        form.reset();
    }

    // 重置缩略图上传状态
    clearEditUpload();

    // 重置VOD隐藏字段
    document.getElementById('edit_vod_file_id').value = '';
    document.getElementById('edit_vod_video_url').value = '';
    document.getElementById('edit_vod_duration').value = '';

    // 重置视频类型选择
    document.getElementById('edit_video_url_option').checked = true;
    document.getElementById('edit_video_url_input').style.display = 'block';
    document.getElementById('edit_video_upload_input').style.display = 'none';

    // 重置VOD上传区域显示状态
    const uploadArea = document.getElementById('edit_video_upload_area');
    const progressDiv = document.getElementById('edit_video_upload_progress');
    const successDiv = document.getElementById('edit_video_upload_success');

    if (uploadArea) uploadArea.style.display = 'block';
    if (progressDiv) progressDiv.style.display = 'none';
    if (successDiv) successDiv.style.display = 'none';

    // 重置缩略图单选按钮状态
    const urlRadio = document.getElementById('edit_thumbnail_url');
    const uploadRadio = document.getElementById('edit_thumbnail_upload');
    const urlInput = document.getElementById('edit_thumbnail_url_input');
    const uploadInput = document.getElementById('edit_thumbnail_upload_input');

    if (urlRadio && uploadRadio && urlInput && uploadInput) {
        urlRadio.checked = true;
        uploadRadio.checked = false;
        urlInput.style.display = 'block';
        uploadInput.style.display = 'none';
    }
}

// 编辑课时
function editLesson(lesson) {
    console.log('编辑课时数据:', lesson);

    document.getElementById('edit_lesson_id').value = lesson.id;
    document.getElementById('edit_title').value = lesson.title;
    document.getElementById('edit_description').value = lesson.description || '';
    document.getElementById('edit_video_url').value = lesson.video_url || '';
    document.getElementById('edit_duration').value = lesson.duration || '';
    document.getElementById('edit_sort_order').value = lesson.sort_order;
    document.getElementById('edit_is_free').checked = lesson.is_free == 1;
    document.getElementById('edit_status').value = lesson.status;

    // 处理VOD相关数据
    const videoType = lesson.video_type || 'url';
    const vodFileId = lesson.vod_file_id || '';
    const vodVideoUrl = lesson.vod_video_url || '';

    console.log('VOD数据:', { videoType, vodFileId, vodVideoUrl });

    // 设置VOD隐藏字段
    document.getElementById('edit_vod_file_id').value = vodFileId;
    document.getElementById('edit_vod_video_url').value = vodVideoUrl;
    document.getElementById('edit_vod_duration').value = lesson.duration || '';

    // 根据视频类型设置单选按钮和显示相应的输入区域
    if (videoType === 'vod') {
        document.getElementById('edit_video_upload_option').checked = true;
        document.getElementById('edit_video_url_input').style.display = 'none';
        document.getElementById('edit_video_upload_input').style.display = 'block';

        // 如果有VOD数据，显示成功状态
        if (vodFileId) {
            showEditVodSuccess(vodFileId, vodVideoUrl, lesson.duration);
        }
    } else {
        document.getElementById('edit_video_url_option').checked = true;
        document.getElementById('edit_video_url_input').style.display = 'block';
        document.getElementById('edit_video_upload_input').style.display = 'none';
    }

    // 处理缩略图
    const thumbnailUrl = lesson.thumbnail || '';
    document.getElementById('edit_thumbnail').value = thumbnailUrl;

    // 重置上传状态
    clearEditUpload();

    // 如果有现有缩略图，默认选择链接方式
    if (thumbnailUrl) {
        document.getElementById('edit_thumbnail_url').checked = true;
        document.getElementById('edit_thumbnail_url_input').style.display = 'block';
        document.getElementById('edit_thumbnail_upload_input').style.display = 'none';
    } else {
        document.getElementById('edit_thumbnail_url').checked = true;
        document.getElementById('edit_thumbnail_url_input').style.display = 'block';
        document.getElementById('edit_thumbnail_upload_input').style.display = 'none';
    }

    showEditLessonModal();
}

// 显示编辑模式下的VOD成功状态
function showEditVodSuccess(fileId, videoUrl, duration) {
    const progressDiv = document.getElementById('edit_video_upload_progress');
    const successDiv = document.getElementById('edit_video_upload_success');
    const uploadArea = document.getElementById('edit_video_upload_area');

    if (progressDiv) progressDiv.style.display = 'none';
    if (uploadArea) uploadArea.style.display = 'none';
    if (successDiv) {
        successDiv.style.display = 'block';

        // 更新显示信息
        const fileIdSpan = document.getElementById('edit_video_file_id');
        const durationSpan = document.getElementById('edit_video_duration_display');

        if (fileIdSpan) fileIdSpan.textContent = fileId;
        if (durationSpan && duration) {
            durationSpan.textContent = formatDuration(duration);
        }
    }
}

// 删除课时
function deleteLesson(lessonId, lessonTitle) {
    if (confirm('确定要删除课时"' + lessonTitle + '"吗？\n删除后将无法恢复，相关的观看记录也会被删除。')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_lesson">
            <input type="hidden" name="lesson_id" value="${lessonId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// 启用排序模式
function enableSortMode() {
    sortMode = !sortMode;
    const sortHandles = document.querySelectorAll('.sort-handle');
    const sortOrders = document.querySelectorAll('.sort-order');
    const button = event.target.closest('button');

    if (sortMode) {
        // 显示拖拽手柄，隐藏排序数字
        sortHandles.forEach(handle => handle.style.display = 'inline');
        sortOrders.forEach(order => order.style.display = 'none');

        // 初始化拖拽排序
        const tbody = document.getElementById('lessonsList');
        if (tbody) {
            sortable = Sortable.create(tbody, {
                handle: '.sort-handle',
                animation: 150,
                onEnd: function(evt) {
                    updateSortOrder();
                }
            });
        }

        button.innerHTML = '<i class="fas fa-save"></i> 保存排序';
        button.className = 'btn btn-primary';
    } else {
        // 隐藏拖拽手柄，显示排序数字
        sortHandles.forEach(handle => handle.style.display = 'none');
        sortOrders.forEach(order => order.style.display = 'inline');

        // 销毁拖拽排序
        if (sortable) {
            sortable.destroy();
            sortable = null;
        }

        button.innerHTML = '<i class="fas fa-sort"></i> 排序模式';
        button.className = 'btn btn-outline';
    }
}

// 更新排序
function updateSortOrder() {
    const rows = document.querySelectorAll('#lessonsList tr');
    const lessonOrders = [];

    rows.forEach((row, index) => {
        const lessonId = row.getAttribute('data-lesson-id');
        lessonOrders.push({
            id: lessonId,
            sort_order: index + 1
        });
    });

    // 发送排序更新请求
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="update_sort">
        <input type="hidden" name="lesson_orders" value='${JSON.stringify(lessonOrders)}'>
    `;
    document.body.appendChild(form);
    form.submit();
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const addModal = document.getElementById('addLessonModal');
    const editModal = document.getElementById('editLessonModal');

    if (event.target == addModal) {
        hideAddLessonModal();
    }
    if (event.target == editModal) {
        hideEditLessonModal();
    }
}

// 表单验证函数
function validateAddForm() {
    const uploadedUrl = document.getElementById('uploaded_thumbnail_url').value;
    const thumbnailUrl = document.getElementById('thumbnail').value;
    const thumbnailType = document.querySelector('input[name="thumbnail_type"]:checked').value;

    console.log('=== 添加课时表单提交验证 ===');
    console.log('缩略图类型:', thumbnailType);
    console.log('上传的URL:', uploadedUrl);
    console.log('链接URL:', thumbnailUrl);
    console.log('========================');

    return true; // 允许提交
}

function validateEditForm() {
    const uploadedUrl = document.getElementById('edit_uploaded_thumbnail_url').value;
    const thumbnailUrl = document.getElementById('edit_thumbnail').value;
    const thumbnailType = document.querySelector('input[name="edit_thumbnail_type"]:checked')?.value;

    console.log('=== 编辑课时表单提交验证 ===');
    console.log('缩略图类型:', thumbnailType);
    console.log('上传的URL:', uploadedUrl);
    console.log('链接URL:', thumbnailUrl);
    console.log('========================');

    return true; // 允许提交
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 自动隐藏提示消息
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            alert.style.display = 'none';
        });
    }, 3000);

    // 初始化图片上传功能
    initThumbnailUpload();
    initEditThumbnailUpload();

    // 初始化视频上传功能
    initVideoUpload();
    initEditVideoUpload();
});

// 初始化添加课时的图片上传功能
function initThumbnailUpload() {
    const thumbnailTypeRadios = document.querySelectorAll('input[name="thumbnail_type"]');
    const urlInput = document.getElementById('thumbnail_url_input');
    const uploadInput = document.getElementById('thumbnail_upload_input');
    const uploadArea = document.getElementById('upload_area');
    const fileInput = document.getElementById('thumbnail_file');
    const preview = document.getElementById('upload_preview');
    const previewImage = document.getElementById('preview_image');
    const uploadedUrlInput = document.getElementById('uploaded_thumbnail_url');

    // 检查必要元素是否存在
    if (!uploadArea || !fileInput || !urlInput || !uploadInput) {
        return;
    }

    // 切换输入方式
    thumbnailTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'url') {
                urlInput.style.display = 'block';
                uploadInput.style.display = 'none';
                clearUpload();
            } else {
                urlInput.style.display = 'none';
                uploadInput.style.display = 'block';
                const thumbnailInput = document.getElementById('thumbnail');
                if (thumbnailInput) thumbnailInput.value = '';
            }
        });
    });

    // 点击上传区域
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    // 文件选择
    fileInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            uploadThumbnailFile(this.files[0]);
        }
    });

    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files && files[0]) {
            uploadThumbnailFile(files[0]);
        }
    });
}

// 初始化编辑课时的图片上传功能
function initEditThumbnailUpload() {
    const thumbnailTypeRadios = document.querySelectorAll('input[name="edit_thumbnail_type"]');
    const urlInput = document.getElementById('edit_thumbnail_url_input');
    const uploadInput = document.getElementById('edit_thumbnail_upload_input');
    const uploadArea = document.getElementById('edit_upload_area');
    const fileInput = document.getElementById('edit_thumbnail_file');
    const preview = document.getElementById('edit_upload_preview');
    const previewImage = document.getElementById('edit_preview_image');
    const uploadedUrlInput = document.getElementById('edit_uploaded_thumbnail_url');

    // 检查必要元素是否存在
    if (!uploadArea || !fileInput || !urlInput || !uploadInput) {
        return;
    }

    // 切换输入方式
    thumbnailTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'url') {
                urlInput.style.display = 'block';
                uploadInput.style.display = 'none';
                clearEditUpload();
            } else {
                urlInput.style.display = 'none';
                uploadInput.style.display = 'block';
                const editThumbnailInput = document.getElementById('edit_thumbnail');
                if (editThumbnailInput) editThumbnailInput.value = '';
            }
        });
    });

    // 点击上传区域
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    // 文件选择
    fileInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            uploadEditThumbnailFile(this.files[0]);
        }
    });

    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files && files[0]) {
            uploadEditThumbnailFile(files[0]);
        }
    });
}

// 上传缩略图文件（添加课时）
function uploadThumbnailFile(file) {
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        alert('只支持 JPG、PNG、GIF、WebP 格式的图片');
        return;
    }

    // 验证文件大小（5MB）
    if (file.size > 5 * 1024 * 1024) {
        alert('图片大小不能超过5MB');
        return;
    }

    const formData = new FormData();
    formData.append('image', file);

    // 显示上传进度
    const uploadArea = document.getElementById('upload_area');
    uploadArea.innerHTML = '<div class="upload-progress"><div class="progress"><div class="progress-bar" style="width: 0%"></div></div><p>上传中...</p></div>';

    fetch('upload_lesson_thumbnail.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示预览
            const preview = document.getElementById('upload_preview');
            const previewImage = document.getElementById('preview_image');
            const uploadedUrlInput = document.getElementById('uploaded_thumbnail_url');

            previewImage.src = data.data.file_url;
            uploadedUrlInput.value = data.data.file_url;

            // 调试信息
            console.log('添加课时 - 上传成功，文件URL:', data.data.file_url);
            console.log('添加课时 - hidden input值:', uploadedUrlInput.value);

            uploadArea.style.display = 'none';
            preview.style.display = 'block';
        } else {
            alert('上传失败: ' + data.message);
            resetUploadArea();
        }
    })
    .catch(error => {
        console.error('上传错误:', error);
        alert('上传失败，请重试');
        resetUploadArea();
    });
}

// 上传缩略图文件（编辑课时）
function uploadEditThumbnailFile(file) {
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        alert('只支持 JPG、PNG、GIF、WebP 格式的图片');
        return;
    }

    // 验证文件大小（5MB）
    if (file.size > 5 * 1024 * 1024) {
        alert('图片大小不能超过5MB');
        return;
    }

    const formData = new FormData();
    formData.append('image', file);

    // 显示上传进度
    const uploadArea = document.getElementById('edit_upload_area');
    uploadArea.innerHTML = '<div class="upload-progress"><div class="progress"><div class="progress-bar" style="width: 0%"></div></div><p>上传中...</p></div>';

    fetch('upload_lesson_thumbnail.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示预览
            const preview = document.getElementById('edit_upload_preview');
            const previewImage = document.getElementById('edit_preview_image');
            const uploadedUrlInput = document.getElementById('edit_uploaded_thumbnail_url');

            previewImage.src = data.data.file_url;
            uploadedUrlInput.value = data.data.file_url;

            // 调试信息
            console.log('编辑课时 - 上传成功，文件URL:', data.data.file_url);
            console.log('编辑课时 - hidden input值:', uploadedUrlInput.value);

            uploadArea.style.display = 'none';
            preview.style.display = 'block';
        } else {
            alert('上传失败: ' + data.message);
            resetEditUploadArea();
        }
    })
    .catch(error => {
        console.error('上传错误:', error);
        alert('上传失败，请重试');
        resetEditUploadArea();
    });
}

// 清除上传（添加课时）
function clearUpload() {
    const uploadArea = document.getElementById('upload_area');
    const preview = document.getElementById('upload_preview');
    const uploadedUrlInput = document.getElementById('uploaded_thumbnail_url');
    const fileInput = document.getElementById('thumbnail_file');

    if (uploadedUrlInput) uploadedUrlInput.value = '';
    if (fileInput) fileInput.value = '';
    if (preview) preview.style.display = 'none';
    if (uploadArea) uploadArea.style.display = 'block';
    resetUploadArea();
}

// 清除上传（编辑课时）
function clearEditUpload() {
    const uploadArea = document.getElementById('edit_upload_area');
    const preview = document.getElementById('edit_upload_preview');
    const uploadedUrlInput = document.getElementById('edit_uploaded_thumbnail_url');
    const fileInput = document.getElementById('edit_thumbnail_file');

    if (uploadedUrlInput) uploadedUrlInput.value = '';
    if (fileInput) fileInput.value = '';
    if (preview) preview.style.display = 'none';
    if (uploadArea) uploadArea.style.display = 'block';
    resetEditUploadArea();
}

// 重置上传区域（添加课时）
function resetUploadArea() {
    const uploadArea = document.getElementById('upload_area');
    if (uploadArea) {
        uploadArea.innerHTML = `
            <div class="upload-content">
                <i class="fas fa-cloud-upload-alt"></i>
                <p>点击选择图片或拖拽图片到此处</p>
                <small>支持 JPG、PNG、GIF、WebP 格式，最大5MB</small>
            </div>
        `;
    }
}

// 重置上传区域（编辑课时）
function resetEditUploadArea() {
    const uploadArea = document.getElementById('edit_upload_area');
    if (uploadArea) {
        uploadArea.innerHTML = `
            <div class="upload-content">
                <i class="fas fa-cloud-upload-alt"></i>
                <p>点击选择图片或拖拽图片到此处</p>
                <small>支持 JPG、PNG、GIF、WebP 格式，最大5MB</small>
            </div>
        `;
    }
}

// ==================== 腾讯云VOD视频上传功能 ====================

let tcVod = null;
let currentUploader = null;

// 初始化腾讯云VOD
function initTcVod() {
    if (typeof TcVod !== 'undefined') {
        tcVod = new TcVod.default({
            getSignature: getVodSignature
        });
        console.log('腾讯云VOD SDK初始化成功');
    } else {
        console.error('腾讯云VOD SDK未加载');
    }
}

// 获取上传签名
async function getVodSignature() {
    try {
        const response = await fetch('../api/vod-signature.php', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            return result.data.signature;
        } else {
            throw new Error(result.message || '获取签名失败');
        }
    } catch (error) {
        console.error('获取VOD签名失败:', error);
        throw error;
    }
}

// 初始化视频上传功能
function initVideoUpload() {
    // 初始化腾讯云VOD
    initTcVod();

    const videoTypeRadios = document.querySelectorAll('input[name="video_type"]');
    const urlInput = document.getElementById('video_url_input');
    const uploadInput = document.getElementById('video_upload_input');
    const uploadArea = document.getElementById('video_upload_area');
    const fileInput = document.getElementById('video_file');

    // 检查必要元素是否存在
    if (!uploadArea || !fileInput || !urlInput || !uploadInput) {
        return;
    }

    // 切换输入方式
    videoTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'url') {
                urlInput.style.display = 'block';
                uploadInput.style.display = 'none';
                clearVideoUpload();
            } else {
                urlInput.style.display = 'none';
                uploadInput.style.display = 'block';
                // 清空视频链接
                const videoUrlInput = document.getElementById('video_url');
                if (videoUrlInput) videoUrlInput.value = '';
                const durationInput = document.getElementById('duration');
                if (durationInput) durationInput.value = '';
            }
        });
    });

    // 点击上传区域
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    // 文件选择
    fileInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            uploadVideoFile(this.files[0]);
        }
    });

    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files && files[0]) {
            uploadVideoFile(files[0]);
        }
    });

    // 取消上传按钮
    const cancelBtn = document.getElementById('video_cancel_btn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            cancelVideoUpload();
        });
    }
}

// 上传视频文件
function uploadVideoFile(file) {
    if (!tcVod) {
        alert('腾讯云VOD SDK未初始化，请刷新页面重试');
        return;
    }

    // 验证文件类型
    const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/mkv', 'video/webm', 'video/m4v', 'video/3gp'];
    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(mp4|avi|mov|wmv|flv|mkv|webm|m4v|3gp)$/i)) {
        alert('不支持的视频格式，请选择 MP4、AVI、MOV、WMV、FLV、MKV、WebM、M4V、3GP 格式的视频文件');
        return;
    }

    // 验证文件大小（5GB）
    if (file.size > 5 * 1024 * 1024 * 1024) {
        alert('视频文件大小不能超过5GB');
        return;
    }

    // 显示上传进度界面
    showVideoUploadProgress(file.name);

    try {
        // 创建上传实例
        currentUploader = tcVod.upload({
            mediaFile: file
        });

        // 监听上传进度
        currentUploader.on('media_progress', function(info) {
            updateVideoUploadProgress(info.percent);
        });

        // 监听上传完成
        currentUploader.on('media_upload', function(info) {
            console.log('视频上传完成:', info);
        });

        // 上传完成
        currentUploader.done().then(function(doneResult) {
            console.log('上传结果:', doneResult);
            handleVideoUploadSuccess(doneResult);
        }).catch(function(error) {
            console.error('上传失败:', error);
            handleVideoUploadError(error);
        });

    } catch (error) {
        console.error('创建上传实例失败:', error);
        alert('上传失败: ' + error.message);
        resetVideoUploadArea();
    }
}

// 显示视频上传进度
function showVideoUploadProgress(fileName) {
    const uploadArea = document.getElementById('video_upload_area');
    const progressDiv = document.getElementById('video_upload_progress');
    const fileNameSpan = document.getElementById('video_file_name');

    if (uploadArea && progressDiv && fileNameSpan) {
        uploadArea.style.display = 'none';
        progressDiv.style.display = 'block';
        fileNameSpan.textContent = fileName;
    }
}

// 更新视频上传进度
function updateVideoUploadProgress(percent) {
    const progressBar = document.getElementById('video_progress_bar');
    const progressPercent = document.getElementById('video_progress_percent');

    if (progressBar && progressPercent) {
        const percentValue = Math.floor(percent * 100);
        progressBar.style.width = percentValue + '%';
        progressPercent.textContent = percentValue + '%';
    }
}

// 处理视频上传成功
function handleVideoUploadSuccess(result) {
    console.log('VOD上传成功，完整结果:', result);

    const progressDiv = document.getElementById('video_upload_progress');
    const successDiv = document.getElementById('video_upload_success');
    const fileIdSpan = document.getElementById('video_file_id');
    const durationSpan = document.getElementById('video_duration_display');

    // 隐藏进度，显示成功界面
    if (progressDiv && successDiv) {
        progressDiv.style.display = 'none';
        successDiv.style.display = 'block';
    }

    // 显示文件信息
    if (fileIdSpan) {
        fileIdSpan.textContent = result.fileId || '未知';
    }

    // 处理时长信息
    let duration = 0;
    if (result.video && result.video.duration) {
        duration = Math.floor(result.video.duration);
    } else if (result.duration) {
        duration = Math.floor(result.duration);
    }

    if (durationSpan) {
        if (duration > 0) {
            durationSpan.textContent = formatDuration(duration);
        } else {
            durationSpan.textContent = '转码中，时长待确定';
        }
    }

    // 设置隐藏字段值
    const fileIdInput = document.getElementById('vod_file_id');
    const videoUrlInput = document.getElementById('vod_video_url');
    const durationInput = document.getElementById('vod_duration');

    if (fileIdInput) {
        fileIdInput.value = result.fileId || '';
        console.log('设置 vod_file_id:', result.fileId);
    }

    // 处理视频URL - 腾讯云VOD上传成功后可能没有立即返回播放URL
    let videoUrl = '';
    if (result.video && result.video.url) {
        videoUrl = result.video.url;
    } else if (result.url) {
        videoUrl = result.url;
    } else {
        // 如果没有返回播放URL，我们先留空，后续通过转码状态检查获取
        console.log('上传成功但未返回播放URL，将通过转码状态检查获取');
    }

    if (videoUrlInput) {
        videoUrlInput.value = videoUrl;
        console.log('设置 vod_video_url:', videoUrl);
    }

    if (durationInput) {
        durationInput.value = duration;
        // 同时设置表单中的时长字段
        const formDurationInput = document.getElementById('duration');
        if (formDurationInput) {
            formDurationInput.value = duration;
        }
        console.log('设置 vod_duration:', duration);
    }

    // 如果没有播放URL，启动转码状态检查
    if (!videoUrl && result.fileId) {
        console.log('启动转码状态检查...');
        checkTranscodingStatus(result.fileId);
    }

    console.log('视频上传成功，文件ID:', result.fileId, '播放URL:', videoUrl);
}

// 检查转码状态
function checkTranscodingStatus(fileId, maxRetries = 10, currentRetry = 0) {
    if (!fileId) {
        console.error('检查转码状态失败：fileId为空');
        return;
    }

    console.log(`检查转码状态 (${currentRetry + 1}/${maxRetries}):`, fileId);

    fetch(`../api/vod-status.php?file_id=${encodeURIComponent(fileId)}`)
        .then(response => response.json())
        .then(data => {
            console.log('转码状态检查结果:', data);

            if (data.success && data.data) {
                const videoData = data.data;

                // 检查是否有可用的播放URL
                let playUrl = '';

                // 优先使用转码后的URL
                if (videoData.transcoding && videoData.transcoding.streams && videoData.transcoding.streams.length > 0) {
                    // 选择第一个转码流的URL
                    playUrl = videoData.transcoding.streams[0].url;
                } else if (videoData.playback_urls && videoData.playback_urls.length > 0) {
                    // 使用自适应流URL
                    playUrl = videoData.playback_urls[0].url;
                }

                if (playUrl) {
                    // 更新播放URL
                    const videoUrlInput = document.getElementById('vod_video_url');
                    if (videoUrlInput) {
                        videoUrlInput.value = playUrl;
                        console.log('更新播放URL:', playUrl);
                    }

                    // 更新时长
                    if (videoData.duration > 0) {
                        const durationInput = document.getElementById('vod_duration');
                        const formDurationInput = document.getElementById('duration');
                        const durationSpan = document.getElementById('video_duration_display');

                        if (durationInput) {
                            durationInput.value = videoData.duration;
                        }
                        if (formDurationInput) {
                            formDurationInput.value = videoData.duration;
                        }
                        if (durationSpan) {
                            durationSpan.textContent = formatDuration(videoData.duration);
                        }
                        console.log('更新时长:', videoData.duration);
                    }

                    console.log('转码完成，获取到播放URL');
                } else if (videoData.transcoding && videoData.transcoding.status === 'PROCESSING') {
                    // 转码还在进行中，继续检查
                    if (currentRetry < maxRetries) {
                        console.log('转码进行中，5秒后重试...');
                        setTimeout(() => {
                            checkTranscodingStatus(fileId, maxRetries, currentRetry + 1);
                        }, 5000);
                    } else {
                        console.log('转码检查达到最大重试次数，停止检查');
                    }
                } else {
                    console.log('转码状态未知或失败:', videoData.transcoding);
                }
            } else {
                console.error('检查转码状态失败:', data.message);

                // 如果还有重试次数，继续重试
                if (currentRetry < maxRetries) {
                    console.log('5秒后重试...');
                    setTimeout(() => {
                        checkTranscodingStatus(fileId, maxRetries, currentRetry + 1);
                    }, 5000);
                }
            }
        })
        .catch(error => {
            console.error('检查转码状态请求失败:', error);

            // 如果还有重试次数，继续重试
            if (currentRetry < maxRetries) {
                console.log('5秒后重试...');
                setTimeout(() => {
                    checkTranscodingStatus(fileId, maxRetries, currentRetry + 1);
                }, 5000);
            }
        });
}

// 检查VOD转码状态（用于课时列表）
function checkVodStatus(lessonId, fileId) {
    if (!fileId) {
        alert('文件ID为空，无法检查状态');
        return;
    }

    console.log('检查VOD状态:', lessonId, fileId);

    // 显示加载状态
    const button = event.target.closest('button');
    const originalHtml = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    fetch(`../api/vod-update-status.php?lesson_id=${lessonId}&file_id=${encodeURIComponent(fileId)}&update_db=1`)
        .then(response => response.json())
        .then(data => {
            console.log('VOD状态检查结果:', data);

            if (data.success && data.data) {
                if (data.data.updated) {
                    alert('转码完成，播放URL已更新！页面将刷新以显示最新状态。');
                    location.reload();
                } else if (data.data.best_play_url) {
                    alert('视频已可播放，但URL未发生变化。');
                } else if (data.data.transcoding && data.data.transcoding.status === 'PROCESSING') {
                    alert('视频仍在转码中，请稍后再试。');
                } else {
                    alert('转码状态：' + (data.data.transcoding.status_text || '未知'));
                }
            } else {
                alert('检查失败：' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('检查VOD状态失败:', error);
            alert('检查失败：' + error.message);
        })
        .finally(() => {
            // 恢复按钮状态
            button.innerHTML = originalHtml;
            button.disabled = false;
        });
}

// 处理视频上传错误
function handleVideoUploadError(error) {
    alert('视频上传失败: ' + (error.message || '未知错误'));
    resetVideoUploadArea();
}

// 取消视频上传
function cancelVideoUpload() {
    if (currentUploader) {
        currentUploader.cancel();
        currentUploader = null;
    }
    resetVideoUploadArea();
}

// 清除视频上传
function clearVideoUpload() {
    if (currentUploader) {
        currentUploader.cancel();
        currentUploader = null;
    }
    resetVideoUploadArea();

    // 清空隐藏字段
    const fileIdInput = document.getElementById('vod_file_id');
    const videoUrlInput = document.getElementById('vod_video_url');
    const durationInput = document.getElementById('vod_duration');

    if (fileIdInput) fileIdInput.value = '';
    if (videoUrlInput) videoUrlInput.value = '';
    if (durationInput) durationInput.value = '';
}

// 重置视频上传区域
function resetVideoUploadArea() {
    const uploadArea = document.getElementById('video_upload_area');
    const progressDiv = document.getElementById('video_upload_progress');
    const successDiv = document.getElementById('video_upload_success');

    if (uploadArea) {
        uploadArea.style.display = 'block';
        uploadArea.innerHTML = `
            <div class="upload-content">
                <i class="fas fa-video"></i>
                <p>点击选择视频文件或拖拽视频到此处</p>
                <small>支持 MP4、AVI、MOV、WMV、FLV、MKV 等格式，最大5GB</small>
            </div>
        `;
    }

    if (progressDiv) progressDiv.style.display = 'none';
    if (successDiv) successDiv.style.display = 'none';
}

// 格式化时长显示
function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

// ==================== 编辑模态框视频上传功能 ====================

let editCurrentUploader = null;

// 初始化编辑视频上传功能
function initEditVideoUpload() {
    const videoTypeRadios = document.querySelectorAll('input[name="edit_video_type"]');
    const urlInput = document.getElementById('edit_video_url_input');
    const uploadInput = document.getElementById('edit_video_upload_input');
    const uploadArea = document.getElementById('edit_video_upload_area');
    const fileInput = document.getElementById('edit_video_file');

    // 检查必要元素是否存在
    if (!uploadArea || !fileInput || !urlInput || !uploadInput) {
        return;
    }

    // 切换输入方式
    videoTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'url') {
                urlInput.style.display = 'block';
                uploadInput.style.display = 'none';
                clearEditVideoUpload();
            } else {
                urlInput.style.display = 'none';
                uploadInput.style.display = 'block';
                // 清空视频链接
                const videoUrlInput = document.getElementById('edit_video_url');
                if (videoUrlInput) videoUrlInput.value = '';
                const durationInput = document.getElementById('edit_duration');
                if (durationInput) durationInput.value = '';
            }
        });
    });

    // 点击上传区域
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    // 文件选择
    fileInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            uploadEditVideoFile(this.files[0]);
        }
    });

    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files && files[0]) {
            uploadEditVideoFile(files[0]);
        }
    });

    // 取消上传按钮
    const cancelBtn = document.getElementById('edit_video_cancel_btn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            cancelEditVideoUpload();
        });
    }
}

// 上传编辑视频文件
function uploadEditVideoFile(file) {
    if (!tcVod) {
        alert('腾讯云VOD SDK未初始化，请刷新页面重试');
        return;
    }

    // 验证文件类型
    const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/mkv', 'video/webm', 'video/m4v', 'video/3gp'];
    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(mp4|avi|mov|wmv|flv|mkv|webm|m4v|3gp)$/i)) {
        alert('不支持的视频格式，请选择 MP4、AVI、MOV、WMV、FLV、MKV、WebM、M4V、3GP 格式的视频文件');
        return;
    }

    // 验证文件大小（5GB）
    if (file.size > 5 * 1024 * 1024 * 1024) {
        alert('视频文件大小不能超过5GB');
        return;
    }

    // 显示上传进度界面
    showEditVideoUploadProgress(file.name);

    try {
        // 创建上传实例
        editCurrentUploader = tcVod.upload({
            mediaFile: file
        });

        // 监听上传进度
        editCurrentUploader.on('media_progress', function(info) {
            updateEditVideoUploadProgress(info.percent);
        });

        // 监听上传完成
        editCurrentUploader.on('media_upload', function(info) {
            console.log('编辑视频上传完成:', info);
        });

        // 上传完成
        editCurrentUploader.done().then(function(doneResult) {
            console.log('编辑上传结果:', doneResult);
            handleEditVideoUploadSuccess(doneResult);
        }).catch(function(error) {
            console.error('编辑上传失败:', error);
            handleEditVideoUploadError(error);
        });

    } catch (error) {
        console.error('创建编辑上传实例失败:', error);
        alert('上传失败: ' + error.message);
        resetEditVideoUploadArea();
    }
}

// 显示编辑视频上传进度
function showEditVideoUploadProgress(fileName) {
    const uploadArea = document.getElementById('edit_video_upload_area');
    const progressDiv = document.getElementById('edit_video_upload_progress');
    const fileNameSpan = document.getElementById('edit_video_file_name');

    if (uploadArea && progressDiv && fileNameSpan) {
        uploadArea.style.display = 'none';
        progressDiv.style.display = 'block';
        fileNameSpan.textContent = fileName;
    }
}

// 更新编辑视频上传进度
function updateEditVideoUploadProgress(percent) {
    const progressBar = document.getElementById('edit_video_progress_bar');
    const progressPercent = document.getElementById('edit_video_progress_percent');

    if (progressBar && progressPercent) {
        const percentValue = Math.floor(percent * 100);
        progressBar.style.width = percentValue + '%';
        progressPercent.textContent = percentValue + '%';
    }
}

// 处理编辑视频上传成功
function handleEditVideoUploadSuccess(result) {
    const progressDiv = document.getElementById('edit_video_upload_progress');
    const successDiv = document.getElementById('edit_video_upload_success');
    const fileIdSpan = document.getElementById('edit_video_file_id');
    const durationSpan = document.getElementById('edit_video_duration_display');

    // 隐藏进度，显示成功界面
    if (progressDiv && successDiv) {
        progressDiv.style.display = 'none';
        successDiv.style.display = 'block';
    }

    // 显示文件信息
    if (fileIdSpan) {
        fileIdSpan.textContent = result.fileId || '未知';
    }

    if (durationSpan && result.video && result.video.duration) {
        const duration = Math.floor(result.video.duration);
        durationSpan.textContent = formatDuration(duration);
    } else {
        durationSpan.textContent = '未知';
    }

    // 设置隐藏字段值
    const fileIdInput = document.getElementById('edit_vod_file_id');
    const videoUrlInput = document.getElementById('edit_vod_video_url');
    const durationInput = document.getElementById('edit_vod_duration');

    if (fileIdInput) {
        fileIdInput.value = result.fileId || '';
    }

    if (videoUrlInput && result.video && result.video.url) {
        videoUrlInput.value = result.video.url;
    }

    if (durationInput && result.video && result.video.duration) {
        durationInput.value = Math.floor(result.video.duration);
        // 同时设置表单中的时长字段
        const formDurationInput = document.getElementById('edit_duration');
        if (formDurationInput) {
            formDurationInput.value = Math.floor(result.video.duration);
        }
    }

    console.log('编辑视频上传成功，文件ID:', result.fileId);
}

// 处理编辑视频上传错误
function handleEditVideoUploadError(error) {
    alert('视频上传失败: ' + (error.message || '未知错误'));
    resetEditVideoUploadArea();
}

// 取消编辑视频上传
function cancelEditVideoUpload() {
    if (editCurrentUploader) {
        editCurrentUploader.cancel();
        editCurrentUploader = null;
    }
    resetEditVideoUploadArea();
}

// 清除编辑视频上传
function clearEditVideoUpload() {
    if (editCurrentUploader) {
        editCurrentUploader.cancel();
        editCurrentUploader = null;
    }
    resetEditVideoUploadArea();

    // 清空隐藏字段
    const fileIdInput = document.getElementById('edit_vod_file_id');
    const videoUrlInput = document.getElementById('edit_vod_video_url');
    const durationInput = document.getElementById('edit_vod_duration');

    if (fileIdInput) fileIdInput.value = '';
    if (videoUrlInput) videoUrlInput.value = '';
    if (durationInput) durationInput.value = '';
}

// 重置编辑视频上传区域
function resetEditVideoUploadArea() {
    const uploadArea = document.getElementById('edit_video_upload_area');
    const progressDiv = document.getElementById('edit_video_upload_progress');
    const successDiv = document.getElementById('edit_video_upload_success');

    if (uploadArea) {
        uploadArea.style.display = 'block';
        uploadArea.innerHTML = `
            <div class="upload-content">
                <i class="fas fa-video"></i>
                <p>点击选择视频文件或拖拽视频到此处</p>
                <small>支持 MP4、AVI、MOV、WMV、FLV、MKV 等格式，最大5GB</small>
            </div>
        `;
    }

    if (progressDiv) progressDiv.style.display = 'none';
    if (successDiv) successDiv.style.display = 'none';
}
</script>

<?php render_admin_footer(); ?>
