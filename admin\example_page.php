<?php
/**
 * 示例页面 - 展示如何使用UI设计规范开发新功能
 * 这个文件演示了标准化组件的使用方法
 */

session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

$success_message = '';
$error_message = '';

// 示例：处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['demo_action'])) {
        $demo_name = trim($_POST['demo_name']);
        $demo_email = trim($_POST['demo_email']);
        $demo_type = $_POST['demo_type'];
        
        if (empty($demo_name) || empty($demo_email)) {
            $error_message = '请填写所有必填字段';
        } else {
            // 这里可以添加数据库操作
            $success_message = '操作执行成功！';
        }
    }
}

// 示例数据
$demo_data = [
    ['id' => 1, 'name' => '张三', 'email' => '<EMAIL>', 'type' => 'VIP', 'status' => 'active'],
    ['id' => 2, 'name' => '李四', 'email' => '<EMAIL>', 'type' => '普通', 'status' => 'inactive'],
    ['id' => 3, 'name' => '王五', 'email' => '<EMAIL>', 'type' => 'VIP', 'status' => 'active'],
];

// 渲染页面头部
render_admin_header('示例页面', 'example');
?>

<!-- 显示消息 -->
<?php if ($success_message): ?>
    <?php show_success_message($success_message); ?>
<?php endif; ?>

<?php if ($error_message): ?>
    <?php show_error_message($error_message); ?>
<?php endif; ?>

<!-- 页面说明 -->
<?php render_card_start('页面说明'); ?>
    <div style="background: #e8f4fd; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db;">
        <h4 style="margin: 0 0 10px 0; color: #2c3e50;">
            <i class="fas fa-info-circle"></i> 关于此页面
        </h4>
        <p style="margin: 0; color: #666;">
            这是一个示例页面，展示了如何使用标准化的UI组件和设计规范来开发新的管理功能。
            所有新功能页面都应该遵循这个模板的结构和样式。
        </p>
    </div>
<?php render_card_end(); ?>

<!-- 表单示例 -->
<?php render_card_start('表单组件示例'); ?>
    <?php render_form_start('', 'post', 'demoForm'); ?>
        <div class="admin-form-row">
            <div class="admin-form-col">
                <?php render_form_input('姓名', 'demo_name', 'text', '', true, '请输入姓名'); ?>
            </div>
            <div class="admin-form-col">
                <?php render_form_input('邮箱', 'demo_email', 'email', '', true, '请输入邮箱地址'); ?>
            </div>
        </div>
        
        <div class="admin-form-group">
            <label class="admin-form-label" for="demo_type">用户类型</label>
            <select class="admin-form-input" name="demo_type" id="demo_type">
                <option value="">请选择类型</option>
                <option value="VIP">VIP用户</option>
                <option value="普通">普通用户</option>
            </select>
        </div>
        
        <div class="admin-form-group">
            <label class="admin-form-label" for="demo_description">描述信息</label>
            <textarea class="admin-form-input" name="demo_description" id="demo_description" 
                      rows="3" placeholder="请输入描述信息（可选）"></textarea>
        </div>
        
        <div class="admin-actions">
            <?php render_button('提交', 'submit', 'admin-btn-primary', 'return validateForm("demoForm")'); ?>
            <?php render_button('重置', 'button', 'admin-btn-secondary', 'resetForm()'); ?>
        </div>
        
        <input type="hidden" name="demo_action" value="1">
    <?php render_form_end(); ?>
<?php render_card_end(); ?>

<!-- 按钮组件示例 -->
<?php render_card_start('按钮组件示例'); ?>
    <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 20px;">
        <?php render_button('主要按钮', 'button', 'admin-btn-primary'); ?>
        <?php render_button('成功按钮', 'button', 'admin-btn-success'); ?>
        <?php render_button('危险按钮', 'button', 'admin-btn-danger'); ?>
        <?php render_button('次要按钮', 'button', 'admin-btn-secondary'); ?>
    </div>
    
    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
        <?php render_button('小按钮', 'button', 'admin-btn-primary admin-btn-sm'); ?>
        <?php render_button('小成功', 'button', 'admin-btn-success admin-btn-sm'); ?>
        <?php render_button('小危险', 'button', 'admin-btn-danger admin-btn-sm'); ?>
        <?php render_button('小次要', 'button', 'admin-btn-secondary admin-btn-sm'); ?>
    </div>
<?php render_card_end(); ?>

<!-- 状态标签示例 -->
<?php render_card_start('状态标签示例'); ?>
    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
        <?php render_badge('成功状态', 'success'); ?>
        <?php render_badge('危险状态', 'danger'); ?>
        <?php render_badge('警告状态', 'warning'); ?>
        <?php render_badge('信息状态', 'info'); ?>
    </div>
<?php render_card_end(); ?>

<!-- 表格组件示例 -->
<?php render_card_start('表格组件示例'); ?>
    <?php render_table_start(['ID', '姓名', '邮箱', '类型', '状态', '操作']); ?>
        <?php foreach ($demo_data as $row): ?>
        <tr>
            <td><?php echo $row['id']; ?></td>
            <td><strong><?php echo htmlspecialchars($row['name']); ?></strong></td>
            <td><?php echo htmlspecialchars($row['email']); ?></td>
            <td>
                <?php if ($row['type'] === 'VIP'): ?>
                    <?php render_badge('VIP', 'warning'); ?>
                <?php else: ?>
                    <?php render_badge('普通', 'info'); ?>
                <?php endif; ?>
            </td>
            <td>
                <?php if ($row['status'] === 'active'): ?>
                    <?php render_badge('活跃', 'success'); ?>
                <?php else: ?>
                    <?php render_badge('非活跃', 'danger'); ?>
                <?php endif; ?>
            </td>
            <td>
                <div class="admin-actions">
                    <?php render_link_button('编辑', '#', 'admin-btn-primary admin-btn-sm'); ?>
                    <?php render_link_button('删除', '#', 'admin-btn-danger admin-btn-sm', 'return confirmDelete()'); ?>
                </div>
            </td>
        </tr>
        <?php endforeach; ?>
    <?php render_table_end(); ?>
<?php render_card_end(); ?>

<!-- 统计卡片示例 -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
    <div class="admin-card">
        <div class="admin-card-body" style="text-align: center;">
            <div style="width: 50px; height: 50px; background: #3498db; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                <i class="fas fa-chart-bar" style="font-size: 24px; color: white;"></i>
            </div>
            <h3 style="margin: 0; font-size: 28px; color: #2c3e50;">1,234</h3>
            <p style="margin: 5px 0 0 0; color: #666;">总数据量</p>
        </div>
    </div>
    
    <div class="admin-card">
        <div class="admin-card-body" style="text-align: center;">
            <div style="width: 50px; height: 50px; background: #27ae60; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                <i class="fas fa-check-circle" style="font-size: 24px; color: white;"></i>
            </div>
            <h3 style="margin: 0; font-size: 28px; color: #2c3e50;">98.5%</h3>
            <p style="margin: 5px 0 0 0; color: #666;">成功率</p>
        </div>
    </div>
    
    <div class="admin-card">
        <div class="admin-card-body" style="text-align: center;">
            <div style="width: 50px; height: 50px; background: #e74c3c; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                <i class="fas fa-exclamation-triangle" style="font-size: 24px; color: white;"></i>
            </div>
            <h3 style="margin: 0; font-size: 28px; color: #2c3e50;">12</h3>
            <p style="margin: 5px 0 0 0; color: #666;">待处理</p>
        </div>
    </div>
</div>

<!-- 开发提示 -->
<?php render_card_start('开发提示'); ?>
    <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #f39c12;">
        <h4 style="margin: 0 0 15px 0; color: #856404;">
            <i class="fas fa-lightbulb"></i> 开发新功能时请注意
        </h4>
        <ul style="margin: 0; padding-left: 20px; color: #856404;">
            <li>使用 <code>render_admin_header()</code> 和 <code>render_admin_footer()</code> 包装页面</li>
            <li>使用 <code>render_card_start()</code> 和 <code>render_card_end()</code> 创建内容区域</li>
            <li>使用标准化的表单、按钮、表格组件函数</li>
            <li>遵循统一的CSS类命名规范</li>
            <li>在导航菜单中添加新页面的链接</li>
            <li>确保响应式设计在移动设备上正常显示</li>
        </ul>
    </div>
<?php render_card_end(); ?>

<script>
function resetForm() {
    document.getElementById('demoForm').reset();
}

// 这个页面不会出现在实际的导航菜单中，仅作为开发参考
console.log('示例页面加载完成 - 这是一个UI组件展示页面');
</script>

<?php render_admin_footer(); ?>
