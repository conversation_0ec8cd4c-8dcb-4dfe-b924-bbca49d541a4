<?php
/**
 * 课程API诊断工具
 * 详细检查课程相关的所有问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

echo "<h1>课程API诊断工具</h1>";

// 1. 检查数据库连接
echo "<h2>1. 数据库连接检查</h2>";
try {
    require_once '../includes/db.php';
    if ($conn->connect_error) {
        echo "❌ 数据库连接失败: " . $conn->connect_error . "<br>";
        exit;
    } else {
        echo "✅ 数据库连接成功<br>";
    }
} catch (Exception $e) {
    echo "❌ 数据库连接异常: " . $e->getMessage() . "<br>";
    exit;
}

// 2. 检查courses表结构
echo "<h2>2. courses表结构检查</h2>";
try {
    $result = $conn->query("DESCRIBE courses");
    if (!$result) {
        echo "❌ courses表不存在或查询失败: " . $conn->error . "<br>";
    } else {
        echo "✅ courses表存在，字段如下:<br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>默认值</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['Field']}</td>";
            echo "<td>{$row['Type']}</td>";
            echo "<td>{$row['Null']}</td>";
            echo "<td>{$row['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "❌ 检查courses表结构异常: " . $e->getMessage() . "<br>";
}

// 3. 检查courses表数据
echo "<h2>3. courses表数据检查</h2>";
try {
    $result = $conn->query("SELECT COUNT(*) as count FROM courses");
    if (!$result) {
        echo "❌ 查询courses表数据失败: " . $conn->error . "<br>";
    } else {
        $row = $result->fetch_assoc();
        $count = $row['count'];
        echo "✅ courses表共有 {$count} 条记录<br>";
        
        if ($count > 0) {
            // 显示前几条记录
            $result = $conn->query("SELECT id, title, status, is_recommended, view_count, price, teacher_name FROM courses LIMIT 5");
            if ($result) {
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr><th>ID</th><th>标题</th><th>状态</th><th>推荐</th><th>观看数</th><th>价格</th><th>讲师</th></tr>";
                while ($row = $result->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>{$row['id']}</td>";
                    echo "<td>{$row['title']}</td>";
                    echo "<td>{$row['status']}</td>";
                    echo "<td>{$row['is_recommended']}</td>";
                    echo "<td>{$row['view_count']}</td>";
                    echo "<td>{$row['price']}</td>";
                    echo "<td>{$row['teacher_name']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
    }
} catch (Exception $e) {
    echo "❌ 检查courses表数据异常: " . $e->getMessage() . "<br>";
}

// 4. 检查auth.php文件
echo "<h2>4. auth.php文件检查</h2>";
try {
    if (file_exists('auth.php')) {
        echo "✅ auth.php文件存在<br>";
        require_once 'auth.php';
        $auth = new AuthAPI();
        echo "✅ AuthAPI类实例化成功<br>";
    } else {
        echo "❌ auth.php文件不存在<br>";
    }
} catch (Exception $e) {
    echo "❌ auth.php文件检查异常: " . $e->getMessage() . "<br>";
}

// 5. 测试课程列表API
echo "<h2>5. 课程列表API测试</h2>";
try {
    // 模拟API调用
    ob_start();
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_GET = [];
    
    // 捕获可能的输出
    include 'courseList.php';
    $output = ob_get_clean();
    
    if (!empty($output)) {
        echo "✅ 课程列表API执行成功<br>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars($output);
        echo "</pre>";
    } else {
        echo "❌ 课程列表API无输出<br>";
    }
} catch (Exception $e) {
    echo "❌ 课程列表API测试异常: " . $e->getMessage() . "<br>";
}

// 6. 测试推荐课程API
echo "<h2>6. 推荐课程API测试</h2>";
try {
    ob_start();
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_GET = ['recommended' => 1, 'limit' => 6];
    
    include 'courseList.php';
    $output = ob_get_clean();
    
    if (!empty($output)) {
        echo "✅ 推荐课程API执行成功<br>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars($output);
        echo "</pre>";
    } else {
        echo "❌ 推荐课程API无输出<br>";
    }
} catch (Exception $e) {
    echo "❌ 推荐课程API测试异常: " . $e->getMessage() . "<br>";
}

// 7. 检查管理员表
echo "<h2>7. 管理员表检查</h2>";
try {
    $result = $conn->query("SELECT COUNT(*) as count FROM admins");
    if (!$result) {
        echo "❌ 查询admins表失败: " . $conn->error . "<br>";
    } else {
        $row = $result->fetch_assoc();
        $count = $row['count'];
        echo "✅ admins表共有 {$count} 条记录<br>";
        
        if ($count == 0) {
            echo "⚠️ 警告: 没有管理员账户，这可能导致课程创建失败<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ 检查admins表异常: " . $e->getMessage() . "<br>";
}

// 8. PHP环境检查
echo "<h2>8. PHP环境检查</h2>";
echo "✅ PHP版本: " . PHP_VERSION . "<br>";
echo "✅ MySQL扩展: " . (extension_loaded('mysqli') ? '已加载' : '❌ 未加载') . "<br>";
echo "✅ JSON扩展: " . (extension_loaded('json') ? '已加载' : '❌ 未加载') . "<br>";

// 9. 文件权限检查
echo "<h2>9. 文件权限检查</h2>";
$files_to_check = ['courseList.php', 'auth.php', '../includes/db.php'];
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ {$file} 存在且可读<br>";
    } else {
        echo "❌ {$file} 不存在或不可读<br>";
    }
}

echo "<h2>诊断完成</h2>";
echo "<p>如果以上检查都通过，但微信小程序仍然无法获取课程数据，请检查：</p>";
echo "<ul>";
echo "<li>微信开发者工具的网络设置</li>";
echo "<li>本地防火墙设置</li>";
echo "<li>API请求的具体错误信息</li>";
echo "</ul>";

echo "<p><a href='test_courses.html'>点击这里进行浏览器API测试</a></p>";
echo "<p><a href='../wx/test-api.html'>点击这里进行微信小程序连接测试</a></p>";
?>
