<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

$success_message = '';
$error_message = '';

// Add admin
if (isset($_POST['add_admin'])) {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $email = trim($_POST['email']);
    $name = trim($_POST['name']);

    if (empty($username) || empty($password) || empty($confirm_password)) {
        $error_message = '请填写所有必填字段';
    } elseif ($password !== $confirm_password) {
        $error_message = '两次输入的密码不一致';
    } elseif (strlen($password) < 6) {
        $error_message = '密码长度至少6位';
    } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = '邮箱格式不正确';
    } else {
        // 检查用户名是否已存在
        $check_stmt = $conn->prepare("SELECT id FROM admins WHERE username = ?");
        if ($check_stmt) {
            $check_stmt->bind_param("s", $username);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                $error_message = '该用户名已存在';
            } else {
                $password_hash = password_hash($password, PASSWORD_DEFAULT);

                // 处理空值
                $email = empty($email) ? null : $email;
                $name = empty($name) ? null : $name;

                $stmt = $conn->prepare("INSERT INTO admins (username, password, email, name, status) VALUES (?, ?, ?, ?, 'active')");
                if ($stmt) {
                    $stmt->bind_param("ssss", $username, $password_hash, $email, $name);
                    if ($stmt->execute()) {
                        $success_message = '管理员添加成功';
                        // 清空表单数据
                        $_POST = array();
                    } else {
                        $error_message = '添加管理员失败：' . $stmt->error;
                    }
                    $stmt->close();
                } else {
                    $error_message = '数据库准备语句失败：' . $conn->error;
                }
            }
            $check_stmt->close();
        } else {
            $error_message = '数据库查询失败：' . $conn->error;
        }
    }
}

// Change password
if (isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_new_password = $_POST['confirm_new_password'];
    $admin_id = $_SESSION['admin_id'];

    if (empty($current_password) || empty($new_password) || empty($confirm_new_password)) {
        $error_message = '请填写所有密码字段';
    } elseif ($new_password !== $confirm_new_password) {
        $error_message = '新密码两次输入不一致';
    } elseif (strlen($new_password) < 6) {
        $error_message = '新密码长度至少6位';
    } else {
        // 验证当前密码
        $stmt = $conn->prepare("SELECT password FROM admins WHERE id = ?");
        $stmt->bind_param("i", $admin_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $admin = $result->fetch_assoc();

        if ($admin && password_verify($current_password, $admin['password'])) {
            $new_password_hash = password_hash($new_password, PASSWORD_DEFAULT);
            $update_stmt = $conn->prepare("UPDATE admins SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $update_stmt->bind_param("si", $new_password_hash, $admin_id);
            if ($update_stmt->execute()) {
                $success_message = '密码修改成功';
            } else {
                $error_message = '密码修改失败';
            }
        } else {
            $error_message = '当前密码不正确';
        }
    }
}

// Edit admin
if (isset($_POST['edit_admin'])) {
    $edit_id = intval($_POST['edit_id']);
    $edit_username = trim($_POST['edit_username']);
    $edit_email = trim($_POST['edit_email']);
    $edit_name = trim($_POST['edit_name']);
    $edit_status = $_POST['edit_status'];

    if (empty($edit_username)) {
        $error_message = '用户名不能为空';
    } elseif (!empty($edit_email) && !filter_var($edit_email, FILTER_VALIDATE_EMAIL)) {
        $error_message = '邮箱格式不正确';
    } else {
        // 检查用户名是否已被其他管理员使用
        $check_stmt = $conn->prepare("SELECT id FROM admins WHERE username = ? AND id != ?");
        $check_stmt->bind_param("si", $edit_username, $edit_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error_message = '该用户名已被其他管理员使用';
        } else {
            $stmt = $conn->prepare("UPDATE admins SET username = ?, email = ?, name = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->bind_param("ssssi", $edit_username, $edit_email, $edit_name, $edit_status, $edit_id);
            if ($stmt->execute()) {
                $success_message = '管理员信息更新成功';
            } else {
                $error_message = '更新管理员信息失败';
            }
        }
    }
}

// Reset password
if (isset($_POST['reset_password'])) {
    $reset_id = intval($_POST['reset_id']);
    $new_password = $_POST['reset_new_password'];
    $confirm_password = $_POST['reset_confirm_password'];

    if ($reset_id == $_SESSION['admin_id']) {
        $error_message = '不能重置当前登录管理员的密码，请使用修改密码功能';
    } elseif (empty($new_password) || empty($confirm_password)) {
        $error_message = '请填写新密码';
    } elseif ($new_password !== $confirm_password) {
        $error_message = '两次输入的密码不一致';
    } elseif (strlen($new_password) < 6) {
        $error_message = '密码长度至少6位';
    } else {
        $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("UPDATE admins SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->bind_param("si", $password_hash, $reset_id);
        if ($stmt->execute()) {
            $success_message = '密码重置成功';
        } else {
            $error_message = '密码重置失败';
        }
    }
}

// Toggle status
if (isset($_GET['toggle_status'])) {
    $toggle_id = intval($_GET['toggle_status']);

    if ($toggle_id == $_SESSION['admin_id']) {
        $error_message = '不能修改当前登录管理员的状态';
    } else {
        // 获取当前状态
        $stmt = $conn->prepare("SELECT status FROM admins WHERE id = ?");
        $stmt->bind_param("i", $toggle_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $admin = $result->fetch_assoc();

        if ($admin) {
            $new_status = ($admin['status'] === 'active') ? 'inactive' : 'active';
            $update_stmt = $conn->prepare("UPDATE admins SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $update_stmt->bind_param("si", $new_status, $toggle_id);
            if ($update_stmt->execute()) {
                $success_message = '管理员状态更新成功';
            } else {
                $error_message = '状态更新失败';
            }
        }
    }
}

// Delete admin
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);

    // 防止删除当前登录的管理员
    if ($id == $_SESSION['admin_id']) {
        $error_message = '不能删除当前登录的管理员';
    } else {
        $stmt = $conn->prepare("DELETE FROM admins WHERE id = ?");
        $stmt->bind_param("i", $id);
        if ($stmt->execute()) {
            $success_message = '管理员删除成功';
        } else {
            $error_message = '删除管理员失败';
        }
    }
}

// Get admins with all information
$result = $conn->query("SELECT id, username, email, name, status, last_login_at, created_at FROM admins ORDER BY created_at DESC");

// 渲染页面头部
render_admin_header('管理员管理', 'admins');
?>

<!-- 显示消息 -->
<?php if ($success_message): ?>
    <?php show_success_message($success_message); ?>
<?php endif; ?>

<?php if ($error_message): ?>
    <?php show_error_message($error_message); ?>
<?php endif; ?>

<!-- 操作按钮区域 -->
<div class="admin-actions-bar" style="margin-bottom: 20px;">
    <button type="button" class="admin-btn admin-btn-primary" onclick="showChangePasswordModal()">
        <i class="fas fa-key"></i> 修改密码
    </button>
</div>

<!-- 添加管理员表单 -->
<?php render_card_start('添加新管理员'); ?>
    <?php render_form_start('', 'post', 'addAdminForm'); ?>
        <div class="admin-form-row">
            <div class="admin-form-col">
                <?php render_form_input('用户名', 'username', 'text', '', true, '请输入管理员用户名'); ?>
            </div>
            <div class="admin-form-col">
                <?php render_form_input('邮箱', 'email', 'email', '', false, '请输入邮箱地址（可选）'); ?>
            </div>
        </div>

        <div class="admin-form-row">
            <div class="admin-form-col">
                <?php render_form_input('姓名', 'name', 'text', '', false, '请输入真实姓名（可选）'); ?>
            </div>
        </div>

        <div class="admin-form-row">
            <div class="admin-form-col">
                <?php render_form_input('密码', 'password', 'password', '', true, '请输入密码（至少6位）'); ?>
            </div>
            <div class="admin-form-col">
                <?php render_form_input('确认密码', 'confirm_password', 'password', '', true, '请再次输入密码'); ?>
            </div>
        </div>

        <div class="admin-actions">
            <button type="submit" name="add_admin" class="admin-btn admin-btn-primary" onclick="return validateAdminForm()">
                <i class="fas fa-plus"></i> 添加管理员
            </button>
        </div>
    <?php render_form_end(); ?>
<?php render_card_end(); ?>

<!-- 管理员列表 -->
<?php render_card_start('管理员列表'); ?>
    <?php if ($result->num_rows > 0): ?>
        <?php render_table_start(['ID', '用户名', '邮箱', '姓名', '状态', '最后登录', '创建时间', '操作']); ?>
            <?php while ($row = $result->fetch_assoc()): ?>
            <tr>
                <td><?php echo $row['id']; ?></td>
                <td>
                    <strong><?php echo htmlspecialchars($row['username']); ?></strong>
                    <?php if ($row['id'] == $_SESSION['admin_id']): ?>
                        <span style="color: #27ae60; font-size: 12px;">(当前用户)</span>
                    <?php endif; ?>
                </td>
                <td><?php echo $row['email'] ? htmlspecialchars($row['email']) : '<span style="color: #999;">未设置</span>'; ?></td>
                <td><?php echo $row['name'] ? htmlspecialchars($row['name']) : '<span style="color: #999;">未设置</span>'; ?></td>
                <td>
                    <?php if ($row['status'] === 'active'): ?>
                        <?php if ($row['id'] == $_SESSION['admin_id']): ?>
                            <?php render_badge('在线', 'success'); ?>
                        <?php else: ?>
                            <?php render_badge('启用', 'success'); ?>
                        <?php endif; ?>
                    <?php else: ?>
                        <?php render_badge('禁用', 'danger'); ?>
                    <?php endif; ?>
                </td>
                <td>
                    <?php if ($row['last_login_at']): ?>
                        <?php echo date('Y-m-d H:i', strtotime($row['last_login_at'])); ?>
                    <?php else: ?>
                        <span style="color: #999;">从未登录</span>
                    <?php endif; ?>
                </td>
                <td><?php echo date('Y-m-d H:i', strtotime($row['created_at'])); ?></td>
                <td>
                    <div class="admin-actions">
                        <button type="button" class="admin-btn admin-btn-info admin-btn-sm"
                                onclick="showEditModal(<?php echo $row['id']; ?>, '<?php echo htmlspecialchars($row['username'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($row['email'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($row['name'], ENT_QUOTES); ?>', '<?php echo $row['status']; ?>')">
                            <i class="fas fa-edit"></i> 编辑
                        </button>

                        <?php if ($row['id'] != $_SESSION['admin_id']): ?>
                            <button type="button" class="admin-btn admin-btn-warning admin-btn-sm"
                                    onclick="showResetPasswordModal(<?php echo $row['id']; ?>, '<?php echo htmlspecialchars($row['username'], ENT_QUOTES); ?>')">
                                <i class="fas fa-key"></i> 重置密码
                            </button>

                            <a href="admins.php?toggle_status=<?php echo $row['id']; ?>"
                               class="admin-btn <?php echo $row['status'] === 'active' ? 'admin-btn-secondary' : 'admin-btn-success'; ?> admin-btn-sm"
                               onclick="return confirm('确定要<?php echo $row['status'] === 'active' ? '禁用' : '启用'; ?>管理员 <?php echo htmlspecialchars($row['username']); ?> 吗？')">
                                <i class="fas <?php echo $row['status'] === 'active' ? 'fa-ban' : 'fa-check'; ?>"></i>
                                <?php echo $row['status'] === 'active' ? '禁用' : '启用'; ?>
                            </a>

                            <a href="admins.php?delete=<?php echo $row['id']; ?>"
                               class="admin-btn admin-btn-danger admin-btn-sm"
                               onclick="return confirmDelete('确定要删除管理员 <?php echo htmlspecialchars($row['username']); ?> 吗？')">
                                <i class="fas fa-trash"></i> 删除
                            </a>
                        <?php else: ?>
                            <span style="color: #999; font-size: 12px;">当前用户</span>
                        <?php endif; ?>
                    </div>
                </td>
            </tr>
            <?php endwhile; ?>
        <?php render_table_end(); ?>
    <?php else: ?>
        <div style="text-align: center; padding: 40px; color: #666;">
            <i class="fas fa-user-shield" style="font-size: 48px; margin-bottom: 15px; opacity: 0.3;"></i>
            <p>暂无管理员数据</p>
        </div>
    <?php endif; ?>
<?php render_card_end(); ?>

<!-- 修改密码模态框 -->
<div id="changePasswordModal" class="admin-modal" style="display: none;">
    <div class="admin-modal-content">
        <div class="admin-modal-header">
            <h3>修改密码</h3>
            <span class="admin-modal-close" onclick="closeModal('changePasswordModal')">&times;</span>
        </div>
        <form method="post" onsubmit="return validateChangePasswordForm()">
            <div class="admin-modal-body">
                <div class="admin-form-group">
                    <label>当前密码 *</label>
                    <input type="password" name="current_password" required placeholder="请输入当前密码">
                </div>
                <div class="admin-form-group">
                    <label>新密码 *</label>
                    <input type="password" name="new_password" required placeholder="请输入新密码（至少6位）">
                </div>
                <div class="admin-form-group">
                    <label>确认新密码 *</label>
                    <input type="password" name="confirm_new_password" required placeholder="请再次输入新密码">
                </div>
            </div>
            <div class="admin-modal-footer">
                <button type="button" class="admin-btn admin-btn-secondary" onclick="closeModal('changePasswordModal')">取消</button>
                <button type="submit" name="change_password" class="admin-btn admin-btn-primary">确认修改</button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑管理员模态框 -->
<div id="editAdminModal" class="admin-modal" style="display: none;">
    <div class="admin-modal-content">
        <div class="admin-modal-header">
            <h3>编辑管理员信息</h3>
            <span class="admin-modal-close" onclick="closeModal('editAdminModal')">&times;</span>
        </div>
        <form method="post" onsubmit="return validateEditAdminForm()">
            <div class="admin-modal-body">
                <input type="hidden" name="edit_id" id="edit_id">
                <div class="admin-form-group">
                    <label>用户名 *</label>
                    <input type="text" name="edit_username" id="edit_username" required placeholder="请输入用户名">
                </div>
                <div class="admin-form-group">
                    <label>邮箱</label>
                    <input type="email" name="edit_email" id="edit_email" placeholder="请输入邮箱地址">
                </div>
                <div class="admin-form-group">
                    <label>姓名</label>
                    <input type="text" name="edit_name" id="edit_name" placeholder="请输入真实姓名">
                </div>
                <div class="admin-form-group">
                    <label>状态 *</label>
                    <select name="edit_status" id="edit_status" required>
                        <option value="active">启用</option>
                        <option value="inactive">禁用</option>
                    </select>
                </div>
            </div>
            <div class="admin-modal-footer">
                <button type="button" class="admin-btn admin-btn-secondary" onclick="closeModal('editAdminModal')">取消</button>
                <button type="submit" name="edit_admin" class="admin-btn admin-btn-primary">保存修改</button>
            </div>
        </form>
    </div>
</div>

<!-- 重置密码模态框 -->
<div id="resetPasswordModal" class="admin-modal" style="display: none;">
    <div class="admin-modal-content">
        <div class="admin-modal-header">
            <h3>重置密码</h3>
            <span class="admin-modal-close" onclick="closeModal('resetPasswordModal')">&times;</span>
        </div>
        <form method="post" onsubmit="return validateResetPasswordForm()">
            <div class="admin-modal-body">
                <input type="hidden" name="reset_id" id="reset_id">
                <p>正在为管理员 <strong id="reset_username"></strong> 重置密码</p>
                <div class="admin-form-group">
                    <label>新密码 *</label>
                    <input type="password" name="reset_new_password" required placeholder="请输入新密码（至少6位）">
                </div>
                <div class="admin-form-group">
                    <label>确认新密码 *</label>
                    <input type="password" name="reset_confirm_password" required placeholder="请再次输入新密码">
                </div>
            </div>
            <div class="admin-modal-footer">
                <button type="button" class="admin-btn admin-btn-secondary" onclick="closeModal('resetPasswordModal')">取消</button>
                <button type="submit" name="reset_password" class="admin-btn admin-btn-warning">重置密码</button>
            </div>
        </form>
    </div>
</div>

<style>
.admin-modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.admin-modal-content {
    background-color: #fff;
    margin: 5% auto;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.admin-modal-header {
    padding: 20px;
    border-bottom: 1px solid #e1e8ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.admin-modal-close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
}

.admin-modal-close:hover {
    color: #333;
}

.admin-modal-body {
    padding: 20px;
}

.admin-modal-footer {
    padding: 20px;
    border-top: 1px solid #e1e8ed;
    text-align: right;
}

.admin-modal-footer .admin-btn {
    margin-left: 10px;
}

.admin-form-group {
    margin-bottom: 15px;
}

.admin-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
}

.admin-form-group input,
.admin-form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #e1e8ed;
    border-radius: 4px;
    font-size: 14px;
}

.admin-form-group input:focus,
.admin-form-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.admin-actions-bar {
    display: flex;
    gap: 10px;
    align-items: center;
}
</style>

<script>
function validateAdminForm() {
    const username = document.querySelector('input[name="username"]').value.trim();
    const password = document.querySelector('input[name="password"]').value;
    const confirmPassword = document.querySelector('input[name="confirm_password"]').value;
    const email = document.querySelector('input[name="email"]').value.trim();

    if (!username || !password || !confirmPassword) {
        alert('请填写所有必填字段');
        return false;
    }

    if (password !== confirmPassword) {
        alert('两次输入的密码不一致');
        return false;
    }

    if (password.length < 6) {
        alert('密码长度至少6位');
        return false;
    }

    if (email && !isValidEmail(email)) {
        alert('邮箱格式不正确');
        return false;
    }

    return true;
}

function showChangePasswordModal() {
    document.getElementById('changePasswordModal').style.display = 'block';
}

function showEditModal(id, username, email, name, status) {
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_username').value = username;
    document.getElementById('edit_email').value = email || '';
    document.getElementById('edit_name').value = name || '';
    document.getElementById('edit_status').value = status;
    document.getElementById('editAdminModal').style.display = 'block';
}

function showResetPasswordModal(id, username) {
    document.getElementById('reset_id').value = id;
    document.getElementById('reset_username').textContent = username;
    document.getElementById('resetPasswordModal').style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    // 清空表单
    const form = document.querySelector('#' + modalId + ' form');
    if (form) {
        form.reset();
    }
}

function validateChangePasswordForm() {
    const currentPassword = document.querySelector('input[name="current_password"]').value;
    const newPassword = document.querySelector('input[name="new_password"]').value;
    const confirmPassword = document.querySelector('input[name="confirm_new_password"]').value;

    if (!currentPassword || !newPassword || !confirmPassword) {
        alert('请填写所有密码字段');
        return false;
    }

    if (newPassword !== confirmPassword) {
        alert('新密码两次输入不一致');
        return false;
    }

    if (newPassword.length < 6) {
        alert('新密码长度至少6位');
        return false;
    }

    return true;
}

function validateEditAdminForm() {
    const username = document.querySelector('input[name="edit_username"]').value.trim();
    const email = document.querySelector('input[name="edit_email"]').value.trim();

    if (!username) {
        alert('用户名不能为空');
        return false;
    }

    if (email && !isValidEmail(email)) {
        alert('邮箱格式不正确');
        return false;
    }

    return true;
}

function validateResetPasswordForm() {
    const newPassword = document.querySelector('input[name="reset_new_password"]').value;
    const confirmPassword = document.querySelector('input[name="reset_confirm_password"]').value;

    if (!newPassword || !confirmPassword) {
        alert('请填写新密码');
        return false;
    }

    if (newPassword !== confirmPassword) {
        alert('两次输入的密码不一致');
        return false;
    }

    if (newPassword.length < 6) {
        alert('密码长度至少6位');
        return false;
    }

    return true;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modals = document.querySelectorAll('.admin-modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}
</script>

<?php render_admin_footer(); ?>
