# TabBar 图标解决方案

## ✅ 当前已修复方案

**主要方案：在线图标（已配置）**
目前 `pages.json` 已配置使用 Icons8 提供的在线图标：

### 首页图标
- 未选中：https://img.icons8.com/ios/50/7A7E83/home--v1.png
- 选中：https://img.icons8.com/ios-filled/50/007bff/home--v1.png

### 课程图标
- 未选中：https://img.icons8.com/ios/50/7A7E83/video.png
- 选中：https://img.icons8.com/ios-filled/50/007bff/video.png

### 个人图标
- 未选中：https://img.icons8.com/ios/50/7A7E83/user.png
- 选中：https://img.icons8.com/ios-filled/50/007bff/user.png

## 🔧 快速修复步骤

1. **无需任何操作** - 在线图标已配置完成
2. **重新编译项目** - 图标将自动显示
3. **如果网络问题** - 使用下方备用方案

## 🔄 备用方案

### 方案1: 本地图标（推荐备用）

**使用方法：**
1. 打开 `static/tabbar/download-icons.html` 下载图标
2. 将 `pages-local-icons.json` 重命名为 `pages.json`
3. 重新编译项目

**优点：** 加载速度快，无需网络
**缺点：** 增加包体积

### 方案2: uni-icons字体图标

**使用方法：**
1. 在页面中使用 `custom-tabbar` 组件
2. 隐藏原生tabBar：在pages.json中设置 `"tabBar": { "custom": true }`

**优点：** 体积小，样式统一
**缺点：** 需要自定义组件

### 方案3: 其他在线图标库

**Feather Icons:**
```json
"iconPath": "https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/icons/home.svg"
```

**Material Icons:**
```json
"iconPath": "https://fonts.gstatic.com/s/i/materialicons/home/<USER>/24px.svg"
```

## 图标设计规范

### 尺寸要求
- 推荐尺寸：81x81px
- 最小尺寸：40x40px
- 最大尺寸：120x120px

### 颜色规范
- 未选中状态：#7A7E83（灰色）
- 选中状态：#007bff（蓝色）
- 背景：透明

### 设计风格
- 简洁明了
- 线条清晰
- 易于识别
- 符合功能含义

## 注意事项

1. **网络图标**：使用在线图标需要网络连接，可能影响加载速度
2. **本地图标**：本地图标加载更快，但需要增加包体积
3. **版权问题**：确保使用的图标有合法的使用权限
4. **兼容性**：确保图标在不同设备上显示正常

## 推荐资源

### 免费图标库
- [Icons8](https://icons8.com/) - 提供多种风格的图标
- [Feather Icons](https://feathericons.com/) - 简洁的线性图标
- [Heroicons](https://heroicons.com/) - Tailwind CSS 团队制作
- [Lucide](https://lucide.dev/) - Feather Icons 的继承者

### 中文图标库
- [Iconfont](https://www.iconfont.cn/) - 阿里巴巴图标库
- [IconPark](https://iconpark.oceanengine.com/) - 字节跳动图标库

### 设计工具
- [Figma](https://www.figma.com/) - 在线设计工具
- [Sketch](https://www.sketch.com/) - Mac 设计工具
- [Adobe Illustrator](https://www.adobe.com/products/illustrator.html) - 专业矢量设计
