/**
 * 用户认证API服务
 * 处理登录、注册、用户信息等认证相关接口
 */

import request from '../utils/request.js';
import { setAccessToken, setRefreshToken, setUserInfo } from '../utils/storage.js';

/**
 * 获取设备信息
 * @returns {Object} 设备信息
 */
function getDeviceInfo() {
	try {
		// 使用新的API获取设备信息
		const deviceInfo = uni.getDeviceInfo ? uni.getDeviceInfo() : {};
		const appBaseInfo = uni.getAppBaseInfo ? uni.getAppBaseInfo() : {};
		const windowInfo = uni.getWindowInfo ? uni.getWindowInfo() : {};

		return {
			platform: deviceInfo.platform || '',
			model: deviceInfo.model || '',
			system: deviceInfo.system || '',
			version: appBaseInfo.version || '',
			...deviceInfo,
			...appBaseInfo,
			...windowInfo
		};
	} catch (error) {
		console.warn('获取设备信息失败，使用默认值:', error);
		return {
			platform: 'unknown',
			model: 'unknown',
			system: 'unknown',
			version: 'unknown'
		};
	}
}

/**
 * 传统登录（用户名/邮箱 + 密码）
 * @param {String} credential 用户名或邮箱
 * @param {String} password 密码
 * @param {Object} deviceInfo 设备信息
 * @returns {Promise} 登录结果
 */
export function loginWithPassword(credential, password, deviceInfo = {}) {
	return new Promise((resolve, reject) => {
		const systemInfo = getDeviceInfo();
		request.post('auth-login.php', {
			credential,
			password,
			device_info: {
				platform: systemInfo.platform,
				model: systemInfo.model,
				system: systemInfo.system,
				version: systemInfo.version,
				...deviceInfo
			}
		}).then(response => {
			if (response.code === 200) {
				const { access_token, refresh_token, user } = response.data;
				
				// 保存token和用户信息
				setAccessToken(access_token);
				setRefreshToken(refresh_token);
				setUserInfo(user);
				
				resolve(response);
			} else {
				reject(response);
			}
		}).catch(reject);
	});
}

/**
 * 微信登录
 * @param {String} code 微信授权码
 * @param {Object} userInfo 微信用户信息
 * @param {Object} deviceInfo 设备信息
 * @returns {Promise} 登录结果
 */
export function loginWithWechat(code, userInfo = {}, deviceInfo = {}) {
	return new Promise(async (resolve, reject) => {
		const systemInfo = getDeviceInfo();

		console.log('开始微信登录请求:', {
			code: code,
			userInfo: userInfo,
			deviceInfo: deviceInfo,
			systemInfo: systemInfo
		});

		// 先测试简单的GET请求
		console.log('先测试request.get...');
		try {
			const testResponse = await request.get('test.php');
			console.log('request.get测试成功:', testResponse);
		} catch (testError) {
			console.error('request.get测试失败:', testError);
		}

		request.post('auth-wechat.php', {
			code,
			userInfo,
			device_info: {
				platform: systemInfo.platform,
				model: systemInfo.model,
				system: systemInfo.system,
				version: systemInfo.version,
				...deviceInfo
			}
		}).then(response => {
			console.log('微信登录响应:', response);

			if (response.code === 200 || response.code === 202) {
				const { access_token, refresh_token, user, openid } = response.data;

				// 保存token和用户信息
				setAccessToken(access_token);
				setRefreshToken(refresh_token);
				setUserInfo(user);
				
				// 保存openid用于支付
				if (openid) {
					uni.setStorageSync('user_openid', openid);
					console.log('已保存用户openid:', openid);
				}

				resolve(response);
			} else {
				reject(response);
			}
		}).catch(error => {
			console.error('微信登录请求失败:', error);
			reject(error);
		});
	});
}

/**
 * 用户注册
 * @param {Object} userData 用户数据
 * @returns {Promise} 注册结果
 */
export function register(userData) {
	const systemInfo = getDeviceInfo();
	return request.post('auth-register.php', {
		name: userData.name,
		email: userData.email,
		username: userData.username,
		password: userData.password,
		phone: userData.phone || '',
		nickname: userData.nickname || userData.name,
		device_info: {
			platform: systemInfo.platform,
			model: systemInfo.model,
			system: systemInfo.system,
			version: systemInfo.version
		}
	});
}

/**
 * 获取当前用户信息
 * @returns {Promise} 用户信息
 */
export function getCurrentUser() {
	return request.get('auth-me.php');
}

/**
 * 更新用户信息
 * @param {Object} userData 用户数据
 * @returns {Promise} 更新结果
 */
export function updateUserInfo(userData) {
	return request.put('auth-me.php', userData);
}

/**
 * 修改密码
 * @param {Object|String} passwordData 密码数据对象或旧密码（兼容旧版本）
 * @param {String} newPassword 新密码（仅在使用旧版本参数时需要）
 * @returns {Promise} 修改结果
 */
export function changePassword(passwordData, newPassword = null) {
	let requestData;

	// 兼容新旧两种调用方式
	if (typeof passwordData === 'object' && passwordData !== null) {
		// 新方式：传递对象
		requestData = passwordData;
	} else {
		// 旧方式：传递两个字符串参数（向后兼容）
		requestData = {
			old_password: passwordData,
			new_password: newPassword
		};
	}

	return request.post('auth-change-password.php', requestData);
}

/**
 * 退出登录
 * @returns {Promise} 退出结果
 */
export function logout() {
	return request.post('auth-logout.php');
}

/**
 * 刷新访问令牌
 * @param {String} refreshToken 刷新令牌
 * @returns {Promise} 新的访问令牌
 */
export function refreshAccessToken(refreshToken) {
	return request.post('auth-refresh.php', {
		refresh_token: refreshToken
	});
}

/**
 * 微信授权获取用户信息
 * 注意：微信小程序已不再支持直接获取用户头像昵称，需要用户主动授权
 * @returns {Promise} 微信用户信息
 */
export function getWechatUserInfo() {
	return new Promise((resolve, reject) => {
		// 微信小程序新版本不再支持直接获取用户信息
		// 返回空对象，让后端使用默认头像
		console.log('微信小程序不再支持直接获取用户信息，使用默认头像');
		resolve({
			nickName: '微信用户',
			avatarUrl: '', // 空头像，后端会使用默认头像
			gender: 0
		});
	});
}

/**
 * 获取微信用户头像（新版本API）
 * 需要用户主动点击按钮触发
 * @returns {Promise} 用户头像信息
 */
export function chooseWechatAvatar() {
	return new Promise((resolve, reject) => {
		// 使用头像昵称填写能力
		uni.chooseAvatar({
			success: (res) => {
				resolve({
					avatarUrl: res.avatarUrl
				});
			},
			fail: reject
		});
	});
}

/**
 * 微信登录获取code
 * @returns {Promise} 微信授权码
 */
export function getWechatLoginCode() {
	return new Promise((resolve, reject) => {
		uni.login({
			provider: 'weixin',
			success: (res) => {
				if (res.code) {
					resolve(res.code);
				} else {
					reject({ code: -1, message: '获取微信授权码失败' });
				}
			},
			fail: reject
		});
	});
}

/**
 * 检查登录状态
 * @returns {Promise} 登录状态检查结果
 */
export function checkLoginStatus() {
	return getCurrentUser();
}

/**
 * 绑定微信账号
 * @param {String} code 微信授权码
 * @param {Object} userInfo 微信用户信息
 * @returns {Promise} 绑定结果
 */
export function bindWechatAccount(code, userInfo) {
	return request.post('auth-bind-wechat.php', {
		code,
		userInfo
	});
}

/**
 * 解绑微信账号
 * @returns {Promise} 解绑结果
 */
export function unbindWechatAccount() {
	return request.post('auth-unbind-wechat.php');
}

/**
 * 发送验证码
 * @param {String} phone 手机号
 * @param {String} type 验证码类型
 * @returns {Promise} 发送结果
 */
export function sendVerificationCode(phone, type = 'login') {
	return request.post('auth-send-code.php', {
		phone,
		type
	});
}

/**
 * 手机号验证码登录
 * @param {String} phone 手机号
 * @param {String} code 验证码
 * @param {Object} deviceInfo 设备信息
 * @returns {Promise} 登录结果
 */
export function loginWithPhone(phone, code, deviceInfo = {}) {
	return new Promise((resolve, reject) => {
		request.post('auth-phone-login.php', {
			phone,
			code,
			device_info: {
				platform: uni.getSystemInfoSync().platform,
				model: uni.getSystemInfoSync().model,
				system: uni.getSystemInfoSync().system,
				version: uni.getSystemInfoSync().version,
				...deviceInfo
			}
		}).then(response => {
			if (response.code === 200) {
				const { access_token, refresh_token, user } = response.data;
				
				// 保存token和用户信息
				setAccessToken(access_token);
				setRefreshToken(refresh_token);
				setUserInfo(user);
				
				resolve(response);
			} else {
				reject(response);
			}
		}).catch(reject);
	});
}

/**
 * 重置密码
 * @param {String} phone 手机号
 * @param {String} code 验证码
 * @param {String} newPassword 新密码
 * @returns {Promise} 重置结果
 */
export function resetPassword(phone, code, newPassword) {
	return request.post('auth-reset-password.php', {
		phone,
		code,
		new_password: newPassword
	});
}

/**
 * 更新用户资料（昵称等）
 * @param {Object} profileData 用户资料数据
 * @returns {Promise} 更新结果
 */
export function updateUserProfile(profileData) {
	return request.put('auth-profile.php', profileData);
}
