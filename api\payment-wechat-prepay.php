<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);

require_once '../includes/db.php';
require_once '../includes/auth.php';

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 验证用户身份
$user = authenticate_user();
if (!$user) {
    http_response_code(401);
    echo json_encode([
        'code' => 401,
        'message' => '未授权访问',
        'data' => null
    ]);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
if (!isset($input['order_id']) || !isset($input['openid'])) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少必需参数',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查是否是临时openid（用于测试账号密码登录用户）
$openid = $input['openid'];
if (strpos($openid, 'temp_openid_') === 0) {
    echo json_encode([
        'code' => 400,
        'message' => '当前登录方式不支持微信支付',
        'data' => null,
        'user_message' => '账号密码登录用户暂不支持微信支付功能，请使用微信登录后再试',
        'suggestion' => '您可以：1. 使用微信登录 2. 联系客服处理订单 3. 等待其他支付方式上线'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $user_id = $user['id'];
    $order_id = intval($input['order_id']);
    $openid = trim($input['openid']);
    
    // 验证订单
    $stmt = $conn->prepare("
        SELECT * FROM orders 
        WHERE id = ? AND user_id = ? AND order_status = 'pending' AND payment_status = 'unpaid'
    ");
    $stmt->bind_param("ii", $order_id, $user_id);
    $stmt->execute();
    $order = $stmt->get_result()->fetch_assoc();
    
    if (!$order) {
        echo json_encode([
            'code' => 404,
            'message' => '订单不存在或状态不正确',
            'data' => null
        ]);
        exit;
    }
    
    // 检查订单是否过期
    if (strtotime($order['expire_time']) < time()) {
        // 更新订单状态为过期
        $stmt = $conn->prepare("UPDATE orders SET order_status = 'expired' WHERE id = ?");
        $stmt->bind_param("i", $order_id);
        $stmt->execute();
        
        echo json_encode([
            'code' => 400,
            'message' => '订单已过期',
            'data' => null
        ]);
        exit;
    }
    
    // 获取微信支付配置
    $stmt = $conn->prepare("
        SELECT setting_key, setting_value 
        FROM settings 
        WHERE setting_key IN ('wechat_pay_enabled', 'wechat_pay_app_id', 'wechat_pay_mch_id', 
                              'wechat_pay_api_key', 'wechat_pay_notify_url')
    ");
    $stmt->execute();
    $settings_result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    $settings = [];
    foreach ($settings_result as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
    
    // 检查微信支付是否启用
    if (empty($settings['wechat_pay_enabled']) || $settings['wechat_pay_enabled'] !== '1') {
        echo json_encode([
            'code' => 400,
            'message' => '微信支付功能暂未开放，请联系客服或稍后再试',
            'data' => null,
            'user_message' => '支付功能正在维护中，请稍后再试或联系客服'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 检查必需的配置
    $required_settings = ['wechat_pay_app_id', 'wechat_pay_mch_id', 'wechat_pay_api_key'];
    $missing_settings = [];

    foreach ($required_settings as $key) {
        if (empty($settings[$key])) {
            $missing_settings[] = $key;
        }
    }

    if (!empty($missing_settings)) {
        echo json_encode([
            'code' => 500,
            'message' => '支付系统配置异常，请联系客服处理',
            'data' => null,
            'user_message' => '支付功能暂时不可用，请联系客服或稍后再试',
            'admin_debug' => [
                'missing_settings' => $missing_settings,
                'available_settings' => array_keys($settings)
            ]
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 验证AppID格式
    if (!preg_match('/^wx[a-f0-9]{16}$/', $settings['wechat_pay_app_id'])) {
        echo json_encode([
            'code' => 500,
            'message' => '微信AppID格式不正确',
            'data' => null,
            'debug' => [
                'app_id' => $settings['wechat_pay_app_id']
            ]
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 验证商户号格式
    if (!preg_match('/^\d{10}$/', $settings['wechat_pay_mch_id'])) {
        echo json_encode([
            'code' => 500,
            'message' => '微信支付商户号格式不正确（应为10位数字）',
            'data' => null,
            'debug' => [
                'mch_id' => $settings['wechat_pay_mch_id']
            ]
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 生成支付流水号
    $payment_no = 'PAY' . date('YmdHis') . sprintf('%06d', $user_id) . sprintf('%04d', rand(1000, 9999));
    
    // 创建支付记录
    $stmt = $conn->prepare("
        INSERT INTO payments (payment_no, order_id, user_id, payment_method, amount, 
                             payment_status, created_at) 
        VALUES (?, ?, ?, 'wechat', ?, 'pending', NOW())
    ");
    $stmt->bind_param("siid", $payment_no, $order_id, $user_id, $order['actual_amount']);
    $stmt->execute();
    $payment_id = $conn->insert_id;
    
    // 准备微信支付参数
    $wechat_params = [
        'appid' => $settings['wechat_pay_app_id'],
        'mch_id' => $settings['wechat_pay_mch_id'],
        'nonce_str' => generate_nonce_str(),
        'body' => '课程购买-订单号:' . $order['order_no'],
        'out_trade_no' => $payment_no,
        'total_fee' => intval($order['actual_amount'] * 100), // 转换为分
        'spbill_create_ip' => get_client_ip(),
        'notify_url' => $settings['wechat_pay_notify_url'] ?: 'https://your-domain.com/api/payment-wechat-notify.php',
        'trade_type' => 'JSAPI',
        'openid' => $openid
    ];
    
    // 生成签名
    $wechat_params['sign'] = generate_wechat_sign($wechat_params, $settings['wechat_pay_api_key']);
    
    // 调用微信统一下单API
    $xml_data = array_to_xml($wechat_params);
    $response = call_wechat_api('https://api.mch.weixin.qq.com/pay/unifiedorder', $xml_data);
    
    if (!$response) {
        throw new Exception('调用微信支付API失败');
    }
    
    $result = xml_to_array($response);
    
    if ($result['return_code'] !== 'SUCCESS') {
        throw new Exception('微信支付API返回错误: ' . ($result['return_msg'] ?? '未知错误'));
    }
    
    if ($result['result_code'] !== 'SUCCESS') {
        throw new Exception('微信支付失败: ' . ($result['err_code_des'] ?? $result['err_code'] ?? '未知错误'));
    }
    
    // 更新支付记录
    $stmt = $conn->prepare("
        UPDATE payments 
        SET prepay_id = ?, third_party_no = ? 
        WHERE id = ?
    ");
    $stmt->bind_param("ssi", $result['prepay_id'], $result['prepay_id'], $payment_id);
    $stmt->execute();
    
    // 生成小程序支付参数
    $timeStamp = time();
    $nonceStr = generate_nonce_str();
    $package = 'prepay_id=' . $result['prepay_id'];
    
    $pay_params = [
        'appId' => $settings['wechat_pay_app_id'],
        'timeStamp' => (string)$timeStamp,
        'nonceStr' => $nonceStr,
        'package' => $package,
        'signType' => 'MD5'
    ];
    
    $pay_params['paySign'] = generate_wechat_sign($pay_params, $settings['wechat_pay_api_key']);
    
    echo json_encode([
        'code' => 200,
        'message' => '预支付订单创建成功',
        'data' => [
            'payment_id' => $payment_id,
            'payment_no' => $payment_no,
            'pay_params' => $pay_params
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage(),
        'data' => null
    ]);
}

/**
 * 生成随机字符串
 */
function generate_nonce_str($length = 32) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $str = '';
    for ($i = 0; $i < $length; $i++) {
        $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
    }
    return $str;
}

/**
 * 生成微信签名
 */
function generate_wechat_sign($params, $key) {
    ksort($params);
    $string = '';
    foreach ($params as $k => $v) {
        if ($k !== 'sign' && $v !== '' && $v !== null) {
            $string .= $k . '=' . $v . '&';
        }
    }
    $string .= 'key=' . $key;
    return strtoupper(md5($string));
}

/**
 * 数组转XML
 */
function array_to_xml($array) {
    $xml = '<xml>';
    foreach ($array as $key => $val) {
        $xml .= '<' . $key . '><![CDATA[' . $val . ']]></' . $key . '>';
    }
    $xml .= '</xml>';
    return $xml;
}

/**
 * XML转数组
 */
function xml_to_array($xml) {
    return json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
}

/**
 * 调用微信API
 */
function call_wechat_api($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception('CURL错误: ' . $error);
    }
    
    return $response;
}

/**
 * 获取客户端IP
 */
function get_client_ip() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}
?>
