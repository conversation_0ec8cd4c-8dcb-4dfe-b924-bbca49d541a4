/* ========================================
   后台管理系统 UI 设计规范样式表
   ======================================== */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

/* 主布局容器 */
.admin-layout {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 */
.admin-sidebar {
    width: 250px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.admin-sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.admin-sidebar-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 5px;
}

.admin-sidebar-subtitle {
    font-size: 12px;
    opacity: 0.8;
}

/* 侧边栏导航 */
.admin-nav {
    padding: 20px 0;
}

.admin-nav-item {
    display: block;
    padding: 12px 20px;
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    position: relative;
}

.admin-nav-item:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
    border-left-color: #fff;
}

.admin-nav-item.active {
    background-color: rgba(255,255,255,0.15);
    color: white;
    border-left-color: #fff;
}

.admin-nav-item i {
    width: 20px;
    margin-right: 10px;
    text-align: center;
}

/* 主内容区域 */
.admin-main {
    flex: 1;
    margin-left: 250px;
    background-color: #f5f7fa;
}

/* 顶部导航栏 */
.admin-header {
    background: white;
    padding: 15px 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 999;
}

.admin-header-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
}

.admin-header-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
}

.admin-logout-btn {
    background: #e74c3c;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.admin-logout-btn:hover {
    background: #c0392b;
    color: white;
    text-decoration: none;
}

/* 内容区域 */
.admin-content {
    padding: 30px;
}

/* 卡片容器 */
.admin-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 30px;
    overflow: hidden;
}

.admin-card-header {
    padding: 20px 30px;
    border-bottom: 1px solid #eee;
    background: #fafbfc;
}

.admin-card-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.admin-card-body {
    padding: 30px;
}

/* 表单样式 */
.admin-form {
    max-width: 600px;
}

.admin-form-group {
    margin-bottom: 20px;
}

.admin-form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.admin-form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.admin-form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.admin-form-row {
    display: flex;
    gap: 20px;
}

.admin-form-col {
    flex: 1;
}

/* 按钮样式 */
.admin-btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.admin-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.admin-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
}

.admin-btn-success {
    background: #27ae60;
    color: white;
}

.admin-btn-success:hover {
    background: #229954;
    color: white;
    text-decoration: none;
}

.admin-btn-danger {
    background: #e74c3c;
    color: white;
}

.admin-btn-danger:hover {
    background: #c0392b;
    color: white;
    text-decoration: none;
}

.admin-btn-secondary {
    background: #95a5a6;
    color: white;
}

.admin-btn-secondary:hover {
    background: #7f8c8d;
    color: white;
    text-decoration: none;
}

.admin-btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

/* 表格样式 */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.admin-table th {
    background: #f8f9fa;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #555;
    border-bottom: 2px solid #e9ecef;
}

.admin-table td {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
}

.admin-table tr:hover {
    background-color: #f8f9fa;
}

.admin-table tr:last-child td {
    border-bottom: none;
}

/* 操作按钮组 */
.admin-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 状态标签 */
.admin-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.admin-badge-success {
    background: #d4edda;
    color: #155724;
}

.admin-badge-danger {
    background: #f8d7da;
    color: #721c24;
}

.admin-badge-warning {
    background: #fff3cd;
    color: #856404;
}

.admin-badge-info {
    background: #d1ecf1;
    color: #0c5460;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .admin-sidebar.active {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .admin-header {
        padding: 15px 20px;
    }
    
    .admin-content {
        padding: 20px;
    }
    
    .admin-card-body {
        padding: 20px;
    }
    
    .admin-form-row {
        flex-direction: column;
        gap: 0;
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }
.mt-20 { margin-top: 20px; }
.p-20 { padding: 20px; }
