<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

$order_id = intval($_GET['id'] ?? 0);
if ($order_id <= 0) {
    header('Location: orders.php');
    exit;
}

// 获取订单详情
$stmt = $conn->prepare("
    SELECT 
        o.*,
        u.name as user_name,
        u.email as user_email,
        u.phone as user_phone
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    WHERE o.id = ?
");
$stmt->bind_param("i", $order_id);
$stmt->execute();
$order = $stmt->get_result()->fetch_assoc();

if (!$order) {
    header('Location: orders.php');
    exit;
}

// 获取订单商品
$stmt = $conn->prepare("
    SELECT 
        oi.*,
        c.thumbnail,
        c.cover_image,
        c.status as course_status
    FROM order_items oi
    LEFT JOIN courses c ON oi.course_id = c.id
    WHERE oi.order_id = ?
    ORDER BY oi.id
");
$stmt->bind_param("i", $order_id);
$stmt->execute();
$items = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// 获取支付记录
$stmt = $conn->prepare("
    SELECT *
    FROM payments
    WHERE order_id = ?
    ORDER BY created_at DESC
");
$stmt->bind_param("i", $order_id);
$stmt->execute();
$payments = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// 获取退款记录
$stmt = $conn->prepare("
    SELECT 
        r.*,
        a.username as processed_by_name
    FROM refunds r
    LEFT JOIN admins a ON r.processed_by = a.id
    WHERE r.order_id = ?
    ORDER BY r.created_at DESC
");
$stmt->bind_param("i", $order_id);
$stmt->execute();
$refunds = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// 渲染页面头部
render_admin_header('订单详情 - ' . $order['order_no'], 'orders');
?>

<!-- 订单基本信息 -->
<?php render_card_start('订单信息'); ?>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
        <div>
            <h4 style="margin: 0 0 15px 0; color: #2c3e50;">基本信息</h4>
            <table class="admin-info-table">
                <tr>
                    <td><strong>订单号：</strong></td>
                    <td><?php echo htmlspecialchars($order['order_no']); ?></td>
                </tr>
                <tr>
                    <td><strong>订单状态：</strong></td>
                    <td>
                        <?php 
                        $status_colors = [
                            'pending' => 'warning',
                            'paid' => 'success', 
                            'cancelled' => 'secondary',
                            'refunded' => 'info',
                            'expired' => 'danger'
                        ];
                        $status_texts = [
                            'pending' => '待支付',
                            'paid' => '已支付',
                            'cancelled' => '已取消', 
                            'refunded' => '已退款',
                            'expired' => '已过期'
                        ];
                        $color = $status_colors[$order['order_status']] ?? 'secondary';
                        $text = $status_texts[$order['order_status']] ?? $order['order_status'];
                        ?>
                        <span class="admin-badge admin-badge-<?php echo $color; ?>">
                            <?php echo $text; ?>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>支付状态：</strong></td>
                    <td>
                        <?php 
                        $payment_colors = [
                            'unpaid' => 'warning',
                            'paid' => 'success',
                            'refunding' => 'info', 
                            'refunded' => 'secondary',
                            'failed' => 'danger'
                        ];
                        $payment_texts = [
                            'unpaid' => '未支付',
                            'paid' => '已支付',
                            'refunding' => '退款中',
                            'refunded' => '已退款', 
                            'failed' => '支付失败'
                        ];
                        $color = $payment_colors[$order['payment_status']] ?? 'secondary';
                        $text = $payment_texts[$order['payment_status']] ?? $order['payment_status'];
                        ?>
                        <span class="admin-badge admin-badge-<?php echo $color; ?>">
                            <?php echo $text; ?>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>支付方式：</strong></td>
                    <td>
                        <?php if ($order['payment_method']): ?>
                            <?php echo $order['payment_method'] === 'wechat' ? '微信支付' : $order['payment_method']; ?>
                        <?php else: ?>
                            <span class="text-muted">未支付</span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td><strong>创建时间：</strong></td>
                    <td><?php echo date('Y-m-d H:i:s', strtotime($order['created_at'])); ?></td>
                </tr>
                <tr>
                    <td><strong>过期时间：</strong></td>
                    <td><?php echo date('Y-m-d H:i:s', strtotime($order['expire_time'])); ?></td>
                </tr>
                <?php if ($order['payment_time']): ?>
                <tr>
                    <td><strong>支付时间：</strong></td>
                    <td><?php echo date('Y-m-d H:i:s', strtotime($order['payment_time'])); ?></td>
                </tr>
                <?php endif; ?>
            </table>
        </div>
        
        <div>
            <h4 style="margin: 0 0 15px 0; color: #2c3e50;">用户信息</h4>
            <table class="admin-info-table">
                <tr>
                    <td><strong>用户姓名：</strong></td>
                    <td><?php echo htmlspecialchars($order['user_name']); ?></td>
                </tr>
                <tr>
                    <td><strong>邮箱：</strong></td>
                    <td><?php echo htmlspecialchars($order['user_email']); ?></td>
                </tr>
                <?php if ($order['user_phone']): ?>
                <tr>
                    <td><strong>手机号：</strong></td>
                    <td><?php echo htmlspecialchars($order['user_phone']); ?></td>
                </tr>
                <?php endif; ?>
            </table>
        </div>
        
        <div>
            <h4 style="margin: 0 0 15px 0; color: #2c3e50;">金额信息</h4>
            <table class="admin-info-table">
                <tr>
                    <td><strong>商品总额：</strong></td>
                    <td>¥<?php echo number_format($order['total_amount'], 2); ?></td>
                </tr>
                <tr>
                    <td><strong>优惠金额：</strong></td>
                    <td>¥<?php echo number_format($order['discount_amount'], 2); ?></td>
                </tr>
                <tr>
                    <td><strong>实付金额：</strong></td>
                    <td><strong style="color: #e74c3c;">¥<?php echo number_format($order['actual_amount'], 2); ?></strong></td>
                </tr>
            </table>
        </div>
    </div>
    
    <?php if ($order['remark']): ?>
    <div style="margin-top: 20px;">
        <h4 style="margin: 0 0 10px 0; color: #2c3e50;">订单备注</h4>
        <p style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 0;">
            <?php echo nl2br(htmlspecialchars($order['remark'])); ?>
        </p>
    </div>
    <?php endif; ?>
<?php render_card_end(); ?>

<!-- 订单商品 -->
<?php render_card_start('订单商品'); ?>
    <?php if (empty($items)): ?>
        <div class="admin-empty-state">
            <p>暂无商品信息</p>
        </div>
    <?php else: ?>
        <div class="admin-table-container">
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>课程</th>
                        <th>单价</th>
                        <th>原价</th>
                        <th>数量</th>
                        <th>小计</th>
                        <th>课程状态</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($items as $item): ?>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <?php if ($item['thumbnail'] || $item['cover_image']): ?>
                                        <img src="<?php echo htmlspecialchars($item['thumbnail'] ?: $item['cover_image']); ?>" 
                                             alt="课程封面" style="width: 50px; height: 35px; object-fit: cover; border-radius: 4px;">
                                    <?php endif; ?>
                                    <div>
                                        <strong><?php echo htmlspecialchars($item['course_title']); ?></strong><br>
                                        <small>课程ID: <?php echo $item['course_id']; ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>¥<?php echo number_format($item['course_price'], 2); ?></td>
                            <td>¥<?php echo number_format($item['original_price'], 2); ?></td>
                            <td><?php echo $item['quantity']; ?></td>
                            <td><strong>¥<?php echo number_format($item['subtotal'], 2); ?></strong></td>
                            <td>
                                <?php if ($item['course_status'] === 'active'): ?>
                                    <span class="admin-badge admin-badge-success">正常</span>
                                <?php else: ?>
                                    <span class="admin-badge admin-badge-warning">已下架</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
<?php render_card_end(); ?>

<!-- 支付记录 -->
<?php if (!empty($payments)): ?>
<?php render_card_start('支付记录'); ?>
    <div class="admin-table-container">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>支付流水号</th>
                    <th>支付方式</th>
                    <th>支付金额</th>
                    <th>支付状态</th>
                    <th>第三方流水号</th>
                    <th>创建时间</th>
                    <th>支付时间</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($payments as $payment): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($payment['payment_no']); ?></td>
                        <td><?php echo $payment['payment_method'] === 'wechat' ? '微信支付' : $payment['payment_method']; ?></td>
                        <td>¥<?php echo number_format($payment['amount'], 2); ?></td>
                        <td>
                            <?php 
                            $colors = [
                                'pending' => 'warning',
                                'success' => 'success',
                                'failed' => 'danger',
                                'cancelled' => 'secondary'
                            ];
                            $texts = [
                                'pending' => '处理中',
                                'success' => '成功',
                                'failed' => '失败',
                                'cancelled' => '已取消'
                            ];
                            $color = $colors[$payment['payment_status']] ?? 'secondary';
                            $text = $texts[$payment['payment_status']] ?? $payment['payment_status'];
                            ?>
                            <span class="admin-badge admin-badge-<?php echo $color; ?>">
                                <?php echo $text; ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($payment['transaction_id']): ?>
                                <small><?php echo htmlspecialchars($payment['transaction_id']); ?></small>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td><small><?php echo date('Y-m-d H:i:s', strtotime($payment['created_at'])); ?></small></td>
                        <td>
                            <?php if ($payment['paid_at']): ?>
                                <small><?php echo date('Y-m-d H:i:s', strtotime($payment['paid_at'])); ?></small>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php render_card_end(); ?>
<?php endif; ?>

<!-- 退款记录 -->
<?php if (!empty($refunds)): ?>
<?php render_card_start('退款记录'); ?>
    <div class="admin-table-container">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>退款单号</th>
                    <th>退款金额</th>
                    <th>退款原因</th>
                    <th>退款状态</th>
                    <th>处理人</th>
                    <th>申请时间</th>
                    <th>处理时间</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($refunds as $refund): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($refund['refund_no']); ?></td>
                        <td>¥<?php echo number_format($refund['refund_amount'], 2); ?></td>
                        <td><?php echo htmlspecialchars($refund['refund_reason']); ?></td>
                        <td>
                            <?php 
                            $colors = [
                                'pending' => 'warning',
                                'processing' => 'info',
                                'success' => 'success',
                                'failed' => 'danger',
                                'cancelled' => 'secondary'
                            ];
                            $texts = [
                                'pending' => '待处理',
                                'processing' => '处理中',
                                'success' => '成功',
                                'failed' => '失败',
                                'cancelled' => '已取消'
                            ];
                            $color = $colors[$refund['refund_status']] ?? 'secondary';
                            $text = $texts[$refund['refund_status']] ?? $refund['refund_status'];
                            ?>
                            <span class="admin-badge admin-badge-<?php echo $color; ?>">
                                <?php echo $text; ?>
                            </span>
                        </td>
                        <td><?php echo htmlspecialchars($refund['processed_by_name'] ?? '系统'); ?></td>
                        <td><small><?php echo date('Y-m-d H:i:s', strtotime($refund['created_at'])); ?></small></td>
                        <td>
                            <?php if ($refund['processed_at']): ?>
                                <small><?php echo date('Y-m-d H:i:s', strtotime($refund['processed_at'])); ?></small>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php render_card_end(); ?>
<?php endif; ?>

<!-- 操作按钮 -->
<div style="margin: 20px 0; text-align: center;">
    <a href="orders.php" class="admin-btn admin-btn-secondary">返回订单列表</a>
    
    <?php if ($order['order_status'] === 'pending'): ?>
        <button onclick="cancelOrder(<?php echo $order['id']; ?>)" 
                class="admin-btn admin-btn-warning">取消订单</button>
    <?php endif; ?>
    
    <?php if ($order['order_status'] === 'paid' && $order['payment_status'] === 'paid'): ?>
        <button onclick="refundOrder(<?php echo $order['id']; ?>)" 
                class="admin-btn admin-btn-danger">申请退款</button>
    <?php endif; ?>
</div>

<script>
function cancelOrder(orderId) {
    if (confirm('确定要取消这个订单吗？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'orders.php';
        
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'cancel_order';
        
        const orderIdInput = document.createElement('input');
        orderIdInput.type = 'hidden';
        orderIdInput.name = 'order_id';
        orderIdInput.value = orderId;
        
        form.appendChild(actionInput);
        form.appendChild(orderIdInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function refundOrder(orderId) {
    const reason = prompt('请输入退款原因：');
    if (reason && reason.trim()) {
        if (confirm('确定要退款这个订单吗？\n退款原因：' + reason)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'orders.php';
            
            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'refund_order';
            
            const orderIdInput = document.createElement('input');
            orderIdInput.type = 'hidden';
            orderIdInput.name = 'order_id';
            orderIdInput.value = orderId;
            
            const reasonInput = document.createElement('input');
            reasonInput.type = 'hidden';
            reasonInput.name = 'refund_reason';
            reasonInput.value = reason.trim();
            
            form.appendChild(actionInput);
            form.appendChild(orderIdInput);
            form.appendChild(reasonInput);
            document.body.appendChild(form);
            form.submit();
        }
    }
}
</script>

<?php render_admin_footer(); ?>
