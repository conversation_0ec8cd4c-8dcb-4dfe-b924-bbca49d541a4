<?php
/**
 * 模拟支付API
 * 用于测试支付流程，不需要真实的微信支付配置
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);

require_once '../includes/db.php';
require_once '../includes/auth.php';

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 验证用户身份
$user = authenticate_user();
if (!$user) {
    http_response_code(401);
    echo json_encode([
        'code' => 401,
        'message' => '未授权访问',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取请求数据
$raw_input = file_get_contents('php://input');
$input = json_decode($raw_input, true);

// 调试信息
error_log('Mock payment request - User ID: ' . $user['id'] . ', Raw input: ' . $raw_input);

// 检查JSON解析是否成功
if (json_last_error() !== JSON_ERROR_NONE) {
    echo json_encode([
        'code' => 400,
        'message' => 'JSON数据格式错误: ' . json_last_error_msg(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 验证必需参数
if (!isset($input['order_id'])) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少订单ID参数',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $order_id = intval($input['order_id']);
    $user_id = $user['id'];
    
    // 查询订单信息
    $stmt = $conn->prepare("
        SELECT id, order_no, user_id, total_amount, actual_amount, order_status, 
               payment_status, expire_time, created_at
        FROM orders 
        WHERE id = ? AND user_id = ?
    ");
    $stmt->bind_param("ii", $order_id, $user_id);
    $stmt->execute();
    $order = $stmt->get_result()->fetch_assoc();
    
    if (!$order) {
        echo json_encode([
            'code' => 404,
            'message' => '订单不存在',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查订单状态
    if ($order['order_status'] !== 'pending') {
        echo json_encode([
            'code' => 400,
            'message' => '订单状态不正确，无法支付',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if ($order['payment_status'] !== 'unpaid') {
        echo json_encode([
            'code' => 400,
            'message' => '订单已支付或支付状态异常',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查订单是否过期
    if (strtotime($order['expire_time']) < time()) {
        // 更新订单状态为过期
        $stmt = $conn->prepare("UPDATE orders SET order_status = 'expired' WHERE id = ?");
        $stmt->bind_param("i", $order_id);
        $stmt->execute();
        
        echo json_encode([
            'code' => 400,
            'message' => '订单已过期',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 开始事务
    $conn->autocommit(false);
    $conn->begin_transaction();
    
    // 生成支付流水号
    $payment_no = 'MOCK' . date('YmdHis') . sprintf('%06d', $user_id) . sprintf('%04d', rand(1000, 9999));
    
    // 创建支付记录
    $stmt = $conn->prepare("
        INSERT INTO payments (payment_no, order_id, user_id, payment_method, amount, 
                             payment_status, transaction_id, paid_at, created_at) 
        VALUES (?, ?, ?, 'mock', ?, 'success', ?, NOW(), NOW())
    ");
    $mock_transaction_id = 'MOCK_' . time() . '_' . rand(10000, 99999);
    $stmt->bind_param("siids", $payment_no, $order_id, $user_id, $order['actual_amount'], $mock_transaction_id);
    $stmt->execute();
    $payment_id = $conn->insert_id;
    
    // 更新订单状态
    $stmt = $conn->prepare("
        UPDATE orders 
        SET order_status = 'paid', payment_status = 'paid', payment_method = 'mock', payment_time = NOW() 
        WHERE id = ?
    ");
    $stmt->bind_param("i", $order_id);
    $stmt->execute();
    
    // 获取订单商品
    $stmt = $conn->prepare("SELECT course_id FROM order_items WHERE order_id = ?");
    $stmt->bind_param("i", $order_id);
    $stmt->execute();
    $order_items = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // 为用户分配课程
    foreach ($order_items as $item) {
        $course_id = $item['course_id'];
        
        // 检查用户是否已经拥有该课程
        $check_stmt = $conn->prepare("
            SELECT id FROM user_courses 
            WHERE user_id = ? AND course_id = ?
        ");
        $check_stmt->bind_param("ii", $user_id, $course_id);
        $check_stmt->execute();
        $existing = $check_stmt->get_result()->fetch_assoc();
        
        if (!$existing) {
            // 分配课程给用户
            $assign_stmt = $conn->prepare("
                INSERT INTO user_courses (user_id, course_id, assigned_by, order_id, purchase_type, purchase_price, status) 
                VALUES (?, ?, 1, ?, 'purchased', ?, 'active')
            ");
            $assign_stmt->bind_param("iiid", $user_id, $course_id, $order_id, $order['actual_amount']);
            $assign_stmt->execute();
        } else {
            // 更新现有记录
            $update_stmt = $conn->prepare("
                UPDATE user_courses 
                SET status = 'active', order_id = ?, purchase_type = 'purchased', purchase_price = ? 
                WHERE user_id = ? AND course_id = ?
            ");
            $update_stmt->bind_param("idii", $order_id, $order['actual_amount'], $user_id, $course_id);
            $update_stmt->execute();
        }
    }
    
    $conn->commit();
    $conn->autocommit(true);
    
    // 返回成功响应
    echo json_encode([
        'code' => 200,
        'message' => '模拟支付成功',
        'data' => [
            'payment_id' => $payment_id,
            'payment_no' => $payment_no,
            'transaction_id' => $mock_transaction_id,
            'amount' => $order['actual_amount'],
            'order_id' => $order_id,
            'order_no' => $order['order_no'],
            'payment_method' => 'mock',
            'paid_at' => date('Y-m-d H:i:s'),
            'message' => '这是模拟支付，仅用于测试。在生产环境中请配置真实的微信支付。'
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // 记录错误日志
    error_log('Mock payment error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
    
    // 返回友好的错误信息
    echo json_encode([
        'code' => 500,
        'message' => '模拟支付失败: ' . $e->getMessage(),
        'data' => null,
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Error $e) {
    // 捕获PHP致命错误
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    error_log('PHP Fatal Error in mock payment: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
    
    echo json_encode([
        'code' => 500,
        'message' => '系统错误，请稍后重试',
        'data' => null,
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ], JSON_UNESCAPED_UNICODE);
}
?>
