<?php
/**
 * 用户个人资料API
 * 支持JWT认证，处理用户个人信息的获取和更新
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    // 验证用户认证
    $user = $auth->requireAuth();
    
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            getUserProfile($auth, $user);
            break;
        case 'POST':
        case 'PUT':
            updateUserProfile($auth, $user);
            break;
        default:
            $auth->jsonResponse(405, '不支持的请求方法');
    }
} catch (Exception $e) {
    error_log('用户个人资料API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 获取用户个人资料
 */
function getUserProfile($auth, $user) {
    $conn = $auth->getConn();
    
    $user_id = $user['id'];
    
    // 获取用户详细信息
    $stmt = $conn->prepare("
        SELECT 
            u.id, u.name, u.email, u.username, u.phone, u.avatar, 
            u.nickname, u.gender, u.birthday, u.status, u.login_type,
            u.last_login_at, u.login_count, u.created_at,
            wu.openid, wu.unionid
        FROM users u
        LEFT JOIN wechat_users wu ON u.id = wu.user_id
        WHERE u.id = ?
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $auth->jsonResponse(404, '用户不存在');
    }
    
    $user_data = $result->fetch_assoc();
    
    // 获取用户统计信息
    $stats = getUserStats($conn, $user_id);
    
    // 格式化用户信息
    $profile = [
        'id' => (int)$user_data['id'],
        'name' => $user_data['name'],
        'email' => $user_data['email'],
        'username' => $user_data['username'],
        'phone' => $user_data['phone'],
        'avatar' => $user_data['avatar'],
        'nickname' => $user_data['nickname'],
        'gender' => $user_data['gender'] ? (int)$user_data['gender'] : null,
        'birthday' => $user_data['birthday'],
        'status' => $user_data['status'],
        'login_type' => $user_data['login_type'],
        'last_login_at' => $user_data['last_login_at'] ? date('Y-m-d H:i:s', strtotime($user_data['last_login_at'])) : null,
        'login_count' => (int)$user_data['login_count'],
        'created_at' => date('Y-m-d H:i:s', strtotime($user_data['created_at'])),
        'wechat_info' => [
            'is_bound' => !empty($user_data['openid']),
            'openid' => $user_data['openid'] ? substr($user_data['openid'], 0, 8) . '***' : null,
            'unionid' => $user_data['unionid'] ? substr($user_data['unionid'], 0, 8) . '***' : null
        ],
        'statistics' => $stats
    ];
    
    $auth->jsonResponse(200, '获取用户资料成功', $profile);
}

/**
 * 更新用户个人资料
 */
function updateUserProfile($auth, $user) {
    $conn = $auth->getConn();
    
    $user_id = $user['id'];
    
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $auth->jsonResponse(400, '请求数据格式错误');
    }
    
    // 允许更新的字段
    $allowed_fields = ['name', 'nickname', 'phone', 'gender', 'birthday', 'avatar'];
    $update_fields = [];
    $params = [];
    $param_types = "";

    // 输入验证
    foreach ($allowed_fields as $field) {
        if (isset($input[$field])) {
            $value = $input[$field];

            // 字段特定验证
            switch ($field) {
                case 'name':
                case 'nickname':
                    if (empty(trim($value))) {
                        $auth->jsonResponse(400, $field === 'name' ? '姓名不能为空' : '昵称不能为空');
                    }
                    if (strlen($value) > 50) {
                        $auth->jsonResponse(400, $field === 'name' ? '姓名长度不能超过50个字符' : '昵称长度不能超过50个字符');
                    }
                    break;

                case 'phone':
                    if (!empty($value) && !preg_match('/^1[3-9]\d{9}$/', $value)) {
                        $auth->jsonResponse(400, '手机号格式不正确');
                    }
                    break;

                case 'gender':
                    if (!in_array($value, [0, 1, 2, '0', '1', '2'])) {
                        $auth->jsonResponse(400, '性别值无效');
                    }
                    break;

                case 'birthday':
                    if (!empty($value) && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
                        $auth->jsonResponse(400, '生日格式不正确，请使用YYYY-MM-DD格式');
                    }
                    // 如果是空字符串，转换为NULL
                    if ($value === '') {
                        $value = null;
                    }
                    break;

                case 'avatar':
                    if (!empty($value) && strlen($value) > 500) {
                        $auth->jsonResponse(400, '头像URL长度不能超过500个字符');
                    }
                    break;
            }

            $update_fields[] = "$field = ?";
            $params[] = $value;

            // 根据字段类型设置参数类型
            if ($field === 'gender') {
                $param_types .= "i";
            } elseif ($field === 'birthday' && $value === null) {
                $param_types .= "s"; // NULL值也用字符串类型
            } else {
                $param_types .= "s";
            }
        }
    }

    if (empty($update_fields)) {
        $auth->jsonResponse(400, '没有可更新的字段');
    }
    
    // 特殊处理性别字段
    if (isset($input['gender'])) {
        $gender_index = array_search('gender = ?', $update_fields);
        if ($gender_index !== false) {
            $param_types = substr_replace($param_types, 'i', $gender_index, 1);
            $params[$gender_index] = (int)$input['gender'];
        }
    }
    
    // 添加更新时间
    $update_fields[] = "updated_at = NOW()";
    
    // 构建SQL
    $sql = "UPDATE users SET " . implode(", ", $update_fields) . " WHERE id = ?";
    $params[] = $user_id;
    $param_types .= "i";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($param_types, ...$params);
    
    if ($stmt->execute()) {
        // 检查是否有行被更新
        if ($stmt->affected_rows === 0) {
            $auth->jsonResponse(400, '没有数据被更新，请检查输入的数据是否有变化');
        }

        // 获取更新后的用户信息
        $updated_user = $auth->getUserById($user_id);

        if (!$updated_user) {
            error_log('更新后获取用户信息失败，用户ID: ' . $user_id);
            $auth->jsonResponse(500, '更新成功但获取用户信息失败');
        }

        $auth->jsonResponse(200, '更新用户资料成功', [
            'user' => $updated_user,
            'updated_fields' => array_keys(array_intersect_key($input, array_flip($allowed_fields)))
        ]);
    } else {
        $error_msg = $conn->error;
        error_log('更新用户资料失败: ' . $error_msg . ', SQL: ' . $sql . ', 参数: ' . json_encode($params));

        // 根据错误类型提供更具体的错误信息
        if (strpos($error_msg, 'Duplicate entry') !== false) {
            if (strpos($error_msg, 'phone') !== false) {
                $auth->jsonResponse(400, '该手机号已被其他用户使用');
            } elseif (strpos($error_msg, 'email') !== false) {
                $auth->jsonResponse(400, '该邮箱已被其他用户使用');
            } else {
                $auth->jsonResponse(400, '数据重复，请检查输入信息');
            }
        } elseif (strpos($error_msg, 'Data too long') !== false) {
            $auth->jsonResponse(400, '输入的数据过长，请检查输入内容');
        } else {
            $auth->jsonResponse(500, '更新用户资料失败：' . $error_msg);
        }
    }
}

/**
 * 获取用户统计信息
 */
function getUserStats($conn, $user_id) {
    $stats = [
        'courses_count' => 0,
        'announcements_read' => 0,
        'announcements_favorite' => 0,
        'total_study_time' => 0
    ];
    
    try {
        // 用户课程数量
        $course_stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_courses WHERE user_id = ? AND status = 'active'");
        $course_stmt->bind_param("i", $user_id);
        $course_stmt->execute();
        $course_result = $course_stmt->get_result();
        if ($course_result) {
            $stats['courses_count'] = (int)$course_result->fetch_assoc()['count'];
        }
        
        // 已读公告数量
        $read_stmt = $conn->prepare("SELECT COUNT(*) as count FROM announcement_reads WHERE user_id = ?");
        $read_stmt->bind_param("i", $user_id);
        $read_stmt->execute();
        $read_result = $read_stmt->get_result();
        if ($read_result) {
            $stats['announcements_read'] = (int)$read_result->fetch_assoc()['count'];
        }
        
        // 收藏公告数量（如果表存在）
        $favorite_check = $conn->query("SHOW TABLES LIKE 'announcement_favorites'");
        if ($favorite_check && $favorite_check->num_rows > 0) {
            $favorite_stmt = $conn->prepare("SELECT COUNT(*) as count FROM announcement_favorites WHERE user_id = ?");
            $favorite_stmt->bind_param("i", $user_id);
            $favorite_stmt->execute();
            $favorite_result = $favorite_stmt->get_result();
            if ($favorite_result) {
                $stats['announcements_favorite'] = (int)$favorite_result->fetch_assoc()['count'];
            }
        }
        
        // 总学习时间（如果有观看记录表）
        $watch_check = $conn->query("SHOW TABLES LIKE 'course_watch_logs'");
        if ($watch_check && $watch_check->num_rows > 0) {
            $watch_stmt = $conn->prepare("SELECT SUM(watch_time) as total_time FROM course_watch_logs WHERE user_id = ?");
            $watch_stmt->bind_param("i", $user_id);
            $watch_stmt->execute();
            $watch_result = $watch_stmt->get_result();
            if ($watch_result) {
                $total_time = $watch_result->fetch_assoc()['total_time'];
                $stats['total_study_time'] = $total_time ? (int)$total_time : 0;
            }
        }
        
    } catch (Exception $e) {
        error_log('获取用户统计信息时出错: ' . $e->getMessage());
    }
    
    return $stats;
}
?>
