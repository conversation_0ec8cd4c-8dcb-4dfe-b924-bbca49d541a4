<?php
/**
 * 码支付易支付异步回调处理
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);

require_once '../includes/db.php';

// 记录回调日志
function log_callback($message, $data = null) {
    $log_file = '../logs/epay_callback_' . date('Y-m-d') . '.log';
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $log_content = date('Y-m-d H:i:s') . ' - ' . $message;
    if ($data) {
        $log_content .= ' - Data: ' . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    $log_content .= "\n";
    
    file_put_contents($log_file, $log_content, FILE_APPEND | LOCK_EX);
}

try {
    // 记录原始回调数据
    $callback_data = $_POST;
    log_callback('收到码支付易支付回调', $callback_data);
    
    // 验证必需参数
    $required_params = ['pid', 'trade_no', 'out_trade_no', 'type', 'name', 'money', 'trade_status', 'sign'];
    foreach ($required_params as $param) {
        if (!isset($callback_data[$param]) || $callback_data[$param] === '') {
            log_callback('缺少必需参数: ' . $param);
            echo 'fail';
            exit;
        }
    }
    
    $pid = $callback_data['pid'];
    $trade_no = $callback_data['trade_no']; // 第三方交易号
    $out_trade_no = $callback_data['out_trade_no']; // 我们的支付流水号
    $type = $callback_data['type'];
    $name = $callback_data['name'];
    $money = $callback_data['money'];
    $trade_status = $callback_data['trade_status'];
    $sign = $callback_data['sign'];
    
    // 获取码支付易支付配置
    $stmt = $conn->prepare("
        SELECT setting_key, setting_value 
        FROM settings 
        WHERE setting_key IN ('epay_partner_id', 'epay_partner_key')
    ");
    $stmt->execute();
    $settings_result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    $settings = [];
    foreach ($settings_result as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
    
    // 验证商户ID
    if ($pid !== $settings['epay_partner_id']) {
        log_callback('商户ID不匹配', ['received' => $pid, 'expected' => $settings['epay_partner_id']]);
        echo 'fail';
        exit;
    }
    
    // 验证签名
    $verify_sign = generate_epay_sign($callback_data, $settings['epay_partner_key']);
    if ($sign !== $verify_sign) {
        log_callback('签名验证失败', ['received' => $sign, 'calculated' => $verify_sign]);
        echo 'fail';
        exit;
    }
    
    // 查找支付记录
    $stmt = $conn->prepare("
        SELECT p.*, o.order_no, o.user_id as order_user_id 
        FROM payments p 
        LEFT JOIN orders o ON p.order_id = o.id 
        WHERE p.payment_no = ?
    ");
    $stmt->bind_param("s", $out_trade_no);
    $stmt->execute();
    $payment = $stmt->get_result()->fetch_assoc();
    
    if (!$payment) {
        log_callback('支付记录不存在', ['payment_no' => $out_trade_no]);
        echo 'fail';
        exit;
    }
    
    // 验证金额
    if (abs(floatval($money) - floatval($payment['amount'])) > 0.01) {
        log_callback('金额不匹配', [
            'callback_amount' => $money,
            'order_amount' => $payment['amount']
        ]);
        echo 'fail';
        exit;
    }
    
    // 检查支付状态
    if ($payment['payment_status'] === 'success') {
        log_callback('支付已处理', ['payment_no' => $out_trade_no]);
        echo 'success';
        exit;
    }
    
    // 处理支付成功
    if ($trade_status === 'TRADE_SUCCESS') {
        // 开始事务
        $conn->begin_transaction();
        
        try {
            // 更新支付记录
            $stmt = $conn->prepare("
                UPDATE payments 
                SET payment_status = 'success', 
                    transaction_id = ?, 
                    callback_data = ?, 
                    paid_at = NOW(),
                    updated_at = NOW()
                WHERE id = ?
            ");
            $callback_json = json_encode($callback_data, JSON_UNESCAPED_UNICODE);
            $stmt->bind_param("ssi", $trade_no, $callback_json, $payment['id']);
            $stmt->execute();
            
            // 更新订单状态
            $stmt = $conn->prepare("
                UPDATE orders 
                SET order_status = 'paid', 
                    payment_status = 'paid', 
                    payment_method = ?, 
                    payment_time = NOW(),
                    updated_at = NOW()
                WHERE id = ?
            ");
            $payment_method = 'epay_' . $type;
            $stmt->bind_param("si", $payment_method, $payment['order_id']);
            $stmt->execute();
            
            // 处理课程购买逻辑
            process_course_purchase($conn, $payment['order_id'], $payment['order_user_id']);
            
            // 提交事务
            $conn->commit();
            
            log_callback('支付处理成功', [
                'payment_no' => $out_trade_no,
                'trade_no' => $trade_no,
                'amount' => $money
            ]);
            
            echo 'success';
            
        } catch (Exception $e) {
            // 回滚事务
            $conn->rollback();
            log_callback('支付处理失败: ' . $e->getMessage());
            echo 'fail';
        }
        
    } else {
        log_callback('支付状态异常', ['trade_status' => $trade_status]);
        echo 'fail';
    }
    
} catch (Exception $e) {
    log_callback('回调处理异常: ' . $e->getMessage());
    echo 'fail';
}

/**
 * 处理课程购买逻辑
 */
function process_course_purchase($conn, $order_id, $user_id) {
    // 获取订单中的课程
    $stmt = $conn->prepare("
        SELECT course_id, quantity 
        FROM order_items 
        WHERE order_id = ?
    ");
    $stmt->bind_param("i", $order_id);
    $stmt->execute();
    $order_items = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    foreach ($order_items as $item) {
        // 检查用户是否已经拥有该课程
        $stmt = $conn->prepare("
            SELECT id FROM user_courses 
            WHERE user_id = ? AND course_id = ?
        ");
        $stmt->bind_param("ii", $user_id, $item['course_id']);
        $stmt->execute();
        $exists = $stmt->get_result()->num_rows > 0;
        
        if (!$exists) {
            // 添加用户课程记录
            $stmt = $conn->prepare("
                INSERT INTO user_courses (user_id, course_id, purchase_time, status) 
                VALUES (?, ?, NOW(), 'active')
            ");
            $stmt->bind_param("ii", $user_id, $item['course_id']);
            $stmt->execute();
        }
    }
}

/**
 * 生成码支付易支付签名
 */
function generate_epay_sign($params, $key) {
    // 过滤空值和签名参数
    $filtered_params = [];
    foreach ($params as $k => $v) {
        if ($k !== 'sign' && $k !== 'sign_type' && $v !== '' && $v !== null) {
            $filtered_params[$k] = $v;
        }
    }
    
    // 按键名排序
    ksort($filtered_params);
    
    // 构建签名字符串
    $sign_string = '';
    foreach ($filtered_params as $k => $v) {
        $sign_string .= $k . '=' . $v . '&';
    }
    $sign_string .= 'key=' . $key;
    
    return md5($sign_string);
}
?>
