<template>
	<view class="edit-profile-container">
		<view class="form-container">
			<!-- 头像 -->
			<view class="form-group">
				<view class="form-label">头像</view>
				<view class="avatar-section" @click="chooseAvatar">
					<image
						:src="formData.avatar || '/static/images/default-avatar.svg'"
						class="avatar-image"
						mode="aspectFill"
					/>
					<view class="avatar-overlay">
						<simple-icon type="camera" color="#fff" size="24"></simple-icon>
					</view>
				</view>
			</view>

			<!-- 姓名 -->
			<view class="form-group">
				<view class="form-label">姓名 <text class="required">*</text></view>
				<input 
					v-model="formData.name" 
					class="form-input" 
					placeholder="请输入姓名"
					maxlength="20"
				/>
			</view>

			<!-- 昵称 -->
			<view class="form-group">
				<view class="form-label">昵称</view>
				<input 
					v-model="formData.nickname" 
					class="form-input" 
					placeholder="请输入昵称"
					maxlength="20"
				/>
			</view>

			<!-- 手机号 -->
			<view class="form-group">
				<view class="form-label">手机号</view>
				<input 
					v-model="formData.phone" 
					class="form-input" 
					placeholder="请输入手机号"
					type="number"
					maxlength="11"
				/>
			</view>

			<!-- 性别 -->
			<view class="form-group">
				<view class="form-label">性别</view>
				<view class="gender-options">
					<view 
						class="gender-option" 
						:class="{ active: formData.gender === 1 }"
						@click="selectGender(1)"
					>
						<simple-icon type="person" color="#007bff" size="16"></simple-icon>
						<text>男</text>
					</view>
					<view 
						class="gender-option" 
						:class="{ active: formData.gender === 2 }"
						@click="selectGender(2)"
					>
						<simple-icon type="person" color="#e91e63" size="16"></simple-icon>
						<text>女</text>
					</view>
					<view 
						class="gender-option" 
						:class="{ active: formData.gender === 0 || !formData.gender }"
						@click="selectGender(0)"
					>
						<simple-icon type="help" color="#6c757d" size="16"></simple-icon>
						<text>保密</text>
					</view>
				</view>
			</view>

			<!-- 生日 -->
			<view class="form-group">
				<view class="form-label">生日</view>
				<picker 
					mode="date" 
					:value="formData.birthday" 
					@change="onBirthdayChange"
					:end="todayDate"
				>
					<view class="date-picker">
						<text class="date-text">{{ formData.birthday || '请选择生日' }}</text>
						<simple-icon type="calendar" color="#999" size="16"></simple-icon>
					</view>
				</picker>
			</view>
		</view>

		<!-- 保存按钮 -->
		<view class="save-section">
			<button 
				class="save-btn" 
				:class="{ disabled: !canSave }"
				:disabled="!canSave"
				@click="saveProfile"
			>
				{{ saving ? '保存中...' : '保存' }}
			</button>
		</view>
	</view>
</template>

<script>
	import { getCurrentUser } from '../../api/auth.js';
	import request from '../../utils/request.js';
	import { showSuccess, showError, showLoading, hideLoading, showSmartError } from '../../utils/storage.js';
	import SimpleIcon from '../../components/simple-icon/simple-icon.vue';

	export default {
		components: {
			SimpleIcon
		},
		data() {
			return {
				formData: {
					name: '',
					nickname: '',
					phone: '',
					gender: 0,
					birthday: '',
					avatar: ''
				},
				originalData: {},
				saving: false,
				todayDate: ''
			};
		},
		
		computed: {
			canSave() {
				return !this.saving && 
					   this.formData.name.trim() && 
					   this.hasChanges();
			}
		},
		
		onLoad() {
			this.initPage();
		},
		
		methods: {
			/**
			 * 初始化页面
			 */
			async initPage() {
				// 设置今天的日期
				const today = new Date();
				this.todayDate = today.toISOString().split('T')[0];
				
				await this.loadUserProfile();
			},
			
			/**
			 * 加载用户资料
			 */
			async loadUserProfile() {
				try {
					showLoading('加载中...');
					const response = await getCurrentUser();
					
					if (response.code === 200) {
						const user = response.data.user;
						this.formData = {
							name: user.name || '',
							nickname: user.nickname || '',
							phone: user.phone || '',
							gender: user.gender || 0,
							birthday: user.birthday || '',
							avatar: user.avatar || ''
						};
						
						// 保存原始数据用于比较
						this.originalData = { ...this.formData };
					} else {
						showError(response.message || '加载用户信息失败');
					}
				} catch (error) {
					console.error('加载用户信息失败:', error);
					showError('加载用户信息失败');
				} finally {
					hideLoading();
				}
			},
			
			/**
			 * 检查是否有变更
			 */
			hasChanges() {
				return JSON.stringify(this.formData) !== JSON.stringify(this.originalData);
			},
			
			/**
			 * 选择性别
			 */
			selectGender(gender) {
				this.formData.gender = gender;
			},
			
			/**
			 * 生日变更
			 */
			onBirthdayChange(e) {
				this.formData.birthday = e.detail.value;
			},
			
			/**
			 * 选择头像
			 */
			chooseAvatar() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						this.uploadAvatar(tempFilePath);
					},
					fail: (error) => {
						console.error('选择图片失败:', error);
						// 只有在非用户取消的情况下才显示错误提示
						if (error.errMsg && !error.errMsg.includes('cancel')) {
							showError('选择图片失败');
						}
					}
				});
			},
			
			/**
			 * 上传头像
			 */
			uploadAvatar(filePath) {
				showLoading('上传中...');

				// 使用uni.uploadFile上传文件
				uni.uploadFile({
					url: 'https://wx.yx420.cn/api/upload.php',
					filePath: filePath,
					name: 'file',
					formData: {
						type: 'avatar'
					},
					header: {
						'Authorization': 'Bearer ' + uni.getStorageSync('access_token')
					},
					success: (uploadRes) => {
						try {
							const response = JSON.parse(uploadRes.data);
							if (response.code === 200) {
								this.formData.avatar = 'https://wx.yx420.cn' + response.data.file_url;
								showSuccess('头像上传成功');
							} else {
								showError(response.message || '头像上传失败');
							}
						} catch (error) {
							console.error('解析上传响应失败:', error);
							showError('头像上传失败');
						}
					},
					fail: (error) => {
						console.error('头像上传失败:', error);
						showError('头像上传失败');
					},
					complete: () => {
						hideLoading();
					}
				});
			},
			
			/**
			 * 保存个人资料
			 */
			async saveProfile() {
				if (!this.canSave) return;
				
				// 验证必填字段
				if (!this.formData.name.trim()) {
					showError('请输入姓名');
					return;
				}
				
				// 验证手机号格式
				if (this.formData.phone && !/^1[3-9]\d{9}$/.test(this.formData.phone)) {
					showError('请输入正确的手机号');
					return;
				}
				
				try {
					this.saving = true;
					showLoading('保存中...');
					
					const response = await request.put('profile.php', this.formData);
					
					if (response.code === 200) {
						showSuccess('保存成功');
						
						// 更新原始数据
						this.originalData = { ...this.formData };
						
						// 返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					} else {
						showError(response.message || '保存失败');
					}
				} catch (error) {
					console.error('保存个人资料失败:', error);
					showSmartError(error, '保存失败，请稍后重试');
				} finally {
					this.saving = false;
					hideLoading();
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
.edit-profile-container {
	background: #f8f9fa;
	min-height: 100vh;
	padding: 15px;
}

.form-container {
	background: #fff;
	border-radius: 12px;
	padding: 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-group {
	margin-bottom: 25px;

	.form-label {
		font-size: 16px;
		color: #333;
		margin-bottom: 10px;
		font-weight: 500;

		.required {
			color: #dc3545;
		}
	}

	.form-input {
		width: 100%;
		height: 45px;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		padding: 0 15px;
		font-size: 16px;
		color: #333;
		background: #fff;

		&:focus {
			border-color: #007bff;
		}
	}
}

.avatar-section {
	position: relative;
	width: 80px;
	height: 80px;
	border-radius: 50%;
	overflow: hidden;
	border: 2px solid #e9ecef;

	.avatar-image {
		width: 100%;
		height: 100%;
	}

	.avatar-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		opacity: 0;
		transition: opacity 0.3s;
	}

	&:active .avatar-overlay {
		opacity: 1;
	}
}

.gender-options {
	display: flex;
	gap: 15px;

	.gender-option {
		flex: 1;
		height: 45px;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 8px;
		background: #fff;
		transition: all 0.3s;

		&.active {
			border-color: #007bff;
			background: #f8f9ff;
		}

		text {
			font-size: 14px;
			color: #666;
		}

		&.active text {
			color: #007bff;
		}
	}
}

.date-picker {
	height: 45px;
	border: 1px solid #e9ecef;
	border-radius: 8px;
	padding: 0 15px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #fff;

	.date-text {
		font-size: 16px;
		color: #333;
	}
}

.save-section {
	margin-top: 30px;

	.save-btn {
		width: 100%;
		height: 50px;
		background: #007bff;
		color: #fff;
		border: none;
		border-radius: 25px;
		font-size: 16px;
		font-weight: 500;

		&.disabled {
			background: #6c757d;
			opacity: 0.6;
		}
	}
}
</style>
