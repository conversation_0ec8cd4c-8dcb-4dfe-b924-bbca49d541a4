<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>创建默认头像</title>
</head>
<body>
    <canvas id="canvas" width="120" height="120" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadImage()">下载默认头像</button>
    <br><br>
    <textarea id="base64Output" style="width: 100%; height: 200px;" placeholder="Base64数据将显示在这里"></textarea>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // 创建默认头像
        function createDefaultAvatar() {
            // 设置背景色
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, 120, 120);
            
            // 绘制圆形背景
            ctx.beginPath();
            ctx.arc(60, 60, 50, 0, 2 * Math.PI);
            ctx.fillStyle = '#e0e0e0';
            ctx.fill();
            
            // 绘制人物图标
            // 头部
            ctx.beginPath();
            ctx.arc(60, 45, 15, 0, 2 * Math.PI);
            ctx.fillStyle = '#999';
            ctx.fill();
            
            // 身体
            ctx.beginPath();
            ctx.arc(60, 85, 25, 0, Math.PI, true);
            ctx.fillStyle = '#999';
            ctx.fill();
            
            // 添加边框
            ctx.beginPath();
            ctx.arc(60, 60, 50, 0, 2 * Math.PI);
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 2;
            ctx.stroke();
        }
        
        function downloadImage() {
            const link = document.createElement('a');
            link.download = 'default-avatar.png';
            link.href = canvas.toDataURL();
            link.click();
            
            // 同时显示base64数据
            document.getElementById('base64Output').value = canvas.toDataURL();
        }
        
        // 初始化
        createDefaultAvatar();
    </script>
</body>
</html>
