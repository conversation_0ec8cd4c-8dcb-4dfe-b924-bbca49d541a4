<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';
require_once '../includes/lesson_db_setup.php';

// 确保数据库表结构正确
$setup_result = setupLessonTables($conn);

$success_message = '';
$error_message = '';

// 创建测试课时数据
if (isset($_POST['create_test_data'])) {
    $course_id = intval($_POST['course_id']);
    
    if ($course_id <= 0) {
        $error_message = '请选择有效的课程';
    } else {
        // 检查课程是否存在
        $course_check = $conn->prepare("SELECT id, title FROM courses WHERE id = ?");
        $course_check->bind_param("i", $course_id);
        $course_check->execute();
        $course_result = $course_check->get_result();
        $course = $course_result->fetch_assoc();
        
        if (!$course) {
            $error_message = '课程不存在';
        } else {
            // 创建测试课时数据
            $test_lessons = [
                [
                    'title' => '第1课：基础入门',
                    'description' => '本课程将带您了解基础知识，为后续学习打下坚实基础。',
                    'video_url' => 'https://sample-videos.com/zip/10/mp4/480/mp4-sample-1.mp4',
                    'duration' => 1800, // 30分钟
                    'sort_order' => 1,
                    'is_free' => 1,
                    'thumbnail' => 'https://picsum.photos/400/300?random=101'
                ],
                [
                    'title' => '第2课：核心概念',
                    'description' => '深入学习核心概念，掌握重要的理论知识。',
                    'video_url' => 'https://sample-videos.com/zip/10/mp4/480/mp4-sample-2.mp4',
                    'duration' => 2400, // 40分钟
                    'sort_order' => 2,
                    'is_free' => 0,
                    'thumbnail' => 'https://picsum.photos/400/300?random=102'
                ],
                [
                    'title' => '第3课：实践应用',
                    'description' => '通过实际案例学习如何应用所学知识。',
                    'video_url' => 'https://sample-videos.com/zip/10/mp4/480/mp4-sample-3.mp4',
                    'duration' => 3000, // 50分钟
                    'sort_order' => 3,
                    'is_free' => 0,
                    'thumbnail' => 'https://picsum.photos/400/300?random=103'
                ],
                [
                    'title' => '第4课：高级技巧',
                    'description' => '学习高级技巧和最佳实践，提升专业技能。',
                    'video_url' => 'https://sample-videos.com/zip/10/mp4/480/mp4-sample-4.mp4',
                    'duration' => 2700, // 45分钟
                    'sort_order' => 4,
                    'is_free' => 0,
                    'thumbnail' => 'https://picsum.photos/400/300?random=104'
                ],
                [
                    'title' => '第5课：项目实战',
                    'description' => '完整的项目实战，综合运用所有知识点。',
                    'video_url' => 'https://sample-videos.com/zip/10/mp4/480/mp4-sample-5.mp4',
                    'duration' => 3600, // 60分钟
                    'sort_order' => 5,
                    'is_free' => 0,
                    'thumbnail' => 'https://picsum.photos/400/300?random=105'
                ]
            ];
            
            $admin_id = $_SESSION['admin_id'] ?? 1;
            $created_count = 0;
            
            foreach ($test_lessons as $lesson_data) {
                $insert_sql = "INSERT INTO lessons (course_id, title, description, video_url, duration, sort_order, is_free, thumbnail, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_sql);
                $insert_stmt->bind_param("isssiiisi", 
                    $course_id, 
                    $lesson_data['title'], 
                    $lesson_data['description'], 
                    $lesson_data['video_url'], 
                    $lesson_data['duration'], 
                    $lesson_data['sort_order'], 
                    $lesson_data['is_free'], 
                    $lesson_data['thumbnail'], 
                    $admin_id
                );
                
                if ($insert_stmt->execute()) {
                    $created_count++;
                }
            }
            
            if ($created_count > 0) {
                // 更新课程统计信息
                updateCourseStats($conn, $course_id);
                $success_message = "成功创建 {$created_count} 个测试课时";
            } else {
                $error_message = '创建测试课时失败';
            }
        }
    }
}

// 更新课程统计信息的函数
function updateCourseStats($conn, $course_id) {
    $stats_sql = "SELECT COUNT(*) as lesson_count, COALESCE(SUM(duration), 0) as total_duration FROM lessons WHERE course_id = ? AND status = 1";
    $stats_stmt = $conn->prepare($stats_sql);
    $stats_stmt->bind_param("i", $course_id);
    $stats_stmt->execute();
    $stats_result = $stats_stmt->get_result();
    $stats = $stats_result->fetch_assoc();
    
    $update_course_sql = "UPDATE courses SET lesson_count = ?, total_duration = ? WHERE id = ?";
    $update_course_stmt = $conn->prepare($update_course_sql);
    $update_course_stmt->bind_param("iii", $stats['lesson_count'], $stats['total_duration'], $course_id);
    $update_course_stmt->execute();
}

// 获取所有课程
$courses_query = "SELECT id, title FROM courses ORDER BY id DESC";
$courses_result = $conn->query($courses_query);
$courses = $courses_result->fetch_all(MYSQLI_ASSOC);

// 获取课时统计信息
$stats_query = "
    SELECT 
        c.id,
        c.title,
        COUNT(l.id) as lesson_count,
        COALESCE(SUM(l.duration), 0) as total_duration,
        COUNT(CASE WHEN l.is_free = 1 THEN 1 END) as free_lessons,
        COUNT(CASE WHEN l.is_free = 0 THEN 1 END) as paid_lessons
    FROM courses c
    LEFT JOIN lessons l ON c.id = l.course_id AND l.status = 1
    GROUP BY c.id, c.title
    ORDER BY c.id DESC
";
$stats_result = $conn->query($stats_query);
$course_stats = $stats_result->fetch_all(MYSQLI_ASSOC);

render_admin_header('课时管理测试');
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <!-- 数据库状态 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">数据库状态检查</h5>
                </div>
                <div class="card-body">
                    <?php if ($setup_result['success']): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> 数据库表结构正常
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> 数据库表结构异常
                        </div>
                    <?php endif; ?>
                    
                    <div class="mt-3">
                        <h6>初始化信息：</h6>
                        <ul class="list-unstyled">
                            <?php foreach ($setup_result['messages'] as $message): ?>
                                <li><i class="fas fa-info-circle text-info"></i> <?php echo htmlspecialchars($message); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 创建测试数据 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">创建测试课时数据</h5>
                </div>
                <div class="card-body">
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($success_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($error_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="course_id" class="form-label">选择课程</label>
                                    <select class="form-select" id="course_id" name="course_id" required>
                                        <option value="">请选择课程</option>
                                        <?php foreach ($courses as $course): ?>
                                            <option value="<?php echo $course['id']; ?>">
                                                <?php echo htmlspecialchars($course['title']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="submit" name="create_test_data" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> 创建测试课时数据
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> 
                        将为选中的课程创建5个测试课时，包含不同的时长、免费/付费状态等。
                    </div>
                </div>
            </div>
            
            <!-- 课程课时统计 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">课程课时统计</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>课程ID</th>
                                    <th>课程标题</th>
                                    <th>课时数量</th>
                                    <th>总时长</th>
                                    <th>免费课时</th>
                                    <th>付费课时</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($course_stats as $stat): ?>
                                    <tr>
                                        <td><?php echo $stat['id']; ?></td>
                                        <td><?php echo htmlspecialchars($stat['title']); ?></td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo $stat['lesson_count']; ?></span>
                                        </td>
                                        <td><?php echo gmdate("H:i:s", $stat['total_duration']); ?></td>
                                        <td>
                                            <span class="badge bg-success"><?php echo $stat['free_lessons']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?php echo $stat['paid_lessons']; ?></span>
                                        </td>
                                        <td>
                                            <a href="lessons.php?course_id=<?php echo $stat['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-cog"></i> 管理课时
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php render_admin_footer(); ?>
