<?php
/**
 * 快速修复微信支付问题
 * 这个脚本将临时禁用微信支付，并提供友好的错误提示
 */

header('Content-Type: text/html; charset=utf-8');
require_once '../includes/db.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='utf-8'><title>微信支付快速修复</title></head><body>";
echo "<h1>微信支付快速修复</h1>";

try {
    // 1. 禁用微信支付
    $stmt = $conn->prepare("
        INSERT INTO settings (setting_key, setting_value, setting_type, description) 
        VALUES ('wechat_pay_enabled', '0', 'boolean', '是否启用微信支付')
        ON DUPLICATE KEY UPDATE setting_value = '0'
    ");
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ 已禁用微信支付功能</p>";
    } else {
        echo "<p style='color: red;'>❌ 禁用微信支付失败</p>";
    }
    
    // 2. 设置占位符配置
    $configs = [
        'wechat_pay_app_id' => 'wxa936b5c9fa9b1893',
        'wechat_pay_mch_id' => '1234567890',
        'wechat_pay_api_key' => 'your_api_key_here_32_characters',
        'wechat_pay_notify_url' => 'https://your-domain.com/api/payment-wechat-notify.php'
    ];
    
    foreach ($configs as $key => $value) {
        $stmt = $conn->prepare("
            INSERT INTO settings (setting_key, setting_value, setting_type, description) 
            VALUES (?, ?, 'string', ?)
            ON DUPLICATE KEY UPDATE setting_value = IF(setting_value = '' OR setting_value IS NULL, VALUES(setting_value), setting_value)
        ");
        
        $description = '';
        switch ($key) {
            case 'wechat_pay_app_id':
                $description = '微信小程序AppID';
                break;
            case 'wechat_pay_mch_id':
                $description = '微信支付商户号';
                break;
            case 'wechat_pay_api_key':
                $description = '微信支付API密钥';
                break;
            case 'wechat_pay_notify_url':
                $description = '微信支付回调URL';
                break;
        }
        
        $stmt->bind_param("sss", $key, $value, $description);
        $stmt->execute();
    }
    
    echo "<p style='color: green;'>✅ 已设置占位符配置</p>";
    
    // 3. 创建改进的支付接口
    $improved_payment_content = '<?php
header("Content-Type: application/json; charset=utf-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    exit(0);
}

require_once "../includes/db.php";
require_once "../includes/auth.php";

// 验证用户身份
$user = authenticate_user();
if (!$user) {
    http_response_code(401);
    echo json_encode([
        "code" => 401,
        "message" => "未授权访问",
        "data" => null
    ]);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents("php://input"), true);

// 验证必需参数
if (!isset($input["order_id"]) || !isset($input["openid"])) {
    echo json_encode([
        "code" => 400,
        "message" => "缺少必需参数",
        "data" => null
    ]);
    exit;
}

// 检查微信支付是否启用
$stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = \"wechat_pay_enabled\"");
$stmt->execute();
$enabled = $stmt->get_result()->fetch_assoc();

if (!$enabled || $enabled["setting_value"] !== "1") {
    echo json_encode([
        "code" => 503,
        "message" => "微信支付功能暂时不可用",
        "data" => null,
        "user_message" => "支付功能正在维护中，请稍后再试或联系客服。\n\n您可以：\n1. 稍后再试\n2. 联系客服获取帮助\n3. 查看其他课程",
        "admin_message" => "请在后台配置微信支付参数后启用支付功能"
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查配置完整性
$stmt = $conn->prepare("
    SELECT setting_key, setting_value 
    FROM settings 
    WHERE setting_key IN (\"wechat_pay_app_id\", \"wechat_pay_mch_id\", \"wechat_pay_api_key\")
");
$stmt->execute();
$configs = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

$missing_configs = [];
foreach ($configs as $config) {
    if (empty($config["setting_value"]) || 
        $config["setting_value"] === "your_api_key_here_32_characters" ||
        $config["setting_value"] === "1234567890") {
        $missing_configs[] = $config["setting_key"];
    }
}

if (!empty($missing_configs)) {
    echo json_encode([
        "code" => 500,
        "message" => "微信支付配置不完整",
        "data" => null,
        "user_message" => "支付功能暂时不可用，请联系客服。\n\n可能的原因：\n1. 支付系统正在维护\n2. 配置正在更新\n3. 系统升级中",
        "admin_message" => "请完善微信支付配置：" . implode(", ", $missing_configs)
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 如果配置完整，转到原始支付处理
try {
    include "payment-wechat-prepay.php";
} catch (Exception $e) {
    echo json_encode([
        "code" => 500,
        "message" => "支付处理失败",
        "data" => null,
        "user_message" => "支付功能暂时不可用，请稍后再试或联系客服",
        "admin_message" => "支付处理异常：" . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>';
    
    if (file_put_contents('../api/payment-wechat-prepay-improved.php', $improved_payment_content)) {
        echo "<p style='color: green;'>✅ 已创建改进的支付接口</p>";
    } else {
        echo "<p style='color: red;'>❌ 创建改进支付接口失败</p>";
    }
    
    echo "<div style='background: #d4edda; padding: 20px; border: 1px solid #c3e6cb; margin: 20px 0;'>";
    echo "<h2>✅ 修复完成</h2>";
    echo "<p>已完成以下修复：</p>";
    echo "<ul>";
    echo "<li>禁用微信支付功能，避免错误信息显示</li>";
    echo "<li>设置占位符配置，保证系统正常运行</li>";
    echo "<li>创建改进的支付接口，提供友好的错误提示</li>";
    echo "<li>用户将看到'支付功能正在维护中'的提示</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border: 1px solid #ffeaa7; margin: 20px 0;'>";
    echo "<h3>⚠️ 注意事项</h3>";
    echo "<p>当前支付功能已暂时禁用，用户将看到友好的提示信息。要启用支付功能，请：</p>";
    echo "<ol>";
    echo "<li>在微信支付商户平台获取真实的商户号和API密钥</li>";
    echo "<li>更新数据库中的配置信息</li>";
    echo "<li>将wechat_pay_enabled设置为1</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #e2f3ff; padding: 20px; border: 1px solid #b8daff; margin: 20px 0;'>";
    echo "<h3>🔧 快速操作</h3>";
    echo "<p>";
    echo "<a href='check_wechat_config.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; margin: 5px;'>检查配置</a>";
    echo "<a href='../admin/settings.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; margin: 5px;'>后台管理</a>";
    echo "<a href='../wx/pages/courses/index.vue' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; margin: 5px;'>查看课程</a>";
    echo "</p>";
    echo "</div>";
    
    echo "<div style='background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; margin: 20px 0;'>";
    echo "<h3>📋 测试步骤</h3>";
    echo "<ol>";
    echo "<li>打开小程序课程详情页</li>";
    echo "<li>点击'立即购买'按钮</li>";
    echo "<li>确认看到'支付功能正在维护中'的提示</li>";
    echo "<li>错误信息不再显示'appid和mch_id不匹配'</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 修复失败: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>