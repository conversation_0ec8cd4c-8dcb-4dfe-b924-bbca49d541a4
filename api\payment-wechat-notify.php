<?php
header('Content-Type: text/xml; charset=utf-8');

require_once '../includes/db.php';

// 记录日志函数
function log_payment_notify($message, $data = null) {
    $log_file = '../logs/payment_notify_' . date('Y-m-d') . '.log';
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $log_content = date('Y-m-d H:i:s') . ' - ' . $message;
    if ($data) {
        $log_content .= ' - Data: ' . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    $log_content .= PHP_EOL;
    
    file_put_contents($log_file, $log_content, FILE_APPEND | LOCK_EX);
}

try {
    // 获取微信回调数据
    $xml_data = file_get_contents('php://input');
    log_payment_notify('收到微信支付回调', ['xml' => $xml_data]);
    
    if (empty($xml_data)) {
        log_payment_notify('回调数据为空');
        echo '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[数据为空]]></return_msg></xml>';
        exit;
    }
    
    // 解析XML数据
    $data = xml_to_array($xml_data);
    log_payment_notify('解析回调数据', $data);
    
    // 验证基本参数
    if (!isset($data['return_code']) || $data['return_code'] !== 'SUCCESS') {
        log_payment_notify('微信返回失败', $data);
        echo '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[微信返回失败]]></return_msg></xml>';
        exit;
    }
    
    if (!isset($data['result_code']) || $data['result_code'] !== 'SUCCESS') {
        log_payment_notify('支付结果失败', $data);
        echo '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[支付失败]]></return_msg></xml>';
        exit;
    }
    
    // 获取微信支付配置
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'wechat_pay_api_key'");
    $stmt->execute();
    $api_key = $stmt->get_result()->fetch_assoc()['setting_value'] ?? '';
    
    if (empty($api_key)) {
        log_payment_notify('微信支付API密钥未配置');
        echo '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[配置错误]]></return_msg></xml>';
        exit;
    }
    
    // 验证签名
    $sign = $data['sign'];
    unset($data['sign']);
    $calculated_sign = generate_wechat_sign($data, $api_key);
    
    if ($sign !== $calculated_sign) {
        log_payment_notify('签名验证失败', [
            'received_sign' => $sign,
            'calculated_sign' => $calculated_sign
        ]);
        echo '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[签名验证失败]]></return_msg></xml>';
        exit;
    }
    
    // 获取支付信息
    $out_trade_no = $data['out_trade_no']; // 我们的支付流水号
    $transaction_id = $data['transaction_id']; // 微信交易号
    $total_fee = intval($data['total_fee']); // 支付金额（分）
    $openid = $data['openid'];
    
    $conn->begin_transaction();
    
    // 查询支付记录
    $stmt = $conn->prepare("
        SELECT p.*, o.id as order_id, o.actual_amount, o.order_status, o.payment_status as order_payment_status
        FROM payments p
        LEFT JOIN orders o ON p.order_id = o.id
        WHERE p.payment_no = ? AND p.payment_method = 'wechat'
    ");
    $stmt->bind_param("s", $out_trade_no);
    $stmt->execute();
    $payment = $stmt->get_result()->fetch_assoc();
    
    if (!$payment) {
        log_payment_notify('支付记录不存在', ['payment_no' => $out_trade_no]);
        $conn->rollback();
        echo '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[支付记录不存在]]></return_msg></xml>';
        exit;
    }
    
    // 验证金额
    $expected_amount = intval($payment['actual_amount'] * 100);
    if ($total_fee !== $expected_amount) {
        log_payment_notify('支付金额不匹配', [
            'expected' => $expected_amount,
            'received' => $total_fee
        ]);
        $conn->rollback();
        echo '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[金额不匹配]]></return_msg></xml>';
        exit;
    }
    
    // 检查是否已经处理过
    if ($payment['payment_status'] === 'success') {
        log_payment_notify('支付已处理过', ['payment_id' => $payment['id']]);
        $conn->commit();
        echo '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>';
        exit;
    }
    
    // 更新支付记录
    $stmt = $conn->prepare("
        UPDATE payments 
        SET payment_status = 'success', 
            transaction_id = ?, 
            callback_data = ?,
            paid_at = NOW(),
            updated_at = NOW()
        WHERE id = ?
    ");
    $callback_data = json_encode($data, JSON_UNESCAPED_UNICODE);
    $stmt->bind_param("ssi", $transaction_id, $callback_data, $payment['id']);
    $stmt->execute();
    
    // 更新订单状态
    $stmt = $conn->prepare("
        UPDATE orders 
        SET order_status = 'paid', 
            payment_status = 'paid', 
            payment_method = 'wechat',
            payment_time = NOW(),
            updated_at = NOW()
        WHERE id = ?
    ");
    $stmt->bind_param("i", $payment['order_id']);
    $stmt->execute();
    
    // 获取订单商品，为用户分配课程权限
    $stmt = $conn->prepare("SELECT course_id, course_price FROM order_items WHERE order_id = ?");
    $stmt->bind_param("i", $payment['order_id']);
    $stmt->execute();
    $order_items = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    foreach ($order_items as $item) {
        // 检查用户是否已有该课程
        $stmt = $conn->prepare("
            SELECT id FROM user_courses 
            WHERE user_id = ? AND course_id = ?
        ");
        $stmt->bind_param("ii", $payment['user_id'], $item['course_id']);
        $stmt->execute();
        $existing = $stmt->get_result()->fetch_assoc();
        
        if ($existing) {
            // 更新现有记录
            $stmt = $conn->prepare("
                UPDATE user_courses 
                SET status = 'active', 
                    purchase_type = 'purchased',
                    purchase_price = ?,
                    order_id = ?,
                    assigned_at = NOW()
                WHERE id = ?
            ");
            $stmt->bind_param("dii", $item['course_price'], $payment['order_id'], $existing['id']);
            $stmt->execute();
        } else {
            // 创建新记录
            $stmt = $conn->prepare("
                INSERT INTO user_courses (user_id, course_id, assigned_by, assigned_at, 
                                        status, purchase_type, purchase_price, order_id) 
                VALUES (?, ?, 1, NOW(), 'active', 'purchased', ?, ?)
            ");
            $stmt->bind_param("iidi", $payment['user_id'], $item['course_id'], $item['course_price'], $payment['order_id']);
            $stmt->execute();
        }
    }
    
    $conn->commit();
    
    log_payment_notify('支付处理成功', [
        'payment_id' => $payment['id'],
        'order_id' => $payment['order_id'],
        'transaction_id' => $transaction_id
    ]);
    
    // 返回成功响应
    echo '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>';
    
} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollback();
    }
    
    log_payment_notify('支付回调处理异常', ['error' => $e->getMessage()]);
    echo '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理异常]]></return_msg></xml>';
}

/**
 * XML转数组
 */
function xml_to_array($xml) {
    return json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
}

/**
 * 生成微信签名
 */
function generate_wechat_sign($params, $key) {
    ksort($params);
    $string = '';
    foreach ($params as $k => $v) {
        if ($k !== 'sign' && $v !== '' && $v !== null) {
            $string .= $k . '=' . $v . '&';
        }
    }
    $string .= 'key=' . $key;
    return strtoupper(md5($string));
}
?>
