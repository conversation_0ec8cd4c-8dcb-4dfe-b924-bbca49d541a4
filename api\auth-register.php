<?php
/**
 * 用户注册API
 * 支持传统用户名/邮箱注册
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

$auth = new AuthAPI();

// 检查是否允许注册
$settings_result = $auth->getConn()->query("SELECT setting_value FROM settings WHERE setting_key = 'allow_registration'");
$allow_registration = $settings_result->fetch_assoc()['setting_value'] ?? '1';

if ($allow_registration !== '1') {
    $auth->jsonResponse(403, '系统暂时关闭用户注册');
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    $auth->jsonResponse(400, '无效的请求数据');
}

$username = trim($input['username'] ?? '');
$email = trim($input['email'] ?? '');
$password = $input['password'] ?? '';
$name = trim($input['name'] ?? '');
$phone = trim($input['phone'] ?? '');
$nickname = trim($input['nickname'] ?? $name);

// 验证必填字段
if (empty($username) || empty($email) || empty($password) || empty($name)) {
    $auth->jsonResponse(400, '请填写所有必填字段（用户名、邮箱、密码、姓名）');
}

// 验证邮箱格式
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $auth->jsonResponse(400, '邮箱格式不正确');
}

// 验证用户名格式（只允许字母、数字、下划线）
if (!preg_match('/^[a-zA-Z0-9_]{3,20}$/', $username)) {
    $auth->jsonResponse(400, '用户名只能包含字母、数字、下划线，长度3-20位');
}

// 验证密码长度
$min_password_length = 6;
$settings_result = $auth->getConn()->query("SELECT setting_value FROM settings WHERE setting_key = 'password_min_length'");
if ($row = $settings_result->fetch_assoc()) {
    $min_password_length = intval($row['setting_value']);
}

if (strlen($password) < $min_password_length) {
    $auth->jsonResponse(400, "密码长度不能少于{$min_password_length}位");
}

try {
    // 检查用户名是否已存在
    $stmt = $auth->getConn()->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->bind_param("s", $username);
    $stmt->execute();
    if ($stmt->get_result()->num_rows > 0) {
        $auth->jsonResponse(409, '用户名已被注册');
    }

    // 检查邮箱是否已存在
    $stmt = $auth->getConn()->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    if ($stmt->get_result()->num_rows > 0) {
        $auth->jsonResponse(409, '邮箱已被注册');
    }
    
    // 检查手机号是否已存在（如果提供了手机号）
    if (!empty($phone)) {
        if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
            $auth->jsonResponse(400, '手机号格式不正确');
        }
        
        $stmt = $auth->getConn()->prepare("SELECT id FROM users WHERE phone = ?");
        $stmt->bind_param("s", $phone);
        $stmt->execute();
        if ($stmt->get_result()->num_rows > 0) {
            $auth->jsonResponse(409, '手机号已被注册');
        }
    }
    
    // 哈希密码
    $hashed_password = $auth->hashPassword($password);
    
    // 插入用户数据
    $stmt = $auth->getConn()->prepare("INSERT INTO users (username, email, password, name, phone, nickname, status, login_type) VALUES (?, ?, ?, ?, ?, ?, 'active', 'traditional')");
    $stmt->bind_param("ssssss", $username, $email, $hashed_password, $name, $phone, $nickname);

    if ($stmt->execute()) {
        $user_id = $auth->getConn()->insert_id;
        
        // 记录注册日志
        $auth->logLogin($user_id, 'traditional', 'username', 'success');
        
        // 获取用户信息
        $user_info = $auth->getUserById($user_id);
        
        // 生成令牌（注册后自动登录）
        $access_token = $auth->generateJWT($user_id, 'access');
        $refresh_token = $auth->generateJWT($user_id, 'refresh');
        
        // 保存令牌
        $auth->saveToken($user_id, $access_token, 'access');
        $auth->saveToken($user_id, $refresh_token, 'refresh');
        
        // 更新最后登录信息
        $auth->updateLastLogin($user_id);
        
        $auth->jsonResponse(201, '注册成功', [
            'access_token' => $access_token,
            'refresh_token' => $refresh_token,
            'token_type' => 'Bearer',
            'expires_in' => 7200,
            'user' => [
                'id' => $user_info['id'],
                'name' => $user_info['name'],
                'email' => $user_info['email'],
                'username' => $user_info['username'],
                'nickname' => $user_info['nickname'],
                'avatar' => $user_info['avatar'],
                'phone' => $user_info['phone'],
                'gender' => $user_info['gender'],
                'login_type' => $user_info['login_type']
            ]
        ]);
    } else {
        $auth->jsonResponse(500, '注册失败，请稍后重试');
    }
    
} catch (Exception $e) {
    error_log('注册错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}