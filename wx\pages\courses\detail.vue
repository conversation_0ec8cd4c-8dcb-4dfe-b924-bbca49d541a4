<template>
	<view class="course-detail-container">
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<uni-load-more status="loading"></uni-load-more>
		</view>
		
		<!-- 课程内容 -->
		<view class="course-content" v-else-if="courseInfo">
			<!-- 视频播放器 -->
			<view class="video-section" v-if="currentLesson && hasLessonAccess(currentLesson)">
				<!-- 课时信息 -->
				<view class="lesson-info" :class="{ 'compact-mode': isVideoPlaying }">
					<view class="lesson-title">{{ currentLesson.title }}</view>
					<view class="lesson-meta">
						<text class="lesson-duration">{{ formatDuration(currentLesson.duration) }}</text>
						<uni-tag
							v-if="currentLesson.is_free"
							text="免费"
							type="success"
							size="mini"
						></uni-tag>
						<uni-tag
							v-else
							text="付费"
							type="warning"
							size="mini"
						></uni-tag>
						<text class="lesson-progress">第{{ currentLessonIndex + 1 }}课时/共{{ lessons.length }}课时</text>
					</view>

					<!-- 退出播放模式按钮 -->
					<view class="exit-player-btn" v-if="isVideoPlaying" @click="exitVideoMode">
						<uni-icons type="close" size="20" color="#666"></uni-icons>
					</view>
				</view>

				<!-- 视频播放器 -->
				<video
					id="lesson-video"
					class="video-player"
					:class="{ 'fullscreen-mode': isVideoPlaying }"
					:src="currentLesson.video_url"
					:poster="currentLesson.thumbnail || courseInfo.thumbnail"
					controls
					:show-fullscreen-btn="true"
					:show-play-btn="true"
					:show-center-play-btn="true"
					@timeupdate="handleTimeUpdate"
					@ended="handleVideoEnded"
					@play="handleVideoPlay"
					@pause="handleVideoPause"
					@click="handleVideoClick"
				></video>

				<!-- 播放控制 -->
				<view class="video-controls" :class="{ 'compact-mode': isVideoPlaying }">
					<button
						class="control-btn"
						:disabled="currentLessonIndex <= 0"
						@click="playPreviousLesson"
					>
						<uni-icons type="left" size="16"></uni-icons>
						<text v-if="!isVideoPlaying">上一课时</text>
					</button>
					<button
						class="control-btn primary"
						@click="togglePlayPause"
					>
						<uni-icons :type="isPlaying ? 'pause' : 'play'" size="16"></uni-icons>
						<text v-if="!isVideoPlaying">{{ isPlaying ? '暂停' : '播放' }}</text>
					</button>
					<button
						class="control-btn"
						:disabled="currentLessonIndex >= lessons.length - 1"
						@click="playNextLesson"
					>
						<text v-if="!isVideoPlaying">下一课时</text>
						<uni-icons type="right" size="16"></uni-icons>
					</button>
				</view>
			</view>
			
			<!-- 课程信息 -->
			<view class="course-info" :class="{ 'compact-mode': isVideoPlaying }">
				<view class="course-header">
					<text class="course-title">{{ courseInfo.title }}</text>
					<uni-tag
						v-if="courseInfo.user_info && courseInfo.user_info.status"
						:text="getStatusText(courseInfo.user_info.status, courseInfo.user_info.is_expired)"
						:type="getStatusColor(courseInfo.user_info.status, courseInfo.user_info.is_expired)"
					></uni-tag>
				</view>

				<view class="course-meta">
					<view class="meta-item">
						<uni-icons type="clock" size="14" color="#999" class="meta-icon"></uni-icons>
						<text class="meta-text">{{ courseInfo.duration_formatted || '未知时长' }}</text>
					</view>
					<view class="meta-item">
						<uni-icons type="calendar" size="14" color="#999" class="meta-icon"></uni-icons>
						<text class="meta-text">{{ formatDate(courseInfo.created_at) }}</text>
					</view>
				</view>

				<!-- 继续学习按钮区域 -->
				<view class="learning-action" v-if="!isVideoPlaying">
					<button
						class="learning-btn primary"
						v-if="hasAccess && courseInfo.video_url"
						@click="startLearning"
					>
						<uni-icons type="play-filled" size="16" color="#fff"></uni-icons>
						{{ courseInfo.user_info && courseInfo.user_info.watch_progress > 0 ? '继续学习' : '开始学习' }}
					</button>
					<button
						class="learning-btn secondary"
						v-else-if="!isLoggedIn"
						@click="goToLogin"
					>
						<uni-icons type="person" size="16" color="#fff"></uni-icons>
						登录后学习
					</button>
					<button
						class="learning-btn primary"
						v-else-if="!hasAccess && !courseInfo.is_free && courseInfo.is_on_sale"
						@click="buyCourse"
					>
						<uni-icons type="wallet" size="16" color="#fff"></uni-icons>
						立即购买 ¥{{ courseInfo.price }}
					</button>
					<button
						class="learning-btn disabled"
						v-else
						disabled
					>
						<uni-icons type="info" size="16" color="#6c757d"></uni-icons>
						{{ courseInfo.is_free ? '免费课程' : (courseInfo.is_on_sale ? '暂无访问权限' : '暂未上架') }}
					</button>
				</view>
				
				<!-- 学习进度 -->
				<view class="progress-section" v-if="courseInfo.user_info && !isVideoPlaying">
					<view class="progress-header">
						<text class="progress-title">学习进度</text>
						<text class="progress-percent">{{ courseInfo.user_info.watch_progress.toFixed(1) }}%</text>
					</view>
					<view class="progress-bar">
						<view
							class="progress-fill"
							:style="{ width: courseInfo.user_info.watch_progress + '%' }"
						></view>
					</view>
					<view class="progress-stats">
						<text class="stat-text">观看次数: {{ courseInfo.user_info.watch_count }}</text>
						<text class="stat-text" v-if="courseInfo.user_info.last_watched_at">
							最后学习: {{ formatDate(courseInfo.user_info.last_watched_at) }}
						</text>
					</view>
				</view>

				<!-- 课程描述 -->
				<view class="description-section" v-if="!isVideoPlaying">
					<text class="section-title">课程介绍</text>
					<text class="description-text">{{ courseInfo.description || '暂无课程介绍' }}</text>
				</view>

				<!-- 课时列表 -->
				<view class="lessons-section" v-if="!isVideoPlaying">
					<view class="section-header">
						<text class="section-title">课程目录</text>
						<text class="lesson-count" v-if="lessons.length > 0">共{{ lessons.length }}课时</text>
						<text class="lesson-count" v-else>暂无课时</text>
					</view>

					<!-- 课时统计信息 -->
					<view class="lesson-stats" v-if="lessons.length > 0">
						<view class="stat-item">
							<text class="stat-label">总课时：</text>
							<text class="stat-value">{{ lessons.length }}节</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">总时长：</text>
							<text class="stat-value">{{ getTotalDuration() }}</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">免费课时：</text>
							<text class="stat-value">{{ getFreeCount() }}节</text>
						</view>
					</view>

					<!-- 课时列表 -->
					<view class="lessons-list" v-if="lessons.length > 0">
						<view
							class="lesson-item"
							v-for="(lesson, index) in lessons"
							:key="lesson.id"
							@click="selectLesson(lesson, index)"
							:class="{ 'active': currentLessonIndex === index, 'locked': !hasLessonAccess(lesson) }"
						>
							<view class="lesson-number">{{ index + 1 }}</view>
							<view class="lesson-content">
								<view class="lesson-title">{{ lesson.title }}</view>
								<view class="lesson-meta">
									<text class="lesson-duration" v-if="lesson.duration_formatted">
										{{ lesson.duration_formatted }}
									</text>
									<uni-tag
										v-if="lesson.is_free"
										text="免费"
										type="success"
										size="mini"
									></uni-tag>
									<uni-tag
										v-else
										text="付费"
										type="warning"
										size="mini"
									></uni-tag>
									<!-- 权限状态 -->
									<uni-tag
										v-if="!hasLessonAccess(lesson)"
										text="需购买"
										type="error"
										size="mini"
									></uni-tag>
								</view>
								<view class="lesson-description" v-if="lesson.description">
									{{ lesson.description }}
								</view>
							</view>
							<view class="lesson-status">
								<uni-icons
									v-if="currentLessonIndex === index"
									type="play-filled"
									size="20"
									color="#007bff"
								></uni-icons>
								<uni-icons
									v-else-if="getLessonProgress(lesson.id) && getLessonProgress(lesson.id).is_completed"
									type="checkmarkempty"
									size="20"
									color="#4cd964"
								></uni-icons>
								<uni-icons
									v-else-if="getLessonProgress(lesson.id) && getLessonProgress(lesson.id).completion_rate > 0"
									type="loop"
									size="20"
									color="#ff9500"
								></uni-icons>
								<uni-icons
									v-else-if="hasLessonAccess(lesson)"
									type="play"
									size="16"
									color="#c7c7cc"
								></uni-icons>
								<uni-icons
									v-else
									type="locked"
									size="16"
									color="#f56c6c"
								></uni-icons>
							</view>
						</view>

						<!-- 空状态 -->
						<view class="empty-lessons" v-if="lessons.length === 0">
							<view class="empty-icon">📚</view>
							<text class="empty-text">暂无课时内容</text>
							<text class="empty-desc">课程正在准备中，请稍后查看</text>
						</view>
					</view>
				</view>
				
				<!-- 权限信息 -->
				<view class="access-section" v-if="courseInfo.user_info">
					<text class="section-title">权限信息</text>
					<view class="access-info">
						<view class="access-item">
							<text class="access-label">{{ courseInfo.user_info.purchase_type === 'purchased' ? '购买时间' : '获得时间' }}:</text>
							<text class="access-value">{{ formatDate(courseInfo.user_info.assigned_at) }}</text>
						</view>
						<view class="access-item" v-if="courseInfo.user_info.expires_at">
							<text class="access-label">过期时间:</text>
							<text class="access-value">{{ formatDate(courseInfo.user_info.expires_at) }}</text>
						</view>
					</view>
				</view>
			</view>
			

		</view>
		
		<!-- 错误状态 -->
		<view class="error-state" v-else-if="error">
			<uni-icons type="info" size="40" color="#ff6b6b" class="error-icon"></uni-icons>
			<text class="error-text">{{ error }}</text>
			<button class="retry-btn" @click="loadCourseDetail">重试</button>
		</view>
	</view>
</template>

<script>
	import { isLoggedIn, formatDate, showSuccess, showError, showConfirm } from '../../utils/storage.js';
	import { authMixin } from '../../utils/auth.js';
	import { getCourseDetail, updateCourseProgress, getLessonList, getCourseProgress } from '../../api/course.js';
	import { createOrder, wechatPrepay } from '../../api/order.js';
	
	export default {
		mixins: [authMixin],
		
		data() {
			return {
				loading: false,
				courseId: null,
				courseInfo: null,
				error: null,
				isLoggedIn: false,
				hasAccess: false,

				// 课时相关
				lessons: [],
				currentLesson: null,
				currentLessonIndex: -1,
				currentLessonId: null,
				lessonsProgress: {},
				lessonError: null,
				lessonLoading: false,

				// 视频播放相关
				currentTime: 0,
				duration: 0,
				isPlaying: false,
				isVideoPlaying: false, // 新增：视频播放模式状态
				lastUpdateTime: 0
			};
		},
		
		onLoad(options) {
			if (options.id) {
				this.courseId = parseInt(options.id);
				this.initPage();
			} else {
				this.error = '课程ID无效';
			}
		},
		
		onShow() {
			this.checkLoginStatus();
		},
		
		onUnload() {
			// 页面卸载时保存进度
			this.saveProgress();
		},
		
		methods: {
			/**
			 * 初始化页面
			 */
			initPage() {
				this.checkLoginStatus();
				this.loadCourseDetail();
				this.loadLessonList();
				if (this.isLoggedIn) {
					this.loadCourseProgress();
				}
			},

			/**
			 * 获取总时长
			 */
			getTotalDuration() {
				if (!this.lessons || this.lessons.length === 0) return '0分钟';

				const totalSeconds = this.lessons.reduce((total, lesson) => {
					return total + (lesson.duration || 0);
				}, 0);

				if (totalSeconds < 60) {
					return `${totalSeconds}秒`;
				} else if (totalSeconds < 3600) {
					const minutes = Math.floor(totalSeconds / 60);
					return `${minutes}分钟`;
				} else {
					const hours = Math.floor(totalSeconds / 3600);
					const minutes = Math.floor((totalSeconds % 3600) / 60);
					return `${hours}小时${minutes}分钟`;
				}
			},

			/**
			 * 获取免费课时数量
			 */
			getFreeCount() {
				if (!this.lessons || this.lessons.length === 0) return 0;
				return this.lessons.filter(lesson => lesson.is_free === 1).length;
			},

			/**
			 * 检查课时访问权限
			 */
			hasLessonAccess(lesson) {
				// 如果是免费课时，直接允许访问
				if (lesson.is_free === 1) {
					return true;
				}

				// 如果用户未登录，不允许访问付费课时
				if (!this.isLoggedIn) {
					return false;
				}

				// 如果用户有课程访问权限，允许访问所有课时
				if (this.hasAccess) {
					return true;
				}

				// 其他情况不允许访问
				return false;
			},

			/**
			 * 格式化时长
			 */
			formatDuration(seconds) {
				if (!seconds || seconds <= 0) return '未知时长';

				const hours = Math.floor(seconds / 3600);
				const minutes = Math.floor((seconds % 3600) / 60);
				const secs = seconds % 60;

				if (hours > 0) {
					return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
				} else {
					return `${minutes}:${secs.toString().padStart(2, '0')}`;
				}
			},
			
			/**
			 * 检查登录状态
			 */
			checkLoginStatus() {
				this.isLoggedIn = isLoggedIn();
			},
			
			/**
			 * 加载课程详情
			 */
			async loadCourseDetail() {
				if (!this.courseId) return;
				
				this.loading = true;
				this.error = null;
				
				try {
					const response = await getCourseDetail(this.courseId);
					if (response.code === 200) {
						this.courseInfo = response.data;

						// 判断用户是否有访问权限
						// 1. 如果是免费课程且用户已登录，则有访问权限
						// 2. 如果用户有课程权限记录且有访问权限，则有访问权限
						this.hasAccess = (this.courseInfo.is_free && this.isLoggedIn) ||
										 (this.courseInfo.user_info && this.courseInfo.user_info.has_access);

						// 设置页面标题
						uni.setNavigationBarTitle({
							title: this.courseInfo.title
						});
					} else {
						this.error = response.message || '加载课程详情失败';
					}
				} catch (error) {
					console.error('加载课程详情失败:', error);
					this.error = error.message || '网络错误，请稍后重试';
				} finally {
					this.loading = false;
				}
			},
			
			/**
			 * 开始学习
			 */
			startLearning() {
				// 如果没有选择课时，默认选择第一个有权限的课时
				if (!this.currentLesson && this.lessons.length > 0) {
					// 查找第一个有权限的课时
					const firstAccessibleLesson = this.lessons.find((lesson) => {
						return this.hasLessonAccess(lesson);
					});

					if (firstAccessibleLesson) {
						const index = this.lessons.indexOf(firstAccessibleLesson);
						this.selectLesson(firstAccessibleLesson, index);
					} else {
						uni.showToast({
							title: '没有可播放的课时',
							icon: 'none'
						});
					}
				} else if (this.currentLesson) {
					// 跳转到播放器页面
					uni.navigateTo({
						url: `/pages/lessons/player?id=${this.currentLesson.id}&course_id=${this.courseId}&title=${encodeURIComponent(this.currentLesson.title)}`
					});
				} else {
					uni.showToast({
						title: '请先选择课时',
						icon: 'none'
					});
				}
			},

				/**
				 * 原来的开始学习方法（备份）
				 */
				_startLearningOld() {
					// 保留原来的逻辑作为备份
					if (!this.isPlaying) {
						setTimeout(() => {
							this.togglePlayPause();
						}, 500);
					}
				},
			
			/**
			 * 视频时间更新
			 */
			handleTimeUpdate(e) {
				this.currentTime = e.detail.currentTime;
				this.duration = e.detail.duration;
				
				// 每10秒更新一次进度
				const now = Date.now();
				if (now - this.lastUpdateTime > 10000) {
					this.updateProgress();
					this.lastUpdateTime = now;
				}
			},
			
			/**
			 * 视频播放结束
			 */
			handleVideoEnded() {
				this.isPlaying = false;
				this.updateProgress();

				// 检查是否有下一课时
				if (this.currentLessonIndex < this.lessons.length - 1) {
					uni.showModal({
						title: '课时完成',
						content: '当前课时已完成，是否继续播放下一课时？',
						showCancel: true,
						cancelText: '稍后',
						confirmText: '继续',
						success: (res) => {
							if (res.confirm) {
								this.playNextLesson();
							} else {
								uni.showToast({
									title: '课时学习完成！',
									icon: 'success'
								});
							}
						}
					});
				} else {
					uni.showToast({
						title: '恭喜！课程学习完成！',
						icon: 'success'
					});
				}
			},
			
			/**
			 * 视频开始播放
			 */
			handleVideoPlay() {
				this.isPlaying = true;
				// 延迟激活视频播放模式，给用户一些时间适应
				setTimeout(() => {
					this.isVideoPlaying = true;
				}, 2000);
			},

			/**
			 * 视频暂停
			 */
			handleVideoPause() {
				this.isPlaying = false;
				this.updateProgress();
			},

			/**
			 * 处理视频点击事件
			 */
			handleVideoClick() {
				// 当用户点击视频播放器时，激活视频播放模式
				if (this.isPlaying) {
					this.isVideoPlaying = true;
				}
			},

			/**
			 * 退出视频播放模式
			 */
			exitVideoMode() {
				this.isVideoPlaying = false;
				// 滚动到视频区域顶部
				uni.pageScrollTo({
					selector: '.video-section',
					duration: 300
				});
			},
			
			/**
			 * 更新学习进度
			 */
			async updateProgress() {
				if (!this.hasAccess || !this.duration || this.duration <= 0) return;
				
				const progress = (this.currentTime / this.duration) * 100;
				
				try {
					await updateCourseProgress(this.courseId, {
						watchTime: Math.floor(this.currentTime),
						progressPosition: Math.floor(this.currentTime),
						watchProgress: Math.min(progress, 100)
					});
					
					// 更新本地进度显示
					if (this.courseInfo.user_info) {
						this.courseInfo.user_info.watch_progress = Math.min(progress, 100);
						this.courseInfo.user_info.watch_count = (this.courseInfo.user_info.watch_count || 0) + 1;
					}
				} catch (error) {
					console.error('更新学习进度失败:', error);
				}
			},
			
			/**
			 * 保存进度
			 */
			saveProgress() {
				if (this.isPlaying || this.currentTime > 0) {
					this.updateProgress();
				}
			},
			
			/**
			 * 跳转到登录页
			 */
			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/login'
				});
			},

			/**
			 * 购买课程
			 */
			async buyCourse() {
				if (!this.isLoggedIn) {
					this.goToLogin();
					return;
				}

				if (!this.courseInfo || this.courseInfo.is_free) {
					showError('课程信息错误');
					return;
				}

				const confirmed = await showConfirm(
					`确定要购买课程《${this.courseInfo.title}》吗？\n价格：¥${this.courseInfo.price}`
				);
				if (!confirmed) return;

				try {
					uni.showLoading({
						title: '正在创建订单...'
					});

					// 创建订单
					const orderResponse = await createOrder([this.courseInfo.id]);

					console.log('订单创建响应:', orderResponse);

					if (orderResponse.code !== 200) {
						const errorMsg = orderResponse.message || '创建订单失败';
						console.error('创建订单失败:', errorMsg);
						showError(errorMsg);
						return;
					}

					const order = orderResponse.data;
					if (!order || !order.order_id) {
						console.error('订单数据异常:', order);
						showError('订单数据异常，请重试');
						return;
					}

					// 获取用户openid
					let openid = uni.getStorageSync('user_openid');
					if (!openid) {
						console.log('本地没有openid，尝试从API获取');
						try {
							// 从API获取openid
							const openidResponse = await uni.request({
								url: 'https://wx.yx420.cn/api/user-openid.php',
								method: 'GET',
								header: {
									'Authorization': `Bearer ${uni.getStorageSync('access_token')}`
								}
							});

							if (openidResponse.data && openidResponse.data.code === 200) {
								openid = openidResponse.data.data.openid;
								// 保存到本地
								uni.setStorageSync('user_openid', openid);
								console.log('从API获取到openid:', openid);
							}
						} catch (error) {
							console.error('从API获取openid失败:', error);
						}
					}

					if (!openid) {
						console.error('用户openid不存在，可能是账号密码登录用户');

						// 询问用户是否要继续（用于测试）
						const continueWithoutWechat = await showConfirm(
							'检测到您使用账号密码登录，无法使用微信支付。\n' +
							'是否继续创建订单？（订单将创建但需要其他方式完成支付）'
						);

						if (!continueWithoutWechat) {
							return;
						}

						// 为账号密码登录用户生成一个临时openid用于测试
						openid = 'temp_openid_' + Date.now();
						console.log('使用临时openid进行测试:', openid);
					}

					uni.showLoading({
						title: '正在创建支付订单...'
					});

					// 创建微信支付订单
					const payResponse = await wechatPrepay(order.order_id, openid);

					console.log('支付预下单响应:', payResponse);

					if (payResponse.code !== 200) {
						const errorMsg = payResponse.user_message || payResponse.message || '创建支付订单失败';
						console.error('创建支付订单失败:', payResponse);

						// 如果是账号密码登录用户，提供测试支付选项
						if (payResponse.code === 400 && payResponse.user_message && payResponse.user_message.includes('账号密码登录')) {
							const useTestPayment = await showConfirm(
								errorMsg + '\n\n' +
								'是否使用测试支付完成购买？\n' +
								'（仅用于开发测试，课程将直接分配到您的账户）'
							);

							if (useTestPayment) {
								await this.completeTestPayment(order.order_id);
								return;
							}
						} else {
							// 如果有建议信息，显示更详细的提示
							if (payResponse.suggestion) {
								showError(errorMsg + '\n\n' + payResponse.suggestion);
							} else {
								showError(errorMsg);
							}
						}
						return;
					}

					const payParams = payResponse.data?.pay_params;
					if (!payParams) {
						console.error('支付参数异常:', payResponse.data);
						showError('支付参数异常，请重试');
						return;
					}

					// 调用微信支付
					uni.requestPayment({
						...payParams,
						success: () => {
							showSuccess('支付成功');
							// 刷新课程信息
							this.loadCourseDetail();
						},
						fail: (err) => {
							console.error('支付失败:', err);
							if (err.errMsg !== 'requestPayment:fail cancel') {
								showError('支付失败: ' + (err.errMsg || '未知错误'));
							}
						}
					});

				} catch (error) {
					console.error('购买课程失败:', error);
					let errorMessage = '购买课程失败';

					if (error && error.message) {
						errorMessage = error.message;
					} else if (error && error.code) {
						errorMessage = `购买课程失败 (错误码: ${error.code})`;
					}

					showError(errorMessage);
				} finally {
					uni.hideLoading();
				}
			},
			
			/**
			 * 获取状态文本
			 */
			getStatusText(status, isExpired) {
				if (status === 'active' && !isExpired) return '进行中';
				if (status === 'active' && isExpired) return '已过期';
				if (status === 'expired') return '已过期';
				if (status === 'revoked') return '已撤销';
				return '未知';
			},
			
			/**
			 * 获取状态颜色
			 */
			getStatusColor(status, isExpired) {
				if (status === 'active' && !isExpired) return 'success';
				if (status === 'active' && isExpired) return 'warning';
				if (status === 'expired') return 'warning';
				if (status === 'revoked') return 'error';
				return 'default';
			},
			
			/**
			 * 完成测试支付
			 */
			async completeTestPayment(orderId) {
				try {
					uni.showLoading({
						title: '正在处理测试支付...'
					});

					const response = await uni.request({
						url: 'https://wx.yx420.cn/api/payment-test-complete.php',
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'Authorization': `Bearer ${uni.getStorageSync('access_token')}`
						},
						data: {
							order_id: orderId
						}
					});

					console.log('测试支付响应:', response.data);

					if (response.data && response.data.code === 200) {
						showSuccess('测试支付成功！课程已添加到您的账户');
						// 刷新课程信息
						this.loadCourseDetail();
					} else {
						const errorMsg = response.data?.message || '测试支付失败';
						showError(errorMsg);
					}
				} catch (error) {
					console.error('测试支付失败:', error);
					showError('测试支付失败，请稍后重试');
				} finally {
					uni.hideLoading();
				}
			},

			/**
			 * 格式化日期
			 */
			formatDate(date) {
				return formatDate(date, 'YYYY-MM-DD HH:mm');
			},

			/**
			 * 加载课时列表
			 */
			async loadLessonList() {
				if (!this.courseId) {
					console.error('课程ID为空，无法加载课时列表');
					return;
				}

				this.lessonLoading = true;
				this.lessonError = null;

				try {
					console.log('开始加载课时列表，课程ID:', this.courseId);
					const response = await getLessonList(this.courseId);
					console.log('课时列表API响应:', response);

					if (response && response.success) {
						this.lessons = response.data?.lessons || [];
						this.lessonError = null;
						console.log('课时列表加载成功，数量:', this.lessons.length);

						// 如果有课时数据，格式化一下
						this.lessons = this.lessons.map(lesson => ({
							...lesson,
							duration: parseInt(lesson.duration) || 0,
							is_free: parseInt(lesson.is_free) || 0,
							status: parseInt(lesson.status) || 0
						}));

					} else {
						const errorMsg = response?.message || '未知错误';
						console.error('加载课时列表失败:', errorMsg);
						this.lessonError = errorMsg;
						this.lessons = [];
					}
				} catch (error) {
					console.error('加载课时列表异常:', error);
					this.lessonError = error.message || '网络请求失败';
					this.lessons = [];
				} finally {
					this.lessonLoading = false;
				}
			},

			/**
			 * 加载课程观看进度
			 */
			async loadCourseProgress() {
				try {
					const response = await getCourseProgress(this.courseId);
					if (response.success && response.data.lessons_progress) {
						// 将进度数据转换为以lesson_id为key的对象
						this.lessonsProgress = {};
						response.data.lessons_progress.forEach(progress => {
							this.lessonsProgress[progress.lesson_id] = progress;
						});
					}
				} catch (error) {
					console.error('加载课程进度异常:', error);
				}
			},

			/**
			 * 获取课时观看进度
			 */
			getLessonProgress(lessonId) {
				return this.lessonsProgress[lessonId] || null;
			},

			/**
			 * 选择课时
			 */
			selectLesson(lesson, index) {
				// 检查是否有权限观看
				if (!this.hasLessonAccess(lesson)) {
					if (lesson.is_free === 0) {
						uni.showModal({
							title: '需要购买课程',
							content: '该课时为付费内容，请先购买课程后观看',
							showCancel: true,
							cancelText: '取消',
							confirmText: '立即购买',
							success: (res) => {
								if (res.confirm) {
									this.buyCourse();
								}
							}
						});
					} else {
						uni.showToast({
							title: '请先登录',
							icon: 'none'
						});
					}
					return;
				}

				// 跳转到新的播放器页面
				uni.navigateTo({
					url: `/pages/lessons/player?id=${lesson.id}&course_id=${this.courseId}&title=${encodeURIComponent(lesson.title)}`
				});
			},

			/**
			 * 播放上一课时
			 */
			playPreviousLesson() {
				if (this.currentLessonIndex <= 0) return;

				const prevIndex = this.currentLessonIndex - 1;
				const prevLesson = this.lessons[prevIndex];
				this.selectLesson(prevLesson, prevIndex);
			},

			/**
			 * 播放下一课时
			 */
			playNextLesson() {
				if (this.currentLessonIndex >= this.lessons.length - 1) return;

				const nextIndex = this.currentLessonIndex + 1;
				const nextLesson = this.lessons[nextIndex];
				this.selectLesson(nextLesson, nextIndex);
			},

			/**
			 * 切换播放/暂停
			 */
			togglePlayPause() {
				const videoContext = uni.createVideoContext('lesson-video');
				if (this.isPlaying) {
					videoContext.pause();
				} else {
					videoContext.play();
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
.course-detail-container {
	background: #f8f9fa;
	min-height: 100vh;
}

.loading-container {
	padding: 60px 20px;
	text-align: center;
}

.video-section {
	background: #000;
	margin-bottom: 20px;
	transition: all 0.3s ease;

	.lesson-info {
		background: #fff;
		padding: 15px;
		border-bottom: 1px solid #eee;
		position: relative;
		transition: all 0.3s ease;

		&.compact-mode {
			padding: 10px 15px;

			.lesson-title {
				font-size: 14px;
			}
		}

		.lesson-title {
			font-size: 16px;
			font-weight: 500;
			color: #333;
			margin-bottom: 8px;
			transition: font-size 0.3s ease;
		}

		.lesson-meta {
			display: flex;
			align-items: center;
			gap: 8px;
			font-size: 12px;
			color: #666;

			.lesson-duration {
				color: #999;
			}

			.lesson-progress {
				color: #007bff;
				font-weight: 500;
			}
		}

		.exit-player-btn {
			position: absolute;
			top: 15px;
			right: 15px;
			width: 30px;
			height: 30px;
			background: rgba(0, 0, 0, 0.1);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			transition: all 0.3s ease;

			&:active {
				background: rgba(0, 0, 0, 0.2);
				transform: scale(0.95);
			}
		}
	}

	.video-player {
		width: 100%;
		height: 210px;
		transition: all 0.3s ease;

		&.fullscreen-mode {
			height: 280px;
		}
	}

	.video-controls {
		background: #fff;
		padding: 15px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-top: 1px solid #eee;
		transition: all 0.3s ease;

		&.compact-mode {
			padding: 10px 15px;

			.control-btn {
				padding: 6px 10px;
				margin: 0 3px;
				min-width: 40px;

				text {
					display: none;
				}
			}
		}

		.control-btn {
			flex: 1;
			margin: 0 5px;
			padding: 8px 12px;
			border: 1px solid #ddd;
			border-radius: 4px;
			background: #fff;
			color: #333;
			font-size: 14px;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 4px;
			transition: all 0.3s ease;

			&:disabled {
				opacity: 0.5;
				color: #999;
			}

			&.primary {
				background: #007bff;
				color: #fff;
				border-color: #007bff;
			}

			&:not(:disabled):active {
				opacity: 0.8;
				transform: scale(0.98);
			}
		}
	}
}

.course-info {
	background: #fff;
	padding: 20px;
	transition: all 0.3s ease;

	&.compact-mode {
		padding: 10px 20px;

		.course-header .course-title {
			font-size: 16px;
		}

		.course-meta {
			margin-bottom: 10px;
		}
	}

	.course-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 15px;

		.course-title {
			flex: 1;
			font-size: 20px;
			font-weight: 600;
			color: #333;
			line-height: 1.4;
			margin-right: 15px;
			transition: font-size 0.3s ease;
		}
	}

	.course-meta {
		display: flex;
		gap: 20px;
		margin-bottom: 20px;
		transition: margin 0.3s ease;

		.meta-item {
			display: flex;
			align-items: center;
			gap: 5px;

			.meta-icon {
				margin-right: 3px;
			}

			.meta-text {
				font-size: 14px;
				color: #666;
			}
		}
	}

	.learning-action {
		margin-top: 20px;

		.learning-btn {
			width: 100%;
			height: 50px;
			border-radius: 25px;
			font-size: 16px;
			font-weight: 500;
			border: none;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 8px;
			transition: all 0.3s ease;

			&.primary {
				background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
				color: #fff;

				&:active {
					transform: scale(0.98);
				}
			}

			&.secondary {
				background: #6c757d;
				color: #fff;

				&:active {
					transform: scale(0.98);
				}
			}

			&.disabled {
				background: #e9ecef;
				color: #6c757d;
			}
		}
	}
}

.progress-section {
	background: #fff;
	margin-top: 10px;
	padding: 20px;
	border-top: 1px solid #f0f0f0;

	.progress-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;

		.progress-title {
			font-size: 16px;
			font-weight: 500;
			color: #333;
		}

		.progress-percent {
			font-size: 16px;
			font-weight: 600;
			color: #007bff;
		}
	}

	.progress-bar {
		height: 8px;
		background: #e9ecef;
		border-radius: 4px;
		overflow: hidden;
		margin-bottom: 10px;

		.progress-fill {
			height: 100%;
			background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
			border-radius: 4px;
			transition: width 0.3s ease;
		}
	}

	.progress-stats {
		display: flex;
		justify-content: space-between;

		.stat-text {
			font-size: 12px;
			color: #999;
		}
	}
}

.description-section, .access-section {
	background: #fff;
	margin-top: 10px;
	padding: 20px;
	border-top: 1px solid #f0f0f0;

	.section-title {
		display: block;
		font-size: 16px;
		font-weight: 500;
		color: #333;
		margin-bottom: 15px;
	}

	.description-text {
		font-size: 14px;
		color: #666;
		line-height: 1.6;
	}
}

.access-info {
	.access-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 8px 0;
		border-bottom: 1px solid #f8f9fa;

		&:last-child {
			border-bottom: none;
		}

		.access-label {
			font-size: 14px;
			color: #666;
		}

		.access-value {
			font-size: 14px;
			color: #333;
		}
	}
}

.lessons-section {
	background: #fff;
	margin-top: 10px;
	padding: 20px;
	border-top: 1px solid #f0f0f0;

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;

		.section-title {
			font-size: 16px;
			font-weight: 500;
			color: #333;
		}

		.lesson-count {
			font-size: 12px;
			color: #999;
		}
	}

	.lesson-stats {
		display: flex;
		justify-content: space-around;
		background: #f8f9fa;
		border-radius: 8px;
		padding: 12px;
		margin-bottom: 15px;

		.stat-item {
			text-align: center;
			flex: 1;

			.stat-label {
				font-size: 12px;
				color: #666;
				display: block;
				margin-bottom: 4px;
			}

			.stat-value {
				font-size: 14px;
				font-weight: 500;
				color: #333;
			}
		}
	}

	.lessons-list {
		.lesson-item {
			display: flex;
			align-items: flex-start;
			padding: 15px 0;
			border-bottom: 1px solid #f8f9fa;
			transition: background-color 0.2s ease;

			&:last-child {
				border-bottom: none;
			}

			&:active {
				background-color: #f8f9fa;
			}

			&.active {
				background-color: #e3f2fd;
				border-left: 3px solid #007bff;
				padding-left: 12px;
			}

			&.locked {
				opacity: 0.6;
				background-color: #f5f5f5;
			}

			.lesson-number {
				width: 30px;
				height: 30px;
				background: #f8f9fa;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 12px;
				font-weight: 500;
				color: #666;
				margin-right: 12px;
				flex-shrink: 0;
			}

			.lesson-content {
				flex: 1;
				min-width: 0;

				.lesson-title {
					font-size: 14px;
					font-weight: 500;
					color: #333;
					margin-bottom: 5px;
					line-height: 1.4;
				}

				.lesson-meta {
					display: flex;
					align-items: center;
					gap: 8px;
					margin-bottom: 5px;

					.lesson-duration {
						font-size: 12px;
						color: #999;
					}
				}

				.lesson-description {
					font-size: 12px;
					color: #999;
					line-height: 1.4;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
				}
			}

			.lesson-status {
				margin-left: 10px;
				flex-shrink: 0;
			}
		}
	}

	.empty-lessons {
		text-align: center;
		padding: 40px 20px;

		.empty-icon {
			font-size: 48px;
			margin-bottom: 16px;
		}

		.empty-text {
			display: block;
			font-size: 16px;
			color: #333;
			margin-bottom: 8px;
		}

		.empty-desc {
			display: block;
			font-size: 14px;
			color: #999;
		}
	}

	.debug-info {
		background: #f0f0f0;
		padding: 10px;
		margin: 10px 0;
		font-size: 12px;
		border-radius: 4px;

		text {
			display: block;
			margin-bottom: 4px;
		}
	}
}



.error-state {
	text-align: center;
	padding: 60px 20px;

	.error-icon {
		margin-bottom: 15px;
	}

	.error-text {
		display: block;
		font-size: 16px;
		color: #999;
		margin: 20px 0;
	}

	.retry-btn {
		background: #007bff;
		color: #fff;
		border: none;
		border-radius: 20px;
		padding: 10px 30px;
		font-size: 14px;
	}
}
</style>
