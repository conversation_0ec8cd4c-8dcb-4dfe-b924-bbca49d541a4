<template>
	<view class="lesson-list-container">
		<!-- 课时列表头部 -->
		<view class="lesson-header" v-if="showHeader">
			<view class="header-title">
				<text class="title-text">课时列表</text>
				<text class="lesson-count">({{ lessons.length }})</text>
			</view>
			<view class="header-stats" v-if="statistics">
				<text class="stat-item">总时长: {{ formatTotalDuration(statistics.total_duration) }}</text>
				<text class="stat-item">免费: {{ statistics.free_lessons }}</text>
			</view>
		</view>
		
		<!-- 课时列表 -->
		<view class="lesson-list">
			<view 
				class="lesson-item" 
				v-for="(lesson, index) in lessons" 
				:key="lesson.id"
				@click="handleLessonClick(lesson, index)"
			>
				<!-- 课时序号 -->
				<view class="lesson-number">
					<text class="number-text">{{ lesson.sort_order || (index + 1) }}</text>
				</view>
				
				<!-- 课时内容 -->
				<view class="lesson-content">
					<view class="lesson-title">{{ lesson.title }}</view>
					<view class="lesson-meta">
						<view class="meta-left">
							<text class="lesson-duration">{{ formatDuration(lesson.duration) }}</text>
							<uni-tag
								v-if="lesson.is_free"
								text="免费"
								type="success"
								size="mini"
							></uni-tag>
							<uni-tag
								v-else
								text="付费"
								type="warning"
								size="mini"
							></uni-tag>
							<uni-tag
								v-if="lesson.video_type === 'vod'"
								text="高清"
								type="primary"
								size="mini"
							></uni-tag>
						</view>
						<view class="meta-right">
							<uni-icons 
								v-if="hasAccess(lesson)"
								type="play" 
								size="16" 
								color="#007aff"
							></uni-icons>
							<uni-icons 
								v-else
								type="locked" 
								size="16" 
								color="#999"
							></uni-icons>
						</view>
					</view>
					
					<!-- 课时描述 -->
					<view class="lesson-description" v-if="lesson.description && showDescription">
						<text class="description-text">{{ lesson.description }}</text>
					</view>
					
					<!-- 学习进度 -->
					<view class="lesson-progress" v-if="lesson.progress && showProgress">
						<view class="progress-bar">
							<view 
								class="progress-fill" 
								:style="{ width: lesson.progress.completion_rate + '%' }"
							></view>
						</view>
						<text class="progress-text">{{ Math.floor(lesson.progress.completion_rate) }}%</text>
					</view>
				</view>
				
				<!-- 课时状态 -->
				<view class="lesson-status">
					<uni-icons 
						v-if="lesson.progress && lesson.progress.is_completed"
						type="checkmarkempty" 
						size="20" 
						color="#4cd964"
					></uni-icons>
					<uni-icons 
						v-else-if="lesson.progress && lesson.progress.completion_rate > 0"
						type="play-filled" 
						size="20" 
						color="#ff9500"
					></uni-icons>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="lessons.length === 0 && !loading">
			<uni-icons type="videocam" size="60" color="#ccc"></uni-icons>
			<text class="empty-text">暂无课时内容</text>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<uni-load-more status="loading"></uni-load-more>
		</view>
	</view>
</template>

<script>
export default {
	name: 'LessonList',
	props: {
		// 课时列表数据
		lessons: {
			type: Array,
			default: () => []
		},
		
		// 课程ID
		courseId: {
			type: Number,
			default: 0
		},
		
		// 统计信息
		statistics: {
			type: Object,
			default: null
		},
		
		// 是否显示头部
		showHeader: {
			type: Boolean,
			default: true
		},
		
		// 是否显示描述
		showDescription: {
			type: Boolean,
			default: false
		},
		
		// 是否显示进度
		showProgress: {
			type: Boolean,
			default: true
		},
		
		// 加载状态
		loading: {
			type: Boolean,
			default: false
		},
		
		// 用户权限检查函数
		accessChecker: {
			type: Function,
			default: null
		}
	},
	
	methods: {
		handleLessonClick(lesson, index) {
			// 检查是否有访问权限
			if (!this.hasAccess(lesson)) {
				uni.showToast({
					title: '需要购买课程才能观看',
					icon: 'none'
				});
				return;
			}
			
			// 触发课时点击事件
			this.$emit('lesson-click', {
				lesson,
				index,
				courseId: this.courseId
			});
			
			// 默认跳转到播放页面
			if (!this.$listeners['lesson-click']) {
				uni.navigateTo({
					url: `/pages/lessons/player?lesson_id=${lesson.id}&course_id=${this.courseId}`
				});
			}
		},
		
		hasAccess(lesson) {
			// 如果提供了自定义权限检查函数，使用它
			if (this.accessChecker && typeof this.accessChecker === 'function') {
				return this.accessChecker(lesson);
			}
			
			// 默认权限检查：免费课时或已购买课程
			return lesson.is_free || lesson.has_access;
		},
		
		formatDuration(seconds) {
			if (!seconds) return '00:00';
			
			const hours = Math.floor(seconds / 3600);
			const minutes = Math.floor((seconds % 3600) / 60);
			const secs = seconds % 60;
			
			if (hours > 0) {
				return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
			} else {
				return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
			}
		},
		
		formatTotalDuration(seconds) {
			if (!seconds) return '0分钟';
			
			const hours = Math.floor(seconds / 3600);
			const minutes = Math.floor((seconds % 3600) / 60);
			
			if (hours > 0) {
				return `${hours}小时${minutes}分钟`;
			} else {
				return `${minutes}分钟`;
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.lesson-list-container {
	background-color: white;
}

.lesson-header {
	padding: 30rpx;
	border-bottom: 1px solid #f0f0f0;
	
	.header-title {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
		
		.title-text {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}
		
		.lesson-count {
			margin-left: 10rpx;
			font-size: 26rpx;
			color: #666;
		}
	}
	
	.header-stats {
		display: flex;
		gap: 20rpx;
		
		.stat-item {
			font-size: 24rpx;
			color: #999;
		}
	}
}

.lesson-list {
	.lesson-item {
		display: flex;
		align-items: flex-start;
		padding: 30rpx;
		border-bottom: 1px solid #f8f9fa;
		transition: background-color 0.2s;
		
		&:active {
			background-color: #f8f9fa;
		}
		
		&:last-child {
			border-bottom: none;
		}
	}
}

.lesson-number {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background-color: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	flex-shrink: 0;
	
	.number-text {
		font-size: 24rpx;
		color: #666;
		font-weight: 500;
	}
}

.lesson-content {
	flex: 1;
	min-width: 0;
	
	.lesson-title {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 12rpx;
		line-height: 1.4;
	}
	
	.lesson-meta {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 12rpx;
		
		.meta-left {
			display: flex;
			align-items: center;
			gap: 12rpx;
			
			.lesson-duration {
				font-size: 24rpx;
				color: #666;
			}
		}
	}
	
	.lesson-description {
		margin-bottom: 12rpx;
		
		.description-text {
			font-size: 26rpx;
			color: #999;
			line-height: 1.4;
		}
	}
	
	.lesson-progress {
		display: flex;
		align-items: center;
		gap: 12rpx;
		
		.progress-bar {
			flex: 1;
			height: 6rpx;
			background-color: #f0f0f0;
			border-radius: 3rpx;
			overflow: hidden;
			
			.progress-fill {
				height: 100%;
				background-color: #007aff;
				border-radius: 3rpx;
				transition: width 0.3s;
			}
		}
		
		.progress-text {
			font-size: 22rpx;
			color: #666;
			min-width: 60rpx;
		}
	}
}

.lesson-status {
	margin-left: 20rpx;
	flex-shrink: 0;
}

.empty-state, .loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 40rpx;
	
	.empty-text {
		margin-top: 20rpx;
		color: #999;
		font-size: 28rpx;
	}
}
</style>
