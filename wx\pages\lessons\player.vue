<template>
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
        <uni-load-more status="loading"></uni-load-more>
        <text class="loading-text">加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
        <uni-icons type="info" size="60" color="#f56c6c"></uni-icons>
        <text class="error-text">{{ error }}</text>
        <button class="retry-btn" @click="retryLoad">重试</button>
    </view>

    <!-- 播放器内容 -->
    <view v-else-if="lessonInfo && (lessonInfo.id > 0 || lessonInfo.title)">
        <view class="chapter">
            <view class="sticky-header" :class="{ 'sticky-active': enableSticky }">
                <view class="player">
                    <video
                        :src="playUrl"
                        :poster="lessonInfo.thumbnail || lessonInfo.course_thumbnail"
                        @play="playHandler"
                        @pause="pauseHandler"
                        @ended="endedHandler"
                        @timeupdate="timeUpdateHandler"
                        @error="errorHandler"
                        controls
                        :show-fullscreen-btn="true"
                        :show-play-btn="true"
                        :show-center-play-btn="true"
                        :enable-progress-gesture="true"
                        :page-gesture="false"
                    ></video>
                </view>
                <view class="title">{{ lessonInfo.title }}</view>

                <!-- 课时描述区域 -->
                <view class="lesson-description-container" v-if="lessonInfo.description && lessonInfo.description.trim()">
                    <view class="description-header" @click="toggleDescription">
                        <view class="header-left">
                            <text class="header-title">课时介绍</text>
                        </view>
                        <view class="header-right">
                            <text class="expand-icon" :class="{ expanded: isDescriptionExpanded }">▼</text>
                        </view>
                    </view>
                    <view class="description-content" :class="{ expanded: isDescriptionExpanded }">
                        <view class="description-text">{{ lessonInfo.description }}</view>
                    </view>
                </view>

                <!-- 课程导航 - 移动到播放器下方 -->
                <view class="lesson-navigation">
                    <view class="nav-btn-container">
                        <view class="nav-btn prev-btn" @click="goToPrevLesson" :class="{ disabled: !canPlayPrev }">
                            <text>◀ 上一集</text>
                            <text v-if="prevLesson && !hasLessonAccess(prevLesson)" class="lock-icon">🔒</text>
                        </view>
                        <view class="nav-btn next-btn" @click="goToNextLesson" :class="{ disabled: !canPlayNext }">
                            <text>下一集 ▶</text>
                            <text v-if="nextLesson && !hasLessonAccess(nextLesson)" class="lock-icon">🔒</text>
                        </view>
                    </view>
                </view>

                <!-- 课时列表 -->
                <view class="lesson-list-container" v-if="lessonList.length > 0">
                    <!-- 标题栏 -->
                    <view class="lesson-list-header" @click="toggleLessonList">
                        <view class="header-left">
                            <text class="header-title">课程目录</text>
                            <text class="lesson-count" v-if="lessonList.length > 0">({{ currentLessonIndex + 1 }}/{{ lessonList.length }})</text>
                        </view>
                        <view class="header-right">
                            <text class="expand-icon" :class="{ expanded: isLessonListExpanded }">▼</text>
                        </view>
                    </view>

                    <!-- 课时列表内容 -->
                    <view class="lesson-list-content" :class="{ expanded: isLessonListExpanded }">
                        <view class="lesson-list-wrapper">
                            <view
                                v-for="(lesson, index) in lessonList"
                                :key="lesson.id"
                                class="lesson-item"
                                :class="{
                                    current: lesson.id == lessonInfo.id,
                                    locked: !hasLessonAccess(lesson)
                                }"
                                @click="selectLesson(lesson, index)"
                            >
                                <view class="lesson-index">{{ index + 1 }}</view>
                                <view class="lesson-info">
                                    <view class="lesson-title">{{ lesson.title }}</view>
                                    <view class="lesson-meta">
                                        <text class="lesson-duration" v-if="lesson.duration_formatted">
                                            {{ lesson.duration_formatted }}
                                        </text>
                                        <text class="lesson-type" :class="{ free: lesson.is_free === 1 }">
                                            {{ lesson.is_free === 1 ? '免费' : '付费' }}
                                        </text>
                                        <text v-if="!hasLessonAccess(lesson)" class="lock-icon">🔒</text>
                                    </view>
                                </view>
                                <view v-if="lesson.id == lessonInfo.id" class="playing-indicator">
                                    <text class="playing-text">播放中</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view class="fixbar-padding"></view>
        <view class="fixbar">
            <view class="left">
                <view class="icon-btn">
                    <uni-icons name="eye" size="20"></uni-icons>
                    <text v-if="lessonInfo.view_count">{{ lessonInfo.view_count }}</text>
                </view>
                <view class="icon-btn" @click="toggleLike">
                    <uni-icons :name="likeIcon.name" size="20" :color="likeIcon.color"></uni-icons>
                    <text v-if="lessonInfo.like_count">{{ lessonInfo.like_count }}</text>
                </view>
            </view>

            <!-- 课程导航 -->
            <view class="center">
                <!-- 中间区域预留 -->
            </view>

            <!-- 调试信息 - 开发时可启用 -->
            <!-- <view v-if="lessonList.length > 0" class="debug-info" style="position: fixed; top: 100rpx; right: 20rpx; background: rgba(0,0,0,0.8); color: white; padding: 10rpx; font-size: 20rpx; border-radius: 10rpx; z-index: 999;">
                <text>课时总数: {{ lessonList.length }}</text><br>
                <text>当前索引: {{ currentLessonIndex }}</text><br>
                <text>可上一集: {{ canPlayPrev ? '是' : '否' }}</text><br>
                <text>可下一集: {{ canPlayNext ? '是' : '否' }}</text>
            </view> -->

            <view class="right">
                <!-- 预留右侧功能区域 -->
            </view>
        </view>
    </view>
</template>

<script>
import { lessonApi, checkCourseAccess } from '@/api/course.js';
import { getUserInfo } from '@/utils/storage.js';

export default {
    data() {
        return {
            loading: true,
            error: '',
            enableSticky: true,
            playUrl: '',
            courseId: null,
            lessonId: null,
            lessonInfo: {
                id: 0,
                title: '',
                thumbnail: '',
                course_thumbnail: '',
                like_count: 0,
                view_count: 0,
                me: {
                    liked: 0,
                    owned: 0,
                    plan_id: 0,
                    position: 0,
                    logged: 0
                }
            },
            learning: {
                interval: null,
                interval_time: 15000,
                request_id: this.generateRequestId(),
                plan_id: 0,
                position: 0,
            },

            currentFormat: 'm3u8', // 当前播放格式
            retryCount: 0, // 重试次数
            maxRetries: 8, // 最大重试次数，增加到8次以尝试更多格式

            // 课程导航相关
            lessonList: [], // 课程的所有课时列表
            currentLessonIndex: -1, // 当前课时在列表中的索引
            prevLesson: null, // 上一集课时信息
            nextLesson: null, // 下一集课时信息

            // 权限控制相关
            hasAccess: false, // 用户是否有课程访问权限
            isLoggedIn: false, // 用户是否已登录
            courseInfo: null, // 课程信息

            // 课时列表相关
            isLessonListExpanded: false, // 课时列表是否展开

            // 课时描述相关
            isDescriptionExpanded: false, // 课时描述是否展开
        }
    },
    computed: {
        likeIcon() {
            let liked = this.lessonInfo.me.liked == 1
            return {
                name: liked ? 'heart-filled' : 'heart',
                color: liked ? '#ff4757' : '#666',
            }
        },

        // 检查是否有多种格式可选
        hasMultipleFormats() {
            const hasM3u8 = !!(this.lessonInfo.vod_video_url_m3u8 || (this.lessonInfo.vod_video_url && this.lessonInfo.vod_video_url.includes('.m3u8')))
            const hasMp4 = !!(this.lessonInfo.vod_video_url_mp4 || (this.lessonInfo.vod_video_url && this.lessonInfo.vod_video_url.includes('.mp4')) || this.lessonInfo.video_url)
            return hasM3u8 && hasMp4
        },

        // 是否可以播放上一集
        canPlayPrev() {
            return this.currentLessonIndex > 0 && this.prevLesson && this.hasLessonAccess(this.prevLesson)
        },

        // 是否可以播放下一集
        canPlayNext() {
            return this.currentLessonIndex >= 0 && this.currentLessonIndex < this.lessonList.length - 1 && this.nextLesson && this.hasLessonAccess(this.nextLesson)
        }
    },
    onLoad(e) {
        console.log('播放器页面参数:', e)

        // 保存课时ID
        this.lessonId = e.id

        // 设置页面标题
        if (e.title) {
            uni.setNavigationBarTitle({
                title: decodeURIComponent(e.title)
            })
        }

        // 保存课程ID
        if (e.course_id) {
            this.courseId = e.course_id
        }

        // 先测试API连接
        this.testAPI()

        this.loadLessonInfo(e.id)
    },

    onShow() {
        this.enableSticky = true
    },
    onHide() {
        this.enableSticky = false
    },
    methods: {
        generateRequestId() {
            return Math.random().toString(36).substr(2, 16);
        },

        // ==================== 课时描述相关方法 ====================

        /**
         * 切换课时描述展开/收起状态
         */
        toggleDescription() {
            this.isDescriptionExpanded = !this.isDescriptionExpanded;
            console.log('课时描述展开状态:', this.isDescriptionExpanded);
        },

        /**
         * 检查是否自动展开课时描述
         */
        checkAutoExpandDescription() {
            if (this.lessonInfo.description && this.lessonInfo.description.trim()) {
                // 如果描述内容较短（少于100个字符），自动展开
                if (this.lessonInfo.description.trim().length <= 100) {
                    setTimeout(() => {
                        this.isDescriptionExpanded = true;
                        console.log('课时描述较短，自动展开');
                    }, 300);
                } else {
                    console.log('课时描述较长，默认收起');
                }
            }
        },

        // ==================== 课时列表相关方法 ====================

        /**
         * 切换课时列表展开/收起状态
         */
        toggleLessonList() {
            this.isLessonListExpanded = !this.isLessonListExpanded;
            console.log('课时列表展开状态:', this.isLessonListExpanded);
        },

        /**
         * 选择课时进行播放
         */
        selectLesson(lesson, index) {
            console.log('选择课时:', lesson.title);

            // 检查是否有权限观看
            if (!this.hasLessonAccess(lesson)) {
                if (lesson.is_free === 1) {
                    uni.showModal({
                        title: '需要登录',
                        content: '请先登录后观看免费课程',
                        showCancel: true,
                        cancelText: '取消',
                        confirmText: '去登录',
                        success: (res) => {
                            if (res.confirm) {
                                // 跳转到登录页面
                                uni.navigateTo({
                                    url: '/pages/auth/login'
                                });
                            }
                        }
                    });
                } else {
                    uni.showModal({
                        title: '需要购买课程',
                        content: '该课时为付费内容，请先购买课程后观看',
                        showCancel: true,
                        cancelText: '取消',
                        confirmText: '立即购买',
                        success: (res) => {
                            if (res.confirm) {
                                // 跳转到课程详情页面
                                uni.redirectTo({
                                    url: `/pages/courses/detail?id=${this.lessonInfo.course_id}`
                                });
                            }
                        }
                    });
                }
                return;
            }

            // 如果是当前课时，不需要跳转
            if (lesson.id == this.lessonInfo.id) {
                uni.showToast({
                    title: '当前正在播放此课时',
                    icon: 'none'
                });
                return;
            }

            // 跳转到选中的课时
            uni.redirectTo({
                url: `/pages/lessons/player?id=${lesson.id}&course_id=${this.lessonInfo.course_id}&title=${encodeURIComponent(lesson.title || '')}`
            });
        },

        // ==================== 权限控制相关方法 ====================

        /**
         * 检查课时访问权限（参考课程详情页面的逻辑）
         */
        hasLessonAccess(lesson) {
            // 如果是免费课时，直接允许访问
            if (lesson && lesson.is_free === 1) {
                return true;
            }

            // 如果用户未登录，不允许访问付费课时
            if (!this.isLoggedIn) {
                return false;
            }

            // 如果用户有课程访问权限，允许访问所有课时
            if (this.hasAccess) {
                return true;
            }

            // 其他情况不允许访问
            return false;
        },

        /**
         * 检查当前课时的访问权限
         */
        async checkCurrentLessonAccess() {
            const userInfo = getUserInfo();
            this.isLoggedIn = !!userInfo;

            // 如果当前课时是免费的，直接允许访问
            if (this.lessonInfo.is_free === 1) {
                this.hasAccess = true;
                return true;
            }

            // 如果用户未登录，不允许访问付费课时
            if (!this.isLoggedIn) {
                this.hasAccess = false;
                this.showAccessDeniedDialog('请先登录后观看付费课程');
                return false;
            }

            // 检查课程访问权限
            try {
                const response = await checkCourseAccess(this.lessonInfo.course_id);
                this.hasAccess = response.has_access || false;

                if (!this.hasAccess) {
                    this.showAccessDeniedDialog('该课程为付费内容，请先购买课程后观看');
                    return false;
                }

                return true;
            } catch (e) {
                console.error('检查课程权限失败:', e);
                this.hasAccess = false;
                this.showAccessDeniedDialog('网络连接异常，请稍后重试');
                return false;
            }
        },

        /**
         * 显示权限不足对话框
         */
        showAccessDeniedDialog(message) {
            uni.showModal({
                title: '访问受限',
                content: message,
                showCancel: true,
                cancelText: '返回',
                confirmText: '了解更多',
                success: (res) => {
                    if (res.confirm) {
                        // 跳转到课程详情页面
                        uni.redirectTo({
                            url: `/pages/courses/detail?id=${this.lessonInfo.course_id}`
                        });
                    } else {
                        // 返回上一页
                        uni.navigateBack();
                    }
                }
            });
        },

        playHandler() {
            this.setLearningInterval()
        },

        pauseHandler() {
            this.clearLearningInterval()
        },

        endedHandler() {
            this.clearLearningInterval()
            this.learningLesson()
        },

        errorHandler(e) {
            console.log('M3U8视频播放错误:', e.target)
            console.log('当前播放URL:', this.playUrl)
            console.log('当前重试次数:', this.retryCount, '最大重试次数:', this.maxRetries)

            // 如果当前M3U8播放失败且未超过重试次数，尝试其他M3U8格式
            if (this.playUrl.includes('.m3u8') && e.target.errMsg && (e.target.errMsg.includes('404') || e.target.errMsg.includes('网络')) && this.retryCount < this.maxRetries) {
                console.log('当前M3U8播放失败，尝试其他M3U8格式')

                // 增加重试次数
                this.retryCount++

                // 尝试生成其他可能的M3U8 URL
                const alternativeUrls = this.generateAlternativeM3u8Urls(this.playUrl)

                if (alternativeUrls.length > 0 && this.retryCount <= alternativeUrls.length) {
                    const nextUrl = alternativeUrls[this.retryCount - 1]
                    console.log(`🔄 尝试备选M3U8地址 (${this.retryCount}/${Math.min(this.maxRetries, alternativeUrls.length)}):`)
                    console.log('   失败URL:', this.playUrl)
                    console.log('   备选URL:', nextUrl)

                    this.playUrl = nextUrl

                    // 显示更友好的加载提示
                    uni.showToast({
                        title: `正在加载... (${this.retryCount}/${Math.min(this.maxRetries, alternativeUrls.length)})`,
                        icon: 'loading',
                        duration: 2000
                    })
                    return
                }
            }

            // 如果所有格式都失败或超过重试次数，显示错误
            const errorMsg = this.retryCount > 0 ?
                `播放失败，已尝试${this.retryCount}种格式` :
                '播放失败，请检查网络连接'

            uni.showToast({
                title: errorMsg,
                icon: 'none',
                duration: 3000
            })

            // 显示详细错误信息给开发者
            console.error('❌ M3U8播放失败详情:', {
                originalUrl: this.playUrl,
                error: e.target.errMsg,
                lessonId: this.lessonId,
                retryCount: this.retryCount,
                maxRetries: this.maxRetries,
                lessonInfo: {
                    vod_file_id: this.lessonInfo?.vod_file_id,
                    video_type: this.lessonInfo?.video_type
                }
            })

            // 重置重试次数
            this.retryCount = 0
        },

        timeUpdateHandler(e) {
            this.learning.position = e.detail.currentTime
        },

        setLearningInterval() {
            this.clearLearningInterval()
            this.learning.interval = setInterval(this.learningLesson, this.learning.interval_time)
        },

        clearLearningInterval() {
            if (this.learning.interval != null) {
                clearInterval(this.learning.interval)
                this.learning.interval = null
            }
        },

        getPlayUrl() {
            console.log('=== 获取M3U8播放地址 ===')
            console.log('课时信息:', {
                vod_video_url: this.lessonInfo.vod_video_url,
                vod_video_url_m3u8: this.lessonInfo.vod_video_url_m3u8,
                video_type: this.lessonInfo.video_type,
                vod_file_id: this.lessonInfo.vod_file_id
            })

            // 临时测试：如果是特定的file_id，直接使用已知可用的M3U8链接
            if (this.lessonInfo.vod_file_id === '5145403692166581384') {
                const knownM3u8 = 'https://1325256586.vod-qcloud.com/b8f78255vodtranscq1325256586/81fe92c65145403692166581384/v.f1658984.m3u8'
                console.log('使用已知可用的M3U8链接:', knownM3u8)
                return knownM3u8
            }

            // 只使用M3U8格式的视频
            if (this.lessonInfo.vod_video_url_m3u8) {
                console.log('使用专用M3U8格式VOD视频:', this.lessonInfo.vod_video_url_m3u8)
                return this.lessonInfo.vod_video_url_m3u8
            }

            // 检查主VOD URL是否为M3U8格式
            if (this.lessonInfo.vod_video_url && this.lessonInfo.vod_video_url.includes('.m3u8')) {
                console.log('使用主VOD视频URL(M3U8):', this.lessonInfo.vod_video_url)
                return this.lessonInfo.vod_video_url
            }

            // 如果主VOD URL是MP4格式，尝试转换为M3U8
            if (this.lessonInfo.vod_video_url && this.lessonInfo.vod_video_url.includes('.mp4')) {
                const m3u8Url = this.tryConvertToM3u8(this.lessonInfo.vod_video_url)
                if (m3u8Url) {
                    console.log('转换MP4为M3U8格式:', m3u8Url)
                    return m3u8Url
                }
            }

            console.log('❌ 没有找到M3U8格式的播放地址')
            return ''
        },

        // 尝试将MP4 URL转换为M3U8 URL
        tryConvertToM3u8(mp4Url) {
            try {
                console.log('尝试转换MP4到M3U8:', mp4Url)

                // 解析腾讯云VOD URL结构
                // MP4格式: https://1325256586.vod-qcloud.com/e20d94advodcq1325256586/81fe92c65145403692166581384/7RXQW6IOeFMA.mp4
                // M3U8格式: https://1325256586.vod-qcloud.com/b8f78255vodtranscq1325256586/81fe92c65145403692166581384/v.f1658984.m3u8

                const urlParts = mp4Url.split('/')
                if (urlParts.length >= 5) {
                    const protocol = urlParts[0] // https:
                    const domain = urlParts[2]   // 1325256586.vod-qcloud.com
                    const originalPath = urlParts[3]  // e20d94advodcq1325256586
                    const fileId = urlParts[4]   // 81fe92c65145403692166581384

                    // 提取appid
                    const appIdMatch = domain.match(/^(\d+)\./)
                    const appId = appIdMatch ? appIdMatch[1] : '1325256586'

                    // 腾讯云VOD的M3U8 URL正确格式
                    const possibleUrls = [
                        // 腾讯云标准转码格式 (vodtranscq + appid)
                        `${protocol}//${domain}/b8f78255vodtranscq${appId}/${fileId}/v.f1658984.m3u8`,
                        `${protocol}//${domain}/b8f78255vodtranscq${appId}/${fileId}/v.f1658985.m3u8`,
                        `${protocol}//${domain}/b8f78255vodtranscq${appId}/${fileId}/v.f1658986.m3u8`,
                        // 其他可能的转码模板
                        `${protocol}//${domain}/b8f78255vodtranscq${appId}/${fileId}/playlist.m3u8`,
                        // 使用原路径的格式
                        `${protocol}//${domain}/${originalPath}/${fileId}/playlist.m3u8`,
                        // 直接替换扩展名
                        mp4Url.replace('.mp4', '.m3u8')
                    ]

                    console.log('生成的M3U8 URLs:', possibleUrls)

                    // 返回最可能的URL（根据您提供的格式）
                    return possibleUrls[0]
                }

                // 如果URL结构不符合预期，使用简单替换
                const simpleM3u8 = mp4Url.replace('.mp4', '.m3u8')
                console.log('使用简单替换:', simpleM3u8)
                return simpleM3u8

            } catch (e) {
                console.error('转换M3U8 URL失败:', e)
                return null
            }
        },

        // 生成备选的M3U8 URL
        generateAlternativeM3u8Urls(currentUrl) {
            try {
                const alternatives = []

                // 解析当前URL
                const urlParts = currentUrl.split('/')
                if (urlParts.length >= 5) {
                    const protocol = urlParts[0]
                    const domain = urlParts[2]
                    const appPath = urlParts[3]
                    const fileId = urlParts[4]

                    // 提取appid
                    const appIdMatch = domain.match(/^(\d+)\./)
                    const appId = appIdMatch ? appIdMatch[1] : '1325256586'

                    // 优化模板顺序：把最可能成功的格式放在前面
                    const templates = [
                        'v.f1658984.m3u8',  // 最常用的转码格式，优先尝试
                        'v.f1658985.m3u8',  // 第二常用格式
                        'v.f1658986.m3u8',  // 第三常用格式
                        'v.f1658987.m3u8',  // 添加更多可能的转码格式
                        'v.f1658988.m3u8',
                        'adp.10.m3u8',      // 自适应流格式
                        'adp.20.m3u8',
                        'adp.30.m3u8',
                        'master.m3u8',      // 主播放列表
                        'index.m3u8',       // 索引文件
                        'playlist.m3u8'     // 播放列表（通常失败率较高，放在后面）
                    ]

                    // 优先生成vodtranscq格式的URL（成功率更高）
                    templates.forEach(template => {
                        const url = `${protocol}//${domain}/b8f78255vodtranscq${appId}/${fileId}/${template}`
                        if (url !== currentUrl) {
                            alternatives.push(url)
                        }
                    })

                    // 然后尝试原路径格式
                    templates.forEach(template => {
                        const url = `${protocol}//${domain}/${appPath}/${fileId}/${template}`
                        if (url !== currentUrl && !alternatives.includes(url)) {
                            alternatives.push(url)
                        }
                    })

                    // 添加一些特殊格式尝试
                    const specialFormats = [
                        `${protocol}//${domain}/b8f78255vodtranscq${appId}/${fileId}/v.f240.m3u8`,
                        `${protocol}//${domain}/b8f78255vodtranscq${appId}/${fileId}/v.f480.m3u8`,
                        `${protocol}//${domain}/b8f78255vodtranscq${appId}/${fileId}/v.f720.m3u8`,
                        `${protocol}//${domain}/b8f78255vodtranscq${appId}/${fileId}/v.f1080.m3u8`
                    ]

                    specialFormats.forEach(url => {
                        if (url !== currentUrl && !alternatives.includes(url)) {
                            alternatives.push(url)
                        }
                    })
                }

                console.log('生成的备选M3U8 URLs:', alternatives.slice(0, 5), `(共${alternatives.length}个)`)
                return alternatives
            } catch (e) {
                console.error('生成备选M3U8 URL失败:', e)
                return []
            }
        },

        // 检查视频格式
        getVideoFormat(url) {
            if (!url) return '无'
            if (url.includes('.m3u8')) return 'M3U8 (HLS流媒体)'
            return 'M3U8专用播放器'
        },

        // 切换播放格式
        switchFormat(format) {
            console.log('切换播放格式到:', format)

            let newUrl = ''
            if (format === 'm3u8') {
                if (this.lessonInfo.vod_video_url_m3u8) {
                    newUrl = this.lessonInfo.vod_video_url_m3u8
                } else if (this.lessonInfo.vod_video_url && this.lessonInfo.vod_video_url.includes('.m3u8')) {
                    newUrl = this.lessonInfo.vod_video_url
                }
            } else if (format === 'mp4') {
                if (this.lessonInfo.vod_video_url_mp4) {
                    newUrl = this.lessonInfo.vod_video_url_mp4
                } else if (this.lessonInfo.vod_video_url && this.lessonInfo.vod_video_url.includes('.mp4')) {
                    newUrl = this.lessonInfo.vod_video_url
                } else if (this.lessonInfo.video_url) {
                    newUrl = this.lessonInfo.video_url
                }
            }

            if (newUrl && newUrl !== this.playUrl) {
                this.playUrl = newUrl
                this.currentFormat = format

                uni.showToast({
                    title: '已切换播放格式',
                    icon: 'success',
                    duration: 2000
                })

                console.log('新播放地址:', newUrl)
                console.log('新格式:', this.getVideoFormat(newUrl))
            } else {
                uni.showToast({
                    title: '该格式暂不可用',
                    icon: 'none'
                })
            }
        },

        getCurrentUrl() {
            return `/pages/lessons/player?id=${this.lessonInfo.id}`
        },








        // ==================== 课程导航相关方法 ====================

        /**
         * 获取课程的课时列表
         */
        async loadLessonList() {
            if (!this.lessonInfo.course_id) {
                console.log('没有课程ID，无法获取课时列表')
                return
            }

            try {
                console.log('获取课程课时列表，课程ID:', this.lessonInfo.course_id)
                const response = await lessonApi.getLessonList({
                    course_id: this.lessonInfo.course_id,
                    show_all: 1 // 显示所有课时
                })

                console.log('课时列表API响应:', response)

                let lessonData = null
                if (response && response.success && response.data) {
                    lessonData = response.data
                } else if (response && response.code === 0 && response.data) {
                    lessonData = response.data
                } else if (response && response.code === 200 && response.data) {
                    lessonData = response.data
                } else {
                    console.error('课时列表API响应格式不匹配:', response)
                    return
                }

                // 处理课时列表数据
                if (Array.isArray(lessonData)) {
                    this.lessonList = lessonData
                } else if (lessonData.list && Array.isArray(lessonData.list)) {
                    this.lessonList = lessonData.list
                } else if (lessonData.lessons && Array.isArray(lessonData.lessons)) {
                    // 处理新的API格式：{course: {…}, lessons: Array(6), pagination: {…}, statistics: {…}}
                    this.lessonList = lessonData.lessons
                    console.log('使用lessons字段作为课时列表')
                } else {
                    console.error('课时列表数据格式不正确')
                    return
                }

                console.log('课时列表加载成功，共', this.lessonList.length, '个课时')

                // 更新导航信息
                this.updateNavigationInfo()

                // 可选：首次加载时自动展开课时列表（如果课时数量不多）
                if (this.lessonList.length <= 10 && !this.isLessonListExpanded) {
                    // 延迟一点时间再展开，让用户看到加载完成的效果
                    setTimeout(() => {
                        this.isLessonListExpanded = true;
                    }, 500);
                }

            } catch (e) {
                console.error('获取课时列表失败:', e)
            }
        },

        /**
         * 找到当前课时在列表中的索引
         */
        findCurrentLessonIndex() {
            if (!this.lessonList.length || !this.lessonInfo.id) {
                return -1
            }

            return this.lessonList.findIndex(lesson => lesson.id == this.lessonInfo.id)
        },

        /**
         * 更新导航信息（上一集/下一集）
         */
        updateNavigationInfo() {
            this.currentLessonIndex = this.findCurrentLessonIndex()

            console.log('当前课时索引:', this.currentLessonIndex)
            console.log('课时列表长度:', this.lessonList.length)

            // 重置导航信息
            this.prevLesson = null
            this.nextLesson = null

            if (this.currentLessonIndex >= 0) {
                // 设置上一集
                if (this.currentLessonIndex > 0) {
                    this.prevLesson = this.lessonList[this.currentLessonIndex - 1]
                    console.log('上一集:', this.prevLesson)
                }

                // 设置下一集
                if (this.currentLessonIndex < this.lessonList.length - 1) {
                    this.nextLesson = this.lessonList[this.currentLessonIndex + 1]
                    console.log('下一集:', this.nextLesson)
                }
            }
        },

        /**
         * 跳转到上一集
         */
        goToPrevLesson() {
            console.log('点击上一集按钮')

            if (this.lessonList.length === 0) {
                uni.showToast({
                    title: '加载中，请稍候',
                    icon: 'none'
                })
                return
            }

            if (!this.canPlayPrev) {
                // 检查具体原因
                if (this.currentLessonIndex <= 0) {
                    uni.showToast({
                        title: '已经是第一集了',
                        icon: 'none'
                    })
                } else if (!this.hasLessonAccess(this.prevLesson)) {
                    uni.showModal({
                        title: '访问受限',
                        content: this.prevLesson.is_free === 1 ? '请先登录后观看' : '该课时为付费内容，请先购买课程',
                        showCancel: true,
                        cancelText: '取消',
                        confirmText: '了解更多',
                        success: (res) => {
                            if (res.confirm) {
                                uni.redirectTo({
                                    url: `/pages/courses/detail?id=${this.lessonInfo.course_id}`
                                });
                            }
                        }
                    });
                }
                return
            }

            console.log('跳转到上一集:', this.prevLesson.title)

            // 跳转到上一集播放页面
            uni.redirectTo({
                url: `/pages/lessons/player?id=${this.prevLesson.id}&course_id=${this.lessonInfo.course_id}&title=${encodeURIComponent(this.prevLesson.title || '')}`
            })
        },

        /**
         * 跳转到下一集
         */
        goToNextLesson() {
            console.log('点击下一集按钮')

            if (this.lessonList.length === 0) {
                uni.showToast({
                    title: '加载中，请稍候',
                    icon: 'none'
                })
                return
            }

            if (!this.canPlayNext) {
                // 检查具体原因
                if (this.currentLessonIndex >= this.lessonList.length - 1) {
                    uni.showToast({
                        title: '已经是最后一集了',
                        icon: 'none'
                    })
                } else if (!this.hasLessonAccess(this.nextLesson)) {
                    uni.showModal({
                        title: '访问受限',
                        content: this.nextLesson.is_free === 1 ? '请先登录后观看' : '该课时为付费内容，请先购买课程',
                        showCancel: true,
                        cancelText: '取消',
                        confirmText: '了解更多',
                        success: (res) => {
                            if (res.confirm) {
                                uni.redirectTo({
                                    url: `/pages/courses/detail?id=${this.lessonInfo.course_id}`
                                });
                            }
                        }
                    });
                }
                return
            }

            console.log('跳转到下一集:', this.nextLesson.title)

            // 跳转到下一集播放页面
            uni.redirectTo({
                url: `/pages/lessons/player?id=${this.nextLesson.id}&course_id=${this.lessonInfo.course_id}&title=${encodeURIComponent(this.nextLesson.title || '')}`
            })
        },
        toggleLike() {
            const userInfo = getUserInfo()
            if (!userInfo) {
                uni.showToast({
                    title: '请先登录',
                    icon: 'none'
                })
                return
            }

            // 模拟切换点赞状态
            if (this.lessonInfo.me.liked == 0) {
                this.lessonInfo.me.liked = 1
                this.lessonInfo.like_count++
                uni.showToast({
                    title: '点赞成功',
                    icon: 'success'
                })
            } else {
                this.lessonInfo.me.liked = 0
                this.lessonInfo.like_count--
                uni.showToast({
                    title: '取消点赞',
                    icon: 'none'
                })
            }
        },

        learningLesson() {
            const userInfo = getUserInfo()
            if (!userInfo) {
                return false
            }
            if (this.learning.plan_id == 0) {
                return false
            }

            // 调用学习进度API
            lessonApi.saveWatchProgress({
                lesson_id: this.lessonInfo.id,
                course_id: this.lessonInfo.course_id,
                watch_time: this.learning.position,
                progress_position: this.learning.position,
                completion_rate: Math.min(100, (this.learning.position / this.lessonInfo.duration) * 100)
            }).catch(e => {
                console.log('保存学习进度失败:', e)
            })
        },

        async loadLessonInfo(id) {
            try {
                this.loading = true
                this.error = ''

                console.log('=== 开始加载课时信息 ===')
                console.log('课时ID:', id)
                console.log('课程ID:', this.courseId)
                console.log('API配置:', {
                    baseURL: this.$config?.API_BASE_URL || 'unknown',
                    debug: this.$config?.DEBUG || false
                })

                // 直接测试API URL
                const testUrl = `https://wx.yx420.cn/api/lessonDetail.php?lesson_id=${id}&course_id=${this.courseId || 0}`
                console.log('测试URL:', testUrl)

                const response = await lessonApi.getLessonDetail({
                    lesson_id: id,
                    course_id: this.courseId || 0
                })

                console.log('=== API响应详情 ===')
                console.log('响应对象:', response)
                console.log('响应类型:', typeof response)
                console.log('响应结构:', response ? Object.keys(response) : 'null')
                console.log('是否有success字段:', 'success' in (response || {}))
                console.log('是否有code字段:', 'code' in (response || {}))
                console.log('是否有data字段:', 'data' in (response || {}))

                // 适配不同的响应格式
                let lessonData = null
                if (response && response.success && response.data) {
                    // 原有API格式
                    console.log('使用原有API格式 (success)')
                    lessonData = response.data
                } else if (response && response.code === 0 && response.data) {
                    // 新API格式
                    console.log('使用新API格式 (code: 0)')
                    lessonData = response.data
                } else if (response && response.code === 200 && response.data) {
                    // HTTP 200格式
                    console.log('使用HTTP 200格式')
                    lessonData = response.data
                } else {
                    console.error('API响应格式不匹配:', response)
                    throw new Error(response?.message || response?.error || '加载课时失败')
                }

                this.lessonInfo = {
                    ...lessonData,
                    id: lessonData.id || id,
                    comment_count: lessonData.comment_count || 0,
                    like_count: lessonData.like_count || 0,
                    view_count: lessonData.view_count || 0,
                    me: lessonData.me || {
                        liked: 0,
                        owned: 1,
                        plan_id: 1,
                        position: 0,
                        logged: getUserInfo() ? 1 : 0
                    }
                }

                this.learning.plan_id = this.lessonInfo.me.plan_id || 1
                this.learning.position = this.lessonInfo.me.position || 0
                this.playUrl = this.getPlayUrl()

                console.log('课时信息加载成功:', this.lessonInfo)
                console.log('播放地址:', this.playUrl)
                console.log('视频格式:', this.getVideoFormat(this.playUrl))
                console.log('备选播放地址:', {
                    m3u8: this.lessonInfo.vod_video_url_m3u8,
                    mp4: this.lessonInfo.vod_video_url_mp4,
                    main: this.lessonInfo.vod_video_url,
                    normal: this.lessonInfo.video_url
                })

                // 检查当前课时的访问权限
                const hasAccess = await this.checkCurrentLessonAccess();
                if (!hasAccess) {
                    // 如果没有权限，停止加载
                    return;
                }

                // 加载课程的课时列表，用于导航
                this.loadLessonList()

                // 如果课时描述较短，自动展开
                this.checkAutoExpandDescription()

            } catch (e) {
                console.error('加载课时失败:', e)
                this.error = '加载失败，请重试'
                uni.showToast({
                    title: this.error,
                    icon: 'none'
                })
            } finally {
                this.loading = false
            }
        },

        retryLoad() {
            if (this.lessonId) {
                this.loadLessonInfo(this.lessonId)
            }
        },

        // 测试API连接
        async testAPI() {
            try {
                console.log('=== 直接测试API ===')

                // 使用uni.request直接测试
                const result = await new Promise((resolve, reject) => {
                    uni.request({
                        url: `https://wx.yx420.cn/api/lessonDetail.php?lesson_id=${this.lessonId}`,
                        method: 'GET',
                        success: (res) => {
                            console.log('uni.request成功:', res)
                            resolve(res)
                        },
                        fail: (err) => {
                            console.error('uni.request失败:', err)
                            reject(err)
                        }
                    })
                })

                console.log('直接请求结果:', result)

            } catch (e) {
                console.error('直接API测试失败:', e)
            }
        },


    }
}
</script>

<style lang="scss" scoped>
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400rpx;
    background: white;
}

.loading-text {
    margin-top: 20rpx;
    color: #666;
    font-size: 28rpx;
}

.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400rpx;
    background: white;
    padding: 40rpx;
}

.error-text {
    margin: 20rpx 0;
    color: #f56c6c;
    font-size: 28rpx;
    text-align: center;
}

.retry-btn {
    background-color: #007aff;
    color: white;
    border: none;
    border-radius: 40rpx;
    padding: 20rpx 40rpx;
    font-size: 28rpx;
}

.chapter {
    background-color: white;
}

.sticky-header {
    position: relative;

    &.sticky-active {
        position: sticky;
        top: 0;
        z-index: 100;
    }
}

.player,
video {
    width: 100%;
    height: 422rpx;
}

.title {
    color: #333;
    background-color: white;
    font-weight: 600;
    padding: 20rpx;
    font-size: 32rpx;
    line-height: 1.4;
}

/* 课程导航样式 */
.lesson-navigation {
    background-color: white;
    padding: 20rpx;
    border-bottom: 1px solid #f0f0f0;
}

.nav-btn-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40rpx;
}







.fixbar-padding {
    height: 120rpx;
}

.fixbar {
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9;
    position: fixed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    padding: 20rpx;
    border-top: 1px solid #f0f0f0;
}

.fixbar .left {
    display: flex;
    align-items: center;
    flex: 1;
}

.fixbar .center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    margin: 0 20rpx;
}

.fixbar .center .nav-btn:first-child {
    margin-right: 20rpx;
}

.fixbar .right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
}

.icon-btn {
    display: flex;
    align-items: center;
    margin-right: 30rpx;
    font-size: 24rpx;
    color: #666;

    text {
        margin-left: 8rpx;
    }
}

.nav-btn {
    display: flex;
    align-items: center;
    padding: 20rpx 40rpx;
    border-radius: 60rpx;
    background-color: #f8f9fa;
    border: 2rpx solid #e9ecef;
    font-size: 28rpx;
    min-width: 160rpx;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.nav-btn text {
    font-weight: 500;
    color: #333;
}

.nav-btn.disabled {
    opacity: 0.5;
    background-color: #f5f5f5;
    border-color: #ddd;
}

.nav-btn.disabled text {
    color: #999;
}

.nav-btn:not(.disabled):active {
    transform: scale(0.95);
    background-color: #e9ecef;
}

/* 上一集按钮样式 */
.prev-btn:not(.disabled) {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

.prev-btn:not(.disabled) text {
    color: #2196f3;
}

.prev-btn:not(.disabled):active {
    background-color: #bbdefb;
}

/* 下一集按钮样式 */
.next-btn:not(.disabled) {
    background-color: #e8f5e8;
    border-color: #4caf50;
}

.next-btn:not(.disabled) text {
    color: #4caf50;
}

.next-btn:not(.disabled):active {
    background-color: #c8e6c9;
}

/* 锁定图标样式 */
.lock-icon {
    margin-left: 8rpx;
    font-size: 20rpx;
    opacity: 0.7;
}

/* 课时描述容器样式 */
.lesson-description-container {
    background-color: white;
    margin: 20rpx 20rpx 0 20rpx;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
}

/* 课时描述标题栏 */
.description-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 32rpx;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.description-header:active {
    background-color: #e9ecef;
}

.description-header .header-left {
    display: flex;
    align-items: center;
}

.description-header .header-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
}

.description-header .header-right {
    display: flex;
    align-items: center;
}

.description-header .expand-icon {
    font-size: 24rpx;
    color: #666;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.description-header .expand-icon.expanded {
    transform: rotate(180deg);
}

/* 课时描述内容 */
.description-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.description-content.expanded {
    max-height: 500rpx; /* 根据内容调整 */
}

.description-text {
    padding: 32rpx;
    font-size: 28rpx;
    line-height: 1.8;
    color: #555;
    background-color: white;
    white-space: pre-wrap; /* 保持换行格式 */
    word-wrap: break-word;
    border-radius: 0 0 16rpx 16rpx;
    position: relative;
}

/* 添加一个渐变效果，让描述区域更有层次感 */
.description-text::before {
    content: '';
    position: absolute;
    top: 0;
    left: 32rpx;
    right: 32rpx;
    height: 2rpx;
    background: linear-gradient(90deg, transparent, #e9ecef, transparent);
}

/* 课时列表容器样式 */
.lesson-list-container {
    background-color: white;
    margin: 20rpx 20rpx 0 20rpx;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
}

/* 课时列表标题栏 */
.lesson-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 32rpx;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.lesson-list-header:active {
    background-color: #e9ecef;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
}

.lesson-count {
    margin-left: 16rpx;
    font-size: 24rpx;
    color: #666;
}

.header-right {
    display: flex;
    align-items: center;
}

.expand-icon {
    font-size: 24rpx;
    color: #666;
    transition: transform 0.3s ease;
}

.expand-icon.expanded {
    transform: rotate(180deg);
}

/* 课时列表内容 */
.lesson-list-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.lesson-list-content.expanded {
    max-height: 60vh; /* 使用视口高度，适应不同屏幕 */
}

.lesson-list-wrapper {
    padding: 0;
    overflow-y: auto;
    max-height: inherit;
}

/* 课时项样式 */
.lesson-item {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
    position: relative;
}

.lesson-item:last-child {
    border-bottom: none;
}

.lesson-item:active {
    background-color: #f8f9fa;
}

.lesson-item.current {
    background-color: #e3f2fd;
    border-left: 6rpx solid #2196f3;
}

.lesson-item.locked {
    opacity: 0.6;
}

/* 课时序号 */
.lesson-index {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    font-weight: 600;
    color: #666;
    margin-right: 24rpx;
    flex-shrink: 0;
}

.lesson-item.current .lesson-index {
    background-color: #2196f3;
    color: white;
}

/* 课时信息 */
.lesson-info {
    flex: 1;
    min-width: 0;
}

.lesson-title {
    font-size: 28rpx;
    color: #333;
    line-height: 1.4;
    margin-bottom: 8rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.lesson-item.current .lesson-title {
    color: #2196f3;
    font-weight: 600;
}

.lesson-meta {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.lesson-duration {
    font-size: 22rpx;
    color: #999;
}

.lesson-type {
    font-size: 20rpx;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    background-color: #ff9800;
    color: white;
}

.lesson-type.free {
    background-color: #4caf50;
}

/* 播放中指示器 */
.playing-indicator {
    position: absolute;
    right: 32rpx;
    top: 50%;
    transform: translateY(-50%);
}

.playing-text {
    font-size: 20rpx;
    color: #2196f3;
    background-color: rgba(33, 150, 243, 0.1);
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
}


</style>
