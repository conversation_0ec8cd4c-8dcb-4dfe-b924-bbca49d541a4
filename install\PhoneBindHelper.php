<?php
/**
 * 手机号绑定助手类
 * 
 * 由于数据库触发器权限限制，该类实现了原本在触发器中的逻辑
 * 在用户更新手机号相关操作时，需要调用此类的方法
 */
class PhoneBindHelper
{
    private $pdo;
    
    public function __construct($pdo)
    {
        $this->pdo = $pdo;
    }
    
    /**
     * 更新用户手机号时的处理逻辑
     * 应在更新users表的phone字段前调用
     * 
     * @param int $userId 用户ID
     * @param string $newPhone 新手机号
     * @param string $oldPhone 原手机号
     * @return array 需要更新的字段
     */
    public function handlePhoneUpdate($userId, $newPhone, $oldPhone = null)
    {
        $updateFields = [];
        
        // 如果手机号发生变化且不为空，设置为需要验证
        if (!empty($newPhone) && $newPhone !== $oldPhone) {
            $updateFields['phone_verified'] = 0;
            $updateFields['phone_bind_required'] = 1;
        }
        
        // 如果手机号被清空，重置相关状态
        if (empty($newPhone)) {
            $updateFields['phone_verified'] = 0;
            $updateFields['phone_bind_time'] = null;
        }
        
        return $updateFields;
    }
    
    /**
     * 更新用户手机号和相关状态
     * 
     * @param int $userId 用户ID
     * @param string $newPhone 新手机号
     * @return bool 是否更新成功
     */
    public function updateUserPhone($userId, $newPhone)
    {
        try {
            // 获取当前手机号
            $stmt = $this->pdo->prepare("SELECT phone FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $currentUser = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$currentUser) {
                return false;
            }
            
            $oldPhone = $currentUser['phone'];
            
            // 获取需要更新的字段
            $updateFields = $this->handlePhoneUpdate($userId, $newPhone, $oldPhone);
            
            // 构建更新SQL
            $setParts = ['phone = ?'];
            $params = [$newPhone];
            
            foreach ($updateFields as $field => $value) {
                if ($value === null) {
                    $setParts[] = "$field = NULL";
                } else {
                    $setParts[] = "$field = ?";
                    $params[] = $value;
                }
            }
            
            $params[] = $userId; // WHERE条件的参数
            
            $sql = "UPDATE users SET " . implode(', ', $setParts) . " WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            
            return $stmt->execute($params);
            
        } catch (Exception $e) {
            error_log("更新用户手机号失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 验证手机号成功后的处理
     * 
     * @param int $userId 用户ID
     * @return bool 是否更新成功
     */
    public function verifyPhoneSuccess($userId)
    {
        try {
            $sql = "UPDATE users SET 
                        phone_verified = 1, 
                        phone_bind_time = NOW(),
                        phone_bind_required = 0
                    WHERE id = ?";
            
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([$userId]);
            
        } catch (Exception $e) {
            error_log("手机号验证状态更新失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查用户是否需要绑定手机号
     * 
     * @param int $userId 用户ID
     * @return array 用户手机号状态信息
     */
    public function checkPhoneBindStatus($userId)
    {
        try {
            $sql = "SELECT 
                        phone,
                        phone_verified,
                        phone_bind_required,
                        phone_bind_time,
                        CASE 
                            WHEN phone_verified = 1 THEN 'verified'
                            WHEN phone_bind_required = 1 AND (phone IS NULL OR phone = '') THEN 'required'
                            WHEN phone IS NOT NULL AND phone != '' AND phone_verified = 0 THEN 'pending'
                            ELSE 'optional'
                        END as bind_status
                    FROM users 
                    WHERE id = ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$userId]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("检查手机号绑定状态失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 强制要求用户绑定手机号
     * 
     * @param int $userId 用户ID
     * @return bool 是否更新成功
     */
    public function requirePhoneBind($userId)
    {
        try {
            $sql = "UPDATE users SET phone_bind_required = 1 WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([$userId]);
            
        } catch (Exception $e) {
            error_log("设置手机号绑定要求失败: " . $e->getMessage());
            return false;
        }
    }
}

/**
 * 使用示例：
 * 
 * // 初始化
 * $phoneHelper = new PhoneBindHelper($pdo);
 * 
 * // 用户更新手机号
 * $success = $phoneHelper->updateUserPhone($userId, $newPhone);
 * 
 * // 手机号验证成功
 * $success = $phoneHelper->verifyPhoneSuccess($userId);
 * 
 * // 检查绑定状态
 * $status = $phoneHelper->checkPhoneBindStatus($userId);
 * 
 * // 强制要求绑定
 * $success = $phoneHelper->requirePhoneBind($userId);
 */