<?php
/**
 * 公告列表API
 * 支持JWT认证，获取公告列表（只读权限）
 * 包含CSRF保护和SQL注入防护
 */

require_once 'auth.php';

// 设置安全头
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
    header('Access-Control-Max-Age: 86400');
    exit;
}

// 只允许GET请求（普通用户只能查看公告）
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '方法不允许',
        'data' => null,
        'timestamp' => time()
    ]);
    exit;
}

$auth = new AuthAPI();

try {
    // 获取用户信息（可选认证，允许游客访问）
    $user = $auth->getCurrentUser();

    // 获取公告列表
    getAnnouncements($auth, $user);

} catch (Exception $e) {
    error_log('公告列表API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误', null, [
        'error_id' => uniqid(),
        'timestamp' => time()
    ]);
}
/**
 * 获取公告列表
 * 包含参数验证和SQL注入防护
 */
function getAnnouncements($auth, $user) {
    $conn = $auth->getConn();

    // 参数验证和清理
    $page = isset($_GET['page']) ? filter_var($_GET['page'], FILTER_VALIDATE_INT) : 1;
    $limit = isset($_GET['limit']) ? filter_var($_GET['limit'], FILTER_VALIDATE_INT) : 10;
    $type = isset($_GET['type']) ? filter_var($_GET['type'], FILTER_SANITIZE_STRING) : '';
    $category_id = isset($_GET['category_id']) ? filter_var($_GET['category_id'], FILTER_VALIDATE_INT) : 0;
    $status = isset($_GET['status']) ? filter_var($_GET['status'], FILTER_SANITIZE_STRING) : 'published';
    $search = isset($_GET['search']) ? filter_var($_GET['search'], FILTER_SANITIZE_STRING) : '';

    // 参数范围验证
    if ($page === false || $page < 1) $page = 1;
    if ($limit === false || $limit < 1 || $limit > 50) $limit = 10;
    if ($category_id === false || $category_id < 0) $category_id = 0;

    // 验证状态参数
    $allowed_statuses = ['draft', 'published', 'archived'];
    if (!in_array($status, $allowed_statuses)) {
        $status = 'published';
    }

    // 验证类型参数
    $allowed_types = ['notice', 'urgent', 'system', 'activity'];
    if (!empty($type) && !in_array($type, $allowed_types)) {
        $type = '';
    }

    $offset = ($page - 1) * $limit;
    
    // 构建WHERE条件（使用预处理语句防止SQL注入）
    $where_conditions = ["a.status = ?"];
    $params = [$status];
    $param_types = "s";

    if (!empty($type)) {
        $where_conditions[] = "a.type = ?";
        $params[] = $type;
        $param_types .= "s";
    }

    if ($category_id > 0) {
        $where_conditions[] = "a.category_id = ?";
        $params[] = $category_id;
        $param_types .= "i";
    }

    // 搜索功能
    if (!empty($search)) {
        $search_term = '%' . $search . '%';
        $where_conditions[] = "(a.title LIKE ? OR a.content LIKE ?)";
        $params[] = $search_term;
        $params[] = $search_term;
        $param_types .= "ss";
    }

    // 只显示未过期的公告
    $where_conditions[] = "(a.expire_time IS NULL OR a.expire_time > NOW())";

    $where_clause = "WHERE " . implode(' AND ', $where_conditions);
    
    // 获取总数（使用预处理语句）
    $count_sql = "SELECT COUNT(*) as total FROM announcements a
                  LEFT JOIN announcement_categories c ON a.category_id = c.id
                  $where_clause";

    $count_stmt = $conn->prepare($count_sql);
    if (!$count_stmt) {
        throw new Exception('COUNT SQL预处理失败: ' . $conn->error);
    }

    if (!empty($params)) {
        $count_stmt->bind_param($param_types, ...$params);
    }

    if (!$count_stmt->execute()) {
        throw new Exception('COUNT SQL执行失败: ' . $count_stmt->error);
    }

    $total = $count_stmt->get_result()->fetch_assoc()['total'];
    $count_stmt->close();
    
    // 获取数据（使用预处理语句）
    $sql = "SELECT
                a.id, a.title, a.content, a.type, a.priority, a.is_pinned,
                a.category_id, a.publish_time, a.expire_time, a.view_count,
                a.created_at, a.updated_at,
                c.name as category_name,
                ad.username as author_name
            FROM announcements a
            LEFT JOIN announcement_categories c ON a.category_id = c.id
            LEFT JOIN admins ad ON a.author_id = ad.id
            $where_clause
            ORDER BY a.is_pinned DESC, a.priority DESC, a.publish_time DESC
            LIMIT ? OFFSET ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('SELECT SQL预处理失败: ' . $conn->error);
    }

    $params[] = $limit;
    $params[] = $offset;
    $param_types .= "ii";

    $stmt->bind_param($param_types, ...$params);

    if (!$stmt->execute()) {
        throw new Exception('SELECT SQL执行失败: ' . $stmt->error);
    }

    $result = $stmt->get_result();
    $announcements = [];

    while ($row = $result->fetch_assoc()) {
        // 数据清理和格式化
        $announcements[] = [
            'id' => (int)$row['id'],
            'title' => htmlspecialchars($row['title'], ENT_QUOTES, 'UTF-8'),
            'content' => htmlspecialchars($row['content'], ENT_QUOTES, 'UTF-8'),
            'type' => $row['type'],
            'priority' => (int)$row['priority'],
            'is_pinned' => (int)$row['is_pinned'],
            'category_id' => $row['category_id'] ? (int)$row['category_id'] : null,
            'category_name' => $row['category_name'] ? htmlspecialchars($row['category_name'], ENT_QUOTES, 'UTF-8') : null,
            'author_name' => $row['author_name'] ? htmlspecialchars($row['author_name'], ENT_QUOTES, 'UTF-8') : null,
            'publish_time' => $row['publish_time'],
            'expire_time' => $row['expire_time'],
            'view_count' => (int)$row['view_count'],
            'created_at' => $row['created_at'],
            'updated_at' => $row['updated_at']
        ];
    }

    $stmt->close();

    $response_data = [
        'list' => $announcements,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => (int)$total,
            'pages' => ceil($total / $limit),
            'has_more' => ($page * $limit) < $total
        ],
        'filters' => [
            'type' => $type,
            'category_id' => $category_id,
            'status' => $status,
            'search' => $search
        ]
    ];

    $auth->jsonResponse(200, '获取成功', $response_data, [
        'user_id' => $user ? $user['id'] : null,
        'timestamp' => time(),
        'cache_ttl' => 180 // 3分钟缓存建议
    ]);
}