<?php
$servername = "localhost";
$username = "qq";
$password = "123456";
$dbname = "qq";

// Create connection (without specifying database first)
$conn = new mysqli($servername, $username, $password);

// Check connection
if ($conn->connect_error) {
  die("Connection failed: " . $conn->connect_error);
}

// Create database
$sql = "CREATE DATABASE IF NOT EXISTS `$dbname` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
if ($conn->query($sql) === TRUE) {
  echo "Database created successfully<br>";
} else {
  echo "Error creating database: " . $conn->error;
}

// Select the database
$conn->select_db($dbname);

// Read SQL file      
$sql = file_get_contents("install.sql");

if ($conn->multi_query($sql)) {
  echo "Installation completed successfully!";
} else {
  echo "Error installing database: " . $conn->error;
}

$conn->close();
?>
