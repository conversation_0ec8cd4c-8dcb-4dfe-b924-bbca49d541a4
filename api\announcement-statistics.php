<?php
/**
 * 公告统计信息API
 * 支持JWT认证，返回公告相关的统计数据
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        getAnnouncementStatistics($auth);
    } else {
        $auth->jsonResponse(405, '不支持的请求方法');
    }
} catch (Exception $e) {
    error_log('获取公告统计信息API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 获取公告统计信息
 */
function getAnnouncementStatistics($auth) {
    $conn = $auth->getConn();
    
    // 检查用户认证（可选）
    $user = $auth->getCurrentUser();
    $user_id = $user ? $user['id'] : null;
    
    // 基础统计信息
    $statistics = [
        'total_announcements' => 0,
        'published_announcements' => 0,
        'urgent_announcements' => 0,
        'pinned_announcements' => 0,
        'categories_count' => 0
    ];
    
    // 用户相关统计（需要登录）
    if ($user_id) {
        $statistics['user_stats'] = [
            'read_count' => 0,
            'unread_count' => 0,
            'favorite_count' => 0
        ];
    }
    
    try {
        // 1. 获取公告总数
        $total_result = $conn->query("SELECT COUNT(*) as count FROM announcements");
        if ($total_result) {
            $statistics['total_announcements'] = (int)$total_result->fetch_assoc()['count'];
        }
        
        // 2. 获取已发布公告数
        $published_result = $conn->query("SELECT COUNT(*) as count FROM announcements WHERE status = 'published'");
        if ($published_result) {
            $statistics['published_announcements'] = (int)$published_result->fetch_assoc()['count'];
        }
        
        // 3. 获取紧急公告数
        $urgent_result = $conn->query("SELECT COUNT(*) as count FROM announcements WHERE status = 'published' AND type = 'urgent'");
        if ($urgent_result) {
            $statistics['urgent_announcements'] = (int)$urgent_result->fetch_assoc()['count'];
        }
        
        // 4. 获取置顶公告数
        $pinned_result = $conn->query("SELECT COUNT(*) as count FROM announcements WHERE status = 'published' AND is_pinned = 1");
        if ($pinned_result) {
            $statistics['pinned_announcements'] = (int)$pinned_result->fetch_assoc()['count'];
        }
        
        // 5. 获取分类数量
        $categories_result = $conn->query("SELECT COUNT(*) as count FROM announcement_categories WHERE is_active = 1");
        if ($categories_result) {
            $statistics['categories_count'] = (int)$categories_result->fetch_assoc()['count'];
        }
        
        // 6. 按类型统计公告
        $type_stats = [];
        $type_result = $conn->query("SELECT type, COUNT(*) as count FROM announcements WHERE status = 'published' GROUP BY type");
        if ($type_result) {
            while ($row = $type_result->fetch_assoc()) {
                $type_stats[$row['type']] = (int)$row['count'];
            }
        }
        $statistics['by_type'] = $type_stats;
        
        // 7. 按优先级统计公告
        $priority_stats = [];
        $priority_result = $conn->query("SELECT priority, COUNT(*) as count FROM announcements WHERE status = 'published' GROUP BY priority ORDER BY priority DESC");
        if ($priority_result) {
            while ($row = $priority_result->fetch_assoc()) {
                $priority_label = '';
                switch ($row['priority']) {
                    case 2: $priority_label = 'urgent'; break;
                    case 1: $priority_label = 'important'; break;
                    case 0: $priority_label = 'normal'; break;
                    default: $priority_label = 'unknown'; break;
                }
                $priority_stats[$priority_label] = (int)$row['count'];
            }
        }
        $statistics['by_priority'] = $priority_stats;
        
        // 8. 用户相关统计（需要登录）
        if ($user_id) {
            // 用户已读公告数
            $user_read_stmt = $conn->prepare("
                SELECT COUNT(DISTINCT ar.announcement_id) as count
                FROM announcement_reads ar
                INNER JOIN announcements a ON ar.announcement_id = a.id
                WHERE ar.user_id = ? AND a.status = 'published'
            ");
            $user_read_stmt->bind_param("i", $user_id);
            $user_read_stmt->execute();
            $user_read_result = $user_read_stmt->get_result();
            if ($user_read_result) {
                $statistics['user_stats']['read_count'] = (int)$user_read_result->fetch_assoc()['count'];
            }
            
            // 计算未读数量
            $statistics['user_stats']['unread_count'] = max(0, 
                $statistics['published_announcements'] - $statistics['user_stats']['read_count']
            );
            
            // 用户收藏公告数（如果收藏表存在）
            $favorite_check = $conn->query("SHOW TABLES LIKE 'announcement_favorites'");
            if ($favorite_check && $favorite_check->num_rows > 0) {
                $user_favorite_stmt = $conn->prepare("
                    SELECT COUNT(*) as count
                    FROM announcement_favorites af
                    INNER JOIN announcements a ON af.announcement_id = a.id
                    WHERE af.user_id = ? AND a.status = 'published'
                ");
                $user_favorite_stmt->bind_param("i", $user_id);
                $user_favorite_stmt->execute();
                $user_favorite_result = $user_favorite_stmt->get_result();
                if ($user_favorite_result) {
                    $statistics['user_stats']['favorite_count'] = (int)$user_favorite_result->fetch_assoc()['count'];
                }
            }
        }
        
        // 9. 最近发布的公告
        $recent_result = $conn->query("
            SELECT id, title, type, priority, publish_time 
            FROM announcements 
            WHERE status = 'published' 
            ORDER BY publish_time DESC 
            LIMIT 5
        ");
        $recent_announcements = [];
        if ($recent_result) {
            while ($row = $recent_result->fetch_assoc()) {
                $recent_announcements[] = [
                    'id' => (int)$row['id'],
                    'title' => $row['title'],
                    'type' => $row['type'],
                    'priority' => (int)$row['priority'],
                    'publish_time' => $row['publish_time'] ? date('Y-m-d H:i:s', strtotime($row['publish_time'])) : null
                ];
            }
        }
        $statistics['recent_announcements'] = $recent_announcements;
        
    } catch (Exception $e) {
        error_log('获取统计信息时出错: ' . $e->getMessage());
        // 继续返回已获取的统计信息
    }
    
    $auth->jsonResponse(200, '获取公告统计信息成功', $statistics);
}
?>
