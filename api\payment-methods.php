<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);

require_once '../includes/db.php';
require_once '../includes/auth.php';

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 验证用户身份
$user = authenticate_user();
if (!$user) {
    http_response_code(401);
    echo json_encode([
        'code' => 401,
        'message' => '未授权访问',
        'data' => null
    ]);
    exit;
}

try {
    // 获取支付配置
    $stmt = $conn->prepare("
        SELECT setting_key, setting_value 
        FROM settings 
        WHERE setting_key IN (
            'wechat_pay_enabled', 'wechat_pay_app_id', 'wechat_pay_mch_id', 'wechat_pay_api_key',
            'epay_enabled', 'epay_api_url', 'epay_partner_id', 'epay_partner_key', 'epay_supported_methods'
        )
    ");
    $stmt->execute();
    $settings_result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    $settings = [];
    foreach ($settings_result as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
    
    $payment_methods = [];
    
    // 检查微信支付
    $wechat_enabled = !empty($settings['wechat_pay_enabled']) && $settings['wechat_pay_enabled'] === '1';
    $wechat_configured = !empty($settings['wechat_pay_app_id']) && 
                        !empty($settings['wechat_pay_mch_id']) && 
                        !empty($settings['wechat_pay_api_key']);
    
    if ($wechat_enabled && $wechat_configured) {
        $payment_methods[] = [
            'id' => 'wechat',
            'name' => '微信支付',
            'description' => '使用微信支付快速完成购买',
            'icon' => 'wechat',
            'type' => 'wechat',
            'enabled' => true,
            'sort_order' => 1
        ];
    }
    
    // 检查码支付易支付
    $epay_enabled = !empty($settings['epay_enabled']) && $settings['epay_enabled'] === '1';
    $epay_configured = !empty($settings['epay_api_url']) && 
                      !empty($settings['epay_partner_id']) && 
                      !empty($settings['epay_partner_key']);
    
    if ($epay_enabled && $epay_configured) {
        $supported_methods = explode(',', $settings['epay_supported_methods'] ?? 'alipay,wxpay,qqpay');
        
        // 支付宝
        if (in_array('alipay', $supported_methods)) {
            $payment_methods[] = [
                'id' => 'epay_alipay',
                'name' => '支付宝',
                'description' => '使用支付宝安全支付',
                'icon' => 'alipay',
                'type' => 'epay',
                'epay_type' => 'alipay',
                'enabled' => true,
                'sort_order' => 2
            ];
        }
        
        // 微信支付（码支付）
        if (in_array('wxpay', $supported_methods)) {
            $payment_methods[] = [
                'id' => 'epay_wxpay',
                'name' => '微信支付（扫码）',
                'description' => '使用微信扫码支付',
                'icon' => 'wechat',
                'type' => 'epay',
                'epay_type' => 'wxpay',
                'enabled' => true,
                'sort_order' => 3
            ];
        }
        
        // QQ支付
        if (in_array('qqpay', $supported_methods)) {
            $payment_methods[] = [
                'id' => 'epay_qqpay',
                'name' => 'QQ钱包',
                'description' => '使用QQ钱包支付',
                'icon' => 'qq',
                'type' => 'epay',
                'epay_type' => 'qqpay',
                'enabled' => true,
                'sort_order' => 4
            ];
        }
    }
    
    // 按排序字段排序
    usort($payment_methods, function($a, $b) {
        return $a['sort_order'] - $b['sort_order'];
    });
    
    // 检查用户openid（用于微信支付限制）
    $user_openid = $user['openid'] ?? '';
    $is_temp_user = strpos($user_openid, 'temp_openid_') === 0;
    
    // 如果是临时用户，禁用微信支付
    if ($is_temp_user) {
        foreach ($payment_methods as &$method) {
            if ($method['type'] === 'wechat') {
                $method['enabled'] = false;
                $method['disabled_reason'] = '账号密码登录用户暂不支持微信支付，请使用微信登录';
            }
        }
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '获取支付方式成功',
        'data' => [
            'payment_methods' => $payment_methods,
            'user_info' => [
                'is_temp_user' => $is_temp_user,
                'has_openid' => !empty($user_openid) && !$is_temp_user
            ]
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '获取支付方式失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?>
