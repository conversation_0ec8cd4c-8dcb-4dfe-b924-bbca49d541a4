<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

$success_message = '';
$error_message = '';

// 添加或编辑轮播图
if (isset($_POST['add_banner'])) {
    $title = trim($_POST['title']);
    $image_url = trim($_POST['image_url']);
    $link_type = $_POST['link_type'];
    $link_value = trim($_POST['link_value']);
    $start_time = $_POST['start_time'] ? $_POST['start_time'] : null;
    $end_time = $_POST['end_time'] ? $_POST['end_time'] : null;
    $status = $_POST['status'];
    $sort_order = intval($_POST['sort_order']);
    $banner_id = intval($_POST['banner_id']);

    if (empty($title) || empty($image_url)) {
        $error_message = '请填写轮播图标题和图片链接';
    } else {
        // 验证链接类型和链接值的匹配
        if ($link_type !== 'none' && empty($link_value)) {
            $error_message = '请填写链接值';
        } else {
            if ($banner_id > 0) {
                // 编辑轮播图
                $stmt = $conn->prepare("UPDATE banners SET title = ?, image_url = ?, link_type = ?, link_value = ?, start_time = ?, end_time = ?, status = ?, sort_order = ? WHERE id = ?");
                if ($stmt === false) {
                    $error_message = '准备更新语句失败：' . $conn->error;
                } else {
                    $stmt->bind_param("sssssssii", $title, $image_url, $link_type, $link_value, $start_time, $end_time, $status, $sort_order, $banner_id);

                    if ($stmt->execute()) {
                        $success_message = '轮播图更新成功';
                    } else {
                        $error_message = '更新轮播图失败：' . $conn->error;
                    }
                }
            } else {
                // 添加轮播图
                $stmt = $conn->prepare("INSERT INTO banners (title, image_url, link_type, link_value, start_time, end_time, status, sort_order, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                if ($stmt === false) {
                    $error_message = '准备插入语句失败：' . $conn->error;
                } else {
                    $created_by = $_SESSION['admin_id'] ?? 1; // 使用当前管理员ID
                    $stmt->bind_param("sssssssii", $title, $image_url, $link_type, $link_value, $start_time, $end_time, $status, $sort_order, $created_by);

                    if ($stmt->execute()) {
                        $success_message = '轮播图添加成功';
                    } else {
                        $error_message = '添加轮播图失败：' . $conn->error;
                    }
                }
            }
        }
    }
}

// 删除轮播图
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);

    $stmt = $conn->prepare("DELETE FROM banners WHERE id = ?");
    if ($stmt === false) {
        $error_message = '准备删除语句失败：' . $conn->error;
    } else {
        $stmt->bind_param("i", $id);
        if ($stmt->execute()) {
            $success_message = '轮播图删除成功';
        } else {
            $error_message = '删除轮播图失败：' . $conn->error;
        }
    }
}

// 获取编辑的轮播图信息
$edit_banner = null;
if (isset($_GET['edit'])) {
    $edit_id = intval($_GET['edit']);
    $stmt = $conn->prepare("SELECT * FROM banners WHERE id = ?");
    if ($stmt) {
        $stmt->bind_param("i", $edit_id);
        if ($stmt->execute()) {
            $edit_banner = $stmt->get_result()->fetch_assoc();
        }
    }
}

// 获取轮播图列表
$banners = [];
$sql = "SELECT b.*, a.username as creator_name
        FROM banners b
        LEFT JOIN admins a ON b.created_by = a.id
        ORDER BY b.sort_order ASC, b.created_at DESC";
$result = $conn->query($sql);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $banners[] = $row;
    }
}

// 获取课程列表（用于链接选择）
$courses = [];
$course_result = $conn->query("SELECT id, title FROM courses WHERE status = 'active' ORDER BY title");
if ($course_result) {
    while ($row = $course_result->fetch_assoc()) {
        $courses[] = $row;
    }
}

// 获取公告列表（用于链接选择）
$announcements = [];
$announcement_result = $conn->query("SELECT id, title FROM announcements WHERE status = 'published' ORDER BY title");
if ($announcement_result) {
    while ($row = $announcement_result->fetch_assoc()) {
        $announcements[] = $row;
    }
}

render_admin_header('轮播图管理', 'banners');
?>

<!-- 页面内容 -->
<?php if ($success_message): ?>
    <?php show_success_message($success_message); ?>
<?php endif; ?>

<?php if ($error_message): ?>
    <?php show_error_message($error_message); ?>
<?php endif; ?>

<!-- 添加/编辑轮播图表单 -->
<?php render_card_start($edit_banner ? '编辑轮播图' : '添加轮播图'); ?>
<?php render_form_start('', 'post', 'bannerForm'); ?>
    <input type="hidden" name="banner_id" value="<?php echo $edit_banner ? $edit_banner['id'] : 0; ?>">

    <div class="row">
        <div class="col-md-6">
            <?php render_form_input('轮播图标题', 'title', 'text', $edit_banner['title'] ?? '', true, '请输入轮播图标题'); ?>
        </div>
        <div class="col-md-6">
            <div class="admin-form-group">
                <label class="admin-form-label" for="status">状态</label>
                <select class="admin-form-input" name="status" id="status" required>
                    <option value="active" <?php echo ($edit_banner['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>启用</option>
                    <option value="inactive" <?php echo ($edit_banner['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>禁用</option>
                </select>
            </div>
        </div>
    </div>

    <div class="admin-form-group">
        <label class="admin-form-label" for="image_url">图片链接</label>
        <div class="input-group">
            <input class="admin-form-input" type="text" name="image_url" id="image_url"
                   value="<?php echo htmlspecialchars($edit_banner['image_url'] ?? ''); ?>"
                   placeholder="请输入图片链接或点击上传" required>
            <button type="button" class="admin-btn admin-btn-secondary" onclick="uploadBannerImage()">上传图片</button>
        </div>
        <div id="imagePreview" style="margin-top: 10px;"></div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="admin-form-group">
                <label class="admin-form-label" for="link_type">链接类型</label>
                <select class="admin-form-input" name="link_type" id="link_type" onchange="toggleLinkValue()">
                    <option value="none" <?php echo ($edit_banner['link_type'] ?? 'none') === 'none' ? 'selected' : ''; ?>>无链接</option>
                    <option value="course" <?php echo ($edit_banner['link_type'] ?? '') === 'course' ? 'selected' : ''; ?>>课程</option>
                    <option value="announcement" <?php echo ($edit_banner['link_type'] ?? '') === 'announcement' ? 'selected' : ''; ?>>公告</option>
                    <option value="external" <?php echo ($edit_banner['link_type'] ?? '') === 'external' ? 'selected' : ''; ?>>外部链接</option>
                </select>
            </div>
        </div>
        <div class="col-md-6">
            <div class="admin-form-group" id="linkValueGroup">
                <label class="admin-form-label" for="link_value">链接值</label>
                <input class="admin-form-input" type="text" name="link_value" id="link_value"
                       value="<?php echo htmlspecialchars($edit_banner['link_value'] ?? ''); ?>"
                       placeholder="根据链接类型填写相应值">
                <select class="admin-form-input" name="link_value_select" id="link_value_select" style="display: none;" onchange="updateLinkValue()">
                    <option value="">请选择</option>
                </select>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <?php render_form_input('开始时间', 'start_time', 'datetime-local',
                $edit_banner['start_time'] ? date('Y-m-d\TH:i', strtotime($edit_banner['start_time'])) : '',
                false, '留空表示立即生效'); ?>
        </div>
        <div class="col-md-4">
            <?php render_form_input('结束时间', 'end_time', 'datetime-local',
                $edit_banner['end_time'] ? date('Y-m-d\TH:i', strtotime($edit_banner['end_time'])) : '',
                false, '留空表示永不过期'); ?>
        </div>
        <div class="col-md-4">
            <?php render_form_input('排序', 'sort_order', 'number', $edit_banner['sort_order'] ?? 0, false, '数字越小越靠前'); ?>
        </div>
    </div>

    <div class="admin-form-actions">
        <button type="submit" name="add_banner" class="admin-btn admin-btn-primary">
            <?php echo $edit_banner ? '更新轮播图' : '添加轮播图'; ?>
        </button>
        <?php if ($edit_banner): ?>
            <?php render_link_button('取消编辑', 'banners.php', 'admin-btn-secondary'); ?>
        <?php endif; ?>
    </div>
<?php render_form_end(); ?>
<?php render_card_end(); ?>

<!-- 轮播图列表 -->
<?php render_card_start('轮播图列表'); ?>
<?php if (empty($banners)): ?>
    <p class="text-muted">暂无轮播图数据</p>
<?php else: ?>
    <?php render_table_start(['ID', '标题', '图片预览', '链接类型', '状态', '排序', '创建者', '创建时间', '操作']); ?>
    <?php foreach ($banners as $banner): ?>
        <tr>
            <td><?php echo $banner['id']; ?></td>
            <td><?php echo htmlspecialchars($banner['title']); ?></td>
            <td>
                <img src="<?php echo htmlspecialchars($banner['image_url']); ?>"
                     alt="轮播图" style="width: 80px; height: 45px; object-fit: cover; border-radius: 4px;">
            </td>
            <td>
                <?php
                $link_types = [
                    'none' => '无链接',
                    'course' => '课程',
                    'announcement' => '公告',
                    'external' => '外部链接'
                ];
                echo $link_types[$banner['link_type']] ?? $banner['link_type'];
                ?>
            </td>
            <td>
                <?php if ($banner['status'] === 'active'): ?>
                    <?php render_badge('启用', 'success'); ?>
                <?php else: ?>
                    <?php render_badge('禁用', 'danger'); ?>
                <?php endif; ?>
            </td>
            <td><?php echo $banner['sort_order']; ?></td>
            <td><?php echo htmlspecialchars($banner['creator_name'] ?? '未知'); ?></td>
            <td><?php echo date('Y-m-d H:i', strtotime($banner['created_at'])); ?></td>
            <td>
                <?php render_link_button('编辑', 'banners.php?edit=' . $banner['id'], 'admin-btn-sm admin-btn-primary'); ?>
                <?php render_link_button('删除', 'banners.php?delete=' . $banner['id'], 'admin-btn-sm admin-btn-danger',
                    'return confirmDelete("确定要删除这个轮播图吗？")'); ?>
            </td>
        </tr>
    <?php endforeach; ?>
    <?php render_table_end(); ?>
<?php endif; ?>
<?php render_card_end(); ?>

<script>
// 课程和公告数据（用于JavaScript）
const courses = <?php echo json_encode($courses); ?>;
const announcements = <?php echo json_encode($announcements); ?>;

// 切换链接值输入方式
function toggleLinkValue() {
    const linkType = document.getElementById('link_type').value;
    const linkValueInput = document.getElementById('link_value');
    const linkValueSelect = document.getElementById('link_value_select');

    // 清空选择框
    linkValueSelect.innerHTML = '<option value="">请选择</option>';

    if (linkType === 'course') {
        // 显示课程选择框
        courses.forEach(course => {
            const option = document.createElement('option');
            option.value = course.id;
            option.textContent = course.title;
            linkValueSelect.appendChild(option);
        });
        linkValueInput.style.display = 'none';
        linkValueSelect.style.display = 'block';
        linkValueInput.placeholder = '选择课程';
    } else if (linkType === 'announcement') {
        // 显示公告选择框
        announcements.forEach(announcement => {
            const option = document.createElement('option');
            option.value = announcement.id;
            option.textContent = announcement.title;
            linkValueSelect.appendChild(option);
        });
        linkValueInput.style.display = 'none';
        linkValueSelect.style.display = 'block';
        linkValueInput.placeholder = '选择公告';
    } else if (linkType === 'external') {
        // 显示外部链接输入框
        linkValueInput.style.display = 'block';
        linkValueSelect.style.display = 'none';
        linkValueInput.placeholder = '请输入外部链接URL';
    } else {
        // 无链接
        linkValueInput.style.display = 'block';
        linkValueSelect.style.display = 'none';
        linkValueInput.placeholder = '无需填写';
        linkValueInput.value = '';
    }
}

// 更新链接值
function updateLinkValue() {
    const linkValueSelect = document.getElementById('link_value_select');
    const linkValueInput = document.getElementById('link_value');
    linkValueInput.value = linkValueSelect.value;
}

// 上传轮播图图片
function uploadBannerImage() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (!file) return;

        // 验证文件类型
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('只支持 JPG、PNG、GIF、WebP 格式的图片');
            return;
        }

        // 验证文件大小（5MB）
        if (file.size > 5 * 1024 * 1024) {
            alert('图片大小不能超过5MB');
            return;
        }

        // 显示上传进度
        const preview = document.getElementById('imagePreview');
        preview.innerHTML = '<div class="upload-progress">正在上传...</div>';

        // 创建FormData
        const formData = new FormData();
        formData.append('image', file);

        // 上传文件
        fetch('upload_banner_image.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新图片链接
                document.getElementById('image_url').value = data.data.file_url;

                // 显示预览
                preview.innerHTML = `
                    <div class="upload-success">
                        <img src="${data.data.file_url}" alt="预览" style="max-width: 200px; max-height: 100px; object-fit: cover; border-radius: 4px;">
                        <p>上传成功：${data.data.file_name}</p>
                    </div>
                `;
            } else {
                preview.innerHTML = `<div class="upload-error">上传失败：${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('上传错误:', error);
            preview.innerHTML = '<div class="upload-error">上传失败，请重试</div>';
        });
    };
    input.click();
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    toggleLinkValue();

    // 如果有图片URL，显示预览
    const imageUrl = document.getElementById('image_url').value;
    if (imageUrl) {
        const preview = document.getElementById('imagePreview');
        preview.innerHTML = `
            <div class="current-image">
                <img src="${imageUrl}" alt="当前图片" style="max-width: 200px; max-height: 100px; object-fit: cover; border-radius: 4px;">
                <p>当前图片</p>
            </div>
        `;
    }
});
</script>

<style>
.input-group {
    display: flex;
    gap: 10px;
}

.input-group .admin-form-input {
    flex: 1;
}

.upload-progress, .upload-success, .upload-error, .current-image {
    padding: 10px;
    border-radius: 4px;
    text-align: center;
}

.upload-progress {
    background-color: #e3f2fd;
    color: #1976d2;
}

.upload-success {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.upload-error {
    background-color: #ffebee;
    color: #c62828;
}

.current-image {
    background-color: #f5f5f5;
    color: #666;
}

.row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.col-md-4, .col-md-6 {
    flex: 1;
}

.admin-form-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}
</style>

<?php render_admin_footer(); ?>