<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007bff;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon-test {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }
        .icon-test img {
            width: 24px;
            height: 24px;
            margin-right: 10px;
        }
        .status {
            margin-left: auto;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .tabbar-preview {
            display: flex;
            background: white;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            padding: 10px;
            margin: 20px 0;
        }
        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
        }
        .tab-item img {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
        }
        .tab-text {
            font-size: 12px;
            color: #7A7E83;
        }
        .tab-item.active .tab-text {
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 TabBar图标测试</h1>
        
        <div class="test-section">
            <h3>📱 TabBar预览效果</h3>
            <div class="tabbar-preview">
                <div class="tab-item active">
                    <img src="home-active.png" alt="首页" onerror="this.style.display='none'">
                    <span class="tab-text">首页</span>
                </div>
                <div class="tab-item">
                    <img src="course.png" alt="课程" onerror="this.style.display='none'">
                    <span class="tab-text">课程</span>
                </div>
                <div class="tab-item">
                    <img src="profile.png" alt="我的" onerror="this.style.display='none'">
                    <span class="tab-text">我的</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔗 图标连接测试</h3>
            <div id="icon-tests">
                <!-- 图标测试将通过JavaScript动态生成 -->
            </div>
        </div>

        <div class="test-section">
            <h3>📋 测试结果</h3>
            <div id="test-results">
                <p>正在测试图标连接...</p>
            </div>
        </div>
    </div>

    <script>
        const icons = [
            { name: '首页-普通', url: 'home.png' },
            { name: '首页-选中', url: 'home-active.png' },
            { name: '课程-普通', url: 'course.png' },
            { name: '课程-选中', url: 'course-active.png' },
            { name: '个人-普通', url: 'profile.png' },
            { name: '个人-选中', url: 'profile-active.png' }
        ];

        function testIcons() {
            const testsContainer = document.getElementById('icon-tests');
            const resultsContainer = document.getElementById('test-results');
            let successCount = 0;
            let totalCount = icons.length;

            testsContainer.innerHTML = '';
            resultsContainer.innerHTML = '<p>正在测试图标连接...</p>';

            icons.forEach((icon, index) => {
                const testDiv = document.createElement('div');
                testDiv.className = 'icon-test';
                
                const img = document.createElement('img');
                img.src = icon.url;
                img.alt = icon.name;
                
                const label = document.createElement('span');
                label.textContent = icon.name;
                
                const status = document.createElement('span');
                status.className = 'status';
                status.textContent = '测试中...';
                
                testDiv.appendChild(img);
                testDiv.appendChild(label);
                testDiv.appendChild(status);
                testsContainer.appendChild(testDiv);

                img.onload = function() {
                    status.textContent = '✅ 成功';
                    status.className = 'status success';
                    successCount++;
                    updateResults();
                };

                img.onerror = function() {
                    status.textContent = '❌ 失败';
                    status.className = 'status error';
                    updateResults();
                };
            });

            function updateResults() {
                const completedTests = document.querySelectorAll('.status.success, .status.error').length;
                if (completedTests === totalCount) {
                    resultsContainer.innerHTML = `
                        <h4>测试完成</h4>
                        <p><strong>成功:</strong> ${successCount}/${totalCount}</p>
                        <p><strong>失败:</strong> ${totalCount - successCount}/${totalCount}</p>
                        ${successCount === totalCount ? 
                            '<p style="color: #28a745;">🎉 所有图标都可以正常加载！</p>' : 
                            '<p style="color: #dc3545;">⚠️ 部分图标加载失败，建议使用备用方案。</p>'
                        }
                    `;
                }
            }
        }

        // 页面加载完成后开始测试
        window.onload = testIcons;
    </script>
</body>
</html>
