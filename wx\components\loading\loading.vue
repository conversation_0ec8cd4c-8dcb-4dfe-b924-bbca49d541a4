<template>
	<view class="loading-container" v-if="show">
		<view class="loading-mask" :class="{ transparent: !mask }"></view>
		<view class="loading-content">
			<view class="loading-spinner">
				<view class="spinner-item" v-for="i in 12" :key="i"></view>
			</view>
			<text class="loading-text" v-if="text">{{ text }}</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'Loading',
		props: {
			// 是否显示
			show: {
				type: Boolean,
				default: false
			},
			// 加载文本
			text: {
				type: String,
				default: '加载中...'
			},
			// 是否显示遮罩
			mask: {
				type: Boolean,
				default: true
			},
			// 遮罩颜色
			maskColor: {
				type: String,
				default: 'rgba(0, 0, 0, 0.5)'
			}
		}
	};
</script>

<style lang="scss" scoped>
.loading-container {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.loading-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	
	&.transparent {
		background: transparent;
	}
}

.loading-content {
	position: relative;
	background: rgba(0, 0, 0, 0.8);
	border-radius: 12px;
	padding: 30px;
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 120px;
}

.loading-spinner {
	width: 40px;
	height: 40px;
	position: relative;
	margin-bottom: 15px;
	
	.spinner-item {
		position: absolute;
		width: 3px;
		height: 12px;
		background: #fff;
		border-radius: 2px;
		animation: loading-spin 1.2s linear infinite;
		
		&:nth-child(1) {
			top: 0;
			left: 50%;
			margin-left: -1.5px;
			animation-delay: 0s;
		}
		
		&:nth-child(2) {
			top: 3px;
			right: 3px;
			transform: rotate(30deg);
			animation-delay: -0.1s;
		}
		
		&:nth-child(3) {
			top: 9px;
			right: 0;
			transform: rotate(60deg);
			animation-delay: -0.2s;
		}
		
		&:nth-child(4) {
			top: 17px;
			right: 3px;
			transform: rotate(90deg);
			animation-delay: -0.3s;
		}
		
		&:nth-child(5) {
			bottom: 0;
			right: 50%;
			margin-right: -1.5px;
			transform: rotate(120deg);
			animation-delay: -0.4s;
		}
		
		&:nth-child(6) {
			bottom: 3px;
			right: 3px;
			transform: rotate(150deg);
			animation-delay: -0.5s;
		}
		
		&:nth-child(7) {
			bottom: 0;
			left: 50%;
			margin-left: -1.5px;
			transform: rotate(180deg);
			animation-delay: -0.6s;
		}
		
		&:nth-child(8) {
			bottom: 3px;
			left: 3px;
			transform: rotate(210deg);
			animation-delay: -0.7s;
		}
		
		&:nth-child(9) {
			bottom: 9px;
			left: 0;
			transform: rotate(240deg);
			animation-delay: -0.8s;
		}
		
		&:nth-child(10) {
			top: 17px;
			left: 3px;
			transform: rotate(270deg);
			animation-delay: -0.9s;
		}
		
		&:nth-child(11) {
			top: 9px;
			left: 0;
			transform: rotate(300deg);
			animation-delay: -1.0s;
		}
		
		&:nth-child(12) {
			top: 3px;
			left: 3px;
			transform: rotate(330deg);
			animation-delay: -1.1s;
		}
	}
}

.loading-text {
	color: #fff;
	font-size: 14px;
	text-align: center;
}

@keyframes loading-spin {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0.2;
	}
}
</style>
