-- MySQL dump 10.13  Distrib 5.7.38, for Win64 (x86_64)
--
-- Host: localhost    Database: qq
-- ------------------------------------------------------
-- Server version	5.7.38-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admin_upload_logs`
--

DROP TABLE IF EXISTS `admin_upload_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_upload_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL,
  `upload_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'course_thumbnail',
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_url` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_upload_type` (`upload_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_upload_logs`
--

LOCK TABLES `admin_upload_logs` WRITE;
/*!40000 ALTER TABLE `admin_upload_logs` DISABLE KEYS */;
INSERT INTO `admin_upload_logs` VALUES (1,1,'course_thumbnail','course_1752647003_6877455b8e4f9.jpg',24401,'0','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-16 06:23:23'),(2,1,'lesson_thumbnail','lesson_1752676709_6877b9654094f.jpg',24401,'0','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-16 14:38:29'),(3,1,'lesson_thumbnail','lesson_1752676733_6877b97d71bb8.jpg',24401,'0','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-16 14:38:53'),(4,1,'lesson_thumbnail','lesson_1752677277_6877bb9db922e.jpg',24401,'0','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-16 14:47:57'),(5,1,'lesson_thumbnail','lesson_1752677288_6877bba8c778e.jpg',24401,'0','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-16 14:48:08'),(6,1,'lesson_thumbnail','lesson_1752677847_6877bdd7df8ad.jpg',24401,'0','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-16 14:57:27'),(7,1,'lesson_thumbnail','lesson_1752677861_6877bde50c881.jpg',24401,'0','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-16 14:57:41'),(8,1,'banner_image','banner_1752691429_6877f2e5194ce.jpg',24401,'0','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-16 18:43:49'),(9,1,'banner_image','banner_1752691695_6877f3ef88d18.jpg',24401,'0','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-16 18:48:15');
/*!40000 ALTER TABLE `admin_upload_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admins`
--

DROP TABLE IF EXISTS `admins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '管理员用户名',
  `password` varchar(255) NOT NULL COMMENT '管理员密码',
  `email` varchar(100) DEFAULT NULL COMMENT '管理员邮箱',
  `name` varchar(100) DEFAULT NULL COMMENT '管理员姓名',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active启用 inactive禁用',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_username` (`username`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admins`
--

LOCK TABLES `admins` WRITE;
/*!40000 ALTER TABLE `admins` DISABLE KEYS */;
INSERT INTO `admins` VALUES (1,'123456','$2y$10$rZwMwXeVNUvB8kCG0iImWuML9DaKEfapF/TAwTvDIyq7GJff4Hqzu','<EMAIL>','系统管理员','active',NULL,'2025-07-15 11:53:13','2025-07-17 07:45:35'),(2,'12345','$2y$10$HPMfTW0d7GCfnCalMZ4C9eN.A1T/U8uRVfFhbb.5ll4t7FMXpDyaC','','123456','active',NULL,'2025-07-18 07:56:12','2025-07-18 07:56:12');
/*!40000 ALTER TABLE `admins` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `announcement_categories`
--

DROP TABLE IF EXISTS `announcement_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `announcement_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0否 1是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort` (`sort_order`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='公告分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `announcement_categories`
--

LOCK TABLES `announcement_categories` WRITE;
/*!40000 ALTER TABLE `announcement_categories` DISABLE KEYS */;
INSERT INTO `announcement_categories` VALUES (1,'系统公告','系统维护、更新等相关公告',1,1,'2025-07-15 11:53:13','2025-07-15 11:53:13'),(2,'活动公告','各类活动、促销等公告',2,1,'2025-07-15 11:53:13','2025-07-15 11:53:13'),(3,'通知公告','一般性通知公告',3,1,'2025-07-15 11:53:13','2025-07-15 11:53:13'),(4,'紧急公告','紧急重要公告',0,1,'2025-07-15 11:53:13','2025-07-15 11:53:13');
/*!40000 ALTER TABLE `announcement_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `announcement_favorites`
--

DROP TABLE IF EXISTS `announcement_favorites`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `announcement_favorites` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `announcement_id` int(11) NOT NULL COMMENT '公告ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_announcement` (`user_id`,`announcement_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_announcement` (`announcement_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `announcement_favorites_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `announcement_favorites_ibfk_2` FOREIGN KEY (`announcement_id`) REFERENCES `announcements` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='公告收藏表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `announcement_favorites`
--

LOCK TABLES `announcement_favorites` WRITE;
/*!40000 ALTER TABLE `announcement_favorites` DISABLE KEYS */;
INSERT INTO `announcement_favorites` VALUES (1,13,1,'2025-07-18 20:56:02');
/*!40000 ALTER TABLE `announcement_favorites` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `announcement_reads`
--

DROP TABLE IF EXISTS `announcement_reads`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `announcement_reads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `announcement_id` int(11) NOT NULL COMMENT '公告ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `read_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_announcement` (`announcement_id`,`user_id`),
  KEY `idx_announcement` (`announcement_id`),
  KEY `idx_user` (`user_id`),
  CONSTRAINT `announcement_reads_ibfk_1` FOREIGN KEY (`announcement_id`) REFERENCES `announcements` (`id`) ON DELETE CASCADE,
  CONSTRAINT `announcement_reads_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='公告阅读记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `announcement_reads`
--

LOCK TABLES `announcement_reads` WRITE;
/*!40000 ALTER TABLE `announcement_reads` DISABLE KEYS */;
INSERT INTO `announcement_reads` VALUES (1,1,13,'2025-07-18 20:55:59');
/*!40000 ALTER TABLE `announcement_reads` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `announcements`
--

DROP TABLE IF EXISTS `announcements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `announcements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `type` enum('notice','urgent','system','activity') NOT NULL DEFAULT 'notice' COMMENT '公告类型：通知、紧急、系统、活动',
  `status` enum('draft','published','archived') NOT NULL DEFAULT 'draft' COMMENT '状态：草稿、已发布、已归档',
  `priority` tinyint(1) NOT NULL DEFAULT '0' COMMENT '优先级：0普通 1重要 2紧急',
  `is_pinned` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否置顶：0否 1是',
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `author_id` int(11) NOT NULL COMMENT '发布者ID',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '查看次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_pinned` (`is_pinned`),
  KEY `idx_category` (`category_id`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_author` (`author_id`),
  CONSTRAINT `announcements_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `announcement_categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `announcements_ibfk_2` FOREIGN KEY (`author_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='公告表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `announcements`
--

LOCK TABLES `announcements` WRITE;
/*!40000 ALTER TABLE `announcements` DISABLE KEYS */;
INSERT INTO `announcements` VALUES (1,'1','111','notice','published',0,0,NULL,NULL,NULL,1,1,'2025-07-17 07:46:20','2025-07-18 20:55:59');
/*!40000 ALTER TABLE `announcements` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `banners`
--

DROP TABLE IF EXISTS `banners`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `banners` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '轮播图标题',
  `image_url` varchar(500) NOT NULL COMMENT '图片链接',
  `link_type` enum('course','announcement','external','none') NOT NULL DEFAULT 'none' COMMENT '链接类型：course课程 announcement公告 external外部链接 none无链接',
  `link_value` varchar(500) DEFAULT NULL COMMENT '链接值：课程ID、公告ID或外部URL',
  `start_time` datetime DEFAULT NULL COMMENT '开始显示时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束显示时间',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active启用 inactive禁用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序（数字越小越靠前）',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '点击次数',
  `created_by` int(11) NOT NULL COMMENT '创建者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_link_type` (`link_type`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_created_by` (`created_by`),
  CONSTRAINT `banners_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `banners`
--

LOCK TABLES `banners` WRITE;
/*!40000 ALTER TABLE `banners` DISABLE KEYS */;
INSERT INTO `banners` VALUES (10,'欢迎使用在线学习平台','/uploads/banners/2025/07/banner_1752701153_687818e16b4b8.jpg','none','','2025-07-17 05:22:00','2025-08-16 05:22:00','active',1,0,1,'2025-07-16 21:22:38','2025-07-16 21:25:54'),(11,'精品课程推荐','/uploads/banners/2025/07/banner_1752701455_68781a0f1f5a8.jpg','course','1','2025-07-17 05:22:00','2025-08-16 05:22:00','active',2,0,1,'2025-07-16 21:22:38','2025-07-16 21:30:56'),(12,'最新公告通知','/uploads/banners/2025/07/banner_1752701473_68781a21cf42c.jpg','announcement','1','2025-07-17 05:22:00','2025-08-16 05:22:00','active',3,0,1,'2025-07-16 21:22:38','2025-07-16 21:31:14');
/*!40000 ALTER TABLE `banners` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `course_watch_logs`
--

DROP TABLE IF EXISTS `course_watch_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `course_watch_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `course_id` int(11) NOT NULL COMMENT '课程ID',
  `watch_time` int(11) NOT NULL DEFAULT '0' COMMENT '观看时长（秒）',
  `progress_position` int(11) NOT NULL DEFAULT '0' COMMENT '观看进度位置（秒）',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '观看时间',
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_course` (`course_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `course_watch_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `course_watch_logs_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程观看记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `course_watch_logs`
--

LOCK TABLES `course_watch_logs` WRITE;
/*!40000 ALTER TABLE `course_watch_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `course_watch_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `courses`
--

DROP TABLE IF EXISTS `courses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `courses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '课程标题',
  `description` text COMMENT '课程描述',
  `video_url` varchar(500) NOT NULL COMMENT '视频链接',
  `thumbnail` varchar(500) DEFAULT NULL COMMENT '课程缩略图',
  `duration` int(11) DEFAULT NULL COMMENT '视频时长（秒）',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active启用 inactive禁用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `created_by` int(11) NOT NULL COMMENT '创建者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `cover_image` varchar(500) DEFAULT NULL COMMENT '课程封面图片',
  `is_recommended` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否推荐：0否 1是',
  `is_recommend` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否推荐（兼容字段）：0否 1是',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '观看次数',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '课程价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `is_free` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否免费：0否 1是',
  `teacher_name` varchar(100) DEFAULT NULL COMMENT '讲师姓名',
  `subtitle` varchar(500) DEFAULT NULL COMMENT '课程副标题',
  `rating` decimal(3,2) DEFAULT '0.00' COMMENT '课程评分（0-5）',
  `student_count` int(11) NOT NULL DEFAULT '0' COMMENT '学生数量',
  `sales_count` int(11) NOT NULL DEFAULT '0' COMMENT '销售数量',
  `is_on_sale` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否上架销售：0否 1是',
  `lesson_count` int(11) NOT NULL DEFAULT '0' COMMENT '课时数量',
  `total_duration` int(11) NOT NULL DEFAULT '0' COMMENT '总时长（秒）',
  `tags` varchar(500) DEFAULT NULL COMMENT '课程标签，逗号分隔',
  `difficulty` enum('beginner','intermediate','advanced') DEFAULT 'beginner' COMMENT '课程难度',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort_order`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_price` (`price`),
  KEY `idx_is_free` (`is_free`),
  KEY `idx_is_on_sale` (`is_on_sale`),
  CONSTRAINT `courses_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='课程表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `courses`
--

LOCK TABLES `courses` WRITE;
/*!40000 ALTER TABLE `courses` DISABLE KEYS */;
INSERT INTO `courses` VALUES (1,'Web前端开发基础','从零开始学习HTML、CSS、JavaScript，掌握现代Web前端开发技术。适合初学者入门学习。','https://sample-videos.com/zip/10/mp4/480/mp4-sample-1.mp4','https://picsum.photos/400/300?random=1',3600,'active',1,1,'2025-07-15 16:22:14','2025-07-18 13:12:55','https://picsum.photos/800/450?random=1',1,1,1250,0.01,299.00,0,'张老师','0',4.00,320,0,1,6,6300,'','beginner'),(2,'Python编程入门','Python语言基础语法、数据结构、面向对象编程等核心概念详解。','https://sample-videos.com/zip/10/mp4/480/mp4-sample-2.mp4','http://127.0.0.1/uploads/course_thumbnails/2025/07/course_1752647003_6877455b8e4f9.jpg',4200,'active',2,1,'2025-07-15 16:22:14','2025-07-16 10:53:55','https://picsum.photos/800/450?random=2',1,1,980,0.01,0.00,0,'李老师','0',4.00,450,0,1,4,7500,'','beginner');
/*!40000 ALTER TABLE `courses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_verification_tokens`
--

DROP TABLE IF EXISTS `email_verification_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `email_verification_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `email` varchar(100) NOT NULL COMMENT '邮箱地址',
  `token` varchar(255) NOT NULL COMMENT '验证令牌',
  `expires_at` timestamp NOT NULL COMMENT '过期时间',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已使用：0未使用 1已使用',
  `used_at` timestamp NULL DEFAULT NULL COMMENT '使用时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_token` (`token`),
  KEY `idx_email` (`email`),
  KEY `idx_expires_at` (`expires_at`),
  CONSTRAINT `email_verification_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮箱验证令牌表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_verification_tokens`
--

LOCK TABLES `email_verification_tokens` WRITE;
/*!40000 ALTER TABLE `email_verification_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `email_verification_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lesson_watch_logs`
--

DROP TABLE IF EXISTS `lesson_watch_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lesson_watch_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `lesson_id` int(11) NOT NULL COMMENT '课时ID',
  `course_id` int(11) NOT NULL COMMENT '课程ID',
  `watch_time` int(11) NOT NULL DEFAULT '0' COMMENT '观看时长（秒）',
  `progress_position` int(11) NOT NULL DEFAULT '0' COMMENT '观看进度位置（秒）',
  `is_completed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否完成：0否 1是',
  `completion_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '完成率（0-100）',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '观看时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_lesson` (`user_id`,`lesson_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_lesson_id` (`lesson_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_is_completed` (`is_completed`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=623 DEFAULT CHARSET=utf8mb4 COMMENT='课时观看记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lesson_watch_logs`
--

LOCK TABLES `lesson_watch_logs` WRITE;
/*!40000 ALTER TABLE `lesson_watch_logs` DISABLE KEYS */;
INSERT INTO `lesson_watch_logs` VALUES (1,8,2,1,0,0,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94','2025-07-16 12:16:16','2025-07-16 12:16:16'),(2,8,6,2,1,1,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94','2025-07-16 12:16:59','2025-07-16 12:25:28'),(3,8,7,2,1,1,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94','2025-07-16 12:17:01','2025-07-16 12:25:31'),(4,8,8,2,11,11,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94','2025-07-16 12:17:04','2025-07-16 12:25:44'),(5,8,9,2,6,6,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94','2025-07-16 12:17:09','2025-07-16 12:25:39'),(6,8,3,1,0,0,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/95','2025-07-16 12:23:03','2025-07-16 12:23:04'),(14,9,1,1,400,400,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/192','2025-07-16 15:54:19','2025-07-17 21:45:35'),(21,9,2,1,390,390,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/197','2025-07-16 16:03:46','2025-07-17 21:45:35'),(22,9,5,1,400,400,0,11.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/197','2025-07-16 16:03:57','2025-07-16 16:46:08'),(25,9,4,1,372,372,0,13.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/197','2025-07-16 16:04:20','2025-07-16 16:04:20'),(27,9,3,1,1,1,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/79','2025-07-17 21:13:47','2025-07-17 21:17:32'),(140,9,10,1,37,37,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/103','2025-07-17 21:44:12','2025-07-17 21:44:35'),(141,9,6,2,2,2,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/3','2025-07-18 07:20:50','2025-07-18 07:34:10'),(142,13,6,2,92,92,0,1.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/2','2025-07-18 11:18:52','2025-07-18 20:58:45'),(601,13,1,1,105,105,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/82','2025-07-18 15:19:15','2025-07-18 21:02:26'),(610,13,2,1,6,6,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/87','2025-07-18 15:24:22','2025-07-18 20:50:26'),(622,13,10,1,5,5,0,0.00,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/91','2025-07-18 15:27:47','2025-07-18 20:58:51');
/*!40000 ALTER TABLE `lesson_watch_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lessons`
--

DROP TABLE IF EXISTS `lessons`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lessons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) NOT NULL COMMENT '课程ID',
  `title` varchar(255) NOT NULL COMMENT '课时标题',
  `description` text COMMENT '课时描述',
  `video_url` varchar(500) DEFAULT NULL COMMENT '视频链接',
  `vod_file_id` varchar(100) DEFAULT NULL COMMENT '腾讯云点播文件ID',
  `vod_video_url` varchar(500) DEFAULT NULL COMMENT '腾讯云点播视频URL',
  `video_type` enum('url','vod') DEFAULT 'url' COMMENT '视频类型：url链接 vod腾讯云点播',
  `duration` int(11) DEFAULT NULL COMMENT '时长（秒）',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用 1启用',
  `is_free` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否免费：0否 1是',
  `thumbnail` varchar(500) DEFAULT NULL COMMENT '课时缩略图',
  `created_by` int(11) NOT NULL COMMENT '创建者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `lessons_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COMMENT='课时表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lessons`
--

LOCK TABLES `lessons` WRITE;
/*!40000 ALTER TABLE `lessons` DISABLE KEYS */;
INSERT INTO `lessons` VALUES (1,1,'第1课：HTML基础入门','学习HTML的基本语法和常用标签，为网页开发打下基础。','','5145403692208013494','https://1325256586.vod-qcloud.com/e20d94advodcq1325256586/fabd267b5145403692208013494/playlist.m3u8','vod',0,1,1,1,'0',1,'2025-07-16 10:53:55','2025-07-18 06:14:32'),(2,1,'第2课：CSS样式设计','掌握CSS选择器、属性和布局技巧，美化网页外观。','','5145403692150010817','https://1325256586.vod-qcloud.com/e20d94advodcq1325256586/bbb662b35145403692150010817/playlist.m3u8','vod',0,2,1,1,'0',1,'2025-07-16 10:53:55','2025-07-17 21:40:47'),(3,1,'第3课：JavaScript基础','学习JavaScript语法、变量、函数等基础概念。','https://1325256586.vod-qcloud.com/b8f78255vodtranscq1325256586/81fe92c65145403692166581384/v.f1658984.m3u8','','','url',0,3,1,0,'0',1,'2025-07-16 10:53:55','2025-07-17 20:25:36'),(4,1,'第4课：DOM操作','掌握DOM元素的获取、修改和事件处理。','https://sample-videos.com/zip/10/mp4/480/mp4-sample-4.mp4',NULL,NULL,'url',2700,4,1,0,'https://picsum.photos/400/300?random=104',1,'2025-07-16 10:53:55','2025-07-16 10:53:55'),(5,1,'第5课：项目实战','综合运用HTML、CSS、JavaScript完成完整项目。','https://1325256586.vod-qcloud.com/17eaf91cvodtranssh1325256586/02397e805145403691090617815/v.f1658984.m3u8',NULL,NULL,'url',3600,5,1,0,'0',1,'2025-07-16 10:53:55','2025-07-16 12:24:19'),(6,2,'第1课：Python环境搭建','安装Python开发环境，配置IDE和必要工具。','','5145403692232192175','https://1325256586.vod-qcloud.com/e20d94advodcq1325256586/f48a8bdc5145403692232192175/playlist.m3u8','vod',1200,1,1,1,'0',1,'2025-07-16 10:53:55','2025-07-18 07:20:30'),(7,2,'第2课：变量和数据类型','学习Python的基本数据类型和变量操作。','https://sample-videos.com/zip/10/mp4/480/mp4-sample-7.mp4',NULL,NULL,'url',1800,2,1,1,'https://picsum.photos/400/300?random=202',1,'2025-07-16 10:53:55','2025-07-16 10:53:55'),(8,2,'第3课：控制结构','掌握条件语句、循环语句等控制结构。','https://1325256586.vod-qcloud.com/17eaf91cvodtranssh1325256586/02397e805145403691090617815/v.f1658984.m3u8',NULL,NULL,'url',2100,3,1,0,'0',1,'2025-07-16 10:53:55','2025-07-16 12:25:18'),(9,2,'第4课：函数和模块','学习函数定义、参数传递和模块导入。','https://1325256586.vod-qcloud.com/17eaf91cvodtranssh1325256586/02397e805145403691090617815/v.f1658984.m3u8',NULL,NULL,'url',2400,4,1,0,'0',1,'2025-07-16 10:53:55','2025-07-16 12:25:02'),(10,1,'11','11','','5145403692205583689','https://1325256586.vod-qcloud.com/e20d94advodcq1325256586/1ff6ef4a5145403692205583689/playlist.m3u8','vod',0,6,1,0,'0',1,'2025-07-17 21:43:34','2025-07-18 20:01:22');
/*!40000 ALTER TABLE `lessons` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_items`
--

DROP TABLE IF EXISTS `order_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `course_id` int(11) NOT NULL COMMENT '课程ID',
  `course_title` varchar(255) NOT NULL COMMENT '课程标题（冗余字段）',
  `course_price` decimal(10,2) NOT NULL COMMENT '课程价格',
  `original_price` decimal(10,2) NOT NULL COMMENT '课程原价',
  `quantity` int(11) NOT NULL DEFAULT '1' COMMENT '数量',
  `subtotal` decimal(10,2) NOT NULL COMMENT '小计金额',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_course_id` (`course_id`),
  CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=91 DEFAULT CHARSET=utf8mb4 COMMENT='订单商品表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_items`
--

LOCK TABLES `order_items` WRITE;
/*!40000 ALTER TABLE `order_items` DISABLE KEYS */;
INSERT INTO `order_items` VALUES (86,86,2,'Python编程入门',0.01,0.00,1,0.01,'2025-07-18 07:49:13'),(87,87,2,'Python编程入门',0.01,0.00,1,0.01,'2025-07-18 11:16:46'),(88,88,2,'Python编程入门',0.01,0.00,1,0.01,'2025-07-18 11:34:18'),(89,89,2,'Python编程入门',0.01,0.00,1,0.01,'2025-07-18 19:27:40'),(90,90,2,'Python编程入门',0.01,0.00,1,0.01,'2025-07-18 20:52:22');
/*!40000 ALTER TABLE `order_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `actual_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际支付金额',
  `order_status` enum('pending','paid','cancelled','refunded','expired') NOT NULL DEFAULT 'pending' COMMENT '订单状态：待支付、已支付、已取消、已退款、已过期',
  `payment_status` enum('unpaid','paid','refunding','refunded','failed') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态：未支付、已支付、退款中、已退款、支付失败',
  `payment_method` enum('wechat','alipay') DEFAULT NULL COMMENT '支付方式：微信、支付宝',
  `payment_time` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `expire_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单过期时间',
  `remark` text COMMENT '订单备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_expire_time` (`expire_time`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=91 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `orders`
--

LOCK TABLES `orders` WRITE;
/*!40000 ALTER TABLE `orders` DISABLE KEYS */;
INSERT INTO `orders` VALUES (86,'ORD202507181549130000103069',10,0.01,0.00,0.01,'cancelled','unpaid',NULL,NULL,'2025-07-18 08:19:13',NULL,'2025-07-18 07:49:13','2025-07-18 11:16:16'),(87,'ORD202507181916460000134617',13,0.01,0.00,0.01,'cancelled','unpaid',NULL,NULL,'2025-07-18 11:46:46',NULL,'2025-07-18 11:16:46','2025-07-18 20:57:54'),(88,'ORD202507181934180000132817',13,0.01,0.00,0.01,'cancelled','unpaid',NULL,NULL,'2025-07-18 12:04:18',NULL,'2025-07-18 11:34:18','2025-07-18 20:57:53'),(89,'ORD202507190327400000137495',13,0.01,0.00,0.01,'cancelled','unpaid',NULL,NULL,'2025-07-18 19:57:40',NULL,'2025-07-18 19:27:40','2025-07-18 20:57:48'),(90,'ORD202507190452220000131909',13,0.01,0.00,0.01,'cancelled','unpaid',NULL,NULL,'2025-07-18 21:22:22',NULL,'2025-07-18 20:52:22','2025-07-18 20:57:45');
/*!40000 ALTER TABLE `orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payments`
--

DROP TABLE IF EXISTS `payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `payment_no` varchar(32) NOT NULL COMMENT '支付流水号',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `payment_status` enum('pending','success','failed','cancelled','refunded') NOT NULL DEFAULT 'pending' COMMENT '支付状态',
  `third_party_no` varchar(64) DEFAULT NULL COMMENT '第三方支付流水号',
  `prepay_id` varchar(64) DEFAULT NULL COMMENT '微信预支付ID',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '微信交易号',
  `callback_data` text COMMENT '支付回调数据',
  `error_message` text COMMENT '错误信息',
  `paid_at` timestamp NULL DEFAULT NULL COMMENT '支付完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_payment_no` (`payment_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_third_party_no` (`third_party_no`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='支付记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payments`
--

LOCK TABLES `payments` WRITE;
/*!40000 ALTER TABLE `payments` DISABLE KEYS */;
INSERT INTO `payments` VALUES (1,'PAY202507181916470000137300',87,13,'wechat',0.01,'pending','wx18191647024992365f13e692dcedb20000','wx18191647024992365f13e692dcedb20000',NULL,NULL,NULL,NULL,'2025-07-18 11:16:47','2025-07-18 11:16:47'),(2,'PAY202507181934180000131667',88,13,'wechat',0.01,'pending','wx18193418120658a2184bc7ad11085b0001','wx18193418120658a2184bc7ad11085b0001',NULL,NULL,NULL,NULL,'2025-07-18 11:34:18','2025-07-18 11:34:18'),(3,'PAY202507190327400000136541',89,13,'wechat',0.01,'pending','wx190327409083618b42b0996485747a0000','wx190327409083618b42b0996485747a0000',NULL,NULL,NULL,NULL,'2025-07-18 19:27:40','2025-07-18 19:27:41'),(4,'PAY202507190452220000138206',90,13,'wechat',0.01,'pending','wx190452221116925c68c98c95f3501e0000','wx190452221116925c68c98c95f3501e0000',NULL,NULL,NULL,NULL,'2025-07-18 20:52:22','2025-07-18 20:52:22'),(5,'PAY202507190453290000137969',90,13,'wechat',0.01,'pending','wx190453295698256b292d7a80304de40001','wx190453295698256b292d7a80304de40001',NULL,NULL,NULL,NULL,'2025-07-18 20:53:29','2025-07-18 20:53:30');
/*!40000 ALTER TABLE `payments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `phone_bind_logs`
--

DROP TABLE IF EXISTS `phone_bind_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `phone_bind_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `action` enum('bind','unbind','verify') NOT NULL COMMENT '操作类型：bind绑定 unbind解绑 verify验证',
  `status` enum('success','failed') NOT NULL COMMENT '操作状态：success成功 failed失败',
  `failure_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_action` (`action`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `phone_bind_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='手机号绑定日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `phone_bind_logs`
--

LOCK TABLES `phone_bind_logs` WRITE;
/*!40000 ALTER TABLE `phone_bind_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `phone_bind_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `refunds`
--

DROP TABLE IF EXISTS `refunds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `refunds` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `refund_no` varchar(32) NOT NULL COMMENT '退款单号',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `payment_id` int(11) NOT NULL COMMENT '支付记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
  `refund_reason` varchar(500) DEFAULT NULL COMMENT '退款原因',
  `refund_status` enum('pending','processing','success','failed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '退款状态',
  `third_party_refund_no` varchar(64) DEFAULT NULL COMMENT '第三方退款流水号',
  `callback_data` text COMMENT '退款回调数据',
  `error_message` text COMMENT '错误信息',
  `processed_by` int(11) DEFAULT NULL COMMENT '处理人（管理员ID）',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_refund_no` (`refund_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_payment_id` (`payment_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_refund_status` (`refund_status`),
  KEY `idx_processed_by` (`processed_by`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `refunds_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `refunds_ibfk_2` FOREIGN KEY (`payment_id`) REFERENCES `payments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `refunds_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `refunds_ibfk_4` FOREIGN KEY (`processed_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `refunds`
--

LOCK TABLES `refunds` WRITE;
/*!40000 ALTER TABLE `refunds` DISABLE KEYS */;
/*!40000 ALTER TABLE `refunds` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `settings`
--

DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(255) NOT NULL COMMENT '设置键',
  `setting_value` text NOT NULL COMMENT '设置值',
  `setting_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string' COMMENT '设置类型',
  `description` varchar(500) DEFAULT NULL COMMENT '设置描述',
  `group_name` varchar(100) DEFAULT 'general' COMMENT '设置分组',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_setting_key` (`setting_key`),
  KEY `idx_group` (`group_name`)
) ENGINE=InnoDB AUTO_INCREMENT=81300 DEFAULT CHARSET=utf8mb4 COMMENT='系统设置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `settings`
--

LOCK TABLES `settings` WRITE;
/*!40000 ALTER TABLE `settings` DISABLE KEYS */;
INSERT INTO `settings` VALUES (1,'website_title','课程视频管理系统','string','网站标题','basic','2025-07-15 11:53:13','2025-07-15 11:53:13'),(2,'website_description','基于uni-app的在线课程视频管理系统','string','网站描述','basic','2025-07-15 11:53:13','2025-07-15 11:53:13'),(3,'wechat_app_id','wxf3cbfda938fac14c','string','微信小程序AppID','wechat','2025-07-15 11:53:13','2025-07-16 05:44:06'),(4,'wechat_app_secret','111674e2d2059383a2dbff1c2351c07f','string','微信小程序AppSecret','wechat','2025-07-15 11:53:13','2025-07-16 05:44:06'),(5,'jwt_secret_key','default-secret-************************************','string','JWT密钥','auth','2025-07-15 11:53:13','2025-07-16 05:42:45'),(6,'jwt_access_token_expire','7200','number','JWT访问令牌过期时间（秒）','auth','2025-07-15 11:53:13','2025-07-15 11:53:13'),(7,'jwt_refresh_token_expire','604800','number','JWT刷新令牌过期时间（秒）','auth','2025-07-15 11:53:13','2025-07-15 11:53:13'),(8,'login_max_attempts','5','number','登录最大尝试次数','auth','2025-07-15 11:53:13','2025-07-15 11:53:13'),(9,'login_lockout_duration','1800','number','登录锁定时间（秒）','auth','2025-07-15 11:53:13','2025-07-15 11:53:13'),(10,'password_min_length','6','number','密码最小长度','auth','2025-07-15 11:53:13','2025-07-15 11:53:13'),(11,'require_email_verification','0','boolean','是否需要邮箱验证：1需要 0不需要','auth','2025-07-15 11:53:13','2025-07-18 09:00:06'),(12,'allow_registration','1','boolean','是否允许用户注册：1允许 0不允许','auth','2025-07-15 11:53:13','2025-07-18 09:00:06'),(13,'phone_bind_required','1','boolean','是否要求绑定手机号','phone','2025-07-15 11:53:13','2025-07-18 14:53:46'),(14,'phone_bind_force_new_user','1','boolean','新用户是否强制绑定手机号','phone','2025-07-15 11:53:13','2025-07-18 14:53:46'),(15,'sms_code_length','6','number','短信验证码长度','sms','2025-07-15 11:53:13','2025-07-15 11:53:13'),(16,'sms_code_expire_minutes','5','number','短信验证码过期时间（分钟）','sms','2025-07-15 11:53:13','2025-07-15 11:53:13'),(17,'sms_daily_limit','10','number','每日短信发送限制','sms','2025-07-15 11:53:13','2025-07-15 11:53:13'),(18,'sms_provider','mock','string','短信服务提供商','sms','2025-07-15 11:53:13','2025-07-15 11:53:13'),(19,'sms_api_key','','string','短信API密钥','sms','2025-07-15 11:53:13','2025-07-15 11:53:13'),(20,'sms_api_secret','','string','短信API密钥','sms','2025-07-15 11:53:13','2025-07-15 11:53:13'),(21,'sms_sign_name','课程系统','string','短信签名','sms','2025-07-15 11:53:13','2025-07-15 11:53:13'),(22,'sms_template_bind','您的验证码是：{code}，有效期{expire}分钟，请勿泄露给他人。','string','绑定手机号短信模板','sms','2025-07-15 11:53:13','2025-07-15 11:53:13'),(23,'db_installed','1','boolean','数据库是否已安装','system','2025-07-15 11:53:13','2025-07-15 11:53:13'),(24,'db_version','1.0.0','string','数据库版本','system','2025-07-15 11:53:13','2025-07-15 11:53:13'),(25,'install_time','2025-07-15 19:53:13','string','安装时间','system','2025-07-15 11:53:13','2025-07-15 11:53:13'),(26,'wechat_pay_enabled','1','boolean','是否启用微信支付','payment','2025-07-15 14:13:18','2025-07-15 16:41:08'),(27,'wechat_pay_app_id','wxf3cbfda938fac14c','string','微信小程序AppID','payment','2025-07-15 14:13:18','2025-07-15 17:58:41'),(28,'wechat_pay_mch_id','1637053783','string','微信支付商户号','payment','2025-07-15 14:13:18','2025-07-15 16:40:48'),(29,'wechat_pay_api_key','yxy3241734714YXYyxy3241734714YXY','string','微信支付API密钥','payment','2025-07-15 14:13:18','2025-07-15 16:40:48'),(30,'wechat_pay_cert_path','','string','微信支付证书路径','payment','2025-07-15 14:13:18','2025-07-15 14:13:18'),(31,'wechat_pay_key_path','','string','微信支付私钥路径','payment','2025-07-15 14:13:18','2025-07-15 14:13:18'),(32,'wechat_pay_notify_url','http://127.0.0.1/api/payment-wechat-notify.php','string','微信支付回调地址','payment','2025-07-15 14:13:18','2025-07-15 17:39:15'),(33,'order_expire_minutes','30','number','订单过期时间（分钟）','payment','2025-07-15 14:13:18','2025-07-15 14:13:18'),(34,'auto_cancel_unpaid_orders','1','boolean','是否自动取消未支付订单','payment','2025-07-15 14:13:18','2025-07-15 14:13:18'),(35,'refund_enabled','1','boolean','是否启用退款功能','payment','2025-07-15 14:13:18','2025-07-15 14:13:18'),(36,'payment_system_installed','1','boolean','支付系统是否已安装','system','2025-07-15 14:13:18','2025-07-15 14:13:18'),(37,'payment_system_version','1.0.0','string','支付系统版本','system','2025-07-15 14:13:18','2025-07-15 14:13:18'),(565,'phone_bind_mode','direct','string','手机号绑定模式','general','2025-07-16 05:36:14','2025-07-16 06:31:38'),(7145,'phone_bind_force_purchase','1','boolean','购买课程时是否强制绑定手机号','phone','2025-07-16 07:00:26','2025-07-16 07:00:26'),(21394,'lesson_auto_next','1','boolean','是否自动播放下一课时','lesson','2025-07-16 11:55:23','2025-07-16 11:55:23'),(21395,'lesson_completion_threshold','90','number','课时完成判定阈值（百分比）','lesson','2025-07-16 11:55:23','2025-07-16 11:55:23'),(21396,'lesson_progress_save_interval','30','number','进度保存间隔（秒）','lesson','2025-07-16 11:55:23','2025-07-16 11:55:23'),(55637,'epay_enabled','1','boolean','是否启用码支付易支付','payment','2025-07-16 22:21:04','2025-07-16 22:21:25'),(55638,'epay_api_url','http://116.196.85.235','string','码支付易支付API地址','payment','2025-07-16 22:21:04','2025-07-17 01:09:29'),(55639,'epay_partner_id','1000','string','码支付易支付商户PID','payment','2025-07-16 22:21:04','2025-07-16 22:21:12'),(55640,'epay_partner_key','37afed13232154b6d90a0b9542ad5893','string','码支付易支付商户密钥','payment','2025-07-16 22:21:04','2025-07-16 22:21:12'),(55641,'epay_notify_url','http://127.0.0.1/api/payment-epay-notify.php','string','码支付易支付回调通知URL','payment','2025-07-16 22:21:04','2025-07-16 22:21:04'),(55642,'epay_return_url','http://127.0.0.1/pages/payment/result','string','码支付易支付同步返回URL','payment','2025-07-16 22:21:04','2025-07-17 01:09:43'),(55643,'epay_supported_methods','alipay,wxpay','string','支持的支付方式（逗号分隔）','payment','2025-07-16 22:21:04','2025-07-17 01:21:43'),(65532,'registration_auto_login','1','string','注册后是否自动登录：1是 0否','general','2025-07-18 09:00:06','2025-07-18 09:00:06'),(65533,'email_verification_expire_hours','24','string','邮箱验证链接有效期（小时）','general','2025-07-18 09:00:06','2025-07-18 09:00:06'),(65534,'smtp_host','','string','SMTP服务器地址','general','2025-07-18 09:00:06','2025-07-18 09:00:06'),(65535,'smtp_port','587','string','SMTP端口','general','2025-07-18 09:00:06','2025-07-18 09:00:06'),(65536,'smtp_username','','string','SMTP用户名','general','2025-07-18 09:00:06','2025-07-18 09:00:06'),(65537,'smtp_password','','string','SMTP密码','general','2025-07-18 09:00:06','2025-07-18 09:00:06'),(65538,'smtp_encryption','tls','string','SMTP加密方式：tls/ssl','general','2025-07-18 09:00:06','2025-07-18 09:00:06'),(65539,'smtp_from_email','','string','发件人邮箱','general','2025-07-18 09:00:06','2025-07-18 09:00:06'),(65540,'smtp_from_name','课程学习系统','string','发件人名称','general','2025-07-18 09:00:06','2025-07-18 09:00:06'),(72229,'allow_password_change','0','string',NULL,'general','2025-07-18 13:40:13','2025-07-18 14:05:05'),(72230,'allow_profile_edit','1','string',NULL,'general','2025-07-18 13:40:13','2025-07-18 14:13:57'),(72231,'password_change_notice','管理员已禁用密码修改功能，如需修改密码请联系客服。','string',NULL,'general','2025-07-18 13:40:13','2025-07-18 13:40:13'),(72232,'user_features_update_time','2025-07-18 22:13:57','string',NULL,'general','2025-07-18 13:40:13','2025-07-18 14:13:57'),(79592,'phone_bind_allow_change','1','string',NULL,'general','2025-07-18 14:53:46','2025-07-18 14:53:46'),(79626,'admin_email','<EMAIL>','string',NULL,'general','2025-07-18 14:59:32','2025-07-18 14:59:32'),(79627,'admin_panel_title','课程管理中心','string',NULL,'general','2025-07-18 14:59:32','2025-07-18 21:15:27'),(79628,'admin_panel_subtitle','阳者','string',NULL,'general','2025-07-18 14:59:32','2025-07-18 14:59:32'),(79630,'timezone','Asia/Shanghai','string',NULL,'general','2025-07-18 14:59:32','2025-07-18 14:59:32'),(79631,'max_upload_size','10','string',NULL,'general','2025-07-18 14:59:32','2025-07-18 14:59:32'),(80063,'miniprogram_name','课程学习系统','string',NULL,'general','2025-07-18 21:24:32','2025-07-18 21:24:32'),(80064,'miniprogram_welcome_text','欢迎使用课程学习系统','string',NULL,'general','2025-07-18 21:24:32','2025-07-18 21:24:32'),(80069,'miniprogram_logo','uploads/miniprogram_logo_1752873915.jpg','string',NULL,'general','2025-07-18 21:24:32','2025-07-18 21:25:15'),(80134,'app_logo_url','/uploads/logos/logo_20250719161521_687b54192f598.jpg','string','应用Logo图片URL','appearance','2025-07-19 08:03:30','2025-07-19 08:15:21'),(80135,'app_title','课程学习','string','应用主标题','appearance','2025-07-19 08:03:30','2025-07-19 08:06:01'),(80136,'app_subtitle','在线学习，随时随','string','应用副标题','appearance','2025-07-19 08:03:30','2025-07-19 08:06:01'),(80137,'logo_upload_path','/uploads/logos/','string','Logo上传路径','appearance','2025-07-19 08:03:30','2025-07-19 08:03:30'),(80138,'max_logo_size','2048','number','Logo文件最大大小（KB）','appearance','2025-07-19 08:03:30','2025-07-19 08:03:30'),(80139,'allowed_logo_types','jpg,jpeg,png,gif','string','允许的Logo文件类型','appearance','2025-07-19 08:03:30','2025-07-19 08:03:30');
/*!40000 ALTER TABLE `settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sms_codes`
--

DROP TABLE IF EXISTS `sms_codes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sms_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `code` varchar(10) NOT NULL COMMENT '验证码',
  `type` enum('bind','login','reset') NOT NULL DEFAULT 'bind' COMMENT '验证码类型：bind绑定 login登录 reset重置',
  `user_id` int(11) DEFAULT NULL COMMENT '关联用户ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已使用：0未使用 1已使用',
  `expires_at` timestamp NOT NULL COMMENT '过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_code` (`code`),
  KEY `idx_type` (`type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_is_used` (`is_used`),
  KEY `idx_expires_at` (`expires_at`),
  CONSTRAINT `sms_codes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信验证码表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sms_codes`
--

LOCK TABLES `sms_codes` WRITE;
/*!40000 ALTER TABLE `sms_codes` DISABLE KEYS */;
/*!40000 ALTER TABLE `sms_codes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `upload_logs`
--

DROP TABLE IF EXISTS `upload_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `upload_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `upload_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_url` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_upload_type` (`upload_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `upload_logs`
--

LOCK TABLES `upload_logs` WRITE;
/*!40000 ALTER TABLE `upload_logs` DISABLE KEYS */;
INSERT INTO `upload_logs` VALUES (1,3,'avatar','3_1752583314_68764c92c36dc.jpg',24401,'/uploads/avatar/2025/07/3_1752583314_68764c92c36dc.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-15 12:41:54'),(2,8,'avatar','8_1752649571_68774f636ee2c.jpg',24401,'/uploads/avatar/2025/07/8_1752649571_68774f636ee2c.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 07:06:11'),(3,8,'avatar','8_1752649795_6877504334d2d.jpg',24401,'/uploads/avatar/2025/07/8_1752649795_6877504334d2d.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 07:09:55'),(4,8,'avatar','8_1752650694_687753c67f36f.jpg',24401,'/uploads/avatar/2025/07/8_1752650694_687753c67f36f.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 07:24:54'),(5,8,'avatar','8_1752650736_687753f06105e.jpg',24401,'/uploads/avatar/2025/07/8_1752650736_687753f06105e.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 07:25:36'),(6,8,'avatar','8_1752653003_68775ccb28053.jpg',24401,'/uploads/avatar/2025/07/8_1752653003_68775ccb28053.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 08:03:23'),(7,8,'avatar','8_1752658807_687773770d727.jpg',24401,'/uploads/avatar/2025/07/8_1752658807_687773770d727.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 09:40:07'),(8,8,'avatar','8_1752658872_687773b8584f2.jpg',24401,'/uploads/avatar/2025/07/8_1752658872_687773b8584f2.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 09:41:12'),(9,8,'avatar','8_1752658878_687773bee659d.jpg',24401,'/uploads/avatar/2025/07/8_1752658878_687773bee659d.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 09:41:18'),(10,8,'avatar','8_1752658889_687773c90de43.jpg',24401,'/uploads/avatar/2025/07/8_1752658889_687773c90de43.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 09:41:29'),(11,8,'avatar','8_1752659370_687775aa9ef15.jpg',24401,'/uploads/avatar/2025/07/8_1752659370_687775aa9ef15.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 09:49:30'),(12,8,'avatar','8_1752659631_687776af58bb5.jpg',24401,'/uploads/avatar/2025/07/8_1752659631_687776af58bb5.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 09:53:51'),(13,8,'avatar','8_1752659917_687777cd0ae9f.jpg',24401,'/uploads/avatar/2025/07/8_1752659917_687777cd0ae9f.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 09:58:37'),(14,8,'avatar','8_1752659974_687778064d390.jpg',24401,'/uploads/avatar/2025/07/8_1752659974_687778064d390.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 09:59:34'),(15,8,'avatar','8_1752660394_687779aae0f5d.jpg',24401,'/uploads/avatar/2025/07/8_1752660394_687779aae0f5d.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 10:06:34'),(16,8,'avatar','8_1752660668_68777abcce9a8.jpg',24401,'/uploads/avatar/2025/07/8_1752660668_68777abcce9a8.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-16 10:11:08'),(17,13,'avatar','13_1752847882_687a560a9c179.jpg',24401,'/uploads/avatar/2025/07/13_1752847882_687a560a9c179.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-18 14:11:22'),(18,13,'avatar','13_1752848285_687a579d81fd9.jpg',24401,'/uploads/avatar/2025/07/13_1752848285_687a579d81fd9.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-18 14:18:05'),(19,13,'avatar','13_1752848663_687a591785d75.jpg',24401,'/uploads/avatar/2025/07/13_1752848663_687a591785d75.jpg','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/','2025-07-18 14:24:23'),(20,13,'avatar','13_1752872063_687ab47fa51de.jpg',24401,'/uploads/avatar/2025/07/13_1752872063_687ab47fa51de.jpg','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540615) XWEB/14199','2025-07-18 20:54:23');
/*!40000 ALTER TABLE `upload_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_courses`
--

DROP TABLE IF EXISTS `user_courses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_courses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `course_id` int(11) NOT NULL COMMENT '课程ID',
  `assigned_by` int(11) NOT NULL COMMENT '分配者ID（管理员）',
  `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `status` enum('active','expired','revoked') NOT NULL DEFAULT 'active' COMMENT '状态：active有效 expired过期 revoked撤销',
  `last_watched_at` timestamp NULL DEFAULT NULL COMMENT '最后观看时间',
  `watch_progress` decimal(5,2) DEFAULT '0.00' COMMENT '观看进度百分比（0-100）',
  `watch_count` int(11) NOT NULL DEFAULT '0' COMMENT '观看次数',
  `total_watch_time` int(11) NOT NULL DEFAULT '0' COMMENT '总观看时长（秒）',
  `order_id` int(11) DEFAULT NULL COMMENT '关联订单ID',
  `purchase_type` enum('assigned','purchased') NOT NULL DEFAULT 'assigned' COMMENT '获得方式：分配、购买',
  `purchase_price` decimal(10,2) DEFAULT NULL COMMENT '购买价格',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_course` (`user_id`,`course_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_course` (`course_id`),
  KEY `idx_assigned_by` (`assigned_by`),
  KEY `idx_status` (`status`),
  KEY `idx_expires` (`expires_at`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_purchase_type` (`purchase_type`),
  KEY `idx_expires_at` (`expires_at`),
  CONSTRAINT `user_courses_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_courses_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_courses_ibfk_3` FOREIGN KEY (`assigned_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='用户课程关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_courses`
--

LOCK TABLES `user_courses` WRITE;
/*!40000 ALTER TABLE `user_courses` DISABLE KEYS */;
INSERT INTO `user_courses` VALUES (4,10,2,1,'2025-07-18 07:49:33',NULL,'active',NULL,0.00,0,0,NULL,'assigned',NULL),(5,13,1,1,'2025-07-18 13:13:16',NULL,'active','2025-07-18 21:02:26',0.00,1,0,NULL,'assigned',NULL);
/*!40000 ALTER TABLE `user_courses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_login_logs`
--

DROP TABLE IF EXISTS `user_login_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `login_type` enum('traditional','wechat') NOT NULL COMMENT '登录类型',
  `login_method` enum('username','email','phone','wechat_code') NOT NULL COMMENT '登录方式',
  `login_status` enum('success','failed') NOT NULL COMMENT '登录状态',
  `failure_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `device_info` text COMMENT '设备信息',
  `location` varchar(255) DEFAULT NULL COMMENT '登录地点',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_type` (`login_type`),
  KEY `idx_login_status` (`login_status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `user_login_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_login_logs`
--

LOCK TABLES `user_login_logs` WRITE;
/*!40000 ALTER TABLE `user_login_logs` DISABLE KEYS */;
INSERT INTO `user_login_logs` VALUES (9,NULL,'traditional','username','failed','用户不存在','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',NULL,NULL,'2025-07-15 17:31:35'),(47,10,'traditional','username','success',NULL,'127.0.0.1','Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36 HBuilderX',NULL,NULL,'2025-07-18 07:48:54'),(51,13,'wechat','wechat_code','success',NULL,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/14',NULL,NULL,'2025-07-18 08:02:15'),(52,10,'traditional','username','success',NULL,'127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,'2025-07-18 08:57:25'),(53,13,'wechat','wechat_code','success',NULL,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/2',NULL,NULL,'2025-07-18 11:16:25'),(54,13,'wechat','wechat_code','success',NULL,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/17',NULL,NULL,'2025-07-18 13:39:51'),(55,13,'wechat','wechat_code','success',NULL,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/37',NULL,NULL,'2025-07-18 14:19:03'),(56,13,'wechat','wechat_code','success',NULL,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/44',NULL,NULL,'2025-07-18 14:25:20'),(57,13,'wechat','wechat_code','success',NULL,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/178',NULL,NULL,'2025-07-18 17:26:06'),(58,13,'wechat','wechat_code','success',NULL,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/309',NULL,NULL,'2025-07-18 19:26:37'),(59,13,'wechat','wechat_code','success',NULL,'127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540615) XWEB/14199',NULL,NULL,'2025-07-18 20:50:59'),(60,13,'wechat','wechat_code','success',NULL,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/18',NULL,NULL,'2025-07-19 08:17:14');
/*!40000 ALTER TABLE `user_login_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `user_phone_status`
--

DROP TABLE IF EXISTS `user_phone_status`;
/*!50001 DROP VIEW IF EXISTS `user_phone_status`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `user_phone_status` AS SELECT 
 1 AS `id`,
 1 AS `name`,
 1 AS `email`,
 1 AS `phone`,
 1 AS `phone_verified`,
 1 AS `phone_bind_required`,
 1 AS `phone_bind_time`,
 1 AS `login_type`,
 1 AS `status`,
 1 AS `bind_status`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `user_registration_logs`
--

DROP TABLE IF EXISTS `user_registration_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_registration_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(100) NOT NULL COMMENT '注册邮箱',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `status` enum('success','failed','pending_verification') NOT NULL DEFAULT 'pending_verification' COMMENT '注册状态',
  `failure_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `user_id` int(11) DEFAULT NULL COMMENT '成功注册的用户ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户注册日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_registration_logs`
--

LOCK TABLES `user_registration_logs` WRITE;
/*!40000 ALTER TABLE `user_registration_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_registration_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_tokens`
--

DROP TABLE IF EXISTS `user_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `token` varchar(500) NOT NULL COMMENT 'JWT令牌',
  `token_type` enum('access','refresh') NOT NULL DEFAULT 'access' COMMENT '令牌类型',
  `expires_at` timestamp NOT NULL COMMENT '过期时间',
  `is_revoked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已撤销',
  `device_info` text COMMENT '设备信息',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_token` (`token`(255)),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_token_type` (`token_type`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_is_revoked` (`is_revoked`),
  CONSTRAINT `user_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=131 DEFAULT CHARSET=utf8mb4 COMMENT='用户令牌表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_tokens`
--

LOCK TABLES `user_tokens` WRITE;
/*!40000 ALTER TABLE `user_tokens` DISABLE KEYS */;
INSERT INTO `user_tokens` VALUES (101,10,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMCwidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1MjgyNDkzNCwiZXhwIjoxNzUyODMyMTM0fQ.U2A8uYeMgCbejD8ELl2JQASsVeUOmWR54iRnW-23RvI','access','2025-07-18 09:48:54',0,'Array','127.0.0.1','Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36 HBuilderX','2025-07-18 07:48:54'),(102,10,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMCwidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI4MjQ5MzQsImV4cCI6MTc1MzQyOTczNH0.It7zqAc4quZeoVzB2ojnk-G_th8EsmUEbODQOrL0CME','refresh','2025-07-25 07:48:54',0,'Array','127.0.0.1','Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36 HBuilderX','2025-07-18 07:48:54'),(109,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1MjgyNTczNSwiZXhwIjoxNzUyODMyOTM1fQ.S5S8mqaOZinG8agVopjMHYVJMEp--zeDOc3sC27v488','access','2025-07-18 10:02:15',1,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/14','2025-07-18 08:02:15'),(110,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI4MjU3MzUsImV4cCI6MTc1MzQzMDUzNX0.O1Z88jXref3P-GcjHrfkhAcUJdnG8O2D9uXlvYAzcTo','refresh','2025-07-25 08:02:15',1,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/14','2025-07-18 08:02:15'),(111,10,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMCwidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1MjgyOTA0NSwiZXhwIjoxNzUyODM2MjQ1fQ.wmAqh_wAJ5foi67JDoM-pWi8tDdfh6gfyean071O9Fg','access','2025-07-18 10:57:25',0,NULL,'127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-18 08:57:25'),(112,10,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMCwidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI4MjkwNDUsImV4cCI6MTc1MzQzMzg0NX0.R8rxsD8D5t4rsXVLM4j_VuSsTlOqV7ZJAwCFiG7CaZ4','refresh','2025-07-25 08:57:25',0,NULL,'127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-18 08:57:25'),(113,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1MjgzNzM4NSwiZXhwIjoxNzUyODQ0NTg1fQ.mzicqqF0pAZRzqauE1FGp2w9y22OJ3D_L5EMHoPZc8w','access','2025-07-18 13:16:25',1,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/2','2025-07-18 11:16:25'),(114,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI4MzczODUsImV4cCI6MTc1MzQ0MjE4NX0.9xKqcCFTlJ-W7oDBbY3QZntBpPy2ki8EyW6A1_1SEyE','refresh','2025-07-25 11:16:25',1,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/2','2025-07-18 11:16:25'),(115,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1Mjg0NDcxOCwiZXhwIjoxNzUyODUxOTE4fQ.BO5MqYUaJl4MBLIJnE54LHkQgoQDTV01CYlYNhG0Ioo','access','2025-07-18 15:18:38',1,NULL,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/3','2025-07-18 13:18:38'),(116,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1Mjg0NTk5MSwiZXhwIjoxNzUyODUzMTkxfQ.8umvS0WVPKcTNRHWIJEoCLVtZ8sljx8KXEuZf8IMIG4','access','2025-07-18 15:39:51',0,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/17','2025-07-18 13:39:51'),(117,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI4NDU5OTEsImV4cCI6MTc1MzQ1MDc5MX0.5t_M_I21kNWGqUlvDqJzlW_xgXaGODH_KMQTyqx3nSQ','refresh','2025-07-25 13:39:51',0,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/17','2025-07-18 13:39:51'),(118,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1Mjg0ODM0MywiZXhwIjoxNzUyODU1NTQzfQ.mCNVUXiMyHqmGQ-bh_uefuhFuvplHM9prGBF1Vkb1bc','access','2025-07-18 16:19:03',0,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/37','2025-07-18 14:19:03'),(119,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI4NDgzNDMsImV4cCI6MTc1MzQ1MzE0M30.L3RQokPDToFM5uZsBpoHB6r4oAvTPRzRzCOI_e1Iwpw','refresh','2025-07-25 14:19:03',0,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/37','2025-07-18 14:19:03'),(120,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1Mjg0ODcyMCwiZXhwIjoxNzUyODU1OTIwfQ.4nCVy3azLn9lWbJ5xWKEUnn5J03fzBk3HxTHF8aSHA4','access','2025-07-18 16:25:20',0,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/44','2025-07-18 14:25:20'),(121,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI4NDg3MjAsImV4cCI6MTc1MzQ1MzUyMH0.9l7SKCFznu5-a_beruJK_lB4KVF1VwWChps_yVLnv2Q','refresh','2025-07-25 14:25:20',0,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/44','2025-07-18 14:25:20'),(122,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1Mjg1NTk4MiwiZXhwIjoxNzUyODYzMTgyfQ.6DsmucUUe_X3BNCuau6b-6KMFmRul6Cy8xGy6v1ZCxc','access','2025-07-18 18:26:22',0,NULL,'127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/126','2025-07-18 16:26:22'),(123,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1Mjg1OTU2NiwiZXhwIjoxNzUyODY2NzY2fQ.Gk0e_EGqZ26i_fFZSlf5ShuoPssvGnDV-EUIsndl7qs','access','2025-07-18 19:26:06',0,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/178','2025-07-18 17:26:06'),(124,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI4NTk1NjYsImV4cCI6MTc1MzQ2NDM2Nn0.YqWWdShHySYJIr2Ly5thR1jFaEszk12_ki7ZJ_7jhN4','refresh','2025-07-25 17:26:06',0,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/178','2025-07-18 17:26:06'),(125,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1Mjg2Njc5NywiZXhwIjoxNzUyODczOTk3fQ.NUFdKnNhc-L_4c0SCmA69eRxHxr7-jVK8qeQoZE69ys','access','2025-07-18 21:26:37',0,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/309','2025-07-18 19:26:37'),(126,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI4NjY3OTcsImV4cCI6MTc1MzQ3MTU5N30.7XdHAtZu2gdoLAUzzSHTjgrqo2EHTaVqJc-8gD_IUuA','refresh','2025-07-25 19:26:37',0,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/309','2025-07-18 19:26:37'),(127,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1Mjg3MTg1OSwiZXhwIjoxNzUyODc5MDU5fQ.JRyrXpDY1TxDtSqi8VB_O1FqFWiGkx1lbwIdfkY1xCA','access','2025-07-18 22:50:59',0,'Array','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540615) XWEB/14199','2025-07-18 20:50:59'),(128,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI4NzE4NTksImV4cCI6MTc1MzQ3NjY1OX0.MXIlKWAfRJQQKzDvAojD3ZYY8EN1jpol7Br21F6iVuo','refresh','2025-07-25 20:50:59',0,'Array','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540615) XWEB/14199','2025-07-18 20:50:59'),(129,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1MjkxMzAzNCwiZXhwIjoxNzUyOTIwMjM0fQ.Zj9Xt7f47TQVAvCFl717lNzM1pjxPCCoREt9LxAW0dg','access','2025-07-19 10:17:14',0,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/18','2025-07-19 08:17:14'),(130,13,'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMywidHlwZSI6InJlZnJlc2giLCJpYXQiOjE3NTI5MTMwMzQsImV4cCI6MTc1MzUxNzgzNH0.EeYVV4OgzsZMLSnlP3q5XvTlv-bpP7qeSNKLPrhQjDU','refresh','2025-07-26 08:17:14',0,'Array','127.0.0.1','Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/18','2025-07-19 08:17:14');
/*!40000 ALTER TABLE `user_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '用户真实姓名',
  `email` varchar(100) NOT NULL COMMENT '用户邮箱',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名（传统登录）',
  `password` varchar(255) DEFAULT NULL COMMENT '密码（传统登录）',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
  `gender` tinyint(1) DEFAULT '0' COMMENT '性别：0未知 1男 2女',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `status` enum('active','inactive','banned') NOT NULL DEFAULT 'active' COMMENT '状态：active活跃 inactive非活跃 banned禁用',
  `login_type` enum('traditional','wechat','both') NOT NULL DEFAULT 'traditional' COMMENT '登录类型：traditional传统 wechat微信 both两种',
  `phone_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '手机号是否已验证：0未验证 1已验证',
  `email_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '邮箱是否已验证：0未验证 1已验证',
  `email_verified_at` timestamp NULL DEFAULT NULL COMMENT '邮箱验证时间',
  `phone_bind_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否需要绑定手机号：0不需要 1需要',
  `phone_bind_time` timestamp NULL DEFAULT NULL COMMENT '手机号绑定时间',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int(11) NOT NULL DEFAULT '0' COMMENT '登录次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_email` (`email`),
  UNIQUE KEY `unique_username` (`username`),
  KEY `idx_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_login_type` (`login_type`),
  KEY `idx_phone_verified` (`phone_verified`),
  KEY `idx_phone_bind_required` (`phone_bind_required`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (10,'yxy y','<EMAIL>','123456','$2y$10$FNVfKKelA1NkMemOJuqF2u9mzLqJDrLWRY3gIIh0EJBjR6Rh3zGX6','17818001540',NULL,'yxy y',0,NULL,'active','traditional',0,0,NULL,0,NULL,'2025-07-18 08:57:25','127.0.0.1',2,'2025-07-18 07:48:39','2025-07-18 08:57:25'),(13,'微信用','<EMAIL>',NULL,'$2y$10$VWtb6kFzc2TT7Pu5rUdRr.BA3RFLuyPa2znjpU/QLkWozAVOBAr0W','','','微信用户',0,NULL,'active','wechat',0,0,NULL,0,NULL,'2025-07-19 08:17:14','127.0.0.1',9,'2025-07-18 08:02:15','2025-07-19 08:17:14');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `watermark_configs`
--

DROP TABLE IF EXISTS `watermark_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `watermark_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `lesson_id` int(11) DEFAULT NULL,
  `config_data` json NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_lesson_id` (`lesson_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `watermark_configs`
--

LOCK TABLES `watermark_configs` WRITE;
/*!40000 ALTER TABLE `watermark_configs` DISABLE KEYS */;
INSERT INTO `watermark_configs` VALUES (1,NULL,NULL,'{\"enabled\": 1, \"anti_record\": {\"enabled\": 1, \"overlay_duration\": 2, \"show_probability\": 0.1, \"detection_interval\": 10}, \"time_watermark\": {\"format\": \"Y-m-d H:i:s\", \"enabled\": 1, \"opacity\": 0.6, \"position\": \"top-right\"}, \"user_watermark\": {\"moving\": 1, \"enabled\": 0, \"opacity\": 0.6, \"position\": \"top-left\", \"show_user_id\": 1, \"show_username\": 1}, \"course_watermark\": {\"enabled\": 0, \"opacity\": 0.6, \"position\": \"bottom-left\", \"show_course_title\": 0}, \"security_logging\": {\"enabled\": 1, \"log_app_hide\": 1, \"log_screen_capture\": 1, \"log_suspicious_behavior\": 1}, \"random_watermarks\": {\"count\": 5, \"texts\": [\"版权保护\", \"禁止录制\", \"学习专用\", \"仅供学习\", \"请勿传播\"], \"enabled\": 1, \"opacity_range\": [0.1, 0.4], \"update_interval\": 30}}','2025-07-18 17:42:32','2025-07-18 17:43:02');
/*!40000 ALTER TABLE `watermark_configs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wechat_users`
--

DROP TABLE IF EXISTS `wechat_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wechat_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '关联users表ID',
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `unionid` varchar(100) DEFAULT NULL COMMENT '微信unionid',
  `session_key` varchar(100) DEFAULT NULL COMMENT '微信session_key',
  `access_token` varchar(200) DEFAULT NULL COMMENT '微信access_token',
  `refresh_token` varchar(200) DEFAULT NULL COMMENT '微信refresh_token',
  `expires_in` int(11) DEFAULT NULL COMMENT 'token过期时间',
  `scope` varchar(100) DEFAULT NULL COMMENT '授权作用域',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_openid` (`openid`),
  UNIQUE KEY `unique_user_id` (`user_id`),
  KEY `idx_unionid` (`unionid`),
  CONSTRAINT `wechat_users_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='微信用户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wechat_users`
--

LOCK TABLES `wechat_users` WRITE;
/*!40000 ALTER TABLE `wechat_users` DISABLE KEYS */;
INSERT INTO `wechat_users` VALUES (8,13,'oaSIB7e0u1rDV3LdF3ROpIaN60n8','oWAzP64r8raFa5ReqtFFU8aIotyQ','ALRqSkBMc8WjspcnhtGSqg==',NULL,NULL,NULL,NULL,'2025-07-18 08:02:15','2025-07-19 08:17:14');
/*!40000 ALTER TABLE `wechat_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'qq'
--

--
-- Dumping routines for database 'qq'
--
/*!50003 DROP PROCEDURE IF EXISTS `CleanExpiredEmailTokens` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE PROCEDURE `CleanExpiredEmailTokens`()
BEGIN
    DELETE FROM `email_verification_tokens`
    WHERE `expires_at` < NOW()
       OR (`is_used` = 1 AND `used_at` < DATE_SUB(NOW(), INTERVAL 7 DAY));
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CleanExpiredSmsCodes` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE PROCEDURE `CleanExpiredSmsCodes`()
BEGIN
    DELETE FROM `sms_codes`
    WHERE `expires_at` < NOW()
       OR (`is_used` = 1 AND `created_at` < DATE_SUB(NOW(), INTERVAL 1 DAY));
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CleanExpiredTokens` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE PROCEDURE `CleanExpiredTokens`()
BEGIN
    DELETE FROM `user_tokens`
    WHERE `expires_at` < NOW()
       OR `is_revoked` = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;

--
-- Final view structure for view `user_phone_status`
--

/*!50001 DROP VIEW IF EXISTS `user_phone_status`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50001 VIEW `user_phone_status` AS select `u`.`id` AS `id`,`u`.`name` AS `name`,`u`.`email` AS `email`,`u`.`phone` AS `phone`,`u`.`phone_verified` AS `phone_verified`,`u`.`phone_bind_required` AS `phone_bind_required`,`u`.`phone_bind_time` AS `phone_bind_time`,`u`.`login_type` AS `login_type`,`u`.`status` AS `status`,(case when (`u`.`phone_verified` = 1) then 'verified' when ((`u`.`phone_bind_required` = 1) and (isnull(`u`.`phone`) or (`u`.`phone` = ''))) then 'required' when ((`u`.`phone` is not null) and (`u`.`phone` <> '') and (`u`.`phone_verified` = 0)) then 'pending' else 'optional' end) AS `bind_status` from `users` `u` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-19 16:44:42
