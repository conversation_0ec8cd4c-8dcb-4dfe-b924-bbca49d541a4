<?php
/**
 * 支付系统基础功能测试脚本
 * 用于验证支付系统的基本功能是否正常
 */

require_once '../includes/db.php';
require_once '../includes/init_payment_tables.php';

echo "=== 微信小程序课程支付系统测试 ===\n\n";

// 测试数据库连接
echo "1. 测试数据库连接...\n";
if ($conn->ping()) {
    echo "✅ 数据库连接正常\n";
} else {
    echo "❌ 数据库连接失败\n";
    exit(1);
}

// 测试支付表初始化
echo "\n2. 测试支付表初始化...\n";
if (init_payment_tables()) {
    echo "✅ 支付表初始化成功\n";
} else {
    echo "❌ 支付表初始化失败\n";
    exit(1);
}

// 检查必需的表是否存在
echo "\n3. 检查数据表结构...\n";
$required_tables = ['orders', 'order_items', 'payments', 'refunds', 'settings'];
foreach ($required_tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "✅ 表 $table 存在\n";
    } else {
        echo "❌ 表 $table 不存在\n";
    }
}

// 检查支付配置
echo "\n4. 检查支付配置...\n";
$payment_settings = [
    'wechat_pay_enabled',
    'wechat_pay_app_id', 
    'wechat_pay_mch_id',
    'wechat_pay_api_key',
    'order_expire_minutes'
];

foreach ($payment_settings as $setting) {
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->bind_param("s", $setting);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $value = $row['setting_value'];
        if ($setting === 'wechat_pay_api_key' && !empty($value)) {
            $value = str_repeat('*', strlen($value) - 4) . substr($value, -4);
        }
        echo "✅ $setting: " . ($value ?: '(未设置)') . "\n";
    } else {
        echo "❌ $setting: 配置项不存在\n";
    }
}

// 测试API文件是否存在
echo "\n5. 检查API文件...\n";
$api_files = [
    '../api/order-create.php',
    '../api/order-list.php', 
    '../api/order-detail.php',
    '../api/order-cancel.php',
    '../api/payment-wechat-prepay.php',
    '../api/payment-wechat-notify.php'
];

foreach ($api_files as $file) {
    if (file_exists($file)) {
        echo "✅ " . basename($file) . " 存在\n";
    } else {
        echo "❌ " . basename($file) . " 不存在\n";
    }
}

// 测试后台管理文件
echo "\n6. 检查后台管理文件...\n";
$admin_files = [
    '../admin/orders.php',
    '../admin/order-detail.php',
    '../admin/settings.php'
];

foreach ($admin_files as $file) {
    if (file_exists($file)) {
        echo "✅ " . basename($file) . " 存在\n";
    } else {
        echo "❌ " . basename($file) . " 不存在\n";
    }
}

// 测试微信小程序文件
echo "\n7. 检查微信小程序文件...\n";
$wx_files = [
    '../wx/pages/orders/list.vue',
    '../wx/pages/orders/detail.vue',
    '../wx/api/order.js'
];

foreach ($wx_files as $file) {
    if (file_exists($file)) {
        echo "✅ " . basename($file) . " 存在\n";
    } else {
        echo "❌ " . basename($file) . " 不存在\n";
    }
}

// 检查日志目录
echo "\n8. 检查日志目录...\n";
$log_dir = '../logs';
if (!is_dir($log_dir)) {
    if (mkdir($log_dir, 0755, true)) {
        echo "✅ 日志目录创建成功\n";
    } else {
        echo "❌ 日志目录创建失败\n";
    }
} else {
    echo "✅ 日志目录已存在\n";
}

// 检查目录权限
if (is_writable($log_dir)) {
    echo "✅ 日志目录可写\n";
} else {
    echo "❌ 日志目录不可写\n";
}

echo "\n=== 测试完成 ===\n";
echo "如果所有项目都显示 ✅，说明支付系统基础功能正常。\n";
echo "如果有 ❌ 项目，请根据提示进行修复。\n\n";

echo "下一步操作建议：\n";
echo "1. 在后台管理系统配置微信支付参数\n";
echo "2. 测试创建订单功能\n";
echo "3. 测试微信支付功能（需要真实的商户号和密钥）\n";
echo "4. 测试支付回调处理\n";
echo "5. 测试退款功能\n";
?>
