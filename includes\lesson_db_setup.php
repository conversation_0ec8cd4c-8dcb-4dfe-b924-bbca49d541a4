<?php
/**
 * 课时管理数据库表结构自动创建脚本
 * 确保课时相关的数据库表存在且结构正确
 */

require_once 'db.php';

function setupLessonTables($conn) {
    $success = true;
    $messages = [];
    
    try {
        // 检查并更新lessons表结构
        $check_lessons = $conn->query("SHOW TABLES LIKE 'lessons'");
        if ($check_lessons->num_rows == 0) {
            // 创建lessons表
            $create_lessons = "
                CREATE TABLE `lessons` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `course_id` int(11) NOT NULL COMMENT '课程ID',
                    `title` varchar(255) NOT NULL COMMENT '课时标题',
                    `description` text COMMENT '课时描述',
                    `video_url` varchar(500) DEFAULT NULL COMMENT '视频链接',
                    `vod_file_id` varchar(100) DEFAULT NULL COMMENT '腾讯云点播文件ID',
                    `vod_video_url` varchar(500) DEFAULT NULL COMMENT '腾讯云点播视频URL',
                    `video_type` enum('url','vod') DEFAULT 'url' COMMENT '视频类型：url链接 vod腾讯云点播',
                    `duration` int(11) DEFAULT NULL COMMENT '时长（秒）',
                    `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
                    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用 1启用',
                    `is_free` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否免费：0否 1是',
                    `thumbnail` varchar(500) DEFAULT NULL COMMENT '课时缩略图',
                    `created_by` int(11) NOT NULL COMMENT '创建者ID',
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    KEY `idx_course_id` (`course_id`),
                    KEY `idx_status` (`status`),
                    KEY `idx_sort_order` (`sort_order`),
                    KEY `idx_created_by` (`created_by`),
                    KEY `idx_is_free` (`is_free`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课时表'
            ";
            
            if ($conn->query($create_lessons)) {
                $messages[] = "lessons表创建成功";
            } else {
                $success = false;
                $messages[] = "lessons表创建失败: " . $conn->error;
            }
        } else {
            // 检查并添加缺失的字段
            $columns_to_check = [
                'is_free' => "ALTER TABLE `lessons` ADD COLUMN `is_free` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否免费：0否 1是' AFTER `status`",
                'thumbnail' => "ALTER TABLE `lessons` ADD COLUMN `thumbnail` varchar(500) DEFAULT NULL COMMENT '课时缩略图' AFTER `is_free`",
                'created_by' => "ALTER TABLE `lessons` ADD COLUMN `created_by` int(11) NOT NULL COMMENT '创建者ID' AFTER `thumbnail`",
                'vod_file_id' => "ALTER TABLE `lessons` ADD COLUMN `vod_file_id` varchar(100) DEFAULT NULL COMMENT '腾讯云点播文件ID' AFTER `video_url`",
                'vod_video_url' => "ALTER TABLE `lessons` ADD COLUMN `vod_video_url` varchar(500) DEFAULT NULL COMMENT '腾讯云点播视频URL' AFTER `vod_file_id`",
                'video_type' => "ALTER TABLE `lessons` ADD COLUMN `video_type` enum('url','vod') DEFAULT 'url' COMMENT '视频类型：url链接 vod腾讯云点播' AFTER `vod_video_url`"
            ];
            
            foreach ($columns_to_check as $column => $sql) {
                $check_column = $conn->query("SHOW COLUMNS FROM `lessons` LIKE '$column'");
                if ($check_column->num_rows == 0) {
                    if ($conn->query($sql)) {
                        $messages[] = "lessons表字段 $column 添加成功";
                    } else {
                        $success = false;
                        $messages[] = "lessons表字段 $column 添加失败: " . $conn->error;
                    }
                }
            }
        }
        
        // 检查并创建lesson_watch_logs表
        $check_watch_logs = $conn->query("SHOW TABLES LIKE 'lesson_watch_logs'");
        if ($check_watch_logs->num_rows == 0) {
            $create_watch_logs = "
                CREATE TABLE `lesson_watch_logs` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `user_id` int(11) NOT NULL COMMENT '用户ID',
                    `lesson_id` int(11) NOT NULL COMMENT '课时ID',
                    `course_id` int(11) NOT NULL COMMENT '课程ID',
                    `watch_time` int(11) NOT NULL DEFAULT '0' COMMENT '观看时长（秒）',
                    `progress_position` int(11) NOT NULL DEFAULT '0' COMMENT '观看进度位置（秒）',
                    `is_completed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否完成：0否 1是',
                    `completion_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '完成率（0-100）',
                    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
                    `user_agent` text COMMENT '用户代理',
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '观看时间',
                    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    PRIMARY KEY (`id`),
                    KEY `idx_user_id` (`user_id`),
                    KEY `idx_lesson_id` (`lesson_id`),
                    KEY `idx_course_id` (`course_id`),
                    KEY `idx_is_completed` (`is_completed`),
                    KEY `idx_created_at` (`created_at`),
                    UNIQUE KEY `unique_user_lesson` (`user_id`, `lesson_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课时观看记录表'
            ";
            
            if ($conn->query($create_watch_logs)) {
                $messages[] = "lesson_watch_logs表创建成功";
            } else {
                $success = false;
                $messages[] = "lesson_watch_logs表创建失败: " . $conn->error;
            }
        }
        
        // 检查并添加课程表的课时数量字段
        $check_lesson_count = $conn->query("SHOW COLUMNS FROM `courses` LIKE 'lesson_count'");
        if ($check_lesson_count->num_rows == 0) {
            $add_lesson_count = "ALTER TABLE `courses` ADD COLUMN `lesson_count` int(11) NOT NULL DEFAULT '0' COMMENT '课时数量' AFTER `is_on_sale`";
            if ($conn->query($add_lesson_count)) {
                $messages[] = "courses表lesson_count字段添加成功";
            } else {
                $success = false;
                $messages[] = "courses表lesson_count字段添加失败: " . $conn->error;
            }
        }
        
        // 检查并添加课程表的总时长字段
        $check_total_duration = $conn->query("SHOW COLUMNS FROM `courses` LIKE 'total_duration'");
        if ($check_total_duration->num_rows == 0) {
            $add_total_duration = "ALTER TABLE `courses` ADD COLUMN `total_duration` int(11) NOT NULL DEFAULT '0' COMMENT '总时长（秒）' AFTER `lesson_count`";
            if ($conn->query($add_total_duration)) {
                $messages[] = "courses表total_duration字段添加成功";
            } else {
                $success = false;
                $messages[] = "courses表total_duration字段添加失败: " . $conn->error;
            }
        }
        
    } catch (Exception $e) {
        $success = false;
        $messages[] = "数据库操作异常: " . $e->getMessage();
    }
    
    return [
        'success' => $success,
        'messages' => $messages
    ];
}

// 如果直接访问此文件，执行安装
if (basename($_SERVER['PHP_SELF']) == 'lesson_db_setup.php') {
    $result = setupLessonTables($conn);
    
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => $result['success'],
        'message' => implode("\n", $result['messages'])
    ], JSON_UNESCAPED_UNICODE);
}
?>
