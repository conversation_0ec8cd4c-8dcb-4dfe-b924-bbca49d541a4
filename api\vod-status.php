<?php
/**
 * 腾讯云点播视频状态查询API
 * 用于查询视频的转码状态和获取播放信息
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/vod_config.php';
require_once '../includes/db.php';

try {
    // 获取参数
    $file_id = isset($_GET['file_id']) ? trim($_GET['file_id']) : '';
    $lesson_id = isset($_GET['lesson_id']) ? intval($_GET['lesson_id']) : 0;
    
    // 验证参数
    if (empty($file_id) && $lesson_id <= 0) {
        echo json_encode([
            'success' => false,
            'message' => '请提供fileId或lessonId参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 如果提供了lesson_id，从数据库获取file_id
    if ($lesson_id > 0 && empty($file_id)) {
        $lesson_query = "SELECT vod_file_id FROM lessons WHERE id = ?";
        $lesson_stmt = $conn->prepare($lesson_query);
        $lesson_stmt->bind_param("i", $lesson_id);
        $lesson_stmt->execute();
        $lesson_result = $lesson_stmt->get_result();
        $lesson = $lesson_result->fetch_assoc();
        
        if (!$lesson || empty($lesson['vod_file_id'])) {
            echo json_encode([
                'success' => false,
                'message' => '课时不存在或未使用腾讯云点播'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        $file_id = $lesson['vod_file_id'];
    }
    
    // 验证配置
    $configValidation = VodConfig::validateConfig();
    if (!$configValidation['valid']) {
        echo json_encode([
            'success' => false,
            'message' => '腾讯云点播配置错误: ' . implode(', ', $configValidation['errors'])
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $config = VodConfig::getConfig();
    
    // 构建请求参数
    $params = [
        'Action' => 'DescribeMediaInfos',
        'Version' => '2018-07-17',
        'Region' => $config['upload_region'],
        'FileIds.0' => $file_id,
        'Timestamp' => time(),
        'Nonce' => rand(10000, 99999),
        'SecretId' => $config['secret_id']
    ];
    
    // 如果有子应用ID，添加到参数中
    if ($config['sub_app_id'] > 0) {
        $params['SubAppId'] = $config['sub_app_id'];
    }
    
    // 按键名排序
    ksort($params);
    
    // 构建请求字符串
    $request_string = '';
    foreach ($params as $key => $value) {
        $request_string .= $key . '=' . $value . '&';
    }
    $request_string = rtrim($request_string, '&');
    
    // 构建签名原文字符串
    $sign_str = "GET" . "vod.tencentcloudapi.com" . "/?" . $request_string;
    
    // 生成签名
    $signature = base64_encode(hash_hmac('sha1', $sign_str, $config['secret_key'], true));
    
    // 添加签名到请求参数
    $params['Signature'] = $signature;
    
    // 构建请求URL
    $request_url = 'https://vod.tencentcloudapi.com/?' . http_build_query($params);
    
    // 发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $request_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code != 200) {
        echo json_encode([
            'success' => false,
            'message' => '请求腾讯云API失败，HTTP状态码: ' . $http_code
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (isset($result['Response']['Error'])) {
        echo json_encode([
            'success' => false,
            'message' => '腾讯云API错误: ' . $result['Response']['Error']['Message'],
            'error_code' => $result['Response']['Error']['Code']
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理视频信息
    $media_info = $result['Response']['MediaInfoSet'][0] ?? null;
    
    if (!$media_info) {
        echo json_encode([
            'success' => false,
            'message' => '未找到视频信息'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 提取基本信息
    $basic_info = $media_info['BasicInfo'] ?? [];
    $transcoding_info = $media_info['TranscodeInfo'] ?? [];
    $adaptive_streaming_info = $media_info['AdaptiveDynamicStreamingInfo'] ?? [];
    
    // 格式化返回数据
    $video_data = [
        'file_id' => $basic_info['FileId'] ?? '',
        'name' => $basic_info['Name'] ?? '',
        'duration' => $basic_info['Duration'] ?? 0,
        'duration_formatted' => formatDuration($basic_info['Duration'] ?? 0),
        'size' => $basic_info['Size'] ?? 0,
        'size_formatted' => formatSize($basic_info['Size'] ?? 0),
        'status' => $basic_info['Status'] ?? '',
        'status_text' => getStatusText($basic_info['Status'] ?? ''),
        'create_time' => $basic_info['CreateTime'] ?? '',
        'update_time' => $basic_info['UpdateTime'] ?? '',
        'thumbnail_url' => $basic_info['CoverUrl'] ?? '',
        'transcoding' => [
            'status' => $transcoding_info['Status'] ?? '',
            'status_text' => getTranscodingStatusText($transcoding_info['Status'] ?? ''),
            'completed' => ($transcoding_info['Status'] ?? '') === 'SUCCESS',
            'streams' => []
        ],
        'playback_urls' => []
    ];
    
    // 添加转码流信息
    if (isset($transcoding_info['TranscodeSet']) && is_array($transcoding_info['TranscodeSet'])) {
        foreach ($transcoding_info['TranscodeSet'] as $transcode) {
            $video_data['transcoding']['streams'][] = [
                'url' => $transcode['Url'] ?? '',
                'definition' => $transcode['Definition'] ?? 0,
                'bitrate' => $transcode['Bitrate'] ?? 0,
                'height' => $transcode['Height'] ?? 0,
                'width' => $transcode['Width'] ?? 0,
                'size' => $transcode['Size'] ?? 0,
                'size_formatted' => formatSize($transcode['Size'] ?? 0),
                'duration' => $transcode['Duration'] ?? 0,
                'format' => $transcode['Container'] ?? ''
            ];
        }
    }
    
    // 添加自适应流信息
    if (isset($adaptive_streaming_info['AdaptiveDynamicStreamingSet']) && is_array($adaptive_streaming_info['AdaptiveDynamicStreamingSet'])) {
        foreach ($adaptive_streaming_info['AdaptiveDynamicStreamingSet'] as $stream) {
            $video_data['playback_urls'][] = [
                'url' => $stream['Url'] ?? '',
                'definition' => $stream['Definition'] ?? 0,
                'format' => $stream['Package'] ?? '',
                'drm_type' => $stream['DrmType'] ?? ''
            ];
        }
    }
    
    // 返回结果
    echo json_encode([
        'success' => true,
        'data' => $video_data,
        'message' => '获取视频信息成功'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '服务器错误',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 格式化时长
 */
function formatDuration($seconds) {
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $secs = $seconds % 60;
    
    if ($hours > 0) {
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $secs);
    } else {
        return sprintf('%02d:%02d', $minutes, $secs);
    }
}

/**
 * 格式化文件大小
 */
function formatSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = 0;
    while ($bytes >= 1024 && $i < count($units) - 1) {
        $bytes /= 1024;
        $i++;
    }
    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * 获取视频状态文本
 */
function getStatusText($status) {
    $status_map = [
        'NORMAL' => '正常',
        'PROCESSING' => '处理中',
        'FAILED' => '处理失败',
        'DELETED' => '已删除'
    ];
    
    return $status_map[$status] ?? $status;
}

/**
 * 获取转码状态文本
 */
function getTranscodingStatusText($status) {
    $status_map = [
        'PROCESSING' => '转码中',
        'SUCCESS' => '转码成功',
        'FAILED' => '转码失败'
    ];
    
    return $status_map[$status] ?? $status;
}
?>
