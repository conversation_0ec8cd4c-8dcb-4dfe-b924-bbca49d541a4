<?php
/**
 * 增强版课程详情API
 * 包含课程介绍、讲师信息、课程大纲等详细信息
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    $course_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    $user_id = 0;
    
    // 尝试获取用户信息（可选）
    try {
        $user = $auth->requireAuth();
        $user_id = $user['id'];
    } catch (Exception $e) {
        // 未登录用户也可以查看课程基本信息
    }

    if ($course_id <= 0) {
        $auth->jsonResponse(400, '缺少课程ID');
    }

    $conn = $auth->getConn();

    // 获取课程基本信息
    $stmt = $conn->prepare("
        SELECT c.*, 
               a.username as creator_name,
               COALESCE((SELECT COUNT(*) FROM lessons WHERE course_id = c.id AND status = 1), 0) as lesson_count,
               COALESCE((SELECT SUM(duration) FROM lessons WHERE course_id = c.id AND status = 1), 0) as total_duration
        FROM courses c
        LEFT JOIN admins a ON c.created_by = a.id
        WHERE c.id = ? AND c.status = 'active'
    ");
    $stmt->bind_param("i", $course_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $auth->jsonResponse(404, '课程不存在或已禁用');
    }

    $course = $result->fetch_assoc();

    // 格式化时长
    if ($course['duration']) {
        $course['duration_formatted'] = gmdate("H:i:s", $course['duration']);
    }
    if ($course['total_duration']) {
        $course['total_duration_formatted'] = gmdate("H:i:s", $course['total_duration']);
    }

    // 获取课程大纲（课时列表）
    $lessons_stmt = $conn->prepare("
        SELECT id, title, description, duration, sort_order, is_free, thumbnail
        FROM lessons 
        WHERE course_id = ? AND status = 1 
        ORDER BY sort_order ASC, id ASC
    ");
    $lessons_stmt->bind_param("i", $course_id);
    $lessons_stmt->execute();
    $lessons_result = $lessons_stmt->get_result();
    
    $lessons = [];
    while ($lesson = $lessons_result->fetch_assoc()) {
        $lesson['duration_formatted'] = $lesson['duration'] ? gmdate("H:i:s", $lesson['duration']) : null;
        $lessons[] = $lesson;
    }

    // 获取用户学习信息（如果已登录）
    $user_info = null;
    if ($user_id > 0) {
        $user_course_stmt = $conn->prepare("
            SELECT assigned_at, expires_at, last_watched_at, watch_progress, watch_count, status
            FROM user_courses
            WHERE user_id = ? AND course_id = ?
        ");
        $user_course_stmt->bind_param("ii", $user_id, $course_id);
        $user_course_stmt->execute();
        $user_course_result = $user_course_stmt->get_result();

        if ($user_course_result->num_rows > 0) {
            $user_course = $user_course_result->fetch_assoc();
            $is_expired = $user_course['expires_at'] && strtotime($user_course['expires_at']) < time();
            
            $user_info = [
                'assigned_at' => $user_course['assigned_at'],
                'expires_at' => $user_course['expires_at'],
                'last_watched_at' => $user_course['last_watched_at'],
                'watch_progress' => floatval($user_course['watch_progress']),
                'watch_count' => intval($user_course['watch_count']),
                'status' => $user_course['status'],
                'is_expired' => $is_expired,
                'has_access' => !$is_expired && $user_course['status'] === 'active'
            ];
        }
    }

    // 构建课程介绍结构
    $course_introduction = [
        'overview' => $course['description'],
        'teacher_info' => [
            'name' => $course['teacher_name'] ?: '暂无',
            'title' => '讲师', // 可以后续扩展
            'description' => '经验丰富的专业讲师' // 可以后续扩展
        ],
        'course_outline' => $lessons,
        'course_features' => [
            '课时数量：' . count($lessons) . '课时',
            '总时长：' . ($course['total_duration_formatted'] ?: '待更新'),
            '难度等级：' . ($course['difficulty'] === 'beginner' ? '初级' : ($course['difficulty'] === 'intermediate' ? '中级' : '高级')),
            '学习方式：在线视频学习',
            '课程类型：' . ($course['is_free'] ? '免费课程' : '付费课程')
        ],
        'learning_objectives' => [
            '掌握' . $course['title'] . '的核心知识点',
            '通过实践项目提升技能水平',
            '获得系统性的学习体验',
            '建立完整的知识体系'
        ]
    ];

    // 移除敏感信息
    if (!$user_info || !$user_info['has_access']) {
        unset($course['video_url']);
    }

    $response_data = [
        'course_info' => $course,
        'course_introduction' => $course_introduction,
        'user_info' => $user_info,
        'has_access' => $user_info ? $user_info['has_access'] : ($course['is_free'] && $user_id > 0)
    ];

    $auth->jsonResponse(200, '获取成功', $response_data);

} catch (Exception $e) {
    error_log('增强版课程详情API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}
?>