# 系统配置指南

本文档详细介绍如何配置腾讯云VOD课时管理系统。

## 📋 前置要求

### 1. 服务器环境

确保您的服务器满足以下要求：

- **PHP版本**: 7.0 或更高版本
- **MySQL版本**: 5.7 或更高版本
- **Web服务器**: Apache 2.4+ 或 Nginx 1.10+
- **PHP扩展**:
  - cURL
  - JSON
  - OpenSSL
  - MySQLi 或 PDO
  - GD 或 ImageMagick（用于图片处理）

### 2. 腾讯云服务

- 已注册腾讯云账号
- 已开通腾讯云点播服务（VOD）
- 获取API密钥（SecretId 和 SecretKey）

## 🔧 详细配置步骤

### 步骤1：腾讯云点播服务配置

#### 1.1 开通点播服务

1. 登录 [腾讯云控制台](https://console.cloud.tencent.com/)
2. 搜索并进入"云点播"服务
3. 点击"立即开通"
4. 选择合适的计费方式（按量计费或资源包）

#### 1.2 获取API密钥

1. 进入 [访问管理控制台](https://console.cloud.tencent.com/cam/capi)
2. 点击"新建密钥"
3. 记录生成的 SecretId 和 SecretKey
4. **重要**: 妥善保管密钥，不要泄露给他人

#### 1.3 配置域名和防盗链（可选）

1. 在点播控制台中配置播放域名
2. 设置防盗链规则（Referer、Key防盗链等）
3. 配置HTTPS证书（推荐）

### 步骤2：系统文件配置

#### 2.1 配置腾讯云VOD参数

编辑 `includes/vod_config.php` 文件：

```php
<?php
class VodConfig {
    // 腾讯云API密钥配置
    const SECRET_ID = 'AKIDxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx';     // 替换为您的SecretId
    const SECRET_KEY = 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx';        // 替换为您的SecretKey
    
    // 点播应用配置
    const SUB_APP_ID = 0;                    // 子应用ID，0表示主应用
    
    // 上传配置
    const UPLOAD_REGION = 'ap-beijing';      // 上传地域
    const STORAGE_REGION = 'ap-beijing';     // 存储地域
    
    // 视频处理配置
    const PROCEDURE = '';                    // 任务流模板名称，留空表示不进行任务流处理
    
    // 分类配置
    const CLASS_ID = 0;                      // 分类ID，0表示其他分类
    
    // 过期时间配置（秒）
    const SIGNATURE_EXPIRE_TIME = 3600;      // 签名过期时间，1小时
    
    // 文件大小限制（字节）
    const MAX_VIDEO_SIZE = 5 * 1024 * 1024 * 1024;  // 5GB
    const MAX_IMAGE_SIZE = 10 * 1024 * 1024;         // 10MB
}
?>
```

#### 2.2 地域选择说明

选择合适的地域可以提高上传速度和用户体验：

| 地域 | 代码 | 说明 |
|------|------|------|
| 北京 | ap-beijing | 华北地区 |
| 上海 | ap-shanghai | 华东地区 |
| 广州 | ap-guangzhou | 华南地区 |
| 成都 | ap-chengdu | 西南地区 |
| 重庆 | ap-chongqing | 西南地区 |
| 香港 | ap-hongkong | 港澳台地区 |
| 新加坡 | ap-singapore | 东南亚地区 |

### 步骤3：数据库配置

#### 3.1 自动数据库更新

系统会自动检查并更新数据库表结构。首次访问管理后台时，系统会：

1. 检查 `lessons` 表是否存在必要字段
2. 自动添加VOD相关字段：
   - `vod_file_id`: 腾讯云点播文件ID
   - `vod_video_url`: 腾讯云点播视频URL
   - `video_type`: 视频类型（url/vod）

#### 3.2 手动数据库更新（如需要）

如果自动更新失败，可以手动执行以下SQL：

```sql
-- 添加VOD相关字段
ALTER TABLE `lessons` ADD COLUMN `vod_file_id` varchar(100) DEFAULT NULL COMMENT '腾讯云点播文件ID' AFTER `video_url`;
ALTER TABLE `lessons` ADD COLUMN `vod_video_url` varchar(500) DEFAULT NULL COMMENT '腾讯云点播视频URL' AFTER `vod_file_id`;
ALTER TABLE `lessons` ADD COLUMN `video_type` enum('url','vod') DEFAULT 'url' COMMENT '视频类型：url链接 vod腾讯云点播' AFTER `vod_video_url`;

-- 创建索引（可选，提高查询性能）
CREATE INDEX idx_vod_file_id ON lessons(vod_file_id);
CREATE INDEX idx_video_type ON lessons(video_type);
```

### 步骤4：文件权限配置

#### 4.1 设置目录权限

确保以下目录具有写入权限（755或777）：

```bash
# 课时缩略图上传目录
chmod 755 uploads/lesson_thumbnails/

# 管理后台上传目录
chmod 755 admin/uploads/

# 临时文件目录
chmod 755 tmp/
```

#### 4.2 创建必要目录

如果目录不存在，请手动创建：

```bash
mkdir -p uploads/lesson_thumbnails
mkdir -p admin/uploads
mkdir -p tmp
mkdir -p logs
```

### 步骤5：Web服务器配置

#### 5.1 Apache配置

在 `.htaccess` 文件中添加：

```apache
# 设置上传文件大小限制
php_value upload_max_filesize 5G
php_value post_max_size 5G
php_value max_execution_time 3600
php_value max_input_time 3600
php_value memory_limit 512M

# 启用URL重写
RewriteEngine On

# 防止直接访问配置文件
<Files "vod_config.php">
    Order Allow,Deny
    Deny from all
</Files>
```

#### 5.2 Nginx配置

在 Nginx 配置文件中添加：

```nginx
server {
    # 设置上传文件大小限制
    client_max_body_size 5G;
    
    # 设置超时时间
    client_body_timeout 3600s;
    client_header_timeout 3600s;
    
    # PHP配置
    location ~ \.php$ {
        fastcgi_read_timeout 3600;
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # 防止直接访问配置文件
    location ~ /includes/.*\.php$ {
        deny all;
    }
}
```

### 步骤6：小程序配置

#### 6.1 域名白名单配置

在微信小程序管理后台配置以下域名：

**request合法域名**:
```
https://your-domain.com
```

**uploadFile合法域名**:
```
https://vod2.qcloud.com
https://your-domain.com
```

**downloadFile合法域名**:
```
https://your-domain.com
https://your-vod-domain.com
```

#### 6.2 小程序配置文件

编辑 `wx/utils/config.js`：

```javascript
const CONFIG = {
    // API基础URL
    BASE_URL: 'https://your-domain.com/api/',
    
    // 腾讯云VOD配置
    VOD_CONFIG: {
        // 如果需要在小程序中直接上传，配置相关参数
        REGION: 'ap-beijing'
    },
    
    // 其他配置...
};

export default CONFIG;
```

## ✅ 配置验证

### 1. 环境检查

访问 `/api/vod-config.php?action=check` 检查配置状态：

```bash
curl "https://your-domain.com/api/vod-config.php?action=check"
```

### 2. 签名测试

访问 `/api/vod-config.php?action=test` 测试签名生成：

```bash
curl "https://your-domain.com/api/vod-config.php?action=test"
```

### 3. 上传测试

1. 登录管理后台
2. 进入课时管理页面
3. 尝试添加课时并上传视频
4. 检查上传进度和结果

## 🚨 常见配置问题

### 问题1：签名生成失败

**错误信息**: "SECRET_ID未配置" 或 "SECRET_KEY未配置"

**解决方案**:
1. 检查 `vod_config.php` 中的密钥配置
2. 确认密钥格式正确（不包含空格或特殊字符）
3. 验证腾讯云账号权限

### 问题2：文件上传失败

**错误信息**: "文件大小超出限制" 或 "上传超时"

**解决方案**:
1. 检查PHP配置中的文件大小限制
2. 调整Web服务器的超时设置
3. 检查磁盘空间是否充足

### 问题3：视频播放失败

**错误信息**: 小程序中视频无法播放

**解决方案**:
1. 检查小程序域名白名单配置
2. 确认视频URL格式正确
3. 检查腾讯云点播防盗链设置

### 问题4：数据库连接失败

**错误信息**: "数据库连接失败"

**解决方案**:
1. 检查数据库配置信息
2. 确认数据库服务正常运行
3. 验证数据库用户权限

## 📞 获取帮助

如果在配置过程中遇到问题：

1. 查看系统日志文件
2. 检查浏览器控制台错误信息
3. 参考故障排除文档
4. 联系技术支持团队

## 🔄 配置更新

当需要更新配置时：

1. 备份当前配置文件
2. 修改相应配置参数
3. 重启Web服务器（如需要）
4. 验证配置是否生效

---

**注意**: 配置完成后，建议进行全面测试以确保所有功能正常工作。
