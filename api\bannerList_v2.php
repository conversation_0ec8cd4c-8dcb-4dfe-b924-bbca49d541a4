<?php
/**
 * 轮播图列表API - 简化版本
 * 不依赖认证系统，直接返回轮播图数据
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '方法不允许',
        'data' => null
    ]);
    exit;
}

try {
    require_once '../includes/db.php';
    
    if (!$conn || $conn->connect_error) {
        throw new Exception('数据库连接失败');
    }
    
    // 检查banners表是否存在
    $table_check = $conn->query("SHOW TABLES LIKE 'banners'");
    if (!$table_check || $table_check->num_rows == 0) {
        echo json_encode([
            'code' => 200,
            'message' => '获取成功（表不存在）',
            'data' => [
                'list' => [],
                'total' => 0
            ]
        ]);
        exit;
    }
    
    // 参数处理
    $status = isset($_GET['status']) ? $_GET['status'] : 'active';
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
    
    if ($limit < 1 || $limit > 50) $limit = 10;
    if (!in_array($status, ['active', 'inactive'])) $status = 'active';
    
    // 构建查询
    $where_conditions = ["status = ?"];
    $params = [$status];
    $param_types = "s";
    
    // 只显示有效期内的轮播图
    $where_conditions[] = "(start_time IS NULL OR start_time <= NOW())";
    $where_conditions[] = "(end_time IS NULL OR end_time > NOW())";
    
    $where_clause = "WHERE " . implode(' AND ', $where_conditions);
    
    // 获取总数
    $count_sql = "SELECT COUNT(*) as total FROM banners $where_clause";
    $count_stmt = $conn->prepare($count_sql);
    
    if (!$count_stmt) {
        throw new Exception('COUNT查询准备失败: ' . $conn->error);
    }
    
    $count_stmt->bind_param($param_types, ...$params);
    
    if (!$count_stmt->execute()) {
        throw new Exception('COUNT查询执行失败: ' . $count_stmt->error);
    }
    
    $total = $count_stmt->get_result()->fetch_assoc()['total'];
    $count_stmt->close();
    
    // 获取数据
    $sql = "SELECT 
                id, title, image_url, link_type, link_value,
                start_time, end_time, status, sort_order, view_count,
                created_at, updated_at
            FROM banners 
            $where_clause
            ORDER BY sort_order ASC, created_at DESC
            LIMIT ?";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('数据查询准备失败: ' . $conn->error);
    }
    
    $params[] = $limit;
    $param_types .= "i";
    
    $stmt->bind_param($param_types, ...$params);
    
    if (!$stmt->execute()) {
        throw new Exception('数据查询执行失败: ' . $stmt->error);
    }
    
    $result = $stmt->get_result();
    $banners = [];
    
    // 获取当前域名和协议
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $base_url = $protocol . '://' . $host;

    while ($row = $result->fetch_assoc()) {
        // 处理图片URL，确保返回完整的HTTP URL
        $image_url = $row['image_url'];
        if (!empty($image_url) && !preg_match('/^https?:\/\//', $image_url)) {
            // 如果不是完整URL，则拼接基础URL
            $image_url = $base_url . '/' . ltrim($image_url, '/');
        }

        $banners[] = [
            'id' => (int)$row['id'],
            'title' => $row['title'],
            'image_url' => $image_url,
            'link_type' => $row['link_type'],
            'link_value' => $row['link_value'],
            'start_time' => $row['start_time'],
            'end_time' => $row['end_time'],
            'status' => $row['status'],
            'sort_order' => (int)$row['sort_order'],
            'view_count' => (int)$row['view_count'],
            'created_at' => $row['created_at'],
            'updated_at' => $row['updated_at']
        ];
    }
    
    $stmt->close();
    
    echo json_encode([
        'code' => 200,
        'message' => '获取成功',
        'data' => [
            'list' => $banners,
            'total' => (int)$total,
            'limit' => $limit,
            'status' => $status
        ],
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    error_log('轮播图API错误: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'code' => 500,
        'message' => '服务器错误: ' . $e->getMessage(),
        'data' => null,
        'timestamp' => time()
    ]);
}
?>
