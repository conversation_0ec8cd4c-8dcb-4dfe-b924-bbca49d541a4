<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0); // 不直接显示错误，通过JSON返回

require_once '../includes/db.php';
require_once '../includes/auth.php';

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 验证用户身份
$user = authenticate_user();
if (!$user) {
    http_response_code(401);
    echo json_encode([
        'code' => 401,
        'message' => '未授权访问',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取请求数据
$raw_input = file_get_contents('php://input');
$input = json_decode($raw_input, true);

// 调试信息
error_log('Order creation request - User ID: ' . $user['id'] . ', Raw input: ' . $raw_input);

// 检查JSON解析是否成功
if (json_last_error() !== JSON_ERROR_NONE) {
    echo json_encode([
        'code' => 400,
        'message' => 'JSON数据格式错误: ' . json_last_error_msg(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 验证必需参数
if (!isset($input['course_ids']) || !is_array($input['course_ids']) || empty($input['course_ids'])) {
    echo json_encode([
        'code' => 400,
        'message' => '课程ID列表不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 开始事务
    $conn->autocommit(false);
    $conn->begin_transaction();
    
    $user_id = $user['id'];
    $course_ids = array_map('intval', $input['course_ids']);
    $total_amount = 0;
    $order_items = [];
    
    // 验证课程并计算总价
    $placeholders = str_repeat('?,', count($course_ids) - 1) . '?';
    $stmt = $conn->prepare("
        SELECT id, title, price, original_price, is_free, is_on_sale, status
        FROM courses
        WHERE id IN ($placeholders) AND status = 'active' AND is_on_sale = 1
    ");
    $stmt->bind_param(str_repeat('i', count($course_ids)), ...$course_ids);
    $stmt->execute();
    $courses = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    if (count($courses) !== count($course_ids)) {
        throw new Exception('部分课程不存在或不可购买');
    }
    
    // 检查用户是否已经拥有这些课程
    $placeholders = str_repeat('?,', count($course_ids) - 1) . '?';
    $stmt = $conn->prepare("
        SELECT course_id
        FROM user_courses
        WHERE user_id = ? AND course_id IN ($placeholders) AND status = 'active'
    ");
    $bind_types = 'i' . str_repeat('i', count($course_ids));
    $bind_values = array_merge([$user_id], $course_ids);
    $stmt->bind_param($bind_types, ...$bind_values);
    $stmt->execute();
    $owned_courses = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    if (!empty($owned_courses)) {
        $owned_ids = array_column($owned_courses, 'course_id');
        throw new Exception('您已拥有课程ID: ' . implode(', ', $owned_ids));
    }
    
    // 计算订单金额
    foreach ($courses as $course) {
        if ($course['is_free'] == 1) {
            throw new Exception('免费课程无需购买');
        }
        
        $course_price = floatval($course['price']);
        $original_price = floatval($course['original_price'] ?? $course_price);
        
        $total_amount += $course_price;
        $order_items[] = [
            'course_id' => $course['id'],
            'course_title' => $course['title'],
            'course_price' => $course_price,
            'original_price' => $original_price,
            'quantity' => 1,
            'subtotal' => $course_price
        ];
    }
    
    if ($total_amount <= 0) {
        throw new Exception('订单金额必须大于0');
    }
    
    // 生成订单号
    $order_no = 'ORD' . date('YmdHis') . sprintf('%06d', $user_id) . sprintf('%04d', rand(1000, 9999));
    
    // 计算订单过期时间
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'order_expire_minutes'");
    $stmt->execute();
    $expire_minutes = $stmt->get_result()->fetch_assoc()['setting_value'] ?? 30;
    $expire_time = date('Y-m-d H:i:s', time() + ($expire_minutes * 60));
    
    // 创建订单
    $stmt = $conn->prepare("
        INSERT INTO orders (order_no, user_id, total_amount, discount_amount, actual_amount, 
                           order_status, payment_status, expire_time, created_at) 
        VALUES (?, ?, ?, 0.00, ?, 'pending', 'unpaid', ?, NOW())
    ");
    $stmt->bind_param("sidds", $order_no, $user_id, $total_amount, $total_amount, $expire_time);
    $stmt->execute();
    $order_id = $conn->insert_id;
    
    // 创建订单商品
    foreach ($order_items as $item) {
        $stmt = $conn->prepare("
            INSERT INTO order_items (order_id, course_id, course_title, course_price, 
                                   original_price, quantity, subtotal, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->bind_param("iisddid", 
            $order_id, 
            $item['course_id'], 
            $item['course_title'], 
            $item['course_price'], 
            $item['original_price'], 
            $item['quantity'], 
            $item['subtotal']
        );
        $stmt->execute();
    }
    
    $conn->commit();
    $conn->autocommit(true);
    
    // 返回订单信息
    echo json_encode([
        'code' => 200,
        'message' => '订单创建成功',
        'data' => [
            'order_id' => $order_id,
            'order_no' => $order_no,
            'total_amount' => $total_amount,
            'actual_amount' => $total_amount,
            'expire_time' => $expire_time,
            'items' => $order_items
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $conn->rollback();
    $conn->autocommit(true);

    // 记录错误日志
    error_log('Order creation error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());

    // 返回友好的错误信息
    $error_message = $e->getMessage();

    // 对于一些常见错误，提供更友好的提示
    if (strpos($error_message, 'Duplicate entry') !== false) {
        $error_message = '订单创建失败，请稍后重试';
    } elseif (strpos($error_message, 'foreign key constraint') !== false) {
        $error_message = '数据关联错误，请联系管理员';
    } elseif (strpos($error_message, 'Table') !== false && strpos($error_message, "doesn't exist") !== false) {
        $error_message = '系统配置错误，请联系管理员';
    }

    echo json_encode([
        'code' => 500,
        'message' => $error_message,
        'data' => null,
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ], JSON_UNESCAPED_UNICODE);
} catch (Error $e) {
    // 捕获PHP致命错误
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    error_log('PHP Fatal Error in order creation: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());

    echo json_encode([
        'code' => 500,
        'message' => '系统错误，请稍后重试',
        'data' => null,
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ], JSON_UNESCAPED_UNICODE);
}
?>
