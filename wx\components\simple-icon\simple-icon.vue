<template>
	<text class="simple-icon" :style="iconStyle" @click="handleClick">{{ iconText }}</text>
</template>

<script>
	export default {
		name: 'SimpleIcon',
		props: {
			type: {
				type: String,
				default: ''
			},
			size: {
				type: [String, Number],
				default: 16
			},
			color: {
				type: String,
				default: '#333'
			}
		},
		computed: {
			iconText() {
				const iconMap = {
					// 基础图标
					'star': '⭐',
					'star-filled': '⭐',
					'play': '▶️',
					'play-filled': '▶️',
					'play-circle': '📚',
					'person': '👤',
					'person-filled': '👨‍🏫',
					'eye': '👁️',
					'sound': '🔊',
					'sound-filled': '📢',
					'notification': '🔔',
					'notification-filled': '🔔',
					'locked': '🔒',
					'trash': '🗑️',
					'reload': '🔄',
					'info': 'ℹ️',
					'paperplane': '🛡️',
					'forward': '>',
					'back': '<',
					'arrow-right': '→',
					'arrow-left': '←',
					'arrow-up': '↑',
					'arrow-down': '↓',
					'home': '🏠',
					'search': '🔍',
					'user': '👤',
					'settings': '⚙️',
					'gear': '⚙️',
					'heart': '❤️',
					'heart-filled': '❤️',
					'share': '📤',
					'download': '📥',
					'upload': '📤',
					'edit': '✏️',
					'delete': '🗑️',
					'add': '➕',
					'minus': '➖',
					'close': '✖️',
					'check': '✅',
					'warning': '⚠️',
					'error': '❌',
					'success': '✅',
					'fire': '🔥',
					'paperclip': '📌',
					'pushpin': '📌',
					'calendar': '📅',
					'clock': '🕐',
					'location': '📍',
					'phone': '📞',
					'email': '📧',
					'link': '🔗',
					'image': '🖼️',
					'video': '🎥',
					'videocam': '🎥',
					'music': '🎵',
					'file': '📄',
					'folder': '📁',
					'camera': '📷',
					'mic': '🎤',
					'wifi': '📶',
					'bluetooth': '📶',
					'battery': '🔋',
					'refresh': '🔄',
					'sync': '🔄',
					'cloud': '☁️',
					'sun': '☀️',
					'moon': '🌙',
					'weather': '🌤️',
					'help': '❓',
					'weixin': '💬',
					// 订单相关图标
					'order': '📋',
					'shopping-cart': '🛒',
					'payment': '💳',
					'money': '💰',
					'checkmark': '✅',
					'time': '⏰',
					'refresh': '🔄'
				};
				return iconMap[this.type] || this.type || '•';
			},
			iconStyle() {
				const sizeValue = typeof this.size === 'number' ? this.size + 'px' : this.size;
				return {
					fontSize: sizeValue,
					color: this.color,
					lineHeight: '1',
					display: 'inline-block',
					textAlign: 'center'
				};
			}
		},
		methods: {
			handleClick() {
				this.$emit('click');
			}
		}
	};
</script>

<style scoped>
	.simple-icon {
		display: inline-block;
		text-align: center;
		vertical-align: middle;
		user-select: none;
	}
</style>
