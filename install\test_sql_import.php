<?php
/**
 * 测试SQL导入脚本
 * 用于验证修复后的SQL文件是否可以正常导入
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 数据库配置
$host = 'localhost';
$username = 'root';  // 请根据实际情况修改
$password = '';      // 请根据实际情况修改
$database = 'test_qq'; // 测试数据库名

echo "<h1>SQL导入测试</h1>";
echo "<p>测试修复后的SQL文件是否可以正常导入</p>";

try {
    // 连接MySQL
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ 数据库连接成功</p>";
    
    // 创建测试数据库
    $pdo->exec("DROP DATABASE IF EXISTS `$database`");
    $pdo->exec("CREATE DATABASE `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE `$database`");
    
    echo "<p>✅ 测试数据库创建成功</p>";
    
    // 读取SQL文件
    $sqlFile = __DIR__ . '/qq_20250719_164441.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    echo "<p>✅ SQL文件读取成功，大小: " . number_format(strlen($sql)) . " 字节</p>";
    
    // 分割SQL语句
    $statements = explode(';', $sql);
    $successCount = 0;
    $errorCount = 0;
    $errors = [];
    
    echo "<p>开始执行SQL语句...</p>";
    echo "<div style='max-height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
    
    foreach ($statements as $index => $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '/*') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $successCount++;
            if ($successCount % 50 == 0) {
                echo "<p>已执行 $successCount 条语句...</p>";
            }
        } catch (PDOException $e) {
            $errorCount++;
            $error = "语句 " . ($index + 1) . ": " . $e->getMessage();
            $errors[] = $error;
            echo "<p style='color: red;'>❌ $error</p>";
            
            // 如果是权限错误，特别标注
            if (strpos($e->getMessage(), '1227') !== false || strpos($e->getMessage(), 'SUPER privilege') !== false) {
                echo "<p style='color: red; font-weight: bold;'>⚠️ 这是权限错误，说明还有DEFINER子句未修复！</p>";
            }
        }
    }
    
    echo "</div>";
    
    echo "<h2>导入结果</h2>";
    echo "<p>✅ 成功执行: $successCount 条语句</p>";
    echo "<p>❌ 失败: $errorCount 条语句</p>";
    
    if ($errorCount == 0) {
        echo "<p style='color: green; font-weight: bold;'>🎉 所有SQL语句执行成功！权限问题已修复。</p>";
        
        // 验证存储过程是否创建成功
        $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Db = '$database'");
        $procedures = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>创建的存储过程:</h3>";
        if (count($procedures) > 0) {
            echo "<ul>";
            foreach ($procedures as $proc) {
                echo "<li>{$proc['Name']} - 创建时间: {$proc['Created']}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>未找到存储过程</p>";
        }
        
        // 验证视图是否创建成功
        $stmt = $pdo->query("SHOW FULL TABLES WHERE Table_type = 'VIEW'");
        $views = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>创建的视图:</h3>";
        if (count($views) > 0) {
            echo "<ul>";
            foreach ($views as $view) {
                echo "<li>{$view['Tables_in_' . $database]}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>未找到视图</p>";
        }
        
    } else {
        echo "<h3>错误详情:</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li style='color: red;'>$error</li>";
        }
        echo "</ul>";
    }
    
    // 清理测试数据库
    $pdo->exec("DROP DATABASE `$database`");
    echo "<p>✅ 测试数据库已清理</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><strong>说明:</strong></p>";
echo "<ul>";
echo "<li>此脚本用于测试修复后的SQL文件是否可以正常导入</li>";
echo "<li>如果出现权限错误(#1227)，说明还有DEFINER子句未修复</li>";
echo "<li>测试完成后会自动清理测试数据库</li>";
echo "<li>请根据实际情况修改数据库连接参数</li>";
echo "</ul>";
?>
