/**
 * 课程API服务
 * 处理课程列表、详情、学习进度等课程相关接口
 */

import request from '../utils/request.js';
import CONFIG from '../utils/config.js';
import { getUserInfo } from '../utils/storage.js';

/**
 * 获取课程列表
 * @param {Object} params 查询参数
 * @returns {Promise} 课程列表
 */
export function getCourseList(params = {}) {
	const userInfo = getUserInfo();
	const queryParams = {
		page: params.page || 1,
		limit: params.limit || CONFIG.PAGE_SIZE,
		search: params.search || '',
		...params
	};

	// 只有在需要用户课程信息时才传递user_id
	if (params.needUserInfo && userInfo) {
		queryParams.user_id = userInfo.id;
	}

	return request.get('courseList.php', queryParams);
}

/**
 * 获取课程详情
 * @param {Number} courseId 课程ID
 * @returns {Promise} 课程详情
 */
export function getCourseDetail(courseId) {
	const userInfo = getUserInfo();
	const params = {
		id: courseId
	};

	// 只有在用户已登录时才传递user_id
	if (userInfo) {
		params.user_id = userInfo.id;
	}

	return request.get('courseDetail.php', params);
}

/**
 * 获取课时列表
 * @param {Number} courseId 课程ID
 * @param {Object} params 查询参数
 * @returns {Promise} 课时列表
 */
export function getLessonList(courseId, params = {}) {
	const queryParams = {
		course_id: courseId,
		page: params.page || 1,
		limit: params.limit || 50,
		...params
	};

	return request.get('lessonList.php', queryParams);
}

/**
 * 获取课时详情
 * @param {Number} lessonId 课时ID
 * @param {Number} courseId 课程ID
 * @returns {Promise} 课时详情
 */
export function getLessonDetail(lessonId, courseId) {
	const params = {
		lesson_id: lessonId,
		course_id: courseId
	};

	return request.get('lessonDetail.php', params);
}

/**
 * 记录课时观看进度
 * @param {Object} progressData 进度数据
 * @returns {Promise} 记录结果
 */
export function updateLessonProgress(progressData) {
	return request.post('lessonProgress.php', progressData);
}

/**
 * 获取课时观看进度
 * @param {Number} lessonId 课时ID
 * @param {Number} courseId 课程ID
 * @returns {Promise} 观看进度
 */
export function getLessonProgress(lessonId, courseId) {
	const params = {
		lesson_id: lessonId,
		course_id: courseId
	};

	return request.get('lessonProgress.php', params);
}

/**
 * 获取课程观看进度
 * @param {Number} courseId 课程ID
 * @returns {Promise} 课程观看进度
 */
export function getCourseProgress(courseId) {
	const params = {
		course_id: courseId
	};

	return request.get('lessonProgress.php', params);
}

/**
 * 更新课程学习进度
 * @param {Number} courseId 课程ID
 * @param {Object} progressData 进度数据
 * @returns {Promise} 更新结果
 */
export function updateCourseProgress(courseId, progressData) {
	const userInfo = getUserInfo();
	
	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}
	
	return request.post('courseDetail.php', {
		user_id: userInfo.id,
		course_id: courseId,
		watch_time: progressData.watchTime || 0,
		progress_position: progressData.progressPosition || 0,
		watch_progress: progressData.watchProgress || 0
	});
}

/**
 * 获取用户课程列表
 * @param {Object} params 查询参数
 * @returns {Promise} 用户课程列表
 */
export function getUserCourses(params = {}) {
	const userInfo = getUserInfo();

	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}

	const queryParams = {
		user_id: userInfo.id,
		page: params.page || 1,
		limit: params.limit || CONFIG.PAGE_SIZE,
		status: params.status || 'active',
		search: params.search || '',
		...params
	};

	return request.get('userCourses.php', queryParams);
}

/**
 * 搜索课程
 * @param {String} keyword 搜索关键词
 * @param {Object} params 其他参数
 * @returns {Promise} 搜索结果
 */
export function searchCourses(keyword, params = {}) {
	return getCourseList({
		search: keyword,
		...params
	});
}

/**
 * 获取推荐课程
 * @param {Number} limit 数量限制
 * @returns {Promise} 推荐课程列表
 */
export function getRecommendedCourses(limit = 10) {
	return getCourseList({
		limit,
		recommended: 1
	});
}

/**
 * 获取热门课程
 * @param {Number} limit 数量限制
 * @returns {Promise} 热门课程列表
 */
export function getPopularCourses(limit = 10) {
	return getCourseList({
		limit,
		popular: 1
	});
}

/**
 * 获取最新课程
 * @param {Number} limit 数量限制
 * @returns {Promise} 最新课程列表
 */
export function getLatestCourses(limit = 10) {
	return getCourseList({
		limit,
		latest: 1
	});
}

/**
 * 收藏课程
 * @param {Number} courseId 课程ID
 * @returns {Promise} 收藏结果
 */
export function favoriteCourse(courseId) {
	const userInfo = getUserInfo();
	
	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}
	
	return request.post('course-favorite.php', {
		user_id: userInfo.id,
		course_id: courseId
	});
}

/**
 * 取消收藏课程
 * @param {Number} courseId 课程ID
 * @returns {Promise} 取消收藏结果
 */
export function unfavoriteCourse(courseId) {
	const userInfo = getUserInfo();
	
	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}
	
	return request.delete('course-favorite.php', {
		data: {
			user_id: userInfo.id,
			course_id: courseId
		}
	});
}

/**
 * 获取收藏的课程列表
 * @param {Object} params 查询参数
 * @returns {Promise} 收藏课程列表
 */
export function getFavoriteCourses(params = {}) {
	const userInfo = getUserInfo();
	
	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}
	
	const queryParams = {
		user_id: userInfo.id,
		page: params.page || 1,
		limit: params.limit || CONFIG.PAGE_SIZE,
		...params
	};
	
	return request.get('course-favorites.php', queryParams);
}

/**
 * 获取学习统计
 * @returns {Promise} 学习统计数据
 */
export function getStudyStatistics() {
	const userInfo = getUserInfo();

	// 允许未登录用户访问，返回默认统计数据
	const params = {};
	if (userInfo && userInfo.id) {
		params.user_id = userInfo.id;
	}

	return request.get('study-statistics.php', params);
}

/**
 * 获取学习历史
 * @param {Object} params 查询参数
 * @returns {Promise} 学习历史
 */
export function getStudyHistory(params = {}) {
	const userInfo = getUserInfo();
	
	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}
	
	const queryParams = {
		user_id: userInfo.id,
		page: params.page || 1,
		limit: params.limit || CONFIG.PAGE_SIZE,
		...params
	};
	
	return request.get('study-history.php', queryParams);
}

/**
 * 记录课程访问
 * @param {Number} courseId 课程ID
 * @returns {Promise} 记录结果
 */
export function recordCourseVisit(courseId) {
	const userInfo = getUserInfo();
	
	if (!userInfo) {
		return Promise.resolve(); // 未登录用户不记录访问
	}
	
	return request.post('course-visit.php', {
		user_id: userInfo.id,
		course_id: courseId,
		visit_time: new Date().toISOString()
	});
}

/**
 * 提交课程评价
 * @param {Number} courseId 课程ID
 * @param {Object} reviewData 评价数据
 * @returns {Promise} 提交结果
 */
export function submitCourseReview(courseId, reviewData) {
	const userInfo = getUserInfo();
	
	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}
	
	return request.post('course-review.php', {
		user_id: userInfo.id,
		course_id: courseId,
		rating: reviewData.rating,
		comment: reviewData.comment || ''
	});
}

/**
 * 获取课程评价列表
 * @param {Number} courseId 课程ID
 * @param {Object} params 查询参数
 * @returns {Promise} 评价列表
 */
export function getCourseReviews(courseId, params = {}) {
	const queryParams = {
		course_id: courseId,
		page: params.page || 1,
		limit: params.limit || CONFIG.PAGE_SIZE,
		...params
	};
	
	return request.get('course-reviews.php', queryParams);
}

/**
 * 检查课程访问权限
 * @param {Number} courseId 课程ID
 * @returns {Promise} 权限检查结果
 */
export function checkCourseAccess(courseId) {
	const userInfo = getUserInfo();

	if (!userInfo) {
		return Promise.resolve({ has_access: false, reason: 'not_logged_in' });
	}

	return request.get('courseDetail.php', {
		id: courseId,
		user_id: userInfo.id
	}).then(response => {
		// 从课程详情响应中提取权限信息
		if (response && response.data) {
			const courseData = response.data;
			return {
				has_access: courseData.user_info?.has_access || courseData.is_free === 1,
				reason: courseData.is_free === 1 ? 'free_course' : (courseData.user_info?.has_access ? 'purchased' : 'not_purchased'),
				course_info: courseData
			};
		}
		return { has_access: false, reason: 'unknown' };
	}).catch(error => {
		console.error('检查课程权限失败:', error);
		return { has_access: false, reason: 'error' };
	});
}

/**
 * 获取课程播放地址
 * @param {Number} courseId 课程ID
 * @returns {Promise} 播放地址
 */
export function getCoursePlayUrl(courseId) {
	const userInfo = getUserInfo();
	
	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}
	
	return request.get('course-play.php', {
		user_id: userInfo.id,
		course_id: courseId
	});
}

// ==================== 课时相关API ====================

/**
 * 课时API服务
 */
export const lessonApi = {
	/**
	 * 获取课时列表
	 * @param {Object} params 查询参数
	 * @returns {Promise} 课时列表
	 */
	getLessonList(params = {}) {
		const queryParams = {
			course_id: params.course_id,
			page: params.page || 1,
			limit: params.limit || CONFIG.PAGE_SIZE,
			show_all: params.show_all || 0,
			...params
		};

		return request.get('lessonList.php', queryParams);
	},

	/**
	 * 获取课时详情
	 * @param {Object} params 查询参数
	 * @returns {Promise} 课时详情
	 */
	getLessonDetail(params = {}) {
		const queryParams = {
			lesson_id: params.lesson_id,
			course_id: params.course_id || 0
		};

		return request.get('lessonDetail.php', queryParams);
	},

	/**
	 * 保存观看进度
	 * @param {Object} params 进度数据
	 * @returns {Promise} 保存结果
	 */
	saveWatchProgress(params = {}) {
		const userInfo = getUserInfo();

		if (!userInfo) {
			return Promise.reject({ code: 401, message: '请先登录' });
		}

		const progressData = {
			user_id: userInfo.id,
			lesson_id: params.lesson_id,
			course_id: params.course_id,
			watch_time: params.watch_time || 0,
			progress_position: params.progress_position || 0,
			completion_rate: params.completion_rate || 0,
			is_completed: params.is_completed || 0
		};

		return request.post('lessonProgress.php', progressData);
	},

	/**
	 * 获取观看进度
	 * @param {Object} params 查询参数
	 * @returns {Promise} 观看进度
	 */
	getWatchProgress(params = {}) {
		const userInfo = getUserInfo();

		if (!userInfo) {
			return Promise.reject({ code: 401, message: '请先登录' });
		}

		const queryParams = {
			user_id: userInfo.id,
			lesson_id: params.lesson_id,
			course_id: params.course_id
		};

		return request.get('lessonProgress.php', queryParams);
	},

	/**
	 * 获取课时VOD信息
	 * @param {Number} lessonId 课时ID
	 * @returns {Promise} VOD信息
	 */
	getLessonVodInfo(lessonId) {
		return request.get('lesson-vod.php', {
			lesson_id: lessonId
		});
	},

	/**
	 * 获取VOD视频状态
	 * @param {Object} params 查询参数
	 * @returns {Promise} 视频状态
	 */
	getVodStatus(params = {}) {
		const queryParams = {};

		if (params.file_id) {
			queryParams.file_id = params.file_id;
		}

		if (params.lesson_id) {
			queryParams.lesson_id = params.lesson_id;
		}

		return request.get('vod-status.php', queryParams);
	}
};
