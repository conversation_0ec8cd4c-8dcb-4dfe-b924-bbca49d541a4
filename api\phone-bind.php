<?php
/**
 * 手机号绑定API
 * 支持手机号绑定、验证、解绑等操作
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

$auth = new AuthAPI();

// 验证用户登录状态
$user = $auth->getCurrentUser();
if (!$user) {
    $auth->jsonResponse(401, '请先登录');
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    $auth->jsonResponse(400, '无效的请求数据');
}

$action = $input['action'] ?? '';
$phone = trim($input['phone'] ?? '');
$code = trim($input['code'] ?? '');

try {
    // 获取系统配置
    $settings_result = $auth->getConn()->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('phone_bind_required', 'phone_bind_mode', 'sms_code_expire_minutes', 'phone_bind_allow_change')");
    $settings = [];
    while ($row = $settings_result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }

    $phone_bind_required = $settings['phone_bind_required'] ?? '1';
    $phone_bind_mode = $settings['phone_bind_mode'] ?? 'direct';
    $phone_bind_allow_change = $settings['phone_bind_allow_change'] ?? '1';
    $expire_minutes = intval($settings['sms_code_expire_minutes'] ?? 5);
    
    switch ($action) {
        case 'bind':
            // 绑定手机号
            if (empty($phone)) {
                $auth->jsonResponse(400, '请输入手机号');
            }

            // 验证手机号格式
            if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
                $auth->jsonResponse(400, '手机号格式不正确');
            }

            // 检查是否允许修改手机号
            if (!empty($user['phone']) && $user['phone_verified'] && $phone_bind_allow_change !== '1') {
                $auth->jsonResponse(400, '系统不允许修改已绑定的手机号');
            }

            // 根据绑定模式处理
            if ($phone_bind_mode === 'sms') {
                // 短信验证模式
                if (empty($code)) {
                    $auth->jsonResponse(400, '请输入验证码');
                }

                // 验证验证码
                $verify_result = verifyCode($auth->getConn(), $phone, $code, 'bind', $user['id']);
                if (!$verify_result['success']) {
                    logPhoneAction($auth->getConn(), $user['id'], $phone, 'bind', 'failed', $verify_result['message']);
                    $auth->jsonResponse(400, $verify_result['message']);
                }
            } elseif ($phone_bind_mode === 'direct') {
                // 直接绑定模式（无需验证码）
                $verify_result = ['success' => true, 'code_id' => null];
            } else {
                $auth->jsonResponse(400, '未知的绑定模式');
            }
            
            // 检查手机号是否已被其他用户绑定
            $check_stmt = $auth->getConn()->prepare("SELECT id, name FROM users WHERE phone = ? AND id != ? AND phone_verified = 1");
            $check_stmt->bind_param("si", $phone, $user['id']);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                logPhoneAction($auth->getConn(), $user['id'], $phone, 'bind', 'failed', '手机号已被其他用户绑定');
                $auth->jsonResponse(400, '该手机号已被其他用户绑定');
            }
            
            // 开始事务
            $auth->getConn()->begin_transaction();
            
            try {
                // 更新用户手机号
                $stmt = $auth->getConn()->prepare("UPDATE users SET phone = ?, phone_verified = 1, phone_bind_required = 0, phone_bind_time = NOW(), updated_at = NOW() WHERE id = ?");
                $stmt->bind_param("si", $phone, $user['id']);
                $stmt->execute();
                
                // 标记验证码为已使用（仅在短信验证模式下）
                if ($phone_bind_mode === 'sms' && $verify_result['code_id']) {
                    $mark_used_stmt = $auth->getConn()->prepare("UPDATE sms_codes SET is_used = 1 WHERE id = ?");
                    $mark_used_stmt->bind_param("i", $verify_result['code_id']);
                    $mark_used_stmt->execute();
                }
                
                $auth->getConn()->commit();
                
                // 记录成功日志
                logPhoneAction($auth->getConn(), $user['id'], $phone, 'bind', 'success');
                
                $auth->jsonResponse(200, '手机号绑定成功', [
                    'phone' => $phone,
                    'bind_time' => date('Y-m-d H:i:s')
                ]);
                
            } catch (Exception $e) {
                $auth->getConn()->rollback();
                throw $e;
            }
            break;
            
        case 'unbind':
            // 解绑手机号
            if (empty($user['phone']) || !$user['phone_verified']) {
                $auth->jsonResponse(400, '您尚未绑定手机号');
            }
            
            // 检查是否允许解绑
            if ($phone_bind_required === '1' && $user['login_type'] === 'wechat') {
                $auth->jsonResponse(400, '微信用户必须绑定手机号，无法解绑');
            }
            
            // 开始事务
            $auth->getConn()->begin_transaction();
            
            try {
                // 清除手机号信息
                $stmt = $auth->getConn()->prepare("UPDATE users SET phone = NULL, phone_verified = 0, phone_bind_time = NULL, updated_at = NOW() WHERE id = ?");
                $stmt->bind_param("i", $user['id']);
                $stmt->execute();
                
                $auth->getConn()->commit();
                
                // 记录成功日志
                logPhoneAction($auth->getConn(), $user['id'], $user['phone'], 'unbind', 'success');
                
                $auth->jsonResponse(200, '手机号解绑成功');
                
            } catch (Exception $e) {
                $auth->getConn()->rollback();
                throw $e;
            }
            break;
            
        case 'verify':
            // 验证手机号（重新验证已绑定的手机号）
            if (empty($user['phone'])) {
                $auth->jsonResponse(400, '您尚未绑定手机号');
            }
            
            if (empty($code)) {
                $auth->jsonResponse(400, '请输入验证码');
            }
            
            // 验证验证码
            $verify_result = verifyCode($auth->getConn(), $user['phone'], $code, 'bind', $user['id']);
            if (!$verify_result['success']) {
                logPhoneAction($auth->getConn(), $user['id'], $user['phone'], 'verify', 'failed', $verify_result['message']);
                $auth->jsonResponse(400, $verify_result['message']);
            }
            
            // 更新验证状态
            $stmt = $auth->getConn()->prepare("UPDATE users SET phone_verified = 1, phone_bind_time = NOW(), updated_at = NOW() WHERE id = ?");
            $stmt->bind_param("i", $user['id']);
            $stmt->execute();
            
            // 标记验证码为已使用
            $mark_used_stmt = $auth->getConn()->prepare("UPDATE sms_codes SET is_used = 1 WHERE id = ?");
            $mark_used_stmt->bind_param("i", $verify_result['code_id']);
            $mark_used_stmt->execute();
            
            // 记录成功日志
            logPhoneAction($auth->getConn(), $user['id'], $user['phone'], 'verify', 'success');
            
            $auth->jsonResponse(200, '手机号验证成功');
            break;
            
        case 'status':
            // 获取绑定状态
            $bind_status = 'optional';
            
            if ($user['phone_verified']) {
                $bind_status = 'verified';
            } elseif ($user['phone_bind_required']) {
                $bind_status = 'required';
            } elseif (!empty($user['phone'])) {
                $bind_status = 'pending';
            }
            
            $auth->jsonResponse(200, '获取绑定状态成功', [
                'phone' => $user['phone'] ? substr($user['phone'], 0, 3) . '****' . substr($user['phone'], -4) : null,
                'phone_verified' => (bool)$user['phone_verified'],
                'phone_bind_required' => (bool)$user['phone_bind_required'],
                'bind_status' => $bind_status,
                'bind_time' => $user['phone_bind_time'],
                'can_unbind' => $phone_bind_required !== '1' || $user['login_type'] !== 'wechat'
            ]);
            break;
            
        default:
            $auth->jsonResponse(400, '无效的操作类型');
    }
    
} catch (Exception $e) {
    error_log('手机号绑定错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 验证短信验证码
 */
function verifyCode($conn, $phone, $code, $type, $user_id = null) {
    // 查找有效的验证码
    $stmt = $conn->prepare("SELECT id, expires_at FROM sms_codes WHERE phone = ? AND code = ? AND type = ? AND is_used = 0 AND expires_at > NOW() ORDER BY created_at DESC LIMIT 1");
    $stmt->bind_param("sss", $phone, $code, $type);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return ['success' => false, 'message' => '验证码无效或已过期'];
    }
    
    $code_record = $result->fetch_assoc();
    
    // 如果指定了用户ID，验证是否匹配
    if ($user_id !== null) {
        $user_stmt = $conn->prepare("SELECT user_id FROM sms_codes WHERE id = ?");
        $user_stmt->bind_param("i", $code_record['id']);
        $user_stmt->execute();
        $user_result = $user_stmt->get_result();
        
        if ($user_result->num_rows > 0) {
            $code_user = $user_result->fetch_assoc();
            if ($code_user['user_id'] && $code_user['user_id'] != $user_id) {
                return ['success' => false, 'message' => '验证码不匹配'];
            }
        }
    }
    
    return ['success' => true, 'code_id' => $code_record['id']];
}

/**
 * 记录手机号操作日志
 */
function logPhoneAction($conn, $user_id, $phone, $action, $status, $failure_reason = null) {
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    $stmt = $conn->prepare("INSERT INTO phone_bind_logs (user_id, phone, action, status, failure_reason, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("issssss", $user_id, $phone, $action, $status, $failure_reason, $ip_address, $user_agent);
    $stmt->execute();
}
