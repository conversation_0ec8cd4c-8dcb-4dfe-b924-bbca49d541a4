<template>
	<view class="announcement-detail-container">
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<uni-load-more status="loading"></uni-load-more>
		</view>
		
		<!-- 公告内容 -->
		<view class="announcement-content" v-else-if="announcementInfo">
			<!-- 公告头部 -->
			<view class="announcement-header">
				<text class="announcement-title">{{ announcementInfo.title }}</text>
				<view class="announcement-tags">
					<uni-tag 
						:text="getTypeText(announcementInfo.type)" 
						:type="getTypeColor(announcementInfo.type)"
					></uni-tag>
					<uni-tag 
						v-if="announcementInfo.priority > 0"
						:text="getPriorityText(announcementInfo.priority)" 
						type="error"
					></uni-tag>
					<uni-tag 
						v-if="announcementInfo.is_pinned"
						text="置顶" 
						type="warning"
					></uni-tag>
				</view>
			</view>
			
			<!-- 公告元信息 -->
			<view class="announcement-meta">
				<view class="meta-item">
					<uni-icons type="person" size="14" color="#999" class="meta-icon"></uni-icons>
					<text class="meta-text">{{ announcementInfo.author_name || '系统' }}</text>
				</view>
				<view class="meta-item">
					<uni-icons type="calendar" size="14" color="#999" class="meta-icon"></uni-icons>
					<text class="meta-text">{{ formatDate(announcementInfo.publish_time) }}</text>
				</view>
				<view class="meta-item">
					<uni-icons type="eye" size="14" color="#999" class="meta-icon"></uni-icons>
					<text class="meta-text">{{ announcementInfo.view_count || 0 }} 次浏览</text>
				</view>
			</view>
			
			<!-- 过期提醒 -->
			<view class="expire-notice" v-if="isExpired">
				<uni-icons type="info" size="16" color="#ff6b6b" class="expire-icon"></uni-icons>
				<text class="expire-text">此公告已于 {{ formatDate(announcementInfo.expire_time) }} 过期</text>
			</view>
			
			<!-- 公告正文 -->
			<view class="announcement-body">
				<rich-text :nodes="formatContent(announcementInfo.content)"></rich-text>
			</view>
			
			<!-- 分类信息 -->
			<view class="category-section" v-if="announcementInfo.category_name">
				<text class="category-label">分类：</text>
				<text class="category-name">{{ announcementInfo.category_name }}</text>
			</view>
			
			<!-- 操作按钮 -->
			<view class="action-section" v-if="isLoggedIn">
				<button
					class="action-btn"
					:class="{ favorited: isFavorited }"
					@click="toggleFavorite"
				>
					<uni-icons
						:type="isFavorited ? 'heart-filled' : 'heart'"
						size="16"
						:color="isFavorited ? '#ff6b6b' : '#999'"
						class="action-icon"
					></uni-icons>
					<text class="btn-text">{{ isFavorited ? '已收藏' : '收藏' }}</text>
				</button>
				<button class="action-btn" @click="shareAnnouncement">
					<uni-icons type="redo" size="16" color="#999" class="action-icon"></uni-icons>
					<text class="btn-text">分享</text>
				</button>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view class="error-state" v-else-if="error">
			<uni-icons type="info" size="60" color="#ccc"></uni-icons>
			<text class="error-text">{{ error }}</text>
			<button class="retry-btn" @click="loadAnnouncementDetail">重试</button>
		</view>
	</view>
</template>

<script>
	import { isLoggedIn, formatDate, showSuccess, showError } from '../../utils/storage.js';
	import { getAnnouncementDetail, markAnnouncementAsRead, favoriteAnnouncement, unfavoriteAnnouncement, checkAnnouncementFavoriteStatus } from '../../api/announcement.js';
	
	export default {
		data() {
			return {
				loading: false,
				announcementId: null,
				announcementInfo: null,
				error: null,
				isLoggedIn: false,
				isFavorited: false
			};
		},
		
		computed: {
			/**
			 * 是否已过期
			 */
			isExpired() {
				if (!this.announcementInfo || !this.announcementInfo.expire_time) {
					return false;
				}
				return new Date(this.announcementInfo.expire_time) < new Date();
			}
		},
		
		onLoad(options) {
			if (options.id) {
				this.announcementId = parseInt(options.id);
				this.initPage();
			} else {
				this.error = '公告ID无效';
			}
		},
		
		onShow() {
			this.checkLoginStatus();
		},
		
		onShareAppMessage() {
			if (this.announcementInfo) {
				return {
					title: this.announcementInfo.title,
					path: `/pages/announcements/detail?id=${this.announcementId}`
				};
			}
			return {};
		},
		
		methods: {
			/**
			 * 初始化页面
			 */
			initPage() {
				this.checkLoginStatus();
				this.loadAnnouncementDetail();
			},
			
			/**
			 * 检查登录状态
			 */
			checkLoginStatus() {
				this.isLoggedIn = isLoggedIn();
			},
			
			/**
			 * 加载公告详情
			 */
			async loadAnnouncementDetail() {
				if (!this.announcementId) return;
				
				this.loading = true;
				this.error = null;
				
				try {
					const response = await getAnnouncementDetail(this.announcementId);
					if (response.code === 200) {
						this.announcementInfo = response.data;
						
						// 设置页面标题
						uni.setNavigationBarTitle({
							title: this.announcementInfo.title.length > 10 
								? this.announcementInfo.title.substring(0, 10) + '...' 
								: this.announcementInfo.title
						});
						
						// 标记为已读
						this.markAsRead();
						
						// 检查收藏状态
						this.checkFavoriteStatus();
					} else {
						this.error = response.message || '加载公告详情失败';
					}
				} catch (error) {
					console.error('加载公告详情失败:', error);
					this.error = error.message || '网络错误，请稍后重试';
				} finally {
					this.loading = false;
				}
			},
			
			/**
			 * 标记为已读
			 */
			async markAsRead() {
				if (!this.isLoggedIn) return;
				
				try {
					await markAnnouncementAsRead(this.announcementId);
				} catch (error) {
					console.error('标记已读失败:', error);
				}
			},
			
			/**
			 * 检查收藏状态
			 */
			async checkFavoriteStatus() {
				if (!this.isLoggedIn) {
					this.isFavorited = false;
					return;
				}

				try {
					const response = await checkAnnouncementFavoriteStatus(this.announcementId);
					if (response.code === 200) {
						this.isFavorited = response.data.is_favorited;
					} else {
						this.isFavorited = false;
					}
				} catch (error) {
					console.error('检查收藏状态失败:', error);
					this.isFavorited = false;
				}
			},
			
			/**
			 * 切换收藏状态
			 */
			async toggleFavorite() {
				if (!this.isLoggedIn) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					return;
				}
				
				try {
					if (this.isFavorited) {
						await unfavoriteAnnouncement(this.announcementId);
						this.isFavorited = false;
						showSuccess('取消收藏成功');
					} else {
						await favoriteAnnouncement(this.announcementId);
						this.isFavorited = true;
						showSuccess('收藏成功');
					}
				} catch (error) {
					console.error('收藏操作失败:', error);
					showError(error.message || '操作失败');
				}
			},
			
			/**
			 * 分享公告
			 */
			shareAnnouncement() {
				// 微信小程序分享需要通过onShareAppMessage实现
				// 这里只是触发分享，实际分享内容在onShareAppMessage中定义
				if (typeof wx !== 'undefined' && wx.showShareMenu) {
					wx.showShareMenu({
						withShareTicket: true,
						success: () => {
							showSuccess('请点击右上角分享按钮进行分享');
						},
						fail: (error) => {
							console.error('显示分享菜单失败:', error);
							showError('分享功能暂不可用');
						}
					});
				} else {
					showError('当前环境不支持分享功能');
				}
			},
			
			/**
			 * 格式化内容
			 */
			formatContent(content) {
				if (!content) return '';
				
				// 将换行符转换为<br>标签
				return content.replace(/\n/g, '<br>');
			},
			
			/**
			 * 获取类型文本
			 */
			getTypeText(type) {
				const typeMap = {
					notice: '通知',
					urgent: '紧急',
					system: '系统',
					activity: '活动'
				};
				return typeMap[type] || '通知';
			},
			
			/**
			 * 获取类型颜色
			 */
			getTypeColor(type) {
				const colorMap = {
					notice: 'primary',
					urgent: 'error',
					system: 'warning',
					activity: 'success'
				};
				return colorMap[type] || 'primary';
			},
			
			/**
			 * 获取优先级文本
			 */
			getPriorityText(priority) {
				const priorityMap = {
					1: '重要',
					2: '紧急'
				};
				return priorityMap[priority] || '';
			},
			
			/**
			 * 格式化日期
			 */
			formatDate(date) {
				return formatDate(date, 'YYYY-MM-DD HH:mm');
			}
		}
	};
</script>

<style lang="scss" scoped>
.announcement-detail-container {
	background: #f8f9fa;
	min-height: 100vh;
}

.loading-container {
	padding: 60px 20px;
	text-align: center;
}

.announcement-content {
	background: #fff;
	margin: 10px;
	border-radius: 12px;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.announcement-header {
	padding: 20px 20px 15px;
	border-bottom: 1px solid #f0f0f0;

	.announcement-title {
		display: block;
		font-size: 20px;
		font-weight: 600;
		color: #333;
		line-height: 1.4;
		margin-bottom: 15px;
	}

	.announcement-tags {
		display: flex;
		gap: 8px;
		flex-wrap: wrap;
	}
}

.announcement-meta {
	padding: 15px 20px;
	border-bottom: 1px solid #f0f0f0;
	display: flex;
	flex-wrap: wrap;
	gap: 20px;

	.meta-item {
		display: flex;
		align-items: center;
		gap: 5px;

		.meta-text {
			font-size: 14px;
			color: #666;
		}
	}
}

.expire-notice {
	padding: 15px 20px;
	background: #fff3cd;
	border-bottom: 1px solid #f0f0f0;
	display: flex;
	align-items: center;
	gap: 8px;

	.expire-text {
		font-size: 14px;
		color: #856404;
	}
}

.announcement-body {
	padding: 20px;
	line-height: 1.6;
	font-size: 16px;
	color: #333;

	// 富文本样式
	:deep(p) {
		margin-bottom: 15px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	:deep(img) {
		max-width: 100%;
		height: auto;
		border-radius: 8px;
		margin: 10px 0;
	}

	:deep(a) {
		color: #007bff;
		text-decoration: none;
	}

	:deep(strong) {
		font-weight: 600;
	}

	:deep(em) {
		font-style: italic;
	}

	:deep(ul), :deep(ol) {
		padding-left: 20px;
		margin-bottom: 15px;
	}

	:deep(li) {
		margin-bottom: 5px;
	}

	:deep(blockquote) {
		border-left: 4px solid #007bff;
		padding-left: 15px;
		margin: 15px 0;
		color: #666;
		font-style: italic;
	}

	:deep(code) {
		background: #f8f9fa;
		padding: 2px 6px;
		border-radius: 4px;
		font-family: 'Courier New', monospace;
		font-size: 14px;
	}

	:deep(pre) {
		background: #f8f9fa;
		padding: 15px;
		border-radius: 8px;
		overflow-x: auto;
		margin: 15px 0;

		code {
			background: none;
			padding: 0;
		}
	}
}

.category-section {
	padding: 15px 20px;
	border-top: 1px solid #f0f0f0;
	display: flex;
	align-items: center;

	.category-label {
		font-size: 14px;
		color: #666;
		margin-right: 8px;
	}

	.category-name {
		font-size: 14px;
		color: #007bff;
		background: #e7f3ff;
		padding: 4px 12px;
		border-radius: 12px;
	}
}

.action-section {
	padding: 20px;
	border-top: 1px solid #f0f0f0;
	display: flex;
	gap: 15px;

	.action-btn {
		flex: 1;
		height: 44px;
		border-radius: 22px;
		border: 1px solid #e9ecef;
		background: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 8px;

		&.favorited {
			background: #fff5f5;
			border-color: #ff6b6b;
		}

		.btn-text {
			font-size: 14px;
			color: #666;
		}
	}
}

.error-state {
	text-align: center;
	padding: 60px 20px;

	.error-text {
		display: block;
		font-size: 16px;
		color: #999;
		margin: 20px 0;
	}

	.retry-btn {
		background: #007bff;
		color: #fff;
		border: none;
		border-radius: 20px;
		padding: 10px 30px;
		font-size: 14px;
	}
}
</style>
