<?php
/**
 * 课时VOD管理API
 * 用于管理课时的腾讯云点播信息
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db.php';
require_once '../includes/lesson_db_setup.php';
require_once '../includes/auth.php';

// 确保数据库表结构正确
$setup_result = setupLessonTables($conn);
if (!$setup_result['success']) {
    echo json_encode([
        'success' => false,
        'message' => '数据库初始化失败',
        'error' => implode(', ', $setup_result['messages'])
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetRequest($conn);
            break;
            
        case 'POST':
            handlePostRequest($conn);
            break;
            
        case 'PUT':
            handlePutRequest($conn);
            break;
            
        case 'DELETE':
            handleDeleteRequest($conn);
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => '不支持的请求方法'
            ], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '服务器错误',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 处理GET请求 - 获取课时VOD信息
 */
function handleGetRequest($conn) {
    $lesson_id = isset($_GET['lesson_id']) ? intval($_GET['lesson_id']) : 0;
    
    if ($lesson_id <= 0) {
        echo json_encode([
            'success' => false,
            'message' => '课时ID无效'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // 查询课时VOD信息
    $sql = "SELECT 
                id,
                course_id,
                title,
                video_url,
                vod_file_id,
                vod_video_url,
                video_type,
                duration,
                thumbnail,
                status
            FROM lessons 
            WHERE id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $lesson_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $lesson = $result->fetch_assoc();
    
    if (!$lesson) {
        echo json_encode([
            'success' => false,
            'message' => '课时不存在'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // 格式化返回数据
    $vod_info = [
        'lesson_id' => intval($lesson['id']),
        'course_id' => intval($lesson['course_id']),
        'title' => $lesson['title'],
        'video_type' => $lesson['video_type'] ?? 'url',
        'video_url' => $lesson['video_url'],
        'vod_file_id' => $lesson['vod_file_id'],
        'vod_video_url' => $lesson['vod_video_url'],
        'duration' => intval($lesson['duration']),
        'duration_formatted' => $lesson['duration'] ? gmdate("H:i:s", $lesson['duration']) : null,
        'thumbnail' => $lesson['thumbnail'],
        'status' => intval($lesson['status']),
        'has_vod' => !empty($lesson['vod_file_id'])
    ];
    
    echo json_encode([
        'success' => true,
        'data' => $vod_info,
        'message' => '获取课时VOD信息成功'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 处理POST请求 - 更新课时VOD信息
 */
function handlePostRequest($conn) {
    // 验证管理员权限
    session_start();
    if (!isset($_SESSION['admin_id'])) {
        echo json_encode([
            'success' => false,
            'message' => '需要管理员权限'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    $lesson_id = isset($input['lesson_id']) ? intval($input['lesson_id']) : 0;
    $vod_file_id = isset($input['vod_file_id']) ? trim($input['vod_file_id']) : '';
    $vod_video_url = isset($input['vod_video_url']) ? trim($input['vod_video_url']) : '';
    $duration = isset($input['duration']) ? intval($input['duration']) : 0;
    $thumbnail = isset($input['thumbnail']) ? trim($input['thumbnail']) : '';
    
    if ($lesson_id <= 0) {
        echo json_encode([
            'success' => false,
            'message' => '课时ID无效'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    if (empty($vod_file_id)) {
        echo json_encode([
            'success' => false,
            'message' => 'VOD文件ID不能为空'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // 检查课时是否存在
    $check_sql = "SELECT id FROM lessons WHERE id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("i", $lesson_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows == 0) {
        echo json_encode([
            'success' => false,
            'message' => '课时不存在'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // 更新课时VOD信息
    $update_sql = "UPDATE lessons SET 
                    vod_file_id = ?, 
                    vod_video_url = ?, 
                    video_type = 'vod',
                    duration = ?,
                    thumbnail = CASE WHEN ? != '' THEN ? ELSE thumbnail END,
                    updated_at = CURRENT_TIMESTAMP
                   WHERE id = ?";
    
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param("ssissi", $vod_file_id, $vod_video_url, $duration, $thumbnail, $thumbnail, $lesson_id);
    
    if ($update_stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => '课时VOD信息更新成功'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'success' => false,
            'message' => '更新失败: ' . $conn->error
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * 处理PUT请求 - 切换视频类型
 */
function handlePutRequest($conn) {
    // 验证管理员权限
    session_start();
    if (!isset($_SESSION['admin_id'])) {
        echo json_encode([
            'success' => false,
            'message' => '需要管理员权限'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // 获取PUT数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    $lesson_id = isset($input['lesson_id']) ? intval($input['lesson_id']) : 0;
    $video_type = isset($input['video_type']) ? trim($input['video_type']) : '';
    
    if ($lesson_id <= 0) {
        echo json_encode([
            'success' => false,
            'message' => '课时ID无效'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    if (!in_array($video_type, ['url', 'vod'])) {
        echo json_encode([
            'success' => false,
            'message' => '视频类型无效'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // 更新视频类型
    $update_sql = "UPDATE lessons SET video_type = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param("si", $video_type, $lesson_id);
    
    if ($update_stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => '视频类型切换成功'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'success' => false,
            'message' => '切换失败: ' . $conn->error
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * 处理DELETE请求 - 删除VOD信息
 */
function handleDeleteRequest($conn) {
    // 验证管理员权限
    session_start();
    if (!isset($_SESSION['admin_id'])) {
        echo json_encode([
            'success' => false,
            'message' => '需要管理员权限'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    $lesson_id = isset($_GET['lesson_id']) ? intval($_GET['lesson_id']) : 0;
    
    if ($lesson_id <= 0) {
        echo json_encode([
            'success' => false,
            'message' => '课时ID无效'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // 清除VOD信息
    $update_sql = "UPDATE lessons SET 
                    vod_file_id = NULL, 
                    vod_video_url = NULL, 
                    video_type = 'url',
                    updated_at = CURRENT_TIMESTAMP
                   WHERE id = ?";
    
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param("i", $lesson_id);
    
    if ($update_stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'VOD信息删除成功'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'success' => false,
            'message' => '删除失败: ' . $conn->error
        ], JSON_UNESCAPED_UNICODE);
    }
}
?>
