<?php
/**
 * 系统设置API
 * 获取和更新小程序需要的系统设置信息
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            getSystemSettings($auth);
            break;
        case 'POST':
            updateSystemSettings($auth);
            break;
        default:
            $auth->jsonResponse(405, '不支持的请求方法');
    }

} catch (Exception $e) {
    error_log('系统设置API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 获取系统设置
 */
function getSystemSettings($auth) {
    $conn = $auth->getConn();
    
    // 定义小程序需要的设置项
    $required_settings = [
        'allow_password_change',
        'allow_profile_edit',
        'password_change_notice',
        'phone_bind_required',
        'phone_bind_mode',
        'phone_bind_force_new_user',
        'phone_bind_allow_change',
        'wechat_app_id',
        'maintenance_mode',
        'website_title',
        'website_description',
        // 新增外观设置
        'app_logo_url',
        'app_title',
        'app_subtitle'
    ];
    
    // 构建查询条件
    $placeholders = str_repeat('?,', count($required_settings) - 1) . '?';
    $stmt = $conn->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ($placeholders)");
    $stmt->bind_param(str_repeat('s', count($required_settings)), ...$required_settings);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $settings = [];
    while ($row = $result->fetch_assoc()) {
        $key = $row['setting_key'];
        $value = $row['setting_value'];
        
        // 转换布尔值
        if (in_array($key, ['allow_password_change', 'allow_profile_edit', 'phone_bind_required', 
                           'phone_bind_force_new_user', 'phone_bind_allow_change', 'maintenance_mode'])) {
            $settings[$key] = $value === '1';
        } else {
            $settings[$key] = $value;
        }
    }
    
    // 设置默认值
    $defaults = [
        'allow_password_change' => true,
        'allow_profile_edit' => true,
        'password_change_notice' => '管理员已禁用密码修改功能，如需修改密码请联系客服。',
        'phone_bind_required' => false,
        'phone_bind_mode' => 'direct',
        'phone_bind_force_new_user' => false,
        'phone_bind_allow_change' => true,
        'wechat_app_id' => '',
        'maintenance_mode' => false,
        'website_title' => '课程学习系统',
        'website_description' => '在线课程学习平台',
        // 新增外观设置默认值
        'app_logo_url' => '/static/logo.png',
        'app_title' => '课程学习系统',
        'app_subtitle' => '在线学习，随时随地'
    ];
    
    // 合并默认值
    foreach ($defaults as $key => $default_value) {
        if (!isset($settings[$key])) {
            $settings[$key] = $default_value;
        }
    }

    // 处理logo URL，为小程序提供专用的图片访问接口
    if (isset($settings['app_logo_url']) && strpos($settings['app_logo_url'], '/uploads/logos/') === 0) {
        // 使用专门的小程序图片API
        $settings['app_logo_url'] = 'https://wx.yx420.cn/api/wx-image.php?path=' . urlencode($settings['app_logo_url']);

        // 同时提供直接访问的备用URL
        $settings['app_logo_url_direct'] = 'https://wx.yx420.cn' . $settings['app_logo_url'];
    }

    // 添加额外信息
    $settings['server_time'] = date('Y-m-d H:i:s');
    $settings['api_version'] = '1.0.0';

    $auth->jsonResponse(200, '获取系统设置成功', $settings);
}

/**
 * 更新系统设置
 */
function updateSystemSettings($auth) {
    // 验证管理员权限
    $user = $auth->getCurrentUser();
    if (!$user || $user['role'] !== 'admin') {
        $auth->jsonResponse(403, '需要管理员权限');
    }

    $conn = $auth->getConn();

    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $auth->jsonResponse(400, '无效的请求数据');
    }

    // 定义允许更新的设置项
    $allowed_settings = [
        'app_title',
        'app_subtitle',
        'website_title',
        'website_description',
        'allow_password_change',
        'allow_profile_edit',
        'password_change_notice'
    ];

    $updated_count = 0;
    $errors = [];

    foreach ($input as $key => $value) {
        if (!in_array($key, $allowed_settings)) {
            $errors[] = "不允许更新设置项: $key";
            continue;
        }

        // 数据验证
        if (!validateSettingValue($key, $value)) {
            $errors[] = "设置项 $key 的值无效";
            continue;
        }

        // 转换布尔值
        if (in_array($key, ['allow_password_change', 'allow_profile_edit'])) {
            $value = $value ? '1' : '0';
        }

        // 更新设置
        $stmt = $conn->prepare("
            INSERT INTO settings (setting_key, setting_value)
            VALUES (?, ?)
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
        ");
        $stmt->bind_param("ss", $key, $value);

        if ($stmt->execute()) {
            $updated_count++;
        } else {
            $errors[] = "更新设置项 $key 失败: " . $conn->error;
        }
    }

    if (!empty($errors)) {
        $auth->jsonResponse(400, '部分设置更新失败', [
            'updated_count' => $updated_count,
            'errors' => $errors
        ]);
    }

    if ($updated_count === 0) {
        $auth->jsonResponse(400, '没有有效的设置项需要更新');
    }

    $auth->jsonResponse(200, '系统设置更新成功', [
        'updated_count' => $updated_count
    ]);
}

/**
 * 验证设置值
 */
function validateSettingValue($key, $value) {
    switch ($key) {
        case 'app_title':
        case 'app_subtitle':
        case 'website_title':
        case 'website_description':
            return is_string($value) && strlen(trim($value)) > 0 && strlen($value) <= 255;

        case 'password_change_notice':
            return is_string($value) && strlen($value) <= 1000;

        case 'allow_password_change':
        case 'allow_profile_edit':
            return is_bool($value);

        default:
            return true;
    }
}
?>
