<?php
require_once '../includes/db.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

function sendResponse($code, $message, $data = null) {
    $response = [
        'code' => $code,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ];
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getAnnouncementDetail();
        break;
    case 'POST':
        markAsRead();
        break;
    default:
        sendResponse(405, '不支持的请求方法');
}

function getAnnouncementDetail() {
    global $conn;
    
    $id = $_GET['id'] ?? 0;
    if (!$id) {
        sendResponse(400, '缺少公告ID');
    }
    
    // 获取公告详情
    $sql = "SELECT 
                a.id, a.title, a.content, a.type, a.priority, a.is_pinned,
                a.category_id, a.publish_time, a.expire_time, a.view_count,
                a.created_at, a.updated_at,
                c.name as category_name,
                ad.username as author_name
            FROM announcements a 
            LEFT JOIN announcement_categories c ON a.category_id = c.id
            LEFT JOIN admins ad ON a.author_id = ad.id
            WHERE a.id = ? AND a.status = 'published'";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        // 增加查看次数
        $update_sql = "UPDATE announcements SET view_count = view_count + 1 WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("i", $id);
        $update_stmt->execute();
        
        $row['view_count'] = $row['view_count'] + 1;
        
        sendResponse(200, '获取成功', $row);
    } else {
        sendResponse(404, '公告不存在或未发布');
    }
}

function markAsRead() {
    global $conn;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $announcement_id = $input['announcement_id'] ?? 0;
    $user_id = $input['user_id'] ?? 0;
    
    if (!$announcement_id || !$user_id) {
        sendResponse(400, '缺少必要参数');
    }
    
    // 检查是否已经阅读过
    $check_sql = "SELECT id FROM announcement_reads WHERE announcement_id = ? AND user_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("ii", $announcement_id, $user_id);
    $check_stmt->execute();
    $exists = $check_stmt->get_result()->fetch_assoc();
    
    if ($exists) {
        sendResponse(200, '已标记为已读');
    }
    
    // 插入阅读记录
    $sql = "INSERT INTO announcement_reads (announcement_id, user_id) VALUES (?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $announcement_id, $user_id);
    
    if ($stmt->execute()) {
        sendResponse(200, '标记为已读成功');
    } else {
        sendResponse(500, '标记失败：' . $conn->error);
    }
}
?>