<template>
	<view class="profile-container">
		<!-- 用户信息头部 -->
		<view class="user-header" v-if="isLoggedIn">
			<view class="user-info">
				<view class="user-avatar" @click="changeAvatar">
					<image
						v-if="shouldShowAvatar && !avatarLoadError"
						:src="getAvatarUrl(userInfo.avatar)"
						mode="aspectFill"
						class="avatar-image"
						@error="handleAvatarError"
						@load="onAvatarLoad"
					></image>
					<view v-else class="default-avatar">
						<simple-icon type="person" size="40" color="#999"></simple-icon>
					</view>
				</view>
				<view class="user-details">
					<text class="user-name clickable" @click="editUserName">{{ userInfo.nickname || userInfo.name || '用户' }}</text>
					<text class="user-email clickable" @click="copyEmail">{{ userInfo.email || '未设置邮箱' }}</text>
					<view class="user-status">
						<uni-tag
							:text="getStatusText(userInfo.status)"
							:type="getStatusColor(userInfo.status)"
							size="mini"
						></uni-tag>
					</view>
					<view class="user-tips">
						<text class="tips-text">点击昵称可修改，点击邮箱可复制</text>
					</view>
				</view>
			</view>
			<view class="user-actions">
				<simple-icon type="settings" size="24" color="#666" @click="goToSettings"></simple-icon>
			</view>
		</view>
		
		<!-- 未登录状态 -->
		<view class="login-prompt" v-else>
			<view class="prompt-content">
				<simple-icon type="person" size="60" color="#ccc"></simple-icon>
				<text class="prompt-text">登录后查看个人信息</text>
				<button class="login-btn" @click="goToLogin">立即登录</button>
			</view>
		</view>
		
		<!-- 学习统计 -->
		<view class="stats-section" v-if="isLoggedIn && studyStats">
			<view class="section-title">学习统计</view>
			<view class="stats-grid">
				<view class="stat-item">
					<text class="stat-number">{{ studyStats.total_courses || 0 }}</text>
					<text class="stat-label">总课程</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ studyStats.active_courses || 0 }}</text>
					<text class="stat-label">进行中</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ studyStats.completed_courses || 0 }}</text>
					<text class="stat-label">已完成</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ studyStats.total_watch_time || 0 }}h</text>
					<text class="stat-label">学习时长</text>
				</view>
			</view>
		</view>
		
		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-group">
				<view class="menu-item" @click="goToCourses" v-if="isLoggedIn">
					<view class="menu-left">
						<simple-icon type="video" size="20" color="#007bff"></simple-icon>
						<text class="menu-text">我的课程</text>
					</view>
					<simple-icon type="forward" size="16" color="#ccc"></simple-icon>
				</view>

				<view class="menu-item" @click="goToOrders" v-if="isLoggedIn">
					<view class="menu-left">
						<simple-icon type="order" size="20" color="#28a745"></simple-icon>
						<text class="menu-text">订单中心</text>
					</view>
					<simple-icon type="forward" size="16" color="#ccc"></simple-icon>
				</view>

				<view class="menu-item" @click="goToSettings">
					<view class="menu-left">
						<simple-icon type="settings" size="20" color="#6c757d"></simple-icon>
						<text class="menu-text">设置</text>
					</view>
					<simple-icon type="forward" size="16" color="#ccc"></simple-icon>
				</view>
			</view>
			
			<view class="menu-group">
				<view class="menu-item" @click="goToAbout">
					<view class="menu-left">
						<simple-icon type="info" size="20" color="#17a2b8"></simple-icon>
						<text class="menu-text">关于我们</text>
					</view>
					<simple-icon type="forward" size="16" color="#ccc"></simple-icon>
				</view>

				<view class="menu-item" @click="contactCustomerService">
					<view class="menu-left">
						<text class="menu-icon">🎧</text>
						<text class="menu-text">联系客服</text>
					</view>
					<simple-icon type="forward" size="16" color="#ccc"></simple-icon>
				</view>
			</view>
		</view>
		
		<!-- 退出登录 -->
		<view class="logout-section" v-if="isLoggedIn">
			<button class="logout-btn" @click="handleLogout">退出登录</button>
		</view>
	</view>
</template>

<script>
	import { isLoggedIn, getUserInfo, setUserInfo, clearUserInfo, getAccessToken, showSuccess, showConfirm, showError, showLoading, hideLoading } from '../../utils/storage.js';
	import { authMixin } from '../../utils/auth.js';
	import { logout } from '../../api/auth.js';
	import { getStudyStatistics } from '../../api/course.js';
	import { getUnreadAnnouncementCount } from '../../api/announcement.js';
	import SimpleIcon from '../../components/simple-icon/simple-icon.vue';

	export default {
		mixins: [authMixin],
		components: {
			SimpleIcon
		},
		
		data() {
			return {
				isLoggedIn: false,
				userInfo: {},
				studyStats: null,
				unreadCount: 0,
				avatarLoadError: false
			};
		},

		computed: {
			/**
			 * 判断是否应该显示头像
			 */
			shouldShowAvatar() {
				// 简化逻辑：只要有头像字段就尝试显示，让图片组件自己处理加载失败
				if (!this.userInfo.avatar || this.userInfo.avatar === '') {
					console.log('shouldShowAvatar: 没有头像数据');
					return false;
				}

				const avatarUrl = this.getAvatarUrl(this.userInfo.avatar);
				console.log('shouldShowAvatar: 头像URL =', avatarUrl);
				return avatarUrl !== '';
			}
		},
		
		onLoad() {
			this.initPage();
		},
		
		onShow() {
			this.checkLoginStatus();
			// 只有在登录状态下才加载数据
			if (this.isLoggedIn) {
				this.loadPageData();
			}
			// 强制更新视图以确保头像正确显示
			this.$forceUpdate();
		},
		
		onPullDownRefresh() {
			this.loadPageData().finally(() => {
				uni.stopPullDownRefresh();
			});
		},
		
		methods: {
			/**
			 * 初始化页面
			 */
			initPage() {
				this.checkLoginStatus();
				this.loadPageData();
			},
			
			/**
			 * 检查登录状态
			 */
			checkLoginStatus() {
				this.isLoggedIn = isLoggedIn();
				if (this.isLoggedIn) {
					this.userInfo = getUserInfo() || {};
					console.log('当前用户信息:', this.userInfo);
					console.log('用户头像:', this.userInfo.avatar);
					// 重置头像错误状态
					this.avatarLoadError = false;
				} else {
					this.userInfo = {};
					this.avatarLoadError = false;
				}
			},
			
			/**
			 * 加载页面数据
			 */
			async loadPageData() {
				if (!this.isLoggedIn) return;

				try {
					// 串行加载数据，避免并发请求导致的token问题
					await this.refreshUserInfo();
					await this.loadStudyStatistics();
					await this.loadUnreadCount();
				} catch (error) {
					console.error('加载页面数据失败:', error);
					// 如果是认证错误，不需要处理，request.js会自动处理
					if (error.code !== 401) {
						uni.showToast({
							title: '加载数据失败',
							icon: 'none'
						});
					}
				}
			},

			/**
			 * 刷新用户信息
			 */
			async refreshUserInfo() {
				try {
					const { getCurrentUser } = await import('../../api/auth.js');
					const response = await getCurrentUser();
					if (response.code === 200) {
						console.log('刷新用户信息成功:', response.data.user);
						this.userInfo = response.data.user;
						setUserInfo(this.userInfo);

						// 重置头像错误状态，让头像重新尝试加载
						this.avatarLoadError = false;

						// 强制更新视图
						this.$forceUpdate();
					}
				} catch (error) {
					console.error('刷新用户信息失败:', error);
					// 如果是认证错误，不显示提示
					if (error.code !== 401) {
						console.log('使用本地缓存的用户信息');
					}
				}
			},

			/**
			 * 验证头像URL是否有效（小程序环境）
			 * 注释掉复杂的验证逻辑，让图片组件自己处理加载失败
			 */
			async validateAvatarUrl(avatar) {
				// 简化验证逻辑，直接返回true，让图片组件自己处理加载
				console.log('跳过头像URL验证，让图片组件自己处理:', avatar);
				return true;
			},
			
			/**
			 * 加载学习统计
			 */
			async loadStudyStatistics() {
				try {
					const response = await getStudyStatistics();
					if (response.code === 200) {
						this.studyStats = response.data.statistics || null;
					}
				} catch (error) {
					console.error('加载学习统计失败:', error);
					// 如果是认证错误，不显示提示
					if (error.code !== 401) {
						// 设置默认值
						this.studyStats = {
							total_courses: 0,
							active_courses: 0,
							completed_courses: 0,
							total_watch_time: 0
						};
					}
				}
			},
			
			/**
			 * 加载未读公告数量
			 */
			async loadUnreadCount() {
				try {
					const response = await getUnreadAnnouncementCount();
					if (response.code === 200) {
						this.unreadCount = response.data.count || 0;
					}
				} catch (error) {
					console.error('加载未读公告数量失败:', error);
					// 如果是认证错误，不显示提示
					if (error.code !== 401) {
						this.unreadCount = 0;
					}
				}
			},
			
			/**
			 * 获取头像URL（参考课程图片处理方式）
			 */
			getAvatarUrl(avatar) {
				console.log('getAvatarUrl 输入:', avatar);

				if (!avatar || avatar === '') {
					console.log('头像为空，返回空字符串');
					return '';
				}

				let result = '';

				// 如果是完整的URL，直接返回（包括微信头像）
				if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
					result = avatar;
					console.log('完整URL，直接返回:', result);
				}
				// 如果是微信临时文件路径，直接返回
				else if (avatar.startsWith('wxfile://') || avatar.startsWith('http://tmp/')) {
					result = avatar;
					console.log('微信临时文件，直接返回:', result);
				}
				// 如果是相对路径，通过静态文件处理器访问
				else {
					// 确保路径以/开头
					const normalizedPath = avatar.startsWith('/') ? avatar : `/${avatar}`;
					// 使用静态文件处理器来访问文件，避免500错误
					result = `https://wx.yx420.cn/api/static-file.php?path=${encodeURIComponent(normalizedPath)}`;
					console.log('相对路径，通过静态文件处理器访问:', result);
				}

				return result;
			},

			/**
			 * 头像加载成功处理
			 */
			onAvatarLoad(e) {
				console.log('头像加载成功:', e);
				this.avatarLoadError = false;
			},

			/**
			 * 头像加载失败处理
			 */
			handleAvatarError(e) {
				console.log('头像加载失败:', e);
				console.log('失败的头像URL:', this.getAvatarUrl(this.userInfo.avatar));
				console.log('原始头像数据:', this.userInfo.avatar);

				// 标记头像加载失败，这会触发显示默认头像
				this.avatarLoadError = true;

				// 强制更新视图以显示默认头像
				this.$forceUpdate();

				// 可选：尝试重新加载用户信息（可能头像已更新）
				setTimeout(() => {
					console.log('尝试重新加载用户信息...');
					this.refreshUserInfoSilently();
				}, 2000);
			},

			/**
			 * 更换头像
			 */
			changeAvatar() {
				if (!this.isLoggedIn) {
					this.goToLogin();
					return;
				}

				// 显示选择方式
				uni.showActionSheet({
					itemList: ['从相册选择', '拍照'],
					success: (res) => {
						console.log('用户选择:', res.tapIndex);
						this.selectAvatarImage(res.tapIndex);
					},
					fail: () => {
						console.log('用户取消选择');
					}
				});
			},

			/**
			 * 选择头像图片
			 */
			selectAvatarImage(tapIndex) {
				const sourceType = tapIndex === 0 ? ['album'] : ['camera'];

				// 检查是否支持 chooseAvatar API
				if (uni.chooseAvatar && tapIndex === 0) {
					// 优先使用微信小程序的头像选择功能（仅相册选择时）
					uni.chooseAvatar({
						success: (res) => {
							console.log('微信头像选择成功:', res);
							this.uploadAvatar(res.avatarUrl);
						},
						fail: (error) => {
							console.error('微信头像选择失败，使用传统方式:', error);
							this.chooseImageFallback(sourceType);
						}
					});
				} else {
					// 使用传统的图片选择方式
					this.chooseImageFallback(sourceType);
				}
			},

			/**
			 * 传统图片选择方式（兜底方案）
			 */
			chooseImageFallback(sourceType) {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: sourceType,
					success: (res) => {
						console.log('选择的图片:', res.tempFilePaths[0]);
						this.uploadAvatar(res.tempFilePaths[0]);
					},
					fail: (error) => {
						console.error('图片选择失败:', error);
						showError('图片选择失败');
					}
				});
			},

			/**
			 * 上传头像（参考课程图片上传方式）
			 */
			async uploadAvatar(avatarUrl) {
				try {
					console.log('开始上传头像:', avatarUrl);
					showLoading('上传头像中...');

					// 获取访问令牌
					const token = getAccessToken();
					if (!token) {
						hideLoading();
						showError('请先登录');
						return;
					}

					// 上传头像到服务器（使用与课程图片相同的方式）
					const uploadResult = await new Promise((resolve, reject) => {
						uni.uploadFile({
							url: 'https://wx.yx420.cn/api/upload.php',
							filePath: avatarUrl,
							name: 'file', // 与课程图片上传保持一致
							formData: {
								type: 'avatar'
							},
							header: {
								'Authorization': `Bearer ${token}`,
								'Content-Type': 'multipart/form-data'
							},
							success: (res) => {
								console.log('上传响应状态:', res.statusCode);
								console.log('上传响应数据:', res.data);

								if (res.statusCode !== 200) {
									reject(new Error(`服务器错误: ${res.statusCode}`));
									return;
								}

								try {
									const data = JSON.parse(res.data);
									console.log('解析后的响应:', data);

									if (data.code === 200) {
										resolve(data);
									} else {
										reject(new Error(data.message || '上传失败'));
									}
								} catch (e) {
									console.error('响应解析失败:', e);
									console.log('原始响应:', res.data);
									reject(new Error('服务器响应格式错误'));
								}
							},
							fail: (error) => {
								console.error('上传请求失败:', error);
								let errorMessage = '上传失败';

								if (error.errMsg) {
									if (error.errMsg.includes('network')) {
										errorMessage = '网络连接失败';
									} else if (error.errMsg.includes('timeout')) {
										errorMessage = '上传超时';
									} else if (error.errMsg.includes('file')) {
										errorMessage = '文件读取失败';
									} else {
										errorMessage = error.errMsg;
									}
								}

								reject(new Error(errorMessage));
							}
						});
					});

					// 上传成功后，立即更新本地显示
					if (uploadResult.data && uploadResult.data.file_url) {
						console.log('上传成功，新头像URL:', uploadResult.data.file_url);

						// 立即更新本地用户信息
						this.userInfo.avatar = uploadResult.data.file_url;
						setUserInfo(this.userInfo);
						this.avatarLoadError = false;

						// 强制更新视图
						this.$forceUpdate();

						hideLoading();
						showSuccess('头像更新成功');

						// 后台异步刷新用户信息确保数据一致性
						this.refreshUserInfoSilently();
					} else {
						throw new Error('上传响应中缺少文件URL');
					}

				} catch (error) {
					hideLoading();
					console.error('上传头像失败:', error);

					// 根据错误类型显示不同的提示
					let errorMessage = '头像上传失败';
					if (error.message) {
						errorMessage = error.message;
					}

					showError(errorMessage);
				}
			},

			/**
			 * 静默刷新用户信息（不显示加载状态）
			 */
			async refreshUserInfoSilently() {
				try {
					const { getCurrentUser } = await import('../../api/auth.js');
					const response = await getCurrentUser();

					if (response.code === 200) {
						console.log('静默刷新用户信息成功:', response.data.user);

						// 更新用户信息
						this.userInfo = response.data.user;
						setUserInfo(this.userInfo);

						// 重置头像错误状态，让头像重新尝试加载
						this.avatarLoadError = false;

						this.$forceUpdate();
					} else {
						console.warn('静默刷新用户信息失败:', response.message);
					}
				} catch (error) {
					console.error('静默刷新用户信息失败:', error);
					// 静默失败，不影响用户体验
				}
			},
			
			/**
			 * 退出登录
			 */
			async handleLogout() {
				const confirmed = await showConfirm('确定要退出登录吗？');
				if (!confirmed) return;
				
				try {
					// 调用退出登录接口
					await logout();
				} catch (error) {
					console.error('退出登录失败:', error);
				} finally {
					// 清除本地用户信息
					clearUserInfo();
					
					// 重新检查登录状态
					this.checkLoginStatus();
					
					showSuccess('已退出登录');
					
					// 跳转到登录页
					uni.reLaunch({
						url: '/pages/login/login'
					});
				}
			},
			
			/**
			 * 跳转到登录页
			 */
			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/login'
				});
			},
			
			/**
			 * 跳转到课程列表
			 */
			goToCourses() {
				uni.switchTab({
					url: '/pages/courses/list'
				});
			},

			/**
			 * 跳转到订单中心
			 */
			goToOrders() {
				uni.navigateTo({
					url: '/pages/orders/list'
				});
			},
			
			/**
			 * 跳转到收藏页面
			 */
			goToFavorites() {
				uni.showToast({
					title: '收藏功能待开发',
					icon: 'none'
				});
			},
			
			/**
			 * 跳转到学习历史
			 */
			goToStudyHistory() {
				uni.showToast({
					title: '学习历史功能待开发',
					icon: 'none'
				});
			},
			
			/**
			 * 跳转到公告列表
			 */
			goToAnnouncements() {
				uni.switchTab({
					url: '/pages/announcements/list'
				});
			},
			
			/**
			 * 跳转到设置页面
			 */
			goToSettings() {
				uni.navigateTo({
					url: '/pages/profile/settings'
				});
			},

			/**
			 * 编辑用户名
			 */
			editUserName() {
				const currentName = this.userInfo.nickname || this.userInfo.name || '';

				uni.showModal({
					title: '修改昵称',
					editable: true,
					placeholderText: '请输入新的昵称',
					content: currentName,
					success: async (res) => {
						if (res.confirm && res.content && res.content.trim()) {
							const newName = res.content.trim();
							if (newName === currentName) {
								return; // 名字没有变化，不需要更新
							}

							try {
								uni.showLoading({
									title: '更新中...'
								});

								// 调用更新用户信息的API
								const { updateUserProfile } = await import('../../api/auth.js');
								const response = await updateUserProfile({
									nickname: newName
								});

								if (response.code === 200) {
									// 更新本地用户信息
									this.userInfo.nickname = newName;
									setUserInfo(this.userInfo);

									uni.hideLoading();
									uni.showToast({
										title: '昵称更新成功',
										icon: 'success'
									});
								} else {
									throw new Error(response.message || '更新失败');
								}
							} catch (error) {
								console.error('更新昵称失败:', error);
								uni.hideLoading();
								uni.showToast({
									title: error.message || '更新失败',
									icon: 'none'
								});
							}
						}
					}
				});
			},

			/**
			 * 复制邮箱地址
			 */
			copyEmail() {
				const email = this.userInfo.email;
				if (!email || email === '未设置邮箱') {
					uni.showToast({
						title: '暂无邮箱信息',
						icon: 'none'
					});
					return;
				}

				// 复制到剪贴板
				uni.setClipboardData({
					data: email,
					success: () => {
						uni.showToast({
							title: '邮箱已复制',
							icon: 'success'
						});
					},
					fail: () => {
						uni.showToast({
							title: '复制失败',
							icon: 'none'
						});
					}
				});
			},
			
			/**
			 * 跳转到关于我们
			 */
			goToAbout() {
				uni.showModal({
					title: '关于我们',
					content: '课程学习系统 v1.0\n基于uni-app开发的在线学习平台',
					showCancel: false
				});
			},
			
			/**
			 * 联系客服 - 直接跳转到小程序页面客服
			 */
			contactCustomerService() {
				// 显示加载提示
				uni.showLoading({
					title: '正在连接客服...'
				});

				try {
					// 直接跳转到小程序的页面客服
					// 使用微信小程序的客服会话功能
					// #ifdef MP-WEIXIN
					wx.openCustomerServiceChat({
						extInfo: {
							url: 'https://work.weixin.qq.com/kfid/kf_001' // 可以配置具体的客服ID
						},
						corpId: '', // 企业微信的corpId，如果有的话
						success: (res) => {
							console.log('客服聊天打开成功:', res);
							uni.hideLoading();
							uni.showToast({
								title: '客服连接成功',
								icon: 'success'
							});
						},
						fail: (err) => {
							console.error('客服聊天打开失败:', err);
							uni.hideLoading();

							// 检查是否是开发者工具环境
							if (err.errMsg && err.errMsg.includes('开发者工具暂时不支持')) {
								uni.showModal({
									title: '开发者工具提示',
									content: '客服功能在开发者工具中无法调试，请在真机上测试。\n\n真机测试时客服功能将正常工作。\n\n您也可以使用小程序右上角菜单中的"联系客服"。',
									showCancel: false,
									confirmText: '我知道了'
								});
							} else {
								// 其他错误情况
								uni.showModal({
									title: '客服连接失败',
									content: '无法打开客服聊天，请稍后再试或通过其他方式联系我们。\n\n您也可以在小程序右上角菜单中选择"联系客服"。',
									showCancel: false,
									confirmText: '我知道了'
								});
							}
						}
					});
					// #endif

					// #ifndef MP-WEIXIN
					// 非微信小程序环境的处理
					uni.hideLoading();
					uni.showModal({
						title: '客服功能',
						content: '客服功能仅在微信小程序中可用，请在微信小程序中使用此功能。',
						showCancel: false,
						confirmText: '我知道了'
					});
					// #endif

				} catch (error) {
					console.error('联系客服失败:', error);
					uni.hideLoading();
					uni.showModal({
						title: '连接失败',
						content: '无法连接到客服，请稍后再试。\n\n您也可以在小程序右上角菜单中选择"联系客服"。',
						showCancel: false,
						confirmText: '我知道了'
					});
				}
			},

			/**
			 * 刷新头像
			 */
			async refreshAvatar() {
				try {
					showLoading('刷新头像中...');

					// 重置头像错误状态
					this.avatarLoadError = false;

					// 重新获取用户信息
					await this.refreshUserInfo();

					hideLoading();
					showSuccess('头像已刷新');

				} catch (error) {
					hideLoading();
					console.error('刷新头像失败:', error);
					showError('刷新头像失败');
				}
			},
			
			/**
			 * 获取状态文本
			 */
			getStatusText(status) {
				const statusMap = {
					active: '正常',
					inactive: '未激活',
					banned: '已禁用'
				};
				return statusMap[status] || '未知';
			},
			
			/**
			 * 获取状态颜色
			 */
			getStatusColor(status) {
				const colorMap = {
					active: 'success',
					inactive: 'warning',
					banned: 'error'
				};
				return colorMap[status] || 'default';
			},


		}
	};
</script>

<style lang="scss" scoped>
.profile-container {
	background: #f8f9fa;
	min-height: 100vh;
}

.user-header {
	background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
	padding: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.user-info {
		display: flex;
		align-items: center;
		flex: 1;

		.user-avatar {
			width: 60px;
			height: 60px;
			border-radius: 30px;
			margin-right: 15px;
			border: 3px solid rgba(255, 255, 255, 0.3);
			display: flex;
			align-items: center;
			justify-content: center;
			overflow: hidden;
			background: rgba(255, 255, 255, 0.1);

			.avatar-image {
				width: 100%;
				height: 100%;
				border-radius: 50%;
			}

			.default-avatar {
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 50%;
			}
		}

		.user-details {
			flex: 1;

			.user-name {
				display: block;
				color: #fff;
				font-size: 20px;
				font-weight: 600;
				margin-bottom: 5px;
			}

			.user-email {
				display: block;
				color: rgba(255, 255, 255, 0.8);
				font-size: 14px;
				margin-bottom: 8px;
			}

			.clickable {
				cursor: pointer;
				transition: all 0.2s ease;
				padding: 4px 8px;
				border-radius: 4px;
				margin: -4px -8px;

				&:hover {
					background: rgba(255, 255, 255, 0.1);
					transform: scale(1.02);
				}

				&:active {
					background: rgba(255, 255, 255, 0.2);
					transform: scale(0.98);
				}
			}

			.user-status {
				display: flex;
				align-items: center;
				margin-bottom: 8px;
			}

			.user-tips {
				.tips-text {
					color: rgba(255, 255, 255, 0.6);
					font-size: 12px;
					font-style: italic;
				}
			}
		}
	}

	.user-actions {
		padding: 8px;
	}
}

.login-prompt {
	background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
	padding: 60px 20px;
	text-align: center;

	.prompt-content {
		.prompt-text {
			display: block;
			color: rgba(255, 255, 255, 0.8);
			font-size: 16px;
			margin: 20px 0 30px;
		}

		.login-btn {
			background: rgba(255, 255, 255, 0.2);
			border: 1px solid rgba(255, 255, 255, 0.3);
			border-radius: 25px;
			color: #fff;
			font-size: 16px;
			padding: 12px 40px;
		}
	}
}

.stats-section {
	background: #fff;
	margin: 15px;
	border-radius: 12px;
	padding: 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

	.section-title {
		font-size: 18px;
		font-weight: 500;
		color: #333;
		margin-bottom: 20px;
	}

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 15px;

		.stat-item {
			text-align: center;

			.stat-number {
				display: block;
				font-size: 20px;
				font-weight: bold;
				color: #007bff;
				margin-bottom: 5px;
			}

			.stat-label {
				display: block;
				font-size: 12px;
				color: #666;
			}
		}
	}
}

.menu-section {
	margin: 15px;

	.menu-group {
		background: #fff;
		border-radius: 12px;
		margin-bottom: 15px;
		overflow: hidden;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

		.menu-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 18px 20px;
			border-bottom: 1px solid #f8f9fa;

			&:last-child {
				border-bottom: none;
			}

			.menu-left {
				display: flex;
				align-items: center;
				gap: 15px;

				.menu-icon {
					font-size: 20px;
					width: 20px;
					height: 20px;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.menu-text {
					font-size: 16px;
					color: #333;
				}
			}
		}
	}
}

.logout-section {
	padding: 20px 15px 40px;

	.logout-btn {
		width: 100%;
		height: 50px;
		background: #dc3545;
		color: #fff;
		border: none;
		border-radius: 25px;
		font-size: 16px;
		font-weight: 500;
	}
}
</style>
