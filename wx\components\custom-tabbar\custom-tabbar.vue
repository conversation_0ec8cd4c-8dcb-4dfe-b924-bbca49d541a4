<template>
	<view class="custom-tabbar">
		<view 
			v-for="(item, index) in tabList" 
			:key="index"
			class="tabbar-item"
			:class="{ active: currentIndex === index }"
			@click="switchTab(item, index)"
		>
			<uni-icons 
				:type="item.icon" 
				:size="24" 
				:color="currentIndex === index ? selectedColor : normalColor"
			></uni-icons>
			<text class="tabbar-text" :style="{ color: currentIndex === index ? selectedColor : normalColor }">
				{{ item.text }}
			</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomTabbar',
	props: {
		current: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			currentIndex: 0,
			normalColor: '#7A7E83',
			selectedColor: '#007bff',
			tabList: [
				{
					pagePath: '/pages/index/index',
					text: '首页',
					icon: 'home'
				},
				{
					pagePath: '/pages/courses/list',
					text: '课程',
					icon: 'videocam'
				},
				{
					pagePath: '/pages/profile/profile',
					text: '我的',
					icon: 'person'
				}
			]
		}
	},
	watch: {
		current(newVal) {
			this.currentIndex = newVal;
		}
	},
	mounted() {
		this.currentIndex = this.current;
	},
	methods: {
		switchTab(item, index) {
			if (this.currentIndex === index) {
				return;
			}
			
			this.currentIndex = index;
			
			uni.switchTab({
				url: item.pagePath,
				fail: (err) => {
					console.error('切换页面失败:', err);
				}
			});
		}
	}
}
</script>

<style scoped>
.custom-tabbar {
	display: flex;
	background-color: #ffffff;
	border-top: 1px solid #e5e5e5;
	height: 50px;
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1000;
}

.tabbar-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 4px 0;
}

.tabbar-text {
	font-size: 10px;
	margin-top: 2px;
	line-height: 1;
}

.tabbar-item.active .tabbar-text {
	font-weight: 500;
}
</style>
