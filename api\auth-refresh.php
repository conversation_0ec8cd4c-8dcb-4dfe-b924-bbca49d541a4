<?php
/**
 * JWT令牌刷新API
 * 使用refresh_token获取新的access_token
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

$auth = new AuthAPI();

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    $auth->jsonResponse(400, '无效的请求数据');
}

$refresh_token = trim($input['refresh_token'] ?? '');

// 验证必填字段
if (empty($refresh_token)) {
    $auth->jsonResponse(400, '缺少刷新令牌');
}

try {
    // 检查令牌是否被撤销
    if ($auth->isTokenRevoked($refresh_token)) {
        $auth->jsonResponse(401, '刷新令牌已失效，请重新登录');
    }
    
    // 验证刷新令牌
    $payload = $auth->verifyJWT($refresh_token);
    
    if (!$payload || $payload['type'] !== 'refresh') {
        $auth->jsonResponse(401, '无效的刷新令牌');
    }
    
    $user_id = $payload['user_id'];
    
    // 检查用户是否存在且状态正常
    $user = $auth->getUserById($user_id);
    if (!$user) {
        $auth->jsonResponse(401, '用户不存在或已被禁用');
    }
    
    if ($user['status'] === 'banned') {
        $auth->jsonResponse(403, '账户已被禁用，请联系管理员');
    }
    
    if ($user['status'] === 'inactive') {
        $auth->jsonResponse(403, '账户未激活，请先激活账户');
    }
    
    // 生成新的访问令牌
    $new_access_token = $auth->generateJWT($user_id, 'access');
    
    // 保存新令牌
    $device_info = $input['device_info'] ?? null;
    $auth->saveToken($user_id, $new_access_token, 'access', $device_info);
    
    // 返回新的访问令牌
    $auth->jsonResponse(200, '令牌刷新成功', [
        'access_token' => $new_access_token,
        'token_type' => 'Bearer',
        'expires_in' => 7200,
        'user' => [
            'id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'username' => $user['username'],
            'nickname' => $user['nickname'],
            'avatar' => $user['avatar'],
            'login_type' => $user['login_type']
        ]
    ]);
    
} catch (Exception $e) {
    error_log('令牌刷新错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}
