<template>
	<view class="index-container">

		<!-- 轮播图区域 -->
		<view class="banner-section" v-if="banners.length > 0">
			<swiper
				class="banner-swiper"
				:indicator-dots="true"
				:autoplay="true"
				:interval="3000"
				:duration="500"
				:circular="true"
				indicator-color="rgba(255, 255, 255, 0.5)"
				indicator-active-color="#007bff"
			>
				<swiper-item
					v-for="(banner, index) in banners"
					:key="banner.id"
					@click="handleBannerClick(banner)"
				>
					<view class="banner-item">
						<image
							class="banner-image"
							:src="banner.image_url"
							mode="aspectFill"
							:lazy-load="true"
							:data-index="index"
							@error="handleImageError"
						></image>
						<view class="banner-overlay">
							<text class="banner-title">{{ banner.title }}</text>
						</view>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 精品推荐课程 -->
		<view class="featured-courses" v-if="featuredCourses.length > 0">
			<view class="section-header">
				<view class="section-title">
					<simple-icon type="star-filled" color="#ffc107" size="20"></simple-icon>
					<text>精品推荐</text>
				</view>
				<text class="section-subtitle">精选优质课程，助您快速提升</text>
				<text class="more-link" @click="goToCourses">更多</text>
			</view>

			<view class="course-grid" :class="{ 'single-course': featuredCourses.length === 1 }">
				<view
					class="course-card"
					v-for="course in featuredCourses"
					:key="course.id"
					@click="goToCourseDetail(course.id)"
				>
					<view class="course-image">
						<image
							class="course-thumbnail"
							:src="course.cover_image || course.thumbnail || '/static/default-course.png'"
							mode="aspectFill"
						></image>
						<view class="course-overlay">
							<view class="play-icon">
								<simple-icon type="play-filled" color="#007bff" size="16"></simple-icon>
							</view>
						</view>
					</view>

					<view class="course-info">
						<text class="course-title">{{ course.title }}</text>

						<view class="course-meta">
							<view class="course-meta-item" v-if="course.teacher_name">
								<simple-icon type="person-filled" color="#007bff" size="12"></simple-icon>
								<text class="course-teacher">{{ course.teacher_name }}</text>
							</view>

							<view class="course-meta-item">
								<simple-icon type="play-circle" color="#6c757d" size="12"></simple-icon>
								<text>{{ course.lesson_count || 0 }}课时</text>
								<text v-if="course.view_count > 0" class="mx-1">•</text>
								<simple-icon v-if="course.view_count > 0" type="eye" color="#6c757d" size="12"></simple-icon>
								<text v-if="course.view_count > 0">{{ course.view_count }}次观看</text>
							</view>
						</view>

						<view
							class="course-bottom"
							:class="{ 'purchased-layout': course.purchase_status === 'purchased' }"
						>
							<view class="course-price">
								<!-- 根据购买状态显示不同内容 -->
								<template v-if="course.purchase_status === 'purchased'">
									<!-- 已购买课程：不显示任何价格信息，留空 -->
									<view class="purchased-placeholder"></view>
								</template>
								<template v-else>
									<!-- 免费课程和未购买课程：显示状态标签 -->
									<uni-tag
										:text="course.status_label || (course.is_free ? '免费' : '¥' + parseFloat(course.price || 0).toFixed(2))"
										:type="course.status_color || (course.is_free ? 'success' : 'warning')"
										size="small"
									></uni-tag>
									<text v-if="course.original_price && course.original_price > course.price"
										  class="price-original">¥{{ parseFloat(course.original_price).toFixed(2) }}</text>
								</template>
							</view>
							<view
								class="course-btn"
								:class="{
									'btn-primary': course.purchase_status === 'free' || course.purchase_status === 'purchased',
									'btn-warning': course.purchase_status === 'not_purchased'
								}"
								@click.stop="handleCourseAction(course)"
							>
								<text>{{ course.action_text || (course.is_purchased ? '开始学习' : (course.is_free ? '免费获取' : '立即购买')) }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 公告区域 -->
		<view class="announcements-section" v-if="isLoggedIn && latestAnnouncements.length > 0">
			<view class="section-header">
				<view class="section-title">
					<simple-icon type="sound-filled" color="#007bff" size="20"></simple-icon>
					<text>最新公告</text>
				</view>
				<text class="more-link" @click="goToAnnouncements">更多</text>
			</view>

			<view class="announcement-list">
				<view
					class="announcement-item"
					v-for="announcement in latestAnnouncements"
					:key="announcement.id"
					@click="goToAnnouncementDetail(announcement.id)"
				>
					<view class="announcement-header">
						<view class="announcement-type" :class="'type-' + announcement.type">
							{{ getAnnouncementTypeText(announcement.type) }}
						</view>
						<view class="announcement-priority" v-if="announcement.priority > 0">
							<simple-icon
								:type="announcement.priority === 2 ? 'fire' : 'star'"
								:color="announcement.priority === 2 ? '#dc3545' : '#ffc107'"
								size="14"
							></simple-icon>
						</view>
						<view class="announcement-pin" v-if="announcement.is_pinned">
							<simple-icon type="paperclip" color="#28a745" size="14"></simple-icon>
						</view>
					</view>

					<text class="announcement-title">{{ announcement.title }}</text>

					<view class="announcement-meta">
						<text class="announcement-time">{{ formatDate(announcement.publish_time) }}</text>
						<text class="announcement-category" v-if="announcement.category_name">
							{{ announcement.category_name }}
						</text>
						<text class="announcement-views" v-if="announcement.view_count > 0">
							{{ announcement.view_count }}次查看
						</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
		</view>
	</view>
</template>

<script>
	import { isLoggedIn, getUserInfo, formatDate } from '../../utils/storage.js';
	import { authMixin } from '../../utils/auth.js';
	import config from '../../utils/config.js';
	import { getLatestAnnouncements } from '../../api/announcement.js';
	import { getRecommendedCourses, getPopularCourses, getLatestCourses, getStudyStatistics } from '../../api/course.js';

	import SimpleIcon from '../../components/simple-icon/simple-icon.vue';

	export default {
		mixins: [authMixin],
		components: {
			SimpleIcon
		},

		data() {
			return {
				config: config,
				loading: false,
				loadingText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多数据了'
				},
				isLoggedIn: false,
				userInfo: {},
				studyStats: null,
				siteSettings: {
					site_name: 'Knowledge Learning Platform',
					site_description: '专业的在线学习平台'
				},

				latestAnnouncements: [],
				featuredCourses: [],
				recommendedCourses: [],
				popularCourses: [],
				latestCourses: [],
				banners: []
			};
		},

		onLoad() {
			this.initPage();
		},

		onShow() {
			this.checkLoginStatus();
			this.loadPageData();
		},

		onPullDownRefresh() {
			this.loadPageData().finally(() => {
				uni.stopPullDownRefresh();
			});
		},

		methods: {
			/**
			 * 初始化页面
			 */
			initPage() {
				this.checkLoginStatus();
				this.loadPageData();
			},

			/**
			 * 检查登录状态
			 */
			checkLoginStatus() {
				this.isLoggedIn = isLoggedIn();
				if (this.isLoggedIn) {
					this.userInfo = getUserInfo() || {};
				}
			},

			/**
			 * 加载页面数据
			 */
			async loadPageData() {
				this.loading = true;

				try {
					// 并行加载数据
					const promises = [
						this.loadSiteSettings(),
						this.loadBanners(),

						this.loadFeaturedCourses(),
						this.loadRecommendedCourses(),
						this.loadPopularCourses(),
						this.loadLatestCourses()
					];

					// 如果已登录，加载公告数据
					if (this.isLoggedIn) {
						promises.push(this.loadLatestAnnouncements());
					}

					// 如果已登录，加载学习统计
					if (this.isLoggedIn) {
						promises.push(this.loadStudyStatistics());
					}

					await Promise.allSettled(promises);
				} catch (error) {
					console.error('加载页面数据失败:', error);
				} finally {
					this.loading = false;
				}
			},

			/**
			 * 加载网站设置
			 */
			async loadSiteSettings() {
				try {
					// 这里可以调用API获取网站设置，暂时使用默认值
					this.siteSettings = {
						site_name: 'Knowledge Learning Platform',
						site_description: '专业的在线学习平台'
					};
				} catch (error) {
					console.error('加载网站设置失败:', error);
				}
			},



			/**
			 * 加载最新公告
			 */
			async loadLatestAnnouncements() {
				try {
					const response = await getLatestAnnouncements(5);
					if (response.code === 200) {
						this.latestAnnouncements = response.data.list || [];
					}
				} catch (error) {
					console.error('加载最新公告失败:', error);
					this.handleApiError(error, '最新公告');
					// 设置默认空公告列表
					this.latestAnnouncements = [];
				}
			},

			/**
			 * 加载精品推荐课程
			 */
			async loadFeaturedCourses() {
				try {
					const response = await getRecommendedCourses(6);
					if (response.code === 200) {
						// 精品推荐课程数据结构
						this.featuredCourses = (response.data.list || []).map(course => ({
							...course,
							cover_image: course.thumbnail,
							teacher_name: course.teacher_name || '专业讲师',
							lesson_count: course.lesson_count || 0,
							view_count: course.view_count || 0,
							price: course.price || 0,
							original_price: course.original_price || null,
							is_free: course.is_free !== undefined ? Boolean(course.is_free) : true,
							// 使用新的购买状态字段
							is_purchased: course.is_purchased || false,
							purchase_status: course.purchase_status || (course.is_free ? 'free' : 'not_purchased'),
							status_label: course.status_label,
							status_color: course.status_color,
							action_text: course.action_text,
							is_recommend: 1
						}));
					}
				} catch (error) {
					console.error('加载精品推荐课程失败:', error);
					this.handleApiError(error, '精品推荐课程');
				}
			},

			/**
			 * 加载推荐课程
			 */
			async loadRecommendedCourses() {
				try {
					const response = await getRecommendedCourses(6);
					if (response.code === 200) {
						this.recommendedCourses = (response.data.list || []).map(course => ({
							...course,
							purchase_status: course.purchase_status || (course.is_free ? 'free' : 'not_purchased'),
							status_label: course.status_label,
							status_color: course.status_color,
							action_text: course.action_text
						}));
					}
				} catch (error) {
					console.error('加载推荐课程失败:', error);
					this.handleApiError(error, '推荐课程');
				}
			},

			/**
			 * 加载热门课程
			 */
			async loadPopularCourses() {
				try {
					const response = await getPopularCourses(6);
					if (response.code === 200) {
						this.popularCourses = (response.data.list || []).map(course => ({
							...course,
							purchase_status: course.purchase_status || (course.is_free ? 'free' : 'not_purchased'),
							status_label: course.status_label,
							status_color: course.status_color,
							action_text: course.action_text
						}));
					}
				} catch (error) {
					console.error('加载热门课程失败:', error);
					this.handleApiError(error, '热门课程');
				}
			},

			/**
			 * 加载最新课程
			 */
			async loadLatestCourses() {
				try {
					const response = await getLatestCourses(6);
					if (response.code === 200) {
						this.latestCourses = (response.data.list || []).map(course => ({
							...course,
							purchase_status: course.purchase_status || (course.is_free ? 'free' : 'not_purchased'),
							status_label: course.status_label,
							status_color: course.status_color,
							action_text: course.action_text
						}));
					}
				} catch (error) {
					console.error('加载最新课程失败:', error);
					this.handleApiError(error, '最新课程');
				}
			},

			/**
			 * 加载学习统计
			 */
			async loadStudyStatistics() {
				try {
					const response = await getStudyStatistics();
					if (response.code === 200) {
						this.studyStats = response.data.statistics || null;
					}
				} catch (error) {
					console.error('加载学习统计失败:', error);
					this.handleApiError(error, '学习统计');
				}
			},

			/**
			 * 加载轮播图 - 使用实时API数据
			 */
			loadBanners() {
				return new Promise((resolve) => {
					uni.request({
						url: this.config.API_BASE_URL + 'bannerList_v2.php',
						method: 'GET',
						data: {
							status: 'active',
							limit: 10
						},
						success: (response) => {
							console.log('轮播图API响应:', response);

							if (response.statusCode === 200 && response.data) {
								const apiData = response.data;
								console.log('轮播图API数据:', apiData);

								if (apiData.code === 200) {
									this.banners = apiData.data.list || [];
									console.log('轮播图列表:', this.banners);
								} else {
									console.warn('轮播图API返回错误:', apiData.message || '未知错误');
									this.banners = [];
								}
							} else {
								console.error('轮播图API请求失败:', response.statusCode);
								this.banners = [];
							}
							resolve();
						},
						fail: (error) => {
							console.error('轮播图API请求失败:', error);
							this.banners = [];
							resolve(); // 即使失败也resolve，不影响其他数据加载
						}
					});
				});
			},

			/**
			 * 处理轮播图点击
			 */
			handleBannerClick(banner) {
				// 增加点击次数（可选）
				this.incrementBannerViewCount(banner.id);

				// 根据链接类型处理跳转
				switch (banner.link_type) {
					case 'course':
						// 跳转到课程详情页
						if (banner.link_value) {
							uni.navigateTo({
								url: `/pages/courses/detail?id=${banner.link_value}`
							});
						}
						break;
					case 'announcement':
						// 跳转到公告详情页
						if (banner.link_value) {
							uni.navigateTo({
								url: `/pages/announcements/detail?id=${banner.link_value}`
							});
						}
						break;
					case 'external':
						// 处理外部链接
						if (banner.link_value) {
							// 在小程序中打开外部链接需要特殊处理
							uni.showModal({
								title: '提示',
								content: '即将跳转到外部链接，是否继续？',
								success: (res) => {
									if (res.confirm) {
										// 复制链接到剪贴板
										uni.setClipboardData({
											data: banner.link_value,
											success: () => {
												uni.showToast({
													title: '链接已复制到剪贴板',
													icon: 'success'
												});
											}
										});
									}
								}
							});
						}
						break;
					case 'none':
					default:
						// 无链接，不做任何操作
						break;
				}
			},

			/**
			 * 增加轮播图点击次数
			 */
			async incrementBannerViewCount(bannerId) {
				try {
					console.log('轮播图点击:', bannerId);
					// 这里可以调用API增加点击次数，暂时省略
					// await uni.request({
					//     url: this.config.API_BASE_URL + '/bannerClick.php',
					//     method: 'POST',
					//     data: { banner_id: bannerId }
					// });
				} catch (error) {
					console.error('更新轮播图点击次数失败:', error);
				}
			},

			/**
			 * 处理图片加载错误
			 */
			handleImageError(e) {
				console.error('图片加载失败:', e);
				// 在小程序中，图片加载失败时可以隐藏该轮播图项
				// 或者显示一个占位符
				const bannerIndex = e.currentTarget.dataset.index;
				if (bannerIndex !== undefined) {
					// 从轮播图列表中移除失败的项
					this.banners = this.banners.filter((_, index) => index != bannerIndex);
				}
			},

			/**
			 * 跳转到登录页
			 */
			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/login'
				});
			},

			/**
			 * 跳转到课程列表
			 */
			goToCourses() {
				uni.switchTab({
					url: '/pages/courses/list'
				});
			},

			/**
			 * 跳转到公告列表
			 */
			goToAnnouncements() {
				uni.navigateTo({
					url: '/pages/announcements/list'
				});
			},

			/**
			 * 跳转到公告详情
			 */
			goToAnnouncementDetail(id) {
				uni.navigateTo({
					url: `/pages/announcements/detail?id=${id}`
				});
			},

			/**
			 * 跳转到个人中心
			 */
			goToProfile() {
				uni.switchTab({
					url: '/pages/profile/profile'
				});
			},

			/**
			 * 跳转到设置页面
			 */
			goToSettings() {
				uni.navigateTo({
					url: '/pages/profile/settings'
				});
			},

			/**
			 * 跳转到通知页面
			 */
			goToNotifications() {
				uni.navigateTo({
					url: '/pages/announcements/list'
				});
			},

			/**
			 * 获取公告类型文本
			 */
			getAnnouncementTypeText(type) {
				const typeMap = {
					'notice': '通知',
					'urgent': '紧急',
					'system': '系统',
					'activity': '活动'
				};
				return typeMap[type] || '通知';
			},

			/**
			 * 跳转到课程详情
			 */
			goToCourseDetail(id) {
				uni.navigateTo({
					url: `/pages/courses/detail?id=${id}`
				});
			},

			// 公告功能已移除

			/**
			 * 格式化日期
			 */
			formatDate(date) {
				return formatDate(date, 'MM-DD HH:mm');
			},



			/**
			 * 处理课程操作
			 */
			handleCourseAction(course) {
				// 根据购买状态处理不同的操作
				switch (course.purchase_status) {
					case 'free':
						// 免费课程
						if (!this.isLoggedIn) {
							uni.showModal({
								title: '需要登录',
								content: '请先登录后学习免费课程',
								showCancel: true,
								cancelText: '取消',
								confirmText: '去登录',
								success: (res) => {
									if (res.confirm) {
										this.goToLogin();
									}
								}
							});
							return;
						}
						this.goToCourseDetail(course.id);
						break;

					case 'purchased':
						// 已购买课程
						this.goToCourseDetail(course.id);
						break;

					case 'not_purchased':
						// 未购买课程，跳转到详情页查看购买选项
						this.goToCourseDetail(course.id);
						break;

					default:
						// 兼容旧的逻辑
						if (!this.isLoggedIn) {
							this.goToLogin();
							return;
						}
						this.goToCourseDetail(course.id);
						break;
				}
			},

			/**
			 * 处理图片加载错误
			 */
			handleImageError(e) {
				console.error('图片加载失败:', e);
				// 可以设置默认图片
			},

			/**
			 * 统一的API错误处理
			 * @param {Object} error 错误对象
			 * @param {String} module 模块名称
			 */
			handleApiError(error, module) {
				let errorMessage = '加载失败，请稍后重试';
				
				if (error.code === 503) {
					errorMessage = '服务器暂时不可用，请稍后重试';
				} else if (error.code === 500) {
					errorMessage = '服务器内部错误，请联系管理员';
				} else if (error.message) {
					errorMessage = error.message;
				}
				
				// 在开发环境显示详细错误
				if (process.env.NODE_ENV === 'development') {
					console.error(`${module}错误详情:`, error);
					errorMessage += `\n错误代码: ${error.code}`;
				}
				
				// 显示错误提示
				uni.showToast({
					title: errorMessage,
					icon: 'error',
					duration: 3000
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
.home-container {
	background: #f8f9fa;
	min-height: 100vh;
}

.header-bar {
	background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
	padding: 20px;
	text-align: center;

	.header-content {
		.header-title {
			display: block;
			color: #fff;
			font-size: 20px;
			font-weight: bold;
			margin-bottom: 5px;
		}

		.header-subtitle {
			display: block;
			color: rgba(255, 255, 255, 0.8);
			font-size: 14px;
		}
	}
}

.welcome-section {
	margin: 0;
	position: relative;

	.welcome-banner {
		height: 220px;
		background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		text-align: center;
		color: white;
		padding: 40px 20px;
		position: relative;
		overflow: hidden;

		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
			opacity: 0.3;
		}

		.welcome-content {
			position: relative;
			z-index: 1;

			.welcome-title {
				display: block;
				font-size: 22px;
				font-weight: 600;
				margin-bottom: 12px;
				text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
			}

			.welcome-description {
				display: block;
				font-size: 15px;
				opacity: 0.95;
				margin-bottom: 25px;
				line-height: 1.4;
				text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
			}

			.welcome-btn {
				background: rgba(255, 255, 255, 0.95);
				color: #007bff;
				padding: 12px 24px;
				border-radius: 25px;
				font-weight: 600;
				font-size: 14px;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
				transition: all 0.3s ease;

				&:active {
					transform: translateY(1px);
					box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
				}
			}
		}
	}
}

/* 轮播图样式 */
.banner-section {
	margin: 15px;
	border-radius: 12px;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

	.banner-swiper {
		height: 240px;
		border-radius: 12px;

		.banner-item {
			position: relative;
			width: 100%;
			height: 100%;

			.banner-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.banner-overlay {
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
				padding: 20px 15px 15px;
				color: white;

				.banner-title {
					display: block;
					font-size: 16px;
					font-weight: 600;
					text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
					line-height: 1.3;
				}
			}
		}
	}
}

.user-header {
	background: #fff;
	margin: 15px;
	border-radius: 12px;
	padding: 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	display: flex;
	justify-content: space-between;
	align-items: center;

	.user-info {
		display: flex;
		align-items: center;

		.user-avatar {
			width: 50px;
			height: 50px;
			border-radius: 25px;
			margin-right: 15px;
			border: 2px solid #007bff;
		}

		.user-details {
			.user-name {
				display: block;
				color: #333;
				font-size: 18px;
				font-weight: 500;
				margin-bottom: 4px;
			}

			.user-subtitle {
				display: block;
				color: #666;
				font-size: 14px;
			}
		}
	}

	.user-actions {
		padding: 8px;

		.uni-icons {
			font-size: 24px;
			color: #007bff;
		}
	}
}

.login-prompt {
	background: #fff;
	margin: 15px;
	border-radius: 12px;
	padding: 40px 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	text-align: center;

	.prompt-content {
		.uni-icons {
			font-size: 40px;
			color: #007bff;
		}

		.prompt-text {
			display: block;
			color: #666;
			font-size: 16px;
			margin: 15px 0 25px;
		}

		.login-btn {
			background: #007bff;
			border: none;
			border-radius: 20px;
			color: #fff;
			font-size: 16px;
			padding: 10px 30px;
		}
	}
}

.study-stats {
	background: #fff;
	margin: 15px;
	border-radius: 12px;
	padding: 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

	.stats-title {
		font-size: 18px;
		font-weight: 500;
		color: #333;
		margin-bottom: 15px;
	}

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 15px;

		.stat-item {
			text-align: center;

			.stat-number {
				display: block;
				font-size: 24px;
				font-weight: bold;
				color: #007bff;
				margin-bottom: 5px;
			}

			.stat-label {
				display: block;
				font-size: 12px;
				color: #666;
			}
		}
	}
}

.quick-actions {
	background: #fff;
	margin: 15px;
	border-radius: 12px;
	padding: 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

	.section-title {
		font-size: 18px;
		font-weight: 500;
		color: #333;
		margin-bottom: 15px;
	}

	.action-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 20px;

		.action-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 15px 10px;
			border-radius: 8px;
			background: #f8f9fa;

			.action-icon {
				font-size: 24px;
				margin-bottom: 8px;

				&.primary { color: #007bff; }
				&.success { color: #28a745; }
				&.warning { color: #ffc107; }
				&.muted { color: #6c757d; }
			}

			.action-text {
				font-size: 12px;
				color: #666;
			}
		}
	}
}

.featured-courses {
	background: #fff;
	margin: 15px;
	border-radius: 12px;
	padding: 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

	.section-header {
		margin-bottom: 20px;
		text-align: center;
		position: relative;

		.section-title {
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 8px;
			font-size: 18px;
			font-weight: 600;
			color: #333;
			margin-bottom: 5px;
		}

		.section-subtitle {
			font-size: 14px;
			color: #6c757d;
			margin-bottom: 10px;
			text-align: center;
		}

		.more-link {
			font-size: 14px;
			color: #007bff;
			position: absolute;
			top: 0;
			right: 0;
		}
	}

	.course-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 15px;

		/* 当只有一个课程时居中显示 */
		&.single-course {
			grid-template-columns: 1fr;
			max-width: 200px;
			margin: 0 auto;
		}

		.course-card {
			background: #fff;
			border-radius: 12px;
			overflow: hidden;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
			transition: all 0.3s ease;
			display: flex;
			flex-direction: column;

			&:active {
				transform: scale(0.98);
			}

			.course-image {
				position: relative;
				height: 100px;
				overflow: hidden;

				.course-thumbnail {
					width: 100%;
					height: 100%;
				}

				.course-overlay {
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background: rgba(0, 0, 0, 0.1);
					display: flex;
					align-items: center;
					justify-content: center;
					opacity: 0;
					transition: opacity 0.3s ease;

					.play-icon {
						width: 32px;
						height: 32px;
						background: rgba(255, 255, 255, 0.9);
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}

				&:active .course-overlay {
					opacity: 1;
				}
			}

			.course-info {
				padding: 15px;
				flex: 1;
				display: flex;
				flex-direction: column;

				.course-title {
					font-size: 14px;
					font-weight: 600;
					color: #333;
					margin-bottom: 10px;
					line-height: 1.3;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
					overflow: hidden;
					min-height: 36px;
				}

				.course-meta {
					display: flex;
					flex-direction: column;
					gap: 5px;
					margin-bottom: 15px;

					.course-meta-item {
						display: flex;
						align-items: center;
						gap: 4px;
						font-size: 12px;
						color: #6c757d;

						.course-teacher {
							color: #007bff;
							font-weight: 500;
						}

						.mx-1 {
							margin: 0 4px;
						}
					}
				}

				.course-bottom {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding-top: 12px;
					border-top: 1px solid #f1f3f4;
					margin-top: auto;
					flex-wrap: nowrap;
					min-height: 40px;

					/* 已购买课程的布局 */
					&.purchased-layout {
						justify-content: center;

						.course-price {
							display: none;
						}

						.course-btn {
							margin-left: 0;
							margin-right: 0;
						}
					}

					.course-price {
						flex: 1;
						min-width: 0;

						.course-free {
							background: #28a745;
							color: white;
							padding: 4px 8px;
							border-radius: 10px;
							font-size: 12px;
							font-weight: 600;
							display: inline-block;
						}

						.purchased-placeholder {
							/* 已购买课程的占位符，保持布局平衡 */
							height: 20px;
						}

						.price-current {
							font-weight: 600;
							color: #dc3545;
							font-size: 14px;
						}

						.price-original {
							text-decoration: line-through;
							color: #6c757d;
							font-size: 12px;
							margin-left: 5px;
						}
					}

					.course-btn {
						background: #007bff;
						color: white;
						padding: 8px 16px;
						border-radius: 15px;
						font-size: 12px;
						font-weight: 600;
						white-space: nowrap;
						flex-shrink: 0;
						margin-left: 8px;
						min-width: 80px;
						text-align: center;
						display: flex;
						align-items: center;
						justify-content: center;
						line-height: 1;

						text {
							text-align: center;
							width: 100%;
						}
					}
				}
			}
		}
	}
}

/* 响应式优化 */
@media screen and (max-width: 320px) {
	.welcome-section {
		.welcome-banner {
			height: 180px;
			padding: 30px 15px;

			.welcome-content {
				.welcome-title {
					font-size: 18px;
				}

				.welcome-description {
					font-size: 13px;
				}

				.welcome-btn {
					padding: 10px 20px;
					font-size: 13px;
				}
			}
		}
	}

	.banner-section {
		margin: 10px;

		.banner-swiper {
			height: 200px;

			.banner-item {
				.banner-overlay {
					padding: 15px 10px 10px;

					.banner-title {
						font-size: 14px;
					}
				}
			}
		}
	}

	.announcements-section {
		margin: 10px;
		padding: 15px;

		.section-header {
			.section-title {
				font-size: 16px;
			}

			.more-link {
				font-size: 12px;
				padding: 4px 8px;
			}
		}

		.announcement-list {
			.announcement-item {
				padding: 12px 0;

				.announcement-header {
					gap: 6px;

					.announcement-type {
						font-size: 10px;
						padding: 3px 6px;
					}
				}

				.announcement-title {
					font-size: 14px;
					min-height: 42px;
				}

				.announcement-meta {
					gap: 8px;
					font-size: 11px;

					.announcement-time,
					.announcement-category {
						padding: 2px 6px;
					}
				}
			}
		}
	}

	.featured-courses {
		.course-grid {
			grid-template-columns: 1fr;
			gap: 10px;

			.course-card {
				.course-info {
					padding: 12px;

					.course-bottom {
						flex-direction: column;
						align-items: stretch;
						gap: 8px;

						.course-btn {
							margin-left: 0;
							width: 100%;
							min-width: auto;
							padding: 10px 16px;
						}
					}
				}
			}
		}
	}
}

/* 中等屏幕优化 */
@media screen and (max-width: 480px) {
	.welcome-section {
		.welcome-banner {
			height: 200px;
		}
	}

	.banner-section {
		margin: 12px;

		.banner-swiper {
			height: 220px;
		}
	}

	.announcements-section {
		margin: 12px;

		.announcement-list {
			.announcement-item {
				.announcement-meta {
					flex-wrap: wrap;
					gap: 10px;
				}
			}
		}
	}
}

/* 确保按钮在所有情况下都保持正确样式 */
.course-btn {
	display: inline-flex !important;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;

	text {
		white-space: nowrap;
		text-align: center;
		line-height: 1;
	}

	/* 按钮状态样式 */
	&.btn-primary {
		background: #007bff !important;
		color: white !important;
	}

	&.btn-warning {
		background: #ffc107 !important;
		color: #212529 !important;
	}
}

.recommended-courses {
	background: #fff;
	margin: 15px;
	border-radius: 12px;
	padding: 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

	.section-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 18px;
		font-weight: 500;
		color: #333;
		margin-bottom: 15px;

		.more-link {
			font-size: 14px;
			color: #007bff;
		}
	}
}

.announcements-section {
	background: #fff;
	margin: 15px;
	border-radius: 8px;
	padding: 12px;
	box-shadow: 0 1px 6px rgba(0, 123, 255, 0.06);
	border: 1px solid rgba(0, 123, 255, 0.08);

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 12px;
		padding-bottom: 8px;
		border-bottom: 1px solid #f8f9fa;

		.section-title {
			display: flex;
			align-items: center;
			gap: 6px;
			font-size: 15px;
			font-weight: 600;
			color: #333;

			text {
				color: #333;
			}
		}

		.more-link {
			font-size: 12px;
			color: #007bff;
			font-weight: 500;
			padding: 4px 8px;
			border-radius: 12px;
			background: rgba(0, 123, 255, 0.08);
			transition: all 0.3s ease;

			&:active {
				background: rgba(0, 123, 255, 0.15);
				transform: scale(0.95);
			}
		}
	}

	.announcement-list {
		.announcement-item {
			padding: 10px 0;
			border-bottom: 1px solid #f1f3f4;
			transition: all 0.3s ease;
			border-radius: 6px;
			margin-bottom: 4px;

			&:last-child {
				border-bottom: none;
				margin-bottom: 0;
			}

			.announcement-header {
				display: flex;
				align-items: center;
				gap: 6px;
				margin-bottom: 6px;
				flex-wrap: wrap;

				.announcement-type {
					padding: 2px 6px;
					border-radius: 8px;
					font-size: 10px;
					font-weight: 600;
					color: #fff;
					text-transform: uppercase;
					letter-spacing: 0.3px;

					&.type-notice {
						background: linear-gradient(135deg, #007bff, #0056b3);
					}

					&.type-urgent {
						background: linear-gradient(135deg, #dc3545, #c82333);
						animation: pulse 2s infinite;
					}

					&.type-system {
						background: linear-gradient(135deg, #6c757d, #545b62);
					}

					&.type-activity {
						background: linear-gradient(135deg, #28a745, #1e7e34);
					}
				}

				.announcement-priority,
				.announcement-pin {
					display: flex;
					align-items: center;
					padding: 1px;
				}
			}

			.announcement-title {
				display: block;
				font-size: 14px;
				font-weight: 500;
				color: #212529;
				margin-bottom: 6px;
				line-height: 1.4;
				display: -webkit-box;
				-webkit-line-clamp: 1;
				line-clamp: 1;
				-webkit-box-orient: vertical;
				overflow: hidden;
				min-height: 20px;
			}

			.announcement-meta {
				display: flex;
				align-items: center;
				gap: 8px;
				font-size: 11px;
				color: #6c757d;
				flex-wrap: wrap;

				.announcement-time {
					color: #007bff;
					font-weight: 500;
					background: rgba(0, 123, 255, 0.08);
					padding: 2px 6px;
					border-radius: 6px;
				}

				.announcement-category {
					background: #e9ecef;
					padding: 2px 6px;
					border-radius: 6px;
					color: #495057;
					font-weight: 500;
				}

				.announcement-views {
					color: #6c757d;
					display: flex;
					align-items: center;
					gap: 2px;

					&::before {
						content: '👁';
						font-size: 9px;
					}
				}
			}

			&:active {
				background: rgba(0, 123, 255, 0.05);
				transform: translateY(1px);
			}
		}
	}
}

/* 紧急公告脉冲动画 */
@keyframes pulse {
	0% {
		box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
	}
	70% {
		box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
	}
	100% {
		box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
	}
}

.course-scroll {
	white-space: nowrap;

	.course-list {
		display: inline-flex;
		gap: 15px;
		padding-bottom: 10px;

		.course-item {
			width: 140px;
			flex-shrink: 0;

			.course-thumbnail {
				width: 100%;
				height: 80px;
				border-radius: 8px;
				margin-bottom: 8px;
			}

			.course-info {
				.course-title {
					display: block;
					font-size: 14px;
					color: #333;
					margin-bottom: 4px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.course-duration {
					display: block;
					font-size: 12px;
					color: #999;
				}
			}
		}
	}
}

.loading-container {
	padding: 20px;
	text-align: center;
}
</style>
