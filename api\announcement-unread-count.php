<?php
/**
 * 获取用户未读公告数量API
 * 支持JWT认证，返回用户未读的公告数量
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        getUnreadCount($auth);
    } else {
        $auth->jsonResponse(405, '不支持的请求方法');
    }
} catch (Exception $e) {
    error_log('获取未读公告数量API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 获取用户未读公告数量
 */
function getUnreadCount($auth) {
    $conn = $auth->getConn();
    
    // 检查是否有用户认证
    $user = $auth->getCurrentUser();
    
    if (!$user) {
        // 未登录用户返回0
        $auth->jsonResponse(200, '获取未读公告数量成功', [
            'count' => 0,
            'is_logged_in' => false
        ]);
        return;
    }
    
    $user_id = $user['id'];
    
    // 获取所有已发布且未过期的公告总数
    $total_sql = "SELECT COUNT(*) as total 
                  FROM announcements 
                  WHERE status = 'published' 
                  AND (expire_time IS NULL OR expire_time > NOW())";
    
    $total_result = $conn->query($total_sql);
    $total_count = $total_result ? $total_result->fetch_assoc()['total'] : 0;
    
    // 获取用户已读的公告数量
    $read_sql = "SELECT COUNT(DISTINCT ar.announcement_id) as read_count
                 FROM announcement_reads ar
                 INNER JOIN announcements a ON ar.announcement_id = a.id
                 WHERE ar.user_id = ? 
                 AND a.status = 'published'
                 AND (a.expire_time IS NULL OR a.expire_time > NOW())";
    
    $read_stmt = $conn->prepare($read_sql);
    $read_stmt->bind_param("i", $user_id);
    $read_stmt->execute();
    $read_result = $read_stmt->get_result();
    $read_count = $read_result ? $read_result->fetch_assoc()['read_count'] : 0;
    
    // 计算未读数量
    $unread_count = max(0, $total_count - $read_count);
    
    // 获取最新的几条未读公告信息（可选）
    $recent_unread = [];
    if ($unread_count > 0) {
        $recent_sql = "SELECT a.id, a.title, a.type, a.priority, a.publish_time
                       FROM announcements a
                       LEFT JOIN announcement_reads ar ON (a.id = ar.announcement_id AND ar.user_id = ?)
                       WHERE a.status = 'published' 
                       AND (a.expire_time IS NULL OR a.expire_time > NOW())
                       AND ar.id IS NULL
                       ORDER BY a.is_pinned DESC, a.priority DESC, a.publish_time DESC
                       LIMIT 5";
        
        $recent_stmt = $conn->prepare($recent_sql);
        $recent_stmt->bind_param("i", $user_id);
        $recent_stmt->execute();
        $recent_result = $recent_stmt->get_result();
        
        while ($row = $recent_result->fetch_assoc()) {
            $recent_unread[] = [
                'id' => (int)$row['id'],
                'title' => $row['title'],
                'type' => $row['type'],
                'priority' => (int)$row['priority'],
                'publish_time' => $row['publish_time'] ? date('Y-m-d H:i:s', strtotime($row['publish_time'])) : null
            ];
        }
    }
    
    $auth->jsonResponse(200, '获取未读公告数量成功', [
        'count' => (int)$unread_count,
        'total_announcements' => (int)$total_count,
        'read_count' => (int)$read_count,
        'is_logged_in' => true,
        'user_id' => $user_id,
        'recent_unread' => $recent_unread
    ]);
}
?>
