<?php
/**
 * 课程图片上传API - 后台管理专用
 * 不需要JWT认证，仅供后台管理使用
 */

session_start();
require_once '../includes/db.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '未登录或登录已过期']);
    exit;
}

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
    exit;
}

try {
    uploadCourseImage();
} catch (Exception $e) {
    error_log('课程图片上传错误: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '服务器内部错误']);
}

/**
 * 处理课程图片上传
 */
function uploadCourseImage() {
    // 检查是否有文件上传
    if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '没有上传文件或上传失败']);
        return;
    }
    
    $file = $_FILES['image'];
    
    // 验证文件类型
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $file_info = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($file_info, $file['tmp_name']);
    finfo_close($file_info);
    
    if (!in_array($mime_type, $allowed_types)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '只支持 JPG、PNG、GIF、WebP 格式的图片']);
        return;
    }
    
    // 验证文件大小（限制5MB）
    $max_size = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $max_size) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '图片大小不能超过5MB']);
        return;
    }
    
    // 创建上传目录
    $upload_dir = dirname(__DIR__) . '/uploads/course_thumbnails/' . date('Y/m');
    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => '创建上传目录失败']);
            return;
        }
    }
    
    // 生成文件名
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (empty($file_extension)) {
        // 根据MIME类型确定扩展名
        $mime_to_ext = [
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp'
        ];
        $file_extension = $mime_to_ext[$mime_type] ?? 'jpg';
    }
    
    $file_name = 'course_' . time() . '_' . uniqid() . '.' . $file_extension;
    $file_path = $upload_dir . '/' . $file_name;
    
    // 移动上传文件
    if (!move_uploaded_file($file['tmp_name'], $file_path)) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => '文件保存失败']);
        return;
    }
    
    // 生成访问URL
    $file_url = '/uploads/course_thumbnails/' . date('Y/m') . '/' . $file_name;
    
    // 记录上传日志
    logCourseImageUpload($_SESSION['admin_id'], $file_name, $file['size'], $file_url);
    
    echo json_encode([
        'success' => true,
        'message' => '图片上传成功',
        'data' => [
            'file_name' => $file_name,
            'file_url' => $file_url,
            'file_size' => $file['size'],
            'uploaded_at' => date('Y-m-d H:i:s')
        ]
    ]);
}

/**
 * 记录课程图片上传日志
 */
function logCourseImageUpload($admin_id, $file_name, $file_size, $file_url) {
    global $conn;
    
    try {
        // 检查是否存在上传日志表
        $table_check = $conn->query("SHOW TABLES LIKE 'admin_upload_logs'");
        if (!$table_check || $table_check->num_rows == 0) {
            // 创建管理员上传日志表
            $create_table_sql = "
                CREATE TABLE admin_upload_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    admin_id INT NOT NULL,
                    upload_type VARCHAR(50) NOT NULL DEFAULT 'course_thumbnail',
                    file_name VARCHAR(255) NOT NULL,
                    file_size INT NOT NULL,
                    file_url VARCHAR(500) NOT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_admin_id (admin_id),
                    INDEX idx_upload_type (upload_type),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            $conn->query($create_table_sql);
        }
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $stmt = $conn->prepare("INSERT INTO admin_upload_logs (admin_id, upload_type, file_name, file_size, file_url, ip_address, user_agent) VALUES (?, 'course_thumbnail', ?, ?, ?, ?, ?)");
        $stmt->bind_param("ississ", $admin_id, $file_name, $file_size, $file_url, $ip_address, $user_agent);
        $stmt->execute();
        
    } catch (Exception $e) {
        error_log('记录课程图片上传日志失败: ' . $e->getMessage());
    }
}
?>
