<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>QCloud VIDEO UGC UPLOAD SDK</title>
  <link href="//cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.5/css/bootstrap.min.css" rel="stylesheet">
  <style type="text/css">
    .text-danger {
      color: red;
    }

    .control-label {
      text-align: left !important;
    }

    #resultBox {
      width: 100%;
      height: 300px;
      border: 1px solid #888;
      padding: 5px;
      overflow: auto;
      margin-bottom: 20px;
    }

    .uploaderMsgBox {
      width: 100%;
      border-bottom: 1px solid #888;
    }

    .cancel-upload {
      text-decoration: none;
      cursor: pointer;
    }
  </style>
</head>

<body>
  <div id="content">
    <div class="container">
      <h1>UGC-Uploader</h1>
    </div>
  </div>
  <div class="container" id="main-area">
    <div class="row" style="padding:10px;">
      <p>
        1.示例中的签名是直接从demo后台获取签名。该 demo 对应的点播账号已开启防盗链功能，此视频分发 URL 仅限 2 个独立 IP 进行访问，有效期为 10 分钟。<br>
        2.示例1点击“直接上传视频”按钮即可上传视频。<br>
        3.示例2点击“添加视频”添加视频文件，点击“添加封面”添加封面文件，然后点击“上传视频和封面”按钮即可上传视频和封面。<br>
        4.取消上传为取消上传中的视频，上传成功的视频不能取消上传。
      </p>
    </div>

    <form ref="vExample">
      <input type="file" style="display:none;" ref="vExampleFile" @change="vExampleUpload" />
    </form>
    <div class="row" style="padding:10px;">
      <h4>示例1：直接上传视频</h4>
      <a href="javascript:void(0);" class="btn btn-default" @click="vExampleAdd">直接上传视频</a>
    </div>

    <form ref="vcExample">
      <input type="file" style="display:none;" ref="vcExampleVideo" @change="setVcExampleVideoName()" />
      <input type="file" style="display:none;" ref="vcExampleCover" @change="setVcExampleCoverName()" />
    </form>
    <div class="row" style="padding:10px;">
      <h4>示例2：上传视频和封面</h4>
      <a href="javascript:void(0);" class="btn btn-default" @click="vcExampleAddVideo">{{vcExampleVideoName || '添加视频'}}</a>
      <a href="javascript:void(0);" class="btn btn-default" @click="vcExampleAddCover">{{vcExampleCoverName || '添加封面'}}</a>
      <a href="javascript:void(0);" class="btn btn-default" @click="vcExampleAddUpload">上传视频和封面</a>
    </div>

    <form ref="cExample">
      <input type="file" style="display:none;" ref="cExampleCover" @change="cExampleUpload" />
    </form>
    <!-- <div class="row form-group form-group-sm" style="padding:10px;">
      <h4>示例3：修改封面</h4>
      <label class="col-sm-1" style="padding: 0;">fileId：</label>
      <div class="col-sm-4">
        <input name="fileId" class="form-control" v-model="cExampleFileId"></input>
      </div>
      <div class="col-sm-4">
        <a href="javascript:void(0);" class="btn btn-default" @click="cExampleAddCover">修改封面</a>
      </div>
    </div> -->
    <div class="row" id="resultBox">
      <!-- 上传信息组件	 -->
      <div class="uploaderMsgBox" v-for="uploaderInfo in uploaderInfos">
        <div v-if="uploaderInfo.videoInfo">
          视频名称：{{uploaderInfo.videoInfo.name + '.' + uploaderInfo.videoInfo.type}}；
          上传进度：{{Math.floor(uploaderInfo.progress * 100) + '%'}}；
          fileId：{{uploaderInfo.fileId}}；
          上传结果：{{uploaderInfo.isVideoUploadCancel ? '已取消' : uploaderInfo.isVideoUploadSuccess ? '上传成功' : '上传中'}}；
          <br>
          地址：{{uploaderInfo.videoUrl}}；
          <a href="javascript:void(0);" class="cancel-upload" v-if="!uploaderInfo.isVideoUploadSuccess && !uploaderInfo.isVideoUploadCancel" @click="uploaderInfo.cancel()">取消上传</a><br>
        </div>

        <div v-if="uploaderInfo.coverInfo">
          封面名称：{{uploaderInfo.coverInfo.name}}；
          上传进度：{{Math.floor(uploaderInfo.coverProgress * 100) + '%'}}；
          上传结果：{{uploaderInfo.isCoverUploadSuccess ? '上传成功' : '上传中'}}；
          <br>
          地址：{{uploaderInfo.coverUrl}}；
          <br>
        </div>
      </div>
    </div>

  </div>
  <script src="https://cdn.jsdelivr.net/npm/es6-promise@4/dist/es6-promise.auto.js"></script>
  <script src="//cdnjs.cloudflare.com/ajax/libs/vue/2.5.21/vue.js"></script>
  <script src="//cdnjs.cloudflare.com/ajax/libs/axios/0.18.0/axios.js"></script>
  <script src="https://cdn-go.cn/cdn/vod-js-sdk-v6/latest/vod-js-sdk-v6.js"></script>

  <script type="text/javascript">

    ;(function () {

      /**
       * 计算签名。
      **/
      function getSignature() {
        return axios.post('https://demo.vod2.myqcloud.com/ugc-upload/', JSON.stringify({
          "Action": "GetUgcUploadSign"
        })).then(function (response) {
          return response.data.data.sign
        })
      };

      /*
        防盗链地址获取。这是腾讯云官网demo的特殊逻辑，用户可忽略此处。
      */
      function getAntiLeechUrl(videoUrl, callback) {
        return axios.post('https://demo.vod2.myqcloud.com/ugc-upload/', JSON.stringify({
          Action: "GetAntiLeechUrl",
          Url: videoUrl,
        })).then(function (response) {
          return response.data.data.url
        })
      }

      var app = new Vue({
        el: '#main-area',
        data: {
          uploaderInfos: [],

          vcExampleVideoName: '',
          vcExampleCoverName: '',

          cExampleFileId: '',
        },
        created: function () {
          this.tcVod = new TcVod.default({
            getSignature: getSignature
          })
        },
        methods: {
          /**
           * vExample示例。添加视频
          **/
          vExampleAdd: function () {
            this.$refs.vExampleFile.click()
          },
          /**
           * vExample示例。上传视频过程。
          **/
          vExampleUpload: function () {
            var self = this;
            var mediaFile = this.$refs.vExampleFile.files[0]

            var uploader = this.tcVod.upload({
              mediaFile: mediaFile,
            })
            uploader.on('media_progress', function (info) {
              uploaderInfo.progress = info.percent;
            })
            uploader.on('media_upload', function (info) {
              uploaderInfo.isVideoUploadSuccess = true;
            })

            console.log(uploader, 'uploader')

            var uploaderInfo = {
              videoInfo: uploader.videoInfo,
              isVideoUploadSuccess: false,
              isVideoUploadCancel: false,
              progress: 0,
              fileId: '',
              videoUrl: '',
              cancel: function() {
                uploaderInfo.isVideoUploadCancel = true;
                uploader.cancel()
              },
            }

            this.uploaderInfos.push(uploaderInfo)

            uploader.done().then(function(doneResult) {
              console.log('doneResult', doneResult)

              uploaderInfo.fileId = doneResult.fileId;

              return getAntiLeechUrl(doneResult.video.url);
            }).then(function (videoUrl) {
              uploaderInfo.videoUrl = videoUrl
              self.$refs.vExample.reset();
            })
          },

          setVcExampleVideoName: function () {
            this.vcExampleVideoName = this.$refs.vcExampleVideo.files[0].name;
          },
          setVcExampleCoverName: function () {
            this.vcExampleCoverName = this.$refs.vcExampleCover.files[0].name;
          },
          /*
            vcExample添加视频
          */
          vcExampleAddVideo: function () {
            this.$refs.vcExampleVideo.click()
          },
          /*
            vcExample添加封面
          */
          vcExampleAddCover: function () {
            this.$refs.vcExampleCover.click()
          },
          /*
            vcExample上传过程
          */
          vcExampleAddUpload: function () {
            var self = this;

            var mediaFile = this.$refs.vcExampleVideo.files[0];
            var coverFile = this.$refs.vcExampleCover.files[0];

            var uploader = this.tcVod.upload({
              mediaFile: mediaFile,
              coverFile: coverFile,
            })
            uploader.on('media_progress', function(info) {
              uploaderInfo.progress = info.percent;
            })
            uploader.on('media_upload', function(info) {
              uploaderInfo.isVideoUploadSuccess = true;
            })
            uploader.on('cover_progress', function(info) {
              uploaderInfo.coverProgress = info.percent;
            })
            uploader.on('cover_upload', function(info) {
              uploaderInfo.isCoverUploadSuccess = true;
            })
            console.log(uploader, 'uploader')

            var uploaderInfo = {
              videoInfo: uploader.videoInfo,
              coverInfo: uploader.coverInfo,
              isVideoUploadSuccess: false,
              isVideoUploadCancel: false,
              isCoverUploadSuccess: false,
              progress: 0,
              coverProgress: 0,
              fileId: '',
              videoUrl: '',
              coverUrl: '',
              cancel: function () {
                uploaderInfo.isVideoUploadCancel = true;
                uploader.cancel()
              },
            }

            this.uploaderInfos.push(uploaderInfo)

            uploader.done().then(function (doneResult) {
              console.log('doneResult', doneResult)

              uploaderInfo.fileId = doneResult.fileId;

              uploaderInfo.coverUrl = doneResult.cover.url;
              return getAntiLeechUrl(doneResult.video.url);
            }).then(function (videoUrl) {
              uploaderInfo.videoUrl = videoUrl
              self.$refs.vcExample.reset();
              self.vcExampleVideoName = ''
              self.vcExampleCoverName = ''
            })
          },


          // cExample 添加封面
          cExampleAddCover: function() {
            this.$refs.cExampleCover.click()
          },
          // cExample 上传过程
          cExampleUpload: function() {
            var self = this;

            var coverFile = this.$refs.cExampleCover.files[0];

            var uploader = this.tcVod.upload({
              fileId: this.cExampleFileId,
              coverFile: coverFile,
            })
            uploader.on('cover_progress', function(info) {
              uploaderInfo.coverProgress = info.percent;
            })
            uploader.on('cover_upload', function(info) {
              uploaderInfo.isCoverUploadSuccess = true;
            })
            console.log(uploader, 'uploader')

            var uploaderInfo = {
              coverInfo: uploader.coverInfo,
              isCoverUploadSuccess: false,
              coverProgress: 0,
              coverUrl: '',
              cancel: function () {
                uploader.cancel()
              },
            }

            this.uploaderInfos.push(uploaderInfo)

            uploader.done().then(function (doneResult) {
              console.log('doneResult', doneResult)

              uploaderInfo.coverUrl = doneResult.cover.url;

              self.$refs.cExample.reset();
            })
          },
        },
      })
    })();

  </script>
  <!-- Global site tag (gtag.js) - Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=UA-26476625-7"></script>
  <script>
    // <NAME_EMAIL>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'UA-26476625-7');
  </script>

</body>

</html>