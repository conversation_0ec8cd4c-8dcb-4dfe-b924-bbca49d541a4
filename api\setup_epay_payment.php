<?php
/**
 * 码支付易支付配置设置脚本
 * 用于设置码支付易支付的基本配置
 */

header('Content-Type: text/html; charset=utf-8');
require_once '../includes/db.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='utf-8'><title>码支付易支付配置</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .config-item{margin:10px 0;} .success{color:green;} .error{color:red;}</style>";
echo "</head><body>";
echo "<h1>码支付易支付配置设置</h1>";

try {
    // 检查settings表是否存在
    $result = $conn->query("SHOW TABLES LIKE 'settings'");
    if ($result->num_rows == 0) {
        echo "<p class='error'>❌ settings表不存在，请先运行数据库初始化脚本</p>";
        exit;
    }
    
    // 码支付易支付配置项
    $epay_settings = [
        'epay_enabled' => [
            'value' => '0', // 默认禁用，需要手动启用
            'type' => 'boolean',
            'description' => '是否启用码支付易支付'
        ],
        'epay_api_url' => [
            'value' => '', // 需要填写真实的API地址
            'type' => 'string',
            'description' => '码支付易支付API地址'
        ],
        'epay_partner_id' => [
            'value' => '', // 需要填写真实的商户PID
            'type' => 'string',
            'description' => '码支付易支付商户PID'
        ],
        'epay_partner_key' => [
            'value' => '', // 需要填写真实的商户密钥
            'type' => 'string',
            'description' => '码支付易支付商户密钥'
        ],
        'epay_notify_url' => [
            'value' => 'https://wx.yx420.cn/api/payment-epay-notify.php',
            'type' => 'string',
            'description' => '码支付易支付回调通知URL'
        ],
        'epay_return_url' => [
            'value' => 'https://wx.yx420.cn/api/payment-epay-return.php',
            'type' => 'string',
            'description' => '码支付易支付同步返回URL'
        ],
        'epay_supported_methods' => [
            'value' => 'alipay,wxpay,qqpay',
            'type' => 'string',
            'description' => '支持的支付方式（逗号分隔）'
        ]
    ];
    
    echo "<h2>正在初始化码支付易支付配置...</h2>";
    
    foreach ($epay_settings as $key => $config) {
        // 检查配置是否已存在
        $stmt = $conn->prepare("SELECT id FROM settings WHERE setting_key = ?");
        $stmt->bind_param("s", $key);
        $stmt->execute();
        $exists = $stmt->get_result()->num_rows > 0;
        
        if (!$exists) {
            // 插入新配置
            $stmt = $conn->prepare("
                INSERT INTO settings (setting_key, setting_value, setting_type, description, group_name) 
                VALUES (?, ?, ?, ?, 'payment')
            ");
            $stmt->bind_param("ssss", $key, $config['value'], $config['type'], $config['description']);
            
            if ($stmt->execute()) {
                echo "<div class='config-item success'>✅ 已添加配置: {$key}</div>";
            } else {
                echo "<div class='config-item error'>❌ 添加配置失败: {$key} - " . $conn->error . "</div>";
            }
        } else {
            echo "<div class='config-item'>ℹ️ 配置已存在: {$key}</div>";
        }
    }
    
    // 检查payments表是否支持epay支付方式
    echo "<h2>检查payments表结构...</h2>";
    
    $result = $conn->query("SHOW COLUMNS FROM payments LIKE 'payment_method'");
    if ($result->num_rows > 0) {
        $column = $result->fetch_assoc();
        $type = $column['Type'];
        
        // 检查是否包含epay选项
        if (strpos($type, 'epay') === false) {
            // 修改枚举类型，添加epay选项
            $alter_sql = "ALTER TABLE payments MODIFY COLUMN payment_method varchar(20) NOT NULL COMMENT '支付方式'";
            if ($conn->query($alter_sql)) {
                echo "<div class='success'>✅ 已更新payments表payment_method字段支持更多支付方式</div>";
            } else {
                echo "<div class='error'>❌ 更新payments表失败: " . $conn->error . "</div>";
            }
        } else {
            echo "<div class='success'>✅ payments表已支持epay支付方式</div>";
        }
    }
    
    echo "<h2>当前码支付易支付配置</h2>";
    
    $stmt = $conn->prepare("
        SELECT setting_key, setting_value, description 
        FROM settings 
        WHERE setting_key LIKE 'epay_%' 
        ORDER BY setting_key
    ");
    $stmt->execute();
    $settings = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    if (empty($settings)) {
        echo "<p class='error'>❌ 未找到码支付易支付配置</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse:collapse;'>";
        echo "<tr><th>配置项</th><th>当前值</th><th>说明</th></tr>";
        
        foreach ($settings as $setting) {
            $value = $setting['setting_value'];
            
            // 隐藏敏感信息
            if (strpos($setting['setting_key'], 'key') !== false && !empty($value)) {
                $value = str_repeat('*', strlen($value) - 4) . substr($value, -4);
            }
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($setting['setting_key']) . "</td>";
            echo "<td>" . htmlspecialchars($value ?: '(未设置)') . "</td>";
            echo "<td>" . htmlspecialchars($setting['description']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<h2>配置说明</h2>";
    echo "<div style='background:#f5f5f5;padding:15px;border-radius:5px;'>";
    echo "<h3>使用步骤：</h3>";
    echo "<ol>";
    echo "<li>在码支付易支付商户后台获取商户PID和密钥</li>";
    echo "<li>在管理后台设置页面配置相关参数</li>";
    echo "<li>启用码支付易支付功能</li>";
    echo "<li>在小程序中测试支付流程</li>";
    echo "</ol>";
    echo "<h3>注意事项：</h3>";
    echo "<ul>";
    echo "<li>请确保回调URL可以正常访问</li>";
    echo "<li>商户密钥请妥善保管，不要泄露</li>";
    echo "<li>建议先在测试环境验证支付流程</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 配置过程中出现错误: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
