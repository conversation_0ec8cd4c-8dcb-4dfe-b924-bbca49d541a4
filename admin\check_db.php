<?php
require_once '../includes/db.php';

// 检查数据库连接
echo "<h2>数据库连接检查</h2>";
echo "数据库连接状态: ";
if ($conn->connect_error) {
    echo "失败 - " . $conn->connect_error;
    exit;
} else {
    echo "成功<br>";
}

// 检查courses表结构并修复
echo "<h2>课程表结构检查与修复</h2>";
$courses_result = $conn->query("SHOW TABLES LIKE 'courses'");
if ($courses_result && $courses_result->num_rows > 0) {
    echo "courses表: 存在<br>";

    // 检查当前表结构
    $structure = $conn->query("DESCRIBE courses");
    $existing_fields = [];
    if ($structure) {
        echo "<h3>当前字段:</h3>";
        while ($field = $structure->fetch_assoc()) {
            $existing_fields[] = $field['Field'];
            echo $field['Field'] . " (" . $field['Type'] . ")<br>";
        }
    }

    // 需要添加的字段
    $required_fields = [
        'cover_image' => "VARCHAR(500) DEFAULT NULL COMMENT '课程封面图片'",
        'is_recommended' => "TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否推荐：0否 1是'",
        'is_recommend' => "TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否推荐（兼容字段）：0否 1是'",
        'view_count' => "INT(11) NOT NULL DEFAULT 0 COMMENT '观看次数'",
        'price' => "DECIMAL(10,2) DEFAULT 0.00 COMMENT '课程价格'",
        'original_price' => "DECIMAL(10,2) DEFAULT NULL COMMENT '原价'",
        'is_free' => "TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否免费：0否 1是'",
        'teacher_name' => "VARCHAR(100) DEFAULT NULL COMMENT '讲师姓名'",
        'subtitle' => "VARCHAR(500) DEFAULT NULL COMMENT '课程副标题'",
        'rating' => "DECIMAL(3,2) DEFAULT 0.00 COMMENT '课程评分（0-5）'",
        'student_count' => "INT(11) NOT NULL DEFAULT 0 COMMENT '学生数量'"
    ];

    echo "<h3>字段修复结果:</h3>";
    foreach ($required_fields as $field_name => $field_definition) {
        if (!in_array($field_name, $existing_fields)) {
            $sql = "ALTER TABLE courses ADD COLUMN $field_name $field_definition";
            if ($conn->query($sql)) {
                echo "✓ 添加字段 $field_name 成功<br>";
            } else {
                echo "✗ 添加字段 $field_name 失败: " . $conn->error . "<br>";
            }
        } else {
            echo "- 字段 $field_name 已存在<br>";
        }
    }

} else {
    echo "courses表: 不存在，需要运行安装脚本<br>";
}

// 检查lessons表（课时表）
echo "<h2>课时表检查</h2>";
$lessons_result = $conn->query("SHOW TABLES LIKE 'lessons'");
if ($lessons_result && $lessons_result->num_rows > 0) {
    echo "lessons表: 存在<br>";
} else {
    echo "lessons表: 不存在，创建中...<br>";
    $create_lessons_sql = "
    CREATE TABLE `lessons` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `course_id` int(11) NOT NULL COMMENT '课程ID',
      `title` varchar(255) NOT NULL COMMENT '课时标题',
      `description` text COMMENT '课时描述',
      `video_url` varchar(500) DEFAULT NULL COMMENT '视频链接',
      `duration` int(11) DEFAULT NULL COMMENT '时长（秒）',
      `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
      `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0禁用 1启用',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `idx_course_id` (`course_id`),
      KEY `idx_status` (`status`),
      KEY `idx_sort_order` (`sort_order`),
      FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课时表'";

    if ($conn->query($create_lessons_sql)) {
        echo "✓ lessons表创建成功<br>";
    } else {
        echo "✗ lessons表创建失败: " . $conn->error . "<br>";
    }
}

// 检查其他重要表
echo "<h2>其他表检查</h2>";
$tables = ['users', 'user_courses', 'banners', 'announcements', 'settings'];

foreach ($tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result === false) {
        echo "查询失败: " . $conn->error . "<br>";
    } else {
        if ($result->num_rows > 0) {
            echo "表 $table: ✓ 存在<br>";

            // 检查记录数量
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            if ($count_result) {
                $count = $count_result->fetch_assoc()['count'];
                echo "  记录数量: $count<br>";
            }
        } else {
            echo "表 $table: ✗ 不存在<br>";
        }
    }
}

// 检查管理员表
$admin_result = $conn->query("SHOW TABLES LIKE 'admins'");
if ($admin_result && $admin_result->num_rows > 0) {
    echo "管理员表: ✓ 存在<br>";
    $admin_count = $conn->query("SELECT COUNT(*) as count FROM admins");
    if ($admin_count) {
        $count = $admin_count->fetch_assoc()['count'];
        echo "  管理员数量: $count<br>";
    }
} else {
    echo "管理员表: ✗ 不存在<br>";
}

echo "<h2>修复完成</h2>";
echo "请刷新页面查看最新状态，或访问课程API测试功能。";
?>