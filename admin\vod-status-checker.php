<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$success_message = '';
$error_message = '';

// 处理批量检查请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'check_all_vod') {
        // 获取所有需要检查的VOD课时
        $vod_lessons_query = "SELECT id, title, vod_file_id, vod_video_url FROM lessons WHERE video_type = 'vod' AND vod_file_id IS NOT NULL AND vod_file_id != ''";
        $vod_lessons_result = $conn->query($vod_lessons_query);
        
        $total_checked = 0;
        $total_updated = 0;
        $errors = [];
        
        while ($lesson = $vod_lessons_result->fetch_assoc()) {
            $total_checked++;
            
            // 调用VOD状态检查API
            $api_url = '../api/vod-update-status.php';
            $post_data = http_build_query([
                'lesson_id' => $lesson['id'],
                'file_id' => $lesson['vod_file_id'],
                'update_db' => '1'
            ]);
            
            $context = stream_context_create([
                'http' => [
                    'method' => 'POST',
                    'header' => 'Content-Type: application/x-www-form-urlencoded',
                    'content' => $post_data
                ]
            ]);
            
            $response = file_get_contents($api_url, false, $context);
            $result = json_decode($response, true);
            
            if ($result && $result['success'] && isset($result['data']['updated']) && $result['data']['updated']) {
                $total_updated++;
            } elseif (!$result || !$result['success']) {
                $errors[] = "课时 \"{$lesson['title']}\" 检查失败: " . ($result['message'] ?? '未知错误');
            }
            
            // 避免请求过于频繁
            usleep(500000); // 0.5秒延迟
        }
        
        $success_message = "批量检查完成！共检查 {$total_checked} 个VOD课时，更新了 {$total_updated} 个播放URL。";
        if (!empty($errors)) {
            $error_message = "部分检查失败：\n" . implode("\n", $errors);
        }
    }
}

// 获取VOD课时统计
$vod_stats_query = "
    SELECT 
        COUNT(*) as total_vod,
        SUM(CASE WHEN vod_video_url IS NOT NULL AND vod_video_url != '' THEN 1 ELSE 0 END) as has_play_url,
        SUM(CASE WHEN vod_video_url IS NULL OR vod_video_url = '' THEN 1 ELSE 0 END) as no_play_url
    FROM lessons 
    WHERE video_type = 'vod' AND vod_file_id IS NOT NULL AND vod_file_id != ''
";
$vod_stats_result = $conn->query($vod_stats_query);
$vod_stats = $vod_stats_result->fetch_assoc();

// 获取需要检查的VOD课时列表
$pending_vod_query = "
    SELECT l.id, l.title, l.vod_file_id, l.vod_video_url, l.created_at, c.title as course_title
    FROM lessons l
    LEFT JOIN courses c ON l.course_id = c.id
    WHERE l.video_type = 'vod' AND l.vod_file_id IS NOT NULL AND l.vod_file_id != ''
    ORDER BY l.created_at DESC
";
$pending_vod_result = $conn->query($pending_vod_query);

render_admin_header('VOD状态检查工具');
?>

<style>
.vod-checker-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stats-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.stats-number {
    font-size: 2em;
    font-weight: bold;
    color: #007bff;
}

.stats-label {
    color: #666;
    margin-top: 5px;
}

.vod-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.vod-table th,
.vod-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.vod-table th {
    background: #f8f9fa;
    font-weight: 600;
}

.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-ok {
    background-color: #28a745;
}

.status-pending {
    background-color: #ffc107;
}

.file-id {
    font-family: monospace;
    font-size: 12px;
    color: #666;
}
</style>

<div class="vod-checker-container">
    <h1>VOD状态检查工具</h1>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success"><?php echo htmlspecialchars($success_message); ?></div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger"><?php echo nl2br(htmlspecialchars($error_message)); ?></div>
    <?php endif; ?>
    
    <!-- 统计卡片 -->
    <div class="stats-cards">
        <div class="stats-card">
            <div class="stats-number"><?php echo $vod_stats['total_vod']; ?></div>
            <div class="stats-label">总VOD课时</div>
        </div>
        <div class="stats-card">
            <div class="stats-number"><?php echo $vod_stats['has_play_url']; ?></div>
            <div class="stats-label">已有播放URL</div>
        </div>
        <div class="stats-card">
            <div class="stats-number"><?php echo $vod_stats['no_play_url']; ?></div>
            <div class="stats-label">待检查转码</div>
        </div>
    </div>
    
    <!-- 批量操作 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>批量操作</h5>
        </div>
        <div class="card-body">
            <p>检查所有VOD课时的转码状态，并自动更新播放URL。</p>
            <form method="POST" onsubmit="return confirm('确定要检查所有VOD课时吗？这可能需要一些时间。');">
                <input type="hidden" name="action" value="check_all_vod">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sync"></i> 批量检查所有VOD状态
                </button>
            </form>
        </div>
    </div>
    
    <!-- VOD课时列表 -->
    <div class="card">
        <div class="card-header">
            <h5>VOD课时列表</h5>
        </div>
        <div class="card-body">
            <?php if ($pending_vod_result->num_rows > 0): ?>
                <table class="vod-table">
                    <thead>
                        <tr>
                            <th>状态</th>
                            <th>课时标题</th>
                            <th>所属课程</th>
                            <th>文件ID</th>
                            <th>播放URL</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($lesson = $pending_vod_result->fetch_assoc()): ?>
                            <tr>
                                <td>
                                    <span class="status-indicator <?php echo !empty($lesson['vod_video_url']) ? 'status-ok' : 'status-pending'; ?>"></span>
                                    <?php echo !empty($lesson['vod_video_url']) ? '已就绪' : '待转码'; ?>
                                </td>
                                <td><?php echo htmlspecialchars($lesson['title']); ?></td>
                                <td><?php echo htmlspecialchars($lesson['course_title']); ?></td>
                                <td>
                                    <span class="file-id"><?php echo htmlspecialchars($lesson['vod_file_id']); ?></span>
                                </td>
                                <td>
                                    <?php if (!empty($lesson['vod_video_url'])): ?>
                                        <a href="<?php echo htmlspecialchars($lesson['vod_video_url']); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-play"></i> 播放
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">暂无</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('Y-m-d H:i', strtotime($lesson['created_at'])); ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="checkSingleVod(<?php echo $lesson['id']; ?>, '<?php echo htmlspecialchars($lesson['vod_file_id']); ?>')">
                                        <i class="fas fa-sync"></i> 检查
                                    </button>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p class="text-muted">暂无VOD课时。</p>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function checkSingleVod(lessonId, fileId) {
    const button = event.target.closest('button');
    const originalHtml = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 检查中';
    button.disabled = true;

    fetch(`../api/vod-update-status.php?lesson_id=${lessonId}&file_id=${encodeURIComponent(fileId)}&update_db=1`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                if (data.data.updated) {
                    alert('转码完成，播放URL已更新！页面将刷新。');
                    location.reload();
                } else if (data.data.best_play_url) {
                    alert('视频已可播放。');
                } else {
                    alert('转码状态：' + (data.data.transcoding.status_text || '未知'));
                }
            } else {
                alert('检查失败：' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            alert('检查失败：' + error.message);
        })
        .finally(() => {
            button.innerHTML = originalHtml;
            button.disabled = false;
        });
}
</script>

<?php render_admin_footer(); ?>
