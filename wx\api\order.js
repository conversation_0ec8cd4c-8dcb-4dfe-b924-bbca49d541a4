import request from '../utils/request.js';

/**
 * 创建订单
 * @param {Array} course_ids 课程ID数组
 */
export function createOrder(course_ids) {
    return request.request({
        url: 'order-create.php',
        method: 'POST',
        data: {
            course_ids
        }
    });
}

/**
 * 获取订单列表
 * @param {Object} params 查询参数
 */
export function getOrderList(params = {}) {
    return request.request({
        url: 'order-list.php',
        method: 'GET',
        data: params
    });
}

/**
 * 获取订单详情
 * @param {Number} order_id 订单ID
 */
export function getOrderDetail(order_id) {
    return request.request({
        url: 'order-detail.php',
        method: 'GET',
        data: {
            id: order_id
        }
    });
}

/**
 * 微信支付预支付
 * @param {Number} order_id 订单ID
 * @param {String} openid 用户openid
 */
export function wechatPrepay(order_id, openid) {
    return request.request({
        url: 'payment-wechat-prepay.php',
        method: 'POST',
        data: {
            order_id,
            openid
        }
    });
}

/**
 * 取消订单
 * @param {Number} order_id 订单ID
 */
export function cancelOrder(order_id) {
    return request.request({
        url: 'order-cancel.php',
        method: 'POST',
        data: {
            order_id
        }
    });
}