/*! For license information please see tcplayer-vr-plugin.1.0.0.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("TcplayerVRPlugin",[],t):"object"==typeof exports?exports.TcplayerVRPlugin=t():e.TcplayerVRPlugin=t()}(self,(function(){return(()=>{var __webpack_modules__={774:module=>{function T(e,t,n,i,r,o,s,a){var u,l=!i,c=(e=+e,t=t||[0],i=i||[[this],[{}]],r=r||{},[]),h=null;function d(){return function(e,t,n){return new(Function.bind.apply(e,t))}.apply(null,arguments)}Function.prototype.bind||(u=[].slice,Function.prototype.bind=function(e){if("function"!=typeof this)throw new TypeError("bind101");var t=u.call(arguments,1),n=t.length,i=this,r=function(){},o=function(){return t.length=n,t.push.apply(t,arguments),i.apply(r.prototype.isPrototypeOf(this)?this:e,t)};return this.prototype&&(r.prototype=this.prototype),o.prototype=new r,o});for(var p=[function(){var n=(r=t[e++])?i.slice(-r):[],r=(i.length-=r,i.pop());i.push(r[0][r[1]].apply(r[0],n))},function(){i[i.length-2]=i[i.length-2]*i.pop()},function(){for(var u=i.pop(),l=t[e++],c=[],h=t[e++],d=t[e++],p=[],f=0;f<h;f++)c[t[e++]]=i[t[e++]];for(f=0;f<d;f++)p[f]=t[e++];var A=function(){var e=c.slice(0);e[0]=[this],e[1]=[arguments],e[2]=[A];for(var i=0;i<p.length&&i<arguments.length;i++)0<p[i]&&(e[p[i]]=[arguments[i]]);return T(l,t,n,e,r,o,s,a)};A.toString=function(){return u},i.push(A)},function(){var n=(r=t[e++])?i.slice(-r):[],r=(i.length-=r,n.unshift(null),i.pop());i.push(d(r[0][r[1]],n))},function(){i[i.length-2]=i[i.length-2]<<i.pop()},function(){i[i.length-1]+=String.fromCharCode(t[e++])},function(){i.length=t[e++]},function(){var r=t[e++],o=r?i.slice(-r):[];i.length-=r,i.push(i.pop().apply(n,o))},function(){var e=i.pop();r[e]||(n[e]=n[e](),r[e]=1),i.push(n[e])},function(){i.push(t[e++])},function(){i.push(null)},function(){i.push(i[i.pop()[0]][0])},function(){i[i.length-2]=i[i.length-2]/i.pop()},function(){i.push([n,i.pop()])},function(){i[i.length-1]=n[i[i.length-1]]},function(){i[i[i.length-1][0]]=void 0===i[i[i.length-1][0]]?[]:i[i[i.length-1][0]]},function(){i[i.length-2]=i[i.length-2]%i.pop()},function(){i.push(~i.pop())},function(){i.push(!1)},function(){var e=i[i.length-2],t=Object.getOwnPropertyDescriptor(e[0],e[1])||{configurable:!0,enumerable:!0};t.set=i[i.length-1],Object.defineProperty(e[0],e[1],t)},function(){i.push(typeof i.pop())},function(){i.push(!i.pop())},function(){var e=i[i.length-2];e[0][e[1]]=i[i.length-1]},function(){i[i.length-1].length?i.push(i[i.length-1].shift(),!0):i.push(void 0,!1)},function(){i.push(void 0)},function(){i[i.length-2]=i[i.length-2]in i.pop()},function(){for(var u=t[e++],l=[],c=t[e++],h=t[e++],d=[],p=0;p<c;p++)l[t[e++]]=i[t[e++]];for(p=0;p<h;p++)d[p]=t[e++];i.push((function e(){var i=l.slice(0);i[0]=[this],i[1]=[arguments],i[2]=[e];for(var c=0;c<d.length&&c<arguments.length;c++)0<d[c]&&(i[d[c]]=[arguments[c]]);return T(u,t,n,i,r,o,s,a)}))},function(){i.push([i.pop(),i.pop()].reverse())},function(){i.length-=t[e++]},function(){},function(){var e,t=[];for(e in i.pop())t.push(e);i.push(t)},function(){c.pop()},function(){var e=i.pop();i.push(e[0][e[1]])},function(){i[i.length-2]=i[i.length-2]^i.pop()},function(){i[i.length-2]=i[i.length-2]>>>i.pop()},function(){i[i.length-2]=i[i.length-2]==i.pop()},function(){i[i.length-2]=i[i.length-2]<i.pop()},function(){c.push([t[e++],i.length,t[e++]])},function(){i[i.length-2]=i[i.length-2]<=i.pop()},function(){var n=t[e++],r=n?i.slice(-n):[];i.length-=n,r.unshift(null),i.push(d(i.pop(),r))},function(){i.push(i[i.length-1])},function(){var e=i.pop();i.push(delete e[0][e[1]])},function(){i[i[i.length-2][0]][0]=i[i.length-1]},function(){i[i.length-2]=i[i.length-2]instanceof i.pop()},function(){i[i.length-2]=i[i.length-2]+i.pop()},function(){i[i.length-2]=i[i.length-2]|i.pop()},function(){var e=i.pop();r[e]||(n[e]=n[e](),r[e]=1),i.push([n,e])},function(){var e=i.pop();i.push([i[i.pop()][0],e])},function(){i.push(i[t[e++]][0])},function(){return!!h},function(){i.pop()},function(){i[i.length-2]=i[i.length-2]&i.pop()},function(){i[i.length-2]=i[i.length-2]===i.pop()},function(){i.push(!0)},function(){var n=t[e++];i[i.length-1]&&(e=n)},function(){i[i.length-2]=i[i.length-2]-i.pop()},function(){var n=t[e++],r=i[i.length-2-n];i[i.length-2-n]=i.pop(),i.push(r)},function(){i.push("")},function(){var e=i[i.length-2],t=Object.getOwnPropertyDescriptor(e[0],e[1])||{configurable:!0,enumerable:!0};t.get=i[i.length-1],Object.defineProperty(e[0],e[1],t)},function(){i[i.length-2]=i[i.length-2]>>i.pop()},function(){i.push([t[e++]])},function(){var n=t[e++];i[n]=void 0===i[n]?[]:i[n]},function(){return!0},function(){i[i.length-1]=t[e++]},function(){h=null},function(){throw i[i.length-1]},function(){var e=i.pop(),t=i.pop();i.push([t[0][t[1]],e])},function(){i[i.length-2]=i[i.length-2]>i.pop()},function(){i[i.length-2]=i[i.length-2]>=i.pop()},function(){e=t[e++]}];;)try{for(var f=!1;!f;)f=p[t[e++]]();if(h)throw h;return l?(i.pop(),i.slice(3+T.v)):i.pop()}catch(t){var A=c.pop();if(void 0===A)throw t;h=t,e=A[0],i.length=A[1],A[2]&&(i[A[2]][0]=h)}}function arrayIndexOf(e,t,n){if("function"==typeof Array.prototype.indexOf)return Array.prototype.indexOf.call(e,t,n);var i,r=o.length;if(0===r)return-1;var s=0|n;if(s>=r)return-1;for(i=Math.max(s>=0?s:r-Math.abs(s),0);i<r;i++)if(i in o&&o[i]===t)return i;return-1}function base64Decode(e){for(var t,n,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".split(""),r=String(e).replace(/[=]+$/,""),o=0,s=0,a="";n=r.charAt(s++);~n&&(t=o%4?64*t+n:n,o++%4)?a+=String.fromCharCode(255&t>>(-2*o&6)):0)n=arrayIndexOf(i,n);return a}T.v=0,module.exports.r=function(symbols){for(var result=[],i=0;i<symbols.length;i++)try{result.push(eval(symbols[i]))}catch(e){result.push(void 0)}return result},module.exports.d=function(e){if("object"!=typeof e[1])return e;var t=e[0],n=e[1],i=[],r=base64Decode(t),o=n.shift(),s=n.shift(),a=0;function u(){for(;a===o;)i.push(s),a++,o=n.shift(),s=n.shift()}for(var l=0;l<r.length;l++){var c=r.charAt(l).charCodeAt(0);u(),i.push(c),a++}return u(),i},module.exports.g=function(e){return e.shift()[0]},module.exports.v=T},85:(e,t,n)=>{"use strict";function i(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],i=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function r(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var i,r,o=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(i=o.next()).done;)s.push(i.value)}catch(e){r={error:e}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return s}function o(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(r(arguments[t]));return e}n.r(t),n.d(t,{DEFAULT_CANVAS_CLASS:()=>di,DEFAULT_IMAGE_CLASS:()=>ar,DEFAULT_WRAPPER_CLASS:()=>sr,ERROR_TYPE:()=>ai,GYRO_MODE:()=>In,PANOVIEWER_EVENTS:()=>ui,PANOVIEWER_OPTIONS:()=>hi,PROJECTION_TYPE:()=>li,PanoViewer:()=>ir,SPINVIEWER_EVENTS:()=>or,SPINVIEWER_OPTIONS:()=>rr,STEREO_FORMAT:()=>ci,SpinViewer:()=>lr,SpriteImage:()=>ur,VERSION:()=>Ht,generateCanvasKey:()=>vr,getValidProps:()=>gr,updatePanoViewer:()=>pr,withMethods:()=>cr,withPanoViewerMethods:()=>hr,withSpinViewerMethods:()=>dr});var s=function(e){return void 0===e},a=function(){function e(e,t){var n,r;if(this._canceled=!1,t)try{for(var o=i(Object.keys(t)),s=o.next();!s.done;s=o.next()){var a=s.value;this[a]=t[a]}}catch(e){n={error:e}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}this.eventType=e}var t=e.prototype;return t.stop=function(){this._canceled=!0},t.isCanceled=function(){return this._canceled},e}(),u=a;const l=function(){function e(){this._eventHandler={}}var t=e.prototype;return t.trigger=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=e instanceof a?e.eventType:e,r=o(this._eventHandler[i]||[]);return r.length<=0||(e instanceof a?(e.currentTarget=this,r.forEach((function(t){t(e)}))):r.forEach((function(e){e.apply(void 0,o(t))}))),this},t.once=function(e,t){var n=this;if("object"==typeof e&&s(t)){var i=e;for(var r in i)this.once(r,i[r]);return this}if("string"==typeof e&&"function"==typeof t){var a=function(){for(var i=[],r=0;r<arguments.length;r++)i[r]=arguments[r];t.apply(void 0,o(i)),n.off(e,a)};this.on(e,a)}return this},t.hasOn=function(e){return!!this._eventHandler[e]},t.on=function(e,t){if("object"==typeof e&&s(t)){var n=e;for(var i in n)this.on(i,n[i]);return this}if("string"==typeof e&&"function"==typeof t){var r=this._eventHandler[e];s(r)&&(this._eventHandler[e]=[],r=this._eventHandler[e]),r.push(t)}return this},t.off=function(e,t){var n,r;if(s(e))return this._eventHandler={},this;if(s(t)){if("string"==typeof e)return delete this._eventHandler[e],this;var o=e;for(var a in o)this.off(a,o[a]);return this}var u=this._eventHandler[e];if(u){var l=0;try{for(var c=i(u),h=c.next();!h.done;h=c.next()){if(h.value===t){u.splice(l,1),u.length<=0&&delete this._eventHandler[e];break}l++}}catch(e){n={error:e}}finally{try{h&&!h.done&&(r=c.return)&&r.call(c)}finally{if(n)throw n.error}}}return this},e.VERSION="3.0.4",e}();function c(e,t){this.name="AggregateError",this.errors=e,this.message=t||""}c.prototype=Error.prototype;var h=setTimeout;function d(e){return Boolean(e&&void 0!==e.length)}function p(){}function f(e){if(!(this instanceof f))throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],_(e,this)}function A(e,t){for(;3===e._state;)e=e._value;0!==e._state?(e._handled=!0,f._immediateFn((function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null!==n){var i;try{i=n(e._value)}catch(e){return void v(t.promise,e)}g(t.promise,i)}else(1===e._state?g:v)(t.promise,e._value)}))):e._deferreds.push(t)}function g(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if(t instanceof f)return e._state=3,e._value=t,void m(e);if("function"==typeof n)return void _((i=n,r=t,function(){i.apply(r,arguments)}),e)}e._state=1,e._value=t,m(e)}catch(t){v(e,t)}var i,r}function v(e,t){e._state=2,e._value=t,m(e)}function m(e){2===e._state&&0===e._deferreds.length&&f._immediateFn((function(){e._handled||f._unhandledRejectionFn(e._value)}));for(var t=0,n=e._deferreds.length;t<n;t++)A(e,e._deferreds[t]);e._deferreds=null}function y(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}function _(e,t){var n=!1;try{e((function(e){n||(n=!0,g(t,e))}),(function(e){n||(n=!0,v(t,e))}))}catch(e){if(n)return;n=!0,v(t,e)}}f.prototype.catch=function(e){return this.then(null,e)},f.prototype.then=function(e,t){var n=new this.constructor(p);return A(this,new y(e,t,n)),n},f.prototype.finally=function(e){var t=this.constructor;return this.then((function(n){return t.resolve(e()).then((function(){return n}))}),(function(n){return t.resolve(e()).then((function(){return t.reject(n)}))}))},f.all=function(e){return new f((function(t,n){if(!d(e))return n(new TypeError("Promise.all accepts an array"));var i=Array.prototype.slice.call(e);if(0===i.length)return t([]);var r=i.length;function o(e,s){try{if(s&&("object"==typeof s||"function"==typeof s)){var a=s.then;if("function"==typeof a)return void a.call(s,(function(t){o(e,t)}),n)}i[e]=s,0==--r&&t(i)}catch(e){n(e)}}for(var s=0;s<i.length;s++)o(s,i[s])}))},f.any=function(e){var t=this;return new t((function(n,i){if(!e||void 0===e.length)return i(new TypeError("Promise.any accepts an array"));var r=Array.prototype.slice.call(e);if(0===r.length)return i();for(var o=[],s=0;s<r.length;s++)try{t.resolve(r[s]).then(n).catch((function(e){o.push(e),o.length===r.length&&i(new c(o,"All promises were rejected"))}))}catch(e){i(e)}}))},f.allSettled=function(e){return new this((function(t,n){if(!e||void 0===e.length)return n(new TypeError(typeof e+" "+e+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var i=Array.prototype.slice.call(e);if(0===i.length)return t([]);var r=i.length;function o(e,n){if(n&&("object"==typeof n||"function"==typeof n)){var s=n.then;if("function"==typeof s)return void s.call(n,(function(t){o(e,t)}),(function(n){i[e]={status:"rejected",reason:n},0==--r&&t(i)}))}i[e]={status:"fulfilled",value:n},0==--r&&t(i)}for(var s=0;s<i.length;s++)o(s,i[s])}))},f.resolve=function(e){return e&&"object"==typeof e&&e.constructor===f?e:new f((function(t){t(e)}))},f.reject=function(e){return new f((function(t,n){n(e)}))},f.race=function(e){return new f((function(t,n){if(!d(e))return n(new TypeError("Promise.race accepts an array"));for(var i=0,r=e.length;i<r;i++)f.resolve(e[i]).then(t,n)}))},f._immediateFn="function"==typeof setImmediate&&function(e){setImmediate(e)}||function(e){h(e,0)},f._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)};const w=f;function b(e,t){for(var n=e.length,i=0;i<n;++i)if(t(e[i],i))return!0;return!1}function x(e,t){for(var n=e.length,i=0;i<n;++i)if(t(e[i],i))return e[i];return null}function E(e){var t=e;if(void 0===t){if("undefined"==typeof navigator||!navigator)return"";t=navigator.userAgent||""}return t.toLowerCase()}function T(e,t){try{return new RegExp(e,"g").exec(t)}catch(e){return null}}function S(e){return e.replace(/_/g,".")}function R(e,t){var n=null,i="-1";return b(e,(function(e){var r=T("("+e.test+")((?:\\/|\\s|:)([0-9|\\.|_]+))?",t);return!(!r||e.brand||(n=e,i=r[3]||"-1",e.versionAlias?i=e.versionAlias:e.versionTest&&(i=function(e,t){var n=T("("+e+")((?:\\/|\\s|:)([0-9|\\.|_]+))",t);return n?n[3]:""}(e.versionTest.toLowerCase(),t)||i),i=S(i),0))})),{preset:n,version:i}}function M(e,t){var n={brand:"",version:"-1"};return b(e,(function(e){var i=C(t,e);return!!i&&(n.brand=e.id,n.version=e.versionAlias||i.version,"-1"!==n.version)})),n}function C(e,t){return x(e,(function(e){var n=e.brand;return T(""+t.test,n.toLowerCase())}))}var P=[{test:"phantomjs",id:"phantomjs"},{test:"whale",id:"whale"},{test:"edgios|edge|edg",id:"edge"},{test:"msie|trident|windows phone",id:"ie",versionTest:"iemobile|msie|rv"},{test:"miuibrowser",id:"miui browser"},{test:"samsungbrowser",id:"samsung internet"},{test:"samsung",id:"samsung internet",versionTest:"version"},{test:"chrome|crios",id:"chrome"},{test:"firefox|fxios",id:"firefox"},{test:"android",id:"android browser",versionTest:"version"},{test:"safari|iphone|ipad|ipod",id:"safari",versionTest:"version"}],F=[{test:"(?=.*applewebkit/(53[0-7]|5[0-2]|[0-4]))(?=.*\\schrome)",id:"chrome",versionTest:"chrome"},{test:"chromium",id:"chrome"},{test:"whale",id:"chrome",versionAlias:"-1",brand:!0}],D=[{test:"applewebkit",id:"webkit",versionTest:"applewebkit|safari"}],I=[{test:"(?=(iphone|ipad))(?!(.*version))",id:"webview"},{test:"(?=(android|iphone|ipad))(?=.*(naver|daum|; wv))",id:"webview"},{test:"webview",id:"webview"}],O=[{test:"windows phone",id:"windows phone"},{test:"windows 2000",id:"window",versionAlias:"5.0"},{test:"windows nt",id:"window"},{test:"win32|windows",id:"window"},{test:"iphone|ipad|ipod",id:"ios",versionTest:"iphone os|cpu os"},{test:"macos|macintel|mac os x",id:"mac"},{test:"android|linux armv81",id:"android"},{test:"tizen",id:"tizen"},{test:"webos|web0s",id:"webos"}];function B(e){return!!R(I,e).preset}const L=function(e){return void 0===e&&function(){if("undefined"==typeof navigator||!navigator||!navigator.userAgentData)return!1;var e=navigator.userAgentData,t=e.brands||e.uaList;return!(!t||!t.length)}()?function(e){var t=navigator.userAgentData,n=(t.uaList||t.brands).slice(),i=e&&e.fullVersionList,r=t.mobile||!1,o=n[0],s=(e&&e.platform||t.platform||navigator.platform).toLowerCase(),a={name:o.brand,version:o.version,majorVersion:-1,webkit:!1,webkitVersion:"-1",chromium:!1,chromiumVersion:"-1",webview:!!M(I,n).brand||B(E())},u={name:"unknown",version:"-1",majorVersion:-1};a.webkit=!a.chromium&&b(D,(function(e){return C(n,e)}));var l=M(F,n);if(a.chromium=!!l.brand,a.chromiumVersion=l.version,!a.chromium){var c=M(D,n);a.webkit=!!c.brand,a.webkitVersion=c.version}var h=x(O,(function(e){return new RegExp(""+e.test,"g").exec(s)}));if(u.name=h?h.id:"",e&&(u.version=e.platformVersion),i&&i.length){var d=M(P,i);a.name=d.brand||a.name,a.version=d.version||a.version}else{var p=M(P,n);a.name=p.brand||a.name,a.version=p.brand&&e?e.uaFullVersion:p.version}return a.webkit&&(u.name=r?"ios":"mac"),"ios"===u.name&&a.webview&&(a.version="-1"),u.version=S(u.version),a.version=S(a.version),u.majorVersion=parseInt(u.version,10),a.majorVersion=parseInt(a.version,10),{browser:a,os:u,isMobile:r,isHints:!0}}():function(e){var t=E(e),n=!!/mobi/g.exec(t),i={name:"unknown",version:"-1",majorVersion:-1,webview:B(t),chromium:!1,chromiumVersion:"-1",webkit:!1,webkitVersion:"-1"},r={name:"unknown",version:"-1",majorVersion:-1},o=R(P,t),s=o.preset,a=o.version,u=R(O,t),l=u.preset,c=u.version,h=R(F,t);if(i.chromium=!!h.preset,i.chromiumVersion=h.version,!i.chromium){var d=R(D,t);i.webkit=!!d.preset,i.webkitVersion=d.version}return l&&(r.name=l.id,r.version=c,r.majorVersion=parseInt(c,10)),s&&(i.name=s.id,i.version=a,i.webview&&"ios"===r.name&&"safari"!==i.name&&(i.webview=!1)),i.majorVersion=parseInt(i.version,10),{browser:i,os:r,isMobile:n,isHints:!1}}(e)};var V=function(){function e(e){this._emitter=new l,this._current=e}var t=e.prototype;return Object.defineProperty(t,"current",{get:function(){return this._current},set:function(e){var t=e!==this._current;this._current=e,t&&this._emitter.trigger("update",e)},enumerable:!1,configurable:!0}),t.subscribe=function(e){this._emitter.on("update",e)},t.unsubscribe=function(e){this._emitter.off("update",e)},e}();function k(e){return e.__observers__||(e.__observers__={}),e.__observers__}function N(e,t,n){var i=k(e);return i[t]||(i[t]=function(e){return new V(e)}(n)),i[t]}function Q(e){e.subscribe=function(e,t){N(this,e).subscribe(t)},e.unsubscribe=function(e,t){var n,i=this;e?e in this&&N(this,e).unsubscribe(t):(n=k(this),Object.keys(n)).forEach((function(e){i.unsubscribe(e)}))}}function U(e){Q(e.prototype)}var W=function(e,t){return(W=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)};function G(e,t){function n(){this.constructor=e}W(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var j,z=function(){return(z=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};j="undefined"==typeof window?{navigator:{userAgent:""}}:window;var X=24,H="left",q="right",Y="middle",Z="any",K="ontouchstart"in j&&"safari"===L().browser.name,$=function(){if("undefined"==typeof document)return"";for(var e=(document.head||document.getElementsByTagName("head")[0]).style,t=["transform","webkitTransform","msTransform","mozTransform"],n=0,i=t.length;n<i;n++)if(t[n]in e)return t[n];return""}(),J={"-webkit-user-select":"none","-ms-user-select":"none","-moz-user-select":"none","user-select":"none","-webkit-user-drag":"none"},ee=function(e){for(var t=[],n=0,i=e.length;n<i;n++)t.push(e[n]);return t},te=function(e,t){var n;if(void 0===t&&(t=!1),"string"==typeof e){if(e.match(/^<([a-z]+)\s*([^>]*)>/)){var i=document.createElement("div");i.innerHTML=e,n=ee(i.childNodes)}else n=ee(document.querySelectorAll(e));t||(n=n.length>=1?n[0]:void 0)}else e===j?n=e:"value"in e||"current"in e?n=e.value||e.current:!e.nodeName||1!==e.nodeType&&9!==e.nodeType?"jQuery"in j&&e instanceof jQuery||e.constructor.prototype.jquery?n=t?e.toArray():e.get(0):Array.isArray(e)&&(n=e.map((function(e){return te(e)})),t||(n=n.length>=1?n[0]:void 0)):n=e;return n},ne=j.requestAnimationFrame||j.webkitRequestAnimationFrame,ie=j.cancelAnimationFrame||j.webkitCancelAnimationFrame;if(ne&&!ie){var re={},oe=ne;ne=function(e){var t=oe((function(n){re[t]&&e(n)}));return re[t]=!0,t},ie=function(e){delete re[e]}}else ne&&ie||(ne=function(e){return j.setTimeout((function(){e(j.performance&&j.performance.now&&j.performance.now()||(new Date).getTime())}),16)},ie=j.clearTimeout);var se=function(e,t){var n={};for(var i in e)i&&(n[i]=t(e[i],i));return n},ae=function(e,t){var n={};for(var i in e)i&&t(e[i],i)&&(n[i]=e[i]);return n},ue=function(e,t){for(var n in e)if(n&&!t(e[n],n))return!1;return!0},le=function(e,t){return ue(e,(function(e,n){return e===t[n]}))},ce={},he=function(e,t){return ce[t]||(ce[t]=fe(t)),ce[t](e)},de=function(e,t){return e&&t?se(e,(function(e,n){return he(e,"number"==typeof t?t:t[n])})):e},pe=function(e){if(!isFinite(e))return 0;var t="".concat(e);if(t.indexOf("e")>=0){for(var n=0,i=1;Math.round(e*i)/i!==e;)i*=10,n++;return n}return t.indexOf(".")>=0?t.length-t.indexOf(".")-1:0},fe=function(e){var t=e<1?Math.pow(10,pe(e)):1;return function(n){return 0===e?0:Math.round(Math.round(n/e)*e*t)/t}},Ae=function(e){var t=!0;return Object.keys(J).forEach((function(n){e&&e[n]===J[n]||(t=!1)})),t},ge=function(e,t){return e&&t?30:e?6:t?X:1},ve=function(e,t,n){return n?!!(30===t||t&e&&n&e):!!(t&e)},me=function(e,t,n){var i,r=((i={})[1]="auto",i[30]="none",i[24]="pan-x",i[6]="pan-y",i),o={};if(e&&e.style){var s=t.touchAction?t.touchAction:r[n],a=z(z({},J),{"touch-action":"none"===e.style["touch-action"]?"none":s});Object.keys(a).forEach((function(t){o[t]=e.style[t],e.style[t]=a[t]}))}return o},ye=function(e,t){e&&e.style&&t&&Object.keys(t).forEach((function(n){e.style[n]=t[n]}))},_e=function(){function e(e){this._axes=e}var t=e.prototype;return t.hold=function(e,t){var n=this._getRoundPos(e).roundPos;this._axes.trigger(new u("hold",{pos:n,input:t.input||null,inputEvent:t.event||null,isTrusted:!0}))},t.triggerRelease=function(e){var t=this._getRoundPos(e.destPos,e.depaPos),n=t.roundPos,i=t.roundDepa;e.destPos=n,e.depaPos=i,e.setTo=this._createUserControll(e.destPos,e.duration),this._axes.trigger(new u("release",z(z({},e),{bounceRatio:this._getBounceRatio(n)})))},t.triggerChange=function(e,t,n,i){var r=this;void 0===i&&(i=!1);var o=this.animationManager,s=o.axisManager,a=o.getEventInfo(),l=this._getRoundPos(e,t),c=l.roundPos,h=l.roundDepa,d=s.moveTo(c,h),p=(null==n?void 0:n.event)||(null==a?void 0:a.event)||null,f={pos:d.pos,delta:d.delta,bounceRatio:this._getBounceRatio(d.pos),holding:i,inputEvent:p,isTrusted:!!p,input:(null==n?void 0:n.input)||(null==a?void 0:a.input)||null,set:p?this._createUserControll(d.pos):function(){}},A=new u("change",f);return this._axes.trigger(A),Object.keys(d.pos).forEach((function(e){var t=d.pos[e];N(r._axes,e,t).current=t})),p&&s.set(f.set().destPos),!A.isCanceled()},t.triggerAnimationStart=function(e){var t=this._getRoundPos(e.destPos,e.depaPos),n=t.roundPos,i=t.roundDepa;e.destPos=n,e.depaPos=i,e.setTo=this._createUserControll(e.destPos,e.duration);var r=new u("animationStart",e);return this._axes.trigger(r),!r.isCanceled()},t.triggerAnimationEnd=function(e){void 0===e&&(e=!1),this._axes.trigger(new u("animationEnd",{isTrusted:e}))},t.triggerFinish=function(e){void 0===e&&(e=!1),this._axes.trigger(new u("finish",{isTrusted:e}))},t.setAnimationManager=function(e){this.animationManager=e},t.destroy=function(){this._axes.off()},t._createUserControll=function(e,t){void 0===t&&(t=0);var n={destPos:z({},e),duration:t};return function(e,t){return e&&(n.destPos=z({},e)),void 0!==t&&(n.duration=t),n}},t._getRoundPos=function(e,t){var n=this._axes.options.round;return{roundPos:de(e,n),roundDepa:de(t,n)}},t._getBounceRatio=function(e){return this._axes.axisManager.map(e,(function(e,t){return e<t.range[0]&&0!==t.bounce[0]?(t.range[0]-e)/t.bounce[0]:e>t.range[1]&&0!==t.bounce[1]?(e-t.range[1])/t.bounce[1]:0}))},e}(),we=function(){function e(e){this._options=e,this._prevented=!1}var t=e.prototype;return t.isInterrupting=function(){return this._options.interruptable||this._prevented},t.isInterrupted=function(){return!this._options.interruptable&&this._prevented},t.setInterrupt=function(e){this._options.interruptable||(this._prevented=e)},e}(),be=function(e,t,n,i){var r=e,o=[n[0]?t[0]:i?t[0]-i[0]:t[0],n[1]?t[1]:i?t[1]+i[1]:t[1]];return r=Math.max(o[0],r),Math.min(o[1],r)},xe=function(e,t){return e<t[0]||e>t[1]},Ee=function(e,t,n){return n[1]&&e>t[1]||n[0]&&e<t[0]},Te=function(e,t,n){var i=e,r=t[0],o=t[1],s=o-r;return n[1]&&e>o&&(i=(i-o)%s+r),n[0]&&e<r&&(i=(i-r)%s+o),i},Se=function(){function e(e){var t=this;this._axis=e,this._complementOptions(),this._pos=Object.keys(this._axis).reduce((function(e,n){return e[n]=t._axis[n].startPos,e}),{})}var t=e.prototype;return t.getDelta=function(e,t){var n=this.get(e);return se(this.get(t),(function(e,t){return e-n[t]}))},t.get=function(e){var t=this;return e&&Array.isArray(e)?e.reduce((function(e,n){return n&&n in t._pos&&(e[n]=t._pos[n]),e}),{}):z(z({},this._pos),e||{})},t.moveTo=function(e,t){void 0===t&&(t=this._pos);var n=se(this._pos,(function(n,i){return i in e&&i in t?e[i]-t[i]:0}));return this.set(this.map(e,(function(e,t){return t?Te(e,t.range,t.circular):0}))),{pos:z({},this._pos),delta:n}},t.set=function(e){for(var t in e)t&&t in this._pos&&(this._pos[t]=e[t])},t.every=function(e,t){var n=this._axis;return ue(e,(function(e,i){return t(e,n[i],i)}))},t.filter=function(e,t){var n=this._axis;return ae(e,(function(e,i){return t(e,n[i],i)}))},t.map=function(e,t){var n=this._axis;return se(e,(function(e,i){return t(e,n[i],i)}))},t.isOutside=function(e){return!this.every(e?this.get(e):this._pos,(function(e,t){return!xe(e,t.range)}))},t.getAxisOptions=function(e){return this._axis[e]},t.setAxis=function(e){var t=this;Object.keys(e).forEach((function(n){if(!t._axis[n])throw new Error("Axis ".concat(n," does not exist in Axes instance"));t._axis[n]=z(z({},t._axis[n]),e[n])})),this._complementOptions()},t._complementOptions=function(){var e=this;Object.keys(this._axis).forEach((function(t){e._axis[t]=z({range:[0,100],startPos:e._axis[t].range[0],bounce:[0,0],circular:[!1,!1]},e._axis[t]),["bounce","circular"].forEach((function(n){var i=e._axis,r=i[t][n];/string|number|boolean/.test(typeof r)&&(i[t][n]=[r,r])}))}))},e}(),Re="ontouchstart"in j,Me="PointerEvent"in j,Ce=Me||"MSPointerEvent"in j,Pe=function(e,t){return!!(!t||t.indexOf(Z)>-1||t.indexOf("none")>-1&&!e.shiftKey&&!e.ctrlKey&&!e.altKey&&!e.metaKey||t.indexOf("shift")>-1&&e.shiftKey||t.indexOf("ctrl")>-1&&e.ctrlKey||t.indexOf("alt")>-1&&e.altKey||t.indexOf("meta")>-1&&e.metaKey)},Fe=function(){function e(){var e=this;this._stopContextMenu=function(t){t.preventDefault(),j.removeEventListener("contextmenu",e._stopContextMenu)}}var t=e.prototype;return t.extendEvent=function(e){var t,n,i,r=this.prevEvent,o=this._getCenter(e),s=r?this._getMovement(e):{x:0,y:0},a=r?this._getScale(e):1,u=r?(n=o.x-r.center.x,i=o.y-r.center.y,180*Math.atan2(i,n)/Math.PI):0,l=r?r.deltaX+s.x:s.x,c=r?r.deltaY+s.y:s.y,h=s.x,d=s.y,p=this._latestInterval,f=Date.now(),A=p?f-p.timestamp:0,g=r?r.velocityX:0,v=r?r.velocityY:0;return(!p||A>=16)&&(p&&(g=(t=[(l-p.deltaX)/A,(c-p.deltaY)/A])[0],v=t[1]),this._latestInterval={timestamp:f,deltaX:l,deltaY:c}),{srcEvent:e,scale:a,angle:u,center:o,deltaX:l,deltaY:c,offsetX:h,offsetY:d,velocityX:g,velocityY:v,preventSystemEvent:!0}},t._getDistance=function(e,t){var n=t.clientX-e.clientX,i=t.clientY-e.clientY;return Math.sqrt(n*n+i*i)},t._getButton=function(e){var t={1:H,2:q,4:Y};return(this._isTouchEvent(e)?H:t[e.buttons])||null},t._isTouchEvent=function(e){return e.type&&e.type.indexOf("touch")>-1},t._isValidButton=function(e,t){return t.indexOf(e)>-1},t._isValidEvent=function(e,t,n){return(!t||Pe(e,t))&&(!n||this._isValidButton(this._getButton(e),n))},t._preventMouseButton=function(e,t){t===q?j.addEventListener("contextmenu",this._stopContextMenu):t===Y&&e.preventDefault()},e}(),De=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.start=["mousedown"],t.move=["mousemove"],t.end=["mouseup"],t}G(t,e);var n=t.prototype;return n.onEventStart=function(e,t,n){var i=this._getButton(e);return this._isValidEvent(e,t,n)?(this._preventMouseButton(e,i),this.extendEvent(e)):null},n.onEventMove=function(e,t,n){return this._isValidEvent(e,t,n)?this.extendEvent(e):null},n.onEventEnd=function(){},n.onRelease=function(){this.prevEvent=null},n.getTouches=function(e,t){if(t){var n={1:H,2:Y,3:q};return this._isValidButton(n[e.which],t)&&-1===this.end.indexOf(e.type)?1:0}return 0},n._getScale=function(){return 1},n._getCenter=function(e){return{x:e.clientX,y:e.clientY}},n._getMovement=function(e){var t=this.prevEvent.srcEvent;return{x:e.clientX-t.clientX,y:e.clientY-t.clientY}},t}(Fe),Ie=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.start=["touchstart"],t.move=["touchmove"],t.end=["touchend","touchcancel"],t}G(t,e);var n=t.prototype;return n.onEventStart=function(e,t){return this._baseTouches=e.touches,this._isValidEvent(e,t)?this.extendEvent(e):null},n.onEventMove=function(e,t){return this._isValidEvent(e,t)?this.extendEvent(e):null},n.onEventEnd=function(e){this._baseTouches=e.touches},n.onRelease=function(){this.prevEvent=null,this._baseTouches=null},n.getTouches=function(e){return e.touches.length},n._getScale=function(e){return 2!==e.touches.length||this._baseTouches.length<2?null:this._getDistance(e.touches[0],e.touches[1])/this._getDistance(this._baseTouches[0],this._baseTouches[1])},n._getCenter=function(e){return{x:e.touches[0].clientX,y:e.touches[0].clientY}},n._getMovement=function(e){var t=this.prevEvent.srcEvent;return e.touches[0].identifier!==t.touches[0].identifier?{x:0,y:0}:{x:e.touches[0].clientX-t.touches[0].clientX,y:e.touches[0].clientY-t.touches[0].clientY}},t}(Fe),Oe=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.start=Me?["pointerdown"]:["MSPointerDown"],t.move=Me?["pointermove"]:["MSPointerMove"],t.end=Me?["pointerup","pointercancel"]:["MSPointerUp","MSPointerCancel"],t._firstInputs=[],t._recentInputs=[],t}G(t,e);var n=t.prototype;return n.onEventStart=function(e,t,n){var i=this._getButton(e);return this._isValidEvent(e,t,n)?(this._preventMouseButton(e,i),this._updatePointerEvent(e),this.extendEvent(e)):null},n.onEventMove=function(e,t,n){return this._isValidEvent(e,t,n)?(this._updatePointerEvent(e),this.extendEvent(e)):null},n.onEventEnd=function(e){this._removePointerEvent(e)},n.onRelease=function(){this.prevEvent=null,this._firstInputs=[],this._recentInputs=[]},n.getTouches=function(){return this._recentInputs.length},n._getScale=function(){return 2!==this._recentInputs.length?null:this._getDistance(this._recentInputs[0],this._recentInputs[1])/this._getDistance(this._firstInputs[0],this._firstInputs[1])},n._getCenter=function(e){return{x:e.clientX,y:e.clientY}},n._getMovement=function(e){var t=this.prevEvent.srcEvent;return e.pointerId!==t.pointerId?{x:0,y:0}:{x:e.clientX-t.clientX,y:e.clientY-t.clientY}},n._updatePointerEvent=function(e){var t=this,n=!1;this._recentInputs.forEach((function(i,r){i.pointerId===e.pointerId&&(n=!0,t._recentInputs[r]=e)})),n||(this._firstInputs.push(e),this._recentInputs.push(e))},n._removePointerEvent=function(e){this._firstInputs=this._firstInputs.filter((function(t){return t.pointerId!==e.pointerId})),this._recentInputs=this._recentInputs.filter((function(t){return t.pointerId!==e.pointerId}))},t}(Fe),Be=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.start=["mousedown","touchstart"],t.move=["mousemove","touchmove"],t.end=["mouseup","touchend","touchcancel"],t}G(t,e);var n=t.prototype;return n.onEventStart=function(e,t,n){var i=this._getButton(e);return this._isTouchEvent(e)&&(this._baseTouches=e.touches),this._isValidEvent(e,t,n)?(this._preventMouseButton(e,i),this.extendEvent(e)):null},n.onEventMove=function(e,t,n){return this._isValidEvent(e,t,n)?this.extendEvent(e):null},n.onEventEnd=function(e){this._isTouchEvent(e)&&(this._baseTouches=e.touches)},n.onRelease=function(){this.prevEvent=null,this._baseTouches=null},n.getTouches=function(e){return this._isTouchEvent(e)?e.touches.length:0},n._getScale=function(e){return this._isTouchEvent(e)?2!==e.touches.length||this._baseTouches.length<2?1:this._getDistance(e.touches[0],e.touches[1])/this._getDistance(this._baseTouches[0],this._baseTouches[1]):this.prevEvent.scale},n._getCenter=function(e){return this._isTouchEvent(e)?{x:e.touches[0].clientX,y:e.touches[0].clientY}:{x:e.clientX,y:e.clientY}},n._getMovement=function(e){var t=this,n=[e,this.prevEvent.srcEvent].map((function(e){return t._isTouchEvent(e)?{id:e.touches[0].identifier,x:e.touches[0].clientX,y:e.touches[0].clientY}:{id:null,x:e.clientX,y:e.clientY}})),i=n[0],r=n[1];return i.id===r.id?{x:i.x-r.x,y:i.y-r.y}:{x:0,y:0}},t}(Fe),Le=function(e,t){return t.reduce((function(t,n,i){return e[i]&&(t[e[i]]=n),t}),{})},Ve=function(e){void 0===e&&(e=[]);var t=!1,n=!1,i=!1;return e.forEach((function(e){switch(e){case"mouse":n=!0;break;case"touch":t=Re;break;case"pointer":i=Ce}})),i?new Oe:t&&n?new Be:t?new Ie:n?new De:null};function ke(e){return e.indexOf("touch")>-1&&{passive:!1}}var Ne=function(){function e(e){var t=e.options,n=e.interruptManager,i=e.eventManager,r=e.axisManager,o=e.animationManager;this._isOutside=!1,this._moveDistance=null,this._isStopped=!1,this.options=t,this._interruptManager=n,this._eventManager=i,this._axisManager=r,this._animationManager=o}var t=e.prototype;return t.get=function(e){return this._axisManager.get(e.axes)},t.hold=function(e,t){if(!this._interruptManager.isInterrupted()&&e.axes.length){var n={input:e,event:t};this._isStopped=!1,this._interruptManager.setInterrupt(!0),this._animationManager.stopAnimation(n),this._moveDistance||this._eventManager.hold(this._axisManager.get(),n),this._isOutside=this._axisManager.isOutside(e.axes),this._moveDistance=this._axisManager.get(e.axes)}},t.change=function(e,t,n,i){if(!this._isStopped&&this._interruptManager.isInterrupting()&&!this._axisManager.every(n,(function(e){return 0===e}))){var r=t.srcEvent?t.srcEvent:t;if(!r.__childrenAxesAlreadyChanged){var o,s=this._moveDistance||this._axisManager.get(e.axes);o=se(s,(function(e,t){return e+(n[t]||0)})),this._moveDistance&&(this._moveDistance=this._axisManager.map(o,(function(e,t){var n=t.circular,i=t.range;return n&&(n[0]||n[1])?Te(e,i,n):e}))),this._isOutside&&this._axisManager.every(s,(function(e,t){return!xe(e,t.range)}))&&(this._isOutside=!1),s=this._atOutside(s),o=this._atOutside(o),this.options.nested&&this._isEndofAxis(n,s,o)||(r.__childrenAxesAlreadyChanged=!0);var a={input:e,event:t};if(i){var u=this._animationManager.getDuration(o,s);this._animationManager.animateTo(o,u,a)}else!this._eventManager.triggerChange(o,s,a,!0)&&(this._isStopped=!0,this._moveDistance=null,this._animationManager.finish(!1))}}},t.release=function(e,t,n,i){if(!this._isStopped&&this._interruptManager.isInterrupting()&&this._moveDistance){var r=t.srcEvent?t.srcEvent:t;r.__childrenAxesAlreadyReleased&&(n=n.map((function(){return 0})));var o=this._axisManager.get(e.axes),s=this._axisManager.get(),a=this._animationManager.getDisplacement(n),u=Le(e.axes,a),l=this._axisManager.get(this._axisManager.map(u,(function(e,t,n){return t.circular&&(t.circular[0]||t.circular[1])?o[n]+e:be(o[n]+e,t.range,t.circular,t.bounce)})));r.__childrenAxesAlreadyReleased=!0;var c=this._animationManager.getDuration(l,o,i);0===c&&(l=z({},s));var h={depaPos:s,destPos:l,duration:c,delta:this._axisManager.getDelta(s,l),inputEvent:t,input:e,isTrusted:!0};this._eventManager.triggerRelease(h),this._moveDistance=null;var d=this._animationManager.getUserControl(h),p=le(d.destPos,s),f={input:e,event:t};p||0===d.duration?(p||this._eventManager.triggerChange(d.destPos,s,f,!0),this._interruptManager.setInterrupt(!1),this._axisManager.isOutside()?this._animationManager.restore(f):this._eventManager.triggerFinish(!0)):this._animationManager.animateTo(d.destPos,d.duration,f)}},t._atOutside=function(e){var t=this;return this._isOutside?this._axisManager.map(e,(function(e,t){var n=t.range[0]-t.bounce[0],i=t.range[1]+t.bounce[1];return e>i?i:e<n?n:e})):this._axisManager.map(e,(function(e,n){var i=n.range[0],r=n.range[1],o=n.bounce,s=n.circular;return s[0]&&e<i||s[1]&&e>r?e:e<i?i-t._animationManager.interpolate(i-e,o[0]):e>r?r+t._animationManager.interpolate(e-r,o[1]):e}))},t._isEndofAxis=function(e,t,n){return this._axisManager.every(t,(function(i,r,o){return 0===e[o]||t[o]===n[o]&&(s=i,a=r.range,u=r.bounce,!(l=r.circular)[0]&&s===a[0]-u[0]||!l[1]&&s===a[1]+u[1]);var s,a,u,l}))},e}(),Qe=function(e,t,n){return Math.max(Math.min(e,n),t)},Ue=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._useDuration=!0,t}G(t,e);var n=t.prototype;return n.interpolate=function(e,t){var n=this._easing(1e-5)/1e-5;return this._easing(e/(t*n))*t},n.updateAnimation=function(e){var t,n=this._animateParam;if(n){var i=(new Date).getTime()-n.startTime,r=(null==e?void 0:e.destPos)||n.destPos,o=null!==(t=null==e?void 0:e.duration)&&void 0!==t?t:n.duration;if((null==e?void 0:e.restart)||o<=i)this.setTo(r,o-i);else{if(null==e?void 0:e.destPos){var s=this.axisManager.get();this._initialEasingPer=this._prevEasingPer,n.delta=this.axisManager.getDelta(s,r),n.destPos=r}if(null==e?void 0:e.duration){var a=(i+this._durationOffset)/n.duration;this._durationOffset=a*o-i,n.duration=o}}}},n._initState=function(e){return this._initialEasingPer=0,this._prevEasingPer=0,this._durationOffset=0,{pos:e.depaPos,easingPer:0,finished:!1}},n._getNextState=function(e){var t=this,n=this._animateParam,i=e.pos,r=n.destPos,o=se(i,(function(e,t){return e<=r[t]?1:-1})),s=((new Date).getTime()-n.startTime+this._durationOffset)/n.duration,a=this._easing(s),u=this.axisManager.map(i,(function(e,u,l){var c=s>=1?r[l]:e+n.delta[l]*(a-t._prevEasingPer)/(1-t._initialEasingPer),h=Te(c,u.range,u.circular);if(c!==h){var d=o[l]*(u.range[1]-u.range[0]);r[l]-=d,i[l]-=d}return h}));return this._prevEasingPer=a,{pos:u,easingPer:a,finished:a>=1}},n._easing=function(e){return e>1?1:this._options.easing(e)},t}(function(){function e(e){var t=e.options,n=e.interruptManager,i=e.eventManager,r=e.axisManager;this._options=t,this.interruptManager=n,this.eventManager=i,this.axisManager=r,this.animationEnd=this.animationEnd.bind(this)}var t=e.prototype;return t.getDuration=function(e,t,n){var i,r=this;if(void 0!==n)i=n;else{var o=se(t,(function(t,n){return function(e,t){var n=Math.sqrt(e/t*2);return n<100?0:n}(Math.abs(t-e[n]),r._options.deceleration)}));i=Object.keys(o).reduce((function(e,t){return Math.max(e,o[t])}),-1/0)}return Qe(i,this._options.minimumDuration,this._options.maximumDuration)},t.getDisplacement=function(e){var t=Math.pow(e.reduce((function(e,t){return e+t*t}),0),1/e.length),n=Math.abs(t/-this._options.deceleration);return e.map((function(e){return e/2*n}))},t.stopAnimation=function(e){if(this._animateParam){var t=this.axisManager.get(),n=this.axisManager.map(t,(function(e,t){return Te(e,t.range,t.circular)}));ue(n,(function(e,n){return t[n]===e}))||this.eventManager.triggerChange(n,t,e,!!e),this._animateParam=null,this._raf&&(i=this._raf,ie(i)),this._raf=null,this.eventManager.triggerAnimationEnd(!!(null==e?void 0:e.event))}var i},t.getEventInfo=function(){return this._animateParam&&this._animateParam.input&&this._animateParam.inputEvent?{input:this._animateParam.input,event:this._animateParam.inputEvent}:null},t.restore=function(e){var t=this.axisManager.get(),n=this.axisManager.map(t,(function(e,t){return Math.min(t.range[1],Math.max(t.range[0],e))}));this.stopAnimation(),this.animateTo(n,this.getDuration(t,n),e)},t.animationEnd=function(){var e=this.getEventInfo();this._animateParam=null;var t=this.axisManager.filter(this.axisManager.get(),(function(e,t){return Ee(e,t.range,t.circular)}));Object.keys(t).length>0&&this.setTo(this.axisManager.map(t,(function(e,t){return Te(e,t.range,t.circular)}))),this.interruptManager.setInterrupt(!1),this.eventManager.triggerAnimationEnd(!!e),this.axisManager.isOutside()?this.restore(e):this.finish(!!e)},t.finish=function(e){this._animateParam=null,this.interruptManager.setInterrupt(!1),this.eventManager.triggerFinish(e)},t.getUserControl=function(e){var t=e.setTo();return t.destPos=this.axisManager.get(t.destPos),t.duration=Qe(t.duration,this._options.minimumDuration,this._options.maximumDuration),t},t.animateTo=function(e,t,n){var i=this;this.stopAnimation();var r=this._createAnimationParam(e,t,n),o=z({},r.depaPos),s=this.eventManager.triggerAnimationStart(r),a=this.getUserControl(r);if(!s&&this.axisManager.every(a.destPos,(function(e,t){return Ee(e,t.range,t.circular)}))&&console.warn("You can't stop the 'animation' event when 'circular' is true."),s&&!le(a.destPos,o)){var u=(null==n?void 0:n.event)||null;this._animateLoop({depaPos:o,destPos:a.destPos,duration:a.duration,delta:this.axisManager.getDelta(o,a.destPos),isTrusted:!!u,inputEvent:u,input:(null==n?void 0:n.input)||null},(function(){return i.animationEnd()}))}},t.setTo=function(e,t){void 0===t&&(t=0);var n=Object.keys(e),i=this.axisManager.get(n);if(le(e,i))return this;this.interruptManager.setInterrupt(!0);var r=ae(e,(function(e,t){return i[t]!==e}));return Object.keys(r).length?(r=this.axisManager.map(r,(function(e,t){var n=t.range,i=t.circular;return i&&(i[0]||i[1])?e:be(e,n,i)})),le(r,i)||(t>0?this.animateTo(r,t):(this.stopAnimation(),this.eventManager.triggerChange(r),this.finish(!1))),this):this},t.setBy=function(e,t){return void 0===t&&(t=0),this.setTo(se(this.axisManager.get(Object.keys(e)),(function(t,n){return t+e[n]})),t)},t._createAnimationParam=function(e,t,n){var i=this.axisManager.get(),r=e,o=(null==n?void 0:n.event)||null;return{depaPos:i,destPos:r,duration:Qe(t,this._options.minimumDuration,this._options.maximumDuration),delta:this.axisManager.getDelta(i,r),inputEvent:o,input:(null==n?void 0:n.input)||null,isTrusted:!!o,done:this.animationEnd}},t._animateLoop=function(e,t){var n=this;if(e.duration){this._animateParam=z(z({},e),{startTime:(new Date).getTime()});var i=se(e.destPos,(function(e){return e})),r=this._initState(this._animateParam),o=function(){n._raf=null;var e=n._animateParam,s=n._getNextState(r),a=!n.eventManager.triggerChange(s.pos,r.pos);if(r=s,s.finished)return e.destPos=n._getFinalPos(e.destPos,i),le(e.destPos,n.axisManager.get(Object.keys(e.destPos)))||n.eventManager.triggerChange(e.destPos,s.pos),void t();a?n.finish(!1):n._raf=ne(o)};o()}else this.eventManager.triggerChange(e.destPos),t()},t._getFinalPos=function(e,t){var n=this,i=1e-6;return se(e,(function(e,r){if(e>=t[r]-i&&e<=t[r]+i)return t[r];var o=n._getRoundUnit(e,r);return he(e,o)}))},t._getRoundUnit=function(e,t){var n,i=this._options.round,r=null;if(!i){var o=this.axisManager.getAxisOptions(t);n=Math.max(pe(o.range[0]),pe(o.range[1]),pe(e)),r=1/Math.pow(10,n)}return r||i},e}()),We=function(e){function t(t,n,i){void 0===t&&(t={}),void 0===n&&(n={}),void 0===i&&(i={});var r=e.call(this)||this;return r.axis=t,r._inputs=[],r.options=z({easing:function(e){return 1-Math.pow(1-e,3)},interruptable:!0,maximumDuration:1/0,minimumDuration:0,deceleration:6e-4,round:null,nested:!1},n),Object.keys(i).forEach((function(e){r.axis[e].startPos=i[e]})),r.interruptManager=new we(r.options),r.axisManager=new Se(r.axis),r.eventManager=new _e(r),r.animationManager=new Ue(r),r.inputObserver=new Ne(r),r.eventManager.setAnimationManager(r.animationManager),r.eventManager.triggerChange(r.axisManager.get()),r}G(t,e);var n=t.prototype;return n.connect=function(e,t){var n;return n="string"==typeof e?e.split(" "):e.concat(),~this._inputs.indexOf(t)&&this.disconnect(t),t.mapAxes(n),t.connect(this.inputObserver),this._inputs.push(t),this},n.disconnect=function(e){if(e){var t=this._inputs.indexOf(e);t>=0&&(this._inputs[t].disconnect(),this._inputs.splice(t,1))}else this._inputs.forEach((function(e){return e.disconnect()})),this._inputs=[];return this},n.get=function(e){return this.axisManager.get(e)},n.setTo=function(e,t){return void 0===t&&(t=0),this.animationManager.setTo(e,t),this},n.setBy=function(e,t){return void 0===t&&(t=0),this.animationManager.setBy(e,t),this},n.setOptions=function(e){return this.options=z(z({},this.options),e),this},n.setAxis=function(e){return this.axisManager.setAxis(e),this},n.stopAnimation=function(){return this.animationManager.stopAnimation(),this.animationManager.finish(!1),this},n.updateAnimation=function(e){return this.animationManager.updateAnimation(e),this},n.isBounceArea=function(e){return this.axisManager.isOutside(e)},n.destroy=function(){this.disconnect(),this.eventManager.destroy()},t.VERSION="3.8.4",t.TRANSFORM=$,t.DIRECTION_NONE=1,t.DIRECTION_LEFT=2,t.DIRECTION_RIGHT=4,t.DIRECTION_UP=8,t.DIRECTION_DOWN=16,t.DIRECTION_HORIZONTAL=6,t.DIRECTION_VERTICAL=X,t.DIRECTION_ALL=30,function(e,t,n,i){var r,o=arguments.length,s=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,n,s):r(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s}([U],t)}(l),Ge=function(){function e(e,t){var n=this;this.axes=[],this.element=null,this._enabled=!1,this._activeEvent=null,this._atRightEdge=!1,this._rightEdgeTimer=0,this._dragged=!1,this._isOverThreshold=!1,this._preventClickWhenDragged=function(e){n._dragged&&(e.preventDefault(),e.stopPropagation()),n._dragged=!1},this._voidFunction=function(){},this.element=te(e),this.options=z({inputType:["touch","mouse","pointer"],inputKey:[Z],inputButton:[H],scale:[1,1],thresholdAngle:45,threshold:0,preventClickOnDrag:!1,iOSEdgeSwipeThreshold:30,releaseOnScroll:!1,touchAction:null},t),this._onPanstart=this._onPanstart.bind(this),this._onPanmove=this._onPanmove.bind(this),this._onPanend=this._onPanend.bind(this)}var t=e.prototype;return t.mapAxes=function(e){this._direction=ge(!!e[0],!!e[1]),this.axes=e},t.connect=function(e){return this._activeEvent&&(this._detachElementEvent(),this._detachWindowEvent(this._activeEvent)),this._attachElementEvent(e),this._originalCssProps=me(this.element,this.options,this._direction),this},t.disconnect=function(){return this._detachElementEvent(),this._detachWindowEvent(this._activeEvent),Ae(this._originalCssProps)||ye(this.element,this._originalCssProps),this._direction=1,this},t.destroy=function(){this.disconnect(),this.element=null},t.enable=function(){return this._enabled=!0,this},t.disable=function(){return this._enabled=!1,this},t.isEnabled=function(){return this._enabled},t.release=function(){var e=this._activeEvent,t=e.prevEvent;return e.onRelease(),this._observer.release(this,t,[0,0]),this._detachWindowEvent(e),this},t._onPanstart=function(e){var t=this.options,n=t.inputKey,i=t.inputButton,r=this._activeEvent,o=r.onEventStart(e,n,i);if(o&&this._enabled&&!(r.getTouches(e,i)>1)&&!1!==o.srcEvent.cancelable){var s=this.options.iOSEdgeSwipeThreshold;this._dragged=!1,this._isOverThreshold=!1,this._observer.hold(this,o),this._atRightEdge=K&&o.center.x>window.innerWidth-s,this._attachWindowEvent(r),r.prevEvent=o}},t._onPanmove=function(e){var t=this,n=this.options,i=n.iOSEdgeSwipeThreshold,r=n.preventClickOnDrag,o=n.releaseOnScroll,s=n.inputKey,a=n.inputButton,u=n.threshold,l=n.thresholdAngle,c=this._activeEvent,h=c.onEventMove(e,s,a),d=c.getTouches(e,a);if(0===d||o&&h&&!h.srcEvent.cancelable)this._onPanend(e);else if(h&&this._enabled&&!(d>1)){var p=function(e,t){if(t<0||t>90)return 1;var n=Math.abs(e);return n>t&&n<180-t?X:6}(h.angle,l),f=ve(6,this._direction,p),A=ve(X,this._direction,p);if(c.prevEvent&&K){if(h.center.x<0)return void this.release();this._atRightEdge&&(clearTimeout(this._rightEdgeTimer),h.deltaX<-i?this._atRightEdge=!1:this._rightEdgeTimer=window.setTimeout((function(){return t.release()}),100))}var g=this._getDistance([h.deltaX,h.deltaY],[f,A]),v=this._getOffset([h.offsetX,h.offsetY],[f,A]),m=v.some((function(e){return 0!==e}));m&&(!1!==h.srcEvent.cancelable&&h.srcEvent.preventDefault(),h.srcEvent.stopPropagation()),h.preventSystemEvent=m,m&&(this._isOverThreshold||g>=u)&&(this._dragged=r,this._isOverThreshold=!0,this._observer.change(this,h,Le(this.axes,v))),c.prevEvent=h}},t._onPanend=function(e){var t=this.options.inputButton,n=this._activeEvent;if(n.onEventEnd(e),this._enabled&&0===n.getTouches(e,t)){this._detachWindowEvent(n),clearTimeout(this._rightEdgeTimer);var i=n.prevEvent,r=this._isOverThreshold?this._getOffset([Math.abs(i.velocityX)*(i.offsetX<0?-1:1),Math.abs(i.velocityY)*(i.offsetY<0?-1:1)],[ve(6,this._direction),ve(X,this._direction)]):[0,0];n.onRelease(),this._observer.release(this,i,r)}},t._attachWindowEvent=function(e){var t=this;null==e||e.move.forEach((function(e){window.addEventListener(e,t._onPanmove,ke(e))})),null==e||e.end.forEach((function(e){window.addEventListener(e,t._onPanend,ke(e))}))},t._detachWindowEvent=function(e){var t=this;null==e||e.move.forEach((function(e){window.removeEventListener(e,t._onPanmove)})),null==e||e.end.forEach((function(e){window.removeEventListener(e,t._onPanend)}))},t._getOffset=function(e,t){var n=this.options.scale;return[t[0]?e[0]*n[0]:0,t[1]?e[1]*n[1]:0]},t._getDistance=function(e,t){return Math.sqrt(Number(t[0])*Math.pow(e[0],2)+Number(t[1])*Math.pow(e[1],2))},t._attachElementEvent=function(e){var t=this,n=Ve(this.options.inputType),i=this.element;if(n){if(!i)throw new Error("Element to connect input does not exist.");this._observer=e,this._enabled=!0,this._activeEvent=n,i.addEventListener("click",this._preventClickWhenDragged,!0),n.start.forEach((function(e){i.addEventListener(e,t._onPanstart)})),n.move.forEach((function(e){i.addEventListener(e,t._voidFunction)}))}},t._detachElementEvent=function(){var e=this,t=this._activeEvent,n=this.element;n&&(n.removeEventListener("click",this._preventClickWhenDragged,!0),null==t||t.start.forEach((function(t){n.removeEventListener(t,e._onPanstart)})),null==t||t.move.forEach((function(t){n.removeEventListener(t,e._voidFunction)}))),this._enabled=!1,this._observer=null},e}(),je=function(){function e(e,t){this.axes=[],this.element=null,this._pinchFlag=!1,this._enabled=!1,this._activeEvent=null,this._isOverThreshold=!1,this.element=te(e),this.options=z({scale:1,threshold:0,inputType:["touch","pointer"],touchAction:"none"},t),this._onPinchStart=this._onPinchStart.bind(this),this._onPinchMove=this._onPinchMove.bind(this),this._onPinchEnd=this._onPinchEnd.bind(this)}var t=e.prototype;return t.mapAxes=function(e){this.axes=e},t.connect=function(e){return this._activeEvent&&this._detachEvent(),this._attachEvent(e),this._originalCssProps=me(this.element,this.options,30),this},t.disconnect=function(){return this._detachEvent(),Ae(this._originalCssProps)||ye(this.element,this._originalCssProps),this},t.destroy=function(){this.disconnect(),this.element=null},t.enable=function(){return this._enabled=!0,this},t.disable=function(){return this._enabled=!1,this},t.isEnabled=function(){return this._enabled},t._onPinchStart=function(e){var t=this._activeEvent,n=t.onEventStart(e);n&&this._enabled&&2===t.getTouches(e)&&(this._baseValue=this._observer.get(this)[this.axes[0]],this._observer.hold(this,e),this._pinchFlag=!0,this._isOverThreshold=!1,t.prevEvent=n)},t._onPinchMove=function(e){var t=this.options.threshold,n=this._activeEvent,i=n.onEventMove(e);if(i&&this._pinchFlag&&this._enabled&&2===n.getTouches(e)){var r=this._getDistance(i.scale),o=this._getOffset(i.scale,n.prevEvent.scale);(this._isOverThreshold||r>=t)&&(this._isOverThreshold=!0,this._observer.change(this,e,Le(this.axes,[o]))),n.prevEvent=i}},t._onPinchEnd=function(e){var t=this._activeEvent;t.onEventEnd(e),!this._pinchFlag||!this._enabled||t.getTouches(e)>=2||(t.onRelease(),this._observer.release(this,e,[0],0),this._baseValue=null,this._pinchFlag=!1)},t._attachEvent=function(e){var t=this,n=Ve(this.options.inputType),i=this.element;if(n){if(!i)throw new Error("Element to connect input does not exist.");this._observer=e,this._enabled=!0,this._activeEvent=n,n.start.forEach((function(e){i.addEventListener(e,t._onPinchStart,!1)})),n.move.forEach((function(e){i.addEventListener(e,t._onPinchMove,!1)})),n.end.forEach((function(e){i.addEventListener(e,t._onPinchEnd,!1)}))}},t._detachEvent=function(){var e=this,t=this._activeEvent,n=this.element;n&&(null==t||t.start.forEach((function(t){n.removeEventListener(t,e._onPinchStart,!1)})),null==t||t.move.forEach((function(t){n.removeEventListener(t,e._onPinchMove,!1)})),null==t||t.end.forEach((function(t){n.removeEventListener(t,e._onPinchEnd,!1)}))),this._enabled=!1,this._observer=null},t._getOffset=function(e,t){return void 0===t&&(t=1),this._baseValue*(e-t)*this.options.scale},t._getDistance=function(e){return Math.abs(e-1)},e}(),ze=function(){function e(e,t){this.axes=[],this.element=null,this._enabled=!1,this._holding=!1,this._timer=null,this.element=te(e),this.options=z({inputKey:[Z],scale:1,releaseDelay:300,useNormalized:!0,useAnimation:!1},t),this._onWheel=this._onWheel.bind(this)}var t=e.prototype;return t.mapAxes=function(e){this._direction=ge(!!e[1],!!e[0]),this.axes=e},t.connect=function(e){return this._detachEvent(),this._attachEvent(e),this},t.disconnect=function(){return this._detachEvent(),this},t.destroy=function(){this.disconnect(),this.element=null},t.enable=function(){return this._enabled=!0,this},t.disable=function(){return this._enabled=!1,this},t.isEnabled=function(){return this._enabled},t._onWheel=function(e){var t=this;if(this._enabled&&Pe(e,this.options.inputKey)){var n=this._getOffset([e.deltaY,e.deltaX],[ve(X,this._direction),ve(6,this._direction)]);0===n[0]&&0===n[1]||(e.preventDefault(),this._holding||(this._observer.hold(this,e),this._holding=!0),this._observer.change(this,e,Le(this.axes,n),this.options.useAnimation),clearTimeout(this._timer),this._timer=setTimeout((function(){t._holding&&(t._holding=!1,t._observer.release(t,e,[0]))}),this.options.releaseDelay))}},t._getOffset=function(e,t){var n=this.options.scale,i=this.options.useNormalized;return[t[0]&&e[0]?(e[0]>0?-1:1)*(i?1:Math.abs(e[0]))*n:0,t[1]&&e[1]?(e[1]>0?-1:1)*(i?1:Math.abs(e[1]))*n:0]},t._attachEvent=function(e){var t=this.element;if(!t)throw new Error("Element to connect input does not exist.");this._observer=e,t.addEventListener("wheel",this._onWheel),this._enabled=!0},t._detachEvent=function(){this.element&&this.element.removeEventListener("wheel",this._onWheel),this._enabled=!1,this._observer=null,this._timer&&(clearTimeout(this._timer),this._timer=null)},e}(),Xe=function(){function e(e,t){this.axes=[],this.element=null,this._enabled=!1,this._holding=!1,this._timer=null,this.element=te(e),this.options=z({scale:[1,1]},t),this._onKeydown=this._onKeydown.bind(this),this._onKeyup=this._onKeyup.bind(this)}var t=e.prototype;return t.mapAxes=function(e){this.axes=e},t.connect=function(e){return this._detachEvent(),"0"!==this.element.getAttribute("tabindex")&&this.element.setAttribute("tabindex","0"),this._attachEvent(e),this},t.disconnect=function(){return this._detachEvent(),this},t.destroy=function(){this.disconnect(),this.element=null},t.enable=function(){return this._enabled=!0,this},t.disable=function(){return this._enabled=!1,this},t.isEnabled=function(){return this._enabled},t._onKeydown=function(e){if(this._enabled){var t=!0,n=1,i=-1;switch(e.keyCode){case 37:case 65:n=-1;break;case 39:case 68:break;case 40:case 83:n=-1,i=1;break;case 38:case 87:i=1;break;default:t=!1}if((-1===i&&!this.axes[0]||1===i&&!this.axes[1])&&(t=!1),t){e.preventDefault();var r=-1===i?[+this.options.scale[0]*n,0]:[0,+this.options.scale[1]*n];this._holding||(this._observer.hold(this,e),this._holding=!0),clearTimeout(this._timer),this._observer.change(this,e,Le(this.axes,r))}}},t._onKeyup=function(e){var t=this;this._holding&&(clearTimeout(this._timer),this._timer=setTimeout((function(){t._observer.release(t,e,[0,0]),t._holding=!1}),80))},t._attachEvent=function(e){var t=this.element;if(!t)throw new Error("Element to connect input does not exist.");this._observer=e,t.addEventListener("keydown",this._onKeydown,!1),t.addEventListener("keypress",this._onKeydown,!1),t.addEventListener("keyup",this._onKeyup,!1),this._enabled=!0},t._detachEvent=function(){var e=this.element;e&&(e.removeEventListener("keydown",this._onKeydown,!1),e.removeEventListener("keypress",this._onKeydown,!1),e.removeEventListener("keyup",this._onKeyup,!1)),this._enabled=!1,this._observer=null},e}();const He=We;var qe=1e-6,Ye="undefined"!=typeof Float32Array?Float32Array:Array;Math.random;var Ze=Math.PI/180;function Ke(e){return e*Ze}function $e(){var e=new Ye(3);return Ye!=Float32Array&&(e[0]=0,e[1]=0,e[2]=0),e}function Je(e){var t=e[0],n=e[1],i=e[2];return Math.hypot(t,n,i)}function et(e,t,n){var i=new Ye(3);return i[0]=e,i[1]=t,i[2]=n,i}function tt(e,t,n){return e[0]=t[0]-n[0],e[1]=t[1]-n[1],e[2]=t[2]-n[2],e}function nt(e,t,n){var i=t[0],r=t[1],o=t[2],s=n[0],a=n[1],u=n[2];return e[0]=r*u-o*a,e[1]=o*s-i*u,e[2]=i*a-r*s,e}function it(e,t,n){var i=t[0],r=t[1],o=t[2];return e[0]=i*n[0]+r*n[3]+o*n[6],e[1]=i*n[1]+r*n[4]+o*n[7],e[2]=i*n[2]+r*n[5]+o*n[8],e}function rt(e,t,n){var i=n[0],r=n[1],o=n[2],s=n[3],a=t[0],u=t[1],l=t[2],c=r*l-o*u,h=o*a-i*l,d=i*u-r*a,p=r*d-o*h,f=o*c-i*d,A=i*h-r*c,g=2*s;return c*=g,h*=g,d*=g,p*=2,f*=2,A*=2,e[0]=a+c+p,e[1]=u+h+f,e[2]=l+d+A,e}Math.hypot||(Math.hypot=function(){for(var e=0,t=arguments.length;t--;)e+=arguments[t]*arguments[t];return Math.sqrt(e)});var ot;function st(){var e=new Ye(9);return Ye!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[5]=0,e[6]=0,e[7]=0),e[0]=1,e[4]=1,e[8]=1,e}function at(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[4],e[4]=t[5],e[5]=t[6],e[6]=t[8],e[7]=t[9],e[8]=t[10],e}function ut(e,t){var n=t[0],i=t[1],r=t[2],o=t[3],s=t[4],a=t[5],u=t[6],l=t[7],c=t[8],h=c*s-a*l,d=-c*o+a*u,p=l*o-s*u,f=n*h+i*d+r*p;return f?(f=1/f,e[0]=h*f,e[1]=(-c*i+r*l)*f,e[2]=(a*i-r*s)*f,e[3]=d*f,e[4]=(c*n-r*u)*f,e[5]=(-a*n+r*o)*f,e[6]=p*f,e[7]=(-l*n+i*u)*f,e[8]=(s*n-i*o)*f,e):null}function lt(){var e=new Ye(4);return Ye!=Float32Array&&(e[0]=0,e[1]=0,e[2]=0),e[3]=1,e}$e(),ot=new Ye(4),Ye!=Float32Array&&(ot[0]=0,ot[1]=0,ot[2]=0,ot[3]=0);var ct=function(e){var t=new Ye(4);return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t},ht=function(e,t,n,i){var r=new Ye(4);return r[0]=e,r[1]=t,r[2]=n,r[3]=i,r},dt=function(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e},pt=function(e,t){var n=t[0],i=t[1],r=t[2],o=t[3],s=n*n+i*i+r*r+o*o;return s>0&&(s=1/Math.sqrt(s)),e[0]=n*s,e[1]=i*s,e[2]=r*s,e[3]=o*s,e};function ft(e,t){var n=new Ye(2);return n[0]=e,n[1]=t,n}function At(e,t){return e[0]=t[0],e[1]=t[1],e}function gt(e,t){var n=t[0],i=t[1],r=n*n+i*i;return r>0&&(r=1/Math.sqrt(r)),e[0]=t[0]*r,e[1]=t[1]*r,e}function vt(){var e=new Ye(16);return Ye!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0),e[0]=1,e[5]=1,e[10]=1,e[15]=1,e}function mt(e,t,n){var i=Math.sin(n),r=Math.cos(n),o=t[4],s=t[5],a=t[6],u=t[7],l=t[8],c=t[9],h=t[10],d=t[11];return t!==e&&(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[12]=t[12],e[13]=t[13],e[14]=t[14],e[15]=t[15]),e[4]=o*r+l*i,e[5]=s*r+c*i,e[6]=a*r+h*i,e[7]=u*r+d*i,e[8]=l*r-o*i,e[9]=c*r-s*i,e[10]=h*r-a*i,e[11]=d*r-u*i,e}function yt(e,t,n){var i=Math.sin(n),r=Math.cos(n),o=t[0],s=t[1],a=t[2],u=t[3],l=t[8],c=t[9],h=t[10],d=t[11];return t!==e&&(e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[12]=t[12],e[13]=t[13],e[14]=t[14],e[15]=t[15]),e[0]=o*r-l*i,e[1]=s*r-c*i,e[2]=a*r-h*i,e[3]=u*r-d*i,e[8]=o*i+l*r,e[9]=s*i+c*r,e[10]=a*i+h*r,e[11]=u*i+d*r,e}$e(),et(1,0,0),et(0,1,0),lt(),lt(),st(),function(){var e;e=new Ye(2),Ye!=Float32Array&&(e[0]=0,e[1]=0)}();var _t=function(e,t,n,i,r){var o,s=1/Math.tan(t/2);return e[0]=s/n,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=s,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[11]=-1,e[12]=0,e[13]=0,e[15]=0,null!=r&&r!==1/0?(o=1/(i-r),e[10]=(r+i)*o,e[14]=2*r*i*o):(e[10]=-1,e[14]=-2*i),e},wt=function(e,t){return(wt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function bt(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}wt(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var xt=function(){return(xt=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};var Et="undefined"!=typeof window,Tt=Et?window.navigator.userAgent:"",St=!!Et&&!!("getComputedStyle"in window),Rt=/MSIE|Trident|Windows Phone|Edge/.test(Tt),Mt=!!Et&&!!("addEventListener"in document),Ct="width",Pt="height";function Ft(e,t){return e.getAttribute(t)||""}function Dt(e){return[].slice.call(e)}function It(e,t){return void 0===t&&(t="data-"),"loading"in e&&"lazy"===e.getAttribute("loading")||!!e.getAttribute(t+"lazy")}function Ot(e,t,n){Mt?e.addEventListener(t,n,!1):e.attachEvent?e.attachEvent("on"+t,n):e["on"+t]=n}function Bt(e,t,n){e.removeEventListener?e.removeEventListener(t,n,!1):e.detachEvent?e.detachEvent("on"+t,n):e["on"+t]=null}function Lt(e,t){var n=e["client"+t]||e["offset"+t];return parseFloat(n||function(e){return(St?window.getComputedStyle(e):e.currentStyle)||{}}(e)[t.toLowerCase()])||0}var Vt=[];function kt(e,t){!Vt.length&&Ot(window,"resize",Qt),e.__PREFIX__=t,Vt.push(e),Nt(e)}function Nt(e,t){void 0===t&&(t="data-");var n=e.__PREFIX__||t,i=parseInt(Ft(e,""+n+Ct),10)||0,r=parseInt(Ft(e,""+n+Pt),10)||0;if(Ft(e,n+"fixed")===Pt){var o=Lt(e,"Height")||r;e.style.width=i/r*o+"px"}else o=Lt(e,"Width")||i,e.style.height=r/i*o+"px"}function Qt(){Vt.forEach((function(e){Nt(e)}))}var Ut=function(e){function t(t,n){void 0===n&&(n={});var i=e.call(this)||this;i.isReady=!1,i.isPreReady=!1,i.hasDataSize=!1,i.hasLoading=!1,i.isSkip=!1,i.onCheck=function(e){if(i.clear(),e&&"error"===e.type&&i.onError(i.element),!i.hasLoading||!i.checkElement()){var t=!i.hasDataSize&&!i.hasLoading;i.onReady(t)}},i.options=xt({prefix:"data-"},n),i.element=t;var r=i.options.prefix;return i.hasDataSize=function(e,t){return void 0===t&&(t="data-"),!!e.getAttribute(t+"width")}(t,r),i.isSkip=function(e,t){return void 0===t&&(t="data-"),!!e.getAttribute(t+"skip")}(t,r),i.hasLoading=It(t,r),i}bt(t,e);var n=t.prototype;return n.check=function(){return this.isSkip||!this.checkElement()?(this.onAlreadyReady(!0),!1):(this.hasDataSize&&kt(this.element,this.options.prefix),(this.hasDataSize||this.hasLoading)&&this.onAlreadyPreReady(),!0)},n.addEvents=function(){var e=this,t=this.element;this.constructor.EVENTS.forEach((function(n){Ot(t,n,e.onCheck)}))},n.clear=function(){var e=this,t=this.element;this.constructor.EVENTS.forEach((function(n){Bt(t,n,e.onCheck)})),this.removeAutoSizer()},n.destroy=function(){this.clear(),this.off()},n.removeAutoSizer=function(){if(this.hasDataSize){var e=this.options.prefix;!function(e,t){var n=Vt.indexOf(e);if(!(n<0)){var i=Ft(e,t+"fixed");delete e.__PREFIX__,e.style[i===Pt?Ct:Pt]="",Vt.splice(n,1),!Vt.length&&Bt(window,"resize",Qt)}}(this.element,e)}},n.onError=function(e){this.trigger("error",{element:this.element,target:e})},n.onPreReady=function(){this.isPreReady||(this.isPreReady=!0,this.trigger("preReady",{element:this.element,hasLoading:this.hasLoading,isSkip:this.isSkip}))},n.onReady=function(e){this.isReady||((e=!this.isPreReady&&e)&&(this.isPreReady=!0),this.removeAutoSizer(),this.isReady=!0,this.trigger("ready",{element:this.element,withPreReady:e,hasLoading:this.hasLoading,isSkip:this.isSkip}))},n.onAlreadyError=function(e){var t=this;setTimeout((function(){t.onError(e)}))},n.onAlreadyPreReady=function(){var e=this;setTimeout((function(){e.onPreReady()}))},n.onAlreadyReady=function(e){var t=this;setTimeout((function(){t.onReady(e)}))},t.EVENTS=[],t}(l),Wt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}bt(t,e);var n=t.prototype;return n.setHasLoading=function(e){this.hasLoading=e},n.check=function(){return this.isSkip?(this.onAlreadyReady(!0),!1):(this.hasDataSize?(kt(this.element,this.options.prefix),this.onAlreadyPreReady()):this.trigger("requestChildren"),!0)},n.checkElement=function(){return!0},n.destroy=function(){this.clear(),this.trigger("requestDestroy"),this.off()},n.onAlreadyPreReady=function(){e.prototype.onAlreadyPreReady.call(this),this.trigger("reqeustReadyChildren")},t.EVENTS=[],t}(Ut),Gt=function(e){function t(t){void 0===t&&(t={});var n=e.call(this)||this;return n.readyCount=0,n.preReadyCount=0,n.totalCount=0,n.totalErrorCount=0,n.isPreReadyOver=!0,n.elementInfos=[],n.options=xt({loaders:{},prefix:"data-"},t),n}bt(t,e);var n=t.prototype;return n.check=function(e){var t=this,n=this.options.prefix;this.clear(),this.elementInfos=Dt(e).map((function(e,i){var r=t.getLoader(e,{prefix:n});return r.check(),r.on("error",(function(e){t.onError(i,e.target)})).on("preReady",(function(e){var n=t.elementInfos[i];n.hasLoading=e.hasLoading,n.isSkip=e.isSkip;var r=t.checkPreReady(i);t.onPreReadyElement(i),r&&t.onPreReady()})).on("ready",(function(e){var n=e.withPreReady,r=e.hasLoading,o=e.isSkip,s=t.elementInfos[i];s.hasLoading=r,s.isSkip=o;var a=n&&t.checkPreReady(i),u=t.checkReady(i);n&&t.onPreReadyElement(i),t.onReadyElement(i),a&&t.onPreReady(),u&&t.onReady()})),{loader:r,element:e,hasLoading:!1,hasError:!1,isPreReady:!1,isReady:!1,isSkip:!1}}));var i=this.elementInfos.length;return this.totalCount=i,i||setTimeout((function(){t.onPreReady(),t.onReady()})),this},n.getTotalCount=function(){return this.totalCount},n.isPreReady=function(){return this.elementInfos.every((function(e){return e.isPreReady}))},n.isReady=function(){return this.elementInfos.every((function(e){return e.isReady}))},n.hasError=function(){return this.totalErrorCount>0},n.clear=function(){this.isPreReadyOver=!1,this.totalCount=0,this.preReadyCount=0,this.readyCount=0,this.totalErrorCount=0,this.elementInfos.forEach((function(e){e.loader&&e.loader.destroy()})),this.elementInfos=[]},n.destroy=function(){this.clear(),this.off()},n.getLoader=function(e,t){var n=this,i=e.tagName.toLowerCase(),r=this.options.loaders,o=t.prefix,s=Object.keys(r);if(r[i])return new r[i](e,t);var a=new Wt(e,t),u=Dt(e.querySelectorAll(s.join(", ")));a.setHasLoading(u.some((function(e){return It(e,o)})));var l=!1,c=this.clone().on("error",(function(e){a.onError(e.target)})).on("ready",(function(){a.onReady(l)}));return a.on("requestChildren",(function(){var t=function(e,t,n){var i=Dt(e.querySelectorAll(function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var i=Array(e),r=0;for(t=0;t<n;t++)for(var o=arguments[t],s=0,a=o.length;s<a;s++,r++)i[r]=o[s];return i}(["["+n+"skip] ["+n+"width]"],t.map((function(e){return["["+n+"skip] "+e,e+"["+n+"skip]","["+n+"width] "+e].join(", ")}))).join(", ")));return Dt(e.querySelectorAll("["+n+"width], "+t.join(", "))).filter((function(e){return-1===i.indexOf(e)}))}(e,s,n.options.prefix);c.check(t).on("preReady",(function(e){(l=e.isReady)||a.onPreReady()}))})).on("reqeustReadyChildren",(function(){c.check(u)})).on("requestDestroy",(function(){c.destroy()})),a},n.clone=function(){return new t(xt({},this.options))},n.checkPreReady=function(e){return this.elementInfos[e].isPreReady=!0,++this.preReadyCount,!(this.preReadyCount<this.totalCount)},n.checkReady=function(e){return this.elementInfos[e].isReady=!0,++this.readyCount,!(this.readyCount<this.totalCount)},n.onError=function(e,t){var n=this.elementInfos[e];n.hasError=!0,this.trigger(new u("error",{element:n.element,index:e,target:t,errorCount:this.getErrorCount(),totalErrorCount:++this.totalErrorCount}))},n.onPreReadyElement=function(e){var t=this.elementInfos[e];this.trigger(new u("preReadyElement",{element:t.element,index:e,preReadyCount:this.preReadyCount,readyCount:this.readyCount,totalCount:this.totalCount,isPreReady:this.isPreReady(),isReady:this.isReady(),hasLoading:t.hasLoading,isSkip:t.isSkip}))},n.onPreReady=function(){this.isPreReadyOver=!0,this.trigger(new u("preReady",{readyCount:this.readyCount,totalCount:this.totalCount,isReady:this.isReady(),hasLoading:this.hasLoading()}))},n.onReadyElement=function(e){var t=this.elementInfos[e];this.trigger(new u("readyElement",{index:e,element:t.element,hasError:t.hasError,errorCount:this.getErrorCount(),totalErrorCount:this.totalErrorCount,preReadyCount:this.preReadyCount,readyCount:this.readyCount,totalCount:this.totalCount,isPreReady:this.isPreReady(),isReady:this.isReady(),hasLoading:t.hasLoading,isPreReadyOver:this.isPreReadyOver,isSkip:t.isSkip}))},n.onReady=function(){this.trigger(new u("ready",{errorCount:this.getErrorCount(),totalErrorCount:this.totalErrorCount,totalCount:this.totalCount}))},n.getErrorCount=function(){return this.elementInfos.filter((function(e){return e.hasError})).length},n.hasLoading=function(){return this.elementInfos.some((function(e){return e.hasLoading}))},t}(l),jt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return bt(t,e),t.prototype.checkElement=function(){var e=this.element,t=e.getAttribute("src");if(e.complete){if(t)return e.naturalWidth||this.onAlreadyError(e),!1;this.onAlreadyPreReady()}return this.addEvents(),Rt&&e.setAttribute("src",t),!0},t.EVENTS=["load","error"],t}(Ut),zt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return bt(t,e),t.prototype.checkElement=function(){var e=this.element;return!(e.readyState>=1||(e.error?(this.onAlreadyError(e),1):(this.addEvents(),0)))},t.EVENTS=["loadedmetadata","error"],t}(Ut);const Xt=function(e){function t(t){return void 0===t&&(t={}),e.call(this,xt({loaders:{img:jt,video:zt}},t))||this}return bt(t,e),t}(Gt);var Ht="3.6.4",qt=function(e,t){return(qt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)};function Yt(e,t){function n(){this.constructor=e}qt(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var Zt=function(){return(Zt=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};function Kt(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],i=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function $t(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var i,r,o=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(i=o.next()).done;)s.push(i.value)}catch(e){r={error:e}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return s}function Jt(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat($t(arguments[t]));return e}var en="undefined"!=typeof window&&window.Math===Math?window:"undefined"!=typeof self&&self.Math===Math?self:Function("return this")(),tn=en.document,nn=en.navigator,rn=L(),on=rn.os.name,sn=rn.browser.name,an="ios"===on,un="mac"===on&&"safari"===sn;en.Float32Array=void 0!==en.Float32Array?en.Float32Array:en.Array,en.Float32Array,en.getComputedStyle;var ln=en.navigator&&en.navigator.userAgent,cn="ontouchstart"in en,hn="ondevicemotion"in en,dn=en.DeviceMotionEvent,pn=en.devicePixelRatio,fn=function(){for(var e,t=null!==(e=null==tn?void 0:tn.documentElement.style)&&void 0!==e?e:{},n=["transform","webkitTransform","msTransform","mozTransform"],i=0,r=n.length;i<r;i++)if(n[i]in t)return n[i];return""}(),An=en.CSS&&en.CSS.supports&&en.CSS.supports("will-change","transform"),gn=!1,vn=function(e){return 180*e/Math.PI},mn={isPowerOfTwo:function(e){return e&&0==(e&e-1)},extractPitchFromQuat:function(e){var t=function(e){var t=et(0,0,1);return rt(t,t,e),t}(e);return-1*Math.atan2(t[1],Math.sqrt(Math.pow(t[0],2)+Math.pow(t[2],2)))}};mn.hypot=Math.hypot||function(e,t){return Math.sqrt(e*e+t*t)};var yn={PITCH_DELTA:1,YAW_DELTA_BY_ROLL:2,YAW_DELTA_BY_YAW:3};yn[yn.PITCH_DELTA]={targetAxis:[0,1,0],meshPoint:[0,0,1]},yn[yn.YAW_DELTA_BY_ROLL]={targetAxis:[0,1,0],meshPoint:[1,0,0]},yn[yn.YAW_DELTA_BY_YAW]={targetAxis:[1,0,0],meshPoint:[0,0,1]};var _n=function(e,t){var n=e[0]*t[1]-t[0]*e[1];return-Math.atan2(n,function(e,t){return e[0]*t[0]+e[1]*t[1]}(e,t))};mn.yawOffsetBetween=function(e,t){var n=ft(e[0],e[2]),i=ft(t[0],t[2]);return gt(n,n),gt(i,i),-_n(n,i)},mn.sign=function(e){return Math.sign?Math.sign(e):Number(e>0)-Number(e<0)||+e},mn.toDegree=vn,mn.getRotationDelta=function(e,t,n){var i=et(yn[n].targetAxis[0],yn[n].targetAxis[1],yn[n].targetAxis[2]),r=yn[n].meshPoint,o=ct(e),s=ct(t);pt(o,o),pt(s,s);var a=et(0,0,1),u=et(0,0,1);rt(a,a,o),rt(u,u,s),rt(i,i,s);var l,c=function(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]}(i,nt($e(),a,u))>0?1:-1,h=et(r[0],r[1],r[2]);l=n!==yn.YAW_DELTA_BY_YAW?et(0,c,0):et(c,0,0),rt(h,h,s),rt(l,l,s);var d=h,p=l,f=$e();nt(f,d,p),function(e,t){var n=t[0],i=t[1],r=t[2],o=n*n+i*i+r*r;o>0&&(o=1/Math.sqrt(o)),e[0]=t[0]*o,e[1]=t[1]*o,e[2]=t[2]*o}(f,f);var A=f[0],g=f[1],v=f[2];rt(u=et(r[0],r[1],r[2]),u,s),rt(a=et(r[0],r[1],r[2]),a,o);var m=Math.abs(a[0]*A+a[1]*g+a[2]*v),y=$e();tt(y,a,function(e,t,n){return e[0]=t[0]*n,e[1]=t[1]*n,e[2]=t[2]*n,e}($e(),f,m));var _=(y[0]*u[0]+y[1]*u[1]+y[2]*u[2])/(Je(y)*Je(u));_>1&&(_=1);var w,b=Math.acos(_),x=nt($e(),u,y);return m=A*x[0]+g*x[1]+v*x[2],w=n!==yn.YAW_DELTA_BY_YAW?m>0?1:-1:m<0?1:-1,vn(b*w*c)},mn.angleBetweenVec2=_n;var wn=-1,bn=null,xn=null,En=/Chrome\/([0-9]+)\.(?:[0-9]*)\.([0-9]*)\.([0-9]*)/i.exec(ln);En&&(wn=parseInt(En[1],10),bn=En[2],xn=En[3]);var Tn,Sn,Rn,Mn=wn,Cn=65===wn&&"3325"===bn&&parseInt(xn,10)<148,Pn=/Android/i.test(ln),Fn=.0014,Dn=[.2,.2],In={NONE:"none",YAWPITCH:"yawPitch",VR:"VR"},On=en.MathUtil||{};On.degToRad=Math.PI/180,On.radToDeg=180/Math.PI,On.Vector2=function(e,t){this.x=e||0,this.y=t||0},On.Vector2.prototype={constructor:On.Vector2,set:function(e,t){return this.x=e,this.y=t,this},copy:function(e){return this.x=e.x,this.y=e.y,this},subVectors:function(e,t){return this.x=e.x-t.x,this.y=e.y-t.y,this}},On.Vector3=function(e,t,n){this.x=e||0,this.y=t||0,this.z=n||0},On.Vector3.prototype={constructor:On.Vector3,set:function(e,t,n){return this.x=e,this.y=t,this.z=n,this},copy:function(e){return this.x=e.x,this.y=e.y,this.z=e.z,this},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)},normalize:function(){var e=this.length();if(0!==e){var t=1/e;this.multiplyScalar(t)}else this.x=0,this.y=0,this.z=0;return this},multiplyScalar:function(e){this.x*=e,this.y*=e,this.z*=e},applyQuaternion:function(e){var t=this.x,n=this.y,i=this.z,r=e.x,o=e.y,s=e.z,a=e.w,u=a*t+o*i-s*n,l=a*n+s*t-r*i,c=a*i+r*n-o*t,h=-r*t-o*n-s*i;return this.x=u*a+h*-r+l*-s-c*-o,this.y=l*a+h*-o+c*-r-u*-s,this.z=c*a+h*-s+u*-o-l*-r,this},dot:function(e){return this.x*e.x+this.y*e.y+this.z*e.z},crossVectors:function(e,t){var n=e.x,i=e.y,r=e.z,o=t.x,s=t.y,a=t.z;return this.x=i*a-r*s,this.y=r*o-n*a,this.z=n*s-i*o,this}},On.Quaternion=function(e,t,n,i){this.x=e||0,this.y=t||0,this.z=n||0,this.w=void 0!==i?i:1},On.Quaternion.prototype={constructor:On.Quaternion,set:function(e,t,n,i){return this.x=e,this.y=t,this.z=n,this.w=i,this},copy:function(e){return this.x=e.x,this.y=e.y,this.z=e.z,this.w=e.w,this},setFromEulerXYZ:function(e,t,n){var i=Math.cos(e/2),r=Math.cos(t/2),o=Math.cos(n/2),s=Math.sin(e/2),a=Math.sin(t/2),u=Math.sin(n/2);return this.x=s*r*o+i*a*u,this.y=i*a*o-s*r*u,this.z=i*r*u+s*a*o,this.w=i*r*o-s*a*u,this},setFromEulerYXZ:function(e,t,n){var i=Math.cos(e/2),r=Math.cos(t/2),o=Math.cos(n/2),s=Math.sin(e/2),a=Math.sin(t/2),u=Math.sin(n/2);return this.x=s*r*o+i*a*u,this.y=i*a*o-s*r*u,this.z=i*r*u-s*a*o,this.w=i*r*o+s*a*u,this},setFromAxisAngle:function(e,t){var n=t/2,i=Math.sin(n);return this.x=e.x*i,this.y=e.y*i,this.z=e.z*i,this.w=Math.cos(n),this},multiply:function(e){return this.multiplyQuaternions(this,e)},multiplyQuaternions:function(e,t){var n=e.x,i=e.y,r=e.z,o=e.w,s=t.x,a=t.y,u=t.z,l=t.w;return this.x=n*l+o*s+i*u-r*a,this.y=i*l+o*a+r*s-n*u,this.z=r*l+o*u+n*a-i*s,this.w=o*l-n*s-i*a-r*u,this},inverse:function(){return this.x*=-1,this.y*=-1,this.z*=-1,this.normalize(),this},normalize:function(){var e=Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w);return 0===e?(this.x=0,this.y=0,this.z=0,this.w=1):(e=1/e,this.x=this.x*e,this.y=this.y*e,this.z=this.z*e,this.w=this.w*e),this},slerp:function(e,t){if(0===t)return this;if(1===t)return this.copy(e);var n=this.x,i=this.y,r=this.z,o=this.w,s=o*e.w+n*e.x+i*e.y+r*e.z;if(s<0?(this.w=-e.w,this.x=-e.x,this.y=-e.y,this.z=-e.z,s=-s):this.copy(e),s>=1)return this.w=o,this.x=n,this.y=i,this.z=r,this;var a=Math.acos(s),u=Math.sqrt(1-s*s);if(Math.abs(u)<.001)return this.w=.5*(o+this.w),this.x=.5*(n+this.x),this.y=.5*(i+this.y),this.z=.5*(r+this.z),this;var l=Math.sin((1-t)*a)/u,c=Math.sin(t*a)/u;return this.w=o*l+this.w*c,this.x=n*l+this.x*c,this.y=i*l+this.y*c,this.z=r*l+this.z*c,this},setFromUnitVectors:function(e,t){return void 0===Tn&&(Tn=new On.Vector3),(Sn=e.dot(t)+1)<1e-6?(Sn=0,Math.abs(e.x)>Math.abs(e.z)?Tn.set(-e.y,e.x,0):Tn.set(0,-e.z,e.y)):Tn.crossVectors(e,t),this.x=Tn.x,this.y=Tn.y,this.z=Tn.z,this.w=Sn,this.normalize(),this}};var Bn,Ln,Vn,kn,Nn,Qn=null!==(Rn=null==nn?void 0:nn.userAgent)&&void 0!==Rn?Rn:"",Un=en.Util||{};Un.MIN_TIMESTEP=.001,Un.MAX_TIMESTEP=1,Un.base64=function(e,t){return"data:"+e+";base64,"+t},Un.clamp=function(e,t,n){return Math.min(Math.max(t,e),n)},Un.lerp=function(e,t,n){return e+(t-e)*n},Un.isIOS=(Bn=/iPad|iPhone|iPod/.test(null==nn?void 0:nn.platform),function(){return Bn}),Un.isWebViewAndroid=(Ln=-1!==Qn.indexOf("Version")&&-1!==Qn.indexOf("Android")&&-1!==Qn.indexOf("Chrome"),function(){return Ln}),Un.isSafari=(Vn=/^((?!chrome|android).)*safari/i.test(Qn),function(){return Vn}),Un.isFirefoxAndroid=(kn=-1!==Qn.indexOf("Firefox")&&-1!==Qn.indexOf("Android"),function(){return kn}),Un.isR7=(Nn=-1!==Qn.indexOf("R7 Build"),function(){return Nn}),Un.isLandscapeMode=function(){var e=90===en.orientation||-90===en.orientation;return Un.isR7()?!e:e},Un.isTimestampDeltaValid=function(e){return!(isNaN(e)||e<=Un.MIN_TIMESTEP||e>Un.MAX_TIMESTEP)},Un.getScreenWidth=function(){return Math.max(en.screen.width,en.screen.height)*en.devicePixelRatio},Un.getScreenHeight=function(){return Math.min(en.screen.width,en.screen.height)*en.devicePixelRatio},Un.requestFullscreen=function(e){if(Un.isWebViewAndroid())return!1;if(e.requestFullscreen)e.requestFullscreen();else if(e.webkitRequestFullscreen)e.webkitRequestFullscreen();else if(e.mozRequestFullScreen)e.mozRequestFullScreen();else{if(!e.msRequestFullscreen)return!1;e.msRequestFullscreen()}return!0},Un.exitFullscreen=function(){if(tn.exitFullscreen)tn.exitFullscreen();else if(tn.webkitExitFullscreen)tn.webkitExitFullscreen();else if(tn.mozCancelFullScreen)tn.mozCancelFullScreen();else{if(!tn.msExitFullscreen)return!1;tn.msExitFullscreen()}return!0},Un.getFullscreenElement=function(){return tn.fullscreenElement||tn.webkitFullscreenElement||tn.mozFullScreenElement||tn.msFullscreenElement},Un.linkProgram=function(e,t,n,i){var r=e.createShader(e.VERTEX_SHADER);e.shaderSource(r,t),e.compileShader(r);var o=e.createShader(e.FRAGMENT_SHADER);e.shaderSource(o,n),e.compileShader(o);var s=e.createProgram();for(var a in e.attachShader(s,r),e.attachShader(s,o),i)e.bindAttribLocation(s,i[a],a);return e.linkProgram(s),e.deleteShader(r),e.deleteShader(o),s},Un.getProgramUniforms=function(e,t){for(var n={},i=e.getProgramParameter(t,e.ACTIVE_UNIFORMS),r="",o=0;o<i;o++)n[r=e.getActiveUniform(t,o).name.replace("[0]","")]=e.getUniformLocation(t,r);return n},Un.orthoMatrix=function(e,t,n,i,r,o,s){var a=1/(t-n),u=1/(i-r),l=1/(o-s);return e[0]=-2*a,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=-2*u,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=2*l,e[11]=0,e[12]=(t+n)*a,e[13]=(r+i)*u,e[14]=(s+o)*l,e[15]=1,e},Un.copyArray=function(e,t){for(var n=0,i=e.length;n<i;n++)t[n]=e[n]},Un.isMobile=function(){var e=!1;return function(t){(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(t)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(t.substr(0,4)))&&(e=!0)}(Qn||(null==nn?void 0:nn.vendor)||en.opera),e},Un.extend=function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},Un.safariCssSizeWorkaround=function(e){if(Un.isIOS()){var t=e.style.width,n=e.style.height;e.style.width=parseInt(t)+1+"px",e.style.height=parseInt(n)+"px",setTimeout((function(){e.style.width=t,e.style.height=n}),100)}en.Util=Un,en.canvas=e},Un.isDebug=function(){return Un.getQueryParameter("debug")},Un.getQueryParameter=function(e){e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var t=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(location.search);return null===t?"":decodeURIComponent(t[1].replace(/\+/g," "))},Un.frameDataFromPose=function(){var e=Math.PI/180,t=.25*Math.PI,n=new Float32Array([0,0,0,1]),i=new Float32Array([0,0,0]);function r(r,o,s,a,u){var l,c,h,d,p,f,A,g,v,m;l=r,c=a?a.fieldOfView:null,h=u.depthNear,d=u.depthFar,p=Math.tan(c?c.upDegrees*e:t),f=Math.tan(c?c.downDegrees*e:t),v=2/((A=Math.tan(c?c.leftDegrees*e:t))+(g=Math.tan(c?c.rightDegrees*e:t))),m=2/(p+f),l[0]=v,l[1]=0,l[2]=0,l[3]=0,l[4]=0,l[5]=m,l[6]=0,l[7]=0,l[8]=-(A-g)*v*.5,l[9]=(p-f)*m*.5,l[10]=d/(h-d),l[11]=-1,l[12]=0,l[13]=0,l[14]=d*h/(h-d),l[15]=0,function(e,t,n){var i=t[0],r=t[1],o=t[2],s=t[3],a=i+i,u=r+r,l=o+o,c=i*a,h=i*u,d=i*l,p=r*u,f=r*l,A=o*l,g=s*a,v=s*u,m=s*l;e[0]=1-(p+A),e[1]=h+m,e[2]=d-v,e[3]=0,e[4]=h-m,e[5]=1-(c+A),e[6]=f+g,e[7]=0,e[8]=d+v,e[9]=f-g,e[10]=1-(c+p),e[11]=0,e[12]=n[0],e[13]=n[1],e[14]=n[2],e[15]=1}(o,s.orientation||n,s.position||i),a&&function(e,t,n){var i,r,o,s,a,u,l,c,h,d,p,f,A=n[0],g=n[1],v=n[2];t===e?(e[12]=t[0]*A+t[4]*g+t[8]*v+t[12],e[13]=t[1]*A+t[5]*g+t[9]*v+t[13],e[14]=t[2]*A+t[6]*g+t[10]*v+t[14],e[15]=t[3]*A+t[7]*g+t[11]*v+t[15]):(i=t[0],r=t[1],o=t[2],s=t[3],a=t[4],u=t[5],l=t[6],c=t[7],h=t[8],d=t[9],p=t[10],f=t[11],e[0]=i,e[1]=r,e[2]=o,e[3]=s,e[4]=a,e[5]=u,e[6]=l,e[7]=c,e[8]=h,e[9]=d,e[10]=p,e[11]=f,e[12]=i*A+a*g+h*v+t[12],e[13]=r*A+u*g+d*v+t[13],e[14]=o*A+l*g+p*v+t[14],e[15]=s*A+c*g+f*v+t[15])}(o,o,a.offset),function(e,t){var n=t[0],i=t[1],r=t[2],o=t[3],s=t[4],a=t[5],u=t[6],l=t[7],c=t[8],h=t[9],d=t[10],p=t[11],f=t[12],A=t[13],g=t[14],v=t[15],m=n*a-i*s,y=n*u-r*s,_=n*l-o*s,w=i*u-r*a,b=i*l-o*a,x=r*l-o*u,E=c*A-h*f,T=c*g-d*f,S=c*v-p*f,R=h*g-d*A,M=h*v-p*A,C=d*v-p*g,P=m*C-y*M+_*R+w*S-b*T+x*E;P&&(P=1/P,e[0]=(a*C-u*M+l*R)*P,e[1]=(r*M-i*C-o*R)*P,e[2]=(A*x-g*b+v*w)*P,e[3]=(d*b-h*x-p*w)*P,e[4]=(u*S-s*C-l*T)*P,e[5]=(n*C-r*S+o*T)*P,e[6]=(g*_-f*x-v*y)*P,e[7]=(c*x-d*_+p*y)*P,e[8]=(s*M-a*S+l*E)*P,e[9]=(i*S-n*M-o*E)*P,e[10]=(f*b-A*_+v*m)*P,e[11]=(h*_-c*b-p*m)*P,e[12]=(a*T-s*R-u*E)*P,e[13]=(n*R-i*T+r*E)*P,e[14]=(A*y-f*w-g*m)*P,e[15]=(c*w-h*y+d*m)*P)}(o,o)}return function(e,t,n){return!(!e||!t||(e.pose=t,e.timestamp=t.timestamp,r(e.leftProjectionMatrix,e.leftViewMatrix,t,n.getEyeParameters("left"),n),r(e.rightProjectionMatrix,e.rightViewMatrix,t,n.getEyeParameters("right"),n),0))}}(),Un.isInsideCrossDomainIFrame=function(){var e=en.self!==en.top,t=Un.getDomainFromUrl(tn.referrer),n=Un.getDomainFromUrl(en.location.href);return e&&t!==n},Un.getDomainFromUrl=function(e){return(e.indexOf("://")>-1?e.split("/")[2]:e.split("/")[0]).split(":")[0]};var Wn=function(){function e(e){this.predictionTimeS=e,this.previousQ=new On.Quaternion,this.previousTimestampS=null,this.deltaQ=new On.Quaternion,this.outQ=new On.Quaternion}return e.prototype.getPrediction=function(e,t,n){if(!this.previousTimestampS)return this.previousQ.copy(e),this.previousTimestampS=n,e;var i=new On.Vector3;i.copy(t),i.normalize();var r=t.length();if(r<20*On.degToRad)return Un.isDebug()&&console.log("Moving slowly, at %s deg/s: no prediction",(On.radToDeg*r).toFixed(1)),this.outQ.copy(e),this.previousQ.copy(e),this.outQ;this.previousTimestampS;var o=r*this.predictionTimeS;return this.deltaQ.setFromAxisAngle(i,o),this.outQ.copy(this.previousQ),this.outQ.multiply(this.deltaQ),this.previousQ.copy(e),this.previousTimestampS=n,this.outQ},e}(),Gn=function(e){function t(){var t=e.call(this)||this;return t._onDeviceMotion=t._onDeviceMotion.bind(t),t._onDeviceOrientation=t._onDeviceOrientation.bind(t),t._onChromeWithoutDeviceMotion=t._onChromeWithoutDeviceMotion.bind(t),t.isWithoutDeviceMotion=Cn,t.isAndroid=Pn,t.stillGyroVec=$e(),t.rawGyroVec=$e(),t.adjustedGyroVec=$e(),t._timer=-1,t.lastDevicemotionTimestamp=0,t._isEnabled=!1,t.enable(),t}Yt(t,e);var n=t.prototype;return n.enable=function(){this.isAndroid&&en.addEventListener("deviceorientation",this._onDeviceOrientation),this.isWithoutDeviceMotion?en.addEventListener("deviceorientation",this._onChromeWithoutDeviceMotion):en.addEventListener("devicemotion",this._onDeviceMotion),this._isEnabled=!0},n.disable=function(){en.removeEventListener("deviceorientation",this._onDeviceOrientation),en.removeEventListener("deviceorientation",this._onChromeWithoutDeviceMotion),en.removeEventListener("devicemotion",this._onDeviceMotion),this._isEnabled=!1},n._onChromeWithoutDeviceMotion=function(e){var t=e.alpha,n=e.beta,i=e.gamma;null!==t&&(t=(t||0)*Math.PI/180,n=(n||0)*Math.PI/180,i=(i||0)*Math.PI/180,this.trigger(new u("devicemotion",{inputEvent:{deviceorientation:{alpha:t,beta:n,gamma:-i}}})))},n._onDeviceOrientation=function(){var e=this;this._timer&&clearTimeout(this._timer),this._timer=en.setTimeout((function(){(new Date).getTime()-e.lastDevicemotionTimestamp<200&&function(e,t){e[0]=t[0],e[1]=t[1],e[2]=t[2]}(e.stillGyroVec,e.rawGyroVec)}),200)},n._onDeviceMotion=function(e){var t=!(null==e.rotationRate.alpha),n=!(null==e.accelerationIncludingGravity.x);if(0!==e.interval&&t&&n){var i=Zt({},e);i.interval=e.interval,i.timeStamp=e.timeStamp,i.type=e.type,i.rotationRate={alpha:e.rotationRate.alpha,beta:e.rotationRate.beta,gamma:e.rotationRate.gamma},i.accelerationIncludingGravity={x:e.accelerationIncludingGravity.x,y:e.accelerationIncludingGravity.y,z:e.accelerationIncludingGravity.z},i.acceleration={x:e.acceleration.x,y:e.acceleration.y,z:e.acceleration.z},this.isAndroid&&(function(e,t,n,i){e[0]=t,e[1]=n,e[2]=i}(this.rawGyroVec,e.rotationRate.alpha||0,e.rotationRate.beta||0,e.rotationRate.gamma||0),tt(this.adjustedGyroVec,this.rawGyroVec,this.stillGyroVec),this.lastDevicemotionTimestamp=(new Date).getTime(),i.adjustedRotationRate={alpha:this.adjustedGyroVec[0],beta:this.adjustedGyroVec[1],gamma:this.adjustedGyroVec[2]}),this.trigger(new u("devicemotion",{inputEvent:i}))}},t}(l),jn=function(){function e(e,t){this.set(e,t)}var t=e.prototype;return t.set=function(e,t){this.sample=e,this.timestampS=t},t.copy=function(e){this.set(e.sample,e.timestampS)},e}(),zn=function(){function e(e){this.addGyroMeasurement=function(e,t){this.currentGyroMeasurement.set(e,t);var n=t-this.previousGyroMeasurement.timestampS;Un.isTimestampDeltaValid(n)&&this.run_(),this.previousGyroMeasurement.copy(this.currentGyroMeasurement)},this.kFilter=e,this.currentAccelMeasurement=new jn,this.currentGyroMeasurement=new jn,this.previousGyroMeasurement=new jn,Un.isIOS()?this.filterQ=new On.Quaternion(-1,0,0,1):this.filterQ=new On.Quaternion(1,0,0,1),this.previousFilterQ=new On.Quaternion,this.previousFilterQ.copy(this.filterQ),this.accelQ=new On.Quaternion,this.isOrientationInitialized=!1,this.estimatedGravity=new On.Vector3,this.measuredGravity=new On.Vector3,this.gyroIntegralQ=new On.Quaternion}var t=e.prototype;return t.addAccelMeasurement=function(e,t){this.currentAccelMeasurement.set(e,t)},t.getOrientation=function(){return this.filterQ},t.run_=function(){if(!this.isOrientationInitialized)return this.accelQ=this.accelToQuaternion_(this.currentAccelMeasurement.sample),this.previousFilterQ.copy(this.accelQ),void(this.isOrientationInitialized=!0);var e=this.currentGyroMeasurement.timestampS-this.previousGyroMeasurement.timestampS,t=this.gyroToQuaternionDelta_(this.currentGyroMeasurement.sample,e);this.gyroIntegralQ.multiply(t),this.filterQ.copy(this.previousFilterQ),this.filterQ.multiply(t);var n=new On.Quaternion;n.copy(this.filterQ),n.inverse(),this.estimatedGravity.set(0,0,-1),this.estimatedGravity.applyQuaternion(n),this.estimatedGravity.normalize(),this.measuredGravity.copy(this.currentAccelMeasurement.sample),this.measuredGravity.normalize();var i=new On.Quaternion;i.setFromUnitVectors(this.estimatedGravity,this.measuredGravity),i.inverse(),Un.isDebug()&&console.log("Delta: %d deg, G_est: (%s, %s, %s), G_meas: (%s, %s, %s)",On.radToDeg*Un.getQuaternionAngle(i),this.estimatedGravity.x.toFixed(1),this.estimatedGravity.y.toFixed(1),this.estimatedGravity.z.toFixed(1),this.measuredGravity.x.toFixed(1),this.measuredGravity.y.toFixed(1),this.measuredGravity.z.toFixed(1));var r=new On.Quaternion;r.copy(this.filterQ),r.multiply(i),this.filterQ.slerp(r,1-this.kFilter),this.previousFilterQ.copy(this.filterQ)},t.accelToQuaternion_=function(e){var t=new On.Vector3;t.copy(e),t.normalize();var n=new On.Quaternion;return n.setFromUnitVectors(new On.Vector3(0,0,-1),t),n.inverse(),n},t.gyroToQuaternionDelta_=function(e,t){var n=new On.Quaternion,i=new On.Vector3;return i.copy(e),i.normalize(),n.setFromAxisAngle(i,e.length()*t),n},e}();zn.prototype.run_=function(){if(!this.isOrientationInitialized)return this.accelQ=this.accelToQuaternion_(this.currentAccelMeasurement.sample),this.previousFilterQ.copy(this.accelQ),void(this.isOrientationInitialized=!0);var e=this.currentGyroMeasurement.timestampS-this.previousGyroMeasurement.timestampS,t=this.gyroToQuaternionDelta_(this.currentGyroMeasurement.sample,e);this.gyroIntegralQ.multiply(t),this.filterQ.copy(this.previousFilterQ),this.filterQ.multiply(t);var n=new On.Quaternion;n.copy(this.filterQ),n.inverse(),this.estimatedGravity.set(0,0,-1),this.estimatedGravity.applyQuaternion(n),this.estimatedGravity.normalize(),this.measuredGravity.copy(this.currentAccelMeasurement.sample),this.measuredGravity.normalize();var i=new On.Quaternion;i.setFromUnitVectors(this.estimatedGravity,this.measuredGravity),i.inverse();var r=new On.Quaternion;r.copy(this.filterQ),r.multiply(i),this.filterQ.slerp(r,1-this.kFilter),this.previousFilterQ.copy(this.filterQ),this.isFilterQuaternionInitialized||(this.isFilterQuaternionInitialized=!0)},zn.prototype.getOrientation=function(){return this.isFilterQuaternionInitialized?this.filterQ:null};var Xn,Hn,qn=function(e){function t(){var t=e.call(this)||this;return t.deviceMotion=new Gn,t.accelerometer=new On.Vector3,t.gyroscope=new On.Vector3,t._onDeviceMotionChange=t._onDeviceMotionChange.bind(t),t._onScreenOrientationChange=t._onScreenOrientationChange.bind(t),t.filter=new zn(.98),t.posePredictor=new Wn(.04),t.filterToWorldQ=new On.Quaternion,t.isFirefoxAndroid=Un.isFirefoxAndroid(),t.isIOS=an||un,t.isChromeUsingDegrees=Mn>=66,t._isEnabled=!1,t.isIOS?t.filterToWorldQ.setFromAxisAngle(new On.Vector3(1,0,0),Math.PI/2):t.filterToWorldQ.setFromAxisAngle(new On.Vector3(1,0,0),-Math.PI/2),t.inverseWorldToScreenQ=new On.Quaternion,t.worldToScreenQ=new On.Quaternion,t.originalPoseAdjustQ=new On.Quaternion,t.originalPoseAdjustQ.setFromAxisAngle(new On.Vector3(0,0,1),-en.orientation*Math.PI/180),t._setScreenTransform(),Un.isLandscapeMode()&&t.filterToWorldQ.multiply(t.inverseWorldToScreenQ),t.resetQ=new On.Quaternion,t.deviceMotion.on("devicemotion",t._onDeviceMotionChange),t.enable(),t}Yt(t,e);var n=t.prototype;return n.enable=function(){this.isEnabled()||(this.deviceMotion.enable(),this._isEnabled=!0,en.addEventListener("orientationchange",this._onScreenOrientationChange))},n.disable=function(){this.isEnabled()&&(this.deviceMotion.disable(),this._isEnabled=!1,en.removeEventListener("orientationchange",this._onScreenOrientationChange))},n.isEnabled=function(){return this._isEnabled},n.destroy=function(){this.disable(),this.deviceMotion=null},n.getOrientation=function(){var e;if(this.deviceMotion.isWithoutDeviceMotion&&this._deviceOrientationQ){this.deviceOrientationFixQ=this.deviceOrientationFixQ||(new On.Quaternion).setFromAxisAngle(new On.Vector3(0,1,0),-this._alpha),e=this._deviceOrientationQ,(n=new On.Quaternion).copy(e),n.multiply(this.filterToWorldQ),n.multiply(this.resetQ),n.multiply(this.worldToScreenQ),n.multiplyQuaternions(this.deviceOrientationFixQ,n);var t=ht(n.x,n.y,n.z,n.w);return pt(t,t)}if(!(e=this.filter.getOrientation()))return null;var n=this._convertFusionToPredicted(e);return t=ht(n.x,n.y,n.z,n.w),pt(t,t)},n._triggerChange=function(){var e=this.getOrientation();e&&(this._prevOrientation?function(e,t){var n=e[0],i=e[1],r=e[2],o=e[3],s=t[0],a=t[1],u=t[2],l=t[3];return Math.abs(n-s)<=qe*Math.max(1,Math.abs(n),Math.abs(s))&&Math.abs(i-a)<=qe*Math.max(1,Math.abs(i),Math.abs(a))&&Math.abs(r-u)<=qe*Math.max(1,Math.abs(r),Math.abs(u))&&Math.abs(o-l)<=qe*Math.max(1,Math.abs(o),Math.abs(l))}(this._prevOrientation,e)||this.trigger(new u("change",{quaternion:e})):this._prevOrientation=e)},n._convertFusionToPredicted=function(e){this.predictedQ=this.posePredictor.getPrediction(e,this.gyroscope,this.previousTimestampS);var t=new On.Quaternion;return t.copy(this.filterToWorldQ),t.multiply(this.resetQ),t.multiply(this.predictedQ),t.multiply(this.worldToScreenQ),t},n._onDeviceMotionChange=function(e){var t=e.inputEvent,n=t.deviceorientation,i=t,r=i.accelerationIncludingGravity,o=i.adjustedRotationRate||i.rotationRate,s=i.timeStamp/1e3;n?(this._alpha||(this._alpha=n.alpha),this._deviceOrientationQ=this._deviceOrientationQ||new On.Quaternion,this._deviceOrientationQ.setFromEulerYXZ(n.beta,n.alpha,n.gamma),this._triggerChange()):(this.isFirefoxAndroid&&(s/=1e3),this.accelerometer.set(-r.x,-r.y,-r.z),this.gyroscope.set(o.alpha,o.beta,o.gamma),(this.isIOS||this.isFirefoxAndroid||this.isChromeUsingDegrees)&&this.gyroscope.multiplyScalar(Math.PI/180),this.filter.addAccelMeasurement(this.accelerometer,s),this.filter.addGyroMeasurement(this.gyroscope,s),this._triggerChange(),this.previousTimestampS=s)},n._onScreenOrientationChange=function(){this._setScreenTransform()},n._setScreenTransform=function(){this.worldToScreenQ.set(0,0,0,1);var e=en.orientation;switch(e){case 0:break;case 90:case-90:case 180:this.worldToScreenQ.setFromAxisAngle(new On.Vector3(0,0,1),e/-180*Math.PI)}this.inverseWorldToScreenQ.copy(this.worldToScreenQ),this.inverseWorldToScreenQ.inverse()},t}(l),Yn=function(e,t){return mn.getRotationDelta(e,t,yn.PITCH_DELTA)},Zn=function(e){function t(t,n){void 0===n&&(n={});var i=e.call(this)||this;return i.element=t,i._prevQuaternion=null,i._quaternion=null,i.fusionPoseSensor=null,i.options=Zt({scale:1,threshold:0},n),i._onPoseChange=i._onPoseChange.bind(i),i}Yt(t,e);var n=t.prototype;return n.mapAxes=function(e){this.axes=e},n.connect=function(e){return this.observer||(this.observer=e,this.fusionPoseSensor=new qn,this.fusionPoseSensor.enable(),this._attachEvent()),this},n.disconnect=function(){return this.observer?(this._dettachEvent(),this.fusionPoseSensor.disable(),this.fusionPoseSensor.destroy(),this.fusionPoseSensor=null,this.observer=null,this):this},n.destroy=function(){this.disconnect(),this.element=null,this.options=null,this.axes=null,this._prevQuaternion=null,this._quaternion=null},n._onPoseChange=function(e){if(!this._prevQuaternion)return this._prevQuaternion=ct(e.quaternion),void(this._quaternion=ct(e.quaternion));var t,n,i,r;dt(this._prevQuaternion,this._quaternion),dt(this._quaternion,e.quaternion),this.observer.change(this,e,(t=this.axes,[(n=this._prevQuaternion,i=this._quaternion,r=mn.getRotationDelta(n,i,yn.YAW_DELTA_BY_YAW),mn.getRotationDelta(n,i,yn.YAW_DELTA_BY_ROLL)*Math.sin(mn.extractPitchFromQuat(i))+r),Yn(this._prevQuaternion,this._quaternion)].reduce((function(e,n,i){return t[i]&&(e[t[i]]=n),e}),{})))},n._attachEvent=function(){this.fusionPoseSensor.on("change",this._onPoseChange)},n._dettachEvent=function(){this.fusionPoseSensor.off("change",this._onPoseChange)},t}(l),Kn=null,$n=0,Jn=function(){function e(){if($n++,Kn)return Kn;Kn=this,this._onDeviceOrientation=this._onDeviceOrientation.bind(this),this._onOrientationChange=this._onOrientationChange.bind(this),this._spinR=0,this._screenOrientationAngle=0,en.addEventListener("deviceorientation",this._onDeviceOrientation),en.addEventListener("orientationchange",this._onOrientationChange)}var t=e.prototype;return t.getRadian=function(){return this._spinR+Ke(this._screenOrientationAngle)},t.unref=function(){--$n>0||(en.removeEventListener("deviceorientation",this._onDeviceOrientation),en.removeEventListener("orientationchange",this._onOrientationChange),this._spinR=0,this._screenOrientationAngle=0,Kn=null,$n=0)},t._onDeviceOrientation=function(e){if(null!==e.beta&&null!==e.gamma){var t=Ke(e.beta),n=Ke(e.gamma);this._spinR=Math.atan2(Math.cos(t)*Math.sin(n),Math.sin(t))}},t._onOrientationChange=function(){en.screen&&en.screen.orientation&&void 0!==en.screen.orientation.angle?this._screenOrientationAngle=screen.orientation.angle:void 0!==en.orientation&&(this._screenOrientationAngle=en.orientation>=0?en.orientation:360+en.orientation)},e}(),ei=function(e){function t(t,n){void 0===n&&(n={});var i=e.call(this,t,n)||this;return i._useRotation=!1,i._screenRotationAngle=null,i.setUseRotation(!(!n||!n.useRotation)),i._userDirection=He.DIRECTION_ALL,i}Yt(t,e);var n=t.prototype;return n.setUseRotation=function(e){this._useRotation=e,this._screenRotationAngle&&(this._screenRotationAngle.unref(),this._screenRotationAngle=null),this._useRotation&&(this._screenRotationAngle=new Jn)},n.connect=function(t){return this._userDirection=this._direction,this._useRotation&&this._direction&He.DIRECTION_ALL&&(this._direction=He.DIRECTION_HORIZONTAL),e.prototype.connect.call(this,t)},n.destroy=function(){this._useRotation&&this._screenRotationAngle&&this._screenRotationAngle.unref(),e.prototype.destroy.call(this)},n._getOffset=function(t,n){if(!1===this._useRotation)return e.prototype._getOffset.call(this,t,n);var i=e.prototype._getOffset.call(this,t,[!0,!0]),r=[0,0],o=this._screenRotationAngle.getRadian(),s=Math.cos(o),a=Math.sin(o);return r[0]=i[0]*s-i[1]*a,r[1]=i[1]*s+i[0]*a,this._userDirection&He.DIRECTION_HORIZONTAL?this._userDirection&He.DIRECTION_VERTICAL||(r[1]=0):r[0]=0,r},t}(Ge),ti=et(0,1,0),ni=function(e){function t(){var t=e.call(this)||this;return t._fusionPoseSensor=new qn,t._quaternion=lt(),t._fusionPoseSensor.enable(),t._fusionPoseSensor.on("change",(function(e){t._quaternion=e.quaternion,t.trigger(new u("change",{isTrusted:!0}))})),t}Yt(t,e);var n=t.prototype;return n.getCombinedQuaternion=function(e){var t=function(e,t,n){n*=.5;var i=Math.sin(n);return e[0]=i*t[0],e[1]=i*t[1],e[2]=i*t[2],e[3]=Math.cos(n),e}(lt(),ti,Ke(-e)),n=function(e,t){return e[0]=-t[0],e[1]=-t[1],e[2]=-t[2],e[3]=t[3],e}(lt(),this._quaternion);return function(e,t,n){var i=t[0],r=t[1],o=t[2],s=t[3],a=n[0],u=n[1],l=n[2],c=n[3];return e[0]=i*c+s*a+r*l-o*u,e[1]=r*c+s*u+o*a-i*l,e[2]=o*c+s*l+i*u-r*a,e[3]=s*c-i*a-r*u-o*l,e}(lt(),n,t)},n.destroy=function(){this.off(),this._fusionPoseSensor&&(this._fusionPoseSensor.off(),this._fusionPoseSensor.destroy(),this._fusionPoseSensor=null)},t}(l),ii=[-180,180],ri=[-90,90],oi=[-180,180],si=function(e){function t(t){var n=e.call(this)||this;n.options={};var i=Zt({element:null,yaw:0,pitch:0,fov:65,showPolePoint:!1,useZoom:!0,useKeyboard:!0,gyroMode:In.YAWPITCH,touchDirection:6,yawRange:ii,pitchRange:ri,fovRange:[30,110],aspectRatio:1},t);return n._element=i.element,n._initialFov=i.fov,n._enabled=!1,n._isAnimating=!1,n._deviceQuaternion=null,n._initAxes(i),n.option(i),n}Yt(t,e);var n=t.prototype;return n.updatePanScale=function(e){void 0===e&&(e={});var t=this._axes.get().fov,n=e.height||parseInt(window.getComputedStyle(this._element).height,10),i=Dn[0]*t/this._initialFov*320/n;return this._axesPanInput.options.scale=[i,i],this._axes.options.deceleration=Fn*t/110,this},n.option=function(e,t){if(!e)return this._getOptions();if(e&&"string"==typeof e&&void 0===t)return this._getOptions(e);var n={},i=[];if("string"==typeof e)i.push(e),n[e]=t;else{var r=e;i=Object.keys(r),n=Zt({},r)}return this._setOptions(this._getValidatedOptions(n)),this._applyOptions(i),this},n.enable=function(){return this._enabled||(this._enabled=!0,this._applyOptions(Object.keys(this.options)),this.updatePanScale()),this},n.disable=function(e){return void 0===e&&(e=!1),this._enabled?(e||this._resetOrientation(),this._axes.disconnect(),this._enabled=!1,this):this},n.lookAt=function(e,t){var n=e.yaw,i=e.pitch,r=e.fov,o=this._axes.get(),s=void 0===n?0:n-o.yaw,a=void 0===i?0:i-o.pitch,u=void 0===r?0:r-o.fov;this._axes.options.maximumDuration=1/0,this._axes.setBy({yaw:s,pitch:a,fov:u},t)},n.getYawPitch=function(){var e=this._axes.get();return{yaw:e.yaw,pitch:e.pitch}},n.getFov=function(){return this._axes.get().fov},n.getQuaternion=function(){var e=this._axes.get();return this._deviceQuaternion.getCombinedQuaternion(e.yaw)},n.shouldRenderWithQuaternion=function(){return this.options.gyroMode===In.VR},n.destroy=function(){this._axes&&this._axes.destroy(),this._axesPanInput&&this._axesPanInput.destroy(),this._axesWheelInput&&this._axesWheelInput.destroy(),this._axesTiltMotionInput&&this._axesTiltMotionInput.destroy(),this._axesPinchInput&&this._axesPinchInput.destroy(),this._axesMoveKeyInput&&this._axesMoveKeyInput.destroy(),this._deviceQuaternion&&this._deviceQuaternion.destroy()},n._initAxes=function(e){var t=this,n=this._updateYawRange(e.yawRange,e.fov,e.aspectRatio),i=this._updatePitchRange(e.pitchRange,e.fov,e.showPolePoint),r=e.gyroMode===In.VR;this._axesPanInput=new ei(this._element,{useRotation:r}),this._axesWheelInput=new ze(this._element,{scale:-4}),this._axesTiltMotionInput=null,this._axesPinchInput=cn?new je(this._element,{scale:-1}):null,this._axesMoveKeyInput=new Xe(this._element,{scale:[-6,6]}),this._axes=new He({yaw:{range:n,circular:this._isCircular(n),bounce:[0,0]},pitch:{range:i,circular:this._isCircular(i),bounce:[0,0]},fov:{range:e.fovRange,circular:[!1,!1],bounce:[0,0]}},{deceleration:Fn,maximumDuration:1e3},{yaw:e.yaw,pitch:e.pitch,fov:e.fov}).on({hold:function(e){t._axes.options.maximumDuration=1e3,t.trigger(new u("hold",{isTrusted:e.isTrusted}))},change:function(e){0!==e.delta.fov&&(t._updateControlScale(e),t.updatePanScale()),t._triggerChange(e)},release:function(e){t._triggerChange(e)},animationEnd:function(e){t.trigger(new u("animationEnd",{isTrusted:e.isTrusted}))}})},n._getValidatedOptions=function(e){return e.yawRange&&(e.yawRange=this._getValidYawRange(e.yawRange,e.fov,e.aspectRatio)),e.pitchRange&&(e.pitchRange=this._getValidPitchRange(e.pitchRange,e.fov)),e},n._getOptions=function(e){var t;return"string"==typeof e?t=this.options[e]:0===arguments.length&&(t=this.options),t},n._setOptions=function(e){for(var t in e)this.options[t]=e[t]},n._applyOptions=function(e){var t=this.options,n=this._axes,i=t.gyroMode===In.VR,r=t.gyroMode===In.YAWPITCH,o=i?2&t.touchDirection:t.touchDirection;if(e.some((function(e){return"showPolePoint"===e||"fov"===e||"aspectRatio"===e||"yawRange"===e||"pitchRange"===e}))&&(e.indexOf("fov")>=0&&(n.setTo({fov:t.fov}),this.updatePanScale()),this._updateControlScale()),e.some((function(e){return"fovRange"===e}))){var s=t.fovRange,a=n.get().fov,u=n.get().fov;At(n.axis.fov.range,s),u<s[0]?u=s[0]:a>s[1]&&(u=s[1]),a!==u&&(n.setTo({fov:u},0),this._updateControlScale(),this.updatePanScale())}if(e.some((function(e){return"gyroMode"===e}))&&hn&&(this._axesTiltMotionInput&&(this._axes.disconnect(this._axesTiltMotionInput),this._axesTiltMotionInput.destroy(),this._axesTiltMotionInput=null),this._deviceQuaternion&&(this._deviceQuaternion.destroy(),this._deviceQuaternion=null),i?this._initDeviceQuaternion():r&&(this._axesTiltMotionInput=new Zn(this._element),this._axes.connect(["yaw","pitch"],this._axesTiltMotionInput)),this._axesPanInput.setUseRotation(i)),e.some((function(e){return"useKeyboard"===e}))&&(t.useKeyboard?n.connect(["yaw","pitch"],this._axesMoveKeyInput):n.disconnect(this._axesMoveKeyInput)),e.some((function(e){return"useZoom"===e}))){var l=t.useZoom;n.disconnect(this._axesWheelInput),l&&n.connect(["fov"],this._axesWheelInput)}this._togglePinchInputByOption(t.touchDirection,t.useZoom),e.some((function(e){return"touchDirection"===e}))&&this._enabled&&this._enableTouch(o)},n._togglePinchInputByOption=function(e,t){this._axesPinchInput&&(this._axes.disconnect(this._axesPinchInput),t&&6===e&&-1===this._axes._inputs.indexOf(this._axesPinchInput)&&this._axes.connect(["fov"],this._axesPinchInput))},n._enableTouch=function(e){this._axesPanInput&&this._axes.disconnect(this._axesPanInput);var t=2&e?"yaw":null,n=4&e?"pitch":null;this._axes.connect([t,n],this._axesPanInput)},n._initDeviceQuaternion=function(){var e=this;this._deviceQuaternion=new ni,this._deviceQuaternion.on("change",(function(t){e._triggerChange(t)}))},n._getValidYawRange=function(e,t,n){var i=this._adjustAspectRatio(n||this.options.aspectRatio||1),r=(t||this._axes.get().fov)*i;return e[1]-e[0]>=r?e:this.options.yawRange||ii},n._getValidPitchRange=function(e,t){var n=t||this._axes.get().fov;return e[1]-e[0]>=n?e:this.options.pitchRange||ri},n._isCircular=function(e){return e[1]-e[0]<360?[!1,!1]:[!0,!0]},n._updateControlScale=function(e){var t=this.options,n=this._axes.get().fov,i=this._updatePitchRange(t.pitchRange,n,t.showPolePoint),r=this._updateYawRange(t.yawRange,n,t.aspectRatio),o=this._axes.get(),s=o.yaw,a=o.pitch;return At(this._axes.axis.yaw.range,r),At(this._axes.axis.pitch.range,i),this._axes.axis.yaw.circular=this._isCircular(r),this._axes.axis.pitch.circular=this._isCircular(i),s<r[0]?s=r[0]:s>r[1]&&(s=r[1]),a<i[0]?a=i[0]:a>i[1]&&(a=i[1]),e&&e.set({yaw:s,pitch:a}),this._axes.setTo({yaw:s,pitch:a},0),this},n._updatePitchRange=function(e,t,n){if(this.options.gyroMode===In.VR)return oi;var i=e[1]-e[0],r=t/2;return!n||i<180?[e[0]+r,e[1]-r]:e.concat()},n._updateYawRange=function(e,t,n){if(this.options.gyroMode===In.VR)return ii;if(e[1]-e[0]>=360)return e.concat();var i=mn.toDegree(Math.atan2(n,1/Math.tan(Ke(t/2))));return[e[0]+i,e[1]-i]},n._triggerChange=function(e){var t=this._axes.get(),n=this.options,i={targetElement:n.element,isTrusted:e.isTrusted,yaw:t.yaw,pitch:t.pitch,fov:t.fov,quaternion:null};n.gyroMode===In.VR&&this._deviceQuaternion&&(i.quaternion=this._deviceQuaternion.getCombinedQuaternion(t.yaw)),this.trigger(new u("change",i))},n._adjustAspectRatio=function(e){for(var t=[.52,.54,.563,.57,.584,.59,.609,.67,.702,.72,.76,.78,.82,.92,.97,1,1.07,1.14,1.19,1.25,1.32,1.38,1.4,1.43,1.53,1.62,1.76,1.77,1.86,1.96,2.26,2.3,2.6,3,5,6],n=[.51,.54,.606,.56,.628,.63,.647,.71,.736,.757,.78,.77,.8,.89,.975,1,1.07,1.1,1.15,1.18,1.22,1.27,1.3,1.33,1.39,1.45,1.54,1.55,1.58,1.62,1.72,1.82,1.92,2,2.24,2.3],i=-1,r=0;r<t.length-1;r++)if(t[r]<=e&&t[r+1]>=e){i=r;break}if(-1===i)return t[0]>e?n[0]:n[n[0].length-1];var o=t[i],s=t[i+1],a=n[i],u=n[i+1];return this._lerp(a,u,(e-o)/(s-o))},n._lerp=function(e,t,n){return e+n*(t-e)},n._resetOrientation=function(){var e=this.options;return this._axes.setTo({yaw:e.yaw,pitch:e.pitch,fov:e.fov},0),this},t.VERSION=Ht,t.CONTROL_MODE_VR=1,t.CONTROL_MODE_YAWPITCH=2,t.TOUCH_DIRECTION_ALL=6,t.TOUCH_DIRECTION_YAW=2,t.TOUCH_DIRECTION_PITCH=4,t.TOUCH_DIRECTION_NONE=1,t}(l),ai={INVALID_DEVICE:10,NO_WEBGL:11,FAIL_IMAGE_LOAD:12,FAIL_BIND_TEXTURE:13,INVALID_RESOURCE:14,RENDERING_CONTEXT_LOST:15},ui={READY:"ready",VIEW_CHANGE:"viewChange",ANIMATION_END:"animationEnd",ERROR:"error"},li={EQUIRECTANGULAR:"equirectangular",CUBEMAP:"cubemap",CUBESTRIP:"cubestrip",PANORAMA:"panorama",STEREOSCOPIC_EQUI:"stereoequi"},ci={TOP_BOTTOM:"3dv",LEFT_RIGHT:"3dh",NONE:""},hi={image:!0,video:!0,projectionType:!0,cubemapConfig:!0,stereoFormat:!0,width:!0,height:!0,yaw:!0,pitch:!0,fov:!0,showPolePoint:!0,useZoom:!0,useKeyboard:!0,gyroMode:!0,yawRange:!0,pitchRange:!0,fovRange:!0,touchDirection:!0,canvasClass:!0},di="view360-canvas",pi=function(e,t){var n,i;if("object"==typeof t?(n=t.src,i=t.type):"string"==typeof t&&(n=t),!n)return!1;var r=document.createElement("source");r.src=n,i&&(r.type=i),e.appendChild(r)},fi={0:"NO_ERROR",1280:"INVALID_ENUM",1281:"INVALID_VALUE",1282:"INVALID_OPERATION",1285:"OUT_OF_MEMORY",1286:"INVALID_FRAMEBUFFER_OPERATION",37442:"CONTEXT_LOST_WEBGL"},Ai=null,gi=function(){function e(){}return e.createShader=function(e,t,n){var i=e.createShader(t);return e.shaderSource(i,n),e.compileShader(i),e.getShaderParameter(i,e.COMPILE_STATUS)?i:(console.error(e.getShaderInfoLog(i)),null)},e.createProgram=function(e,t,n){var i=e.createProgram();return e.attachShader(i,t),e.attachShader(i,n),e.linkProgram(i),e.deleteShader(t),e.deleteShader(n),e.getProgramParameter(i,e.LINK_STATUS)?i:(e.deleteProgram(i),null)},e.initBuffer=function(e,t,n,i,r){var o=e.createBuffer();return e.bindBuffer(t,o),e.bufferData(t,n,e.STATIC_DRAW),o&&(o.itemSize=i,o.numItems=n.length/i),void 0!==r&&(e.enableVertexAttribArray(r),e.vertexAttribPointer(r,o.itemSize,e.FLOAT,!1,0,0)),o},e.getWebglContext=function(e,t){var n,i,r=null,o=Zt({preserveDrawingBuffer:!1,antialias:!1},t),s=function(e){return e.statusMessage};e.addEventListener("webglcontextcreationerror",s);try{for(var a=Kt(["webgl","experimental-webgl","webkit-3d","moz-webgl"]),u=a.next();!u.done;u=a.next()){var l=u.value;try{r=e.getContext(l,o)}catch(e){}if(r)break}}catch(e){n={error:e}}finally{try{u&&!u.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}return e.removeEventListener("webglcontextcreationerror",s),r},e.createTexture=function(e,t){var n=e.createTexture();return e.bindTexture(t,n),e.texParameteri(t,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(t,e.TEXTURE_MIN_FILTER,e.LINEAR),e.texParameteri(t,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(t,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.bindTexture(t,null),n},e.isWebGLAvailable=function(){if(null===Ai){var t=document.createElement("canvas"),n=e.getWebglContext(t);if(Ai=!!n,n){var i=n.getExtension("WEBGL_lose_context");i&&i.loseContext()}}return!!Ai},e.isStableWebGL=function(){var e=L(),t=!0;if("android"===e.os.name){var n=parseFloat(e.os.version);(n<=4.3&&n>=1||4.4===n&&"chrome"!==e.browser.name)&&(t=!1)}return t},e.getErrorNameFromWebGLErrorCode=function(e){return e in fi?fi[e]:"UNKNOWN_ERROR"},e.texImage2D=function(e,t,n){try{e.texImage2D(t,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,n)}catch(e){console.log("error",e.message),console.error("WebGLUtils.texImage2D error:",e)}},e.getMaxTextureSize=function(e){return e.getParameter(e.MAX_TEXTURE_SIZE)},e}(),vi=L(),mi="ie"===vi.browser.name&&11===vi.browser.majorVersion,yi={ERROR:"error"},_i=function(e){function t(){var t=e.call(this)||this;return t._forceDimension=null,t._pixelCanvas=null,t._pixelContext=null,t}Yt(t,e);var n=t.prototype;return n.render=function(e){var t=e.gl,n=e.shaderProgram,i=e.indexBuffer,r=e.mvMatrix,o=e.pMatrix;t.uniformMatrix4fv(n.pMatrixUniform,!1,o),t.uniformMatrix4fv(n.mvMatrixUniform,!1,r),i&&t.drawElements(t.TRIANGLES,i.numItems,t.UNSIGNED_SHORT,0)},n.getDimension=function(e){return{width:e.naturalWidth||e.videoWidth,height:e.naturalHeight||e.videoHeight}},n.updateShaderData=function(e){},n._initPixelSource=function(e,t){if(void 0===t&&(t=null),mi&&e instanceof HTMLVideoElement||t){var n=t||this.getDimension(e),i=n.width,r=n.height;this._pixelCanvas=document.createElement("canvas"),this._pixelCanvas.width=i,this._pixelCanvas.height=r,this._pixelContext=this._pixelCanvas.getContext("2d")}this._forceDimension=t},n._getPixelSource=function(e){if(!this._pixelCanvas)return e;var t=this.getDimension(e),n=this._forceDimension||t;return this._pixelCanvas.width!==n.width&&(this._pixelCanvas.width=n.width),this._pixelCanvas.height!==n.height&&(this._pixelCanvas.height=n.height),this._forceDimension?this._pixelContext.drawImage(e,0,0,t.width,t.height,0,0,n.width,n.height):this._pixelContext.drawImage(e,0,0),this._pixelCanvas},n._extractTileConfig=function(e){var t=Array.isArray(e.tileConfig)?e.tileConfig:Array.apply(void 0,Jt(Array(6))).map((function(){return e.tileConfig}));return t.map((function(e){return Zt({flipHorizontal:!1,rotation:0},e)}))},n._triggerError=function(e){console.error("Renderer Error:",e),this.trigger(new u(yi.ERROR,{message:"string"==typeof e?e:e.message}))},t.EVENTS=yi,t}(l),wi=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}Yt(t,e);var n=t.prototype;return t.extractOrder=function(e){return e.order||"RLUDBF"},n.getVertexPositionData=function(){return t._VERTEX_POSITION_DATA=null!==t._VERTEX_POSITION_DATA?t._VERTEX_POSITION_DATA:[1,-1,1,-1,-1,1,-1,1,1,1,1,1,-1,-1,-1,1,-1,-1,1,1,-1,-1,1,-1,-1,1,-1,1,1,-1,1,1,1,-1,1,1,1,-1,-1,-1,-1,-1,-1,-1,1,1,-1,1,1,-1,-1,1,-1,1,1,1,1,1,1,-1,-1,-1,1,-1,-1,-1,-1,1,-1,-1,1,1],t._VERTEX_POSITION_DATA},n.getIndexData=function(){if(t._INDEX_DATA)return t._INDEX_DATA;for(var e=[],n=this.getVertexPositionData(),i=0;i<n.length/3;i+=4)e.push(i,i+2,i+1,i,i+3,i+2);return t._INDEX_DATA=e,e},n.getTextureCoordData=function(e){var n=this,i=e.image,r=e.imageConfig,o=t.extractOrder(r),s=this.getVertexPositionData(),a=this._extractTileConfig(r),u=r.trim;return"BFUDRL".split("").map((function(e){return a[o.indexOf(e)]})).map((function(e,t){for(var n=Math.floor(e.rotation/90),i=e.flipHorizontal?[0,1,2,3]:[1,0,3,2],r=0;r<Math.abs(n);r++)e.flipHorizontal&&n>0||!e.flipHorizontal&&n<0?i.push(i.shift()):i.unshift(i.pop());for(var o=s.slice(12*t,12*t+12),a=[],u=0;u<4;u++)a[i[u]]=o.splice(0,3);return a})).map((function(e){return n._shrinkCoord({image:i,faceCoords:e,trim:u})})).reduce((function(e,t){return Jt(e,t.reduce((function(e,t){return Jt(e,t)}),[]))}),[])},n.getVertexShaderSource=function(){return"\nattribute vec3 aVertexPosition;\nattribute vec3 aTextureCoord;\nuniform mat4 uMVMatrix;\nuniform mat4 uPMatrix;\nvarying highp vec3 vVertexDirectionVector;\nvoid main(void) {\n  vVertexDirectionVector = aTextureCoord;\n  gl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);\n}"},n.getFragmentShaderSource=function(){return"\nprecision highp float;\nuniform samplerCube uSampler;\nvarying highp vec3 vVertexDirectionVector;\nvoid main(void) {\n  gl_FragColor = textureCube(uSampler, vVertexDirectionVector);\n}"},n.updateTexture=function(e,n,i){var r="RLUDBF",o=t.extractOrder(i),s={};o.split("").forEach((function(e,t){s[e]=t}));try{if(n instanceof Array)for(var a=0;a<6;a++){var u=s[r[a]];gi.texImage2D(e,e.TEXTURE_CUBE_MAP_POSITIVE_X+a,n[u])}else{var l=this.getMaxCubeMapTextureSize(e,n);for(a=0;a<6;a++){u=s[r[a]];var c=this.extractTileFromImage(n,u,l);gi.texImage2D(e,e.TEXTURE_CUBE_MAP_POSITIVE_X+a,c)}}}catch(e){this._triggerError(e)}},n.bindTexture=function(e,t,n,i){e.bindTexture(e.TEXTURE_CUBE_MAP,t),this.updateTexture(e,n,i)},n.getSourceTileSize=function(e){var t=this.getDimension(e),n=t.width,i=t.height,r=n/i;return r===1/6?n:6===r?i:r===2/3?n/2:n/3},n.extractTileFromImage=function(e,t,n){var i=this.getDimension(e).width,r=this.getSourceTileSize(e),o=document.createElement("canvas");o.width=n,o.height=n;var s=o.getContext("2d"),a=i/r,u=r*t%(r*a),l=Math.floor(t/a)*r;return s.drawImage(e,u,l,r,r,0,0,n,n),o},n.getMaxCubeMapTextureSize=function(e,t){var n=L(),i=e.getParameter(e.MAX_CUBE_MAP_TEXTURE_SIZE),r=this.getSourceTileSize(t);if("ie"===n.browser.name&&11===n.browser.majorVersion&&!mn.isPowerOfTwo(r))for(var o=1;o<i;o*=2)if(!(o<r)){r=o;break}if("ios"===n.os.name){var s=n.os.majorVersion;9===s&&(r=1024),8===s&&(r=512)}return Math.min(i,r)},n._shrinkCoord=function(e){var t=e.image,n=e.faceCoords,i=1-e.trim*(2/(Array.isArray(t)?this.getDimension(t[0]).width:this.getSourceTileSize(t))),r=[0,1,2].map((function(e){var t=mn.sign(n[0][e]);return n.some((function(n){return mn.sign(n[e])!==t}))})).map((function(e){return e?i:1}));return n.map((function(e){return e.map((function(e,t){return e*r[t]}))}))},t._VERTEX_POSITION_DATA=null,t._INDEX_DATA=null,t}(_i),bi=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}Yt(t,e);var n=t.prototype;return n.getVertexShaderSource=function(){return"\nattribute vec3 aVertexPosition;\nattribute vec2 aTextureCoord;\nuniform mat4 uMVMatrix;\nuniform mat4 uPMatrix;\nvarying highp vec2 vTextureCoord;\nvoid main(void) {\n  vTextureCoord = aTextureCoord;\n  gl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);\n}"},n.getFragmentShaderSource=function(){return"\n#define PI 3.14159265359\nprecision highp float;\nvarying highp vec2 vTextureCoord;\nuniform sampler2D uSampler;\nuniform bool uIsEAC;\nconst vec2 OPERATE_COORDS_RANGE = vec2(-1.0, 1.0);\nconst vec2 TEXTURE_COORDS_RANGE = vec2(0.0, 1.0);\n// vector type is used for initializing values instead of array.\nconst vec4 TEXTURE_DIVISION_X = vec4(0.0, 1.0 / 3.0, 2.0 / 3.0, 1.0);\nconst vec3 TEXTURE_DIVISION_Y = vec3(0.0, 1.0 / 2.0, 1.0);\nconst float EAC_CONST = 2.0 / PI;\nfloat scale(vec2 domainRange, vec2 targetRange, float val) {\n  float unit = 1.0 / (domainRange[1] - domainRange[0]);\n  return targetRange[0] + (targetRange[1] - targetRange[0]) * (val - domainRange[0]) * unit;\n}\nvoid main(void) {\n  float transformedCoordX;\n  float transformedCoordY;\n\n  if (uIsEAC) {\n    vec2 orgTextureRangeX;\n    vec2 orgTextureRangeY;\n\n    // Apply EAC transform\n    if (vTextureCoord.s >= TEXTURE_DIVISION_X[2]) {\n      orgTextureRangeX = vec2(TEXTURE_DIVISION_X[2], TEXTURE_DIVISION_X[3]);\n    } else if (vTextureCoord.s >= TEXTURE_DIVISION_X[1]) {\n      orgTextureRangeX = vec2(TEXTURE_DIVISION_X[1], TEXTURE_DIVISION_X[2]);\n    } else {\n      orgTextureRangeX = vec2(TEXTURE_DIVISION_X[0], TEXTURE_DIVISION_X[1]);\n    }\n\n    if (vTextureCoord.t >= TEXTURE_DIVISION_Y[1]) {\n      orgTextureRangeY = vec2(TEXTURE_DIVISION_Y[1], TEXTURE_DIVISION_Y[2]);\n    } else {\n      orgTextureRangeY = vec2(TEXTURE_DIVISION_Y[0], TEXTURE_DIVISION_Y[1]);\n    }\n\n    // scaling coors by the coordinates following the range from -1.0 to 1.0.\n    float px = scale(orgTextureRangeX, OPERATE_COORDS_RANGE, vTextureCoord.s);\n    float py = scale(orgTextureRangeY, OPERATE_COORDS_RANGE, vTextureCoord.t);\n\n    float qu = EAC_CONST * atan(px) + 0.5;\n    float qv = EAC_CONST * atan(py) + 0.5;\n\n    // re-scaling coors by original coordinates ranges\n    transformedCoordX = scale(TEXTURE_COORDS_RANGE, orgTextureRangeX, qu);\n    transformedCoordY = scale(TEXTURE_COORDS_RANGE, orgTextureRangeY, qv);\n  } else {\n    // normal cubemap\n    transformedCoordX = vTextureCoord.s;\n    transformedCoordY = vTextureCoord.t;\n  }\n\n  gl_FragColor = texture2D(uSampler, vec2(transformedCoordX, transformedCoordY));\n}"},n.getVertexPositionData=function(){return this._vertices||(this._vertices=[1,-1,1,-1,-1,1,-1,1,1,1,1,1,-1,-1,-1,1,-1,-1,1,1,-1,-1,1,-1,-1,1,-1,1,1,-1,1,1,1,-1,1,1,-1,-1,1,1,-1,1,1,-1,-1,-1,-1,-1,1,-1,-1,1,-1,1,1,1,1,1,1,-1,-1,-1,1,-1,-1,-1,-1,1,-1,-1,1,1]),this._vertices},n.getIndexData=function(){var e=this;return function(){for(var t=[],n=0;n<e._vertices.length/3;n+=4)t.push(n,n+1,n+2,n,n+2,n+3);return t}()},n.getTextureCoordData=function(e){for(var t=this,n=e.image,i=e.imageConfig,r=this.getDimension(n),o=i.trim,s=i.order||"RLUDFB",a=[],u=1;u>=0;u--)for(var l=0;l<3;l++){var c=[l/3,u/2,(l+1)/3,u/2,(l+1)/3,(u+1)/2,l/3,(u+1)/2];a.push(c)}var h=this._extractTileConfig(i);return a=a.map((function(e){return t._shrinkCoord(e,r,o)})).map((function(e,n){return t._transformCoord(e,h[n])})),"BFUDRL".split("").map((function(e){return s.indexOf(e)})).map((function(e){return a[e]})).reduce((function(e,t){return e.concat(t)}),[])},n.updateTexture=function(e,t){gi.texImage2D(e,e.TEXTURE_2D,this._getPixelSource(t))},n.bindTexture=function(e,t,n){var i=this.getDimension(n),r=i.width,o=i.height,s=Math.max(r,o),a=gi.getMaxTextureSize(e);s>a?this._triggerError("Image width("+r+") exceeds device limit("+a+"))"):(this._initPixelSource(n),e.activeTexture(e.TEXTURE0),e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL,!0),e.bindTexture(e.TEXTURE_2D,t),this.updateTexture(e,n))},n._transformCoord=function(e,t){var n=e.slice();return t.flipHorizontal&&(n=this._flipHorizontalCoord(n)),t.rotation&&(n=this._rotateCoord(n,t.rotation)),n},n._shrinkCoord=function(e,t,n){var i=t.width,r=n*(1/t.height),o=n*(1/i);return[e[0]+o,e[1]+r,e[2]-o,e[3]+r,e[4]-o,e[5]-r,e[6]+o,e[7]-r]},n._rotateCoord=function(e,t){var n,i=Math.floor(t/90)%4;if(0===i)return e;var r=[];return i>0?(n=e.splice(0,2*i),r=e.concat(n)):r=(n=e.splice(2*(4+i),2*-i)).concat(e),r},n._flipHorizontalCoord=function(e){return[e[2],e[3],e[0],e[1],e[6],e[7],e[4],e[5]]},t}(_i),xi=-.5*Math.PI,Ei=[],Ti=[],Si=[];for(Xn=0;Xn<=60;Xn++){var Ri=(Xn/60-.5)*Math.PI,Mi=Math.sin(Ri),Ci=Math.cos(Ri);for(Hn=0;Hn<=60;Hn++){var Pi=2*(Hn/60-.5)*Math.PI+xi,Fi=Math.sin(Pi),Di=Math.cos(Pi)*Ci,Ii=Mi,Oi=Fi*Ci,Bi=Hn/60,Li=Xn/60;if(Ei.push(Bi,Li),Ti.push(2*Di,2*Ii,2*Oi),60!==Hn&&60!==Xn){var Vi=61*Xn+Hn,ki=Vi+60+1;Si.push(Vi,ki,Vi+1,ki,ki+1,Vi+1)}}}var Ni=function(e){function t(t){var n=e.call(this)||this;return n._stereoFormat=t,n}Yt(t,e);var n=t.prototype;return n.render=function(t){var n,i,r=t.gl,o=t.shaderProgram;switch(this._stereoFormat){case ci.TOP_BOTTOM:n=[1,.5,0,0],i=[1,.5,0,.5];break;case ci.LEFT_RIGHT:n=[.5,1,0,0],i=[.5,1,.5,0];break;default:n=[1,1,0,0],i=[1,1,0,0]}var s=r.getUniformLocation(o,"uTexScaleOffset");r.uniform4fv(s,Jt(n,i)),e.prototype.render.call(this,t)},n.getVertexPositionData=function(){return t._VERTEX_POSITION_DATA},n.getIndexData=function(){return t._INDEX_DATA},n.getTextureCoordData=function(){return t._TEXTURE_COORD_DATA},n.getVertexShaderSource=function(){return"\nattribute vec3 aVertexPosition;\nattribute vec2 aTextureCoord;\nuniform mat4 uMVMatrix;\nuniform mat4 uPMatrix;\nuniform float uEye;\nuniform vec4 uTexScaleOffset[2];\nvarying highp vec2 vTextureCoord;\nvoid main(void) {\n  vec4 scaleOffset = uTexScaleOffset[int(uEye)];\n  vTextureCoord = aTextureCoord.xy * scaleOffset.xy + scaleOffset.zw;\n  gl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);\n}"},n.getFragmentShaderSource=function(){return"\nprecision highp float;\nvarying highp vec2 vTextureCoord;\nuniform sampler2D uSampler;\nvoid main(void) {\n  gl_FragColor = texture2D(uSampler, vTextureCoord.st);\n}"},n.updateTexture=function(e,t){gi.texImage2D(e,e.TEXTURE_2D,this._getPixelSource(t))},n.bindTexture=function(e,t,n){var i=this.getDimension(n),r=i.width,o=i.height,s=Math.max(r,o),a=gi.getMaxTextureSize(e);s>a?this._triggerError("Image width("+r+") exceeds device limit("+a+"))"):(this._initPixelSource(n),e.activeTexture(e.TEXTURE0),e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL,!0),e.bindTexture(e.TEXTURE_2D,t),this.updateTexture(e,n))},t._VERTEX_POSITION_DATA=Ti,t._TEXTURE_COORD_DATA=Ei,t._INDEX_DATA=Si,t}(_i),Qi=60,Ui=[],Wi=[],Gi=[],ji=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}Yt(t,e);var n=t.prototype;return n.getVertexPositionData=function(){return t._VERTEX_POSITION_DATA},n.getIndexData=function(){return t._INDEX_DATA},n.getTextureCoordData=function(){return t._TEXTURE_COORD_DATA},n.getVertexShaderSource=function(){return"\nattribute vec3 aVertexPosition;\nattribute vec2 aTextureCoord;\nuniform mat4 uMVMatrix;\nuniform mat4 uPMatrix;\nvarying highp vec2 vTextureCoord;\nvoid main(void) {\n  vTextureCoord = aTextureCoord;\n  gl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);\n}"},n.getFragmentShaderSource=function(){return"\nprecision highp float;\nvarying highp vec2 vTextureCoord;\nuniform sampler2D uSampler;\nvoid main(void) {\n  gl_FragColor = texture2D(uSampler, vec2(vTextureCoord.s, vTextureCoord.t));\n}"},n.updateTexture=function(e,t){gi.texImage2D(e,e.TEXTURE_2D,this._getPixelSource(t))},n.bindTexture=function(e,t,n){var i,r=this.getDimension(n),o=r.width,s=r.height,a=Math.max(o,s),u=gi.getMaxTextureSize(e);a>u&&(this._triggerError("Image width("+o+") exceeds device texture limit("+u+"))"),i=o>s?{width:u,height:u*s/o}:{width:u*o/s,height:u}),this._initPixelSource(n,i),e.activeTexture(e.TEXTURE0),e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL,!0),e.bindTexture(e.TEXTURE_2D,t),this.updateTexture(e,n)},n.updateShaderData=function(e){var t,n,i,r,o,s=e.imageAspectRatio,a=void 0===s?6:s;if(a<1?(r=!0,o=1/a):(r=!1,o=a),o>=6){var u=360/o;n=2*Math.PI,i=Math.tan(Ke(u/2))}else n=o,i=.5;Ui.length=0,Wi.length=0,Gi.length=0;for(var l=[-i,i],c=Math.PI/2+(2*Math.PI-n)/2,h=0,d=l.length;h<d;h++)for(t=0;t<=Qi;t++){var p=c+t/Qi*n,f=Math.cos(p),A=l[h],g=Math.sin(p),v=void 0,m=void 0;if(r?(v=1-h,m=t/Qi):(v=t/Qi,m=h),Ui.push(v,m),Wi.push(f,A,g),0===h&&t<Qi){var y=t,_=y+Qi+1;Gi.push(y,_,y+1,_,_+1,y+1)}}},t._VERTEX_POSITION_DATA=Wi,t._TEXTURE_COORD_DATA=Ui,t._INDEX_DATA=Gi,t}(_i),zi="vrdisplaypresentchange",Xi=[0,0,.5,1],Hi=[.5,0,.5,1],qi=function(){function e(){var e=this;this.destroy=function(){var t=e._vrDisplay;e.removeEndCallback(e.destroy),t&&t.isPresenting&&t.exitPresent(),e._clear()},this._frameData=new window.VRFrameData,this._clear()}var t=e.prototype;return Object.defineProperty(t,"context",{get:function(){return this._vrDisplay},enumerable:!1,configurable:!0}),t.canRender=function(){return Boolean(this._vrDisplay)},t.beforeRender=function(e){e.bindFramebuffer(e.FRAMEBUFFER,null)},t.afterRender=function(){this._vrDisplay.submitFrame()},t.getEyeParams=function(e){var t=this._vrDisplay,n=.5*e.drawingBufferWidth,i=e.drawingBufferHeight,r=this._frameData;t.getFrameData(r);var o=r.leftViewMatrix,s=r.rightViewMatrix;return yt(o,o,this._yawOffset),yt(s,s,this._yawOffset),[{viewport:[0,0,n,i],mvMatrix:o,pMatrix:r.leftProjectionMatrix},{viewport:[n,0,n,i],mvMatrix:s,pMatrix:r.rightProjectionMatrix}]},t.isPresenting=function(){return Boolean(this._vrDisplay&&this._vrDisplay.isPresenting)},t.addEndCallback=function(e){window.addEventListener(zi,e)},t.removeEndCallback=function(e){window.removeEventListener(zi,e)},t.requestPresent=function(e){var t=this;return navigator.getVRDisplays().then((function(n){var i=n.length&&n[0];return i?i.capabilities.canPresent?i.requestPresent([{source:e}]).then((function(){var n=i.getEyeParameters("left"),r=i.getEyeParameters("right");e.width=2*Math.max(n.renderWidth,r.renderWidth),e.height=Math.max(n.renderHeight,r.renderHeight),t._setDisplay(i)})):w.reject(new Error("Display lacking capability to present.")):w.reject(new Error("No displays available."))}))},t.setYawOffset=function(e){this._yawOffset=e},t._setDisplay=function(e){this._vrDisplay=e;var t=e.getLayers();if(t.length){var n=t[0];this._leftBounds=n.leftBounds,this._rightBounds=n.rightBounds}this.addEndCallback(this.destroy)},t._clear=function(){this._vrDisplay=null,this._leftBounds=Xi,this._rightBounds=Hi,this._yawOffset=0},e}(),Yi="local",Zi=function(){function e(e){var t=this;void 0===e&&(e={}),this.destroy=function(){var e=t._xrSession;t.removeEndCallback(t.destroy),e&&e.end().then((function(){}),(function(){})),t._clear()},this._clear(),this._options=e}var t=e.prototype;return Object.defineProperty(t,"context",{get:function(){return this._xrSession},enumerable:!1,configurable:!0}),t.canRender=function(e){var t=e.getViewerPose(this._xrRefSpace);return Boolean(t)},t.beforeRender=function(e,t){var n=t.session.renderState.baseLayer;e.bindFramebuffer(e.FRAMEBUFFER,n.framebuffer)},t.afterRender=function(){},t.getEyeParams=function(e,t){var n=this,i=t.session,r=t.getViewerPose(this._xrRefSpace);if(!r)return null;var o=i.renderState.baseLayer;return r.views.map((function(e){var t=o.getViewport(e),i=e.transform.inverse.matrix;return un&&mt(i,i,Ke(180)),yt(i,i,n._yawOffset),{viewport:[t.x,t.y,t.width,t.height],mvMatrix:i,pMatrix:e.projectionMatrix}}))},t.isPresenting=function(){return this._presenting},t.addEndCallback=function(e){var t;null===(t=this._xrSession)||void 0===t||t.addEventListener("end",e)},t.removeEndCallback=function(e){var t;null===(t=this._xrSession)||void 0===t||t.removeEventListener("end",e)},t.requestPresent=function(e,t){return n=this,i=void 0,o=function(){var e,n,i=this;return function(e,t){var n,i,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,i&&(r=2&o[0]?i.return:o[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,i=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!((r=(r=s.trys).length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}(this,(function(r){switch(r.label){case 0:return e=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.forEach((function(t){Object.keys(t).forEach((function(n){var i=t[n];Array.isArray(e[n])&&Array.isArray(i)?e[n]=Jt(e[n],i):e[n]=i}))})),e}({requiredFeatures:[Yi]},this._options),(n=t.getContextAttributes())&&!0!==n.xrCompatible?[4,t.makeXRCompatible()]:[3,2];case 1:r.sent(),r.label=2;case 2:return[2,navigator.xr.requestSession("immersive-vr",e).then((function(e){var n=new window.XRWebGLLayer(e,t);return e.updateRenderState({baseLayer:n}),e.requestReferenceSpace(Yi).then((function(t){i._setSession(e,n,t)}))}))]}}))},new((r=void 0)||(r=Promise))((function(e,t){function s(e){try{u(o.next(e))}catch(e){t(e)}}function a(e){try{u(o.throw(e))}catch(e){t(e)}}function u(t){var n;t.done?e(t.value):(n=t.value,n instanceof r?n:new r((function(e){e(n)}))).then(s,a)}u((o=o.apply(n,i||[])).next())}));var n,i,r,o},t.setYawOffset=function(e){this._yawOffset=e},t._setSession=function(e,t,n){this._xrSession=e,this._xrLayer=t,this._xrRefSpace=n,this._presenting=!0,this.addEndCallback(this.destroy)},t._clear=function(){this._xrSession=null,this._xrLayer=null,this._xrRefSpace=null,this._presenting=!1,this._yawOffset=0,this._options={}},e}(),Ki=function(){function e(){var e=this;this._onLoop=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];e._callback.apply(e,Jt(t)),e._rafId=e._context.requestAnimationFrame(e._onLoop)},this._onLoopNextTick=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var i=performance.now();e._callback.apply(e,Jt(t));var r=performance.now()-i;e._rafTimer>=0&&(clearTimeout(e._rafTimer),e._rafTimer=-1),r<16?e._rafId=e._context.requestAnimationFrame(e._onLoop):e._rafTimer=window.setTimeout(e._onLoop,0)},this._callback=null,this._context=window,this._rafId=-1,this._rafTimer=-1}var t=e.prototype;return t.setCallback=function(e){this._callback=e},t.setContext=function(e){this._context=e},t.start=function(){var e=this._context,t=this._callback;e&&t&&(this._rafId>=0||this._rafTimer>=0||(this._rafId=un?e.requestAnimationFrame(this._onLoopNextTick):e.requestAnimationFrame(this._onLoop)))},t.stop=function(){this._rafId>=0&&this._context.cancelAnimationFrame(this._rafId),this._rafTimer>=0&&clearTimeout(this._rafTimer),this._rafId=-1,this._rafTimer=-1},e}(),$i=li,Ji=pn||1;Ji>2&&(Ji=2);var er={BIND_TEXTURE:"bindTexture",IMAGE_LOADED:"imageLoaded",ERROR:"error",RENDERING_CONTEXT_LOST:"renderingContextLost",RENDERING_CONTEXT_RESTORE:"renderingContextRestore"},tr={INVALID_DEVICE:10,NO_WEBGL:11,FAIL_IMAGE_LOAD:12,RENDERER_ERROR:13},nr=function(e){function t(t,n,i,r,o,s,a,u){var l=e.call(this)||this;return l.textureCoordBuffer=null,l.vertexBuffer=null,l.indexBuffer=null,l.exitVR=function(){var e=l._vr,t=l.context,n=l._animator;e&&(e.removeEndCallback(l.exitVR),e.destroy(),l._vr=null,an&&l._restoreStyle(),l.updateViewportDimensions(l.width,l.height),l._updateViewport(),t.bindFramebuffer(t.FRAMEBUFFER,null),l._bindBuffers(),l._shouldForceDraw=!0,n.stop(),n.setContext(window),n.setCallback(l._render.bind(l)),n.start())},l._renderStereo=function(e,t){var n,i,r=l._vr,o=l.context,s=r.getEyeParams(o,t);if(s){r.beforeRender(o,t);try{for(var a=Kt([0,1]),u=a.next();!u.done;u=a.next()){var c=u.value,h=s[c];l.mvMatrix=h.mvMatrix,l.pMatrix=h.pMatrix,o.viewport.apply(o,Jt(h.viewport)),o.uniform1f(l.shaderProgram.uEye,c),l._bindBuffers(),l._draw()}}catch(e){n={error:e}}finally{try{u&&!u.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}r.afterRender()}},l._onFirstVRFrame=function(e,t){var n=l._vr,i=l.context,r=l._animator;if(n.canRender(t)){var o=et(0,0,-1),s=n.getEyeParams(i,t)[0],a=at(st(),s.mvMatrix),u=at(st(),s.pMatrix),c=ut(st(),a),h=ut(st(),u),d=it($e(),o,h);it(d,d,c);var p=mn.yawOffsetBetween(d,et(0,0,1));0!==p&&(n.setYawOffset(p),r.setCallback(l._renderStereo))}},l.sphericalConfig=a,l.fieldOfView=a.fieldOfView,l.width=n,l.height=i,l._lastQuaternion=null,l._lastYaw=null,l._lastPitch=null,l._lastFieldOfView=null,l.pMatrix=vt(),l.mvMatrix=vt(),_t(l.pMatrix,Ke(l.fieldOfView),n/i,.1,100),l.textureCoordBuffer=null,l.vertexBuffer=null,l.indexBuffer=null,l.canvas=l._initCanvas(o,s,n,i),l._setDefaultCanvasStyle(),l._wrapper=null,l._wrapperOrigStyle=null,l._renderingContextAttributes=u,l._image=null,l._imageConfig=null,l._imageIsReady=!1,l._shouldForceDraw=!1,l._keepUpdate=!1,l._onContentLoad=l._onContentLoad.bind(l),l._onContentError=l._onContentError.bind(l),l._animator=new Ki,l._vr=null,t&&l.setImage({image:t,imageType:a.imageType,isVideo:r,cubemapConfig:a.cubemapConfig}),l}Yt(t,e);var n=t.prototype;return n.setYawPitchControl=function(e){this._yawPitchControl=e},n.getContent=function(){return this._image},n.setImage=function(e){var t=e.image,n=e.imageType,i=e.isVideo,r=void 0!==i&&i,o=e.cubemapConfig;this._imageIsReady=!1,this._isVideo=r,this._imageConfig=Zt({order:n===$i.CUBEMAP?"RLUDBF":"RLUDFB",tileConfig:{flipHorizontal:!1,rotation:0},trim:0},o),this._setImageType(n),this._contentLoader&&this._contentLoader.destroy(),this._contentLoader=(new Xt).on("ready",this._onContentLoad).on("error",this._onContentError),r?(this._image=function(e){if(e instanceof HTMLVideoElement)return e;var t=document.createElement("video");return t.setAttribute("crossorigin","anonymous"),t.setAttribute("webkit-playsinline",""),t.setAttribute("playsinline",""),e instanceof Array?e.forEach((function(e){return pi(t,e)})):pi(t,e),t.querySelectorAll("source").length>0&&t.readyState<1&&t.load(),t}(t),this._contentLoader.check([this._image]),this._keepUpdate=!0):(this._image=function(e){var t=(e instanceof Array?e:[e]).map((function(e){var t=e;return"string"==typeof e&&((t=new Image).crossOrigin="anonymous",t.src=e),t}));return 1===t.length?t[0]:t}(t),this._contentLoader.check(Array.isArray(this._image)?this._image:[this._image]),this._keepUpdate=!1)},n.isImageLoaded=function(){return!!this._image&&this._imageIsReady&&(!this._isVideo||this._image.readyState>=2)},n.bindTexture=function(){var e=this;return new w((function(t,n){var i=e._contentLoader;return e._image?i?void(i.isReady()?(e._bindTexture(),t()):(i.check(Array.isArray(e._image)?e._image:[e._image]),i.once("ready",(function(i){i.errorCount>0?n("Failed to load images."):(e._bindTexture(),t())})))):n("ImageLoader is not initialized"):n("Image is not defined")}))},n.attachTo=function(e){this._hasExternalCanvas||(this.detach(),e.appendChild(this.canvas)),this._wrapper=e},n.forceContextLoss=function(){if(this.hasRenderingContext()){var e=this.context.getExtension("WEBGL_lose_context");e&&e.loseContext()}},n.detach=function(){!this._hasExternalCanvas&&this.canvas.parentElement&&this.canvas.parentElement.removeChild(this.canvas)},n.destroy=function(){this._contentLoader&&this._contentLoader.destroy(),this._animator.stop(),this.detach(),this.forceContextLoss(),this.off(),this.canvas.removeEventListener("webglcontextlost",this._onWebglcontextlost),this.canvas.removeEventListener("webglcontextrestored",this._onWebglcontextrestored)},n.hasRenderingContext=function(){var e=this.context;return!(!e||e.isContextLost()||!e.getProgramParameter(this.shaderProgram,e.LINK_STATUS))},n.updateFieldOfView=function(e){this.fieldOfView=e,this._updateViewport()},n.updateViewportDimensions=function(e,t){var n=!1;this.width=e,this.height=t;var i=e*Ji,r=t*Ji;i!==this.canvas.width&&(this.canvas.width=i,n=!0),r!==this.canvas.height&&(this.canvas.height=r,n=!0),n&&(this._updateViewport(),this._shouldForceDraw=!0)},n.keepUpdate=function(e){e&&!1===this.isImageLoaded()&&(this._shouldForceDraw=!0),this._keepUpdate=e},n.startRender=function(){this._animator.setCallback(this._render.bind(this)),this._animator.start()},n.stopRender=function(){this._animator.stop()},n.renderWithQuaternion=function(e,t){this.isImageLoaded()&&(!1===this._keepUpdate&&this._lastQuaternion&&function(e,t){return e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]}(this._lastQuaternion,e)&&this.fieldOfView&&this.fieldOfView===t&&!1===this._shouldForceDraw||(void 0!==t&&t!==this.fieldOfView&&this.updateFieldOfView(t),this.mvMatrix=function(e,t){var n=t[0],i=t[1],r=t[2],o=t[3],s=n+n,a=i+i,u=r+r,l=n*s,c=i*s,h=i*a,d=r*s,p=r*a,f=r*u,A=o*s,g=o*a,v=o*u;return e[0]=1-h-f,e[1]=c+v,e[2]=d-g,e[3]=0,e[4]=c-v,e[5]=1-l-f,e[6]=p+A,e[7]=0,e[8]=d+g,e[9]=p-A,e[10]=1-l-h,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e}(vt(),e),this._draw(),this._lastQuaternion=ct(e),this._shouldForceDraw&&(this._shouldForceDraw=!1)))},n.renderWithYawPitch=function(e,t,n){this.isImageLoaded()&&(!1===this._keepUpdate&&null!==this._lastYaw&&this._lastYaw===e&&null!==this._lastPitch&&this._lastPitch===t&&this.fieldOfView&&this.fieldOfView===n&&!1===this._shouldForceDraw||(void 0!==n&&n!==this.fieldOfView&&this.updateFieldOfView(n),function(e){e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=1,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=1,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1}(this.mvMatrix),mt(this.mvMatrix,this.mvMatrix,-Ke(t)),yt(this.mvMatrix,this.mvMatrix,-Ke(e)),this._draw(),this._lastYaw=e,this._lastPitch=t,this._shouldForceDraw&&(this._shouldForceDraw=!1)))},n.getProjectionRenderer=function(){return this._renderer},n.enterVR=function(e){var t=this._vr;return gn||navigator.getVRDisplays?t&&t.isPresenting()?w.resolve("VR already enabled."):this._requestPresent(e):w.reject("VR is not available on this browser.")},n._setImageType=function(e){var t=this;if(e&&this._imageType!==e){switch(this._imageType=e,this._isCubeMap=e===$i.CUBEMAP,this._renderer&&this._renderer.off(),e){case $i.CUBEMAP:this._renderer=new wi;break;case $i.CUBESTRIP:this._renderer=new bi;break;case $i.PANORAMA:this._renderer=new ji;break;case $i.STEREOSCOPIC_EQUI:this._renderer=new Ni(this.sphericalConfig.stereoFormat);break;default:this._renderer=new Ni(ci.NONE)}this._renderer.on(_i.EVENTS.ERROR,(function(e){t.trigger(new u(er.ERROR,{type:tr.RENDERER_ERROR,message:e.message}))})),this._initWebGL()}},n._initCanvas=function(e,t,n,i){var r=e.querySelector("."+t),o=r||this._createCanvas(t);return this._hasExternalCanvas=!!r,o.width=n,o.height=i,this._onWebglcontextlost=this._onWebglcontextlost.bind(this),this._onWebglcontextrestored=this._onWebglcontextrestored.bind(this),o.addEventListener("webglcontextlost",this._onWebglcontextlost),o.addEventListener("webglcontextrestored",this._onWebglcontextrestored),o},n._createCanvas=function(e){var t=document.createElement("canvas");return t.className=e,t},n._setDefaultCanvasStyle=function(){var e=this.canvas;e.style.bottom="0",e.style.left="0",e.style.right="0",e.style.top="0",e.style.margin="auto",e.style.maxHeight="100%",e.style.maxWidth="100%",e.style.outline="none",e.style.position="absolute"},n._onContentError=function(){return this._imageIsReady=!1,this._image=null,this.trigger(new u(er.ERROR,{type:tr.FAIL_IMAGE_LOAD,message:"failed to load image"})),!1},n._triggerContentLoad=function(){this.trigger(new u(er.IMAGE_LOADED,{content:this._image,isVideo:this._isVideo,projectionType:this._imageType}))},n._onContentLoad=function(e){e.errorCount>0||(this._imageIsReady=!0,this._triggerContentLoad())},n._initShaderProgram=function(){var e=this.context;this.shaderProgram&&(e.deleteProgram(this.shaderProgram),this.shaderProgram=null);var t=this._renderer,n=t.getVertexShaderSource(),i=t.getFragmentShaderSource(),r=gi.createShader(e,e.VERTEX_SHADER,n),o=gi.createShader(e,e.FRAGMENT_SHADER,i),s=gi.createProgram(e,r,o);if(!s)throw new Error("Failed to initialize shaders: "+gi.getErrorNameFromWebGLErrorCode(e.getError()));e.useProgram(s),s.vertexPositionAttribute=e.getAttribLocation(s,"aVertexPosition"),s.pMatrixUniform=e.getUniformLocation(s,"uPMatrix"),s.mvMatrixUniform=e.getUniformLocation(s,"uMVMatrix"),s.samplerUniform=e.getUniformLocation(s,"uSampler"),s.textureCoordAttribute=e.getAttribLocation(s,"aTextureCoord"),s.uEye=e.getUniformLocation(s,"uEye"),e.enableVertexAttribArray(s.vertexPositionAttribute),e.enableVertexAttribArray(s.textureCoordAttribute),e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT|e.STENCIL_BUFFER_BIT),e.uniform1i(s.samplerUniform,0),this.shaderProgram=s},n._onWebglcontextlost=function(e){e.preventDefault(),this.trigger(new u(er.RENDERING_CONTEXT_LOST))},n._onWebglcontextrestored=function(){this._initWebGL(),this.trigger(new u(er.RENDERING_CONTEXT_RESTORE))},n._updateViewport=function(){_t(this.pMatrix,Ke(this.fieldOfView),this.canvas.width/this.canvas.height,.1,100),this.context.viewport(0,0,this.context.drawingBufferWidth,this.context.drawingBufferHeight)},n._initWebGL=function(){var e;try{this._initRenderingContext(),e=this.context,this.updateViewportDimensions(this.width,this.height),this._initShaderProgram()}catch(e){return this.trigger(new u(er.ERROR,{type:tr.NO_WEBGL,message:"no webgl support"})),this.destroy(),void console.error(e)}e.clearColor(0,0,0,0);var t=this._isCubeMap?e.TEXTURE_CUBE_MAP:e.TEXTURE_2D;this.texture&&e.deleteTexture(this.texture),this.texture=gi.createTexture(e,t),this._imageType===$i.CUBESTRIP&&e.enable(e.CULL_FACE)},n._initRenderingContext=function(){if(!this.hasRenderingContext()){if(!window.WebGLRenderingContext)throw new Error("WebGLRenderingContext not available.");if(this.context=gi.getWebglContext(this.canvas,this._renderingContextAttributes),!this.context)throw new Error("Failed to acquire 3D rendering context")}},n._initBuffers=function(){var e=this._image,t=this._renderer.getVertexPositionData(),n=this._renderer.getIndexData(),i=this._renderer.getTextureCoordData({image:e,imageConfig:this._imageConfig}),r=this.context;this.vertexBuffer=gi.initBuffer(r,r.ARRAY_BUFFER,new Float32Array(t),3,this.shaderProgram.vertexPositionAttribute),this.indexBuffer=gi.initBuffer(r,r.ELEMENT_ARRAY_BUFFER,new Uint16Array(n),1),this.textureCoordBuffer=gi.initBuffer(r,r.ARRAY_BUFFER,new Float32Array(i),this._isCubeMap?3:2,this.shaderProgram.textureCoordAttribute),this._bindBuffers()},n._bindTexture=function(){if(this._imageType===$i.CUBESTRIP){var e=this._renderer.getDimension(this._image),t=e.width,n=e.height,i=t&&n&&t/n!=1.5?1:0;this.context.uniform1f(this.context.getUniformLocation(this.shaderProgram,"uIsEAC"),i)}else if(this._imageType===$i.PANORAMA){var r=this._renderer.getDimension(this._image),o=(t=r.width,n=r.height,t&&n&&t/n);this._renderer.updateShaderData({imageAspectRatio:o})}this._initBuffers(),this._renderer.bindTexture(this.context,this.texture,this._image,this._imageConfig),this._shouldForceDraw=!0,this.trigger(new u(er.BIND_TEXTURE))},n._updateTexture=function(){this._renderer.updateTexture(this.context,this._image,this._imageConfig)},n._render=function(){var e=this._yawPitchControl,t=e.getFov();if(e.shouldRenderWithQuaternion()){var n=e.getQuaternion();this.renderWithQuaternion(n,t)}else{var i=e.getYawPitch();this.renderWithYawPitch(i.yaw,i.pitch,t)}},n._bindBuffers=function(){var e=this.context,t=this.shaderProgram,n=this.vertexBuffer,i=this.textureCoordBuffer;e.bindBuffer(e.ARRAY_BUFFER,n),e.enableVertexAttribArray(t.vertexPositionAttribute),e.vertexAttribPointer(t.vertexPositionAttribute,n.itemSize,e.FLOAT,!1,0,0),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,this.indexBuffer),e.bindBuffer(e.ARRAY_BUFFER,i),e.enableVertexAttribArray(t.textureCoordAttribute),e.vertexAttribPointer(t.textureCoordAttribute,i.itemSize,e.FLOAT,!1,0,0)},n._draw=function(){this._isVideo&&this._keepUpdate&&this._updateTexture(),this._renderer.render({gl:this.context,shaderProgram:this.shaderProgram,indexBuffer:this.indexBuffer,mvMatrix:this.mvMatrix,pMatrix:this.pMatrix})},n._requestPresent=function(e){var t=this,n=this.context,i=this.canvas,r=this._animator;this._vr=gn?new Zi(e):new qi;var o=this._vr;return r.stop(),new w((function(e,s){o.requestPresent(i,n).then((function(){o.addEndCallback(t.exitVR),r.setContext(o.context),r.setCallback(t._onFirstVRFrame),an&&t._setWrapperFullscreen(),t._shouldForceDraw=!0,r.start(),e("success")})).catch((function(e){o.destroy(),t._vr=null,r.start(),s(e)}))}))},n._setWrapperFullscreen=function(){var e=this._wrapper;if(e){this._wrapperOrigStyle=e.getAttribute("style");var t=e.style;t.width="100vw",t.height="100vh",t.position="fixed",t.left="0",t.top="0",t.zIndex="9999"}},n._restoreStyle=function(){var e=this._wrapper,t=this.canvas;e&&(this._wrapperOrigStyle?e.setAttribute("style",this._wrapperOrigStyle):e.removeAttribute("style"),this._wrapperOrigStyle=null,t.removeAttribute("style"),this._setDefaultCanvasStyle())},t.EVENTS=er,t.ERROR_TYPE=tr,t}(l),ir=function(e){function t(n,i){void 0===i&&(i={});var r=e.call(this)||this;if(!gi.isWebGLAvailable())return setTimeout((function(){r.trigger(new u(ui.ERROR,{type:ai.NO_WEBGL,message:"no webgl support"}))}),0),r;if(!gi.isStableWebGL())return setTimeout((function(){r.trigger(new u(ui.ERROR,{type:ai.INVALID_DEVICE,message:"blacklisted browser"}))}),0),r;if(i.image&&i.video)return setTimeout((function(){r.trigger(new u(ui.ERROR,{type:ai.INVALID_RESOURCE,message:"Specifying multi resouces(both image and video) is not valid."}))}),0),r;!function(){var e=window.navigator;e.xr&&(e.xr.isSessionSupported?e.xr.isSessionSupported("immersive-vr").then((function(e){gn=e})).catch((function(){})):e.xr.supportsSession&&e.xr.supportsSession("immersive-vr").then((function(e){gn=e})).catch((function(){})))}(),r._container=n,r._image=i.image||i.video,r._isVideo=!!i.video,r._projectionType=i.projectionType||li.EQUIRECTANGULAR,r._cubemapConfig=Zt({order:r._projectionType===li.CUBEMAP?"RLUDBF":"RLUDFB",tileConfig:{flipHorizontal:!1,rotation:0},trim:0},i.cubemapConfig),r._stereoFormat=i.stereoFormat||ci.TOP_BOTTOM,r._width=i.width||parseInt(window.getComputedStyle(n).width,10),r._height=i.height||parseInt(window.getComputedStyle(n).height,10),r._yaw=i.yaw||0,r._pitch=i.pitch||0,r._fov=i.fov||65,r._gyroMode=i.gyroMode||In.YAWPITCH,r._quaternion=null,r._aspectRatio=0!==r._height?r._width/r._height:1,r._canvasClass=i.canvasClass||di;var o=i.fovRange||[30,110],s=t._isValidTouchDirection(i.touchDirection)?i.touchDirection:si.TOUCH_DIRECTION_ALL,a=Zt(Zt({},i),{element:n,yaw:r._yaw,pitch:r._pitch,fov:r._fov,gyroMode:r._gyroMode,fovRange:o,aspectRatio:r._aspectRatio,touchDirection:s});return r._isReady=!1,r._initYawPitchControl(a),r._initRenderer(r._yaw,r._pitch,r._fov,r._projectionType,r._cubemapConfig),r}Yt(t,e);var n=t.prototype;return t.isSupported=function(){return gi.isWebGLAvailable()&&gi.isStableWebGL()},t.isWebGLAvailable=function(){return gi.isWebGLAvailable()},t.isGyroSensorAvailable=function(e){var n;dn||!e?w.race([new w((function(e){n=function(t){var n=!(null==t.rotationRate.alpha);e(n)},window.addEventListener("devicemotion",n)})),new w((function(e){setTimeout((function(){return e(!1)}),1e3)}))]).then((function(i){window.removeEventListener("devicemotion",n),e&&e(i),t.isGyroSensorAvailable=function(e){return e&&e(i),i}})):e(!1)},t._isValidTouchDirection=function(e){return e===t.TOUCH_DIRECTION.NONE||e===t.TOUCH_DIRECTION.YAW||e===t.TOUCH_DIRECTION.PITCH||e===t.TOUCH_DIRECTION.ALL},n.getVideo=function(){return this._isVideo?this._photoSphereRenderer.getContent():null},n.setVideo=function(e,t){return void 0===t&&(t={}),e&&this.setImage(e,{projectionType:t.projectionType,isVideo:!0,cubemapConfig:t.cubemapConfig,stereoFormat:t.stereoFormat}),this},n.getImage=function(){return this._isVideo?null:this._photoSphereRenderer.getContent()},n.setImage=function(e,t){void 0===t&&(t={});var n=Zt({order:"RLUDBF",tileConfig:{flipHorizontal:!1,rotation:0},trim:0},t.cubemapConfig),i=t.stereoFormat||ci.TOP_BOTTOM,r=!!t.isVideo;return this._image&&this._isVideo!==r?(console.warn("PanoViewer is not currently supporting content type changes. (Image <--\x3e Video)"),this):(e&&(this._deactivate(),this._image=e,this._isVideo=r,this._projectionType=t.projectionType||li.EQUIRECTANGULAR,this._cubemapConfig=n,this._stereoFormat=i,this._initRenderer(this._yaw,this._pitch,this._fov,this._projectionType,this._cubemapConfig)),this)},n.keepUpdate=function(e){return this._photoSphereRenderer.keepUpdate(e),this},n.getProjectionType=function(){return this._projectionType},n.enableSensor=function(){return new w((function(e,t){dn&&"function"==typeof dn.requestPermission?dn.requestPermission().then((function(n){"granted"===n?e():t(new Error("permission denied"))})).catch((function(e){t(e)})):e()}))},n.disableSensor=function(){return this},n.enterVR=function(e){var t=this;return void 0===e&&(e={}),this._isReady?new w((function(n,i){t.enableSensor().then((function(){return t._photoSphereRenderer.enterVR(e)})).then((function(e){return n(e)})).catch((function(e){return i(e)}))})):w.reject(new Error("PanoViewer is not ready to show image."))},n.exitVR=function(){return this._photoSphereRenderer.exitVR(),this},n.setUseZoom=function(e){return"boolean"==typeof e&&this._yawPitchControl.option("useZoom",e),this},n.setUseKeyboard=function(e){return this._yawPitchControl.option("useKeyboard",e),this},n.setGyroMode=function(e){return this._yawPitchControl.option("gyroMode",e),this},n.setFovRange=function(e){return this._yawPitchControl.option("fovRange",e),this},n.getFovRange=function(){return this._yawPitchControl.option("fovRange")},n.updateViewportDimensions=function(e){if(void 0===e&&(e={}),!this._isReady)return this;var t;void 0!==e.width&&void 0!==e.height||(t=window.getComputedStyle(this._container));var n=e.width||parseInt(t.width,10),i=e.height||parseInt(t.height,10);return n===this._width&&i===this._height||(this._width=n,this._height=i,this._aspectRatio=n/i,this._photoSphereRenderer.updateViewportDimensions(n,i),this._yawPitchControl.option("aspectRatio",this._aspectRatio),this._yawPitchControl.updatePanScale({height:i}),this.lookAt({},0)),this},n.getFov=function(){return this._fov},n.getYaw=function(){return this._yaw},n.getPitch=function(){return this._pitch},n.getYawRange=function(){return this._yawPitchControl.option("yawRange")},n.getPitchRange=function(){return this._yawPitchControl.option("pitchRange")},n.setYawRange=function(e){return this._yawPitchControl.option("yawRange",e),this},n.setPitchRange=function(e){return this._yawPitchControl.option("pitchRange",e),this},n.setShowPolePoint=function(e){return this._yawPitchControl.option("showPolePoint",e),this},n.lookAt=function(e,t){if(void 0===t&&(t=0),!this._isReady)return this;var n=void 0!==e.yaw?e.yaw:this._yaw,i=void 0!==e.pitch?e.pitch:this._pitch,r=this._yawPitchControl.option("pitchRange"),o=r[1]-r[0],s=void 0!==e.fov?e.fov:this._fov;return o<s&&(s=o),this._yawPitchControl.lookAt({yaw:n,pitch:i,fov:s},t),0===t&&this._photoSphereRenderer.renderWithYawPitch(n,i,s),this},n.setTouchDirection=function(e){return t._isValidTouchDirection(e)&&this._yawPitchControl.option("touchDirection",e),this},n.getTouchDirection=function(){return this._yawPitchControl.option("touchDirection")},n.destroy=function(){return this._deactivate(),this._yawPitchControl&&(this._yawPitchControl.destroy(),this._yawPitchControl=null),this},n._initRenderer=function(e,t,n,i,r){var o=this;this._photoSphereRenderer=new nr(this._image,this._width,this._height,this._isVideo,this._container,this._canvasClass,{initialYaw:e,initialPitch:t,fieldOfView:n,imageType:i,cubemapConfig:r,stereoFormat:this._stereoFormat}),this._photoSphereRenderer.setYawPitchControl(this._yawPitchControl),this._bindRendererHandler(),this._photoSphereRenderer.bindTexture().then((function(){return o._activate()})).catch((function(){o.trigger(new u(ui.ERROR,{type:ai.FAIL_BIND_TEXTURE,message:"failed to bind texture"}))}))},n._updateYawPitchIfNeeded=function(){if(this._projectionType===t.ProjectionType.PANORAMA){var e=this._photoSphereRenderer.getContent(),n=e.naturalWidth/e.naturalHeight,i=void 0,r=void 0;n<1&&(n=1/n),n<6?(i=mn.toDegree(n),r=2*mn.toDegree(Math.atan(.5))):(i=360,r=360/n);var o=this._yawPitchControl.option("fovRange")[0];this._yawPitchControl.option({fov:r,yawRange:[-i/2,i/2],pitchRange:[-r/2,r/2],fovRange:[o,r]}),this.lookAt({fov:r})}},n._bindRendererHandler=function(){var e=this;this._photoSphereRenderer.on(nr.EVENTS.ERROR,(function(t){e.trigger(new u(ui.ERROR,t))})),this._photoSphereRenderer.on(nr.EVENTS.RENDERING_CONTEXT_LOST,(function(){e._deactivate(),e.trigger(new u(ui.ERROR,{type:ai.RENDERING_CONTEXT_LOST,message:"webgl rendering context lost"}))}))},n._initYawPitchControl=function(e){var t=this;this._yawPitchControl=new si(e),this._yawPitchControl.on(ui.ANIMATION_END,(function(e){t.trigger(new u(ui.ANIMATION_END,e))})),this._yawPitchControl.on("change",(function(e){t._yaw=e.yaw,t._pitch=e.pitch,t._fov=e.fov,t._quaternion=e.quaternion,t.trigger(new u(ui.VIEW_CHANGE,{yaw:e.yaw,pitch:e.pitch,fov:e.fov,quaternion:e.quaternion,isTrusted:e.isTrusted}))}))},n._activate=function(){this._photoSphereRenderer.attachTo(this._container),this._yawPitchControl.enable(),this.updateViewportDimensions(),this._isReady=!0,this._updateYawPitchIfNeeded(),this.trigger(new u(ui.READY)),this._photoSphereRenderer.startRender()},n._deactivate=function(){var e=this.getVideo();e&&e.pause(),this._isReady&&(this._photoSphereRenderer.stopRender(),this._yawPitchControl.disable(),this._isReady=!1),this._photoSphereRenderer&&(this._photoSphereRenderer.destroy(),this._photoSphereRenderer=null)},t.VERSION=Ht,t.ERROR_TYPE=ai,t.EVENTS=ui,t.PROJECTION_TYPE=li,t.GYRO_MODE=In,t.ProjectionType=li,t.STEREO_FORMAT=ci,t.TOUCH_DIRECTION={NONE:si.TOUCH_DIRECTION_NONE,YAW:si.TOUCH_DIRECTION_YAW,PITCH:si.TOUCH_DIRECTION_PITCH,ALL:si.TOUCH_DIRECTION_ALL},t}(l),rr={imageUrl:!0,rowCount:!0,colCount:!0,width:!0,height:!0,autoHeight:!0,colRow:!0,scale:!0,frameIndex:!0,wrapperClass:!0,imageClass:!0},or={LOAD:"load",IMAGE_ERROR:"imageError",CHANGE:"change",ANIMATION_END:"animationEnd"},sr="view360-wrapper",ar="view360-image",ur=function(e){function t(n,i){void 0===i&&(i={});var r=e.call(this)||this,o=i||{};r._el=n,r._rowCount=o.rowCount||1,r._colCount=o.colCount||1,r._totalCount=r._rowCount*r._colCount,r._width=o.width||"auto",r._height=o.height||"auto",r._autoHeight=null==o.autoHeight||o.autoHeight,r._colRow=[0,0],o.colRow?r._colRow=o.colRow:o.frameIndex&&r.setFrameIndex(o.frameIndex),r._el.style.width=t._getSizeString(r._width),r._el.style.height=t._getSizeString(r._height);var s=o.wrapperClass||sr,a=o.imageClass||ar;if(!o.imageUrl)return setTimeout((function(){r.trigger(new u("imageError",{imageUrl:o.imageUrl}))}),0),r;var l=n.querySelector("."+a),c=n.querySelector("."+s);c&&l&&(l.style.display="none"),r._image=l||new Image;var h=r._image;return h.onload=function(){c&&l&&(l.style.display=""),r._bg=t._createBgDiv(c,h,r._rowCount,r._colCount,r._autoHeight),r._el.appendChild(r._bg),r.setColRow(r._colRow[0],r._colRow[1]),r.trigger(new u("load",{target:r._el,bgElement:r._bg})),r._autoPlayReservedInfo&&(r.play(r._autoPlayReservedInfo),r._autoPlayReservedInfo=null)},h.onerror=function(){r.trigger(new u("imageError",{imageUrl:o.imageUrl}))},h.src=o.imageUrl,r}Yt(t,e);var n=t.prototype;return t._createBgDiv=function(e,t,n,i,r){var o=e||document.createElement("div");o.style.position="relative",o.style.overflow="hidden",t.style.position="absolute",t.style.width=100*i+"%",t.style.height=100*n+"%",t.ondragstart=function(){return!1},An&&(t.style.willChange="transform"),o.appendChild(t);var s=t.naturalWidth/i,a=t.naturalHeight/n;if(r){var u=a/s;o.style.paddingBottom=100*u+"%"}else o.style.height="100%";return o},t._getSizeString=function(e){return"number"==typeof e?e+"px":e},n.setFrameIndex=function(e){var t=this.toColRow(e);this.setColRow(t[0],t[1])},n.getFrameIndex=function(){return this._colRow[1]*this._colCount+this._colRow[0]},n.setColRow=function(e,t){t>this._rowCount-1||e>this._colCount-1||(this._image&&fn&&(this._image.style[fn]="translate("+-e/this._colCount*100+"%, "+-t/this._rowCount*100+"%)"),this._colRow=[e,t])},n.getColRow=function(){return this._colRow},n.stop=function(){this._autoPlayTimer&&(clearInterval(this._autoPlayTimer),this._autoPlayTimer=-1)},n.play=function(e){var t=this,n=void 0===e?{interval:1e3/this._totalCount,playCount:0}:e,i=n.interval,r=n.playCount;if(this._bg){this._autoPlayTimer&&(clearInterval(this._autoPlayTimer),this._autoPlayTimer=-1);var o=this.getFrameIndex(),s=0,a=0;this._autoPlayTimer=window.setInterval((function(){o%=t._totalCount;var e=t.toColRow(o);t.setColRow(e[0],e[1]),o++,++a===t._totalCount&&(a=0,s++),r>0&&s===r&&clearInterval(t._autoPlayTimer)}),i)}else this._autoPlayReservedInfo={interval:i,playCount:r}},n.toColRow=function(e){var t=this._colCount,n=this._rowCount;return e<0?[0,0]:e>=this._totalCount?[t-1,n-1]:[e%t,Math.floor(e/t)]},t.VERSION=Ht,t}(l),lr=function(e){function t(t,n){void 0===n&&(n={});var i=e.call(this)||this;i._el=t;var r=Zt({},n),o=r.colCount||1,s=r.rowCount||1;return i._scale=r.scale||1,i._panScale=.21*i._scale,i._frameCount=o*s,i._sprites=new ur(t,r).on({load:function(e){i.trigger(new u("load",e))},imageError:function(e){i.trigger(new u("imageError",{imageUrl:e.imageUrl}))}}),i._panInput=new Ge(i._el,{scale:[i._panScale,i._panScale]}),i._axes=new He({angle:{range:[0,359],circular:!0}}).on({change:function(e){var t=Math.floor(e.pos.angle/(360/i._frameCount)),n=i._frameCount-t-1;i._sprites.setFrameIndex(n),i.trigger(new u("change",{frameIndex:n,colRow:i._sprites.getColRow(),angle:e.pos.angle}))},animationEnd:function(e){i.trigger(new u("animationEnd",{isTrusted:e.isTrusted}))}}),i._axes.connect("angle",i._panInput),i}Yt(t,e);var n=t.prototype;return n.setScale=function(e){return isNaN(e)||e<0||(this._scale=e,this._panScale=.21*e,this._panInput.options.scale=[this._panScale,this._panScale]),this},n.getScale=function(){return this._scale},n.spinBy=function(e,t){return void 0===e&&(e=0),void 0===t&&(t={duration:0}),this._axes.setBy({angle:e},t.duration),this},n.spinTo=function(e,t){return void 0===e&&(e=0),void 0===t&&(t={duration:0}),this._axes.setTo({angle:e},t.duration),this},n.getAngle=function(){return this._axes.get().angle||0},t.VERSION=Ht,t}(l),cr=function(e,t,n){[l.prototype,e.prototype].forEach((function(e){Object.getOwnPropertyNames(e).filter((function(e){return!t[e]&&!e.startsWith("_")&&"constructor"!==e})).forEach((function(i){var r=Object.getOwnPropertyDescriptor(e,i);if(r.value)Object.defineProperty(t,i,{value:function(){for(var e,t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];return(e=r.value).call.apply(e,Jt([this[n]],t))}});else{var o={};r.get&&(o.get=function(){var e;return null===(e=r.get)||void 0===e?void 0:e.call(this[n])}),r.set&&(o.set=function(){for(var e,t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];return null===(e=r.set)||void 0===e?void 0:e.call.apply(e,Jt([this[n]],t))}),Object.defineProperty(t,i,o)}}))}))},hr=function(e,t){cr(ir,e,t)},dr=function(e,t){cr(lr,e,t)},pr=function(e,t,n){fr(t.image,n.image)?e.setImage(t.image,{projectionType:t.projectionType,cubemapConfig:t.cubemapConfig,stereoFormat:t.stereoFormat,isVideo:!1}):fr(t.video,n.video)&&e.setVideo(t.video,{projectionType:t.projectionType,cubemapConfig:t.cubemapConfig,stereoFormat:t.stereoFormat}),["fovRange","gyroMode","pitchRange","showPolePoint","touchDirection","useKeyboard","useZoom","yawRange"].forEach((function(i){Ar(e,i,t,n)}))},fr=function(e,t){return null!=e&&e!==t},Ar=function(e,t,n,i){fr(n[t],i[t])&&e["set"+t[0].toUpperCase()+t.slice(1)](n[t])},gr=function(e){return Object.keys(e).reduce((function(t,n){return null!=e[n]&&(t[n]=e[n]),t}),{})},vr=function(e){var t;do{var n=new Uint32Array(1);crypto.getRandomValues(n),t=n[0]}while(t===e);return t}},58:function(e,t){var n;!function(t,n){"use strict";"object"==typeof e.exports?e.exports=t.document?n(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}:n(t)}("undefined"!=typeof window?window:this,(function(i,r){"use strict";var o=[],s=Object.getPrototypeOf,a=o.slice,u=o.flat?function(e){return o.flat.call(e)}:function(e){return o.concat.apply([],e)},l=o.push,c=o.indexOf,h={},d=h.toString,p=h.hasOwnProperty,f=p.toString,A=f.call(Object),g={},v=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},m=function(e){return null!=e&&e===e.window},y=i.document,_={type:!0,src:!0,nonce:!0,noModule:!0};function w(e,t,n){var i,r,o=(n=n||y).createElement("script");if(o.text=e,t)for(i in _)(r=t[i]||t.getAttribute&&t.getAttribute(i))&&o.setAttribute(i,r);n.head.appendChild(o).parentNode.removeChild(o)}function b(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?h[d.call(e)]||"object":typeof e}var x="3.7.0",E=/HTML$/i,T=function(e,t){return new T.fn.init(e,t)};function S(e){var t=!!e&&"length"in e&&e.length,n=b(e);return!v(e)&&!m(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}function R(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}T.fn=T.prototype={jquery:x,constructor:T,length:0,toArray:function(){return a.call(this)},get:function(e){return null==e?a.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=T.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return T.each(this,e)},map:function(e){return this.pushStack(T.map(this,(function(t,n){return e.call(t,n,t)})))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(T.grep(this,(function(e,t){return(t+1)%2})))},odd:function(){return this.pushStack(T.grep(this,(function(e,t){return t%2})))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:l,sort:o.sort,splice:o.splice},T.extend=T.fn.extend=function(){var e,t,n,i,r,o,s=arguments[0]||{},a=1,u=arguments.length,l=!1;for("boolean"==typeof s&&(l=s,s=arguments[a]||{},a++),"object"==typeof s||v(s)||(s={}),a===u&&(s=this,a--);a<u;a++)if(null!=(e=arguments[a]))for(t in e)i=e[t],"__proto__"!==t&&s!==i&&(l&&i&&(T.isPlainObject(i)||(r=Array.isArray(i)))?(n=s[t],o=r&&!Array.isArray(n)?[]:r||T.isPlainObject(n)?n:{},r=!1,s[t]=T.extend(l,o,i)):void 0!==i&&(s[t]=i));return s},T.extend({expando:"jQuery"+(x+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==d.call(e)||(t=s(e))&&("function"!=typeof(n=p.call(t,"constructor")&&t.constructor)||f.call(n)!==A))},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){w(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,i=0;if(S(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},text:function(e){var t,n="",i=0,r=e.nodeType;if(r){if(1===r||9===r||11===r)return e.textContent;if(3===r||4===r)return e.nodeValue}else for(;t=e[i++];)n+=T.text(t);return n},makeArray:function(e,t){var n=t||[];return null!=e&&(S(Object(e))?T.merge(n,"string"==typeof e?[e]:e):l.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:c.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!E.test(t||n&&n.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,i=0,r=e.length;i<n;i++)e[r++]=t[i];return e.length=r,e},grep:function(e,t,n){for(var i=[],r=0,o=e.length,s=!n;r<o;r++)!t(e[r],r)!==s&&i.push(e[r]);return i},map:function(e,t,n){var i,r,o=0,s=[];if(S(e))for(i=e.length;o<i;o++)null!=(r=t(e[o],o,n))&&s.push(r);else for(o in e)null!=(r=t(e[o],o,n))&&s.push(r);return u(s)},guid:1,support:g}),"function"==typeof Symbol&&(T.fn[Symbol.iterator]=o[Symbol.iterator]),T.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(e,t){h["[object "+t+"]"]=t.toLowerCase()}));var M=o.pop,C=o.sort,P=o.splice,F="[\\x20\\t\\r\\n\\f]",D=new RegExp("^[\\x20\\t\\r\\n\\f]+|((?:^|[^\\\\])(?:\\\\.)*)[\\x20\\t\\r\\n\\f]+$","g");T.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var I=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function O(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}T.escapeSelector=function(e){return(e+"").replace(I,O)};var B=y,L=l;!function(){var e,t,n,r,s,u,l,h,d,f,A=L,v=T.expando,m=0,y=0,_=ee(),w=ee(),b=ee(),x=ee(),E=function(e,t){return e===t&&(s=!0),0},S="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",I="(?:\\\\[\\da-fA-F]{1,6}[\\x20\\t\\r\\n\\f]?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",O="\\[[\\x20\\t\\r\\n\\f]*("+I+")(?:"+F+"*([*^$|!~]?=)"+F+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+I+"))|)"+F+"*\\]",V=":("+I+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+O+")*)|.*)\\)|)",k=new RegExp(F+"+","g"),N=new RegExp("^[\\x20\\t\\r\\n\\f]*,[\\x20\\t\\r\\n\\f]*"),Q=new RegExp("^[\\x20\\t\\r\\n\\f]*([>+~]|[\\x20\\t\\r\\n\\f])[\\x20\\t\\r\\n\\f]*"),U=new RegExp(F+"|>"),W=new RegExp(V),G=new RegExp("^"+I+"$"),j={ID:new RegExp("^#("+I+")"),CLASS:new RegExp("^\\.("+I+")"),TAG:new RegExp("^("+I+"|[*])"),ATTR:new RegExp("^"+O),PSEUDO:new RegExp("^"+V),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\([\\x20\\t\\r\\n\\f]*(even|odd|(([+-]|)(\\d*)n|)[\\x20\\t\\r\\n\\f]*(?:([+-]|)[\\x20\\t\\r\\n\\f]*(\\d+)|))[\\x20\\t\\r\\n\\f]*\\)|)","i"),bool:new RegExp("^(?:"+S+")$","i"),needsContext:new RegExp("^[\\x20\\t\\r\\n\\f]*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\([\\x20\\t\\r\\n\\f]*((?:-\\d)?\\d*)[\\x20\\t\\r\\n\\f]*\\)|)(?=[^-]|$)","i")},z=/^(?:input|select|textarea|button)$/i,X=/^h\d$/i,H=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,q=/[+~]/,Y=new RegExp("\\\\[\\da-fA-F]{1,6}[\\x20\\t\\r\\n\\f]?|\\\\([^\\r\\n\\f])","g"),Z=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},K=function(){ue()},$=de((function(e){return!0===e.disabled&&R(e,"fieldset")}),{dir:"parentNode",next:"legend"});try{A.apply(o=a.call(B.childNodes),B.childNodes),o[B.childNodes.length].nodeType}catch(e){A={apply:function(e,t){L.apply(e,a.call(t))},call:function(e){L.apply(e,a.call(arguments,1))}}}function J(e,t,n,i){var r,o,s,a,l,c,p,f=t&&t.ownerDocument,m=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==m&&9!==m&&11!==m)return n;if(!i&&(ue(t),t=t||u,h)){if(11!==m&&(l=H.exec(e)))if(r=l[1]){if(9===m){if(!(s=t.getElementById(r)))return n;if(s.id===r)return A.call(n,s),n}else if(f&&(s=f.getElementById(r))&&J.contains(t,s)&&s.id===r)return A.call(n,s),n}else{if(l[2])return A.apply(n,t.getElementsByTagName(e)),n;if((r=l[3])&&t.getElementsByClassName)return A.apply(n,t.getElementsByClassName(r)),n}if(!(x[e+" "]||d&&d.test(e))){if(p=e,f=t,1===m&&(U.test(e)||Q.test(e))){for((f=q.test(e)&&ae(t.parentNode)||t)==t&&g.scope||((a=t.getAttribute("id"))?a=T.escapeSelector(a):t.setAttribute("id",a=v)),o=(c=ce(e)).length;o--;)c[o]=(a?"#"+a:":scope")+" "+he(c[o]);p=c.join(",")}try{return A.apply(n,f.querySelectorAll(p)),n}catch(t){x(e,!0)}finally{a===v&&t.removeAttribute("id")}}}return me(e.replace(D,"$1"),t,n,i)}function ee(){var e=[];return function n(i,r){return e.push(i+" ")>t.cacheLength&&delete n[e.shift()],n[i+" "]=r}}function te(e){return e[v]=!0,e}function ne(e){var t=u.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function ie(e){return function(t){return R(t,"input")&&t.type===e}}function re(e){return function(t){return(R(t,"input")||R(t,"button"))&&t.type===e}}function oe(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&$(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function se(e){return te((function(t){return t=+t,te((function(n,i){for(var r,o=e([],n.length,t),s=o.length;s--;)n[r=o[s]]&&(n[r]=!(i[r]=n[r]))}))}))}function ae(e){return e&&void 0!==e.getElementsByTagName&&e}function ue(e){var n,i=e?e.ownerDocument||e:B;return i!=u&&9===i.nodeType&&i.documentElement?(l=(u=i).documentElement,h=!T.isXMLDoc(u),f=l.matches||l.webkitMatchesSelector||l.msMatchesSelector,B!=u&&(n=u.defaultView)&&n.top!==n&&n.addEventListener("unload",K),g.getById=ne((function(e){return l.appendChild(e).id=T.expando,!u.getElementsByName||!u.getElementsByName(T.expando).length})),g.disconnectedMatch=ne((function(e){return f.call(e,"*")})),g.scope=ne((function(){return u.querySelectorAll(":scope")})),g.cssHas=ne((function(){try{return u.querySelector(":has(*,:jqfake)"),!1}catch(e){return!0}})),g.getById?(t.filter.ID=function(e){var t=e.replace(Y,Z);return function(e){return e.getAttribute("id")===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&h){var n=t.getElementById(e);return n?[n]:[]}}):(t.filter.ID=function(e){var t=e.replace(Y,Z);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&h){var n,i,r,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(r=t.getElementsByName(e),i=0;o=r[i++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),t.find.TAG=function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},t.find.CLASS=function(e,t){if(void 0!==t.getElementsByClassName&&h)return t.getElementsByClassName(e)},d=[],ne((function(e){var t;l.appendChild(e).innerHTML="<a id='"+v+"' href='' disabled='disabled'></a><select id='"+v+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||d.push("\\[[\\x20\\t\\r\\n\\f]*(?:value|"+S+")"),e.querySelectorAll("[id~="+v+"-]").length||d.push("~="),e.querySelectorAll("a#"+v+"+*").length||d.push(".#.+[+~]"),e.querySelectorAll(":checked").length||d.push(":checked"),(t=u.createElement("input")).setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),l.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&d.push(":enabled",":disabled"),(t=u.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||d.push("\\[[\\x20\\t\\r\\n\\f]*name[\\x20\\t\\r\\n\\f]*=[\\x20\\t\\r\\n\\f]*(?:''|\"\")")})),g.cssHas||d.push(":has"),d=d.length&&new RegExp(d.join("|")),E=function(e,t){if(e===t)return s=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!g.sortDetached&&t.compareDocumentPosition(e)===n?e===u||e.ownerDocument==B&&J.contains(B,e)?-1:t===u||t.ownerDocument==B&&J.contains(B,t)?1:r?c.call(r,e)-c.call(r,t):0:4&n?-1:1)},u):u}for(e in J.matches=function(e,t){return J(e,null,null,t)},J.matchesSelector=function(e,t){if(ue(e),h&&!x[t+" "]&&(!d||!d.test(t)))try{var n=f.call(e,t);if(n||g.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){x(t,!0)}return J(t,u,null,[e]).length>0},J.contains=function(e,t){return(e.ownerDocument||e)!=u&&ue(e),T.contains(e,t)},J.attr=function(e,n){(e.ownerDocument||e)!=u&&ue(e);var i=t.attrHandle[n.toLowerCase()],r=i&&p.call(t.attrHandle,n.toLowerCase())?i(e,n,!h):void 0;return void 0!==r?r:e.getAttribute(n)},J.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},T.uniqueSort=function(e){var t,n=[],i=0,o=0;if(s=!g.sortStable,r=!g.sortStable&&a.call(e,0),C.call(e,E),s){for(;t=e[o++];)t===e[o]&&(i=n.push(o));for(;i--;)P.call(e,n[i],1)}return r=null,e},T.fn.uniqueSort=function(){return this.pushStack(T.uniqueSort(a.apply(this)))},(t=T.expr={cacheLength:50,createPseudo:te,match:j,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(Y,Z),e[3]=(e[3]||e[4]||e[5]||"").replace(Y,Z),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||J.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&J.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return j.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&W.test(n)&&(t=ce(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(Y,Z).toLowerCase();return"*"===e?function(){return!0}:function(e){return R(e,t)}},CLASS:function(e){var t=_[e+" "];return t||(t=new RegExp("(^|[\\x20\\t\\r\\n\\f])"+e+"("+F+"|$)"))&&_(e,(function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(i){var r=J.attr(i,e);return null==r?"!="===t:!t||(r+="","="===t?r===n:"!="===t?r!==n:"^="===t?n&&0===r.indexOf(n):"*="===t?n&&r.indexOf(n)>-1:"$="===t?n&&r.slice(-n.length)===n:"~="===t?(" "+r.replace(k," ")+" ").indexOf(n)>-1:"|="===t&&(r===n||r.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,i,r){var o="nth"!==e.slice(0,3),s="last"!==e.slice(-4),a="of-type"===t;return 1===i&&0===r?function(e){return!!e.parentNode}:function(t,n,u){var l,c,h,d,p,f=o!==s?"nextSibling":"previousSibling",A=t.parentNode,g=a&&t.nodeName.toLowerCase(),y=!u&&!a,_=!1;if(A){if(o){for(;f;){for(h=t;h=h[f];)if(a?R(h,g):1===h.nodeType)return!1;p=f="only"===e&&!p&&"nextSibling"}return!0}if(p=[s?A.firstChild:A.lastChild],s&&y){for(_=(d=(l=(c=A[v]||(A[v]={}))[e]||[])[0]===m&&l[1])&&l[2],h=d&&A.childNodes[d];h=++d&&h&&h[f]||(_=d=0)||p.pop();)if(1===h.nodeType&&++_&&h===t){c[e]=[m,d,_];break}}else if(y&&(_=d=(l=(c=t[v]||(t[v]={}))[e]||[])[0]===m&&l[1]),!1===_)for(;(h=++d&&h&&h[f]||(_=d=0)||p.pop())&&(!(a?R(h,g):1===h.nodeType)||!++_||(y&&((c=h[v]||(h[v]={}))[e]=[m,_]),h!==t)););return(_-=r)===i||_%i==0&&_/i>=0}}},PSEUDO:function(e,n){var i,r=t.pseudos[e]||t.setFilters[e.toLowerCase()]||J.error("unsupported pseudo: "+e);return r[v]?r(n):r.length>1?(i=[e,e,"",n],t.setFilters.hasOwnProperty(e.toLowerCase())?te((function(e,t){for(var i,o=r(e,n),s=o.length;s--;)e[i=c.call(e,o[s])]=!(t[i]=o[s])})):function(e){return r(e,0,i)}):r}},pseudos:{not:te((function(e){var t=[],n=[],i=ve(e.replace(D,"$1"));return i[v]?te((function(e,t,n,r){for(var o,s=i(e,null,r,[]),a=e.length;a--;)(o=s[a])&&(e[a]=!(t[a]=o))})):function(e,r,o){return t[0]=e,i(t,null,o,n),t[0]=null,!n.pop()}})),has:te((function(e){return function(t){return J(e,t).length>0}})),contains:te((function(e){return e=e.replace(Y,Z),function(t){return(t.textContent||T.text(t)).indexOf(e)>-1}})),lang:te((function(e){return G.test(e||"")||J.error("unsupported lang: "+e),e=e.replace(Y,Z).toLowerCase(),function(t){var n;do{if(n=h?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}})),target:function(e){var t=i.location&&i.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===l},focus:function(e){return e===function(){try{return u.activeElement}catch(e){}}()&&u.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:oe(!1),disabled:oe(!0),checked:function(e){return R(e,"input")&&!!e.checked||R(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!t.pseudos.empty(e)},header:function(e){return X.test(e.nodeName)},input:function(e){return z.test(e.nodeName)},button:function(e){return R(e,"input")&&"button"===e.type||R(e,"button")},text:function(e){var t;return R(e,"input")&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:se((function(){return[0]})),last:se((function(e,t){return[t-1]})),eq:se((function(e,t,n){return[n<0?n+t:n]})),even:se((function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e})),odd:se((function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e})),lt:se((function(e,t,n){var i;for(i=n<0?n+t:n>t?t:n;--i>=0;)e.push(i);return e})),gt:se((function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e}))}}).pseudos.nth=t.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[e]=ie(e);for(e in{submit:!0,reset:!0})t.pseudos[e]=re(e);function le(){}function ce(e,n){var i,r,o,s,a,u,l,c=w[e+" "];if(c)return n?0:c.slice(0);for(a=e,u=[],l=t.preFilter;a;){for(s in i&&!(r=N.exec(a))||(r&&(a=a.slice(r[0].length)||a),u.push(o=[])),i=!1,(r=Q.exec(a))&&(i=r.shift(),o.push({value:i,type:r[0].replace(D," ")}),a=a.slice(i.length)),t.filter)!(r=j[s].exec(a))||l[s]&&!(r=l[s](r))||(i=r.shift(),o.push({value:i,type:s,matches:r}),a=a.slice(i.length));if(!i)break}return n?a.length:a?J.error(e):w(e,u).slice(0)}function he(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function de(e,t,n){var i=t.dir,r=t.next,o=r||i,s=n&&"parentNode"===o,a=y++;return t.first?function(t,n,r){for(;t=t[i];)if(1===t.nodeType||s)return e(t,n,r);return!1}:function(t,n,u){var l,c,h=[m,a];if(u){for(;t=t[i];)if((1===t.nodeType||s)&&e(t,n,u))return!0}else for(;t=t[i];)if(1===t.nodeType||s)if(c=t[v]||(t[v]={}),r&&R(t,r))t=t[i]||t;else{if((l=c[o])&&l[0]===m&&l[1]===a)return h[2]=l[2];if(c[o]=h,h[2]=e(t,n,u))return!0}return!1}}function pe(e){return e.length>1?function(t,n,i){for(var r=e.length;r--;)if(!e[r](t,n,i))return!1;return!0}:e[0]}function fe(e,t,n,i,r){for(var o,s=[],a=0,u=e.length,l=null!=t;a<u;a++)(o=e[a])&&(n&&!n(o,i,r)||(s.push(o),l&&t.push(a)));return s}function Ae(e,t,n,i,r,o){return i&&!i[v]&&(i=Ae(i)),r&&!r[v]&&(r=Ae(r,o)),te((function(o,s,a,u){var l,h,d,p,f=[],g=[],v=s.length,m=o||function(e,t,n){for(var i=0,r=t.length;i<r;i++)J(e,t[i],n);return n}(t||"*",a.nodeType?[a]:a,[]),y=!e||!o&&t?m:fe(m,f,e,a,u);if(n?n(y,p=r||(o?e:v||i)?[]:s,a,u):p=y,i)for(l=fe(p,g),i(l,[],a,u),h=l.length;h--;)(d=l[h])&&(p[g[h]]=!(y[g[h]]=d));if(o){if(r||e){if(r){for(l=[],h=p.length;h--;)(d=p[h])&&l.push(y[h]=d);r(null,p=[],l,u)}for(h=p.length;h--;)(d=p[h])&&(l=r?c.call(o,d):f[h])>-1&&(o[l]=!(s[l]=d))}}else p=fe(p===s?p.splice(v,p.length):p),r?r(null,s,p,u):A.apply(s,p)}))}function ge(e){for(var i,r,o,s=e.length,a=t.relative[e[0].type],u=a||t.relative[" "],l=a?1:0,h=de((function(e){return e===i}),u,!0),d=de((function(e){return c.call(i,e)>-1}),u,!0),p=[function(e,t,r){var o=!a&&(r||t!=n)||((i=t).nodeType?h(e,t,r):d(e,t,r));return i=null,o}];l<s;l++)if(r=t.relative[e[l].type])p=[de(pe(p),r)];else{if((r=t.filter[e[l].type].apply(null,e[l].matches))[v]){for(o=++l;o<s&&!t.relative[e[o].type];o++);return Ae(l>1&&pe(p),l>1&&he(e.slice(0,l-1).concat({value:" "===e[l-2].type?"*":""})).replace(D,"$1"),r,l<o&&ge(e.slice(l,o)),o<s&&ge(e=e.slice(o)),o<s&&he(e))}p.push(r)}return pe(p)}function ve(e,i){var r,o=[],s=[],a=b[e+" "];if(!a){for(i||(i=ce(e)),r=i.length;r--;)(a=ge(i[r]))[v]?o.push(a):s.push(a);(a=b(e,function(e,i){var r=i.length>0,o=e.length>0,s=function(s,a,l,c,d){var p,f,g,v=0,y="0",_=s&&[],w=[],b=n,x=s||o&&t.find.TAG("*",d),E=m+=null==b?1:Math.random()||.1,S=x.length;for(d&&(n=a==u||a||d);y!==S&&null!=(p=x[y]);y++){if(o&&p){for(f=0,a||p.ownerDocument==u||(ue(p),l=!h);g=e[f++];)if(g(p,a||u,l)){A.call(c,p);break}d&&(m=E)}r&&((p=!g&&p)&&v--,s&&_.push(p))}if(v+=y,r&&y!==v){for(f=0;g=i[f++];)g(_,w,a,l);if(s){if(v>0)for(;y--;)_[y]||w[y]||(w[y]=M.call(c));w=fe(w)}A.apply(c,w),d&&!s&&w.length>0&&v+i.length>1&&T.uniqueSort(c)}return d&&(m=E,n=b),_};return r?te(s):s}(s,o))).selector=e}return a}function me(e,n,i,r){var o,s,a,u,l,c="function"==typeof e&&e,d=!r&&ce(e=c.selector||e);if(i=i||[],1===d.length){if((s=d[0]=d[0].slice(0)).length>2&&"ID"===(a=s[0]).type&&9===n.nodeType&&h&&t.relative[s[1].type]){if(!(n=(t.find.ID(a.matches[0].replace(Y,Z),n)||[])[0]))return i;c&&(n=n.parentNode),e=e.slice(s.shift().value.length)}for(o=j.needsContext.test(e)?0:s.length;o--&&(a=s[o],!t.relative[u=a.type]);)if((l=t.find[u])&&(r=l(a.matches[0].replace(Y,Z),q.test(s[0].type)&&ae(n.parentNode)||n))){if(s.splice(o,1),!(e=r.length&&he(s)))return A.apply(i,r),i;break}}return(c||ve(e,d))(r,n,!h,i,!n||q.test(e)&&ae(n.parentNode)||n),i}le.prototype=t.filters=t.pseudos,t.setFilters=new le,g.sortStable=v.split("").sort(E).join("")===v,ue(),g.sortDetached=ne((function(e){return 1&e.compareDocumentPosition(u.createElement("fieldset"))})),T.find=J,T.expr[":"]=T.expr.pseudos,T.unique=T.uniqueSort,J.compile=ve,J.select=me,J.setDocument=ue,J.escape=T.escapeSelector,J.getText=T.text,J.isXML=T.isXMLDoc,J.selectors=T.expr,J.support=T.support,J.uniqueSort=T.uniqueSort}();var V=function(e,t,n){for(var i=[],r=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(r&&T(e).is(n))break;i.push(e)}return i},k=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},N=T.expr.match.needsContext,Q=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function U(e,t,n){return v(t)?T.grep(e,(function(e,i){return!!t.call(e,i,e)!==n})):t.nodeType?T.grep(e,(function(e){return e===t!==n})):"string"!=typeof t?T.grep(e,(function(e){return c.call(t,e)>-1!==n})):T.filter(t,e,n)}T.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?T.find.matchesSelector(i,e)?[i]:[]:T.find.matches(e,T.grep(t,(function(e){return 1===e.nodeType})))},T.fn.extend({find:function(e){var t,n,i=this.length,r=this;if("string"!=typeof e)return this.pushStack(T(e).filter((function(){for(t=0;t<i;t++)if(T.contains(r[t],this))return!0})));for(n=this.pushStack([]),t=0;t<i;t++)T.find(e,r[t],n);return i>1?T.uniqueSort(n):n},filter:function(e){return this.pushStack(U(this,e||[],!1))},not:function(e){return this.pushStack(U(this,e||[],!0))},is:function(e){return!!U(this,"string"==typeof e&&N.test(e)?T(e):e||[],!1).length}});var W,G=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(T.fn.init=function(e,t,n){var i,r;if(!e)return this;if(n=n||W,"string"==typeof e){if(!(i="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:G.exec(e))||!i[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(i[1]){if(t=t instanceof T?t[0]:t,T.merge(this,T.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:y,!0)),Q.test(i[1])&&T.isPlainObject(t))for(i in t)v(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}return(r=y.getElementById(i[2]))&&(this[0]=r,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):v(e)?void 0!==n.ready?n.ready(e):e(T):T.makeArray(e,this)}).prototype=T.fn,W=T(y);var j=/^(?:parents|prev(?:Until|All))/,z={children:!0,contents:!0,next:!0,prev:!0};function X(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}T.fn.extend({has:function(e){var t=T(e,this),n=t.length;return this.filter((function(){for(var e=0;e<n;e++)if(T.contains(this,t[e]))return!0}))},closest:function(e,t){var n,i=0,r=this.length,o=[],s="string"!=typeof e&&T(e);if(!N.test(e))for(;i<r;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?s.index(n)>-1:1===n.nodeType&&T.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?T.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?c.call(T(e),this[0]):c.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(T.uniqueSort(T.merge(this.get(),T(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),T.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return V(e,"parentNode")},parentsUntil:function(e,t,n){return V(e,"parentNode",n)},next:function(e){return X(e,"nextSibling")},prev:function(e){return X(e,"previousSibling")},nextAll:function(e){return V(e,"nextSibling")},prevAll:function(e){return V(e,"previousSibling")},nextUntil:function(e,t,n){return V(e,"nextSibling",n)},prevUntil:function(e,t,n){return V(e,"previousSibling",n)},siblings:function(e){return k((e.parentNode||{}).firstChild,e)},children:function(e){return k(e.firstChild)},contents:function(e){return null!=e.contentDocument&&s(e.contentDocument)?e.contentDocument:(R(e,"template")&&(e=e.content||e),T.merge([],e.childNodes))}},(function(e,t){T.fn[e]=function(n,i){var r=T.map(this,t,n);return"Until"!==e.slice(-5)&&(i=n),i&&"string"==typeof i&&(r=T.filter(i,r)),this.length>1&&(z[e]||T.uniqueSort(r),j.test(e)&&r.reverse()),this.pushStack(r)}}));var H=/[^\x20\t\r\n\f]+/g;function q(e){return e}function Y(e){throw e}function Z(e,t,n,i){var r;try{e&&v(r=e.promise)?r.call(e).done(t).fail(n):e&&v(r=e.then)?r.call(e,t,n):t.apply(void 0,[e].slice(i))}catch(e){n.apply(void 0,[e])}}T.Callbacks=function(e){e="string"==typeof e?function(e){var t={};return T.each(e.match(H)||[],(function(e,n){t[n]=!0})),t}(e):T.extend({},e);var t,n,i,r,o=[],s=[],a=-1,u=function(){for(r=r||e.once,i=t=!0;s.length;a=-1)for(n=s.shift();++a<o.length;)!1===o[a].apply(n[0],n[1])&&e.stopOnFalse&&(a=o.length,n=!1);e.memory||(n=!1),t=!1,r&&(o=n?[]:"")},l={add:function(){return o&&(n&&!t&&(a=o.length-1,s.push(n)),function t(n){T.each(n,(function(n,i){v(i)?e.unique&&l.has(i)||o.push(i):i&&i.length&&"string"!==b(i)&&t(i)}))}(arguments),n&&!t&&u()),this},remove:function(){return T.each(arguments,(function(e,t){for(var n;(n=T.inArray(t,o,n))>-1;)o.splice(n,1),n<=a&&a--})),this},has:function(e){return e?T.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return r=s=[],o=n="",this},disabled:function(){return!o},lock:function(){return r=s=[],n||t||(o=n=""),this},locked:function(){return!!r},fireWith:function(e,n){return r||(n=[e,(n=n||[]).slice?n.slice():n],s.push(n),t||u()),this},fire:function(){return l.fireWith(this,arguments),this},fired:function(){return!!i}};return l},T.extend({Deferred:function(e){var t=[["notify","progress",T.Callbacks("memory"),T.Callbacks("memory"),2],["resolve","done",T.Callbacks("once memory"),T.Callbacks("once memory"),0,"resolved"],["reject","fail",T.Callbacks("once memory"),T.Callbacks("once memory"),1,"rejected"]],n="pending",r={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},catch:function(e){return r.then(null,e)},pipe:function(){var e=arguments;return T.Deferred((function(n){T.each(t,(function(t,i){var r=v(e[i[4]])&&e[i[4]];o[i[1]]((function(){var e=r&&r.apply(this,arguments);e&&v(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[i[0]+"With"](this,r?[e]:arguments)}))})),e=null})).promise()},then:function(e,n,r){var o=0;function s(e,t,n,r){return function(){var a=this,u=arguments,l=function(){var i,l;if(!(e<o)){if((i=n.apply(a,u))===t.promise())throw new TypeError("Thenable self-resolution");l=i&&("object"==typeof i||"function"==typeof i)&&i.then,v(l)?r?l.call(i,s(o,t,q,r),s(o,t,Y,r)):(o++,l.call(i,s(o,t,q,r),s(o,t,Y,r),s(o,t,q,t.notifyWith))):(n!==q&&(a=void 0,u=[i]),(r||t.resolveWith)(a,u))}},c=r?l:function(){try{l()}catch(i){T.Deferred.exceptionHook&&T.Deferred.exceptionHook(i,c.error),e+1>=o&&(n!==Y&&(a=void 0,u=[i]),t.rejectWith(a,u))}};e?c():(T.Deferred.getErrorHook?c.error=T.Deferred.getErrorHook():T.Deferred.getStackHook&&(c.error=T.Deferred.getStackHook()),i.setTimeout(c))}}return T.Deferred((function(i){t[0][3].add(s(0,i,v(r)?r:q,i.notifyWith)),t[1][3].add(s(0,i,v(e)?e:q)),t[2][3].add(s(0,i,v(n)?n:Y))})).promise()},promise:function(e){return null!=e?T.extend(e,r):r}},o={};return T.each(t,(function(e,i){var s=i[2],a=i[5];r[i[1]]=s.add,a&&s.add((function(){n=a}),t[3-e][2].disable,t[3-e][3].disable,t[0][2].lock,t[0][3].lock),s.add(i[3].fire),o[i[0]]=function(){return o[i[0]+"With"](this===o?void 0:this,arguments),this},o[i[0]+"With"]=s.fireWith})),r.promise(o),e&&e.call(o,o),o},when:function(e){var t=arguments.length,n=t,i=Array(n),r=a.call(arguments),o=T.Deferred(),s=function(e){return function(n){i[e]=this,r[e]=arguments.length>1?a.call(arguments):n,--t||o.resolveWith(i,r)}};if(t<=1&&(Z(e,o.done(s(n)).resolve,o.reject,!t),"pending"===o.state()||v(r[n]&&r[n].then)))return o.then();for(;n--;)Z(r[n],s(n),o.reject);return o.promise()}});var K=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;T.Deferred.exceptionHook=function(e,t){i.console&&i.console.warn&&e&&K.test(e.name)&&i.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},T.readyException=function(e){i.setTimeout((function(){throw e}))};var $=T.Deferred();function J(){y.removeEventListener("DOMContentLoaded",J),i.removeEventListener("load",J),T.ready()}T.fn.ready=function(e){return $.then(e).catch((function(e){T.readyException(e)})),this},T.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--T.readyWait:T.isReady)||(T.isReady=!0,!0!==e&&--T.readyWait>0||$.resolveWith(y,[T]))}}),T.ready.then=$.then,"complete"===y.readyState||"loading"!==y.readyState&&!y.documentElement.doScroll?i.setTimeout(T.ready):(y.addEventListener("DOMContentLoaded",J),i.addEventListener("load",J));var ee=function(e,t,n,i,r,o,s){var a=0,u=e.length,l=null==n;if("object"===b(n))for(a in r=!0,n)ee(e,t,a,n[a],!0,o,s);else if(void 0!==i&&(r=!0,v(i)||(s=!0),l&&(s?(t.call(e,i),t=null):(l=t,t=function(e,t,n){return l.call(T(e),n)})),t))for(;a<u;a++)t(e[a],n,s?i:i.call(e[a],a,t(e[a],n)));return r?e:l?t.call(e):u?t(e[0],n):o},te=/^-ms-/,ne=/-([a-z])/g;function ie(e,t){return t.toUpperCase()}function re(e){return e.replace(te,"ms-").replace(ne,ie)}var oe=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function se(){this.expando=T.expando+se.uid++}se.uid=1,se.prototype={cache:function(e){var t=e[this.expando];return t||(t={},oe(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,r=this.cache(e);if("string"==typeof t)r[re(t)]=n;else for(i in t)r[re(i)]=t[i];return r},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][re(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i=e[this.expando];if(void 0!==i){if(void 0!==t){n=(t=Array.isArray(t)?t.map(re):(t=re(t))in i?[t]:t.match(H)||[]).length;for(;n--;)delete i[t[n]]}(void 0===t||T.isEmptyObject(i))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!T.isEmptyObject(t)}};var ae=new se,ue=new se,le=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ce=/[A-Z]/g;function he(e,t,n){var i;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(ce,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(i))){try{n=function(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:le.test(e)?JSON.parse(e):e)}(n)}catch(e){}ue.set(e,t,n)}else n=void 0;return n}T.extend({hasData:function(e){return ue.hasData(e)||ae.hasData(e)},data:function(e,t,n){return ue.access(e,t,n)},removeData:function(e,t){ue.remove(e,t)},_data:function(e,t,n){return ae.access(e,t,n)},_removeData:function(e,t){ae.remove(e,t)}}),T.fn.extend({data:function(e,t){var n,i,r,o=this[0],s=o&&o.attributes;if(void 0===e){if(this.length&&(r=ue.get(o),1===o.nodeType&&!ae.get(o,"hasDataAttrs"))){for(n=s.length;n--;)s[n]&&0===(i=s[n].name).indexOf("data-")&&(i=re(i.slice(5)),he(o,i,r[i]));ae.set(o,"hasDataAttrs",!0)}return r}return"object"==typeof e?this.each((function(){ue.set(this,e)})):ee(this,(function(t){var n;if(o&&void 0===t)return void 0!==(n=ue.get(o,e))||void 0!==(n=he(o,e))?n:void 0;this.each((function(){ue.set(this,e,t)}))}),null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each((function(){ue.remove(this,e)}))}}),T.extend({queue:function(e,t,n){var i;if(e)return t=(t||"fx")+"queue",i=ae.get(e,t),n&&(!i||Array.isArray(n)?i=ae.access(e,t,T.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=T.queue(e,t),i=n.length,r=n.shift(),o=T._queueHooks(e,t);"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===t&&n.unshift("inprogress"),delete o.stop,r.call(e,(function(){T.dequeue(e,t)}),o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return ae.get(e,n)||ae.access(e,n,{empty:T.Callbacks("once memory").add((function(){ae.remove(e,[t+"queue",n])}))})}}),T.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?T.queue(this[0],e):void 0===t?this:this.each((function(){var n=T.queue(this,e,t);T._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&T.dequeue(this,e)}))},dequeue:function(e){return this.each((function(){T.dequeue(this,e)}))},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,r=T.Deferred(),o=this,s=this.length,a=function(){--i||r.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(n=ae.get(o[s],e+"queueHooks"))&&n.empty&&(i++,n.empty.add(a));return a(),r.promise(t)}});var de=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,pe=new RegExp("^(?:([+-])=|)("+de+")([a-z%]*)$","i"),fe=["Top","Right","Bottom","Left"],Ae=y.documentElement,ge=function(e){return T.contains(e.ownerDocument,e)},ve={composed:!0};Ae.getRootNode&&(ge=function(e){return T.contains(e.ownerDocument,e)||e.getRootNode(ve)===e.ownerDocument});var me=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&ge(e)&&"none"===T.css(e,"display")};function ye(e,t,n,i){var r,o,s=20,a=i?function(){return i.cur()}:function(){return T.css(e,t,"")},u=a(),l=n&&n[3]||(T.cssNumber[t]?"":"px"),c=e.nodeType&&(T.cssNumber[t]||"px"!==l&&+u)&&pe.exec(T.css(e,t));if(c&&c[3]!==l){for(u/=2,l=l||c[3],c=+u||1;s--;)T.style(e,t,c+l),(1-o)*(1-(o=a()/u||.5))<=0&&(s=0),c/=o;c*=2,T.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,r=n[1]?c+(n[1]+1)*n[2]:+n[2],i&&(i.unit=l,i.start=c,i.end=r)),r}var _e={};function we(e){var t,n=e.ownerDocument,i=e.nodeName,r=_e[i];return r||(t=n.body.appendChild(n.createElement(i)),r=T.css(t,"display"),t.parentNode.removeChild(t),"none"===r&&(r="block"),_e[i]=r,r)}function be(e,t){for(var n,i,r=[],o=0,s=e.length;o<s;o++)(i=e[o]).style&&(n=i.style.display,t?("none"===n&&(r[o]=ae.get(i,"display")||null,r[o]||(i.style.display="")),""===i.style.display&&me(i)&&(r[o]=we(i))):"none"!==n&&(r[o]="none",ae.set(i,"display",n)));for(o=0;o<s;o++)null!=r[o]&&(e[o].style.display=r[o]);return e}T.fn.extend({show:function(){return be(this,!0)},hide:function(){return be(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each((function(){me(this)?T(this).show():T(this).hide()}))}});var xe,Ee,Te=/^(?:checkbox|radio)$/i,Se=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Re=/^$|^module$|\/(?:java|ecma)script/i;xe=y.createDocumentFragment().appendChild(y.createElement("div")),(Ee=y.createElement("input")).setAttribute("type","radio"),Ee.setAttribute("checked","checked"),Ee.setAttribute("name","t"),xe.appendChild(Ee),g.checkClone=xe.cloneNode(!0).cloneNode(!0).lastChild.checked,xe.innerHTML="<textarea>x</textarea>",g.noCloneChecked=!!xe.cloneNode(!0).lastChild.defaultValue,xe.innerHTML="<option></option>",g.option=!!xe.lastChild;var Me={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Ce(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&R(e,t)?T.merge([e],n):n}function Pe(e,t){for(var n=0,i=e.length;n<i;n++)ae.set(e[n],"globalEval",!t||ae.get(t[n],"globalEval"))}Me.tbody=Me.tfoot=Me.colgroup=Me.caption=Me.thead,Me.th=Me.td,g.option||(Me.optgroup=Me.option=[1,"<select multiple='multiple'>","</select>"]);var Fe=/<|&#?\w+;/;function De(e,t,n,i,r){for(var o,s,a,u,l,c,h=t.createDocumentFragment(),d=[],p=0,f=e.length;p<f;p++)if((o=e[p])||0===o)if("object"===b(o))T.merge(d,o.nodeType?[o]:o);else if(Fe.test(o)){for(s=s||h.appendChild(t.createElement("div")),a=(Se.exec(o)||["",""])[1].toLowerCase(),u=Me[a]||Me._default,s.innerHTML=u[1]+T.htmlPrefilter(o)+u[2],c=u[0];c--;)s=s.lastChild;T.merge(d,s.childNodes),(s=h.firstChild).textContent=""}else d.push(t.createTextNode(o));for(h.textContent="",p=0;o=d[p++];)if(i&&T.inArray(o,i)>-1)r&&r.push(o);else if(l=ge(o),s=Ce(h.appendChild(o),"script"),l&&Pe(s),n)for(c=0;o=s[c++];)Re.test(o.type||"")&&n.push(o);return h}var Ie=/^([^.]*)(?:\.(.+)|)/;function Oe(){return!0}function Be(){return!1}function Le(e,t,n,i,r,o){var s,a;if("object"==typeof t){for(a in"string"!=typeof n&&(i=i||n,n=void 0),t)Le(e,a,n,i,t[a],o);return e}if(null==i&&null==r?(r=n,i=n=void 0):null==r&&("string"==typeof n?(r=i,i=void 0):(r=i,i=n,n=void 0)),!1===r)r=Be;else if(!r)return e;return 1===o&&(s=r,(r=function(e){return T().off(e),s.apply(this,arguments)}).guid=s.guid||(s.guid=T.guid++)),e.each((function(){T.event.add(this,t,r,i,n)}))}function Ve(e,t,n){n?(ae.set(e,t,!1),T.event.add(e,t,{namespace:!1,handler:function(e){var n,i=ae.get(this,t);if(1&e.isTrigger&&this[t]){if(i)(T.event.special[t]||{}).delegateType&&e.stopPropagation();else if(i=a.call(arguments),ae.set(this,t,i),this[t](),n=ae.get(this,t),ae.set(this,t,!1),i!==n)return e.stopImmediatePropagation(),e.preventDefault(),n}else i&&(ae.set(this,t,T.event.trigger(i[0],i.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=Oe)}})):void 0===ae.get(e,t)&&T.event.add(e,t,Oe)}T.event={global:{},add:function(e,t,n,i,r){var o,s,a,u,l,c,h,d,p,f,A,g=ae.get(e);if(oe(e))for(n.handler&&(n=(o=n).handler,r=o.selector),r&&T.find.matchesSelector(Ae,r),n.guid||(n.guid=T.guid++),(u=g.events)||(u=g.events=Object.create(null)),(s=g.handle)||(s=g.handle=function(t){return void 0!==T&&T.event.triggered!==t.type?T.event.dispatch.apply(e,arguments):void 0}),l=(t=(t||"").match(H)||[""]).length;l--;)p=A=(a=Ie.exec(t[l])||[])[1],f=(a[2]||"").split(".").sort(),p&&(h=T.event.special[p]||{},p=(r?h.delegateType:h.bindType)||p,h=T.event.special[p]||{},c=T.extend({type:p,origType:A,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&T.expr.match.needsContext.test(r),namespace:f.join(".")},o),(d=u[p])||((d=u[p]=[]).delegateCount=0,h.setup&&!1!==h.setup.call(e,i,f,s)||e.addEventListener&&e.addEventListener(p,s)),h.add&&(h.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),r?d.splice(d.delegateCount++,0,c):d.push(c),T.event.global[p]=!0)},remove:function(e,t,n,i,r){var o,s,a,u,l,c,h,d,p,f,A,g=ae.hasData(e)&&ae.get(e);if(g&&(u=g.events)){for(l=(t=(t||"").match(H)||[""]).length;l--;)if(p=A=(a=Ie.exec(t[l])||[])[1],f=(a[2]||"").split(".").sort(),p){for(h=T.event.special[p]||{},d=u[p=(i?h.delegateType:h.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=d.length;o--;)c=d[o],!r&&A!==c.origType||n&&n.guid!==c.guid||a&&!a.test(c.namespace)||i&&i!==c.selector&&("**"!==i||!c.selector)||(d.splice(o,1),c.selector&&d.delegateCount--,h.remove&&h.remove.call(e,c));s&&!d.length&&(h.teardown&&!1!==h.teardown.call(e,f,g.handle)||T.removeEvent(e,p,g.handle),delete u[p])}else for(p in u)T.event.remove(e,p+t[l],n,i,!0);T.isEmptyObject(u)&&ae.remove(e,"handle events")}},dispatch:function(e){var t,n,i,r,o,s,a=new Array(arguments.length),u=T.event.fix(e),l=(ae.get(this,"events")||Object.create(null))[u.type]||[],c=T.event.special[u.type]||{};for(a[0]=u,t=1;t<arguments.length;t++)a[t]=arguments[t];if(u.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,u)){for(s=T.event.handlers.call(this,u,l),t=0;(r=s[t++])&&!u.isPropagationStopped();)for(u.currentTarget=r.elem,n=0;(o=r.handlers[n++])&&!u.isImmediatePropagationStopped();)u.rnamespace&&!1!==o.namespace&&!u.rnamespace.test(o.namespace)||(u.handleObj=o,u.data=o.data,void 0!==(i=((T.event.special[o.origType]||{}).handle||o.handler).apply(r.elem,a))&&!1===(u.result=i)&&(u.preventDefault(),u.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,u),u.result}},handlers:function(e,t){var n,i,r,o,s,a=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&e.button>=1))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(o=[],s={},n=0;n<u;n++)void 0===s[r=(i=t[n]).selector+" "]&&(s[r]=i.needsContext?T(r,this).index(l)>-1:T.find(r,this,null,[l]).length),s[r]&&o.push(i);o.length&&a.push({elem:l,handlers:o})}return l=this,u<t.length&&a.push({elem:l,handlers:t.slice(u)}),a},addProp:function(e,t){Object.defineProperty(T.Event.prototype,e,{enumerable:!0,configurable:!0,get:v(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[T.expando]?e:new T.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return Te.test(t.type)&&t.click&&R(t,"input")&&Ve(t,"click",!0),!1},trigger:function(e){var t=this||e;return Te.test(t.type)&&t.click&&R(t,"input")&&Ve(t,"click"),!0},_default:function(e){var t=e.target;return Te.test(t.type)&&t.click&&R(t,"input")&&ae.get(t,"click")||R(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},T.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},T.Event=function(e,t){if(!(this instanceof T.Event))return new T.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Oe:Be,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&T.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[T.expando]=!0},T.Event.prototype={constructor:T.Event,isDefaultPrevented:Be,isPropagationStopped:Be,isImmediatePropagationStopped:Be,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Oe,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Oe,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Oe,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},T.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},T.event.addProp),T.each({focus:"focusin",blur:"focusout"},(function(e,t){function n(e){if(y.documentMode){var n=ae.get(this,"handle"),i=T.event.fix(e);i.type="focusin"===e.type?"focus":"blur",i.isSimulated=!0,n(e),i.target===i.currentTarget&&n(i)}else T.event.simulate(t,e.target,T.event.fix(e))}T.event.special[e]={setup:function(){var i;if(Ve(this,e,!0),!y.documentMode)return!1;(i=ae.get(this,t))||this.addEventListener(t,n),ae.set(this,t,(i||0)+1)},trigger:function(){return Ve(this,e),!0},teardown:function(){var e;if(!y.documentMode)return!1;(e=ae.get(this,t)-1)?ae.set(this,t,e):(this.removeEventListener(t,n),ae.remove(this,t))},_default:function(t){return ae.get(t.target,e)},delegateType:t},T.event.special[t]={setup:function(){var i=this.ownerDocument||this.document||this,r=y.documentMode?this:i,o=ae.get(r,t);o||(y.documentMode?this.addEventListener(t,n):i.addEventListener(e,n,!0)),ae.set(r,t,(o||0)+1)},teardown:function(){var i=this.ownerDocument||this.document||this,r=y.documentMode?this:i,o=ae.get(r,t)-1;o?ae.set(r,t,o):(y.documentMode?this.removeEventListener(t,n):i.removeEventListener(e,n,!0),ae.remove(r,t))}}})),T.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(e,t){T.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,i=this,r=e.relatedTarget,o=e.handleObj;return r&&(r===i||T.contains(i,r))||(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}})),T.fn.extend({on:function(e,t,n,i){return Le(this,e,t,n,i)},one:function(e,t,n,i){return Le(this,e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,T(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(r in e)this.off(r,t,e[r]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Be),this.each((function(){T.event.remove(this,e,n,t)}))}});var ke=/<script|<style|<link/i,Ne=/checked\s*(?:[^=]|=\s*.checked.)/i,Qe=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Ue(e,t){return R(e,"table")&&R(11!==t.nodeType?t:t.firstChild,"tr")&&T(e).children("tbody")[0]||e}function We(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Ge(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function je(e,t){var n,i,r,o,s,a;if(1===t.nodeType){if(ae.hasData(e)&&(a=ae.get(e).events))for(r in ae.remove(t,"handle events"),a)for(n=0,i=a[r].length;n<i;n++)T.event.add(t,r,a[r][n]);ue.hasData(e)&&(o=ue.access(e),s=T.extend({},o),ue.set(t,s))}}function ze(e,t){var n=t.nodeName.toLowerCase();"input"===n&&Te.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function Xe(e,t,n,i){t=u(t);var r,o,s,a,l,c,h=0,d=e.length,p=d-1,f=t[0],A=v(f);if(A||d>1&&"string"==typeof f&&!g.checkClone&&Ne.test(f))return e.each((function(r){var o=e.eq(r);A&&(t[0]=f.call(this,r,o.html())),Xe(o,t,n,i)}));if(d&&(o=(r=De(t,e[0].ownerDocument,!1,e,i)).firstChild,1===r.childNodes.length&&(r=o),o||i)){for(a=(s=T.map(Ce(r,"script"),We)).length;h<d;h++)l=r,h!==p&&(l=T.clone(l,!0,!0),a&&T.merge(s,Ce(l,"script"))),n.call(e[h],l,h);if(a)for(c=s[s.length-1].ownerDocument,T.map(s,Ge),h=0;h<a;h++)l=s[h],Re.test(l.type||"")&&!ae.access(l,"globalEval")&&T.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?T._evalUrl&&!l.noModule&&T._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},c):w(l.textContent.replace(Qe,""),l,c))}return e}function He(e,t,n){for(var i,r=t?T.filter(t,e):e,o=0;null!=(i=r[o]);o++)n||1!==i.nodeType||T.cleanData(Ce(i)),i.parentNode&&(n&&ge(i)&&Pe(Ce(i,"script")),i.parentNode.removeChild(i));return e}T.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var i,r,o,s,a=e.cloneNode(!0),u=ge(e);if(!(g.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||T.isXMLDoc(e)))for(s=Ce(a),i=0,r=(o=Ce(e)).length;i<r;i++)ze(o[i],s[i]);if(t)if(n)for(o=o||Ce(e),s=s||Ce(a),i=0,r=o.length;i<r;i++)je(o[i],s[i]);else je(e,a);return(s=Ce(a,"script")).length>0&&Pe(s,!u&&Ce(e,"script")),a},cleanData:function(e){for(var t,n,i,r=T.event.special,o=0;void 0!==(n=e[o]);o++)if(oe(n)){if(t=n[ae.expando]){if(t.events)for(i in t.events)r[i]?T.event.remove(n,i):T.removeEvent(n,i,t.handle);n[ae.expando]=void 0}n[ue.expando]&&(n[ue.expando]=void 0)}}}),T.fn.extend({detach:function(e){return He(this,e,!0)},remove:function(e){return He(this,e)},text:function(e){return ee(this,(function(e){return void 0===e?T.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)}))}),null,e,arguments.length)},append:function(){return Xe(this,arguments,(function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Ue(this,e).appendChild(e)}))},prepend:function(){return Xe(this,arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Ue(this,e);t.insertBefore(e,t.firstChild)}}))},before:function(){return Xe(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this)}))},after:function(){return Xe(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)}))},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(T.cleanData(Ce(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map((function(){return T.clone(this,e,t)}))},html:function(e){return ee(this,(function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!ke.test(e)&&!Me[(Se.exec(e)||["",""])[1].toLowerCase()]){e=T.htmlPrefilter(e);try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&(T.cleanData(Ce(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)}),null,e,arguments.length)},replaceWith:function(){var e=[];return Xe(this,arguments,(function(t){var n=this.parentNode;T.inArray(this,e)<0&&(T.cleanData(Ce(this)),n&&n.replaceChild(t,this))}),e)}}),T.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(e,t){T.fn[e]=function(e){for(var n,i=[],r=T(e),o=r.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),T(r[s])[t](n),l.apply(i,n.get());return this.pushStack(i)}}));var qe=new RegExp("^("+de+")(?!px)[a-z%]+$","i"),Ye=/^--/,Ze=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=i),t.getComputedStyle(e)},Ke=function(e,t,n){var i,r,o={};for(r in t)o[r]=e.style[r],e.style[r]=t[r];for(r in i=n.call(e),t)e.style[r]=o[r];return i},$e=new RegExp(fe.join("|"),"i");function Je(e,t,n){var i,r,o,s,a=Ye.test(t),u=e.style;return(n=n||Ze(e))&&(s=n.getPropertyValue(t)||n[t],a&&s&&(s=s.replace(D,"$1")||void 0),""!==s||ge(e)||(s=T.style(e,t)),!g.pixelBoxStyles()&&qe.test(s)&&$e.test(t)&&(i=u.width,r=u.minWidth,o=u.maxWidth,u.minWidth=u.maxWidth=u.width=s,s=n.width,u.width=i,u.minWidth=r,u.maxWidth=o)),void 0!==s?s+"":s}function et(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(c){l.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",c.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",Ae.appendChild(l).appendChild(c);var e=i.getComputedStyle(c);n="1%"!==e.top,u=12===t(e.marginLeft),c.style.right="60%",s=36===t(e.right),r=36===t(e.width),c.style.position="absolute",o=12===t(c.offsetWidth/3),Ae.removeChild(l),c=null}}function t(e){return Math.round(parseFloat(e))}var n,r,o,s,a,u,l=y.createElement("div"),c=y.createElement("div");c.style&&(c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",g.clearCloneStyle="content-box"===c.style.backgroundClip,T.extend(g,{boxSizingReliable:function(){return e(),r},pixelBoxStyles:function(){return e(),s},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),u},scrollboxSize:function(){return e(),o},reliableTrDimensions:function(){var e,t,n,r;return null==a&&(e=y.createElement("table"),t=y.createElement("tr"),n=y.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",Ae.appendChild(e).appendChild(t).appendChild(n),r=i.getComputedStyle(t),a=parseInt(r.height,10)+parseInt(r.borderTopWidth,10)+parseInt(r.borderBottomWidth,10)===t.offsetHeight,Ae.removeChild(e)),a}}))}();var tt=["Webkit","Moz","ms"],nt=y.createElement("div").style,it={};function rt(e){return T.cssProps[e]||it[e]||(e in nt?e:it[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=tt.length;n--;)if((e=tt[n]+t)in nt)return e}(e)||e)}var ot=/^(none|table(?!-c[ea]).+)/,st={position:"absolute",visibility:"hidden",display:"block"},at={letterSpacing:"0",fontWeight:"400"};function ut(e,t,n){var i=pe.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function lt(e,t,n,i,r,o){var s="width"===t?1:0,a=0,u=0,l=0;if(n===(i?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(l+=T.css(e,n+fe[s],!0,r)),i?("content"===n&&(u-=T.css(e,"padding"+fe[s],!0,r)),"margin"!==n&&(u-=T.css(e,"border"+fe[s]+"Width",!0,r))):(u+=T.css(e,"padding"+fe[s],!0,r),"padding"!==n?u+=T.css(e,"border"+fe[s]+"Width",!0,r):a+=T.css(e,"border"+fe[s]+"Width",!0,r));return!i&&o>=0&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-a-.5))||0),u+l}function ct(e,t,n){var i=Ze(e),r=(!g.boxSizingReliable()||n)&&"border-box"===T.css(e,"boxSizing",!1,i),o=r,s=Je(e,t,i),a="offset"+t[0].toUpperCase()+t.slice(1);if(qe.test(s)){if(!n)return s;s="auto"}return(!g.boxSizingReliable()&&r||!g.reliableTrDimensions()&&R(e,"tr")||"auto"===s||!parseFloat(s)&&"inline"===T.css(e,"display",!1,i))&&e.getClientRects().length&&(r="border-box"===T.css(e,"boxSizing",!1,i),(o=a in e)&&(s=e[a])),(s=parseFloat(s)||0)+lt(e,t,n||(r?"border":"content"),o,i,s)+"px"}function ht(e,t,n,i,r){return new ht.prototype.init(e,t,n,i,r)}T.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Je(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var r,o,s,a=re(t),u=Ye.test(t),l=e.style;if(u||(t=rt(a)),s=T.cssHooks[t]||T.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(r=s.get(e,!1,i))?r:l[t];"string"==(o=typeof n)&&(r=pe.exec(n))&&r[1]&&(n=ye(e,t,r),o="number"),null!=n&&n==n&&("number"!==o||u||(n+=r&&r[3]||(T.cssNumber[a]?"":"px")),g.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,i))||(u?l.setProperty(t,n):l[t]=n))}},css:function(e,t,n,i){var r,o,s,a=re(t);return Ye.test(t)||(t=rt(a)),(s=T.cssHooks[t]||T.cssHooks[a])&&"get"in s&&(r=s.get(e,!0,n)),void 0===r&&(r=Je(e,t,i)),"normal"===r&&t in at&&(r=at[t]),""===n||n?(o=parseFloat(r),!0===n||isFinite(o)?o||0:r):r}}),T.each(["height","width"],(function(e,t){T.cssHooks[t]={get:function(e,n,i){if(n)return!ot.test(T.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?ct(e,t,i):Ke(e,st,(function(){return ct(e,t,i)}))},set:function(e,n,i){var r,o=Ze(e),s=!g.scrollboxSize()&&"absolute"===o.position,a=(s||i)&&"border-box"===T.css(e,"boxSizing",!1,o),u=i?lt(e,t,i,a,o):0;return a&&s&&(u-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-lt(e,t,"border",!1,o)-.5)),u&&(r=pe.exec(n))&&"px"!==(r[3]||"px")&&(e.style[t]=n,n=T.css(e,t)),ut(0,n,u)}}})),T.cssHooks.marginLeft=et(g.reliableMarginLeft,(function(e,t){if(t)return(parseFloat(Je(e,"marginLeft"))||e.getBoundingClientRect().left-Ke(e,{marginLeft:0},(function(){return e.getBoundingClientRect().left})))+"px"})),T.each({margin:"",padding:"",border:"Width"},(function(e,t){T.cssHooks[e+t]={expand:function(n){for(var i=0,r={},o="string"==typeof n?n.split(" "):[n];i<4;i++)r[e+fe[i]+t]=o[i]||o[i-2]||o[0];return r}},"margin"!==e&&(T.cssHooks[e+t].set=ut)})),T.fn.extend({css:function(e,t){return ee(this,(function(e,t,n){var i,r,o={},s=0;if(Array.isArray(t)){for(i=Ze(e),r=t.length;s<r;s++)o[t[s]]=T.css(e,t[s],!1,i);return o}return void 0!==n?T.style(e,t,n):T.css(e,t)}),e,t,arguments.length>1)}}),T.Tween=ht,ht.prototype={constructor:ht,init:function(e,t,n,i,r,o){this.elem=e,this.prop=n,this.easing=r||T.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(T.cssNumber[n]?"":"px")},cur:function(){var e=ht.propHooks[this.prop];return e&&e.get?e.get(this):ht.propHooks._default.get(this)},run:function(e){var t,n=ht.propHooks[this.prop];return this.options.duration?this.pos=t=T.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):ht.propHooks._default.set(this),this}},ht.prototype.init.prototype=ht.prototype,ht.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=T.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){T.fx.step[e.prop]?T.fx.step[e.prop](e):1!==e.elem.nodeType||!T.cssHooks[e.prop]&&null==e.elem.style[rt(e.prop)]?e.elem[e.prop]=e.now:T.style(e.elem,e.prop,e.now+e.unit)}}},ht.propHooks.scrollTop=ht.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},T.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},T.fx=ht.prototype.init,T.fx.step={};var dt,pt,ft=/^(?:toggle|show|hide)$/,At=/queueHooks$/;function gt(){pt&&(!1===y.hidden&&i.requestAnimationFrame?i.requestAnimationFrame(gt):i.setTimeout(gt,T.fx.interval),T.fx.tick())}function vt(){return i.setTimeout((function(){dt=void 0})),dt=Date.now()}function mt(e,t){var n,i=0,r={height:e};for(t=t?1:0;i<4;i+=2-t)r["margin"+(n=fe[i])]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function yt(e,t,n){for(var i,r=(_t.tweeners[t]||[]).concat(_t.tweeners["*"]),o=0,s=r.length;o<s;o++)if(i=r[o].call(n,t,e))return i}function _t(e,t,n){var i,r,o=0,s=_t.prefilters.length,a=T.Deferred().always((function(){delete u.elem})),u=function(){if(r)return!1;for(var t=dt||vt(),n=Math.max(0,l.startTime+l.duration-t),i=1-(n/l.duration||0),o=0,s=l.tweens.length;o<s;o++)l.tweens[o].run(i);return a.notifyWith(e,[l,i,n]),i<1&&s?n:(s||a.notifyWith(e,[l,1,0]),a.resolveWith(e,[l]),!1)},l=a.promise({elem:e,props:T.extend({},t),opts:T.extend(!0,{specialEasing:{},easing:T.easing._default},n),originalProperties:t,originalOptions:n,startTime:dt||vt(),duration:n.duration,tweens:[],createTween:function(t,n){var i=T.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(i),i},stop:function(t){var n=0,i=t?l.tweens.length:0;if(r)return this;for(r=!0;n<i;n++)l.tweens[n].run(1);return t?(a.notifyWith(e,[l,1,0]),a.resolveWith(e,[l,t])):a.rejectWith(e,[l,t]),this}}),c=l.props;for(function(e,t){var n,i,r,o,s;for(n in e)if(r=t[i=re(n)],o=e[n],Array.isArray(o)&&(r=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),(s=T.cssHooks[i])&&"expand"in s)for(n in o=s.expand(o),delete e[i],o)n in e||(e[n]=o[n],t[n]=r);else t[i]=r}(c,l.opts.specialEasing);o<s;o++)if(i=_t.prefilters[o].call(l,e,c,l.opts))return v(i.stop)&&(T._queueHooks(l.elem,l.opts.queue).stop=i.stop.bind(i)),i;return T.map(c,yt,l),v(l.opts.start)&&l.opts.start.call(e,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),T.fx.timer(T.extend(u,{elem:e,anim:l,queue:l.opts.queue})),l}T.Animation=T.extend(_t,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ye(n.elem,e,pe.exec(t),n),n}]},tweener:function(e,t){v(e)?(t=e,e=["*"]):e=e.match(H);for(var n,i=0,r=e.length;i<r;i++)n=e[i],_t.tweeners[n]=_t.tweeners[n]||[],_t.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var i,r,o,s,a,u,l,c,h="width"in t||"height"in t,d=this,p={},f=e.style,A=e.nodeType&&me(e),g=ae.get(e,"fxshow");for(i in n.queue||(null==(s=T._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,d.always((function(){d.always((function(){s.unqueued--,T.queue(e,"fx").length||s.empty.fire()}))}))),t)if(r=t[i],ft.test(r)){if(delete t[i],o=o||"toggle"===r,r===(A?"hide":"show")){if("show"!==r||!g||void 0===g[i])continue;A=!0}p[i]=g&&g[i]||T.style(e,i)}if((u=!T.isEmptyObject(t))||!T.isEmptyObject(p))for(i in h&&1===e.nodeType&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],null==(l=g&&g.display)&&(l=ae.get(e,"display")),"none"===(c=T.css(e,"display"))&&(l?c=l:(be([e],!0),l=e.style.display||l,c=T.css(e,"display"),be([e]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===T.css(e,"float")&&(u||(d.done((function(){f.display=l})),null==l&&(c=f.display,l="none"===c?"":c)),f.display="inline-block")),n.overflow&&(f.overflow="hidden",d.always((function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]}))),u=!1,p)u||(g?"hidden"in g&&(A=g.hidden):g=ae.access(e,"fxshow",{display:l}),o&&(g.hidden=!A),A&&be([e],!0),d.done((function(){for(i in A||be([e]),ae.remove(e,"fxshow"),p)T.style(e,i,p[i])}))),u=yt(A?g[i]:0,i,d),i in g||(g[i]=u.start,A&&(u.end=u.start,u.start=0))}],prefilter:function(e,t){t?_t.prefilters.unshift(e):_t.prefilters.push(e)}}),T.speed=function(e,t,n){var i=e&&"object"==typeof e?T.extend({},e):{complete:n||!n&&t||v(e)&&e,duration:e,easing:n&&t||t&&!v(t)&&t};return T.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in T.fx.speeds?i.duration=T.fx.speeds[i.duration]:i.duration=T.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){v(i.old)&&i.old.call(this),i.queue&&T.dequeue(this,i.queue)},i},T.fn.extend({fadeTo:function(e,t,n,i){return this.filter(me).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var r=T.isEmptyObject(e),o=T.speed(t,n,i),s=function(){var t=_t(this,T.extend({},e),o);(r||ae.get(this,"finish"))&&t.stop(!0)};return s.finish=s,r||!1===o.queue?this.each(s):this.queue(o.queue,s)},stop:function(e,t,n){var i=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each((function(){var t=!0,r=null!=e&&e+"queueHooks",o=T.timers,s=ae.get(this);if(r)s[r]&&s[r].stop&&i(s[r]);else for(r in s)s[r]&&s[r].stop&&At.test(r)&&i(s[r]);for(r=o.length;r--;)o[r].elem!==this||null!=e&&o[r].queue!==e||(o[r].anim.stop(n),t=!1,o.splice(r,1));!t&&n||T.dequeue(this,e)}))},finish:function(e){return!1!==e&&(e=e||"fx"),this.each((function(){var t,n=ae.get(this),i=n[e+"queue"],r=n[e+"queueHooks"],o=T.timers,s=i?i.length:0;for(n.finish=!0,T.queue(this,e,[]),r&&r.stop&&r.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<s;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish}))}}),T.each(["toggle","show","hide"],(function(e,t){var n=T.fn[t];T.fn[t]=function(e,i,r){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(mt(t,!0),e,i,r)}})),T.each({slideDown:mt("show"),slideUp:mt("hide"),slideToggle:mt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(e,t){T.fn[e]=function(e,n,i){return this.animate(t,e,n,i)}})),T.timers=[],T.fx.tick=function(){var e,t=0,n=T.timers;for(dt=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||T.fx.stop(),dt=void 0},T.fx.timer=function(e){T.timers.push(e),T.fx.start()},T.fx.interval=13,T.fx.start=function(){pt||(pt=!0,gt())},T.fx.stop=function(){pt=null},T.fx.speeds={slow:600,fast:200,_default:400},T.fn.delay=function(e,t){return e=T.fx&&T.fx.speeds[e]||e,t=t||"fx",this.queue(t,(function(t,n){var r=i.setTimeout(t,e);n.stop=function(){i.clearTimeout(r)}}))},function(){var e=y.createElement("input"),t=y.createElement("select").appendChild(y.createElement("option"));e.type="checkbox",g.checkOn=""!==e.value,g.optSelected=t.selected,(e=y.createElement("input")).value="t",e.type="radio",g.radioValue="t"===e.value}();var wt,bt=T.expr.attrHandle;T.fn.extend({attr:function(e,t){return ee(this,T.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each((function(){T.removeAttr(this,e)}))}}),T.extend({attr:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?T.prop(e,t,n):(1===o&&T.isXMLDoc(e)||(r=T.attrHooks[t.toLowerCase()]||(T.expr.match.bool.test(t)?wt:void 0)),void 0!==n?null===n?void T.removeAttr(e,t):r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:(e.setAttribute(t,n+""),n):r&&"get"in r&&null!==(i=r.get(e,t))?i:null==(i=T.find.attr(e,t))?void 0:i)},attrHooks:{type:{set:function(e,t){if(!g.radioValue&&"radio"===t&&R(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i=0,r=t&&t.match(H);if(r&&1===e.nodeType)for(;n=r[i++];)e.removeAttribute(n)}}),wt={set:function(e,t,n){return!1===t?T.removeAttr(e,n):e.setAttribute(n,n),n}},T.each(T.expr.match.bool.source.match(/\w+/g),(function(e,t){var n=bt[t]||T.find.attr;bt[t]=function(e,t,i){var r,o,s=t.toLowerCase();return i||(o=bt[s],bt[s]=r,r=null!=n(e,t,i)?s:null,bt[s]=o),r}}));var xt=/^(?:input|select|textarea|button)$/i,Et=/^(?:a|area)$/i;function Tt(e){return(e.match(H)||[]).join(" ")}function St(e){return e.getAttribute&&e.getAttribute("class")||""}function Rt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(H)||[]}T.fn.extend({prop:function(e,t){return ee(this,T.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each((function(){delete this[T.propFix[e]||e]}))}}),T.extend({prop:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&T.isXMLDoc(e)||(t=T.propFix[t]||t,r=T.propHooks[t]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:e[t]=n:r&&"get"in r&&null!==(i=r.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=T.find.attr(e,"tabindex");return t?parseInt(t,10):xt.test(e.nodeName)||Et.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),g.optSelected||(T.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),T.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){T.propFix[this.toLowerCase()]=this})),T.fn.extend({addClass:function(e){var t,n,i,r,o,s;return v(e)?this.each((function(t){T(this).addClass(e.call(this,t,St(this)))})):(t=Rt(e)).length?this.each((function(){if(i=St(this),n=1===this.nodeType&&" "+Tt(i)+" "){for(o=0;o<t.length;o++)r=t[o],n.indexOf(" "+r+" ")<0&&(n+=r+" ");s=Tt(n),i!==s&&this.setAttribute("class",s)}})):this},removeClass:function(e){var t,n,i,r,o,s;return v(e)?this.each((function(t){T(this).removeClass(e.call(this,t,St(this)))})):arguments.length?(t=Rt(e)).length?this.each((function(){if(i=St(this),n=1===this.nodeType&&" "+Tt(i)+" "){for(o=0;o<t.length;o++)for(r=t[o];n.indexOf(" "+r+" ")>-1;)n=n.replace(" "+r+" "," ");s=Tt(n),i!==s&&this.setAttribute("class",s)}})):this:this.attr("class","")},toggleClass:function(e,t){var n,i,r,o,s=typeof e,a="string"===s||Array.isArray(e);return v(e)?this.each((function(n){T(this).toggleClass(e.call(this,n,St(this),t),t)})):"boolean"==typeof t&&a?t?this.addClass(e):this.removeClass(e):(n=Rt(e),this.each((function(){if(a)for(o=T(this),r=0;r<n.length;r++)i=n[r],o.hasClass(i)?o.removeClass(i):o.addClass(i);else void 0!==e&&"boolean"!==s||((i=St(this))&&ae.set(this,"__className__",i),this.setAttribute&&this.setAttribute("class",i||!1===e?"":ae.get(this,"__className__")||""))})))},hasClass:function(e){var t,n,i=0;for(t=" "+e+" ";n=this[i++];)if(1===n.nodeType&&(" "+Tt(St(n))+" ").indexOf(t)>-1)return!0;return!1}});var Mt=/\r/g;T.fn.extend({val:function(e){var t,n,i,r=this[0];return arguments.length?(i=v(e),this.each((function(n){var r;1===this.nodeType&&(null==(r=i?e.call(this,n,T(this).val()):e)?r="":"number"==typeof r?r+="":Array.isArray(r)&&(r=T.map(r,(function(e){return null==e?"":e+""}))),(t=T.valHooks[this.type]||T.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,r,"value")||(this.value=r))}))):r?(t=T.valHooks[r.type]||T.valHooks[r.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(r,"value"))?n:"string"==typeof(n=r.value)?n.replace(Mt,""):null==n?"":n:void 0}}),T.extend({valHooks:{option:{get:function(e){var t=T.find.attr(e,"value");return null!=t?t:Tt(T.text(e))}},select:{get:function(e){var t,n,i,r=e.options,o=e.selectedIndex,s="select-one"===e.type,a=s?null:[],u=s?o+1:r.length;for(i=o<0?u:s?o:0;i<u;i++)if(((n=r[i]).selected||i===o)&&!n.disabled&&(!n.parentNode.disabled||!R(n.parentNode,"optgroup"))){if(t=T(n).val(),s)return t;a.push(t)}return a},set:function(e,t){for(var n,i,r=e.options,o=T.makeArray(t),s=r.length;s--;)((i=r[s]).selected=T.inArray(T.valHooks.option.get(i),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),T.each(["radio","checkbox"],(function(){T.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=T.inArray(T(e).val(),t)>-1}},g.checkOn||(T.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}));var Ct=i.location,Pt={guid:Date.now()},Ft=/\?/;T.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new i.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||T.error("Invalid XML: "+(n?T.map(n.childNodes,(function(e){return e.textContent})).join("\n"):e)),t};var Dt=/^(?:focusinfocus|focusoutblur)$/,It=function(e){e.stopPropagation()};T.extend(T.event,{trigger:function(e,t,n,r){var o,s,a,u,l,c,h,d,f=[n||y],A=p.call(e,"type")?e.type:e,g=p.call(e,"namespace")?e.namespace.split("."):[];if(s=d=a=n=n||y,3!==n.nodeType&&8!==n.nodeType&&!Dt.test(A+T.event.triggered)&&(A.indexOf(".")>-1&&(g=A.split("."),A=g.shift(),g.sort()),l=A.indexOf(":")<0&&"on"+A,(e=e[T.expando]?e:new T.Event(A,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=g.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+g.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:T.makeArray(t,[e]),h=T.event.special[A]||{},r||!h.trigger||!1!==h.trigger.apply(n,t))){if(!r&&!h.noBubble&&!m(n)){for(u=h.delegateType||A,Dt.test(u+A)||(s=s.parentNode);s;s=s.parentNode)f.push(s),a=s;a===(n.ownerDocument||y)&&f.push(a.defaultView||a.parentWindow||i)}for(o=0;(s=f[o++])&&!e.isPropagationStopped();)d=s,e.type=o>1?u:h.bindType||A,(c=(ae.get(s,"events")||Object.create(null))[e.type]&&ae.get(s,"handle"))&&c.apply(s,t),(c=l&&s[l])&&c.apply&&oe(s)&&(e.result=c.apply(s,t),!1===e.result&&e.preventDefault());return e.type=A,r||e.isDefaultPrevented()||h._default&&!1!==h._default.apply(f.pop(),t)||!oe(n)||l&&v(n[A])&&!m(n)&&((a=n[l])&&(n[l]=null),T.event.triggered=A,e.isPropagationStopped()&&d.addEventListener(A,It),n[A](),e.isPropagationStopped()&&d.removeEventListener(A,It),T.event.triggered=void 0,a&&(n[l]=a)),e.result}},simulate:function(e,t,n){var i=T.extend(new T.Event,n,{type:e,isSimulated:!0});T.event.trigger(i,null,t)}}),T.fn.extend({trigger:function(e,t){return this.each((function(){T.event.trigger(e,t,this)}))},triggerHandler:function(e,t){var n=this[0];if(n)return T.event.trigger(e,t,n,!0)}});var Ot=/\[\]$/,Bt=/\r?\n/g,Lt=/^(?:submit|button|image|reset|file)$/i,Vt=/^(?:input|select|textarea|keygen)/i;function kt(e,t,n,i){var r;if(Array.isArray(t))T.each(t,(function(t,r){n||Ot.test(e)?i(e,r):kt(e+"["+("object"==typeof r&&null!=r?t:"")+"]",r,n,i)}));else if(n||"object"!==b(t))i(e,t);else for(r in t)kt(e+"["+r+"]",t[r],n,i)}T.param=function(e,t){var n,i=[],r=function(e,t){var n=v(t)?t():t;i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!T.isPlainObject(e))T.each(e,(function(){r(this.name,this.value)}));else for(n in e)kt(n,e[n],t,r);return i.join("&")},T.fn.extend({serialize:function(){return T.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var e=T.prop(this,"elements");return e?T.makeArray(e):this})).filter((function(){var e=this.type;return this.name&&!T(this).is(":disabled")&&Vt.test(this.nodeName)&&!Lt.test(e)&&(this.checked||!Te.test(e))})).map((function(e,t){var n=T(this).val();return null==n?null:Array.isArray(n)?T.map(n,(function(e){return{name:t.name,value:e.replace(Bt,"\r\n")}})):{name:t.name,value:n.replace(Bt,"\r\n")}})).get()}});var Nt=/%20/g,Qt=/#.*$/,Ut=/([?&])_=[^&]*/,Wt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Gt=/^(?:GET|HEAD)$/,jt=/^\/\//,zt={},Xt={},Ht="*/".concat("*"),qt=y.createElement("a");function Yt(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,r=0,o=t.toLowerCase().match(H)||[];if(v(n))for(;i=o[r++];)"+"===i[0]?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function Zt(e,t,n,i){var r={},o=e===Xt;function s(a){var u;return r[a]=!0,T.each(e[a]||[],(function(e,a){var l=a(t,n,i);return"string"!=typeof l||o||r[l]?o?!(u=l):void 0:(t.dataTypes.unshift(l),s(l),!1)})),u}return s(t.dataTypes[0])||!r["*"]&&s("*")}function Kt(e,t){var n,i,r=T.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((r[n]?e:i||(i={}))[n]=t[n]);return i&&T.extend(!0,e,i),e}qt.href=Ct.href,T.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ct.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Ct.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ht,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":T.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Kt(Kt(e,T.ajaxSettings),t):Kt(T.ajaxSettings,e)},ajaxPrefilter:Yt(zt),ajaxTransport:Yt(Xt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var n,r,o,s,a,u,l,c,h,d,p=T.ajaxSetup({},t),f=p.context||p,A=p.context&&(f.nodeType||f.jquery)?T(f):T.event,g=T.Deferred(),v=T.Callbacks("once memory"),m=p.statusCode||{},_={},w={},b="canceled",x={readyState:0,getResponseHeader:function(e){var t;if(l){if(!s)for(s={};t=Wt.exec(o);)s[t[1].toLowerCase()+" "]=(s[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=s[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return l?o:null},setRequestHeader:function(e,t){return null==l&&(e=w[e.toLowerCase()]=w[e.toLowerCase()]||e,_[e]=t),this},overrideMimeType:function(e){return null==l&&(p.mimeType=e),this},statusCode:function(e){var t;if(e)if(l)x.always(e[x.status]);else for(t in e)m[t]=[m[t],e[t]];return this},abort:function(e){var t=e||b;return n&&n.abort(t),E(0,t),this}};if(g.promise(x),p.url=((e||p.url||Ct.href)+"").replace(jt,Ct.protocol+"//"),p.type=t.method||t.type||p.method||p.type,p.dataTypes=(p.dataType||"*").toLowerCase().match(H)||[""],null==p.crossDomain){u=y.createElement("a");try{u.href=p.url,u.href=u.href,p.crossDomain=qt.protocol+"//"+qt.host!=u.protocol+"//"+u.host}catch(e){p.crossDomain=!0}}if(p.data&&p.processData&&"string"!=typeof p.data&&(p.data=T.param(p.data,p.traditional)),Zt(zt,p,t,x),l)return x;for(h in(c=T.event&&p.global)&&0==T.active++&&T.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!Gt.test(p.type),r=p.url.replace(Qt,""),p.hasContent?p.data&&p.processData&&0===(p.contentType||"").indexOf("application/x-www-form-urlencoded")&&(p.data=p.data.replace(Nt,"+")):(d=p.url.slice(r.length),p.data&&(p.processData||"string"==typeof p.data)&&(r+=(Ft.test(r)?"&":"?")+p.data,delete p.data),!1===p.cache&&(r=r.replace(Ut,"$1"),d=(Ft.test(r)?"&":"?")+"_="+Pt.guid+++d),p.url=r+d),p.ifModified&&(T.lastModified[r]&&x.setRequestHeader("If-Modified-Since",T.lastModified[r]),T.etag[r]&&x.setRequestHeader("If-None-Match",T.etag[r])),(p.data&&p.hasContent&&!1!==p.contentType||t.contentType)&&x.setRequestHeader("Content-Type",p.contentType),x.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+Ht+"; q=0.01":""):p.accepts["*"]),p.headers)x.setRequestHeader(h,p.headers[h]);if(p.beforeSend&&(!1===p.beforeSend.call(f,x,p)||l))return x.abort();if(b="abort",v.add(p.complete),x.done(p.success),x.fail(p.error),n=Zt(Xt,p,t,x)){if(x.readyState=1,c&&A.trigger("ajaxSend",[x,p]),l)return x;p.async&&p.timeout>0&&(a=i.setTimeout((function(){x.abort("timeout")}),p.timeout));try{l=!1,n.send(_,E)}catch(e){if(l)throw e;E(-1,e)}}else E(-1,"No Transport");function E(e,t,s,u){var h,d,y,_,w,b=t;l||(l=!0,a&&i.clearTimeout(a),n=void 0,o=u||"",x.readyState=e>0?4:0,h=e>=200&&e<300||304===e,s&&(_=function(e,t,n){for(var i,r,o,s,a=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(r in a)if(a[r]&&a[r].test(i)){u.unshift(r);break}if(u[0]in n)o=u[0];else{for(r in n){if(!u[0]||e.converters[r+" "+u[0]]){o=r;break}s||(s=r)}o=o||s}if(o)return o!==u[0]&&u.unshift(o),n[o]}(p,x,s)),!h&&T.inArray("script",p.dataTypes)>-1&&T.inArray("json",p.dataTypes)<0&&(p.converters["text script"]=function(){}),_=function(e,t,n,i){var r,o,s,a,u,l={},c=e.dataTypes.slice();if(c[1])for(s in e.converters)l[s.toLowerCase()]=e.converters[s];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(s=l[u+" "+o]||l["* "+o]))for(r in l)if((a=r.split(" "))[1]===o&&(s=l[u+" "+a[0]]||l["* "+a[0]])){!0===s?s=l[r]:!0!==l[r]&&(o=a[0],c.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}(p,_,x,h),h?(p.ifModified&&((w=x.getResponseHeader("Last-Modified"))&&(T.lastModified[r]=w),(w=x.getResponseHeader("etag"))&&(T.etag[r]=w)),204===e||"HEAD"===p.type?b="nocontent":304===e?b="notmodified":(b=_.state,d=_.data,h=!(y=_.error))):(y=b,!e&&b||(b="error",e<0&&(e=0))),x.status=e,x.statusText=(t||b)+"",h?g.resolveWith(f,[d,b,x]):g.rejectWith(f,[x,b,y]),x.statusCode(m),m=void 0,c&&A.trigger(h?"ajaxSuccess":"ajaxError",[x,p,h?d:y]),v.fireWith(f,[x,b]),c&&(A.trigger("ajaxComplete",[x,p]),--T.active||T.event.trigger("ajaxStop")))}return x},getJSON:function(e,t,n){return T.get(e,t,n,"json")},getScript:function(e,t){return T.get(e,void 0,t,"script")}}),T.each(["get","post"],(function(e,t){T[t]=function(e,n,i,r){return v(n)&&(r=r||i,i=n,n=void 0),T.ajax(T.extend({url:e,type:t,dataType:r,data:n,success:i},T.isPlainObject(e)&&e))}})),T.ajaxPrefilter((function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")})),T._evalUrl=function(e,t,n){return T.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){T.globalEval(e,t,n)}})},T.fn.extend({wrapAll:function(e){var t;return this[0]&&(v(e)&&(e=e.call(this[0])),t=T(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map((function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e})).append(this)),this},wrapInner:function(e){return v(e)?this.each((function(t){T(this).wrapInner(e.call(this,t))})):this.each((function(){var t=T(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)}))},wrap:function(e){var t=v(e);return this.each((function(n){T(this).wrapAll(t?e.call(this,n):e)}))},unwrap:function(e){return this.parent(e).not("body").each((function(){T(this).replaceWith(this.childNodes)})),this}}),T.expr.pseudos.hidden=function(e){return!T.expr.pseudos.visible(e)},T.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},T.ajaxSettings.xhr=function(){try{return new i.XMLHttpRequest}catch(e){}};var $t={0:200,1223:204},Jt=T.ajaxSettings.xhr();g.cors=!!Jt&&"withCredentials"in Jt,g.ajax=Jt=!!Jt,T.ajaxTransport((function(e){var t,n;if(g.cors||Jt&&!e.crossDomain)return{send:function(r,o){var s,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(s in e.xhrFields)a[s]=e.xhrFields[s];for(s in e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||r["X-Requested-With"]||(r["X-Requested-With"]="XMLHttpRequest"),r)a.setRequestHeader(s,r[s]);t=function(e){return function(){t&&(t=n=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===e?a.abort():"error"===e?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o($t[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=t(),n=a.onerror=a.ontimeout=t("error"),void 0!==a.onabort?a.onabort=n:a.onreadystatechange=function(){4===a.readyState&&i.setTimeout((function(){t&&n()}))},t=t("abort");try{a.send(e.hasContent&&e.data||null)}catch(e){if(t)throw e}},abort:function(){t&&t()}}})),T.ajaxPrefilter((function(e){e.crossDomain&&(e.contents.script=!1)})),T.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return T.globalEval(e),e}}}),T.ajaxPrefilter("script",(function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")})),T.ajaxTransport("script",(function(e){var t,n;if(e.crossDomain||e.scriptAttrs)return{send:function(i,r){t=T("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&r("error"===e.type?404:200,e.type)}),y.head.appendChild(t[0])},abort:function(){n&&n()}}}));var en,tn=[],nn=/(=)\?(?=&|$)|\?\?/;T.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=tn.pop()||T.expando+"_"+Pt.guid++;return this[e]=!0,e}}),T.ajaxPrefilter("json jsonp",(function(e,t,n){var r,o,s,a=!1!==e.jsonp&&(nn.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&nn.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=v(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(nn,"$1"+r):!1!==e.jsonp&&(e.url+=(Ft.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return s||T.error(r+" was not called"),s[0]},e.dataTypes[0]="json",o=i[r],i[r]=function(){s=arguments},n.always((function(){void 0===o?T(i).removeProp(r):i[r]=o,e[r]&&(e.jsonpCallback=t.jsonpCallback,tn.push(r)),s&&v(o)&&o(s[0]),s=o=void 0})),"script"})),g.createHTMLDocument=((en=y.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===en.childNodes.length),T.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(g.createHTMLDocument?((i=(t=y.implementation.createHTMLDocument("")).createElement("base")).href=y.location.href,t.head.appendChild(i)):t=y),o=!n&&[],(r=Q.exec(e))?[t.createElement(r[1])]:(r=De([e],t,o),o&&o.length&&T(o).remove(),T.merge([],r.childNodes)));var i,r,o},T.fn.load=function(e,t,n){var i,r,o,s=this,a=e.indexOf(" ");return a>-1&&(i=Tt(e.slice(a)),e=e.slice(0,a)),v(t)?(n=t,t=void 0):t&&"object"==typeof t&&(r="POST"),s.length>0&&T.ajax({url:e,type:r||"GET",dataType:"html",data:t}).done((function(e){o=arguments,s.html(i?T("<div>").append(T.parseHTML(e)).find(i):e)})).always(n&&function(e,t){s.each((function(){n.apply(this,o||[e.responseText,t,e])}))}),this},T.expr.pseudos.animated=function(e){return T.grep(T.timers,(function(t){return e===t.elem})).length},T.offset={setOffset:function(e,t,n){var i,r,o,s,a,u,l=T.css(e,"position"),c=T(e),h={};"static"===l&&(e.style.position="relative"),a=c.offset(),o=T.css(e,"top"),u=T.css(e,"left"),("absolute"===l||"fixed"===l)&&(o+u).indexOf("auto")>-1?(s=(i=c.position()).top,r=i.left):(s=parseFloat(o)||0,r=parseFloat(u)||0),v(t)&&(t=t.call(e,n,T.extend({},a))),null!=t.top&&(h.top=t.top-a.top+s),null!=t.left&&(h.left=t.left-a.left+r),"using"in t?t.using.call(e,h):c.css(h)}},T.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each((function(t){T.offset.setOffset(this,e,t)}));var t,n,i=this[0];return i?i.getClientRects().length?(t=i.getBoundingClientRect(),n=i.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,i=this[0],r={top:0,left:0};if("fixed"===T.css(i,"position"))t=i.getBoundingClientRect();else{for(t=this.offset(),n=i.ownerDocument,e=i.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===T.css(e,"position");)e=e.parentNode;e&&e!==i&&1===e.nodeType&&((r=T(e).offset()).top+=T.css(e,"borderTopWidth",!0),r.left+=T.css(e,"borderLeftWidth",!0))}return{top:t.top-r.top-T.css(i,"marginTop",!0),left:t.left-r.left-T.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var e=this.offsetParent;e&&"static"===T.css(e,"position");)e=e.offsetParent;return e||Ae}))}}),T.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(e,t){var n="pageYOffset"===t;T.fn[e]=function(i){return ee(this,(function(e,i,r){var o;if(m(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===r)return o?o[t]:e[i];o?o.scrollTo(n?o.pageXOffset:r,n?r:o.pageYOffset):e[i]=r}),e,i,arguments.length)}})),T.each(["top","left"],(function(e,t){T.cssHooks[t]=et(g.pixelPosition,(function(e,n){if(n)return n=Je(e,t),qe.test(n)?T(e).position()[t]+"px":n}))})),T.each({Height:"height",Width:"width"},(function(e,t){T.each({padding:"inner"+e,content:t,"":"outer"+e},(function(n,i){T.fn[i]=function(r,o){var s=arguments.length&&(n||"boolean"!=typeof r),a=n||(!0===r||!0===o?"margin":"border");return ee(this,(function(t,n,r){var o;return m(t)?0===i.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===r?T.css(t,n,a):T.style(t,n,r,a)}),t,s?r:void 0,s)}}))})),T.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(e,t){T.fn[t]=function(e){return this.on(t,e)}})),T.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),T.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(e,t){T.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}));var rn=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;T.proxy=function(e,t){var n,i,r;if("string"==typeof t&&(n=e[t],t=e,e=n),v(e))return i=a.call(arguments,2),(r=function(){return e.apply(t||this,i.concat(a.call(arguments)))}).guid=e.guid=e.guid||T.guid++,r},T.holdReady=function(e){e?T.readyWait++:T.ready(!0)},T.isArray=Array.isArray,T.parseJSON=JSON.parse,T.nodeName=R,T.isFunction=v,T.isWindow=m,T.camelCase=re,T.type=b,T.now=Date.now,T.isNumeric=function(e){var t=T.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},T.trim=function(e){return null==e?"":(e+"").replace(rn,"$1")},void 0===(n=function(){return T}.apply(t,[]))||(e.exports=n);var on=i.jQuery,sn=i.$;return T.noConflict=function(e){return i.$===T&&(i.$=sn),e&&i.jQuery===T&&(i.jQuery=on),T},void 0===r&&(i.jQuery=i.$=T),T}))},266:(e,t,n)=>{"use strict";var i=n(774);t.g=void 0;var r=n(85),o=n(205),s=n(844);"function"!=typeof navigator.getVRDisplays&&new s({allowCardboardOnDesktop:!0}),t.g={isEnableController:!0,isEnableZoom:!0,gyroMode:"VR",yaw:0,pitch:0,fov:65,yawRange:[-180,180],pitchRange:[-90,90],fovRange:[30,110]};var a=function(){function e(e,n){this.videoEle=null,this.vrEle=null,this.viewer=null,this.controller=null,this.options=Object.assign({},t.g,n),this.player=e,this.enterVR()}return e.prototype.isGyroSensorAvailable=function(){return new Promise((function(e){return r.PanoViewer.isGyroSensorAvailable(e)}))},e.prototype.isSupported=function(){return r.PanoViewer.isSupported()},e.prototype.enterVR=function(e){return i.g(i.v(0,i.d(["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",[7,1655,252,300,416,421,419,473,1366,1429,1430,1368,1496,1501,1499,1653]]),[function(){return void 0===e?void 0:e},function(){return"undefined"==typeof document?void 0:document},function(){return"undefined"==typeof console?void 0:console},function(){return void 0===r?void 0:r},function(){return"undefined"==typeof Object?void 0:Object},function(){return"undefined"==typeof window?void 0:window},function(){return void 0===o?void 0:o}])).call(this)},e.prototype.exitVR=function(){this.videoEle.style.visibility="visible",this.vrEle.style.visibility="hidden",this.controller&&(this.controller.style.visibility="hidden")},e.prototype.lookAt=function(e,t){return this.viewer.lookAt(e,t)},e.prototype.setGyroMode=function(e){return this.viewer.setGyroMode(e)},e.prototype.enableSensor=function(){return this.viewer.enableSensor()},e.pluginName="VR",e}();function u(e,t){return i.g(i.v(0,i.d(["BgQ9Aj0DRZA2BgU9Aj0DPQQ8BAkALjAACQEIAwIqMjI8ADkFcAVsBXUFZwVpBW4Fcy8JAi45BWEFcwVzBWkFZwVuQgkCCCcAPAA5BXAFbAV1BWcFaQVuBXMvIDwDCQIIJwAqMjI8AwkALjkFcAVsBXUFZwVpBW4FTgVhBW0FZUIgLzAEFjIyMAMAAxYyMhg+PAMaCQAAKjIyGD4=",[]]),[function(){return void 0===a?void 0:a},function(){return void 0===t?void 0:t},function(){return"undefined"==typeof Object?void 0:Object}])).call(this)}u.pluginName=a.pluginName,t.default=u},205:(e,t,n)=>{"use strict";n.r(t),n.d(t,{PanoControls:()=>s});var i=n(58),r=n.n(i);function o(e,t,n){var i,o,s;this._$wrapper=r()(e),this._diameter=parseInt(window.getComputedStyle(e).width),this._pieRadius=n||this._diameter/2,this.state={},t&&(i=t.direction,o=t.fov,s=t.horizontalAngleOfImage),this.setState(i,o,s),this._updateNavigator(this._diameter),this._updateRangeCircle(this._diameter,this.state.horizontalAngleOfImage)}o.prototype.setState=function(e,t,n){this.state.direction=e||this.state.direction||0,this.state.fov=t||this.state.fov||0,n&&n!==this.state.horizontalAngleOfImage&&(this.state.horizontalAngleOfImage=n,this._updateRangeCircle(this._diameter,n)),this.render(this.state)},o.prototype.render=function(e){if(this._pie){var t=this._pieRadius,n=e.fov*Math.PI/180,i=Math.sin(n)*t,r=Math.cos(n)*t*-1,o="M 0 0 v -"+t+" A "+t+" "+t+" 1 "+(e.fov>180?1:0)+" 1 "+i+" "+r+" z";this._pie.setAttribute("d",o),this._$navigator.css({transform:"rotateZ("+(-e.direction-e.fov/2)+"deg)"})}},o.prototype.destroy=function(){this._$wrapper.empty()},o.prototype._updateRangeCircle=function(e,t){var n=(t||360)/360,i=e/2-1.5,o=2*Math.PI*i*n,s=2*Math.PI*i*(1-n),a=this._$wrapper.find(".range");a.hasClass("range")||(this._$wrapper.append("<svg class='nav'><circle class='range'></circle></svg>"),a=this._$wrapper.find(".range"));var u=this._$wrapper.find("svg.nav")[0],l=this._$wrapper.find("circle.range")[0];u.setAttribute("width",e),u.setAttribute("height",e),u.setAttribute("viewbox","0 0 "+e+" "+e),l.setAttribute("cx",e/2),l.setAttribute("cy",e/2),l.setAttribute("r",i),l.setAttribute("stroke","rgba(255, 255, 255, 0.8)"),l.setAttribute("stroke-dasharray",o+", "+s),l.setAttribute("stroke-width",2),l.setAttribute("fill","none"),a.css("stroke-dashoffset","-"+(2*Math.PI*i/4+s/2)),r()(this._pie).css({fill:"rgba(255,255,255,.8)"})},o.prototype._updateNavigator=function(e){var t,n;this._$navigator=this._$wrapper.find(".pie"),this._$navigator.hasClass("pie")||(this._$wrapper.append('<div class="pie"><svg><path class="inner-pie"/></svg></div>'),this._$navigator=this._$wrapper.find(".pie")),this._pie=this._$wrapper.find(".inner-pie")[0],t=this._$navigator.find("svg"),n=this._$navigator.find("path"),t.css({width:e,height:e}),t[0].setAttribute("viewbox","0 0 "+e+" "+e),n[0].setAttribute("transform","translate("+e/2+", "+e/2+")"),this._$navigator.css("position","absolute")};var s={init:function(e,t,n){if(!o)return void console.warn("PieView is not initialized.");var i=e.el_;n&&(n.enableTouchOption||n.enableGyroOption)&&GyroTouchOptions(i,t,n),function(e){var t,n=(t=navigator.userAgent||navigator.vendor||window.opera,/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(t)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(t.substr(0,4)),'\t\t\t<div class="panoviewer-control">\t\t\t\t<div class="camera"></div>\t\t\t</div>');document.querySelector(".panoviewer-control")||e.insertAdjacentHTML("beforeend",n)}(i);var r,s,a,u,l=document.querySelector(".camera"),c=new o(l);t.on({ready:function(){var e=t.getYawRange(),n=180/Math.PI*(2*Math.atan(t._aspectRatio*Math.tan(t.getFov()*Math.PI/360)));c.setState(t.getYaw(),n,e[1]-e[0])},viewChange:function(e){var n=180/Math.PI*(2*Math.atan(t._aspectRatio*Math.tan(t.getFov()*Math.PI/360)));c.setState(e.yaw,n)}}),l.addEventListener("click",(function(){t.lookAt({yaw:0,pitch:0},400)})),window.addEventListener("resize",(r=function(){t.updateViewportDimensions();var e=t.getYaw(),n=180/Math.PI*(2*Math.atan(t._aspectRatio*Math.tan(t.getFov()*Math.PI/360)));c.setState(e,n)},function(){var e=this,t=arguments,n=function(){u=null,r.apply(e,t)},i=a;clearTimeout(u),u=setTimeout(n,s),i&&r.apply(e,t)})),t.enableSensor().catch((function(){return!1}));const h=function(){if(/iP(hone|od|ad)/.test(navigator.platform)){var e=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);return[parseInt(e[1],10),parseInt(e[2],10),parseInt(e[3]||0,10)]}}();h&&12===h[0]&&h[1]}}},844:function(e,t,n){e.exports=function(){"use strict";const e=void 0!==n.g?n.g:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},t=Symbol("@@webxr-polyfill/EventTarget");class i{constructor(){this[t]={listeners:new Map}}addEventListener(e,n){if("string"!=typeof e)throw new Error("`type` must be a string");if("function"!=typeof n)throw new Error("`listener` must be a function");const i=this[t].listeners.get(e)||[];i.push(n),this[t].listeners.set(e,i)}removeEventListener(e,n){if("string"!=typeof e)throw new Error("`type` must be a string");if("function"!=typeof n)throw new Error("`listener` must be a function");const i=this[t].listeners.get(e)||[];for(let e=i.length;e>=0;e--)i[e]===n&&i.pop()}dispatchEvent(e,n){const i=this[t].listeners.get(e)||[],r=[];for(let e=0;e<i.length;e++)r[e]=i[e];for(let e of r)e(n);"function"==typeof this[`on${e}`]&&this[`on${e}`](n)}}let r="undefined"!=typeof Float32Array?Float32Array:Array;function o(){let e=new r(16);return r!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0),e[0]=1,e[5]=1,e[10]=1,e[15]=1,e}function s(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=1,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=1,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e}function a(e,t){let n=t[0],i=t[1],r=t[2],o=t[3],s=t[4],a=t[5],u=t[6],l=t[7],c=t[8],h=t[9],d=t[10],p=t[11],f=t[12],A=t[13],g=t[14],v=t[15],m=n*a-i*s,y=n*u-r*s,_=n*l-o*s,w=i*u-r*a,b=i*l-o*a,x=r*l-o*u,E=c*A-h*f,T=c*g-d*f,S=c*v-p*f,R=h*g-d*A,M=h*v-p*A,C=d*v-p*g,P=m*C-y*M+_*R+w*S-b*T+x*E;return P?(P=1/P,e[0]=(a*C-u*M+l*R)*P,e[1]=(r*M-i*C-o*R)*P,e[2]=(A*x-g*b+v*w)*P,e[3]=(d*b-h*x-p*w)*P,e[4]=(u*S-s*C-l*T)*P,e[5]=(n*C-r*S+o*T)*P,e[6]=(g*_-f*x-v*y)*P,e[7]=(c*x-d*_+p*y)*P,e[8]=(s*M-a*S+l*E)*P,e[9]=(i*S-n*M-o*E)*P,e[10]=(f*b-A*_+v*m)*P,e[11]=(h*_-c*b-p*m)*P,e[12]=(a*T-s*R-u*E)*P,e[13]=(n*R-i*T+r*E)*P,e[14]=(A*y-f*w-g*m)*P,e[15]=(c*w-h*y+d*m)*P,e):null}function u(e,t,n){let i=t[0],r=t[1],o=t[2],s=t[3],a=t[4],u=t[5],l=t[6],c=t[7],h=t[8],d=t[9],p=t[10],f=t[11],A=t[12],g=t[13],v=t[14],m=t[15],y=n[0],_=n[1],w=n[2],b=n[3];return e[0]=y*i+_*a+w*h+b*A,e[1]=y*r+_*u+w*d+b*g,e[2]=y*o+_*l+w*p+b*v,e[3]=y*s+_*c+w*f+b*m,y=n[4],_=n[5],w=n[6],b=n[7],e[4]=y*i+_*a+w*h+b*A,e[5]=y*r+_*u+w*d+b*g,e[6]=y*o+_*l+w*p+b*v,e[7]=y*s+_*c+w*f+b*m,y=n[8],_=n[9],w=n[10],b=n[11],e[8]=y*i+_*a+w*h+b*A,e[9]=y*r+_*u+w*d+b*g,e[10]=y*o+_*l+w*p+b*v,e[11]=y*s+_*c+w*f+b*m,y=n[12],_=n[13],w=n[14],b=n[15],e[12]=y*i+_*a+w*h+b*A,e[13]=y*r+_*u+w*d+b*g,e[14]=y*o+_*l+w*p+b*v,e[15]=y*s+_*c+w*f+b*m,e}function l(e,t,n){let i=t[0],r=t[1],o=t[2],s=t[3],a=i+i,u=r+r,l=o+o,c=i*a,h=i*u,d=i*l,p=r*u,f=r*l,A=o*l,g=s*a,v=s*u,m=s*l;return e[0]=1-(p+A),e[1]=h+m,e[2]=d-v,e[3]=0,e[4]=h-m,e[5]=1-(c+A),e[6]=f+g,e[7]=0,e[8]=d+v,e[9]=f-g,e[10]=1-(c+p),e[11]=0,e[12]=n[0],e[13]=n[1],e[14]=n[2],e[15]=1,e}function c(e,t){return e[0]=t[12],e[1]=t[13],e[2]=t[14],e}function h(e,t){let n=t[0]+t[5]+t[10],i=0;return n>0?(i=2*Math.sqrt(n+1),e[3]=.25*i,e[0]=(t[6]-t[9])/i,e[1]=(t[8]-t[2])/i,e[2]=(t[1]-t[4])/i):t[0]>t[5]&&t[0]>t[10]?(i=2*Math.sqrt(1+t[0]-t[5]-t[10]),e[3]=(t[6]-t[9])/i,e[0]=.25*i,e[1]=(t[1]+t[4])/i,e[2]=(t[8]+t[2])/i):t[5]>t[10]?(i=2*Math.sqrt(1+t[5]-t[0]-t[10]),e[3]=(t[8]-t[2])/i,e[0]=(t[1]+t[4])/i,e[1]=.25*i,e[2]=(t[6]+t[9])/i):(i=2*Math.sqrt(1+t[10]-t[0]-t[5]),e[3]=(t[1]-t[4])/i,e[0]=(t[8]+t[2])/i,e[1]=(t[6]+t[9])/i,e[2]=.25*i),e}function d(e,t,n,i,r){let o,s=1/Math.tan(t/2);return e[0]=s/n,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=s,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[11]=-1,e[12]=0,e[13]=0,e[15]=0,null!=r&&r!==1/0?(o=1/(i-r),e[10]=(r+i)*o,e[14]=2*r*i*o):(e[10]=-1,e[14]=-2*i),e}function p(){let e=new r(3);return r!=Float32Array&&(e[0]=0,e[1]=0,e[2]=0),e}function f(e){var t=new r(3);return t[0]=e[0],t[1]=e[1],t[2]=e[2],t}function A(e,t,n){let i=new r(3);return i[0]=e,i[1]=t,i[2]=n,i}function g(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e}function v(e,t,n){return e[0]=t[0]+n[0],e[1]=t[1]+n[1],e[2]=t[2]+n[2],e}function m(e,t,n){return e[0]=t[0]*n,e[1]=t[1]*n,e[2]=t[2]*n,e}function y(e,t){let n=t[0],i=t[1],r=t[2],o=n*n+i*i+r*r;return o>0&&(o=1/Math.sqrt(o),e[0]=t[0]*o,e[1]=t[1]*o,e[2]=t[2]*o),e}function _(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]}function w(e,t,n){let i=n[0],r=n[1],o=n[2],s=n[3],a=t[0],u=t[1],l=t[2],c=r*l-o*u,h=o*a-i*l,d=i*u-r*a,p=r*d-o*h,f=o*c-i*d,A=i*h-r*c,g=2*s;return c*=g,h*=g,d*=g,p*=2,f*=2,A*=2,e[0]=a+c+p,e[1]=u+h+f,e[2]=l+d+A,e}function b(){let e=new r(4);return r!=Float32Array&&(e[0]=0,e[1]=0,e[2]=0),e[3]=1,e}function x(e,t,n){let i=t[0],r=t[1],o=t[2],s=t[3],a=n[0],u=n[1],l=n[2],c=n[3];return e[0]=i*c+s*a+r*l-o*u,e[1]=r*c+s*u+o*a-i*l,e[2]=o*c+s*l+i*u-r*a,e[3]=s*c-i*a-r*u-o*l,e}function E(e,t,n,i){let r,o,s,a,u,l=t[0],c=t[1],h=t[2],d=t[3],p=n[0],f=n[1],A=n[2],g=n[3];return(o=l*p+c*f+h*A+d*g)<0&&(o=-o,p=-p,f=-f,A=-A,g=-g),1-o>1e-6?(r=Math.acos(o),s=Math.sin(r),a=Math.sin((1-i)*r)/s,u=Math.sin(i*r)/s):(a=1-i,u=i),e[0]=a*l+u*p,e[1]=a*c+u*f,e[2]=a*h+u*A,e[3]=a*d+u*g,e}function T(e,t){let n=t[0],i=t[1],r=t[2],o=t[3],s=n*n+i*i+r*r+o*o,a=s?1/s:0;return e[0]=-n*a,e[1]=-i*a,e[2]=-r*a,e[3]=o*a,e}Math.PI,p(),function(){let e=new r(4);r!=Float32Array&&(e[0]=0,e[1]=0,e[2]=0,e[3]=0)}();const S=function(e){let t=new r(4);return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t},R=function(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e},M=function(e,t){let n=t[0],i=t[1],r=t[2],o=t[3],s=n*n+i*i+r*r+o*o;return s>0&&(s=1/Math.sqrt(s),e[0]=n*s,e[1]=i*s,e[2]=r*s,e[3]=o*s),e},C=(p(),A(1,0,0),A(0,1,0),b(),b(),function(){let e=new r(9);r!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[5]=0,e[6]=0,e[7]=0),e[0]=1,e[4]=1,e[8]=1}(),Symbol("@@webxr-polyfill/XRRigidTransform"));class P{constructor(){if(this[C]={matrix:null,position:null,orientation:null,inverse:null},0===arguments.length)this[C].matrix=s(new Float32Array(16));else if(1===arguments.length)arguments[0]instanceof Float32Array?this[C].matrix=arguments[0]:(this[C].position=this._getPoint(arguments[0]),this[C].orientation=DOMPointReadOnly.fromPoint({x:0,y:0,z:0,w:1}));else{if(2!==arguments.length)throw new Error("Too many arguments!");this[C].position=this._getPoint(arguments[0]),this[C].orientation=this._getPoint(arguments[1])}if(this[C].matrix){let e=p();c(e,this[C].matrix),this[C].position=DOMPointReadOnly.fromPoint({x:e[0],y:e[1],z:e[2]});let t=b();h(t,this[C].matrix),this[C].orientation=DOMPointReadOnly.fromPoint({x:t[0],y:t[1],z:t[2],w:t[3]})}else this[C].matrix=s(new Float32Array(16)),l(this[C].matrix,function(e,t,n,i){let o=new r(4);return o[0]=e,o[1]=t,o[2]=n,o[3]=i,o}(this[C].orientation.x,this[C].orientation.y,this[C].orientation.z,this[C].orientation.w),A(this[C].position.x,this[C].position.y,this[C].position.z))}_getPoint(e){return e instanceof DOMPointReadOnly?e:DOMPointReadOnly.fromPoint(e)}get matrix(){return this[C].matrix}get position(){return this[C].position}get orientation(){return this[C].orientation}get inverse(){if(null===this[C].inverse){let e=s(new Float32Array(16));a(e,this[C].matrix),this[C].inverse=new P(e),this[C].inverse[C].inverse=this}return this[C].inverse}}const F=Symbol("@@webxr-polyfill/XRSpace");class D{constructor(e=null,t=null){this[F]={specialType:e,inputSource:t,baseMatrix:null,inverseBaseMatrix:null,lastFrameId:-1}}get _specialType(){return this[F].specialType}get _inputSource(){return this[F].inputSource}_ensurePoseUpdated(e,t){t!=this[F].lastFrameId&&(this[F].lastFrameId=t,this._onPoseUpdate(e))}_onPoseUpdate(e){"viewer"==this[F].specialType&&(this._baseMatrix=e.getBasePoseMatrix())}set _baseMatrix(e){this[F].baseMatrix=e,this[F].inverseBaseMatrix=null}get _baseMatrix(){return this[F].baseMatrix||this[F].inverseBaseMatrix&&(this[F].baseMatrix=new Float32Array(16),a(this[F].baseMatrix,this[F].inverseBaseMatrix)),this[F].baseMatrix}set _inverseBaseMatrix(e){this[F].inverseBaseMatrix=e,this[F].baseMatrix=null}get _inverseBaseMatrix(){return this[F].inverseBaseMatrix||this[F].baseMatrix&&(this[F].inverseBaseMatrix=new Float32Array(16),a(this[F].inverseBaseMatrix,this[F].baseMatrix)),this[F].inverseBaseMatrix}_getSpaceRelativeTransform(e){if(!this._inverseBaseMatrix||!e._baseMatrix)return null;let t=new Float32Array(16);return u(t,this._inverseBaseMatrix,e._baseMatrix),new P(t)}}const I=Symbol("@@webxr-polyfill/XRReferenceSpace"),O=["viewer","local","local-floor","bounded-floor","unbounded"];class B extends D{constructor(e,t=null){if(!O.includes(e))throw new Error(`XRReferenceSpaceType must be one of ${O}`);if(super(e),"bounded-floor"===e&&!t)throw new Error("XRReferenceSpace cannot use 'bounded-floor' type if the platform does not provide the floor level");(function(e){return"bounded-floor"===e||"local-floor"===e})(e)&&!t&&((t=s(new Float32Array(16)))[13]=1.6),this._inverseBaseMatrix=t||s(new Float32Array(16)),this[I]={type:e,transform:t,originOffset:s(new Float32Array(16))}}_transformBasePoseMatrix(e,t){u(e,this._inverseBaseMatrix,t)}_originOffsetMatrix(){return this[I].originOffset}_adjustForOriginOffset(e){let t=new Float32Array(16);a(t,this[I].originOffset),u(e,t,e)}_getSpaceRelativeTransform(e){let t=super._getSpaceRelativeTransform(e);return this._adjustForOriginOffset(t.matrix),new XRRigidTransform(t.matrix)}getOffsetReferenceSpace(e){let t=new B(this[I].type,this[I].transform,this[I].bounds);return u(t[I].originOffset,this[I].originOffset,e.matrix),t}}const L=Symbol("@@webxr-polyfill/XR"),V=["inline","immersive-vr","immersive-ar"],k={inline:{requiredFeatures:["viewer"],optionalFeatures:[]},"immersive-vr":{requiredFeatures:["viewer","local"],optionalFeatures:[]},"immersive-ar":{requiredFeatures:["viewer","local"],optionalFeatures:[]}};let N;if("performance"in e==0){let e=Date.now();N=()=>Date.now()-e}else N=()=>performance.now();var Q=N;const U=Symbol("@@webxr-polyfill/XRPose");class W{constructor(e,t){this[U]={transform:e,emulatedPosition:t}}get transform(){return this[U].transform}get emulatedPosition(){return this[U].emulatedPosition}}const G=Symbol("@@webxr-polyfill/XRViewerPose");class j extends W{constructor(e,t,n=!1){super(e,n),this[G]={views:t}}get views(){return this[G].views}}const z=Symbol("@@webxr-polyfill/XRViewport");class X{constructor(e){this[z]={target:e}}get x(){return this[z].target.x}get y(){return this[z].target.y}get width(){return this[z].target.width}get height(){return this[z].target.height}}const H=["left","right","none"],q=Symbol("@@webxr-polyfill/XRView");class Y{constructor(e,t,n,i){if(!H.includes(n))throw new Error(`XREye must be one of: ${H}`);const r=Object.create(null),o=new X(r);this[q]={device:e,eye:n,viewport:o,temp:r,sessionId:i,transform:t}}get eye(){return this[q].eye}get projectionMatrix(){return this[q].device.getProjectionMatrix(this.eye)}get transform(){return this[q].transform}_getViewport(e){if(this[q].device.getViewport(this[q].sessionId,this.eye,e,this[q].temp))return this[q].viewport}}const Z=Symbol("@@webxr-polyfill/XRFrame"),K="XRFrame access outside the callback that produced it is invalid.";let $=0;class J{constructor(e,t,n){this[Z]={id:++$,active:!1,animationFrame:!1,device:e,session:t,sessionId:n}}get session(){return this[Z].session}getViewerPose(e){if(!this[Z].animationFrame)throw new DOMException("getViewerPose can only be called on XRFrame objects passed to XRSession.requestAnimationFrame callbacks.","InvalidStateError");if(!this[Z].active)throw new DOMException(K,"InvalidStateError");const t=this[Z].device,n=this[Z].session;n[pe].viewerSpace._ensurePoseUpdated(t,this[Z].id),e._ensurePoseUpdated(t,this[Z].id);let i=e._getSpaceRelativeTransform(n[pe].viewerSpace);const r=[];for(let i of n[pe].viewSpaces){i._ensurePoseUpdated(t,this[Z].id);let n=e._getSpaceRelativeTransform(i),o=new Y(t,n,i.eye,this[Z].sessionId);r.push(o)}return new j(i,r,!1)}getPose(e,t){if(!this[Z].active)throw new DOMException(K,"InvalidStateError");const n=this[Z].device;if("target-ray"===e._specialType||"grip"===e._specialType)return n.getInputPose(e._inputSource,t,e._specialType);{e._ensurePoseUpdated(n,this[Z].id),t._ensurePoseUpdated(n,this[Z].id);let i=t._getSpaceRelativeTransform(e);return i?new XRPose(i,!1):null}}}const ee=Symbol("@@webxr-polyfill/XRRenderState"),te=Object.freeze({depthNear:.1,depthFar:1e3,inlineVerticalFieldOfView:null,baseLayer:null});class ne{constructor(e={}){const t=Object.assign({},te,e);this[ee]={config:t}}get depthNear(){return this[ee].config.depthNear}get depthFar(){return this[ee].config.depthFar}get inlineVerticalFieldOfView(){return this[ee].config.inlineVerticalFieldOfView}get baseLayer(){return this[ee].config.baseLayer}}const ie=Symbol("@@webxr-polyfill/polyfilled-xr-compatible"),re=Symbol("@@webxr-polyfill/xr-compatible"),oe=Symbol("@@webxr-polyfill/XRWebGLLayer"),se=Object.freeze({antialias:!0,depth:!1,stencil:!1,alpha:!0,multiview:!1,ignoreDepthValues:!1,framebufferScaleFactor:1}),ae=Symbol("@@webxr-polyfill/XRInputSourceEvent");class ue extends Event{constructor(e,t){super(e,t),this[ae]={frame:t.frame,inputSource:t.inputSource},Object.setPrototypeOf(this,ue.prototype)}get frame(){return this[ae].frame}get inputSource(){return this[ae].inputSource}}const le=Symbol("@@webxr-polyfill/XRSessionEvent");class ce extends Event{constructor(e,t){super(e,t),this[le]={session:t.session},Object.setPrototypeOf(this,ce.prototype)}get session(){return this[le].session}}const he=Symbol("@@webxr-polyfill/XRInputSourcesChangeEvent");class de extends Event{constructor(e,t){super(e,t),this[he]={session:t.session,added:t.added,removed:t.removed},Object.setPrototypeOf(this,de.prototype)}get session(){return this[he].session}get added(){return this[he].added}get removed(){return this[he].removed}}const pe=Symbol("@@webxr-polyfill/XRSession");class fe extends D{constructor(e){super(e)}get eye(){return this._specialType}_onPoseUpdate(e){this._inverseBaseMatrix=e.getBaseViewMatrix(this._specialType)}}class Ae extends i{constructor(e,t,n){super();let i="inline"!=t,r=new ne({inlineVerticalFieldOfView:i?null:.5*Math.PI});this[pe]={device:e,mode:t,immersive:i,ended:!1,suspended:!1,frameCallbacks:[],currentFrameCallbacks:null,frameHandle:0,deviceFrameHandle:null,id:n,activeRenderState:r,pendingRenderState:null,viewerSpace:new B("viewer"),viewSpaces:[],currentInputSources:[]},i?this[pe].viewSpaces.push(new fe("left"),new fe("right")):this[pe].viewSpaces.push(new fe("none")),this[pe].onDeviceFrame=()=>{if(this[pe].ended||this[pe].suspended)return;if(this[pe].deviceFrameHandle=null,this[pe].startDeviceFrameLoop(),null!==this[pe].pendingRenderState&&(this[pe].activeRenderState=new ne(this[pe].pendingRenderState),this[pe].pendingRenderState=null,this[pe].activeRenderState.baseLayer&&this[pe].device.onBaseLayerSet(this[pe].id,this[pe].activeRenderState.baseLayer)),null===this[pe].activeRenderState.baseLayer)return;const t=new J(e,this,this[pe].id),n=this[pe].currentFrameCallbacks=this[pe].frameCallbacks;this[pe].frameCallbacks=[],t[Z].active=!0,t[Z].animationFrame=!0,this[pe].device.onFrameStart(this[pe].id,this[pe].activeRenderState),this._checkInputSourcesChange();const i=Q();for(let e=0;e<n.length;e++)try{n[e].cancelled||"function"!=typeof n[e].callback||n[e].callback(i,t)}catch(e){console.error(e)}this[pe].currentFrameCallbacks=null,t[Z].active=!1,this[pe].device.onFrameEnd(this[pe].id)},this[pe].startDeviceFrameLoop=()=>{null===this[pe].deviceFrameHandle&&(this[pe].deviceFrameHandle=this[pe].device.requestAnimationFrame(this[pe].onDeviceFrame))},this[pe].stopDeviceFrameLoop=()=>{const e=this[pe].deviceFrameHandle;null!==e&&(this[pe].device.cancelAnimationFrame(e),this[pe].deviceFrameHandle=null)},this[pe].onPresentationEnd=t=>{if(t!==this[pe].id)return this[pe].suspended=!1,this[pe].startDeviceFrameLoop(),void this.dispatchEvent("focus",{session:this});this[pe].ended=!0,this[pe].stopDeviceFrameLoop(),e.removeEventListener("@@webxr-polyfill/vr-present-end",this[pe].onPresentationEnd),e.removeEventListener("@@webxr-polyfill/vr-present-start",this[pe].onPresentationStart),e.removeEventListener("@@webxr-polyfill/input-select-start",this[pe].onSelectStart),e.removeEventListener("@@webxr-polyfill/input-select-end",this[pe].onSelectEnd),this.dispatchEvent("end",new ce("end",{session:this}))},e.addEventListener("@@webxr-polyfill/vr-present-end",this[pe].onPresentationEnd),this[pe].onPresentationStart=e=>{e!==this[pe].id&&(this[pe].suspended=!0,this[pe].stopDeviceFrameLoop(),this.dispatchEvent("blur",{session:this}))},e.addEventListener("@@webxr-polyfill/vr-present-start",this[pe].onPresentationStart),this[pe].onSelectStart=e=>{e.sessionId===this[pe].id&&this[pe].dispatchInputSourceEvent("selectstart",e.inputSource)},e.addEventListener("@@webxr-polyfill/input-select-start",this[pe].onSelectStart),this[pe].onSelectEnd=e=>{e.sessionId===this[pe].id&&(this[pe].dispatchInputSourceEvent("selectend",e.inputSource),this[pe].dispatchInputSourceEvent("select",e.inputSource))},e.addEventListener("@@webxr-polyfill/input-select-end",this[pe].onSelectEnd),this[pe].onSqueezeStart=e=>{e.sessionId===this[pe].id&&this[pe].dispatchInputSourceEvent("squeezestart",e.inputSource)},e.addEventListener("@@webxr-polyfill/input-squeeze-start",this[pe].onSqueezeStart),this[pe].onSqueezeEnd=e=>{e.sessionId===this[pe].id&&(this[pe].dispatchInputSourceEvent("squeezeend",e.inputSource),this[pe].dispatchInputSourceEvent("squeeze",e.inputSource))},e.addEventListener("@@webxr-polyfill/input-squeeze-end",this[pe].onSqueezeEnd),this[pe].dispatchInputSourceEvent=(t,n)=>{const i=new J(e,this,this[pe].id),r=new ue(t,{frame:i,inputSource:n});i[Z].active=!0,this.dispatchEvent(t,r),i[Z].active=!1},this[pe].startDeviceFrameLoop(),this.onblur=void 0,this.onfocus=void 0,this.onresetpose=void 0,this.onend=void 0,this.onselect=void 0,this.onselectstart=void 0,this.onselectend=void 0}get renderState(){return this[pe].activeRenderState}get environmentBlendMode(){return this[pe].device.environmentBlendMode||"opaque"}async requestReferenceSpace(e){if(this[pe].ended)return;if(!O.includes(e))throw new TypeError(`XRReferenceSpaceType must be one of ${O}`);if(!this[pe].device.doesSessionSupportReferenceSpace(this[pe].id,e))throw new DOMException(`The ${e} reference space is not supported by this session.`,"NotSupportedError");if("viewer"===e)return this[pe].viewerSpace;let t=await this[pe].device.requestFrameOfReferenceTransform(e);if("bounded-floor"===e){if(!t)throw new DOMException(`${e} XRReferenceSpace not supported by this device.`,"NotSupportedError");if(!this[pe].device.requestStageBounds())throw new DOMException(`${e} XRReferenceSpace not supported by this device.`,"NotSupportedError");throw new DOMException(`The WebXR polyfill does not support the ${e} reference space yet.`,"NotSupportedError")}return new B(e,t)}requestAnimationFrame(e){if(this[pe].ended)return;const t=++this[pe].frameHandle;return this[pe].frameCallbacks.push({handle:t,callback:e,cancelled:!1}),t}cancelAnimationFrame(e){let t=this[pe].frameCallbacks,n=t.findIndex((t=>t&&t.handle===e));n>-1&&(t[n].cancelled=!0,t.splice(n,1)),(t=this[pe].currentFrameCallbacks)&&(n=t.findIndex((t=>t&&t.handle===e)))>-1&&(t[n].cancelled=!0)}get inputSources(){return this[pe].device.getInputSources()}async end(){if(!this[pe].ended)return this[pe].immersive&&(this[pe].ended=!0,this[pe].device.removeEventListener("@@webxr-polyfill/vr-present-start",this[pe].onPresentationStart),this[pe].device.removeEventListener("@@webxr-polyfill/vr-present-end",this[pe].onPresentationEnd),this[pe].device.removeEventListener("@@webxr-polyfill/input-select-start",this[pe].onSelectStart),this[pe].device.removeEventListener("@@webxr-polyfill/input-select-end",this[pe].onSelectEnd),this.dispatchEvent("end",new ce("end",{session:this}))),this[pe].stopDeviceFrameLoop(),this[pe].device.endSession(this[pe].id)}updateRenderState(e){if(this[pe].ended)throw new Error("Can't call updateRenderState on an XRSession that has already ended.");if(e.baseLayer&&e.baseLayer._session!==this)throw new Error("Called updateRenderState with a base layer that was created by a different session.");if(null!==e.inlineVerticalFieldOfView&&void 0!==e.inlineVerticalFieldOfView){if(this[pe].immersive)throw new Error("inlineVerticalFieldOfView must not be set for an XRRenderState passed to updateRenderState for an immersive session.");e.inlineVerticalFieldOfView=Math.min(3.13,Math.max(.01,e.inlineVerticalFieldOfView))}if(null===this[pe].pendingRenderState){const e=this[pe].activeRenderState;this[pe].pendingRenderState={depthNear:e.depthNear,depthFar:e.depthFar,inlineVerticalFieldOfView:e.inlineVerticalFieldOfView,baseLayer:e.baseLayer}}Object.assign(this[pe].pendingRenderState,e)}_checkInputSourcesChange(){const e=[],t=[],n=this.inputSources,i=this[pe].currentInputSources;for(const t of n)i.includes(t)||e.push(t);for(const e of i)n.includes(e)||t.push(e);(e.length>0||t.length>0)&&this.dispatchEvent("inputsourceschange",new de("inputsourceschange",{session:this,added:e,removed:t})),this[pe].currentInputSources.length=0;for(const e of n)this[pe].currentInputSources.push(e)}}const ge=Symbol("@@webxr-polyfill/XRInputSource");class ve{constructor(e){this[ge]={impl:e,gripSpace:new D("grip",this),targetRaySpace:new D("target-ray",this)}}get handedness(){return this[ge].impl.handedness}get targetRayMode(){return this[ge].impl.targetRayMode}get gripSpace(){let e=this[ge].impl.targetRayMode;return"gaze"===e||"screen"===e?null:this[ge].gripSpace}get targetRaySpace(){return this[ge].targetRaySpace}get profiles(){return this[ge].impl.profiles}get gamepad(){return this[ge].impl.gamepad}}const me=Symbol("@@webxr-polyfill/XRReferenceSpaceEvent");class ye extends Event{constructor(e,t){super(e,t),this[me]={referenceSpace:t.referenceSpace,transform:t.transform||null},Object.setPrototypeOf(this,ye.prototype)}get referenceSpace(){return this[me].referenceSpace}get transform(){return this[me].transform}}var _e={XRSystem:class extends i{constructor(e){super(),this[L]={device:null,devicePromise:e,immersiveSession:null,inlineSessions:new Set},e.then((e=>{this[L].device=e}))}async isSessionSupported(e){return this[L].device||await this[L].devicePromise,"inline"!=e?Promise.resolve(this[L].device.isSessionSupported(e)):Promise.resolve(!0)}async requestSession(e,t){if(!this[L].device){if("inline"!=e)throw new Error("Polyfill Error: Must call navigator.xr.isSessionSupported() with any XRSessionMode\nor navigator.xr.requestSession('inline') prior to requesting an immersive\nsession. This is a limitation specific to the WebXR Polyfill and does not apply\nto native implementations of the API.");await this[L].devicePromise}if(!V.includes(e))throw new TypeError(`The provided value '${e}' is not a valid enum value of type XRSessionMode`);const n=k[e],i=n.requiredFeatures.concat(t&&t.requiredFeatures?t.requiredFeatures:[]),r=n.optionalFeatures.concat(t&&t.optionalFeatures?t.optionalFeatures:[]),o=new Set;let s=!1;for(let e of i)this[L].device.isFeatureSupported(e)?o.add(e):(console.error(`The required feature '${e}' is not supported`),s=!0);if(s)throw new DOMException("Session does not support some required features","NotSupportedError");for(let e of r)this[L].device.isFeatureSupported(e)?o.add(e):console.log(`The optional feature '${e}' is not supported`);const a=await this[L].device.requestSession(e,o),u=new XRSession(this[L].device,e,a);"inline"==e?this[L].inlineSessions.add(u):this[L].immersiveSession=u;const l=()=>{"inline"==e?this[L].inlineSessions.delete(u):this[L].immersiveSession=null,u.removeEventListener("end",l)};return u.addEventListener("end",l),u}},XRSession:Ae,XRSessionEvent:ce,XRFrame:J,XRView:Y,XRViewport:X,XRViewerPose:j,XRWebGLLayer:class{constructor(e,t,n={}){const i=Object.assign({},se,n);if(!(e instanceof Ae))throw new Error("session must be a XRSession");if(e.ended)throw new Error("InvalidStateError");if(t[ie]&&!0!==t[re])throw new Error("InvalidStateError");const r=t.getParameter(t.FRAMEBUFFER_BINDING);this[oe]={context:t,config:i,framebuffer:r,session:e}}get context(){return this[oe].context}get antialias(){return this[oe].config.antialias}get ignoreDepthValues(){return!0}get framebuffer(){return this[oe].framebuffer}get framebufferWidth(){return this[oe].context.drawingBufferWidth}get framebufferHeight(){return this[oe].context.drawingBufferHeight}get _session(){return this[oe].session}getViewport(e){return e._getViewport(this)}static getNativeFramebufferScaleFactor(e){if(!e)throw new TypeError("getNativeFramebufferScaleFactor must be passed a session.");return e[pe].ended?0:1}},XRSpace:D,XRReferenceSpace:B,XRReferenceSpaceEvent:ye,XRInputSource:ve,XRInputSourceEvent:ue,XRInputSourcesChangeEvent:de,XRRenderState:ne,XRRigidTransform:P,XRPose:W};const we=e=>"function"!=typeof e.prototype.makeXRCompatible&&(e.prototype.makeXRCompatible=function(){return this[re]=!0,Promise.resolve()},!0),be=e=>{const t=e.prototype.getContext;e.prototype.getContext=function(e,n){const i=t.call(this,e,n);return i&&(i[ie]=!0,n&&"xrCompatible"in n&&(i[re]=n.xrCompatible)),i}};var xe;var Ee,Te,Se="undefined"!=typeof window?window:void 0!==n.g?n.g:"undefined"!=typeof self?self:{},Re=(Te=(function(e,t){e.exports=function(){var e,t,n,i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=function(e,t,n){return e+(t-e)*n},o=function(){var e=/iPad|iPhone|iPod/.test(navigator.platform);return function(){return e}}(),s=function(){var e=-1!==navigator.userAgent.indexOf("Version")&&-1!==navigator.userAgent.indexOf("Android")&&-1!==navigator.userAgent.indexOf("Chrome");return function(){return e}}(),a=function(){var e=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);return function(){return e}}(),u=function(){var e=-1!==navigator.userAgent.indexOf("Firefox")&&-1!==navigator.userAgent.indexOf("Android");return function(){return e}}(),l=(t=navigator.userAgent.match(/.*Chrome\/([0-9]+)/),n=t?parseInt(t[1],10):null,function(){return n}),c=function(){var e;return e=o()&&a()&&-1!==navigator.userAgent.indexOf("13_4"),function(){return e}}(),h=function(){var e=!1;if(65===l()){var t=navigator.userAgent.match(/.*Chrome\/([0-9\.]*)/);if(t){var n=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],i=!0,r=!1,o=void 0;try{for(var s,a=e[Symbol.iterator]();!(i=(s=a.next()).done)&&(n.push(s.value),!t||n.length!==t);i=!0);}catch(e){r=!0,o=e}finally{try{!i&&a.return&&a.return()}finally{if(r)throw o}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}(t[1].split("."),4),i=(n[0],n[1],n[2]),r=n[3];e=3325===parseInt(i,10)&&parseInt(r,10)<148}}return function(){return e}}(),d=function(){var e=-1!==navigator.userAgent.indexOf("R7 Build");return function(){return e}}(),p=function(){var e=90==window.orientation||-90==window.orientation;return d()?!e:e},f=function(){return Math.max(window.screen.width,window.screen.height)*window.devicePixelRatio},A=function(){return Math.min(window.screen.width,window.screen.height)*window.devicePixelRatio},g=function(){if(document.exitFullscreen)document.exitFullscreen();else if(document.webkitExitFullscreen)document.webkitExitFullscreen();else if(document.mozCancelFullScreen)document.mozCancelFullScreen();else{if(!document.msExitFullscreen)return!1;document.msExitFullscreen()}return!0},v=function(e,t,n,i){var r=e.createShader(e.VERTEX_SHADER);e.shaderSource(r,t),e.compileShader(r);var o=e.createShader(e.FRAGMENT_SHADER);e.shaderSource(o,n),e.compileShader(o);var s=e.createProgram();for(var a in e.attachShader(s,r),e.attachShader(s,o),i)e.bindAttribLocation(s,i[a],a);return e.linkProgram(s),e.deleteShader(r),e.deleteShader(o),s},m=function(e,t){for(var n={},i=e.getProgramParameter(t,e.ACTIVE_UNIFORMS),r="",o=0;o<i;o++)n[r=e.getActiveUniform(t,o).name.replace("[0]","")]=e.getUniformLocation(t,r);return n},y=function(){var e,t=!1;return e=navigator.userAgent||navigator.vendor||window.opera,(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(e.substr(0,4)))&&(t=!0),t},_=function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},w=function(e){if(o()){var t=e.style.width,n=e.style.height;e.style.width=parseInt(t)+1+"px",e.style.height=parseInt(n)+"px",setTimeout((function(){e.style.width=t,e.style.height=n}),100)}window.canvas=e},b=function(){var e=Math.PI/180,t=.25*Math.PI,n=new Float32Array([0,0,0,1]),i=new Float32Array([0,0,0]);function r(r,o,s,a,u,l){!function(n,i,r,o){var s=Math.tan(i?i.upDegrees*e:t),a=Math.tan(i?i.downDegrees*e:t),u=Math.tan(i?i.leftDegrees*e:t),l=Math.tan(i?i.rightDegrees*e:t),c=2/(u+l),h=2/(s+a);n[0]=c,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=h,n[6]=0,n[7]=0,n[8]=-(u-l)*c*.5,n[9]=(s-a)*h*.5,n[10]=o/(r-o),n[11]=-1,n[12]=0,n[13]=0,n[14]=o*r/(r-o),n[15]=0}(r,a||null,l.depthNear,l.depthFar);var c,h,d,p,f,A,g,v,m,y,_,w,b,x,E,T,S,R,M,C=s.orientation||n;c=o,d=s.position||i,_=(p=(h=C)[0])*(v=p+p),w=p*(m=(f=h[1])+f),b=p*(y=(A=h[2])+A),x=f*m,E=f*y,T=A*y,S=(g=h[3])*v,R=g*m,M=g*y,c[0]=1-(x+T),c[1]=w+M,c[2]=b-R,c[3]=0,c[4]=w-M,c[5]=1-(_+T),c[6]=E+S,c[7]=0,c[8]=b+R,c[9]=E-S,c[10]=1-(_+x),c[11]=0,c[12]=d[0],c[13]=d[1],c[14]=d[2],c[15]=1,u&&function(e,t,n){var i,r,o,s,a,u,l,c,h,d,p,f,A=n[0],g=n[1],v=n[2];t===e?(e[12]=t[0]*A+t[4]*g+t[8]*v+t[12],e[13]=t[1]*A+t[5]*g+t[9]*v+t[13],e[14]=t[2]*A+t[6]*g+t[10]*v+t[14],e[15]=t[3]*A+t[7]*g+t[11]*v+t[15]):(i=t[0],r=t[1],o=t[2],s=t[3],a=t[4],u=t[5],l=t[6],c=t[7],h=t[8],d=t[9],p=t[10],f=t[11],e[0]=i,e[1]=r,e[2]=o,e[3]=s,e[4]=a,e[5]=u,e[6]=l,e[7]=c,e[8]=h,e[9]=d,e[10]=p,e[11]=f,e[12]=i*A+a*g+h*v+t[12],e[13]=r*A+u*g+d*v+t[13],e[14]=o*A+l*g+p*v+t[14],e[15]=s*A+c*g+f*v+t[15])}(o,o,u),function(e,t){var n=t[0],i=t[1],r=t[2],o=t[3],s=t[4],a=t[5],u=t[6],l=t[7],c=t[8],h=t[9],d=t[10],p=t[11],f=t[12],A=t[13],g=t[14],v=t[15],m=n*a-i*s,y=n*u-r*s,_=n*l-o*s,w=i*u-r*a,b=i*l-o*a,x=r*l-o*u,E=c*A-h*f,T=c*g-d*f,S=c*v-p*f,R=h*g-d*A,M=h*v-p*A,C=d*v-p*g,P=m*C-y*M+_*R+w*S-b*T+x*E;if(!P)return null;P=1/P,e[0]=(a*C-u*M+l*R)*P,e[1]=(r*M-i*C-o*R)*P,e[2]=(A*x-g*b+v*w)*P,e[3]=(d*b-h*x-p*w)*P,e[4]=(u*S-s*C-l*T)*P,e[5]=(n*C-r*S+o*T)*P,e[6]=(g*_-f*x-v*y)*P,e[7]=(c*x-d*_+p*y)*P,e[8]=(s*M-a*S+l*E)*P,e[9]=(i*S-n*M-o*E)*P,e[10]=(f*b-A*_+v*m)*P,e[11]=(h*_-c*b-p*m)*P,e[12]=(a*T-s*R-u*E)*P,e[13]=(n*R-i*T+r*E)*P,e[14]=(A*y-f*w-g*m)*P,e[15]=(c*w-h*y+d*m)*P}(o,o)}return function(e,t,n){return!(!e||!t||(e.pose=t,e.timestamp=t.timestamp,r(e.leftProjectionMatrix,e.leftViewMatrix,t,n._getFieldOfView("left"),n._getEyeOffset("left"),n),r(e.rightProjectionMatrix,e.rightViewMatrix,t,n._getFieldOfView("right"),n._getEyeOffset("right"),n),0))}}(),x=function(e){var t,n=e.indexOf("://");t=-1!==n?n+3:0;var i=e.indexOf("/",t);return-1===i&&(i=e.length),e.substring(0,i)},E=(e={},function(t,n){void 0===e[t]&&(console.warn("webvr-polyfill: "+n),e[t]=!0)}),T=function(e,t){E(e,e+" has been deprecated. This may not work on native WebVR displays. "+(t?"Please use "+t+" instead.":""))},S=function(e,t,n){if(t){for(var i=[],r=null,o=0;o<t.length;++o)switch(s=t[o]){case e.TEXTURE_BINDING_2D:case e.TEXTURE_BINDING_CUBE_MAP:if((u=t[++o])<e.TEXTURE0||u>e.TEXTURE31){console.error("TEXTURE_BINDING_2D or TEXTURE_BINDING_CUBE_MAP must be followed by a valid texture unit"),i.push(null,null);break}r||(r=e.getParameter(e.ACTIVE_TEXTURE)),e.activeTexture(u),i.push(e.getParameter(s),null);break;case e.ACTIVE_TEXTURE:r=e.getParameter(e.ACTIVE_TEXTURE),i.push(null);break;default:i.push(e.getParameter(s))}for(n(e),o=0;o<t.length;++o){var s=t[o],a=i[o];switch(s){case e.ACTIVE_TEXTURE:break;case e.ARRAY_BUFFER_BINDING:e.bindBuffer(e.ARRAY_BUFFER,a);break;case e.COLOR_CLEAR_VALUE:e.clearColor(a[0],a[1],a[2],a[3]);break;case e.COLOR_WRITEMASK:e.colorMask(a[0],a[1],a[2],a[3]);break;case e.CURRENT_PROGRAM:e.useProgram(a);break;case e.ELEMENT_ARRAY_BUFFER_BINDING:e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,a);break;case e.FRAMEBUFFER_BINDING:e.bindFramebuffer(e.FRAMEBUFFER,a);break;case e.RENDERBUFFER_BINDING:e.bindRenderbuffer(e.RENDERBUFFER,a);break;case e.TEXTURE_BINDING_2D:if((u=t[++o])<e.TEXTURE0||u>e.TEXTURE31)break;e.activeTexture(u),e.bindTexture(e.TEXTURE_2D,a);break;case e.TEXTURE_BINDING_CUBE_MAP:var u;if((u=t[++o])<e.TEXTURE0||u>e.TEXTURE31)break;e.activeTexture(u),e.bindTexture(e.TEXTURE_CUBE_MAP,a);break;case e.VIEWPORT:e.viewport(a[0],a[1],a[2],a[3]);break;case e.BLEND:case e.CULL_FACE:case e.DEPTH_TEST:case e.SCISSOR_TEST:case e.STENCIL_TEST:a?e.enable(s):e.disable(s);break;default:console.log("No GL restore behavior for 0x"+s.toString(16))}r&&e.activeTexture(r)}}else n(e)},R=["attribute vec2 position;","attribute vec3 texCoord;","varying vec2 vTexCoord;","uniform vec4 viewportOffsetScale[2];","void main() {","  vec4 viewport = viewportOffsetScale[int(texCoord.z)];","  vTexCoord = (texCoord.xy * viewport.zw) + viewport.xy;","  gl_Position = vec4( position, 1.0, 1.0 );","}"].join("\n"),M=["precision mediump float;","uniform sampler2D diffuse;","varying vec2 vTexCoord;","void main() {","  gl_FragColor = texture2D(diffuse, vTexCoord);","}"].join("\n");function C(e,t,n,i){this.gl=e,this.cardboardUI=t,this.bufferScale=n,this.dirtySubmitFrameBindings=i,this.ctxAttribs=e.getContextAttributes(),this.instanceExt=e.getExtension("ANGLE_instanced_arrays"),this.meshWidth=20,this.meshHeight=20,this.bufferWidth=e.drawingBufferWidth,this.bufferHeight=e.drawingBufferHeight,this.realBindFramebuffer=e.bindFramebuffer,this.realEnable=e.enable,this.realDisable=e.disable,this.realColorMask=e.colorMask,this.realClearColor=e.clearColor,this.realViewport=e.viewport,o()||(this.realCanvasWidth=Object.getOwnPropertyDescriptor(e.canvas.__proto__,"width"),this.realCanvasHeight=Object.getOwnPropertyDescriptor(e.canvas.__proto__,"height")),this.isPatched=!1,this.lastBoundFramebuffer=null,this.cullFace=!1,this.depthTest=!1,this.blend=!1,this.scissorTest=!1,this.stencilTest=!1,this.viewport=[0,0,0,0],this.colorMask=[!0,!0,!0,!0],this.clearColor=[0,0,0,0],this.attribs={position:0,texCoord:1},this.program=v(e,R,M,this.attribs),this.uniforms=m(e,this.program),this.viewportOffsetScale=new Float32Array(8),this.setTextureBounds(),this.vertexBuffer=e.createBuffer(),this.indexBuffer=e.createBuffer(),this.indexCount=0,this.renderTarget=e.createTexture(),this.framebuffer=e.createFramebuffer(),this.depthStencilBuffer=null,this.depthBuffer=null,this.stencilBuffer=null,this.ctxAttribs.depth&&this.ctxAttribs.stencil?this.depthStencilBuffer=e.createRenderbuffer():this.ctxAttribs.depth?this.depthBuffer=e.createRenderbuffer():this.ctxAttribs.stencil&&(this.stencilBuffer=e.createRenderbuffer()),this.patch(),this.onResize()}C.prototype.destroy=function(){var e=this.gl;this.unpatch(),e.deleteProgram(this.program),e.deleteBuffer(this.vertexBuffer),e.deleteBuffer(this.indexBuffer),e.deleteTexture(this.renderTarget),e.deleteFramebuffer(this.framebuffer),this.depthStencilBuffer&&e.deleteRenderbuffer(this.depthStencilBuffer),this.depthBuffer&&e.deleteRenderbuffer(this.depthBuffer),this.stencilBuffer&&e.deleteRenderbuffer(this.stencilBuffer),this.cardboardUI&&this.cardboardUI.destroy()},C.prototype.onResize=function(){var e=this.gl,t=this,n=[e.RENDERBUFFER_BINDING,e.TEXTURE_BINDING_2D,e.TEXTURE0];S(e,n,(function(e){t.realBindFramebuffer.call(e,e.FRAMEBUFFER,null),t.scissorTest&&t.realDisable.call(e,e.SCISSOR_TEST),t.realColorMask.call(e,!0,!0,!0,!0),t.realViewport.call(e,0,0,e.drawingBufferWidth,e.drawingBufferHeight),t.realClearColor.call(e,0,0,0,1),e.clear(e.COLOR_BUFFER_BIT),t.realBindFramebuffer.call(e,e.FRAMEBUFFER,t.framebuffer),e.bindTexture(e.TEXTURE_2D,t.renderTarget),e.texImage2D(e.TEXTURE_2D,0,t.ctxAttribs.alpha?e.RGBA:e.RGB,t.bufferWidth,t.bufferHeight,0,t.ctxAttribs.alpha?e.RGBA:e.RGB,e.UNSIGNED_BYTE,null),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,t.renderTarget,0),t.ctxAttribs.depth&&t.ctxAttribs.stencil?(e.bindRenderbuffer(e.RENDERBUFFER,t.depthStencilBuffer),e.renderbufferStorage(e.RENDERBUFFER,e.DEPTH_STENCIL,t.bufferWidth,t.bufferHeight),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.DEPTH_STENCIL_ATTACHMENT,e.RENDERBUFFER,t.depthStencilBuffer)):t.ctxAttribs.depth?(e.bindRenderbuffer(e.RENDERBUFFER,t.depthBuffer),e.renderbufferStorage(e.RENDERBUFFER,e.DEPTH_COMPONENT16,t.bufferWidth,t.bufferHeight),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.DEPTH_ATTACHMENT,e.RENDERBUFFER,t.depthBuffer)):t.ctxAttribs.stencil&&(e.bindRenderbuffer(e.RENDERBUFFER,t.stencilBuffer),e.renderbufferStorage(e.RENDERBUFFER,e.STENCIL_INDEX8,t.bufferWidth,t.bufferHeight),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.STENCIL_ATTACHMENT,e.RENDERBUFFER,t.stencilBuffer)),!e.checkFramebufferStatus(e.FRAMEBUFFER)===e.FRAMEBUFFER_COMPLETE&&console.error("Framebuffer incomplete!"),t.realBindFramebuffer.call(e,e.FRAMEBUFFER,t.lastBoundFramebuffer),t.scissorTest&&t.realEnable.call(e,e.SCISSOR_TEST),t.realColorMask.apply(e,t.colorMask),t.realViewport.apply(e,t.viewport),t.realClearColor.apply(e,t.clearColor)})),this.cardboardUI&&this.cardboardUI.onResize()},C.prototype.patch=function(){if(!this.isPatched){var e=this,t=this.gl.canvas,n=this.gl;o()||(t.width=f()*this.bufferScale,t.height=A()*this.bufferScale,Object.defineProperty(t,"width",{configurable:!0,enumerable:!0,get:function(){return e.bufferWidth},set:function(n){e.bufferWidth=n,e.realCanvasWidth.set.call(t,n),e.onResize()}}),Object.defineProperty(t,"height",{configurable:!0,enumerable:!0,get:function(){return e.bufferHeight},set:function(n){e.bufferHeight=n,e.realCanvasHeight.set.call(t,n),e.onResize()}})),this.lastBoundFramebuffer=n.getParameter(n.FRAMEBUFFER_BINDING),null==this.lastBoundFramebuffer&&(this.lastBoundFramebuffer=this.framebuffer,this.gl.bindFramebuffer(n.FRAMEBUFFER,this.framebuffer)),this.gl.bindFramebuffer=function(t,i){e.lastBoundFramebuffer=i||e.framebuffer,e.realBindFramebuffer.call(n,t,e.lastBoundFramebuffer)},this.cullFace=n.getParameter(n.CULL_FACE),this.depthTest=n.getParameter(n.DEPTH_TEST),this.blend=n.getParameter(n.BLEND),this.scissorTest=n.getParameter(n.SCISSOR_TEST),this.stencilTest=n.getParameter(n.STENCIL_TEST),n.enable=function(t){switch(t){case n.CULL_FACE:e.cullFace=!0;break;case n.DEPTH_TEST:e.depthTest=!0;break;case n.BLEND:e.blend=!0;break;case n.SCISSOR_TEST:e.scissorTest=!0;break;case n.STENCIL_TEST:e.stencilTest=!0}e.realEnable.call(n,t)},n.disable=function(t){switch(t){case n.CULL_FACE:e.cullFace=!1;break;case n.DEPTH_TEST:e.depthTest=!1;break;case n.BLEND:e.blend=!1;break;case n.SCISSOR_TEST:e.scissorTest=!1;break;case n.STENCIL_TEST:e.stencilTest=!1}e.realDisable.call(n,t)},this.colorMask=n.getParameter(n.COLOR_WRITEMASK),n.colorMask=function(t,i,r,o){e.colorMask[0]=t,e.colorMask[1]=i,e.colorMask[2]=r,e.colorMask[3]=o,e.realColorMask.call(n,t,i,r,o)},this.clearColor=n.getParameter(n.COLOR_CLEAR_VALUE),n.clearColor=function(t,i,r,o){e.clearColor[0]=t,e.clearColor[1]=i,e.clearColor[2]=r,e.clearColor[3]=o,e.realClearColor.call(n,t,i,r,o)},this.viewport=n.getParameter(n.VIEWPORT),n.viewport=function(t,i,r,o){e.viewport[0]=t,e.viewport[1]=i,e.viewport[2]=r,e.viewport[3]=o,e.realViewport.call(n,t,i,r,o)},this.isPatched=!0,w(t)}},C.prototype.unpatch=function(){if(this.isPatched){var e=this.gl,t=this.gl.canvas;o()||(Object.defineProperty(t,"width",this.realCanvasWidth),Object.defineProperty(t,"height",this.realCanvasHeight)),t.width=this.bufferWidth,t.height=this.bufferHeight,e.bindFramebuffer=this.realBindFramebuffer,e.enable=this.realEnable,e.disable=this.realDisable,e.colorMask=this.realColorMask,e.clearColor=this.realClearColor,e.viewport=this.realViewport,this.lastBoundFramebuffer==this.framebuffer&&e.bindFramebuffer(e.FRAMEBUFFER,null),this.isPatched=!1,setTimeout((function(){w(t)}),1)}},C.prototype.setTextureBounds=function(e,t){e||(e=[0,0,.5,1]),t||(t=[.5,0,.5,1]),this.viewportOffsetScale[0]=e[0],this.viewportOffsetScale[1]=e[1],this.viewportOffsetScale[2]=e[2],this.viewportOffsetScale[3]=e[3],this.viewportOffsetScale[4]=t[0],this.viewportOffsetScale[5]=t[1],this.viewportOffsetScale[6]=t[2],this.viewportOffsetScale[7]=t[3]},C.prototype.submitFrame=function(){var e=this.gl,t=this,n=[];if(this.dirtySubmitFrameBindings||n.push(e.CURRENT_PROGRAM,e.ARRAY_BUFFER_BINDING,e.ELEMENT_ARRAY_BUFFER_BINDING,e.TEXTURE_BINDING_2D,e.TEXTURE0),S(e,n,(function(e){t.realBindFramebuffer.call(e,e.FRAMEBUFFER,null);var n=0,i=0;t.instanceExt&&(n=e.getVertexAttrib(t.attribs.position,t.instanceExt.VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE),i=e.getVertexAttrib(t.attribs.texCoord,t.instanceExt.VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE)),t.cullFace&&t.realDisable.call(e,e.CULL_FACE),t.depthTest&&t.realDisable.call(e,e.DEPTH_TEST),t.blend&&t.realDisable.call(e,e.BLEND),t.scissorTest&&t.realDisable.call(e,e.SCISSOR_TEST),t.stencilTest&&t.realDisable.call(e,e.STENCIL_TEST),t.realColorMask.call(e,!0,!0,!0,!0),t.realViewport.call(e,0,0,e.drawingBufferWidth,e.drawingBufferHeight),(t.ctxAttribs.alpha||o())&&(t.realClearColor.call(e,0,0,0,1),e.clear(e.COLOR_BUFFER_BIT)),e.useProgram(t.program),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,t.indexBuffer),e.bindBuffer(e.ARRAY_BUFFER,t.vertexBuffer),e.enableVertexAttribArray(t.attribs.position),e.enableVertexAttribArray(t.attribs.texCoord),e.vertexAttribPointer(t.attribs.position,2,e.FLOAT,!1,20,0),e.vertexAttribPointer(t.attribs.texCoord,3,e.FLOAT,!1,20,8),t.instanceExt&&(0!=n&&t.instanceExt.vertexAttribDivisorANGLE(t.attribs.position,0),0!=i&&t.instanceExt.vertexAttribDivisorANGLE(t.attribs.texCoord,0)),e.activeTexture(e.TEXTURE0),e.uniform1i(t.uniforms.diffuse,0),e.bindTexture(e.TEXTURE_2D,t.renderTarget),e.uniform4fv(t.uniforms.viewportOffsetScale,t.viewportOffsetScale),e.drawElements(e.TRIANGLES,t.indexCount,e.UNSIGNED_SHORT,0),t.cardboardUI&&t.cardboardUI.renderNoState(),t.realBindFramebuffer.call(t.gl,e.FRAMEBUFFER,t.framebuffer),t.ctxAttribs.preserveDrawingBuffer||(t.realClearColor.call(e,0,0,0,0),e.clear(e.COLOR_BUFFER_BIT)),t.dirtySubmitFrameBindings||t.realBindFramebuffer.call(e,e.FRAMEBUFFER,t.lastBoundFramebuffer),t.cullFace&&t.realEnable.call(e,e.CULL_FACE),t.depthTest&&t.realEnable.call(e,e.DEPTH_TEST),t.blend&&t.realEnable.call(e,e.BLEND),t.scissorTest&&t.realEnable.call(e,e.SCISSOR_TEST),t.stencilTest&&t.realEnable.call(e,e.STENCIL_TEST),t.realColorMask.apply(e,t.colorMask),t.realViewport.apply(e,t.viewport),!t.ctxAttribs.alpha&&t.ctxAttribs.preserveDrawingBuffer||t.realClearColor.apply(e,t.clearColor),t.instanceExt&&(0!=n&&t.instanceExt.vertexAttribDivisorANGLE(t.attribs.position,n),0!=i&&t.instanceExt.vertexAttribDivisorANGLE(t.attribs.texCoord,i))})),o()){var i=e.canvas;i.width==t.bufferWidth&&i.height==t.bufferHeight||(t.bufferWidth=i.width,t.bufferHeight=i.height,t.onResize())}},C.prototype.updateDeviceInfo=function(e){var t=this.gl,n=this,i=[t.ARRAY_BUFFER_BINDING,t.ELEMENT_ARRAY_BUFFER_BINDING];S(t,i,(function(t){var i=n.computeMeshVertices_(n.meshWidth,n.meshHeight,e);if(t.bindBuffer(t.ARRAY_BUFFER,n.vertexBuffer),t.bufferData(t.ARRAY_BUFFER,i,t.STATIC_DRAW),!n.indexCount){var r=n.computeMeshIndices_(n.meshWidth,n.meshHeight);t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,n.indexBuffer),t.bufferData(t.ELEMENT_ARRAY_BUFFER,r,t.STATIC_DRAW),n.indexCount=r.length}}))},C.prototype.computeMeshVertices_=function(e,t,n){for(var i=new Float32Array(2*e*t*5),o=n.getLeftEyeVisibleTanAngles(),s=n.getLeftEyeNoLensTanAngles(),a=n.getLeftEyeVisibleScreenRect(s),u=0,l=0;l<2;l++){for(var c=0;c<t;c++)for(var h=0;h<e;h++,u++){var d=h/(e-1),p=c/(t-1),f=d,A=p,g=r(o[0],o[2],d),v=r(o[3],o[1],p),m=Math.sqrt(g*g+v*v),y=n.distortion.distortInverse(m),_=v*y/m;d=(g*y/m-s[0])/(s[2]-s[0]),p=(_-s[3])/(s[1]-s[3]),d=2*(a.x+d*a.width-.5),p=2*(a.y+p*a.height-.5),i[5*u+0]=d,i[5*u+1]=p,i[5*u+2]=f,i[5*u+3]=A,i[5*u+4]=l}var w=o[2]-o[0];o[0]=-(w+o[0]),o[2]=w-o[2],w=s[2]-s[0],s[0]=-(w+s[0]),s[2]=w-s[2],a.x=1-(a.x+a.width)}return i},C.prototype.computeMeshIndices_=function(e,t){for(var n=new Uint16Array(2*(e-1)*(t-1)*6),i=e/2,r=t/2,o=0,s=0,a=0;a<2;a++)for(var u=0;u<t;u++)for(var l=0;l<e;l++,o++)0!=l&&0!=u&&(l<=i==u<=r?(n[s++]=o,n[s++]=o-e-1,n[s++]=o-e,n[s++]=o-e-1,n[s++]=o,n[s++]=o-1):(n[s++]=o-1,n[s++]=o-e,n[s++]=o,n[s++]=o-e,n[s++]=o-1,n[s++]=o-e-1));return n},C.prototype.getOwnPropertyDescriptor_=function(e,t){var n=Object.getOwnPropertyDescriptor(e,t);return void 0!==n.get&&void 0!==n.set||(n.configurable=!0,n.enumerable=!0,n.get=function(){return this.getAttribute(t)},n.set=function(e){this.setAttribute(t,e)}),n};var P=["attribute vec2 position;","uniform mat4 projectionMat;","void main() {","  gl_Position = projectionMat * vec4( position, -1.0, 1.0 );","}"].join("\n"),F=["precision mediump float;","uniform vec4 color;","void main() {","  gl_FragColor = color;","}"].join("\n"),D=Math.PI/180,I=.3125;function O(e){this.gl=e,this.attribs={position:0},this.program=v(e,P,F,this.attribs),this.uniforms=m(e,this.program),this.vertexBuffer=e.createBuffer(),this.gearOffset=0,this.gearVertexCount=0,this.arrowOffset=0,this.arrowVertexCount=0,this.projMat=new Float32Array(16),this.listener=null,this.onResize()}function B(e){this.coefficients=e}O.prototype.destroy=function(){var e=this.gl;this.listener&&e.canvas.removeEventListener("click",this.listener,!1),e.deleteProgram(this.program),e.deleteBuffer(this.vertexBuffer)},O.prototype.listen=function(e,t){var n=this.gl.canvas;this.listener=function(i){var r=n.clientWidth/2;i.clientX>r-42&&i.clientX<r+42&&i.clientY>n.clientHeight-42?e(i):i.clientX<42&&i.clientY<42&&t(i)},n.addEventListener("click",this.listener,!1)},O.prototype.onResize=function(){var e=this.gl,t=this,n=[e.ARRAY_BUFFER_BINDING];S(e,n,(function(e){var n=[],i=e.drawingBufferWidth/2,r=Math.max(screen.width,screen.height)*window.devicePixelRatio,o=e.drawingBufferWidth/r*window.devicePixelRatio,s=4*o/2,a=42*o,u=28*o/2,l=14*o;function c(e,t){var r=(90-e)*D,o=Math.cos(r),s=Math.sin(r);n.push(I*o*u+i,I*s*u+u),n.push(t*o*u+i,t*s*u+u)}n.push(i-s,a),n.push(i-s,e.drawingBufferHeight),n.push(i+s,a),n.push(i+s,e.drawingBufferHeight),t.gearOffset=n.length/2;for(var h=0;h<=6;h++){var d=60*h;c(d,1),c(d+12,1),c(d+20,.75),c(d+40,.75),c(d+48,1)}function p(t,i){n.push(l+t,e.drawingBufferHeight-l-i)}t.gearVertexCount=n.length/2-t.gearOffset,t.arrowOffset=n.length/2;var f=s/Math.sin(45*D);p(0,u),p(u,0),p(u+f,f),p(f,u+f),p(f,u-f),p(0,u),p(u,2*u),p(u+f,2*u-f),p(f,u-f),p(0,u),p(f,u-s),p(28*o,u-s),p(f,u+s),p(28*o,u+s),t.arrowVertexCount=n.length/2-t.arrowOffset,e.bindBuffer(e.ARRAY_BUFFER,t.vertexBuffer),e.bufferData(e.ARRAY_BUFFER,new Float32Array(n),e.STATIC_DRAW)}))},O.prototype.render=function(){var e=this.gl,t=this,n=[e.CULL_FACE,e.DEPTH_TEST,e.BLEND,e.SCISSOR_TEST,e.STENCIL_TEST,e.COLOR_WRITEMASK,e.VIEWPORT,e.CURRENT_PROGRAM,e.ARRAY_BUFFER_BINDING];S(e,n,(function(e){e.disable(e.CULL_FACE),e.disable(e.DEPTH_TEST),e.disable(e.BLEND),e.disable(e.SCISSOR_TEST),e.disable(e.STENCIL_TEST),e.colorMask(!0,!0,!0,!0),e.viewport(0,0,e.drawingBufferWidth,e.drawingBufferHeight),t.renderNoState()}))},O.prototype.renderNoState=function(){var e,t,n,i,r,o,s=this.gl;s.useProgram(this.program),s.bindBuffer(s.ARRAY_BUFFER,this.vertexBuffer),s.enableVertexAttribArray(this.attribs.position),s.vertexAttribPointer(this.attribs.position,2,s.FLOAT,!1,8,0),s.uniform4f(this.uniforms.color,1,1,1,1),e=this.projMat,i=1/(0-(t=s.drawingBufferWidth)),r=1/(0-(n=s.drawingBufferHeight)),o=1/(.1-1024),e[0]=-2*i,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=-2*r,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=2*o,e[11]=0,e[12]=(0+t)*i,e[13]=(n+0)*r,e[14]=1024.1*o,e[15]=1,s.uniformMatrix4fv(this.uniforms.projectionMat,!1,this.projMat),s.drawArrays(s.TRIANGLE_STRIP,0,4),s.drawArrays(s.TRIANGLE_STRIP,this.gearOffset,this.gearVertexCount),s.drawArrays(s.TRIANGLE_STRIP,this.arrowOffset,this.arrowVertexCount)},B.prototype.distortInverse=function(e){for(var t=0,n=1,i=e-this.distort(t);Math.abs(n-t)>1e-4;){var r=e-this.distort(n),o=n-r*((n-t)/(r-i));t=n,n=o,i=r}return n},B.prototype.distort=function(e){for(var t=e*e,n=0,i=0;i<this.coefficients.length;i++)n=t*(n+this.coefficients[i]);return(n+1)*e};var L=Math.PI/180,V=180/Math.PI,k=function(e,t,n){this.x=e||0,this.y=t||0,this.z=n||0};k.prototype={constructor:k,set:function(e,t,n){return this.x=e,this.y=t,this.z=n,this},copy:function(e){return this.x=e.x,this.y=e.y,this.z=e.z,this},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)},normalize:function(){var e=this.length();if(0!==e){var t=1/e;this.multiplyScalar(t)}else this.x=0,this.y=0,this.z=0;return this},multiplyScalar:function(e){this.x*=e,this.y*=e,this.z*=e},applyQuaternion:function(e){var t=this.x,n=this.y,i=this.z,r=e.x,o=e.y,s=e.z,a=e.w,u=a*t+o*i-s*n,l=a*n+s*t-r*i,c=a*i+r*n-o*t,h=-r*t-o*n-s*i;return this.x=u*a+h*-r+l*-s-c*-o,this.y=l*a+h*-o+c*-r-u*-s,this.z=c*a+h*-s+u*-o-l*-r,this},dot:function(e){return this.x*e.x+this.y*e.y+this.z*e.z},crossVectors:function(e,t){var n=e.x,i=e.y,r=e.z,o=t.x,s=t.y,a=t.z;return this.x=i*a-r*s,this.y=r*o-n*a,this.z=n*s-i*o,this}};var N,Q,U=function(e,t,n,i){this.x=e||0,this.y=t||0,this.z=n||0,this.w=void 0!==i?i:1};function W(e){this.width=e.width||f(),this.height=e.height||A(),this.widthMeters=e.widthMeters,this.heightMeters=e.heightMeters,this.bevelMeters=e.bevelMeters}U.prototype={constructor:U,set:function(e,t,n,i){return this.x=e,this.y=t,this.z=n,this.w=i,this},copy:function(e){return this.x=e.x,this.y=e.y,this.z=e.z,this.w=e.w,this},setFromEulerXYZ:function(e,t,n){var i=Math.cos(e/2),r=Math.cos(t/2),o=Math.cos(n/2),s=Math.sin(e/2),a=Math.sin(t/2),u=Math.sin(n/2);return this.x=s*r*o+i*a*u,this.y=i*a*o-s*r*u,this.z=i*r*u+s*a*o,this.w=i*r*o-s*a*u,this},setFromEulerYXZ:function(e,t,n){var i=Math.cos(e/2),r=Math.cos(t/2),o=Math.cos(n/2),s=Math.sin(e/2),a=Math.sin(t/2),u=Math.sin(n/2);return this.x=s*r*o+i*a*u,this.y=i*a*o-s*r*u,this.z=i*r*u-s*a*o,this.w=i*r*o+s*a*u,this},setFromAxisAngle:function(e,t){var n=t/2,i=Math.sin(n);return this.x=e.x*i,this.y=e.y*i,this.z=e.z*i,this.w=Math.cos(n),this},multiply:function(e){return this.multiplyQuaternions(this,e)},multiplyQuaternions:function(e,t){var n=e.x,i=e.y,r=e.z,o=e.w,s=t.x,a=t.y,u=t.z,l=t.w;return this.x=n*l+o*s+i*u-r*a,this.y=i*l+o*a+r*s-n*u,this.z=r*l+o*u+n*a-i*s,this.w=o*l-n*s-i*a-r*u,this},inverse:function(){return this.x*=-1,this.y*=-1,this.z*=-1,this.normalize(),this},normalize:function(){var e=Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w);return 0===e?(this.x=0,this.y=0,this.z=0,this.w=1):(e=1/e,this.x=this.x*e,this.y=this.y*e,this.z=this.z*e,this.w=this.w*e),this},slerp:function(e,t){if(0===t)return this;if(1===t)return this.copy(e);var n=this.x,i=this.y,r=this.z,o=this.w,s=o*e.w+n*e.x+i*e.y+r*e.z;if(s<0?(this.w=-e.w,this.x=-e.x,this.y=-e.y,this.z=-e.z,s=-s):this.copy(e),s>=1)return this.w=o,this.x=n,this.y=i,this.z=r,this;var a=Math.acos(s),u=Math.sqrt(1-s*s);if(Math.abs(u)<.001)return this.w=.5*(o+this.w),this.x=.5*(n+this.x),this.y=.5*(i+this.y),this.z=.5*(r+this.z),this;var l=Math.sin((1-t)*a)/u,c=Math.sin(t*a)/u;return this.w=o*l+this.w*c,this.x=n*l+this.x*c,this.y=i*l+this.y*c,this.z=r*l+this.z*c,this},setFromUnitVectors:function(e,t){return void 0===N&&(N=new k),(Q=e.dot(t)+1)<1e-6?(Q=0,Math.abs(e.x)>Math.abs(e.z)?N.set(-e.y,e.x,0):N.set(0,-e.z,e.y)):N.crossVectors(e,t),this.x=N.x,this.y=N.y,this.z=N.z,this.w=Q,this.normalize(),this}};var G=new W({widthMeters:.11,heightMeters:.062,bevelMeters:.004}),j=new W({widthMeters:.1038,heightMeters:.0584,bevelMeters:.004}),z={CardboardV1:new H({id:"CardboardV1",label:"Cardboard I/O 2014",fov:40,interLensDistance:.06,baselineLensDistance:.035,screenLensDistance:.042,distortionCoefficients:[.441,.156],inverseCoefficients:[-.4410035,.42756155,-.4804439,.5460139,-.58821183,.5733938,-.48303202,.33299083,-.17573841,.0651772,-.01488963,.001559834]}),CardboardV2:new H({id:"CardboardV2",label:"Cardboard I/O 2015",fov:60,interLensDistance:.064,baselineLensDistance:.035,screenLensDistance:.039,distortionCoefficients:[.34,.55],inverseCoefficients:[-.33836704,-.18162185,.862655,-1.2462051,1.0560602,-.58208317,.21609078,-.05444823,.009177956,-.0009904169,6183535e-11,-16981803e-13]})};function X(e,t){this.viewer=z.CardboardV2,this.updateDeviceParams(e),this.distortion=new B(this.viewer.distortionCoefficients);for(var n=0;n<t.length;n++){var i=t[n];z[i.id]=new H(i)}}function H(e){this.id=e.id,this.label=e.label,this.fov=e.fov,this.interLensDistance=e.interLensDistance,this.baselineLensDistance=e.baselineLensDistance,this.screenLensDistance=e.screenLensDistance,this.distortionCoefficients=e.distortionCoefficients,this.inverseCoefficients=e.inverseCoefficients}X.prototype.updateDeviceParams=function(e){this.device=this.determineDevice_(e)||this.device},X.prototype.getDevice=function(){return this.device},X.prototype.setViewer=function(e){this.viewer=e,this.distortion=new B(this.viewer.distortionCoefficients)},X.prototype.determineDevice_=function(e){if(!e)return o()?(console.warn("Using fallback iOS device measurements."),j):(console.warn("Using fallback Android device measurements."),G);var t=.0254/e.xdpi,n=.0254/e.ydpi;return new W({widthMeters:t*f(),heightMeters:n*A(),bevelMeters:.001*e.bevelMm})},X.prototype.getDistortedFieldOfViewLeftEye=function(){var e=this.viewer,t=this.device,n=this.distortion,i=e.screenLensDistance,r=(t.widthMeters-e.interLensDistance)/2,o=e.interLensDistance/2,s=e.baselineLensDistance-t.bevelMeters,a=t.heightMeters-s,u=V*Math.atan(n.distort(r/i)),l=V*Math.atan(n.distort(o/i)),c=V*Math.atan(n.distort(s/i)),h=V*Math.atan(n.distort(a/i));return{leftDegrees:Math.min(u,e.fov),rightDegrees:Math.min(l,e.fov),downDegrees:Math.min(c,e.fov),upDegrees:Math.min(h,e.fov)}},X.prototype.getLeftEyeVisibleTanAngles=function(){var e=this.viewer,t=this.device,n=this.distortion,i=Math.tan(-L*e.fov),r=Math.tan(L*e.fov),o=Math.tan(L*e.fov),s=Math.tan(-L*e.fov),a=t.widthMeters/4,u=t.heightMeters/2,l=e.baselineLensDistance-t.bevelMeters-u,c=e.interLensDistance/2-a,h=-l,d=e.screenLensDistance,p=n.distort((c-a)/d),f=n.distort((h+u)/d),A=n.distort((c+a)/d),g=n.distort((h-u)/d),v=new Float32Array(4);return v[0]=Math.max(i,p),v[1]=Math.min(r,f),v[2]=Math.min(o,A),v[3]=Math.max(s,g),v},X.prototype.getLeftEyeNoLensTanAngles=function(){var e=this.viewer,t=this.device,n=this.distortion,i=new Float32Array(4),r=n.distortInverse(Math.tan(-L*e.fov)),o=n.distortInverse(Math.tan(L*e.fov)),s=n.distortInverse(Math.tan(L*e.fov)),a=n.distortInverse(Math.tan(-L*e.fov)),u=t.widthMeters/4,l=t.heightMeters/2,c=e.baselineLensDistance-t.bevelMeters-l,h=e.interLensDistance/2-u,d=-c,p=e.screenLensDistance,f=(h-u)/p,A=(d+l)/p,g=(h+u)/p,v=(d-l)/p;return i[0]=Math.max(r,f),i[1]=Math.min(o,A),i[2]=Math.min(s,g),i[3]=Math.max(a,v),i},X.prototype.getLeftEyeVisibleScreenRect=function(e){var t=this.viewer,n=this.device,i=t.screenLensDistance,r=(n.widthMeters-t.interLensDistance)/2,o=t.baselineLensDistance-n.bevelMeters,s=(e[0]*i+r)/n.widthMeters,a=(e[1]*i+o)/n.heightMeters,u=(e[2]*i+r)/n.widthMeters,l=(e[3]*i+o)/n.heightMeters;return{x:s,y:l,width:u-s,height:a-l}},X.prototype.getFieldOfViewLeftEye=function(e){return e?this.getUndistortedFieldOfViewLeftEye():this.getDistortedFieldOfViewLeftEye()},X.prototype.getFieldOfViewRightEye=function(e){var t=this.getFieldOfViewLeftEye(e);return{leftDegrees:t.rightDegrees,rightDegrees:t.leftDegrees,upDegrees:t.upDegrees,downDegrees:t.downDegrees}},X.prototype.getUndistortedFieldOfViewLeftEye=function(){var e=this.getUndistortedParams_();return{leftDegrees:V*Math.atan(e.outerDist),rightDegrees:V*Math.atan(e.innerDist),downDegrees:V*Math.atan(e.bottomDist),upDegrees:V*Math.atan(e.topDist)}},X.prototype.getUndistortedViewportLeftEye=function(){var e=this.getUndistortedParams_(),t=this.viewer,n=this.device,i=t.screenLensDistance,r=n.widthMeters/i,o=n.heightMeters/i,s=n.width/r,a=n.height/o,u=Math.round((e.eyePosX-e.outerDist)*s),l=Math.round((e.eyePosY-e.bottomDist)*a);return{x:u,y:l,width:Math.round((e.eyePosX+e.innerDist)*s)-u,height:Math.round((e.eyePosY+e.topDist)*a)-l}},X.prototype.getUndistortedParams_=function(){var e=this.viewer,t=this.device,n=this.distortion,i=e.screenLensDistance,r=e.interLensDistance/2/i,o=t.widthMeters/i,s=t.heightMeters/i,a=o/2-r,u=(e.baselineLensDistance-t.bevelMeters)/i,l=e.fov,c=n.distortInverse(Math.tan(L*l)),h=Math.min(a,c),d=Math.min(r,c),p=Math.min(u,c);return{outerDist:h,innerDist:d,topDist:Math.min(s-u,c),bottomDist:p,eyePosX:a,eyePosY:u}},X.Viewers=z;var q={format:1,last_updated:"2019-11-09T17:36:14Z",devices:[{type:"android",rules:[{mdmh:"asus/*/Nexus 7/*"},{ua:"Nexus 7"}],dpi:[320.8,323],bw:3,ac:500},{type:"android",rules:[{mdmh:"asus/*/ASUS_X00PD/*"},{ua:"ASUS_X00PD"}],dpi:245,bw:3,ac:500},{type:"android",rules:[{mdmh:"asus/*/ASUS_X008D/*"},{ua:"ASUS_X008D"}],dpi:282,bw:3,ac:500},{type:"android",rules:[{mdmh:"asus/*/ASUS_Z00AD/*"},{ua:"ASUS_Z00AD"}],dpi:[403,404.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Google/*/Pixel 2 XL/*"},{ua:"Pixel 2 XL"}],dpi:537.9,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Google/*/Pixel 3 XL/*"},{ua:"Pixel 3 XL"}],dpi:[558.5,553.8],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Google/*/Pixel XL/*"},{ua:"Pixel XL"}],dpi:[537.9,533],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Google/*/Pixel 3/*"},{ua:"Pixel 3"}],dpi:442.4,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Google/*/Pixel 2/*"},{ua:"Pixel 2"}],dpi:441,bw:3,ac:500},{type:"android",rules:[{mdmh:"Google/*/Pixel/*"},{ua:"Pixel"}],dpi:[432.6,436.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"HTC/*/HTC6435LVW/*"},{ua:"HTC6435LVW"}],dpi:[449.7,443.3],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"HTC/*/HTC One XL/*"},{ua:"HTC One XL"}],dpi:[315.3,314.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"htc/*/Nexus 9/*"},{ua:"Nexus 9"}],dpi:289,bw:3,ac:500},{type:"android",rules:[{mdmh:"HTC/*/HTC One M9/*"},{ua:"HTC One M9"}],dpi:[442.5,443.3],bw:3,ac:500},{type:"android",rules:[{mdmh:"HTC/*/HTC One_M8/*"},{ua:"HTC One_M8"}],dpi:[449.7,447.4],bw:3,ac:500},{type:"android",rules:[{mdmh:"HTC/*/HTC One/*"},{ua:"HTC One"}],dpi:472.8,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Huawei/*/Nexus 6P/*"},{ua:"Nexus 6P"}],dpi:[515.1,518],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Huawei/*/BLN-L24/*"},{ua:"HONORBLN-L24"}],dpi:480,bw:4,ac:500},{type:"android",rules:[{mdmh:"Huawei/*/BKL-L09/*"},{ua:"BKL-L09"}],dpi:403,bw:3.47,ac:500},{type:"android",rules:[{mdmh:"LENOVO/*/Lenovo PB2-690Y/*"},{ua:"Lenovo PB2-690Y"}],dpi:[457.2,454.713],bw:3,ac:500},{type:"android",rules:[{mdmh:"LGE/*/Nexus 5X/*"},{ua:"Nexus 5X"}],dpi:[422,419.9],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"LGE/*/LGMS345/*"},{ua:"LGMS345"}],dpi:[221.7,219.1],bw:3,ac:500},{type:"android",rules:[{mdmh:"LGE/*/LG-D800/*"},{ua:"LG-D800"}],dpi:[422,424.1],bw:3,ac:500},{type:"android",rules:[{mdmh:"LGE/*/LG-D850/*"},{ua:"LG-D850"}],dpi:[537.9,541.9],bw:3,ac:500},{type:"android",rules:[{mdmh:"LGE/*/VS985 4G/*"},{ua:"VS985 4G"}],dpi:[537.9,535.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"LGE/*/Nexus 5/*"},{ua:"Nexus 5 B"}],dpi:[442.4,444.8],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"LGE/*/Nexus 4/*"},{ua:"Nexus 4"}],dpi:[319.8,318.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"LGE/*/LG-P769/*"},{ua:"LG-P769"}],dpi:[240.6,247.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"LGE/*/LGMS323/*"},{ua:"LGMS323"}],dpi:[206.6,204.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"LGE/*/LGLS996/*"},{ua:"LGLS996"}],dpi:[403.4,401.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Micromax/*/4560MMX/*"},{ua:"4560MMX"}],dpi:[240,219.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Micromax/*/A250/*"},{ua:"Micromax A250"}],dpi:[480,446.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Micromax/*/Micromax AQ4501/*"},{ua:"Micromax AQ4501"}],dpi:240,bw:3,ac:500},{type:"android",rules:[{mdmh:"motorola/*/G5/*"},{ua:"Moto G (5) Plus"}],dpi:[403.4,403],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/DROID RAZR/*"},{ua:"DROID RAZR"}],dpi:[368.1,256.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/XT830C/*"},{ua:"XT830C"}],dpi:[254,255.9],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/XT1021/*"},{ua:"XT1021"}],dpi:[254,256.7],bw:3,ac:500},{type:"android",rules:[{mdmh:"motorola/*/XT1023/*"},{ua:"XT1023"}],dpi:[254,256.7],bw:3,ac:500},{type:"android",rules:[{mdmh:"motorola/*/XT1028/*"},{ua:"XT1028"}],dpi:[326.6,327.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/XT1034/*"},{ua:"XT1034"}],dpi:[326.6,328.4],bw:3,ac:500},{type:"android",rules:[{mdmh:"motorola/*/XT1053/*"},{ua:"XT1053"}],dpi:[315.3,316.1],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/XT1562/*"},{ua:"XT1562"}],dpi:[403.4,402.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/Nexus 6/*"},{ua:"Nexus 6 B"}],dpi:[494.3,489.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/XT1063/*"},{ua:"XT1063"}],dpi:[295,296.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/XT1064/*"},{ua:"XT1064"}],dpi:[295,295.6],bw:3,ac:500},{type:"android",rules:[{mdmh:"motorola/*/XT1092/*"},{ua:"XT1092"}],dpi:[422,424.1],bw:3,ac:500},{type:"android",rules:[{mdmh:"motorola/*/XT1095/*"},{ua:"XT1095"}],dpi:[422,423.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"motorola/*/G4/*"},{ua:"Moto G (4)"}],dpi:401,bw:4,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/A0001/*"},{ua:"A0001"}],dpi:[403.4,401],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE E1001/*"},{ua:"ONE E1001"}],dpi:[442.4,441.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE E1003/*"},{ua:"ONE E1003"}],dpi:[442.4,441.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE E1005/*"},{ua:"ONE E1005"}],dpi:[442.4,441.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE A2001/*"},{ua:"ONE A2001"}],dpi:[391.9,405.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE A2003/*"},{ua:"ONE A2003"}],dpi:[391.9,405.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE A2005/*"},{ua:"ONE A2005"}],dpi:[391.9,405.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A3000/*"},{ua:"ONEPLUS A3000"}],dpi:401,bw:3,ac:500},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A3003/*"},{ua:"ONEPLUS A3003"}],dpi:401,bw:3,ac:500},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A3010/*"},{ua:"ONEPLUS A3010"}],dpi:401,bw:3,ac:500},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A5000/*"},{ua:"ONEPLUS A5000 "}],dpi:[403.411,399.737],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONE A5010/*"},{ua:"ONEPLUS A5010"}],dpi:[403,400],bw:2,ac:1e3},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A6000/*"},{ua:"ONEPLUS A6000"}],dpi:401,bw:3,ac:500},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A6003/*"},{ua:"ONEPLUS A6003"}],dpi:401,bw:3,ac:500},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A6010/*"},{ua:"ONEPLUS A6010"}],dpi:401,bw:2,ac:500},{type:"android",rules:[{mdmh:"OnePlus/*/ONEPLUS A6013/*"},{ua:"ONEPLUS A6013"}],dpi:401,bw:2,ac:500},{type:"android",rules:[{mdmh:"OPPO/*/X909/*"},{ua:"X909"}],dpi:[442.4,444.1],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-I9082/*"},{ua:"GT-I9082"}],dpi:[184.7,185.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G360P/*"},{ua:"SM-G360P"}],dpi:[196.7,205.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/Nexus S/*"},{ua:"Nexus S"}],dpi:[234.5,229.8],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-I9300/*"},{ua:"GT-I9300"}],dpi:[304.8,303.9],bw:5,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-T230NU/*"},{ua:"SM-T230NU"}],dpi:216,bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SGH-T399/*"},{ua:"SGH-T399"}],dpi:[217.7,231.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SGH-M919/*"},{ua:"SGH-M919"}],dpi:[440.8,437.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-N9005/*"},{ua:"SM-N9005"}],dpi:[386.4,387],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SAMSUNG-SM-N900A/*"},{ua:"SAMSUNG-SM-N900A"}],dpi:[386.4,387.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-I9500/*"},{ua:"GT-I9500"}],dpi:[442.5,443.3],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/GT-I9505/*"},{ua:"GT-I9505"}],dpi:439.4,bw:4,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G900F/*"},{ua:"SM-G900F"}],dpi:[415.6,431.6],bw:5,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G900M/*"},{ua:"SM-G900M"}],dpi:[415.6,431.6],bw:5,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G800F/*"},{ua:"SM-G800F"}],dpi:326.8,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G906S/*"},{ua:"SM-G906S"}],dpi:[562.7,572.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-I9300/*"},{ua:"GT-I9300"}],dpi:[306.7,304.8],bw:5,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-T535/*"},{ua:"SM-T535"}],dpi:[142.6,136.4],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-N920C/*"},{ua:"SM-N920C"}],dpi:[515.1,518.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-N920P/*"},{ua:"SM-N920P"}],dpi:[386.3655,390.144],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-N920W8/*"},{ua:"SM-N920W8"}],dpi:[515.1,518.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-I9300I/*"},{ua:"GT-I9300I"}],dpi:[304.8,305.8],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-I9195/*"},{ua:"GT-I9195"}],dpi:[249.4,256.7],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SPH-L520/*"},{ua:"SPH-L520"}],dpi:[249.4,255.9],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SAMSUNG-SGH-I717/*"},{ua:"SAMSUNG-SGH-I717"}],dpi:285.8,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SPH-D710/*"},{ua:"SPH-D710"}],dpi:[217.7,204.2],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/GT-N7100/*"},{ua:"GT-N7100"}],dpi:265.1,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SCH-I605/*"},{ua:"SCH-I605"}],dpi:265.1,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/Galaxy Nexus/*"},{ua:"Galaxy Nexus"}],dpi:[315.3,314.2],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-N910H/*"},{ua:"SM-N910H"}],dpi:[515.1,518],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-N910C/*"},{ua:"SM-N910C"}],dpi:[515.2,520.2],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G130M/*"},{ua:"SM-G130M"}],dpi:[165.9,164.8],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G928I/*"},{ua:"SM-G928I"}],dpi:[515.1,518.4],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G920F/*"},{ua:"SM-G920F"}],dpi:580.6,bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G920P/*"},{ua:"SM-G920P"}],dpi:[522.5,577],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G925F/*"},{ua:"SM-G925F"}],dpi:580.6,bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G925V/*"},{ua:"SM-G925V"}],dpi:[522.5,576.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G930F/*"},{ua:"SM-G930F"}],dpi:576.6,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G935F/*"},{ua:"SM-G935F"}],dpi:533,bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G950F/*"},{ua:"SM-G950F"}],dpi:[562.707,565.293],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G955U/*"},{ua:"SM-G955U"}],dpi:[522.514,525.762],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G955F/*"},{ua:"SM-G955F"}],dpi:[522.514,525.762],bw:3,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G960F/*"},{ua:"SM-G960F"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G9600/*"},{ua:"SM-G9600"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G960T/*"},{ua:"SM-G960T"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G960N/*"},{ua:"SM-G960N"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G960U/*"},{ua:"SM-G960U"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G9608/*"},{ua:"SM-G9608"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G960FD/*"},{ua:"SM-G960FD"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G960W/*"},{ua:"SM-G960W"}],dpi:[569.575,571.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G965F/*"},{ua:"SM-G965F"}],dpi:529,bw:2,ac:1e3},{type:"android",rules:[{mdmh:"Sony/*/C6903/*"},{ua:"C6903"}],dpi:[442.5,443.3],bw:3,ac:500},{type:"android",rules:[{mdmh:"Sony/*/D6653/*"},{ua:"D6653"}],dpi:[428.6,427.6],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Sony/*/E6653/*"},{ua:"E6653"}],dpi:[428.6,425.7],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Sony/*/E6853/*"},{ua:"E6853"}],dpi:[403.4,401.9],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Sony/*/SGP321/*"},{ua:"SGP321"}],dpi:[224.7,224.1],bw:3,ac:500},{type:"android",rules:[{mdmh:"TCT/*/ALCATEL ONE TOUCH Fierce/*"},{ua:"ALCATEL ONE TOUCH Fierce"}],dpi:[240,247.5],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"THL/*/thl 5000/*"},{ua:"thl 5000"}],dpi:[480,443.3],bw:3,ac:1e3},{type:"android",rules:[{mdmh:"Fly/*/IQ4412/*"},{ua:"IQ4412"}],dpi:307.9,bw:3,ac:1e3},{type:"android",rules:[{mdmh:"ZTE/*/ZTE Blade L2/*"},{ua:"ZTE Blade L2"}],dpi:240,bw:3,ac:500},{type:"android",rules:[{mdmh:"BENEVE/*/VR518/*"},{ua:"VR518"}],dpi:480,bw:3,ac:500},{type:"ios",rules:[{res:[640,960]}],dpi:[325.1,328.4],bw:4,ac:1e3},{type:"ios",rules:[{res:[640,1136]}],dpi:[317.1,320.2],bw:3,ac:1e3},{type:"ios",rules:[{res:[750,1334]}],dpi:326.4,bw:4,ac:1e3},{type:"ios",rules:[{res:[1242,2208]}],dpi:[453.6,458.4],bw:4,ac:1e3},{type:"ios",rules:[{res:[1125,2001]}],dpi:[410.9,415.4],bw:4,ac:1e3},{type:"ios",rules:[{res:[1125,2436]}],dpi:458,bw:4,ac:1e3},{type:"android",rules:[{mdmh:"Huawei/*/EML-L29/*"},{ua:"EML-L29"}],dpi:428,bw:3.45,ac:500},{type:"android",rules:[{mdmh:"Nokia/*/Nokia 7.1/*"},{ua:"Nokia 7.1"}],dpi:[432,431.9],bw:3,ac:500},{type:"ios",rules:[{res:[1242,2688]}],dpi:458,bw:4,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G570M/*"},{ua:"SM-G570M"}],dpi:320,bw:3.684,ac:1e3},{type:"android",rules:[{mdmh:"samsung/*/SM-G970F/*"},{ua:"SM-G970F"}],dpi:438,bw:2.281,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G973F/*"},{ua:"SM-G973F"}],dpi:550,bw:2.002,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G975F/*"},{ua:"SM-G975F"}],dpi:522,bw:2.054,ac:500},{type:"android",rules:[{mdmh:"samsung/*/SM-G977F/*"},{ua:"SM-G977F"}],dpi:505,bw:2.334,ac:500},{type:"ios",rules:[{res:[828,1792]}],dpi:326,bw:5,ac:500}]};function Y(e,t){if(this.dpdb=q,e){this.onDeviceParamsUpdated=t;var n=new XMLHttpRequest,i=this;n.open("GET",e,!0),n.addEventListener("load",(function(){i.loading=!1,n.status>=200&&n.status<=299?i.dpdb=JSON.parse(n.response):console.error("Error loading online DPDB!")})),n.send()}}function Z(e){this.xdpi=e.xdpi,this.ydpi=e.ydpi,this.bevelMm=e.bevelMm}function K(e,t){this.set(e,t)}function $(e,t){this.kFilter=e,this.isDebug=t,this.currentAccelMeasurement=new K,this.currentGyroMeasurement=new K,this.previousGyroMeasurement=new K,o()?this.filterQ=new U(-1,0,0,1):this.filterQ=new U(1,0,0,1),this.previousFilterQ=new U,this.previousFilterQ.copy(this.filterQ),this.accelQ=new U,this.isOrientationInitialized=!1,this.estimatedGravity=new k,this.measuredGravity=new k,this.gyroIntegralQ=new U}function J(e,t){this.predictionTimeS=e,this.isDebug=t,this.previousQ=new U,this.previousTimestampS=null,this.deltaQ=new U,this.outQ=new U}function ee(e,t,n,i){this.yawOnly=n,this.accelerometer=new k,this.gyroscope=new k,this.filter=new $(e,i),this.posePredictor=new J(t,i),this.isFirefoxAndroid=u(),this.isIOS=o();var r=l();this.isDeviceMotionInRadians=!this.isIOS&&r&&r<66,this.isWithoutDeviceMotion=h()||c(),this.filterToWorldQ=new U,o()?this.filterToWorldQ.setFromAxisAngle(new k(1,0,0),Math.PI/2):this.filterToWorldQ.setFromAxisAngle(new k(1,0,0),-Math.PI/2),this.inverseWorldToScreenQ=new U,this.worldToScreenQ=new U,this.originalPoseAdjustQ=new U,this.originalPoseAdjustQ.setFromAxisAngle(new k(0,0,1),-window.orientation*Math.PI/180),this.setScreenTransform_(),p()&&this.filterToWorldQ.multiply(this.inverseWorldToScreenQ),this.resetQ=new U,this.orientationOut_=new Float32Array(4),this.start()}Y.prototype.getDeviceParams=function(){return this.deviceParams},Y.prototype.recalculateDeviceParams_=function(){var e=this.calcDeviceParams_();e?(this.deviceParams=e,this.onDeviceParamsUpdated&&this.onDeviceParamsUpdated(this.deviceParams)):console.error("Failed to recalculate device parameters.")},Y.prototype.calcDeviceParams_=function(){var e=this.dpdb;if(!e)return console.error("DPDB not available."),null;if(1!=e.format)return console.error("DPDB has unexpected format version."),null;if(!e.devices||!e.devices.length)return console.error("DPDB does not have a devices section."),null;var t=navigator.userAgent||navigator.vendor||window.opera,n=f(),i=A();if(!e.devices)return console.error("DPDB has no devices section."),null;for(var r=0;r<e.devices.length;r++){var s=e.devices[r];if(s.rules)if("ios"==s.type||"android"==s.type){if(o()==("ios"==s.type)){for(var a=!1,u=0;u<s.rules.length;u++){var l=s.rules[u];if(this.ruleMatches_(l,t,n,i)){a=!0;break}}if(a)return new Z({xdpi:s.dpi[0]||s.dpi,ydpi:s.dpi[1]||s.dpi,bevelMm:s.bw})}}else console.warn("Device["+r+"] has invalid type.");else console.warn("Device["+r+"] has no rules section.")}return console.warn("No DPDB device match."),null},Y.prototype.ruleMatches_=function(e,t,n,i){if(!e.ua&&!e.res)return!1;if(e.ua&&"SM"===e.ua.substring(0,2)&&(e.ua=e.ua.substring(0,7)),e.ua&&t.indexOf(e.ua)<0)return!1;if(e.res){if(!e.res[0]||!e.res[1])return!1;var r=e.res[0],o=e.res[1];if(Math.min(n,i)!=Math.min(r,o)||Math.max(n,i)!=Math.max(r,o))return!1}return!0},K.prototype.set=function(e,t){this.sample=e,this.timestampS=t},K.prototype.copy=function(e){this.set(e.sample,e.timestampS)},$.prototype.addAccelMeasurement=function(e,t){this.currentAccelMeasurement.set(e,t)},$.prototype.addGyroMeasurement=function(e,t){var n;this.currentGyroMeasurement.set(e,t),n=t-this.previousGyroMeasurement.timestampS,!(isNaN(n)||n<=.001||n>1)&&this.run_(),this.previousGyroMeasurement.copy(this.currentGyroMeasurement)},$.prototype.run_=function(){if(!this.isOrientationInitialized)return this.accelQ=this.accelToQuaternion_(this.currentAccelMeasurement.sample),this.previousFilterQ.copy(this.accelQ),void(this.isOrientationInitialized=!0);var e=this.currentGyroMeasurement.timestampS-this.previousGyroMeasurement.timestampS,t=this.gyroToQuaternionDelta_(this.currentGyroMeasurement.sample,e);this.gyroIntegralQ.multiply(t),this.filterQ.copy(this.previousFilterQ),this.filterQ.multiply(t);var n=new U;n.copy(this.filterQ),n.inverse(),this.estimatedGravity.set(0,0,-1),this.estimatedGravity.applyQuaternion(n),this.estimatedGravity.normalize(),this.measuredGravity.copy(this.currentAccelMeasurement.sample),this.measuredGravity.normalize();var i,r=new U;r.setFromUnitVectors(this.estimatedGravity,this.measuredGravity),r.inverse(),this.isDebug&&console.log("Delta: %d deg, G_est: (%s, %s, %s), G_meas: (%s, %s, %s)",V*((i=r).w>1?(console.warn("getQuaternionAngle: w > 1"),0):2*Math.acos(i.w)),this.estimatedGravity.x.toFixed(1),this.estimatedGravity.y.toFixed(1),this.estimatedGravity.z.toFixed(1),this.measuredGravity.x.toFixed(1),this.measuredGravity.y.toFixed(1),this.measuredGravity.z.toFixed(1));var o=new U;o.copy(this.filterQ),o.multiply(r),this.filterQ.slerp(o,1-this.kFilter),this.previousFilterQ.copy(this.filterQ)},$.prototype.getOrientation=function(){return this.filterQ},$.prototype.accelToQuaternion_=function(e){var t=new k;t.copy(e),t.normalize();var n=new U;return n.setFromUnitVectors(new k(0,0,-1),t),n.inverse(),n},$.prototype.gyroToQuaternionDelta_=function(e,t){var n=new U,i=new k;return i.copy(e),i.normalize(),n.setFromAxisAngle(i,e.length()*t),n},J.prototype.getPrediction=function(e,t,n){if(!this.previousTimestampS)return this.previousQ.copy(e),this.previousTimestampS=n,e;var i=new k;i.copy(t),i.normalize();var r=t.length();if(r<20*L)return this.isDebug&&console.log("Moving slowly, at %s deg/s: no prediction",(V*r).toFixed(1)),this.outQ.copy(e),this.previousQ.copy(e),this.outQ;var o=r*this.predictionTimeS;return this.deltaQ.setFromAxisAngle(i,o),this.outQ.copy(this.previousQ),this.outQ.multiply(this.deltaQ),this.previousQ.copy(e),this.previousTimestampS=n,this.outQ},ee.prototype.getPosition=function(){return null},ee.prototype.getOrientation=function(){var e=void 0;if(this.isWithoutDeviceMotion&&this._deviceOrientationQ)return this.deviceOrientationFixQ=this.deviceOrientationFixQ||(n=(new U).setFromAxisAngle(new k(0,0,-1),0),i=new U,-90===window.orientation?i.setFromAxisAngle(new k(0,1,0),Math.PI/-2):i.setFromAxisAngle(new k(0,1,0),Math.PI/2),n.multiply(i)),this.deviceOrientationFilterToWorldQ=this.deviceOrientationFilterToWorldQ||((t=new U).setFromAxisAngle(new k(1,0,0),-Math.PI/2),t),e=this._deviceOrientationQ,(r=new U).copy(e),r.multiply(this.deviceOrientationFilterToWorldQ),r.multiply(this.resetQ),r.multiply(this.worldToScreenQ),r.multiplyQuaternions(this.deviceOrientationFixQ,r),this.yawOnly&&(r.x=0,r.z=0,r.normalize()),this.orientationOut_[0]=r.x,this.orientationOut_[1]=r.y,this.orientationOut_[2]=r.z,this.orientationOut_[3]=r.w,this.orientationOut_;var t,n,i,r,o=this.filter.getOrientation();return e=this.posePredictor.getPrediction(o,this.gyroscope,this.previousTimestampS),(r=new U).copy(this.filterToWorldQ),r.multiply(this.resetQ),r.multiply(e),r.multiply(this.worldToScreenQ),this.yawOnly&&(r.x=0,r.z=0,r.normalize()),this.orientationOut_[0]=r.x,this.orientationOut_[1]=r.y,this.orientationOut_[2]=r.z,this.orientationOut_[3]=r.w,this.orientationOut_},ee.prototype.resetPose=function(){this.resetQ.copy(this.filter.getOrientation()),this.resetQ.x=0,this.resetQ.y=0,this.resetQ.z*=-1,this.resetQ.normalize(),p()&&this.resetQ.multiply(this.inverseWorldToScreenQ),this.resetQ.multiply(this.originalPoseAdjustQ)},ee.prototype.onDeviceOrientation_=function(e){this._deviceOrientationQ=this._deviceOrientationQ||new U;var t=e.alpha,n=e.beta,i=e.gamma;t=(t||0)*Math.PI/180,n=(n||0)*Math.PI/180,i=(i||0)*Math.PI/180,this._deviceOrientationQ.setFromEulerYXZ(n,t,-i)},ee.prototype.onDeviceMotion_=function(e){this.updateDeviceMotion_(e)},ee.prototype.updateDeviceMotion_=function(e){var t=e.accelerationIncludingGravity,n=e.rotationRate,i=e.timeStamp/1e3,r=i-this.previousTimestampS;return r<0?(E("fusion-pose-sensor:invalid:non-monotonic","Invalid timestamps detected: non-monotonic timestamp from devicemotion"),void(this.previousTimestampS=i)):r<=.001||r>1?(E("fusion-pose-sensor:invalid:outside-threshold","Invalid timestamps detected: Timestamp from devicemotion outside expected range."),void(this.previousTimestampS=i)):(this.accelerometer.set(-t.x,-t.y,-t.z),n&&(d()?this.gyroscope.set(-n.beta,n.alpha,n.gamma):this.gyroscope.set(n.alpha,n.beta,n.gamma),this.isDeviceMotionInRadians||this.gyroscope.multiplyScalar(Math.PI/180),this.filter.addGyroMeasurement(this.gyroscope,i)),this.filter.addAccelMeasurement(this.accelerometer,i),void(this.previousTimestampS=i))},ee.prototype.onOrientationChange_=function(e){this.setScreenTransform_()},ee.prototype.onMessage_=function(e){var t=e.data;t&&t.type&&"devicemotion"===t.type.toLowerCase()&&this.updateDeviceMotion_(t.deviceMotionEvent)},ee.prototype.setScreenTransform_=function(){switch(this.worldToScreenQ.set(0,0,0,1),window.orientation){case 0:break;case 90:this.worldToScreenQ.setFromAxisAngle(new k(0,0,1),-Math.PI/2);break;case-90:this.worldToScreenQ.setFromAxisAngle(new k(0,0,1),Math.PI/2)}this.inverseWorldToScreenQ.copy(this.worldToScreenQ),this.inverseWorldToScreenQ.inverse()},ee.prototype.start=function(){var e,t,n;this.onDeviceMotionCallback_=this.onDeviceMotion_.bind(this),this.onOrientationChangeCallback_=this.onOrientationChange_.bind(this),this.onMessageCallback_=this.onMessage_.bind(this),this.onDeviceOrientationCallback_=this.onDeviceOrientation_.bind(this),o()&&(e=window.self!==window.top,t=x(document.referrer),n=x(window.location.href),e&&t!==n)&&window.addEventListener("message",this.onMessageCallback_),window.addEventListener("orientationchange",this.onOrientationChangeCallback_),this.isWithoutDeviceMotion?window.addEventListener("deviceorientation",this.onDeviceOrientationCallback_):window.addEventListener("devicemotion",this.onDeviceMotionCallback_)},ee.prototype.stop=function(){window.removeEventListener("devicemotion",this.onDeviceMotionCallback_),window.removeEventListener("deviceorientation",this.onDeviceOrientationCallback_),window.removeEventListener("orientationchange",this.onOrientationChangeCallback_),window.removeEventListener("message",this.onMessageCallback_)};var te=new k(1,0,0),ne=new k(0,0,1),ie=new U;ie.setFromAxisAngle(te,-Math.PI/2),ie.multiply((new U).setFromAxisAngle(ne,Math.PI/2));var re=function(){function e(t){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.config=t,this.sensor=null,this.fusionSensor=null,this._out=new Float32Array(4),this.api=null,this.errors=[],this._sensorQ=new U,this._outQ=new U,this._onSensorRead=this._onSensorRead.bind(this),this._onSensorError=this._onSensorError.bind(this),this.init()}return i(e,[{key:"init",value:function(){var e=null;try{(e=new RelativeOrientationSensor({frequency:60,referenceFrame:"screen"})).addEventListener("error",this._onSensorError)}catch(e){this.errors.push(e),"SecurityError"===e.name?(console.error("Cannot construct sensors due to the Feature Policy"),console.warn('Attempting to fall back using "devicemotion"; however this will fail in the future without correct permissions.'),this.useDeviceMotion()):"ReferenceError"===e.name?this.useDeviceMotion():console.error(e)}e&&(this.api="sensor",this.sensor=e,this.sensor.addEventListener("reading",this._onSensorRead),this.sensor.start())}},{key:"useDeviceMotion",value:function(){this.api="devicemotion",this.fusionSensor=new ee(this.config.K_FILTER,this.config.PREDICTION_TIME_S,this.config.YAW_ONLY,this.config.DEBUG),this.sensor&&(this.sensor.removeEventListener("reading",this._onSensorRead),this.sensor.removeEventListener("error",this._onSensorError),this.sensor=null)}},{key:"getOrientation",value:function(){if(this.fusionSensor)return this.fusionSensor.getOrientation();if(!this.sensor||!this.sensor.quaternion)return this._out[0]=this._out[1]=this._out[2]=0,this._out[3]=1,this._out;var e=this.sensor.quaternion;this._sensorQ.set(e[0],e[1],e[2],e[3]);var t=this._outQ;return t.copy(ie),t.multiply(this._sensorQ),this.config.YAW_ONLY&&(t.x=t.z=0,t.normalize()),this._out[0]=t.x,this._out[1]=t.y,this._out[2]=t.z,this._out[3]=t.w,this._out}},{key:"_onSensorError",value:function(e){this.errors.push(e.error),"NotAllowedError"===e.error.name?console.error("Permission to access sensor was denied"):"NotReadableError"===e.error.name?console.error("Sensor could not be read"):console.error(e.error),this.useDeviceMotion()}},{key:"_onSensorRead",value:function(){}}]),e}();function oe(){this.loadIcon_();var e=document.createElement("div");(o=e.style).position="fixed",o.top=0,o.right=0,o.bottom=0,o.left=0,o.backgroundColor="gray",o.fontFamily="sans-serif",o.zIndex=1e6;var t=document.createElement("img");t.src=this.icon,(o=t.style).marginLeft="25%",o.marginTop="25%",o.width="50%",e.appendChild(t);var n=document.createElement("div");(o=n.style).textAlign="center",o.fontSize="16px",o.lineHeight="24px",o.margin="24px 25%",o.width="50%",n.innerHTML="Place your phone into your Cardboard viewer.",e.appendChild(n);var i=document.createElement("div");(o=i.style).backgroundColor="#CFD8DC",o.position="fixed",o.bottom=0,o.width="100%",o.height="48px",o.padding="14px 24px",o.boxSizing="border-box",o.color="#656A6B",e.appendChild(i);var r=document.createElement("div");r.style.float="left",r.innerHTML="No Cardboard viewer?";var o,s=document.createElement("a");s.href="https://www.google.com/get/cardboard/get-cardboard/",s.innerHTML="get one",s.target="_blank",(o=s.style).float="right",o.fontWeight=600,o.textTransform="uppercase",o.borderLeft="1px solid gray",o.paddingLeft="24px",o.textDecoration="none",o.color="#656A6B",i.appendChild(r),i.appendChild(s),this.overlay=e,this.text=n,this.hide()}oe.prototype.show=function(e){e||this.overlay.parentElement?e&&(this.overlay.parentElement&&this.overlay.parentElement!=e&&this.overlay.parentElement.removeChild(this.overlay),e.appendChild(this.overlay)):document.body.appendChild(this.overlay),this.overlay.style.display="block";var t=this.overlay.querySelector("img").style;p()?(t.width="20%",t.marginLeft="40%",t.marginTop="3%"):(t.width="50%",t.marginLeft="25%",t.marginTop="25%")},oe.prototype.hide=function(){this.overlay.style.display="none"},oe.prototype.showTemporarily=function(e,t){this.show(t),this.timer=setTimeout(this.hide.bind(this),e)},oe.prototype.disableShowTemporarily=function(){clearTimeout(this.timer)},oe.prototype.update=function(){this.disableShowTemporarily(),!p()&&y()?this.show():this.hide()},oe.prototype.loadIcon_=function(){this.icon="data:image/svg+xml,"+encodeURIComponent("<svg width='198' height='240' viewBox='0 0 198 240' xmlns='http://www.w3.org/2000/svg'><g fill='none' fill-rule='evenodd'><path d='M149.625 109.527l6.737 3.891v.886c0 .177.013.36.038.549.01.081.02.162.027.242.14 1.415.974 2.998 2.105 3.999l5.72 5.062.081-.09s4.382-2.53 5.235-3.024l25.97 14.993v54.001c0 .771-.386 1.217-.948 1.217-.233 0-.495-.076-.772-.236l-23.967-13.838-.014.024-27.322 15.775-.85-1.323c-4.731-1.529-9.748-2.74-14.951-3.61a.27.27 0 0 0-.007.024l-5.067 16.961-7.891 4.556-.037-.063v27.59c0 .772-.386 1.217-.948 1.217-.232 0-.495-.076-.772-.236l-42.473-24.522c-.95-.549-1.72-1.877-1.72-2.967v-1.035l-.021.047a5.111 5.111 0 0 0-1.816-.399 5.682 5.682 0 0 0-.546.001 13.724 13.724 0 0 1-1.918-.041c-1.655-.153-3.2-.6-4.404-1.296l-46.576-26.89.005.012-10.278-18.75c-1.001-1.827-.241-4.216 1.698-5.336l56.011-32.345a4.194 4.194 0 0 1 2.099-.572c1.326 0 2.572.659 3.227 1.853l.005-.003.227.413-.006.004a9.63 9.63 0 0 0 1.477 2.018l.277.27c1.914 1.85 4.468 2.801 7.113 2.801 1.949 0 3.948-.517 5.775-1.572.013 0 7.319-4.219 7.319-4.219a4.194 4.194 0 0 1 2.099-.572c1.326 0 2.572.658 3.226 1.853l3.25 5.928.022-.018 6.785 3.917-.105-.182 46.881-26.965m0-1.635c-.282 0-.563.073-.815.218l-46.169 26.556-5.41-3.124-3.005-5.481c-.913-1.667-2.699-2.702-4.66-2.703-1.011 0-2.02.274-2.917.792a3825 3825 0 0 1-7.275 4.195l-.044.024a9.937 9.937 0 0 1-4.957 1.353c-2.292 0-4.414-.832-5.976-2.342l-.252-.245a7.992 7.992 0 0 1-1.139-1.534 1.379 1.379 0 0 0-.06-.122l-.227-.414a1.718 1.718 0 0 0-.095-.154c-.938-1.574-2.673-2.545-4.571-2.545-1.011 0-2.02.274-2.917.792L3.125 155.502c-2.699 1.559-3.738 4.94-2.314 7.538l10.278 18.75c.177.323.448.563.761.704l46.426 26.804c1.403.81 3.157 1.332 5.072 1.508a15.661 15.661 0 0 0 2.146.046 4.766 4.766 0 0 1 .396 0c.096.004.19.011.283.022.109 1.593 1.159 3.323 2.529 4.114l42.472 24.522c.524.302 1.058.455 1.59.455 1.497 0 2.583-1.2 2.583-2.852v-26.562l7.111-4.105a1.64 1.64 0 0 0 .749-.948l4.658-15.593c4.414.797 8.692 1.848 12.742 3.128l.533.829a1.634 1.634 0 0 0 2.193.531l26.532-15.317L193 192.433c.523.302 1.058.455 1.59.455 1.497 0 2.583-1.199 2.583-2.852v-54.001c0-.584-.312-1.124-.818-1.416l-25.97-14.993a1.633 1.633 0 0 0-1.636.001c-.606.351-2.993 1.73-4.325 2.498l-4.809-4.255c-.819-.725-1.461-1.933-1.561-2.936a7.776 7.776 0 0 0-.033-.294 2.487 2.487 0 0 1-.023-.336v-.886c0-.584-.312-1.123-.817-1.416l-6.739-3.891a1.633 1.633 0 0 0-.817-.219' fill='#455A64'/><path d='M96.027 132.636l46.576 26.891c1.204.695 1.979 1.587 2.242 2.541l-.01.007-81.374 46.982h-.001c-1.654-.152-3.199-.6-4.403-1.295l-46.576-26.891 83.546-48.235' fill='#FAFAFA'/><path d='M63.461 209.174c-.008 0-.015 0-.022-.002-1.693-.156-3.228-.609-4.441-1.309l-46.576-26.89a.118.118 0 0 1 0-.203l83.546-48.235a.117.117 0 0 1 .117 0l46.576 26.891c1.227.708 2.021 1.612 2.296 2.611a.116.116 0 0 1-.042.124l-.021.016-81.375 46.981a.11.11 0 0 1-.058.016zm-50.747-28.303l46.401 26.79c1.178.68 2.671 1.121 4.32 1.276l81.272-46.922c-.279-.907-1.025-1.73-2.163-2.387l-46.517-26.857-83.313 48.1z' fill='#607D8B'/><path d='M148.327 165.471a5.85 5.85 0 0 1-.546.001c-1.894-.083-3.302-1.038-3.145-2.132a2.693 2.693 0 0 0-.072-1.105l-81.103 46.822c.628.058 1.272.073 1.918.042.182-.009.364-.009.546-.001 1.894.083 3.302 1.038 3.145 2.132l79.257-45.759' fill='#FFF'/><path d='M69.07 211.347a.118.118 0 0 1-.115-.134c.045-.317-.057-.637-.297-.925-.505-.61-1.555-1.022-2.738-1.074a5.966 5.966 0 0 0-.535.001 14.03 14.03 0 0 1-1.935-.041.117.117 0 0 1-.103-.092.116.116 0 0 1 .055-.126l81.104-46.822a.117.117 0 0 1 .171.07c.104.381.129.768.074 1.153-.045.316.057.637.296.925.506.61 1.555 1.021 2.739 1.073.178.008.357.008.535-.001a.117.117 0 0 1 .064.218l-79.256 45.759a.114.114 0 0 1-.059.016zm-3.405-2.372c.089 0 .177.002.265.006 1.266.056 2.353.488 2.908 1.158.227.274.35.575.36.882l78.685-45.429c-.036 0-.072-.001-.107-.003-1.267-.056-2.354-.489-2.909-1.158-.282-.34-.402-.724-.347-1.107a2.604 2.604 0 0 0-.032-.91L63.846 208.97a13.91 13.91 0 0 0 1.528.012c.097-.005.194-.007.291-.007z' fill='#607D8B'/><path d='M2.208 162.134c-1.001-1.827-.241-4.217 1.698-5.337l56.011-32.344c1.939-1.12 4.324-.546 5.326 1.281l.232.41a9.344 9.344 0 0 0 1.47 2.021l.278.27c3.325 3.214 8.583 3.716 12.888 1.23l7.319-4.22c1.94-1.119 4.324-.546 5.325 1.282l3.25 5.928-83.519 48.229-10.278-18.75z' fill='#FAFAFA'/><path d='M12.486 181.001a.112.112 0 0 1-.031-.005.114.114 0 0 1-.071-.056L2.106 162.19c-1.031-1.88-.249-4.345 1.742-5.494l56.01-32.344a4.328 4.328 0 0 1 2.158-.588c1.415 0 2.65.702 3.311 1.882.01.008.018.017.024.028l.227.414a.122.122 0 0 1 .013.038 9.508 9.508 0 0 0 1.439 1.959l.275.266c1.846 1.786 4.344 2.769 7.031 2.769 1.977 0 3.954-.538 5.717-1.557a.148.148 0 0 1 .035-.013l7.284-4.206a4.321 4.321 0 0 1 2.157-.588c1.427 0 2.672.716 3.329 1.914l3.249 5.929a.116.116 0 0 1-.044.157l-83.518 48.229a.116.116 0 0 1-.059.016zm49.53-57.004c-.704 0-1.41.193-2.041.557l-56.01 32.345c-1.882 1.086-2.624 3.409-1.655 5.179l10.221 18.645 83.317-48.112-3.195-5.829c-.615-1.122-1.783-1.792-3.124-1.792a4.08 4.08 0 0 0-2.04.557l-7.317 4.225a.148.148 0 0 1-.035.013 11.7 11.7 0 0 1-5.801 1.569c-2.748 0-5.303-1.007-7.194-2.835l-.278-.27a9.716 9.716 0 0 1-1.497-2.046.096.096 0 0 1-.013-.037l-.191-.347a.11.11 0 0 1-.023-.029c-.615-1.123-1.783-1.793-3.124-1.793z' fill='#607D8B'/><path d='M42.434 155.808c-2.51-.001-4.697-1.258-5.852-3.365-1.811-3.304-.438-7.634 3.059-9.654l12.291-7.098a7.599 7.599 0 0 1 3.789-1.033c2.51 0 4.697 1.258 5.852 3.365 1.811 3.304.439 7.634-3.059 9.654l-12.291 7.098a7.606 7.606 0 0 1-3.789 1.033zm13.287-20.683a7.128 7.128 0 0 0-3.555.971l-12.291 7.098c-3.279 1.893-4.573 5.942-2.883 9.024 1.071 1.955 3.106 3.122 5.442 3.122a7.13 7.13 0 0 0 3.556-.97l12.291-7.098c3.279-1.893 4.572-5.942 2.883-9.024-1.072-1.955-3.106-3.123-5.443-3.123z' fill='#607D8B'/><path d='M149.588 109.407l6.737 3.89v.887c0 .176.013.36.037.549.011.081.02.161.028.242.14 1.415.973 2.998 2.105 3.999l7.396 6.545c.177.156.358.295.541.415 1.579 1.04 2.95.466 3.062-1.282.049-.784.057-1.595.023-2.429l-.003-.16v-1.151l25.987 15.003v54c0 1.09-.77 1.53-1.72.982l-42.473-24.523c-.95-.548-1.72-1.877-1.72-2.966v-34.033' fill='#FAFAFA'/><path d='M194.553 191.25c-.257 0-.54-.085-.831-.253l-42.472-24.521c-.981-.567-1.779-1.943-1.779-3.068v-34.033h.234v34.033c0 1.051.745 2.336 1.661 2.866l42.473 24.521c.424.245.816.288 1.103.122.285-.164.442-.52.442-1.002v-53.933l-25.753-14.868.003 1.106c.034.832.026 1.654-.024 2.439-.054.844-.396 1.464-.963 1.746-.619.309-1.45.173-2.28-.373a5.023 5.023 0 0 1-.553-.426l-7.397-6.544c-1.158-1.026-1.999-2.625-2.143-4.076a9.624 9.624 0 0 0-.027-.238 4.241 4.241 0 0 1-.038-.564v-.82l-6.68-3.856.117-.202 6.738 3.89.058.034v.954c0 .171.012.351.036.533.011.083.021.165.029.246.138 1.395.948 2.935 2.065 3.923l7.397 6.545c.173.153.35.289.527.406.758.499 1.504.63 2.047.359.49-.243.786-.795.834-1.551.05-.778.057-1.591.024-2.417l-.004-.163v-1.355l.175.1 25.987 15.004.059.033v54.068c0 .569-.198.996-.559 1.204a1.002 1.002 0 0 1-.506.131' fill='#607D8B'/><path d='M145.685 163.161l24.115 13.922-25.978 14.998-1.462-.307c-6.534-2.17-13.628-3.728-21.019-4.616-4.365-.524-8.663 1.096-9.598 3.62a2.746 2.746 0 0 0-.011 1.928c1.538 4.267 4.236 8.363 7.995 12.135l.532.845-25.977 14.997-24.115-13.922 75.518-43.6' fill='#FFF'/><path d='M94.282 220.818l-.059-.033-24.29-14.024.175-.101 75.577-43.634.058.033 24.29 14.024-26.191 15.122-.045-.01-1.461-.307c-6.549-2.174-13.613-3.725-21.009-4.614a13.744 13.744 0 0 0-1.638-.097c-3.758 0-7.054 1.531-7.837 3.642a2.62 2.62 0 0 0-.01 1.848c1.535 4.258 4.216 8.326 7.968 12.091l.016.021.526.835.006.01.064.102-.105.061-25.977 14.998-.058.033zm-23.881-14.057l23.881 13.788 24.802-14.32c.546-.315.846-.489 1.017-.575l-.466-.74c-3.771-3.787-6.467-7.881-8.013-12.168a2.851 2.851 0 0 1 .011-2.008c.815-2.199 4.203-3.795 8.056-3.795.557 0 1.117.033 1.666.099 7.412.891 14.491 2.445 21.041 4.621.836.175 1.215.254 1.39.304l25.78-14.884-23.881-13.788-75.284 43.466z' fill='#607D8B'/><path d='M167.23 125.979v50.871l-27.321 15.773-6.461-14.167c-.91-1.996-3.428-1.738-5.624.574a10.238 10.238 0 0 0-2.33 4.018l-6.46 21.628-27.322 15.774v-50.871l75.518-43.6' fill='#FFF'/><path d='M91.712 220.567a.127.127 0 0 1-.059-.016.118.118 0 0 1-.058-.101v-50.871c0-.042.023-.08.058-.101l75.519-43.6a.117.117 0 0 1 .175.101v50.871c0 .041-.023.08-.059.1l-27.321 15.775a.118.118 0 0 1-.094.01.12.12 0 0 1-.071-.063l-6.46-14.168c-.375-.822-1.062-1.275-1.934-1.275-1.089 0-2.364.686-3.5 1.881a10.206 10.206 0 0 0-2.302 3.972l-6.46 21.627a.118.118 0 0 1-.054.068L91.77 220.551a.12.12 0 0 1-.058.016zm.117-50.92v50.601l27.106-15.65 6.447-21.583a10.286 10.286 0 0 1 2.357-4.065c1.18-1.242 2.517-1.954 3.669-1.954.969 0 1.731.501 2.146 1.411l6.407 14.051 27.152-15.676v-50.601l-75.284 43.466z' fill='#607D8B'/><path d='M168.543 126.213v50.87l-27.322 15.774-6.46-14.168c-.91-1.995-3.428-1.738-5.624.574a10.248 10.248 0 0 0-2.33 4.019l-6.461 21.627-27.321 15.774v-50.87l75.518-43.6' fill='#FFF'/><path d='M93.025 220.8a.123.123 0 0 1-.059-.015.12.12 0 0 1-.058-.101v-50.871c0-.042.023-.08.058-.101l75.518-43.6a.112.112 0 0 1 .117 0c.036.02.059.059.059.1v50.871a.116.116 0 0 1-.059.101l-27.321 15.774a.111.111 0 0 1-.094.01.115.115 0 0 1-.071-.062l-6.46-14.168c-.375-.823-1.062-1.275-1.935-1.275-1.088 0-2.363.685-3.499 1.881a10.19 10.19 0 0 0-2.302 3.971l-6.461 21.628a.108.108 0 0 1-.053.067l-27.322 15.775a.12.12 0 0 1-.058.015zm.117-50.919v50.6l27.106-15.649 6.447-21.584a10.293 10.293 0 0 1 2.357-4.065c1.179-1.241 2.516-1.954 3.668-1.954.969 0 1.732.502 2.147 1.412l6.407 14.051 27.152-15.676v-50.601l-75.284 43.466z' fill='#607D8B'/><path d='M169.8 177.083l-27.322 15.774-6.46-14.168c-.91-1.995-3.428-1.738-5.625.574a10.246 10.246 0 0 0-2.329 4.019l-6.461 21.627-27.321 15.774v-50.87l75.518-43.6v50.87z' fill='#FAFAFA'/><path d='M94.282 220.917a.234.234 0 0 1-.234-.233v-50.871c0-.083.045-.161.117-.202l75.518-43.601a.234.234 0 1 1 .35.202v50.871a.233.233 0 0 1-.116.202l-27.322 15.775a.232.232 0 0 1-.329-.106l-6.461-14.168c-.36-.789-.992-1.206-1.828-1.206-1.056 0-2.301.672-3.415 1.844a10.099 10.099 0 0 0-2.275 3.924l-6.46 21.628a.235.235 0 0 1-.107.136l-27.322 15.774a.23.23 0 0 1-.116.031zm.233-50.969v50.331l26.891-15.525 6.434-21.539a10.41 10.41 0 0 1 2.384-4.112c1.201-1.265 2.569-1.991 3.753-1.991 1.018 0 1.818.526 2.253 1.48l6.354 13.934 26.982-15.578v-50.331l-75.051 43.331z' fill='#607D8B'/><path d='M109.894 199.943c-1.774 0-3.241-.725-4.244-2.12a.224.224 0 0 1 .023-.294.233.233 0 0 1 .301-.023c.78.547 1.705.827 2.75.827 1.323 0 2.754-.439 4.256-1.306 5.311-3.067 9.631-10.518 9.631-16.611 0-1.927-.442-3.56-1.278-4.724a.232.232 0 0 1 .323-.327c1.671 1.172 2.591 3.381 2.591 6.219 0 6.242-4.426 13.863-9.865 17.003-1.574.908-3.084 1.356-4.488 1.356zm-2.969-1.542c.813.651 1.82.877 2.968.877h.001c1.321 0 2.753-.327 4.254-1.194 5.311-3.067 9.632-10.463 9.632-16.556 0-1.979-.463-3.599-1.326-4.761.411 1.035.625 2.275.625 3.635 0 6.243-4.426 13.883-9.865 17.023-1.574.909-3.084 1.317-4.49 1.317-.641 0-1.243-.149-1.799-.341z' fill='#607D8B'/><path d='M113.097 197.23c5.384-3.108 9.748-10.636 9.748-16.814 0-2.051-.483-3.692-1.323-4.86-1.784-1.252-4.374-1.194-7.257.47-5.384 3.108-9.748 10.636-9.748 16.814 0 2.051.483 3.692 1.323 4.86 1.784 1.252 4.374 1.194 7.257-.47' fill='#FAFAFA'/><path d='M108.724 198.614c-1.142 0-2.158-.213-3.019-.817-.021-.014-.04.014-.055-.007-.894-1.244-1.367-2.948-1.367-4.973 0-6.242 4.426-13.864 9.865-17.005 1.574-.908 3.084-1.363 4.49-1.363 1.142 0 2.158.309 3.018.913a.23.23 0 0 1 .056.056c.894 1.244 1.367 2.972 1.367 4.997 0 6.243-4.426 13.783-9.865 16.923-1.574.909-3.084 1.276-4.49 1.276zm-2.718-1.109c.774.532 1.688.776 2.718.776 1.323 0 2.754-.413 4.256-1.28 5.311-3.066 9.631-10.505 9.631-16.598 0-1.909-.434-3.523-1.255-4.685-.774-.533-1.688-.799-2.718-.799-1.323 0-2.755.441-4.256 1.308-5.311 3.066-9.631 10.506-9.631 16.599 0 1.909.434 3.517 1.255 4.679z' fill='#607D8B'/><path d='M149.318 114.262l-9.984 8.878 15.893 11.031 5.589-6.112-11.498-13.797' fill='#FAFAFA'/><path d='M169.676 120.84l-9.748 5.627c-3.642 2.103-9.528 2.113-13.147.024-3.62-2.089-3.601-5.488.041-7.591l9.495-5.608-6.729-3.885-81.836 47.071 45.923 26.514 3.081-1.779c.631-.365.869-.898.618-1.39-2.357-4.632-2.593-9.546-.683-14.262 5.638-13.92 24.509-24.815 48.618-28.07 8.169-1.103 16.68-.967 24.704.394.852.145 1.776.008 2.407-.357l3.081-1.778-25.825-14.91' fill='#FAFAFA'/><path d='M113.675 183.459a.47.47 0 0 1-.233-.062l-45.924-26.515a.468.468 0 0 1 .001-.809l81.836-47.071a.467.467 0 0 1 .466 0l6.729 3.885a.467.467 0 0 1-.467.809l-6.496-3.75-80.9 46.533 44.988 25.973 2.848-1.644c.192-.111.62-.409.435-.773-2.416-4.748-2.658-9.814-.7-14.65 2.806-6.927 8.885-13.242 17.582-18.263 8.657-4.998 19.518-8.489 31.407-10.094 8.198-1.107 16.79-.97 24.844.397.739.125 1.561.007 2.095-.301l2.381-1.374-25.125-14.506a.467.467 0 0 1 .467-.809l25.825 14.91a.467.467 0 0 1 0 .809l-3.081 1.779c-.721.417-1.763.575-2.718.413-7.963-1.351-16.457-1.486-24.563-.392-11.77 1.589-22.512 5.039-31.065 9.977-8.514 4.916-14.456 11.073-17.183 17.805-1.854 4.578-1.623 9.376.666 13.875.37.725.055 1.513-.8 2.006l-3.081 1.78a.476.476 0 0 1-.234.062' fill='#455A64'/><path d='M153.316 128.279c-2.413 0-4.821-.528-6.652-1.586-1.818-1.049-2.82-2.461-2.82-3.975 0-1.527 1.016-2.955 2.861-4.02l9.493-5.607a.233.233 0 1 1 .238.402l-9.496 5.609c-1.696.979-2.628 2.263-2.628 3.616 0 1.34.918 2.608 2.585 3.571 3.549 2.049 9.343 2.038 12.914-.024l9.748-5.628a.234.234 0 0 1 .234.405l-9.748 5.628c-1.858 1.072-4.296 1.609-6.729 1.609' fill='#607D8B'/><path d='M113.675 182.992l-45.913-26.508M113.675 183.342a.346.346 0 0 1-.175-.047l-45.913-26.508a.35.35 0 1 1 .35-.607l45.913 26.508a.35.35 0 0 1-.175.654' fill='#455A64'/><path d='M67.762 156.484v54.001c0 1.09.77 2.418 1.72 2.967l42.473 24.521c.95.549 1.72.11 1.72-.98v-54.001' fill='#FAFAFA'/><path d='M112.727 238.561c-.297 0-.62-.095-.947-.285l-42.473-24.521c-1.063-.613-1.895-2.05-1.895-3.27v-54.001a.35.35 0 1 1 .701 0v54.001c0 .96.707 2.18 1.544 2.663l42.473 24.522c.344.198.661.243.87.122.206-.119.325-.411.325-.799v-54.001a.35.35 0 1 1 .7 0v54.001c0 .655-.239 1.154-.675 1.406a1.235 1.235 0 0 1-.623.162' fill='#455A64'/><path d='M112.86 147.512h-.001c-2.318 0-4.499-.522-6.142-1.471-1.705-.984-2.643-2.315-2.643-3.749 0-1.445.952-2.791 2.68-3.788l12.041-6.953c1.668-.962 3.874-1.493 6.212-1.493 2.318 0 4.499.523 6.143 1.472 1.704.984 2.643 2.315 2.643 3.748 0 1.446-.952 2.791-2.68 3.789l-12.042 6.952c-1.668.963-3.874 1.493-6.211 1.493zm12.147-16.753c-2.217 0-4.298.497-5.861 1.399l-12.042 6.952c-1.502.868-2.33 1.998-2.33 3.182 0 1.173.815 2.289 2.293 3.142 1.538.889 3.596 1.378 5.792 1.378h.001c2.216 0 4.298-.497 5.861-1.399l12.041-6.953c1.502-.867 2.33-1.997 2.33-3.182 0-1.172-.814-2.288-2.292-3.142-1.539-.888-3.596-1.377-5.793-1.377z' fill='#607D8B'/><path d='M165.63 123.219l-5.734 3.311c-3.167 1.828-8.286 1.837-11.433.02-3.147-1.817-3.131-4.772.036-6.601l5.734-3.31 11.397 6.58' fill='#FAFAFA'/><path d='M154.233 117.448l9.995 5.771-4.682 2.704c-1.434.827-3.352 1.283-5.399 1.283-2.029 0-3.923-.449-5.333-1.263-1.29-.744-2-1.694-2-2.674 0-.991.723-1.955 2.036-2.713l5.383-3.108m0-.809l-5.734 3.31c-3.167 1.829-3.183 4.784-.036 6.601 1.568.905 3.623 1.357 5.684 1.357 2.077 0 4.159-.46 5.749-1.377l5.734-3.311-11.397-6.58M145.445 179.667c-1.773 0-3.241-.85-4.243-2.245-.067-.092-.057-.275.023-.356.08-.081.207-.12.3-.055.781.548 1.706.812 2.751.811 1.322 0 2.754-.446 4.256-1.313 5.31-3.066 9.631-10.522 9.631-16.615 0-1.927-.442-3.562-1.279-4.726a.235.235 0 0 1 .024-.301.232.232 0 0 1 .3-.027c1.67 1.172 2.59 3.38 2.59 6.219 0 6.242-4.425 13.987-9.865 17.127-1.573.908-3.083 1.481-4.488 1.481zM142.476 178c.814.651 1.82 1.002 2.969 1.002 1.322 0 2.753-.452 4.255-1.32 5.31-3.065 9.631-10.523 9.631-16.617 0-1.98-.463-3.63-1.325-4.793.411 1.035.624 2.26.624 3.62 0 6.242-4.425 13.875-9.865 17.015-1.573.909-3.084 1.376-4.489 1.376a5.49 5.49 0 0 1-1.8-.283z' fill='#607D8B'/><path d='M148.648 176.704c5.384-3.108 9.748-10.636 9.748-16.813 0-2.052-.483-3.693-1.322-4.861-1.785-1.252-4.375-1.194-7.258.471-5.383 3.108-9.748 10.636-9.748 16.813 0 2.051.484 3.692 1.323 4.86 1.785 1.253 4.374 1.195 7.257-.47' fill='#FAFAFA'/><path d='M144.276 178.276c-1.143 0-2.158-.307-3.019-.911a.217.217 0 0 1-.055-.054c-.895-1.244-1.367-2.972-1.367-4.997 0-6.241 4.425-13.875 9.865-17.016 1.573-.908 3.084-1.369 4.489-1.369 1.143 0 2.158.307 3.019.91a.24.24 0 0 1 .055.055c.894 1.244 1.367 2.971 1.367 4.997 0 6.241-4.425 13.875-9.865 17.016-1.573.908-3.084 1.369-4.489 1.369zm-2.718-1.172c.773.533 1.687.901 2.718.901 1.322 0 2.754-.538 4.256-1.405 5.31-3.066 9.631-10.567 9.631-16.661 0-1.908-.434-3.554-1.256-4.716-.774-.532-1.688-.814-2.718-.814-1.322 0-2.754.433-4.256 1.3-5.31 3.066-9.631 10.564-9.631 16.657 0 1.91.434 3.576 1.256 4.738z' fill='#607D8B'/><path d='M150.72 172.361l-.363-.295a24.105 24.105 0 0 0 2.148-3.128 24.05 24.05 0 0 0 1.977-4.375l.443.149a24.54 24.54 0 0 1-2.015 4.46 24.61 24.61 0 0 1-2.19 3.189M115.917 191.514l-.363-.294a24.174 24.174 0 0 0 2.148-3.128 24.038 24.038 0 0 0 1.976-4.375l.443.148a24.48 24.48 0 0 1-2.015 4.461 24.662 24.662 0 0 1-2.189 3.188M114 237.476V182.584 237.476' fill='#607D8B'/><g><path d='M81.822 37.474c.017-.135-.075-.28-.267-.392-.327-.188-.826-.21-1.109-.045l-6.012 3.471c-.131.076-.194.178-.191.285.002.132.002.461.002.578v.043l-.007.128-6.591 3.779c-.001 0-2.077 1.046-2.787 5.192 0 0-.912 6.961-.898 19.745.015 12.57.606 17.07 1.167 21.351.22 1.684 3.001 2.125 3.001 2.125.331.04.698-.027 1.08-.248l75.273-43.551c1.808-1.069 2.667-3.719 3.056-6.284 1.213-7.99 1.675-32.978-.275-39.878-.196-.693-.51-1.083-.868-1.282l-2.086-.79c-.727.028-1.416.467-1.534.535L82.032 37.072l-.21.402' fill='#FFF'/><path d='M144.311 1.701l2.085.79c.358.199.672.589.868 1.282 1.949 6.9 1.487 31.887.275 39.878-.39 2.565-1.249 5.215-3.056 6.284L69.21 93.486a1.78 1.78 0 0 1-.896.258l-.183-.011c0 .001-2.782-.44-3.003-2.124-.56-4.282-1.151-8.781-1.165-21.351-.015-12.784.897-19.745.897-19.745.71-4.146 2.787-5.192 2.787-5.192l6.591-3.779.007-.128v-.043c0-.117 0-.446-.002-.578-.003-.107.059-.21.191-.285l6.012-3.472a.98.98 0 0 1 .481-.11c.218 0 .449.053.627.156.193.112.285.258.268.392l.211-.402 60.744-34.836c.117-.068.806-.507 1.534-.535m0-.997l-.039.001c-.618.023-1.283.244-1.974.656l-.021.012-60.519 34.706a2.358 2.358 0 0 0-.831-.15c-.365 0-.704.084-.98.244l-6.012 3.471c-.442.255-.699.69-.689 1.166l.001.15-6.08 3.487c-.373.199-2.542 1.531-3.29 5.898l-.006.039c-.009.07-.92 7.173-.906 19.875.014 12.62.603 17.116 1.172 21.465l.002.015c.308 2.355 3.475 2.923 3.836 2.98l.034.004c.101.013.204.019.305.019a2.77 2.77 0 0 0 1.396-.392l75.273-43.552c1.811-1.071 2.999-3.423 3.542-6.997 1.186-7.814 1.734-33.096-.301-40.299-.253-.893-.704-1.527-1.343-1.882l-.132-.062-2.085-.789a.973.973 0 0 0-.353-.065' fill='#455A64'/><path d='M128.267 11.565l1.495.434-56.339 32.326' fill='#FFF'/><path d='M74.202 90.545a.5.5 0 0 1-.25-.931l18.437-10.645a.499.499 0 1 1 .499.864L74.451 90.478l-.249.067M75.764 42.654l-.108-.062.046-.171 5.135-2.964.17.045-.045.171-5.135 2.964-.063.017M70.52 90.375V46.421l.063-.036L137.84 7.554v43.954l-.062.036L70.52 90.375zm.25-43.811v43.38l66.821-38.579V7.985L70.77 46.564z' fill='#607D8B'/><path d='M86.986 83.182c-.23.149-.612.384-.849.523l-11.505 6.701c-.237.139-.206.252.068.252h.565c.275 0 .693-.113.93-.252L87.7 83.705c.237-.139.428-.253.425-.256a11.29 11.29 0 0 1-.006-.503c0-.274-.188-.377-.418-.227l-.715.463' fill='#607D8B'/><path d='M75.266 90.782H74.7c-.2 0-.316-.056-.346-.166-.03-.11.043-.217.215-.317l11.505-6.702c.236-.138.615-.371.844-.519l.715-.464a.488.488 0 0 1 .266-.089c.172 0 .345.13.345.421 0 .214.001.363.003.437l.006.004-.004.069c-.003.075-.003.075-.486.356l-11.505 6.702a2.282 2.282 0 0 1-.992.268zm-.6-.25l.034.001h.566c.252 0 .649-.108.866-.234l11.505-6.702c.168-.098.294-.173.361-.214-.004-.084-.004-.218-.004-.437l-.095-.171-.131.049-.714.463c-.232.15-.616.386-.854.525l-11.505 6.702-.029.018z' fill='#607D8B'/><path d='M75.266 89.871H74.7c-.2 0-.316-.056-.346-.166-.03-.11.043-.217.215-.317l11.505-6.702c.258-.151.694-.268.993-.268h.565c.2 0 .316.056.346.166.03.11-.043.217-.215.317l-11.505 6.702a2.282 2.282 0 0 1-.992.268zm-.6-.25l.034.001h.566c.252 0 .649-.107.866-.234l11.505-6.702.03-.018-.035-.001h-.565c-.252 0-.649.108-.867.234l-11.505 6.702-.029.018zM74.37 90.801v-1.247 1.247' fill='#607D8B'/><path d='M68.13 93.901c-.751-.093-1.314-.737-1.439-1.376-.831-4.238-1.151-8.782-1.165-21.352-.015-12.784.897-19.745.897-19.745.711-4.146 2.787-5.192 2.787-5.192l74.859-43.219c.223-.129 2.487-1.584 3.195.923 1.95 6.9 1.488 31.887.275 39.878-.389 2.565-1.248 5.215-3.056 6.283L69.21 93.653c-.382.221-.749.288-1.08.248 0 0-2.781-.441-3.001-2.125-.561-4.281-1.152-8.781-1.167-21.351-.014-12.784.898-19.745.898-19.745.71-4.146 2.787-5.191 2.787-5.191l6.598-3.81.871-.119 6.599-3.83.046-.461L68.13 93.901' fill='#FAFAFA'/><path d='M68.317 94.161l-.215-.013h-.001l-.244-.047c-.719-.156-2.772-.736-2.976-2.292-.568-4.34-1.154-8.813-1.168-21.384-.014-12.654.891-19.707.9-19.777.725-4.231 2.832-5.338 2.922-5.382l6.628-3.827.87-.119 6.446-3.742.034-.334a.248.248 0 0 1 .273-.223.248.248 0 0 1 .223.272l-.059.589-6.752 3.919-.87.118-6.556 3.785c-.031.016-1.99 1.068-2.666 5.018-.007.06-.908 7.086-.894 19.702.014 12.539.597 16.996 1.161 21.305.091.691.689 1.154 1.309 1.452a1.95 1.95 0 0 1-.236-.609c-.781-3.984-1.155-8.202-1.17-21.399-.014-12.653.891-19.707.9-19.777.725-4.231 2.832-5.337 2.922-5.382-.004.001 74.444-42.98 74.846-43.212l.028-.017c.904-.538 1.72-.688 2.36-.433.555.221.949.733 1.172 1.52 2.014 7.128 1.46 32.219.281 39.983-.507 3.341-1.575 5.515-3.175 6.462L69.335 93.869a2.023 2.023 0 0 1-1.018.292zm-.147-.507c.293.036.604-.037.915-.217l75.273-43.551c1.823-1.078 2.602-3.915 2.934-6.106 1.174-7.731 1.731-32.695-.268-39.772-.178-.631-.473-1.032-.876-1.192-.484-.193-1.166-.052-1.921.397l-.034.021-74.858 43.218c-.031.017-1.989 1.069-2.666 5.019-.007.059-.908 7.085-.894 19.702.015 13.155.386 17.351 1.161 21.303.09.461.476.983 1.037 1.139.114.025.185.037.196.039h.001z' fill='#455A64'/><path d='M69.317 68.982c.489-.281.885-.056.885.505 0 .56-.396 1.243-.885 1.525-.488.282-.884.057-.884-.504 0-.56.396-1.243.884-1.526' fill='#FFF'/><path d='M68.92 71.133c-.289 0-.487-.228-.487-.625 0-.56.396-1.243.884-1.526a.812.812 0 0 1 .397-.121c.289 0 .488.229.488.626 0 .56-.396 1.243-.885 1.525a.812.812 0 0 1-.397.121m.794-2.459a.976.976 0 0 0-.49.147c-.548.317-.978 1.058-.978 1.687 0 .486.271.812.674.812a.985.985 0 0 0 .491-.146c.548-.317.978-1.057.978-1.687 0-.486-.272-.813-.675-.813' fill='#8097A2'/><path d='M68.92 70.947c-.271 0-.299-.307-.299-.439 0-.491.361-1.116.79-1.363a.632.632 0 0 1 .303-.096c.272 0 .301.306.301.438 0 .491-.363 1.116-.791 1.364a.629.629 0 0 1-.304.096m.794-2.086a.812.812 0 0 0-.397.121c-.488.283-.884.966-.884 1.526 0 .397.198.625.487.625a.812.812 0 0 0 .397-.121c.489-.282.885-.965.885-1.525 0-.397-.199-.626-.488-.626' fill='#8097A2'/><path d='M69.444 85.35c.264-.152.477-.031.477.272 0 .303-.213.67-.477.822-.263.153-.477.031-.477-.271 0-.302.214-.671.477-.823' fill='#FFF'/><path d='M69.23 86.51c-.156 0-.263-.123-.263-.337 0-.302.214-.671.477-.823a.431.431 0 0 1 .214-.066c.156 0 .263.124.263.338 0 .303-.213.67-.477.822a.431.431 0 0 1-.214.066m.428-1.412c-.1 0-.203.029-.307.09-.32.185-.57.618-.57.985 0 .309.185.524.449.524a.63.63 0 0 0 .308-.09c.32-.185.57-.618.57-.985 0-.309-.185-.524-.45-.524' fill='#8097A2'/><path d='M69.23 86.322l-.076-.149c0-.235.179-.544.384-.661l.12-.041.076.151c0 .234-.179.542-.383.66l-.121.04m.428-1.038a.431.431 0 0 0-.214.066c-.263.152-.477.521-.477.823 0 .214.107.337.263.337a.431.431 0 0 0 .214-.066c.264-.152.477-.519.477-.822 0-.214-.107-.338-.263-.338' fill='#8097A2'/><path d='M139.278 7.769v43.667L72.208 90.16V46.493l67.07-38.724' fill='#455A64'/><path d='M72.083 90.375V46.421l.063-.036 67.257-38.831v43.954l-.062.036-67.258 38.831zm.25-43.811v43.38l66.821-38.579V7.985L72.333 46.564z' fill='#607D8B'/></g><path d='M125.737 88.647l-7.639 3.334V84l-11.459 4.713v8.269L99 100.315l13.369 3.646 13.368-15.314' fill='#455A64'/></g></svg>")};var se="WEBVR_CARDBOARD_VIEWER";function ae(e){try{this.selectedKey=localStorage.getItem(se)}catch(e){console.error("Failed to load viewer profile: %s",e)}this.selectedKey||(this.selectedKey=e||"CardboardV1"),this.dialog=this.createDialog_(X.Viewers),this.root=null,this.onChangeCallbacks_=[]}ae.prototype.show=function(e){this.root=e,e.appendChild(this.dialog),this.dialog.querySelector("#"+this.selectedKey).checked=!0,this.dialog.style.display="block"},ae.prototype.hide=function(){this.root&&this.root.contains(this.dialog)&&this.root.removeChild(this.dialog),this.dialog.style.display="none"},ae.prototype.getCurrentViewer=function(){return X.Viewers[this.selectedKey]},ae.prototype.getSelectedKey_=function(){var e=this.dialog.querySelector("input[name=field]:checked");return e?e.id:null},ae.prototype.onChange=function(e){this.onChangeCallbacks_.push(e)},ae.prototype.fireOnChange_=function(e){for(var t=0;t<this.onChangeCallbacks_.length;t++)this.onChangeCallbacks_[t](e)},ae.prototype.onSave_=function(){if(this.selectedKey=this.getSelectedKey_(),this.selectedKey&&X.Viewers[this.selectedKey]){this.fireOnChange_(X.Viewers[this.selectedKey]);try{localStorage.setItem(se,this.selectedKey)}catch(e){console.error("Failed to save viewer profile: %s",e)}this.hide()}else console.error("ViewerSelector.onSave_: this should never happen!")},ae.prototype.createDialog_=function(e){var t=document.createElement("div");t.classList.add("webvr-polyfill-viewer-selector"),t.style.display="none";var n=document.createElement("div");(r=n.style).position="fixed",r.left=0,r.top=0,r.width="100%",r.height="100%",r.background="rgba(0, 0, 0, 0.3)",n.addEventListener("click",this.hide.bind(this));var i=document.createElement("div"),r=i.style;for(var o in r.boxSizing="border-box",r.position="fixed",r.top="24px",r.left="50%",r.marginLeft="-140px",r.width="280px",r.padding="24px",r.overflow="hidden",r.background="#fafafa",r.fontFamily="'Roboto', sans-serif",r.boxShadow="0px 5px 20px #666",i.appendChild(this.createH1_("Select your viewer")),e)i.appendChild(this.createChoice_(o,e[o].label));return i.appendChild(this.createButton_("Save",this.onSave_.bind(this))),t.appendChild(n),t.appendChild(i),t},ae.prototype.createH1_=function(e){var t=document.createElement("h1"),n=t.style;return n.color="black",n.fontSize="20px",n.fontWeight="bold",n.marginTop=0,n.marginBottom="24px",t.innerHTML=e,t},ae.prototype.createChoice_=function(e,t){var n=document.createElement("div");n.style.marginTop="8px",n.style.color="black";var i=document.createElement("input");i.style.fontSize="30px",i.setAttribute("id",e),i.setAttribute("type","radio"),i.setAttribute("value",e),i.setAttribute("name","field");var r=document.createElement("label");return r.style.marginLeft="4px",r.setAttribute("for",e),r.innerHTML=t,n.appendChild(i),n.appendChild(r),n},ae.prototype.createButton_=function(e,t){var n=document.createElement("button");n.innerHTML=e;var i=n.style;return i.float="right",i.textTransform="uppercase",i.color="#1094f7",i.fontSize="14px",i.letterSpacing=0,i.border=0,i.background="none",i.marginTop="16px",n.addEventListener("click",t),n},"undefined"!=typeof window?window:void 0!==Se||"undefined"!=typeof self&&self;var ue,le,ce=(function(e,t){var n;n=function(){return function(e){var t={};function n(i){if(t[i])return t[i].exports;var r=t[i]={i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:i})},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(1),o="undefined"!=typeof navigator&&parseFloat((""+(/CPU.*OS ([0-9_]{3,4})[0-9_]{0,1}|(CPU like).*AppleWebKit.*Mobile/i.exec(navigator.userAgent)||[0,""])[1]).replace("undefined","3_2").replace("_",".").replace("_",""))<10&&!window.MSStream,s=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),o?this.noSleepTimer=null:(this.noSleepVideo=document.createElement("video"),this.noSleepVideo.setAttribute("playsinline",""),this.noSleepVideo.setAttribute("src",r),this.noSleepVideo.addEventListener("timeupdate",function(e){this.noSleepVideo.currentTime>.5&&(this.noSleepVideo.currentTime=Math.random())}.bind(this)))}return i(e,[{key:"enable",value:function(){o?(this.disable(),this.noSleepTimer=window.setInterval((function(){window.location.href="/",window.setTimeout(window.stop,0)}),15e3)):this.noSleepVideo.play()}},{key:"disable",value:function(){o?this.noSleepTimer&&(window.clearInterval(this.noSleepTimer),this.noSleepTimer=null):this.noSleepVideo.pause()}}]),e}();e.exports=s},function(e,t,n){e.exports="data:video/mp4;base64,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"}])},e.exports=n()}(le={exports:{}}),(ue=le.exports)&&ue.__esModule&&Object.prototype.hasOwnProperty.call(ue,"default")?ue.default:ue),he=1e3,de=[0,0,.5,1],pe=[.5,0,.5,1],fe=window.requestAnimationFrame,Ae=window.cancelAnimationFrame;function ge(e){Object.defineProperties(this,{hasPosition:{writable:!1,enumerable:!0,value:e.hasPosition},hasExternalDisplay:{writable:!1,enumerable:!0,value:e.hasExternalDisplay},canPresent:{writable:!1,enumerable:!0,value:e.canPresent},maxLayers:{writable:!1,enumerable:!0,value:e.maxLayers},hasOrientation:{enumerable:!0,get:function(){return T("VRDisplayCapabilities.prototype.hasOrientation","VRDisplay.prototype.getFrameData"),e.hasOrientation}}})}function ve(e){var t=!("wakelock"in(e=e||{}))||e.wakelock;this.isPolyfilled=!0,this.displayId=he++,this.displayName="",this.depthNear=.01,this.depthFar=1e4,this.isPresenting=!1,Object.defineProperty(this,"isConnected",{get:function(){return T("VRDisplay.prototype.isConnected","VRDisplayCapabilities.prototype.hasExternalDisplay"),!1}}),this.capabilities=new ge({hasPosition:!1,hasOrientation:!1,hasExternalDisplay:!1,canPresent:!1,maxLayers:1}),this.stageParameters=null,this.waitingForPresent_=!1,this.layer_=null,this.originalParent_=null,this.fullscreenElement_=null,this.fullscreenWrapper_=null,this.fullscreenElementCachedStyle_=null,this.fullscreenEventTarget_=null,this.fullscreenChangeHandler_=null,this.fullscreenErrorHandler_=null,t&&y()&&(this.wakelock_=new ce)}ve.prototype.getFrameData=function(e){return b(e,this._getPose(),this)},ve.prototype.getPose=function(){return T("VRDisplay.prototype.getPose","VRDisplay.prototype.getFrameData"),this._getPose()},ve.prototype.resetPose=function(){return T("VRDisplay.prototype.resetPose"),this._resetPose()},ve.prototype.getImmediatePose=function(){return T("VRDisplay.prototype.getImmediatePose","VRDisplay.prototype.getFrameData"),this._getPose()},ve.prototype.requestAnimationFrame=function(e){return fe(e)},ve.prototype.cancelAnimationFrame=function(e){return Ae(e)},ve.prototype.wrapForFullscreen=function(e){if(o())return e;if(!this.fullscreenWrapper_){this.fullscreenWrapper_=document.createElement("div");var t=["height: "+Math.min(screen.height,screen.width)+"px !important","top: 0 !important","left: 0 !important","right: 0 !important","border: 0","margin: 0","padding: 0","z-index: 999999 !important","position: fixed"];this.fullscreenWrapper_.setAttribute("style",t.join("; ")+";"),this.fullscreenWrapper_.classList.add("webvr-polyfill-fullscreen-wrapper")}if(this.fullscreenElement_==e)return this.fullscreenWrapper_;if(this.fullscreenElement_&&(this.originalParent_?this.originalParent_.appendChild(this.fullscreenElement_):this.fullscreenElement_.parentElement.removeChild(this.fullscreenElement_)),this.fullscreenElement_=e,this.originalParent_=e.parentElement,this.originalParent_||document.body.appendChild(e),!this.fullscreenWrapper_.parentElement){var n=this.fullscreenElement_.parentElement;n.insertBefore(this.fullscreenWrapper_,this.fullscreenElement_),n.removeChild(this.fullscreenElement_)}this.fullscreenWrapper_.insertBefore(this.fullscreenElement_,this.fullscreenWrapper_.firstChild),this.fullscreenElementCachedStyle_=this.fullscreenElement_.getAttribute("style");var i=this;return function(){if(i.fullscreenElement_){var e=["position: absolute","top: 0","left: 0","width: "+Math.max(screen.width,screen.height)+"px","height: "+Math.min(screen.height,screen.width)+"px","border: 0","margin: 0","padding: 0"];i.fullscreenElement_.setAttribute("style",e.join("; ")+";")}}(),this.fullscreenWrapper_},ve.prototype.removeFullscreenWrapper=function(){if(this.fullscreenElement_){var e=this.fullscreenElement_;this.fullscreenElementCachedStyle_?e.setAttribute("style",this.fullscreenElementCachedStyle_):e.removeAttribute("style"),this.fullscreenElement_=null,this.fullscreenElementCachedStyle_=null;var t=this.fullscreenWrapper_.parentElement;return this.fullscreenWrapper_.removeChild(e),this.originalParent_===t?t.insertBefore(e,this.fullscreenWrapper_):this.originalParent_&&this.originalParent_.appendChild(e),t.removeChild(this.fullscreenWrapper_),e}},ve.prototype.requestPresent=function(e){var t=this.isPresenting,n=this;return e instanceof Array||(T("VRDisplay.prototype.requestPresent with non-array argument","an array of VRLayers as the first argument"),e=[e]),new Promise((function(i,r){if(n.capabilities.canPresent)if(0==e.length||e.length>n.capabilities.maxLayers)r(new Error("Invalid number of layers."));else{var a=e[0];if(a.source){var u=a.leftBounds||de,l=a.rightBounds||pe;if(t){var c=n.layer_;c.source!==a.source&&(c.source=a.source);for(var h=0;h<4;h++)c.leftBounds[h]=u[h],c.rightBounds[h]=l[h];return n.wrapForFullscreen(n.layer_.source),n.updatePresent_(),void i()}if(n.layer_={predistorted:a.predistorted,source:a.source,leftBounds:u.slice(0),rightBounds:l.slice(0)},n.waitingForPresent_=!1,n.layer_&&n.layer_.source){var d=n.wrapForFullscreen(n.layer_.source);n.addFullscreenListeners_(d,(function(){var e=document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement;n.isPresenting=d===e,n.isPresenting?(screen.orientation&&screen.orientation.lock&&screen.orientation.lock("landscape-primary").catch((function(e){console.error("screen.orientation.lock() failed due to",e.message)})),n.waitingForPresent_=!1,n.beginPresent_(),i()):(screen.orientation&&screen.orientation.unlock&&screen.orientation.unlock(),n.removeFullscreenWrapper(),n.disableWakeLock(),n.endPresent_(),n.removeFullscreenListeners_()),n.fireVRDisplayPresentChange_()}),(function(){n.waitingForPresent_&&(n.removeFullscreenWrapper(),n.removeFullscreenListeners_(),n.disableWakeLock(),n.waitingForPresent_=!1,n.isPresenting=!1,r(new Error("Unable to present.")))})),function(e){if(s())return!1;if(e.requestFullscreen)e.requestFullscreen();else if(e.webkitRequestFullscreen)e.webkitRequestFullscreen();else if(e.mozRequestFullScreen)e.mozRequestFullScreen();else{if(!e.msRequestFullscreen)return!1;e.msRequestFullscreen()}return!0}(d)?(n.enableWakeLock(),n.waitingForPresent_=!0):(o()||s())&&(n.enableWakeLock(),n.isPresenting=!0,n.beginPresent_(),n.fireVRDisplayPresentChange_(),i())}n.waitingForPresent_||o()||(g(),r(new Error("Unable to present.")))}else i()}else r(new Error("VRDisplay is not capable of presenting."))}))},ve.prototype.exitPresent=function(){var e=this.isPresenting,t=this;return this.isPresenting=!1,this.layer_=null,this.disableWakeLock(),new Promise((function(n,i){e?(!g()&&o()&&(t.endPresent_(),t.fireVRDisplayPresentChange_()),s()&&(t.removeFullscreenWrapper(),t.removeFullscreenListeners_(),t.endPresent_(),t.fireVRDisplayPresentChange_()),n()):i(new Error("Was not presenting to VRDisplay."))}))},ve.prototype.getLayers=function(){return this.layer_?[this.layer_]:[]},ve.prototype.fireVRDisplayPresentChange_=function(){var e=new CustomEvent("vrdisplaypresentchange",{detail:{display:this}});window.dispatchEvent(e)},ve.prototype.fireVRDisplayConnect_=function(){var e=new CustomEvent("vrdisplayconnect",{detail:{display:this}});window.dispatchEvent(e)},ve.prototype.addFullscreenListeners_=function(e,t,n){this.removeFullscreenListeners_(),this.fullscreenEventTarget_=e,this.fullscreenChangeHandler_=t,this.fullscreenErrorHandler_=n,t&&(document.fullscreenEnabled?e.addEventListener("fullscreenchange",t,!1):document.webkitFullscreenEnabled?e.addEventListener("webkitfullscreenchange",t,!1):document.mozFullScreenEnabled?document.addEventListener("mozfullscreenchange",t,!1):document.msFullscreenEnabled&&e.addEventListener("msfullscreenchange",t,!1)),n&&(document.fullscreenEnabled?e.addEventListener("fullscreenerror",n,!1):document.webkitFullscreenEnabled?e.addEventListener("webkitfullscreenerror",n,!1):document.mozFullScreenEnabled?document.addEventListener("mozfullscreenerror",n,!1):document.msFullscreenEnabled&&e.addEventListener("msfullscreenerror",n,!1))},ve.prototype.removeFullscreenListeners_=function(){if(this.fullscreenEventTarget_){var e=this.fullscreenEventTarget_;if(this.fullscreenChangeHandler_){var t=this.fullscreenChangeHandler_;e.removeEventListener("fullscreenchange",t,!1),e.removeEventListener("webkitfullscreenchange",t,!1),document.removeEventListener("mozfullscreenchange",t,!1),e.removeEventListener("msfullscreenchange",t,!1)}if(this.fullscreenErrorHandler_){var n=this.fullscreenErrorHandler_;e.removeEventListener("fullscreenerror",n,!1),e.removeEventListener("webkitfullscreenerror",n,!1),document.removeEventListener("mozfullscreenerror",n,!1),e.removeEventListener("msfullscreenerror",n,!1)}this.fullscreenEventTarget_=null,this.fullscreenChangeHandler_=null,this.fullscreenErrorHandler_=null}},ve.prototype.enableWakeLock=function(){this.wakelock_&&this.wakelock_.enable()},ve.prototype.disableWakeLock=function(){this.wakelock_&&this.wakelock_.disable()},ve.prototype.beginPresent_=function(){},ve.prototype.endPresent_=function(){},ve.prototype.submitFrame=function(e){},ve.prototype.getEyeParameters=function(e){return null};var me={ADDITIONAL_VIEWERS:[],DEFAULT_VIEWER:"",MOBILE_WAKE_LOCK:!0,DEBUG:!1,DPDB_URL:"https://dpdb.webvr.rocks/dpdb.json",K_FILTER:.98,PREDICTION_TIME_S:.04,CARDBOARD_UI_DISABLED:!1,ROTATE_INSTRUCTIONS_DISABLED:!1,YAW_ONLY:!1,BUFFER_SCALE:.5,DIRTY_SUBMIT_FRAME_BINDINGS:!1},ye="left",_e="right";function we(e){var t=_({},me);e=_(t,e||{}),ve.call(this,{wakelock:e.MOBILE_WAKE_LOCK}),this.config=e,this.displayName="Cardboard VRDisplay",this.capabilities=new ge({hasPosition:!1,hasOrientation:!0,hasExternalDisplay:!1,canPresent:!0,maxLayers:1}),this.stageParameters=null,this.bufferScale_=this.config.BUFFER_SCALE,this.poseSensor_=new re(this.config),this.distorter_=null,this.cardboardUI_=null,this.dpdb_=new Y(this.config.DPDB_URL,this.onDeviceParamsUpdated_.bind(this)),this.deviceInfo_=new X(this.dpdb_.getDeviceParams(),e.ADDITIONAL_VIEWERS),this.viewerSelector_=new ae(e.DEFAULT_VIEWER),this.viewerSelector_.onChange(this.onViewerChanged_.bind(this)),this.deviceInfo_.setViewer(this.viewerSelector_.getCurrentViewer()),this.config.ROTATE_INSTRUCTIONS_DISABLED||(this.rotateInstructions_=new oe),o()&&window.addEventListener("resize",this.onResize_.bind(this))}return we.prototype=Object.create(ve.prototype),we.prototype._getPose=function(){return{position:null,orientation:this.poseSensor_.getOrientation(),linearVelocity:null,linearAcceleration:null,angularVelocity:null,angularAcceleration:null}},we.prototype._resetPose=function(){this.poseSensor_.resetPose&&this.poseSensor_.resetPose()},we.prototype._getFieldOfView=function(e){var t;if(e==ye)t=this.deviceInfo_.getFieldOfViewLeftEye();else{if(e!=_e)return console.error("Invalid eye provided: %s",e),null;t=this.deviceInfo_.getFieldOfViewRightEye()}return t},we.prototype._getEyeOffset=function(e){var t;if(e==ye)t=[.5*-this.deviceInfo_.viewer.interLensDistance,0,0];else{if(e!=_e)return console.error("Invalid eye provided: %s",e),null;t=[.5*this.deviceInfo_.viewer.interLensDistance,0,0]}return t},we.prototype.getEyeParameters=function(e){var t=this._getEyeOffset(e),n=this._getFieldOfView(e),i={offset:t,renderWidth:.5*this.deviceInfo_.device.width*this.bufferScale_,renderHeight:this.deviceInfo_.device.height*this.bufferScale_};return Object.defineProperty(i,"fieldOfView",{enumerable:!0,get:function(){return T("VRFieldOfView","VRFrameData's projection matrices"),n}}),i},we.prototype.onDeviceParamsUpdated_=function(e){this.config.DEBUG&&console.log("DPDB reported that device params were updated."),this.deviceInfo_.updateDeviceParams(e),this.distorter_&&this.distorter_.updateDeviceInfo(this.deviceInfo_)},we.prototype.updateBounds_=function(){this.layer_&&this.distorter_&&(this.layer_.leftBounds||this.layer_.rightBounds)&&this.distorter_.setTextureBounds(this.layer_.leftBounds,this.layer_.rightBounds)},we.prototype.beginPresent_=function(){var e=this.layer_.source.getContext("webgl");e||(e=this.layer_.source.getContext("experimental-webgl")),e||(e=this.layer_.source.getContext("webgl2")),e&&(this.layer_.predistorted?this.config.CARDBOARD_UI_DISABLED||(e.canvas.width=f()*this.bufferScale_,e.canvas.height=A()*this.bufferScale_,this.cardboardUI_=new O(e)):(this.config.CARDBOARD_UI_DISABLED||(this.cardboardUI_=new O(e)),this.distorter_=new C(e,this.cardboardUI_,this.config.BUFFER_SCALE,this.config.DIRTY_SUBMIT_FRAME_BINDINGS),this.distorter_.updateDeviceInfo(this.deviceInfo_)),this.cardboardUI_&&this.cardboardUI_.listen(function(e){this.viewerSelector_.show(this.layer_.source.parentElement),e.stopPropagation(),e.preventDefault()}.bind(this),function(e){this.exitPresent(),e.stopPropagation(),e.preventDefault()}.bind(this)),this.rotateInstructions_&&(p()&&y()?this.rotateInstructions_.showTemporarily(3e3,this.layer_.source.parentElement):this.rotateInstructions_.update()),this.orientationHandler=this.onOrientationChange_.bind(this),window.addEventListener("orientationchange",this.orientationHandler),this.vrdisplaypresentchangeHandler=this.updateBounds_.bind(this),window.addEventListener("vrdisplaypresentchange",this.vrdisplaypresentchangeHandler),this.fireVRDisplayDeviceParamsChange_())},we.prototype.endPresent_=function(){this.distorter_&&(this.distorter_.destroy(),this.distorter_=null),this.cardboardUI_&&(this.cardboardUI_.destroy(),this.cardboardUI_=null),this.rotateInstructions_&&this.rotateInstructions_.hide(),this.viewerSelector_.hide(),window.removeEventListener("orientationchange",this.orientationHandler),window.removeEventListener("vrdisplaypresentchange",this.vrdisplaypresentchangeHandler)},we.prototype.updatePresent_=function(){this.endPresent_(),this.beginPresent_()},we.prototype.submitFrame=function(e){if(this.distorter_)this.updateBounds_(),this.distorter_.submitFrame();else if(this.cardboardUI_&&this.layer_){var t=this.layer_.source.getContext("webgl");t||(t=this.layer_.source.getContext("experimental-webgl")),t||(t=this.layer_.source.getContext("webgl2"));var n=t.canvas;n.width==this.lastWidth&&n.height==this.lastHeight||this.cardboardUI_.onResize(),this.lastWidth=n.width,this.lastHeight=n.height,this.cardboardUI_.render()}},we.prototype.onOrientationChange_=function(e){this.viewerSelector_.hide(),this.rotateInstructions_&&this.rotateInstructions_.update(),this.onResize_()},we.prototype.onResize_=function(e){if(this.layer_){var t=this.layer_.source.getContext("webgl");t||(t=this.layer_.source.getContext("experimental-webgl")),t||(t=this.layer_.source.getContext("webgl2")),t.canvas.setAttribute("style",["position: absolute","top: 0","left: 0","width: 100vw","height: 100vh","border: 0","margin: 0","padding: 0px","box-sizing: content-box"].join("; ")+";"),w(t.canvas)}},we.prototype.onViewerChanged_=function(e){this.deviceInfo_.setViewer(e),this.distorter_&&this.distorter_.updateDeviceInfo(this.deviceInfo_),this.fireVRDisplayDeviceParamsChange_()},we.prototype.fireVRDisplayDeviceParamsChange_=function(){var e=new CustomEvent("vrdisplaydeviceparamschange",{detail:{vrdisplay:this,deviceInfo:this.deviceInfo_}});window.dispatchEvent(e)},we.VRFrameData=function(){this.leftProjectionMatrix=new Float32Array(16),this.leftViewMatrix=new Float32Array(16),this.rightProjectionMatrix=new Float32Array(16),this.rightViewMatrix=new Float32Array(16),this.pose=null},we.VRDisplay=ve,we}()}(Ee={exports:{}}),Ee.exports))&&Te.__esModule&&Object.prototype.hasOwnProperty.call(Te,"default")?Te.default:Te;class Me extends i{constructor(e){super(),this.global=e,this.onWindowResize=this.onWindowResize.bind(this),this.global.window.addEventListener("resize",this.onWindowResize),this.environmentBlendMode="opaque"}onBaseLayerSet(e,t){throw new Error("Not implemented")}isSessionSupported(e){throw new Error("Not implemented")}isFeatureSupported(e){throw new Error("Not implemented")}async requestSession(e,t){throw new Error("Not implemented")}requestAnimationFrame(e){throw new Error("Not implemented")}onFrameStart(e){throw new Error("Not implemented")}onFrameEnd(e){throw new Error("Not implemented")}doesSessionSupportReferenceSpace(e,t){throw new Error("Not implemented")}requestStageBounds(){throw new Error("Not implemented")}async requestFrameOfReferenceTransform(e,t){}cancelAnimationFrame(e){throw new Error("Not implemented")}endSession(e){throw new Error("Not implemented")}getViewport(e,t,n,i){throw new Error("Not implemented")}getProjectionMatrix(e){throw new Error("Not implemented")}getBasePoseMatrix(){throw new Error("Not implemented")}getBaseViewMatrix(e){throw new Error("Not implemented")}getInputSources(){throw new Error("Not implemented")}getInputPose(e,t,n){throw new Error("Not implemented")}onWindowResize(){this.onWindowResize()}}let Ce={mapping:"xr-standard",profiles:["oculus-go","generic-trigger-touchpad"],buttons:{length:3,0:1,1:null,2:0},gripTransform:{orientation:[.11*Math.PI,0,0,1]}},Pe={mapping:"xr-standard",displayProfiles:{"Oculus Quest":["oculus-touch-v2","oculus-touch","generic-trigger-squeeze-thumbstick"]},profiles:["oculus-touch","generic-trigger-squeeze-thumbstick"],axes:{length:4,0:null,1:null,2:0,3:1},buttons:{length:7,0:1,1:2,2:null,3:0,4:3,5:4,6:null},gripTransform:{position:[0,-.02,.04,1],orientation:[.11*Math.PI,0,0,1]}},Fe={mapping:"xr-standard",profiles:["htc-vive","generic-trigger-squeeze-touchpad"],displayProfiles:{"HTC Vive":["htc-vive","generic-trigger-squeeze-touchpad"],"HTC Vive DVT":["htc-vive","generic-trigger-squeeze-touchpad"],"Valve Index":["valve-index","generic-trigger-squeeze-touchpad-thumbstick"]},buttons:{length:3,0:1,1:2,2:0},gripTransform:{position:[0,0,.05,1]},targetRayTransform:{orientation:[-.08*Math.PI,0,0,1]},userAgentOverrides:{Firefox:{axes:{invert:[1,3]}}}},De={mapping:"xr-standard",profiles:["samsung-gearvr","generic-trigger-touchpad"],buttons:{length:3,0:1,1:null,2:0},gripTransform:{orientation:[.11*Math.PI,0,0,1]}},Ie={mapping:"xr-standard",profiles:["samsung-odyssey","microsoft-mixed-reality","generic-trigger-squeeze-touchpad-thumbstick"],buttons:{length:4,0:1,1:0,2:2,3:4},gripTransform:{position:[0,-.02,.04,1],orientation:[.11*Math.PI,0,0,1]}},Oe={mapping:"xr-standard",profiles:["microsoft-mixed-reality","generic-trigger-squeeze-touchpad-thumbstick"],buttons:{length:4,0:1,1:0,2:2,3:4},gripTransform:{position:[0,-.02,.04,1],orientation:[.11*Math.PI,0,0,1]}},Be={"Daydream Controller":{mapping:"",profiles:["google-daydream","generic-trigger-touchpad"],buttons:{length:3,0:null,1:null,2:0}},"Gear VR Controller":De,"HTC Vive Focus Controller":{mapping:"xr-standard",profiles:["htc-vive-focus","generic-trigger-touchpad"],buttons:{length:3,0:1,1:null,2:0}},"Oculus Go Controller":Ce,"Oculus Touch (Right)":Pe,"Oculus Touch (Left)":Pe,"OpenVR Gamepad":Fe,"Spatial Controller (Spatial Interaction Source) 045E-065A":Oe,"Spatial Controller (Spatial Interaction Source) 045E-065D":Ie,"Windows Mixed Reality (Right)":Oe,"Windows Mixed Reality (Left)":Oe};const Le=A(.155,-.465,-.15),Ve=A(-.155,-.465,-.15),ke=A(0,0,-.25),Ne=A(0,0,.05),Qe=A(-.08,.14,.08),Ue=180/Math.PI;class We{constructor(){this.hand="right",this.headElbowOffset=Le,this.controllerQ=b(),this.lastControllerQ=b(),this.headQ=b(),this.headPos=p(),this.elbowPos=p(),this.wristPos=p(),this.time=null,this.lastTime=null,this.rootQ=b(),this.position=p()}setHandedness(e){this.hand!=e&&(this.hand=e,"left"==this.hand?this.headElbowOffset=Ve:this.headElbowOffset=Le)}update(e,t){this.time=Q(),e&&(R(this.lastControllerQ,this.controllerQ),R(this.controllerQ,e)),t&&(c(this.headPos,t),h(this.headQ,t));let n=this.getHeadYawOrientation_(),i=this.quatAngle_(this.lastControllerQ,this.controllerQ);i/((this.time-this.lastTime)/1e3)>.61?E(this.rootQ,this.rootQ,n,Math.min(i/.175,1)):R(this.rootQ,n);let r=A(0,0,-1);w(r,r,this.controllerQ);let o=_(r,[0,1,0]),s=this.clamp_((o-.12)/.87,0,1),a=S(this.rootQ);T(a,a),x(a,a,this.controllerQ);let u=this.elbowPos;g(u,this.headPos),v(u,u,this.headElbowOffset);let l=f(Qe);m(l,l,s),v(u,u,l);let d=this.quatAngle_(a,b())*Ue,p=(1-Math.pow(d/180,4))*(.4+.6*s*.4),y=b();E(y,y,a,p);let M=T(b(),y),C=S(a);x(C,C,M);let P=this.wristPos;g(P,Ne),w(P,P,y),v(P,P,ke),w(P,P,C),v(P,P,u);let F=f(Qe);m(F,F,s),v(this.position,this.wristPos,F),w(this.position,this.position,this.rootQ),this.lastTime=this.time}getPosition(){return this.position}getHeadYawOrientation_(){let e=p();return function(e,t,n){function i(e,t,n){return e<t?t:e>n?n:e}var r=t[0]*t[0],o=t[1]*t[1],s=t[2]*t[2],a=t[3]*t[3];e[0]=Math.asin(i(2*(t[0]*t[3]-t[1]*t[2]),-1,1)),e[1]=Math.atan2(2*(t[0]*t[2]+t[1]*t[3]),a-r-o+s),e[2]=Math.atan2(2*(t[0]*t[1]+t[2]*t[3]),a-r+o-s)}(e,this.headQ),function(e,t,n,i){let r=.5*Math.PI/180;t*=r,n*=r,i*=r;let o=Math.sin(t),s=Math.cos(t),a=Math.sin(n),u=Math.cos(n),l=Math.sin(i),c=Math.cos(i);return e[0]=o*u*c-s*a*l,e[1]=s*a*c+o*u*l,e[2]=s*u*l-o*a*c,e[3]=s*u*c+o*a*l,e}(b(),0,e[1]*Ue,0)}clamp_(e,t,n){return Math.min(Math.max(e,t),n)}quatAngle_(e,t){let n=[0,0,-1],i=[0,0,-1];return w(n,n,e),w(i,i,t),function(e,t){let n=A(e[0],e[1],e[2]),i=A(t[0],t[1],t[2]);y(n,n),y(i,i);let r=_(n,i);return r>1?0:r<-1?Math.PI:Math.acos(r)}(n,i)}}const Ge=Symbol("@@webxr-polyfill/XRRemappedGamepad"),je={pressed:!1,touched:!1,value:0};Object.freeze(je);class ze{constructor(e,t,n){if(n||(n={}),n.userAgentOverrides)for(let e in n.userAgentOverrides)if(navigator.userAgent.includes(e)){let t=n.userAgentOverrides[e];for(let e in t)e in n?Object.assign(n[e],t[e]):n[e]=t[e];break}let i=new Array(n.axes&&n.axes.length?n.axes.length:e.axes.length),r=new Array(n.buttons&&n.buttons.length?n.buttons.length:e.buttons.length),s=null;if(n.gripTransform){let e=n.gripTransform.orientation||[0,0,0,1];l(s=o(),M(e,e),n.gripTransform.position||[0,0,0])}let a=null;if(n.targetRayTransform){let e=n.targetRayTransform.orientation||[0,0,0,1];l(a=o(),M(e,e),n.targetRayTransform.position||[0,0,0])}let u=n.profiles;n.displayProfiles&&t.displayName in n.displayProfiles&&(u=n.displayProfiles[t.displayName]),this[Ge]={gamepad:e,map:n,profiles:u||[e.id],mapping:n.mapping||e.mapping,axes:i,buttons:r,gripTransform:s,targetRayTransform:a},this._update()}_update(){let e=this[Ge].gamepad,t=this[Ge].map,n=this[Ge].axes;for(let i=0;i<n.length;++i)t.axes&&i in t.axes?null===t.axes[i]?n[i]=0:n[i]=e.axes[t.axes[i]]:n[i]=e.axes[i];if(t.axes&&t.axes.invert)for(let e of t.axes.invert)e<n.length&&(n[e]*=-1);let i=this[Ge].buttons;for(let n=0;n<i.length;++n)t.buttons&&n in t.buttons?null===t.buttons[n]?i[n]=je:i[n]=e.buttons[t.buttons[n]]:i[n]=e.buttons[n]}get id(){return""}get _profiles(){return this[Ge].profiles}get index(){return-1}get connected(){return this[Ge].gamepad.connected}get timestamp(){return this[Ge].gamepad.timestamp}get mapping(){return this[Ge].mapping}get axes(){return this[Ge].axes}get buttons(){return this[Ge].buttons}get hapticActuators(){return this[Ge].gamepad.hapticActuators}}class Xe{constructor(e,t,n=0,i=-1){this.polyfill=e,this.display=t,this.nativeGamepad=null,this.gamepad=null,this.inputSource=new ve(this),this.lastPosition=p(),this.emulatedPosition=!1,this.basePoseMatrix=o(),this.outputMatrix=o(),this.primaryButtonIndex=n,this.primaryActionPressed=!1,this.primarySqueezeButtonIndex=i,this.primarySqueezeActionPressed=!1,this.handedness="",this.targetRayMode="gaze",this.armModel=null}get profiles(){return this.gamepad?this.gamepad._profiles:[]}updateFromGamepad(e){this.nativeGamepad!==e&&(this.nativeGamepad=e,this.gamepad=e?new ze(e,this.display,Be[e.id]):null),this.handedness=""===e.hand?"none":e.hand,this.gamepad&&this.gamepad._update(),e.pose?(this.targetRayMode="tracked-pointer",this.emulatedPosition=!e.pose.hasPosition):""===e.hand&&(this.targetRayMode="gaze",this.emulatedPosition=!1)}updateBasePoseMatrix(){if(this.nativeGamepad&&this.nativeGamepad.pose){let e=this.nativeGamepad.pose,t=e.position,n=e.orientation;if(!t&&!n)return;t?(this.lastPosition[0]=t[0],this.lastPosition[1]=t[1],this.lastPosition[2]=t[2]):e.hasPosition?t=this.lastPosition:(this.armModel||(this.armModel=new We),this.armModel.setHandedness(this.nativeGamepad.hand),this.armModel.update(n,this.polyfill.getBasePoseMatrix()),t=this.armModel.getPosition()),l(this.basePoseMatrix,n,t)}else e=this.basePoseMatrix,t=this.polyfill.getBasePoseMatrix(),e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e[9]=t[9],e[10]=t[10],e[11]=t[11],e[12]=t[12],e[13]=t[13],e[14]=t[14],e[15]=t[15];var e,t;return this.basePoseMatrix}getXRPose(e,t){switch(this.updateBasePoseMatrix(),t){case"target-ray":e._transformBasePoseMatrix(this.outputMatrix,this.basePoseMatrix),this.gamepad&&this.gamepad[Ge].targetRayTransform&&u(this.outputMatrix,this.outputMatrix,this.gamepad[Ge].targetRayTransform);break;case"grip":if(!this.nativeGamepad||!this.nativeGamepad.pose)return null;e._transformBasePoseMatrix(this.outputMatrix,this.basePoseMatrix),this.gamepad&&this.gamepad[Ge].gripTransform&&u(this.outputMatrix,this.outputMatrix,this.gamepad[Ge].gripTransform);break;default:return null}return e._adjustForOriginOffset(this.outputMatrix),new XRPose(new XRRigidTransform(this.outputMatrix),this.emulatedPosition)}}const He={highRefreshRate:!0},qe={oculus:1,openvr:1,"spatial controller (spatial interaction source)":1};let Ye=0;class Ze{constructor(e,t,n={}){if(this.mode=e,this.enabledFeatures=t,this.outputContext=null,this.immersive="immersive-vr"==e||"immersive-ar"==e,this.ended=null,this.baseLayer=null,this.id=++Ye,this.modifiedCanvasLayer=!1,this.outputContext){const e=n.renderContextType||"2d";this.renderContext=this.outputContext.canvas.getContext(e)}}}class Ke extends Me{constructor(e,t){const{canPresent:n}=t.capabilities;super(e),this.display=t,this.frame=new e.VRFrameData,this.sessions=new Map,this.immersiveSession=null,this.canPresent=n,this.baseModelMatrix=o(),this.gamepadInputSources={},this.tempVec3=new Float32Array(3),this.onVRDisplayPresentChange=this.onVRDisplayPresentChange.bind(this),e.window.addEventListener("vrdisplaypresentchange",this.onVRDisplayPresentChange),this.CAN_USE_GAMEPAD=e.navigator&&"getGamepads"in e.navigator,this.HAS_BITMAP_SUPPORT=(e=>!(!e.ImageBitmapRenderingContext||!e.createImageBitmap))(e)}get depthNear(){return this.display.depthNear}set depthNear(e){this.display.depthNear=e}get depthFar(){return this.display.depthFar}set depthFar(e){this.display.depthFar=e}onBaseLayerSet(e,t){const n=this.sessions.get(e),i=t.context.canvas;if(n.immersive){const e=this.display.getEyeParameters("left"),r=this.display.getEyeParameters("right");i.width=2*Math.max(e.renderWidth,r.renderWidth),i.height=Math.max(e.renderHeight,r.renderHeight),this.display.requestPresent([{source:i,attributes:He}]).then((()=>{this.global.document.body.contains(i)||(n.modifiedCanvasLayer=!0,this.global.document.body.appendChild(i),(e=>{e.style.display="block",e.style.position="absolute",e.style.width=e.style.height="1px",e.style.top=e.style.left="0px"})(i)),n.baseLayer=t}))}else n.baseLayer=t}isSessionSupported(e){return"immersive-ar"!=e&&("immersive-vr"!=e||!1!==this.canPresent)}isFeatureSupported(e){switch(e){case"viewer":case"local":case"local-floor":return!0;case"bounded":case"unbounded":default:return!1}}async requestSession(e,t){if(!this.isSessionSupported(e))return Promise.reject();let n="immersive-vr"==e;if(n){const e=this.global.document.createElement("canvas");e.getContext("webgl"),await this.display.requestPresent([{source:e,attributes:He}])}const i=new Ze(e,t,{renderContextType:this.HAS_BITMAP_SUPPORT?"bitmaprenderer":"2d"});return this.sessions.set(i.id,i),n&&(this.immersiveSession=i,this.dispatchEvent("@@webxr-polyfill/vr-present-start",i.id)),Promise.resolve(i.id)}requestAnimationFrame(e){return this.display.requestAnimationFrame(e)}getPrimaryButtonIndex(e){let t=0,n=e.id.toLowerCase();for(let e in qe)if(n.includes(e)){t=qe[e];break}return Math.min(t,e.buttons.length-1)}onFrameStart(e,t){this.display.depthNear=t.depthNear,this.display.depthFar=t.depthFar,this.display.getFrameData(this.frame);const n=this.sessions.get(e);if(n.immersive&&this.CAN_USE_GAMEPAD){let e=this.gamepadInputSources;this.gamepadInputSources={};let t=this.global.navigator.getGamepads();for(let i=0;i<t.length;++i){let r=t[i];if(r&&r.displayId>0){let t=e[i];if(t||(t=new Xe(this,this.display,this.getPrimaryButtonIndex(r))),t.updateFromGamepad(r),this.gamepadInputSources[i]=t,-1!=t.primaryButtonIndex){let e=r.buttons[t.primaryButtonIndex].pressed;e&&!t.primaryActionPressed?this.dispatchEvent("@@webxr-polyfill/input-select-start",{sessionId:n.id,inputSource:t.inputSource}):!e&&t.primaryActionPressed&&this.dispatchEvent("@@webxr-polyfill/input-select-end",{sessionId:n.id,inputSource:t.inputSource}),t.primaryActionPressed=e}if(-1!=t.primarySqueezeButtonIndex){let e=r.buttons[t.primarySqueezeButtonIndex].pressed;e&&!t.primarySqueezeActionPressed?this.dispatchEvent("@@webxr-polyfill/input-squeeze-start",{sessionId:n.id,inputSource:t.inputSource}):!e&&t.primarySqueezeActionPressed&&this.dispatchEvent("@@webxr-polyfill/input-squeeze-end",{sessionId:n.id,inputSource:t.inputSource}),t.primarySqueezeActionPressed=e}}}}if(!n.immersive&&n.baseLayer){const e=n.baseLayer.context.canvas;d(this.frame.leftProjectionMatrix,t.inlineVerticalFieldOfView,e.width/e.height,t.depthNear,t.depthFar)}}onFrameEnd(e){const t=this.sessions.get(e);if(!t.ended&&t.baseLayer){if(t.outputContext&&(!t.immersive||this.display.capabilities.hasExternalDisplay)){const e=t.immersive&&this.display.capabilities.hasExternalDisplay,n=t.baseLayer.context.canvas,i=e?n.width/2:n.width,r=n.height;{const e=t.outputContext.canvas,o=e.width,s=e.height,a=t.renderContext;this.HAS_BITMAP_SUPPORT?n.transferToImageBitmap?a.transferFromImageBitmap(n.transferToImageBitmap()):this.global.createImageBitmap(n,0,0,i,r,{resizeWidth:o,resizeHeight:s}).then((e=>a.transferFromImageBitmap(e))):a.drawImage(n,0,0,i,r,0,0,o,s)}}t.immersive&&t.baseLayer&&this.display.submitFrame()}}cancelAnimationFrame(e){this.display.cancelAnimationFrame(e)}async endSession(e){const t=this.sessions.get(e);if(!t.ended)return t.immersive?this.display.exitPresent():void(t.ended=!0)}doesSessionSupportReferenceSpace(e,t){const n=this.sessions.get(e);return!n.ended&&n.enabledFeatures.has(t)}requestStageBounds(){if(this.display.stageParameters){const e=this.display.stageParameters.sizeX,t=this.display.stageParameters.sizeZ,n=[];return n.push(-e/2),n.push(-t/2),n.push(e/2),n.push(-t/2),n.push(e/2),n.push(t/2),n.push(-e/2),n.push(t/2),n}return null}async requestFrameOfReferenceTransform(e,t){return("local-floor"===e||"bounded-floor"===e)&&this.display.stageParameters&&this.display.stageParameters.sittingToStandingTransform?this.display.stageParameters.sittingToStandingTransform:null}getProjectionMatrix(e){if("left"===e)return this.frame.leftProjectionMatrix;if("right"===e)return this.frame.rightProjectionMatrix;if("none"===e)return this.frame.leftProjectionMatrix;throw new Error("eye must be of type 'left' or 'right'")}getViewport(e,t,n,i){const r=this.sessions.get(e),{width:o,height:s}=n.context.canvas;if(!r.immersive)return i.x=i.y=0,i.width=o,i.height=s,!0;if("left"===t||"none"===t)i.x=0;else{if("right"!==t)return!1;i.x=o/2}return i.y=0,i.width=o/2,i.height=s,!0}getBasePoseMatrix(){let{position:e,orientation:t}=this.frame.pose;return e||t?(e||((e=this.tempVec3)[0]=e[1]=e[2]=0),l(this.baseModelMatrix,t,e),this.baseModelMatrix):this.baseModelMatrix}getBaseViewMatrix(e){if("left"===e||"none"===e)return this.frame.leftViewMatrix;if("right"===e)return this.frame.rightViewMatrix;throw new Error("eye must be of type 'left' or 'right'")}getInputSources(){let e=[];for(let t in this.gamepadInputSources)e.push(this.gamepadInputSources[t].inputSource);return e}getInputPose(e,t,n){if(!t)return null;for(let i in this.gamepadInputSources){let r=this.gamepadInputSources[i];if(r.inputSource===e)return r.getXRPose(t,n)}return null}onWindowResize(){}onVRDisplayPresentChange(e){this.display.isPresenting||this.sessions.forEach((e=>{if(e.immersive&&!e.ended){if(e.modifiedCanvasLayer){const t=e.baseLayer.context.canvas;document.body.removeChild(t),t.setAttribute("style","")}this.immersiveSession===e&&(this.immersiveSession=null),this.dispatchEvent("@@webxr-polyfill/vr-present-end",e.id)}}))}}let $e=0;class Je{constructor(e,t){this.mode=e,this.enabledFeatures=t,this.ended=null,this.baseLayer=null,this.id=++$e}}const et={global:e,webvr:!0,cardboard:!0,cardboardConfig:null,allowCardboardOnDesktop:!1},tt=["navigator","HTMLCanvasElement","WebGLRenderingContext"];return class{constructor(e={}){this.config=Object.freeze(Object.assign({},et,e)),this.global=this.config.global,this.nativeWebXR="xr"in this.global.navigator,this.injected=!1,this.nativeWebXR?this._injectCompatibilityShims(this.global):this._injectPolyfill(this.global)}_injectPolyfill(e){if(!tt.every((t=>!!e[t])))throw new Error(`Global must have the following attributes : ${tt}`);for(const t of Object.keys(_e))void 0!==e[t]?console.warn(`${t} already defined on global.`):e[t]=_e[t];we(e.WebGLRenderingContext)&&(be(e.HTMLCanvasElement),e.OffscreenCanvas&&be(e.OffscreenCanvas),e.WebGL2RenderingContext&&we(e.WebGL2RenderingContext),window.isSecureContext||console.warn("WebXR Polyfill Warning:\nThis page is not running in a secure context (https:// or localhost)!\nThis means that although the page may be able to use the WebXR Polyfill it will\nnot be able to use native WebXR implementations, and as such will not be able to\naccess dedicated VR or AR hardware, and will not be able to take advantage of\nany performance improvements a native WebXR implementation may offer. Please\nhost this content on a secure origin for the best user experience.\n")),this.injected=!0,this._patchNavigatorXR()}_patchNavigatorXR(){let e=async function(e,t){if(t.webvr){let t=await async function(e){let t=null;if("getVRDisplays"in e.navigator)try{const n=await e.navigator.getVRDisplays();n&&n.length&&(t=new Ke(e,n[0]))}catch(e){}return t}(e);if(t)return t}let n=(e=>{var t=!1;return xe=e.navigator.userAgent||e.navigator.vendor||e.opera,(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(xe)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(xe.substr(0,4)))&&(t=!0),t})(e);return n&&t.cardboard||!n&&t.allowCardboardOnDesktop?(e.VRFrameData||(e.VRFrameData=function(){this.rightViewMatrix=new Float32Array(16),this.leftViewMatrix=new Float32Array(16),this.rightProjectionMatrix=new Float32Array(16),this.leftProjectionMatrix=new Float32Array(16),this.pose=null}),new class extends Ke{constructor(e,t){const n=new Re(t||{});super(e,n),this.display=n,this.frame={rightViewMatrix:new Float32Array(16),leftViewMatrix:new Float32Array(16),rightProjectionMatrix:new Float32Array(16),leftProjectionMatrix:new Float32Array(16),pose:null,timestamp:null}}}(e,t.cardboardConfig)):new class extends Me{constructor(e){super(e),this.sessions=new Map,this.projectionMatrix=o(),this.identityMatrix=o()}onBaseLayerSet(e,t){this.sessions.get(e).baseLayer=t}isSessionSupported(e){return"inline"==e}isFeatureSupported(e){switch(e){case"viewer":return!0;default:return!1}}async requestSession(e,t){if(!this.isSessionSupported(e))return Promise.reject();const n=new Je(e,t);return this.sessions.set(n.id,n),Promise.resolve(n.id)}requestAnimationFrame(e){return window.requestAnimationFrame(e)}cancelAnimationFrame(e){window.cancelAnimationFrame(e)}onFrameStart(e,t){const n=this.sessions.get(e);if(n.baseLayer){const e=n.baseLayer.context.canvas;d(this.projectionMatrix,t.inlineVerticalFieldOfView,e.width/e.height,t.depthNear,t.depthFar)}}onFrameEnd(e){}async endSession(e){this.sessions.get(e).ended=!0}doesSessionSupportReferenceSpace(e,t){const n=this.sessions.get(e);return!n.ended&&n.enabledFeatures.has(t)}requestStageBounds(){return null}async requestFrameOfReferenceTransform(e,t){return null}getProjectionMatrix(e){return this.projectionMatrix}getViewport(e,t,n,i){const{width:r,height:o}=n.context.canvas;return this.sessions.get(e),i.x=i.y=0,i.width=r,i.height=o,!0}getBasePoseMatrix(){return this.identityMatrix}getBaseViewMatrix(e){return this.identityMatrix}getInputSources(){return[]}getInputPose(e,t,n){return null}onWindowResize(){}}(e)}(this.global,this.config);this.xr=new _e.XRSystem(e),Object.defineProperty(this.global.navigator,"xr",{value:this.xr,configurable:!0})}_injectCompatibilityShims(e){if(!tt.every((t=>!!e[t])))throw new Error(`Global must have the following attributes : ${tt}`);if(e.navigator.xr&&"supportsSession"in e.navigator.xr&&!("isSessionSupported"in e.navigator.xr)){let t=e.navigator.xr.supportsSession;e.navigator.xr.isSessionSupported=function(e){return t.call(this,e).then((()=>!0)).catch((()=>!1))},e.navigator.xr.supportsSession=function(e){return console.warn("navigator.xr.supportsSession() is deprecated. Please call navigator.xr.isSessionSupported() instead and check the boolean value returned when the promise resolves."),t.call(this,e)}}}}}()}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var n=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e].call(n.exports,n,n.exports,__webpack_require__),n.exports}__webpack_require__.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return __webpack_require__.d(t,{a:t}),t},__webpack_require__.d=(e,t)=>{for(var n in t)__webpack_require__.o(t,n)&&!__webpack_require__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),__webpack_require__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),__webpack_require__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__(266);return __webpack_exports__=__webpack_exports__.default,__webpack_exports__})()}));