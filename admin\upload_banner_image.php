<?php
/**
 * 轮播图图片上传API - 后台管理专用
 * 不需要JWT认证，仅供后台管理使用
 */

session_start();
require_once '../includes/db.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '未登录或登录已过期']);
    exit;
}

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
    exit;
}

try {
    uploadBannerImage();
} catch (Exception $e) {
    error_log('轮播图图片上传错误: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '服务器内部错误']);
}

/**
 * 处理轮播图图片上传
 */
function uploadBannerImage() {
    // 检查是否有文件上传
    if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '没有上传文件或上传失败']);
        return;
    }
    
    $file = $_FILES['image'];
    
    // 验证文件类型
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $file_info = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($file_info, $file['tmp_name']);
    finfo_close($file_info);
    
    if (!in_array($mime_type, $allowed_types)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '只支持 JPG、PNG、GIF、WebP 格式的图片']);
        return;
    }
    
    // 验证文件大小（限制5MB）
    $max_size = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $max_size) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '图片大小不能超过5MB']);
        return;
    }
    
    // 创建上传目录
    $upload_dir = dirname(__DIR__) . '/uploads/banners/' . date('Y/m');
    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => '创建上传目录失败']);
            return;
        }
    }
    
    // 生成文件名
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (empty($file_extension)) {
        // 根据MIME类型确定扩展名
        $mime_to_ext = [
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp'
        ];
        $file_extension = $mime_to_ext[$mime_type] ?? 'jpg';
    }
    
    $file_name = 'banner_' . time() . '_' . uniqid() . '.' . $file_extension;
    $file_path = $upload_dir . '/' . $file_name;
    
    // 移动上传文件
    if (!move_uploaded_file($file['tmp_name'], $file_path)) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => '文件保存失败']);
        return;
    }
    
    // 生成访问URL
    $file_url = '/uploads/banners/' . date('Y/m') . '/' . $file_name;
    
    // 记录上传日志
    logBannerImageUpload($_SESSION['admin_id'], $file_name, $file['size'], $file_url);
    
    echo json_encode([
        'success' => true,
        'message' => '图片上传成功',
        'data' => [
            'file_name' => $file_name,
            'file_url' => $file_url,
            'file_size' => $file['size'],
            'uploaded_at' => date('Y-m-d H:i:s')
        ]
    ]);
}

/**
 * 记录轮播图图片上传日志
 */
function logBannerImageUpload($admin_id, $file_name, $file_size, $file_url) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("INSERT INTO upload_logs (admin_id, file_type, file_name, file_size, file_url, upload_time) VALUES (?, 'banner', ?, ?, ?, NOW())");
        if ($stmt) {
            $stmt->bind_param("isis", $admin_id, $file_name, $file_size, $file_url);
            $stmt->execute();
            $stmt->close();
        }
    } catch (Exception $e) {
        // 日志记录失败不影响主要功能
        error_log('轮播图上传日志记录失败: ' . $e->getMessage());
    }
}

/**
 * 获取图片信息（用于验证和预览）
 */
function getImageInfo($file_path) {
    if (!file_exists($file_path)) {
        return null;
    }
    
    $image_info = getimagesize($file_path);
    if ($image_info === false) {
        return null;
    }
    
    return [
        'width' => $image_info[0],
        'height' => $image_info[1],
        'type' => $image_info[2],
        'mime' => $image_info['mime'],
        'size' => filesize($file_path)
    ];
}

/**
 * 验证图片尺寸比例（推荐16:9或2:1）
 */
function validateImageRatio($width, $height) {
    $ratio = $width / $height;
    
    // 推荐比例范围
    $recommended_ratios = [
        '16:9' => 16/9,
        '2:1' => 2/1,
        '3:1' => 3/1
    ];
    
    $tolerance = 0.1; // 允许10%的误差
    
    foreach ($recommended_ratios as $name => $recommended_ratio) {
        if (abs($ratio - $recommended_ratio) <= $tolerance) {
            return ['valid' => true, 'ratio' => $name];
        }
    }
    
    return [
        'valid' => false, 
        'ratio' => number_format($ratio, 2) . ':1',
        'message' => '建议使用16:9、2:1或3:1的图片比例以获得最佳显示效果'
    ];
}
?>
