<?php
/**
 * 修改密码API
 * 支持JWT认证，处理用户密码修改
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    // 验证用户认证
    $user = $auth->requireAuth();
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        $auth->jsonResponse(405, '不支持的请求方法');
    }
    
    changePassword($auth, $user);
    
} catch (Exception $e) {
    error_log('修改密码API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 修改密码
 */
function changePassword($auth, $user) {
    $conn = $auth->getConn();
    $user_id = $user['id'];

    // 检查是否允许修改密码
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'allow_password_change'");
    $stmt->execute();
    $result = $stmt->get_result();

    $allow_password_change = '1'; // 默认允许
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $allow_password_change = $row['setting_value'];
    }

    if ($allow_password_change !== '1') {
        // 获取禁用提示信息
        $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'password_change_notice'");
        $stmt->execute();
        $result = $stmt->get_result();

        $notice = '管理员已禁用密码修改功能，如需修改密码请联系客服。';
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $notice = $row['setting_value'] ?: $notice;
        }

        $auth->jsonResponse(403, $notice);
    }

    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        $auth->jsonResponse(400, '请求数据格式错误');
    }
    
    // 获取用户当前密码哈希
    $stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $auth->jsonResponse(404, '用户不存在');
    }

    $user_data = $result->fetch_assoc();
    $has_password = !empty($user_data['password']);

    // 根据是否已设置密码来验证必需字段
    if ($has_password) {
        // 已设置密码，需要验证旧密码
        if (!isset($input['old_password']) || empty($input['old_password'])) {
            $auth->jsonResponse(400, "请输入当前密码");
        }
        $old_password = $input['old_password'];
    } else {
        // 未设置密码，不需要旧密码
        $old_password = null;
    }

    // 验证新密码
    if (!isset($input['new_password']) || empty($input['new_password'])) {
        $auth->jsonResponse(400, "请输入新密码");
    }

    $new_password = $input['new_password'];
    
    // 验证新密码强度
    if (strlen($new_password) < 6) {
        $auth->jsonResponse(400, '新密码长度不能少于6位');
    }
    
    if (strlen($new_password) > 50) {
        $auth->jsonResponse(400, '新密码长度不能超过50位');
    }
    
    // 检查新密码是否与旧密码相同（仅在已设置密码时检查）
    if ($has_password && $old_password !== null && $old_password === $new_password) {
        $auth->jsonResponse(400, '新密码不能与旧密码相同');
    }

    // 如果用户已设置密码，验证旧密码
    if ($has_password && $old_password !== null) {
        if (!$auth->verifyPassword($old_password, $user_data['password'])) {
            // 记录密码修改失败日志
            $auth->logLogin($user_id, 'password_change', 'web', 'failed', '旧密码错误');
            $auth->jsonResponse(400, '当前密码错误');
        }
    }
    
    // 哈希新密码
    $new_password_hash = $auth->hashPassword($new_password);
    
    // 更新密码
    $update_stmt = $conn->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
    $update_stmt->bind_param("si", $new_password_hash, $user_id);
    
    if ($update_stmt->execute()) {
        // 记录密码修改成功日志
        $auth->logLogin($user_id, 'password_change', 'web', 'success');
        
        // 撤销用户的所有现有token（强制重新登录）
        $revoke_stmt = $conn->prepare("UPDATE user_tokens SET is_revoked = 1 WHERE user_id = ? AND is_revoked = 0");
        $revoke_stmt->bind_param("i", $user_id);
        $revoke_stmt->execute();
        
        $auth->jsonResponse(200, '密码修改成功，请重新登录', [
            'user_id' => $user_id,
            'changed_at' => date('Y-m-d H:i:s'),
            'force_relogin' => true
        ]);
    } else {
        error_log('更新密码失败: ' . $conn->error);
        $auth->jsonResponse(500, '密码修改失败，请稍后重试');
    }
}
?>
