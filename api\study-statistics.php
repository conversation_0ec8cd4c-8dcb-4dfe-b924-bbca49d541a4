<?php
/**
 * 学习统计API
 * 获取用户的学习统计数据
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

$auth = new AuthAPI();

try {
    // 获取用户信息（可选认证，允许游客访问）
    $user = $auth->getCurrentUser();

    if ($user) {
        // 获取学习统计
        getStudyStatistics($auth, $user['id']);
    } else {
        // 返回默认统计数据（游客用户）
        getDefaultStatistics($auth);
    }

} catch (Exception $e) {
    error_log('学习统计API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 获取学习统计数据
 */
function getStudyStatistics($auth, $user_id) {
    $conn = $auth->getConn();

    // 获取总课程数
    $total_stmt = $conn->prepare("
        SELECT COUNT(*) as total_courses
        FROM user_courses
        WHERE user_id = ? AND status = 'active'
    ");
    $total_stmt->bind_param("i", $user_id);
    $total_stmt->execute();
    $total_result = $total_stmt->get_result()->fetch_assoc();

    // 获取进行中的课程数（进度 < 100%）
    $active_stmt = $conn->prepare("
        SELECT COUNT(*) as active_courses
        FROM user_courses
        WHERE user_id = ? AND status = 'active'
        AND watch_progress < 100
        AND (expires_at IS NULL OR expires_at > NOW())
    ");
    $active_stmt->bind_param("i", $user_id);
    $active_stmt->execute();
    $active_result = $active_stmt->get_result()->fetch_assoc();

    // 获取已完成的课程数（进度 = 100%）
    $completed_stmt = $conn->prepare("
        SELECT COUNT(*) as completed_courses
        FROM user_courses
        WHERE user_id = ? AND status = 'active'
        AND watch_progress >= 100
    ");
    $completed_stmt->bind_param("i", $user_id);
    $completed_stmt->execute();
    $completed_result = $completed_stmt->get_result()->fetch_assoc();

    // 获取平均进度
    $progress_stmt = $conn->prepare("
        SELECT AVG(watch_progress) as average_progress
        FROM user_courses
        WHERE user_id = ? AND status = 'active'
    ");
    $progress_stmt->bind_param("i", $user_id);
    $progress_stmt->execute();
    $progress_result = $progress_stmt->get_result()->fetch_assoc();

    // 获取总观看次数
    $watch_stmt = $conn->prepare("
        SELECT SUM(watch_count) as total_watch_count
        FROM user_courses
        WHERE user_id = ? AND status = 'active'
    ");
    $watch_stmt->bind_param("i", $user_id);
    $watch_stmt->execute();
    $watch_result = $watch_stmt->get_result()->fetch_assoc();

    // 获取最近学习的课程
    $recent_stmt = $conn->prepare("
        SELECT c.title, uc.last_watched_at, uc.watch_progress
        FROM user_courses uc
        LEFT JOIN courses c ON uc.course_id = c.id
        WHERE uc.user_id = ? AND uc.status = 'active'
        AND uc.last_watched_at IS NOT NULL
        ORDER BY uc.last_watched_at DESC
        LIMIT 5
    ");
    $recent_stmt->bind_param("i", $user_id);
    $recent_stmt->execute();
    $recent_result = $recent_stmt->get_result();

    $recent_courses = [];
    while ($row = $recent_result->fetch_assoc()) {
        $recent_courses[] = $row;
    }

    $statistics = [
        'total_courses' => intval($total_result['total_courses']),
        'active_courses' => intval($active_result['active_courses']),
        'completed_courses' => intval($completed_result['completed_courses']),
        'average_progress' => round(floatval($progress_result['average_progress']), 1),
        'total_watch_count' => intval($watch_result['total_watch_count']),
        'recent_courses' => $recent_courses
    ];

    $response_data = [
        'statistics' => $statistics
    ];

    $auth->jsonResponse(200, '获取成功', $response_data);
}

/**
 * 获取默认统计数据（未登录用户）
 */
function getDefaultStatistics($auth) {
    // 返回默认的空统计数据
    $statistics = [
        'total_courses' => 0,
        'active_courses' => 0,
        'completed_courses' => 0,
        'average_progress' => 0,
        'total_watch_count' => 0,
        'recent_courses' => [],
        'is_guest' => true
    ];

    $response_data = [
        'statistics' => $statistics
    ];

    $auth->jsonResponse(200, '获取成功', $response_data);
}
?>
