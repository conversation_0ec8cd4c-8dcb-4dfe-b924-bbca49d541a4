<?php
session_start();

echo "<h2>Session 状态检查</h2>";
echo "<pre>";
echo "Session ID: " . session_id() . "\n";
echo "Session 数据:\n";
print_r($_SESSION);
echo "</pre>";

echo "<h3>登录状态检查</h3>";
echo "<ul>";
echo "<li>loggedin: " . (isset($_SESSION['loggedin']) ? ($_SESSION['loggedin'] ? 'true' : 'false') : 'not set') . "</li>";
echo "<li>admin_logged_in: " . (isset($_SESSION['admin_logged_in']) ? ($_SESSION['admin_logged_in'] ? 'true' : 'false') : 'not set') . "</li>";
echo "<li>username: " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'not set') . "</li>";
echo "<li>admin_id: " . (isset($_SESSION['admin_id']) ? $_SESSION['admin_id'] : 'not set') . "</li>";
echo "</ul>";

echo "<h3>测试链接</h3>";
echo "<ul>";
echo "<li><a href='banners.php'>轮播图管理</a></li>";
echo "<li><a href='users.php'>用户管理</a></li>";
echo "<li><a href='index.php'>后台首页</a></li>";
echo "<li><a href='login.php'>登录页面</a></li>";
echo "</ul>";
?>
