<?php
/**
 * 视频播放诊断工具
 * 用于诊断和修复视频播放黑屏问题
 */

require_once '../includes/db.php';
require_once '../includes/vod_config.php';

// 检查是否有管理员权限
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

$diagnosis_results = [];
$lesson_id = isset($_GET['lesson_id']) ? intval($_GET['lesson_id']) : 0;
$auto_fix = isset($_GET['auto_fix']) && $_GET['auto_fix'] === '1';

// 执行诊断
if ($lesson_id > 0) {
    $diagnosis_results = performVideoDiagnosis($lesson_id, $auto_fix);
}

/**
 * 执行视频播放诊断
 */
function performVideoDiagnosis($lesson_id, $auto_fix = false) {
    global $conn;
    $results = [];
    
    // 1. 检查课时基本信息
    $lesson_sql = "SELECT * FROM lessons WHERE id = ?";
    $lesson_stmt = $conn->prepare($lesson_sql);
    $lesson_stmt->bind_param("i", $lesson_id);
    $lesson_stmt->execute();
    $lesson_result = $lesson_stmt->get_result();
    $lesson = $lesson_result->fetch_assoc();
    
    if (!$lesson) {
        $results[] = [
            'type' => 'error',
            'title' => '课时不存在',
            'message' => "课时ID {$lesson_id} 不存在",
            'solution' => '请检查课时ID是否正确'
        ];
        return $results;
    }
    
    $results[] = [
        'type' => 'info',
        'title' => '课时基本信息',
        'message' => "课时标题: {$lesson['title']}, 视频类型: {$lesson['video_type']}, 状态: " . ($lesson['status'] ? '启用' : '禁用'),
        'data' => $lesson
    ];
    
    // 2. 检查视频类型和URL
    if ($lesson['video_type'] === 'vod') {
        // VOD视频诊断
        $vod_results = diagnoseVodVideo($lesson, $auto_fix);
        $results = array_merge($results, $vod_results);
    } elseif ($lesson['video_type'] === 'url') {
        // 普通视频诊断
        $url_results = diagnoseUrlVideo($lesson, $auto_fix);
        $results = array_merge($results, $url_results);
    } else {
        $results[] = [
            'type' => 'warning',
            'title' => '未知视频类型',
            'message' => "视频类型 '{$lesson['video_type']}' 不被支持",
            'solution' => '请将视频类型设置为 vod 或 url'
        ];
    }
    
    // 3. 检查缩略图
    $thumbnail_results = diagnoseThumbnail($lesson, $auto_fix);
    $results = array_merge($results, $thumbnail_results);
    
    return $results;
}

/**
 * 诊断VOD视频
 */
function diagnoseVodVideo($lesson, $auto_fix = false) {
    $results = [];
    
    // 检查VOD配置
    $config_validation = VodConfig::validateConfig();
    if (!$config_validation['valid']) {
        $results[] = [
            'type' => 'error',
            'title' => 'VOD配置错误',
            'message' => implode(', ', $config_validation['errors']),
            'solution' => '请检查 includes/vod_config.php 中的配置'
        ];
        return $results;
    }
    
    // 检查fileId
    if (empty($lesson['vod_file_id'])) {
        $results[] = [
            'type' => 'error',
            'title' => 'VOD文件ID缺失',
            'message' => '课时设置为VOD类型但没有vod_file_id',
            'solution' => '请重新上传视频或检查上传过程'
        ];
        return $results;
    }
    
    $results[] = [
        'type' => 'info',
        'title' => 'VOD文件ID',
        'message' => "文件ID: {$lesson['vod_file_id']}",
        'data' => ['file_id' => $lesson['vod_file_id']]
    ];
    
    // 检查播放URL
    if (empty($lesson['vod_video_url'])) {
        $results[] = [
            'type' => 'warning',
            'title' => 'VOD播放URL缺失',
            'message' => '数据库中没有保存播放URL，可能需要从腾讯云获取',
            'solution' => '尝试通过API获取播放URL'
        ];
        
        // 尝试获取播放URL
        $vod_status = getVodStatus($lesson['vod_file_id']);
        if ($vod_status['success']) {
            $video_data = $vod_status['data'];
            $play_url = extractPlayUrl($video_data);
            
            if ($play_url) {
                $results[] = [
                    'type' => 'success',
                    'title' => '成功获取播放URL',
                    'message' => "播放URL: {$play_url}",
                    'data' => ['play_url' => $play_url]
                ];
                
                // 自动修复：更新数据库
                if ($auto_fix) {
                    updateLessonVodUrl($lesson['id'], $play_url);
                    $results[] = [
                        'type' => 'success',
                        'title' => '自动修复完成',
                        'message' => '已将播放URL保存到数据库',
                        'solution' => '请刷新页面查看效果'
                    ];
                }
            } else {
                $results[] = [
                    'type' => 'warning',
                    'title' => '视频可能还在转码中',
                    'message' => '从腾讯云获取到视频信息但没有可用的播放URL',
                    'solution' => '请等待转码完成或检查转码状态',
                    'data' => $video_data
                ];
            }
        } else {
            $results[] = [
                'type' => 'error',
                'title' => '无法获取VOD状态',
                'message' => $vod_status['message'],
                'solution' => '请检查网络连接和VOD配置'
            ];
        }
    } else {
        $results[] = [
            'type' => 'success',
            'title' => 'VOD播放URL正常',
            'message' => "播放URL: {$lesson['vod_video_url']}",
            'data' => ['play_url' => $lesson['vod_video_url']]
        ];
        
        // 验证URL是否可访问
        $url_check = checkUrlAccessibility($lesson['vod_video_url']);
        $results[] = $url_check;
    }
    
    return $results;
}

/**
 * 诊断普通视频URL
 */
function diagnoseUrlVideo($lesson, $auto_fix = false) {
    $results = [];
    
    if (empty($lesson['video_url'])) {
        $results[] = [
            'type' => 'error',
            'title' => '视频URL缺失',
            'message' => '课时设置为URL类型但没有video_url',
            'solution' => '请设置有效的视频URL'
        ];
        return $results;
    }
    
    $results[] = [
        'type' => 'info',
        'title' => '视频URL',
        'message' => "URL: {$lesson['video_url']}",
        'data' => ['video_url' => $lesson['video_url']]
    ];
    
    // 验证URL格式
    if (!filter_var($lesson['video_url'], FILTER_VALIDATE_URL)) {
        $results[] = [
            'type' => 'error',
            'title' => 'URL格式无效',
            'message' => '视频URL格式不正确',
            'solution' => '请检查URL格式是否正确'
        ];
        return $results;
    }
    
    // 检查URL可访问性
    $url_check = checkUrlAccessibility($lesson['video_url']);
    $results[] = $url_check;
    
    return $results;
}

/**
 * 诊断缩略图
 */
function diagnoseThumbnail($lesson, $auto_fix = false) {
    $results = [];
    
    if (empty($lesson['thumbnail'])) {
        $results[] = [
            'type' => 'warning',
            'title' => '缩略图缺失',
            'message' => '没有设置课时缩略图',
            'solution' => '建议上传缩略图以提升用户体验'
        ];
        return $results;
    }
    
    $results[] = [
        'type' => 'info',
        'title' => '缩略图URL',
        'message' => "缩略图: {$lesson['thumbnail']}",
        'data' => ['thumbnail' => $lesson['thumbnail']]
    ];
    
    // 检查缩略图URL格式
    $full_thumbnail_url = convertToFullUrl($lesson['thumbnail']);
    if ($full_thumbnail_url !== $lesson['thumbnail']) {
        $results[] = [
            'type' => 'info',
            'title' => '缩略图URL转换',
            'message' => "完整URL: {$full_thumbnail_url}",
            'data' => ['full_thumbnail_url' => $full_thumbnail_url]
        ];
    }
    
    // 检查缩略图可访问性
    if (filter_var($full_thumbnail_url, FILTER_VALIDATE_URL)) {
        $thumbnail_check = checkUrlAccessibility($full_thumbnail_url);
        $results[] = $thumbnail_check;
    }
    
    return $results;
}

/**
 * 获取VOD状态
 */
function getVodStatus($file_id) {
    $url = "http://{$_SERVER['HTTP_HOST']}/api/vod-status.php?file_id=" . urlencode($file_id);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        $result = json_decode($response, true);
        return $result ?: ['success' => false, 'message' => '响应解析失败'];
    } else {
        return ['success' => false, 'message' => "HTTP错误: {$http_code}"];
    }
}

/**
 * 从视频数据中提取播放URL
 */
function extractPlayUrl($video_data) {
    // 优先使用转码后的URL
    if (isset($video_data['transcoding']['streams']) && !empty($video_data['transcoding']['streams'])) {
        $streams = $video_data['transcoding']['streams'];
        // 选择合适清晰度
        foreach ([720, 480, 360] as $height) {
            foreach ($streams as $stream) {
                if ($stream['height'] == $height && !empty($stream['url'])) {
                    return $stream['url'];
                }
            }
        }
        // 如果没有找到指定清晰度，返回第一个
        if (!empty($streams[0]['url'])) {
            return $streams[0]['url'];
        }
    }
    
    // 使用自适应流URL
    if (isset($video_data['playback_urls']) && !empty($video_data['playback_urls'])) {
        return $video_data['playback_urls'][0]['url'];
    }
    
    return null;
}

/**
 * 检查URL可访问性
 */
function checkUrlAccessibility($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_NOBODY, 1);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($http_code >= 200 && $http_code < 400) {
        return [
            'type' => 'success',
            'title' => 'URL可访问',
            'message' => "HTTP状态码: {$http_code}",
            'data' => ['http_code' => $http_code]
        ];
    } else {
        return [
            'type' => 'error',
            'title' => 'URL不可访问',
            'message' => $error ? "错误: {$error}" : "HTTP状态码: {$http_code}",
            'solution' => '请检查URL是否正确或网络连接',
            'data' => ['http_code' => $http_code, 'error' => $error]
        ];
    }
}

/**
 * 更新课时VOD播放URL
 */
function updateLessonVodUrl($lesson_id, $vod_url) {
    global $conn;
    $update_sql = "UPDATE lessons SET vod_video_url = ? WHERE id = ?";
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param("si", $vod_url, $lesson_id);
    return $update_stmt->execute();
}

/**
 * 将相对路径转换为完整URL
 */
function convertToFullUrl($url) {
    if (empty($url)) {
        return '';
    }
    
    if (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0) {
        return $url;
    }
    
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    
    if (strpos($url, '/') !== 0) {
        $url = '/' . $url;
    }
    
    return $protocol . '://' . $host . $url;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频播放诊断工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .diagnosis-result {
            margin-bottom: 1rem;
        }
        .result-success { border-left: 4px solid #28a745; }
        .result-warning { border-left: 4px solid #ffc107; }
        .result-error { border-left: 4px solid #dc3545; }
        .result-info { border-left: 4px solid #17a2b8; }
        .result-data {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-camera-video"></i> 视频播放诊断工具</h2>
                    <a href="lessons.php" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> 返回课时管理
                    </a>
                </div>

                <!-- 诊断表单 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">选择要诊断的课时</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label for="lesson_id" class="form-label">课时ID</label>
                                <input type="number" class="form-control" id="lesson_id" name="lesson_id"
                                       value="<?php echo $lesson_id; ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search"></i> 开始诊断
                                    </button>
                                    <button type="submit" name="auto_fix" value="1" class="btn btn-warning">
                                        <i class="bi bi-wrench"></i> 诊断并自动修复
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 诊断结果 -->
                <?php if (!empty($diagnosis_results)): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-clipboard-check"></i> 诊断结果
                            <?php if ($auto_fix): ?>
                                <span class="badge bg-warning ms-2">自动修复模式</span>
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($diagnosis_results as $result): ?>
                            <div class="diagnosis-result card result-<?php echo $result['type']; ?>">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <?php
                                        $icons = [
                                            'success' => 'check-circle-fill text-success',
                                            'warning' => 'exclamation-triangle-fill text-warning',
                                            'error' => 'x-circle-fill text-danger',
                                            'info' => 'info-circle-fill text-info'
                                        ];
                                        ?>
                                        <i class="bi bi-<?php echo $icons[$result['type']]; ?>"></i>
                                        <?php echo htmlspecialchars($result['title']); ?>
                                    </h6>
                                    <p class="card-text"><?php echo htmlspecialchars($result['message']); ?></p>

                                    <?php if (isset($result['solution'])): ?>
                                        <div class="alert alert-light mb-2">
                                            <strong>解决方案：</strong> <?php echo htmlspecialchars($result['solution']); ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (isset($result['data']) && !empty($result['data'])): ?>
                                        <div class="result-data">
                                            <strong>详细数据：</strong><br>
                                            <?php echo htmlspecialchars(json_encode($result['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- 使用说明 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-question-circle"></i> 使用说明</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>诊断功能</h6>
                                <ul>
                                    <li>检查课时基本信息</li>
                                    <li>验证视频URL可访问性</li>
                                    <li>检查VOD配置和状态</li>
                                    <li>验证缩略图设置</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>自动修复功能</h6>
                                <ul>
                                    <li>自动获取VOD播放URL</li>
                                    <li>更新数据库中的播放地址</li>
                                    <li>修复常见配置问题</li>
                                    <li>提供详细的修复报告</li>
                                </ul>
                            </div>
                        </div>

                        <div class="alert alert-info mt-3">
                            <i class="bi bi-lightbulb"></i>
                            <strong>提示：</strong> 如果视频播放出现黑屏问题，建议先使用诊断功能检查问题原因，然后使用自动修复功能尝试解决。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
