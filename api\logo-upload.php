<?php
/**
 * Logo上传API
 * 处理应用Logo图片上传
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        $auth->jsonResponse(405, '不支持的请求方法');
    }
    
    // 验证管理员权限
    $user = $auth->getCurrentUser();
    if (!$user || $user['role'] !== 'admin') {
        $auth->jsonResponse(403, '需要管理员权限');
    }
    
    uploadLogo($auth);
    
} catch (Exception $e) {
    error_log('Logo上传API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 处理Logo上传
 */
function uploadLogo($auth) {
    $conn = $auth->getConn();
    
    // 检查是否有文件上传
    if (!isset($_FILES['logo']) || $_FILES['logo']['error'] !== UPLOAD_ERR_OK) {
        $error_msg = '文件上传失败';
        if (isset($_FILES['logo']['error'])) {
            switch ($_FILES['logo']['error']) {
                case UPLOAD_ERR_INI_SIZE:
                case UPLOAD_ERR_FORM_SIZE:
                    $error_msg = '文件大小超出限制';
                    break;
                case UPLOAD_ERR_PARTIAL:
                    $error_msg = '文件上传不完整';
                    break;
                case UPLOAD_ERR_NO_FILE:
                    $error_msg = '没有选择文件';
                    break;
                default:
                    $error_msg = '文件上传失败，错误代码：' . $_FILES['logo']['error'];
            }
        }
        $auth->jsonResponse(400, $error_msg);
    }
    
    $file = $_FILES['logo'];
    
    // 获取配置
    $config = getUploadConfig($conn);
    
    // 验证文件类型
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $allowed_types = explode(',', $config['allowed_logo_types']);
    if (!in_array($file_extension, $allowed_types)) {
        $auth->jsonResponse(400, '不支持的文件类型，只允许：' . $config['allowed_logo_types']);
    }
    
    // 验证文件大小
    $max_size = $config['max_logo_size'] * 1024; // 转换为字节
    if ($file['size'] > $max_size) {
        $auth->jsonResponse(400, '文件大小超出限制，最大允许：' . $config['max_logo_size'] . 'KB');
    }
    
    // 验证图片有效性
    $image_info = getimagesize($file['tmp_name']);
    if ($image_info === false) {
        $auth->jsonResponse(400, '无效的图片文件');
    }
    
    // 生成安全的文件名
    $filename = 'logo_' . date('YmdHis') . '_' . uniqid() . '.' . $file_extension;
    
    // 确保上传目录存在
    $upload_dir = $_SERVER['DOCUMENT_ROOT'] . $config['logo_upload_path'];
    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            $auth->jsonResponse(500, '创建上传目录失败');
        }
    }
    
    $file_path = $upload_dir . $filename;
    $url_path = $config['logo_upload_path'] . $filename;
    
    // 移动上传文件
    if (!move_uploaded_file($file['tmp_name'], $file_path)) {
        $auth->jsonResponse(500, '保存文件失败');
    }
    
    // 生成缩略图（可选）
    try {
        createThumbnail($file_path, $upload_dir . 'thumb_' . $filename, 200, 200);
        $thumb_url = $config['logo_upload_path'] . 'thumb_' . $filename;
    } catch (Exception $e) {
        error_log('生成缩略图失败: ' . $e->getMessage());
        $thumb_url = $url_path; // 使用原图作为缩略图
    }
    
    // 删除旧的logo文件（可选）
    try {
        deleteOldLogo($conn, $config['logo_upload_path']);
    } catch (Exception $e) {
        error_log('删除旧logo失败: ' . $e->getMessage());
    }
    
    // 更新数据库中的logo URL
    $update_stmt = $conn->prepare("
        INSERT INTO settings (setting_key, setting_value) 
        VALUES ('app_logo_url', ?) 
        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
    ");
    $update_stmt->bind_param("s", $url_path);
    
    if (!$update_stmt->execute()) {
        // 如果数据库更新失败，删除已上传的文件
        unlink($file_path);
        if (file_exists($upload_dir . 'thumb_' . $filename)) {
            unlink($upload_dir . 'thumb_' . $filename);
        }
        $auth->jsonResponse(500, '更新数据库失败');
    }
    
    $auth->jsonResponse(200, 'Logo上传成功', [
        'logo_url' => $url_path,
        'thumbnail_url' => $thumb_url,
        'filename' => $filename,
        'size' => $file['size'],
        'dimensions' => [
            'width' => $image_info[0],
            'height' => $image_info[1]
        ]
    ]);
}

/**
 * 获取上传配置
 */
function getUploadConfig($conn) {
    $config_keys = ['logo_upload_path', 'max_logo_size', 'allowed_logo_types'];
    $placeholders = str_repeat('?,', count($config_keys) - 1) . '?';
    
    $stmt = $conn->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ($placeholders)");
    $stmt->bind_param(str_repeat('s', count($config_keys)), ...$config_keys);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $config = [
        'logo_upload_path' => '/uploads/logos/',
        'max_logo_size' => 2048,
        'allowed_logo_types' => 'jpg,jpeg,png,gif'
    ];
    
    while ($row = $result->fetch_assoc()) {
        $config[$row['setting_key']] = $row['setting_value'];
    }
    
    return $config;
}

/**
 * 创建缩略图
 */
function createThumbnail($source, $destination, $width, $height) {
    $image_info = getimagesize($source);
    $mime_type = $image_info['mime'];
    
    switch ($mime_type) {
        case 'image/jpeg':
            $source_image = imagecreatefromjpeg($source);
            break;
        case 'image/png':
            $source_image = imagecreatefrompng($source);
            break;
        case 'image/gif':
            $source_image = imagecreatefromgif($source);
            break;
        default:
            throw new Exception('不支持的图片类型');
    }
    
    $source_width = imagesx($source_image);
    $source_height = imagesy($source_image);
    
    // 计算缩放比例
    $ratio = min($width / $source_width, $height / $source_height);
    $new_width = $source_width * $ratio;
    $new_height = $source_height * $ratio;
    
    // 创建缩略图
    $thumbnail = imagecreatetruecolor($new_width, $new_height);
    
    // 保持透明度（PNG）
    if ($mime_type === 'image/png') {
        imagealphablending($thumbnail, false);
        imagesavealpha($thumbnail, true);
        $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
        imagefill($thumbnail, 0, 0, $transparent);
    }
    
    imagecopyresampled($thumbnail, $source_image, 0, 0, 0, 0, $new_width, $new_height, $source_width, $source_height);
    
    // 保存缩略图
    switch ($mime_type) {
        case 'image/jpeg':
            imagejpeg($thumbnail, $destination, 90);
            break;
        case 'image/png':
            imagepng($thumbnail, $destination);
            break;
        case 'image/gif':
            imagegif($thumbnail, $destination);
            break;
    }
    
    imagedestroy($source_image);
    imagedestroy($thumbnail);
}

/**
 * 删除旧的logo文件
 */
function deleteOldLogo($conn, $upload_path) {
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'app_logo_url'");
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $old_url = $row['setting_value'];
        
        // 只删除上传目录中的文件，不删除默认logo
        if (strpos($old_url, $upload_path) === 0) {
            $old_file = $_SERVER['DOCUMENT_ROOT'] . $old_url;
            if (file_exists($old_file)) {
                unlink($old_file);
            }
            
            // 删除对应的缩略图
            $thumb_file = dirname($old_file) . '/thumb_' . basename($old_file);
            if (file_exists($thumb_file)) {
                unlink($thumb_file);
            }
        }
    }
}
?>
