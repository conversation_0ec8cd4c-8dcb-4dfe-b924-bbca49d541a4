<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建默认轮播图</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>创建默认轮播图</h1>
    
    <canvas id="bannerCanvas" width="800" height="400"></canvas>
    
    <div>
        <button onclick="createDefaultBanner()">创建默认轮播图</button>
        <button onclick="downloadImage()">下载图片</button>
        <button onclick="copyBase64()">复制Base64</button>
    </div>
    
    <div id="result" class="result" style="display: none;"></div>

    <script>
        let canvas, ctx;
        
        window.onload = function() {
            canvas = document.getElementById('bannerCanvas');
            ctx = canvas.getContext('2d');
            createDefaultBanner();
        };
        
        function createDefaultBanner() {
            const width = canvas.width;
            const height = canvas.height;
            
            // 清空画布
            ctx.clearRect(0, 0, width, height);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#007bff');
            gradient.addColorStop(1, '#0056b3');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            // 添加装饰图案
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            for (let i = 0; i < 20; i++) {
                const x = Math.random() * width;
                const y = Math.random() * height;
                const radius = Math.random() * 50 + 10;
                
                ctx.beginPath();
                ctx.arc(x, y, radius, 0, Math.PI * 2);
                ctx.fill();
            }
            
            // 添加文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 48px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 主标题
            ctx.fillText('默认轮播图', width / 2, height / 2 - 30);
            
            // 副标题
            ctx.font = '24px Arial, sans-serif';
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.fillText('Default Banner Image', width / 2, height / 2 + 20);
            
            // 添加边框
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 4;
            ctx.strokeRect(20, 20, width - 40, height - 40);
        }
        
        function downloadImage() {
            const link = document.createElement('a');
            link.download = 'default-banner.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function copyBase64() {
            const base64 = canvas.toDataURL('image/png');
            
            // 复制到剪贴板
            navigator.clipboard.writeText(base64).then(() => {
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `
                    <h3>Base64 已复制到剪贴板</h3>
                    <p>图片尺寸: ${canvas.width} x ${canvas.height}</p>
                    <p>格式: PNG</p>
                    <p>Base64长度: ${base64.length} 字符</p>
                    <details>
                        <summary>查看Base64数据</summary>
                        <textarea style="width: 100%; height: 200px; margin-top: 10px;">${base64}</textarea>
                    </details>
                `;
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            });
        }
        
        // 自动生成并下载
        setTimeout(() => {
            downloadImage();
        }, 1000);
    </script>
</body>
</html>
