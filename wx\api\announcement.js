/**
 * 公告API服务
 * 处理公告列表、详情、阅读状态等公告相关接口
 */

import request from '../utils/request.js';
import CONFIG from '../utils/config.js';
import { getUserInfo } from '../utils/storage.js';

/**
 * 获取公告列表
 * @param {Object} params 查询参数
 * @returns {Promise} 公告列表
 */
export function getAnnouncementList(params = {}) {
	const queryParams = {
		page: params.page || 1,
		limit: params.limit || CONFIG.PAGE_SIZE,
		type: params.type || '',
		category_id: params.categoryId || 0,
		status: params.status || 'published',
		...params
	};
	
	return request.get('announcementList.php', queryParams);
}

/**
 * 获取公告详情
 * @param {Number} announcementId 公告ID
 * @returns {Promise} 公告详情
 */
export function getAnnouncementDetail(announcementId) {
	return request.get('announcementDetail.php', {
		id: announcementId
	});
}

/**
 * 标记公告为已读
 * @param {Number} announcementId 公告ID
 * @returns {Promise} 标记结果
 */
export function markAnnouncementAsRead(announcementId) {
	const userInfo = getUserInfo();

	if (!userInfo || !userInfo.id) {
		return Promise.resolve(); // 未登录用户不记录阅读状态
	}

	return request.post('announcementDetail.php', {
		announcement_id: announcementId,
		user_id: userInfo.id
	});
}

/**
 * 获取公告分类列表
 * @returns {Promise} 分类列表
 */
export function getAnnouncementCategories() {
	return request.get('categoryList.php');
}

/**
 * 获取置顶公告
 * @param {Number} limit 数量限制
 * @returns {Promise} 置顶公告列表
 */
export function getPinnedAnnouncements(limit = 5) {
	return getAnnouncementList({
		limit,
		is_pinned: 1,
		status: 'published'
	});
}

/**
 * 获取紧急公告
 * @param {Number} limit 数量限制
 * @returns {Promise} 紧急公告列表
 */
export function getUrgentAnnouncements(limit = 10) {
	return getAnnouncementList({
		limit,
		type: 'urgent',
		status: 'published'
	});
}

/**
 * 获取系统公告
 * @param {Number} limit 数量限制
 * @returns {Promise} 系统公告列表
 */
export function getSystemAnnouncements(limit = 10) {
	return getAnnouncementList({
		limit,
		type: 'system',
		status: 'published'
	});
}

/**
 * 获取活动公告
 * @param {Number} limit 数量限制
 * @returns {Promise} 活动公告列表
 */
export function getActivityAnnouncements(limit = 10) {
	return getAnnouncementList({
		limit,
		type: 'activity',
		status: 'published'
	});
}

/**
 * 搜索公告
 * @param {String} keyword 搜索关键词
 * @param {Object} params 其他参数
 * @returns {Promise} 搜索结果
 */
export function searchAnnouncements(keyword, params = {}) {
	return getAnnouncementList({
		search: keyword,
		...params
	});
}

/**
 * 获取最新公告
 * @param {Number} limit 数量限制
 * @returns {Promise} 最新公告列表
 */
export function getLatestAnnouncements(limit = 10) {
	return getAnnouncementList({
		limit,
		status: 'published',
		sort: 'created_at',
		order: 'desc'
	});
}

/**
 * 获取用户未读公告数量
 * @returns {Promise} 未读公告数量
 */
export function getUnreadAnnouncementCount() {
	const userInfo = getUserInfo();

	if (!userInfo || !userInfo.id) {
		return Promise.resolve({ data: { count: 0 } });
	}

	return request.get('announcement-unread-count.php', {
		user_id: userInfo.id
	});
}

/**
 * 获取用户已读公告列表
 * @param {Object} params 查询参数
 * @returns {Promise} 已读公告列表
 */
export function getReadAnnouncements(params = {}) {
	const userInfo = getUserInfo();

	if (!userInfo || !userInfo.id) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}

	const queryParams = {
		user_id: userInfo.id,
		page: params.page || 1,
		limit: params.limit || CONFIG.PAGE_SIZE,
		...params
	};

	return request.get('announcement-read.php', queryParams);
}

/**
 * 批量标记公告为已读
 * @param {Array} announcementIds 公告ID数组
 * @returns {Promise} 标记结果
 */
export function markAnnouncementsAsRead(announcementIds) {
	const userInfo = getUserInfo();

	if (!userInfo || !userInfo.id) {
		return Promise.resolve();
	}

	return request.post('announcement-batch-read.php', {
		user_id: userInfo.id,
		announcement_ids: announcementIds
	});
}

/**
 * 获取公告统计信息
 * @returns {Promise} 统计信息
 */
export function getAnnouncementStatistics() {
	const userInfo = getUserInfo();
	
	const params = userInfo ? { user_id: userInfo.id } : {};
	
	return request.get('announcement-statistics.php', params);
}

/**
 * 收藏公告
 * @param {Number} announcementId 公告ID
 * @returns {Promise} 收藏结果
 */
export function favoriteAnnouncement(announcementId) {
	const userInfo = getUserInfo();
	
	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}
	
	return request.post('announcement-favorite.php', {
		user_id: userInfo.id,
		announcement_id: announcementId
	});
}

/**
 * 取消收藏公告
 * @param {Number} announcementId 公告ID
 * @returns {Promise} 取消收藏结果
 */
export function unfavoriteAnnouncement(announcementId) {
	const userInfo = getUserInfo();
	
	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}
	
	return request.delete('announcement-favorite.php', {
		data: {
			user_id: userInfo.id,
			announcement_id: announcementId
		}
	});
}

/**
 * 获取收藏的公告列表
 * @param {Object} params 查询参数
 * @returns {Promise} 收藏公告列表
 */
export function getFavoriteAnnouncements(params = {}) {
	const userInfo = getUserInfo();
	
	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}
	
	const queryParams = {
		user_id: userInfo.id,
		page: params.page || 1,
		limit: params.limit || CONFIG.PAGE_SIZE,
		...params
	};
	
	return request.get('announcement-favorites.php', queryParams);
}

/**
 * 举报公告
 * @param {Number} announcementId 公告ID
 * @param {String} reason 举报原因
 * @returns {Promise} 举报结果
 */
export function reportAnnouncement(announcementId, reason) {
	const userInfo = getUserInfo();
	
	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}
	
	return request.post('announcement-report.php', {
		user_id: userInfo.id,
		announcement_id: announcementId,
		reason
	});
}

/**
 * 获取公告阅读历史
 * @param {Object} params 查询参数
 * @returns {Promise} 阅读历史
 */
export function getAnnouncementReadHistory(params = {}) {
	const userInfo = getUserInfo();

	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}

	const queryParams = {
		user_id: userInfo.id,
		page: params.page || 1,
		limit: params.limit || CONFIG.PAGE_SIZE,
		...params
	};

	return request.get('announcement-read-history.php', queryParams);
}

/**
 * 检查公告收藏状态
 * @param {Number} announcementId 公告ID
 * @returns {Promise} 收藏状态
 */
export function checkAnnouncementFavoriteStatus(announcementId) {
	const userInfo = getUserInfo();

	if (!userInfo) {
		return Promise.reject({ code: 401, message: '请先登录' });
	}

	return request.get('announcement-favorite.php', {
		announcement_id: announcementId
	});
}
