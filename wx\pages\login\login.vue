<template>
	<view class="login-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<text class="navbar-title">登录</text>
			</view>
		</view>
		
		<!-- 登录内容 -->
		<view class="login-content">
			<!-- Logo和标题 -->
			<view class="login-header">
				<image class="logo" :src="systemConfig.app_logo_url" mode="aspectFit" @error="onLogoError"></image>
				<text class="app-title">{{ systemConfig.app_title }}</text>
				<text class="app-subtitle">{{ systemConfig.app_subtitle }}</text>
			</view>
			
			<!-- 微信快速登录 -->
			<view class="wechat-login-section">
				<button
					class="wechat-login-btn"
					@click="handleWechatLogin"
					:loading="wechatLoading"
					:disabled="wechatLoading"
				>
					<text class="wechat-icon">💬</text>
					<text class="btn-text">{{ wechatLoading ? '登录中...' : '微信快速登录' }}</text>
				</button>
			</view>
			
			<!-- 分割线 -->
			<view class="divider">
				<view class="divider-line"></view>
				<text class="divider-text">或</text>
				<view class="divider-line"></view>
			</view>
			
			<!-- 传统登录表单 -->
			<view class="traditional-login" v-show="showTraditionalLogin">
				<uni-forms ref="loginForm" :modelValue="loginForm" :rules="loginRules">
					<uni-forms-item name="credential" required>
						<uni-easyinput 
							v-model="loginForm.credential" 
							placeholder="请输入用户名或邮箱"
							prefixIcon="person"
							:clearable="true"
						></uni-easyinput>
					</uni-forms-item>
					
					<uni-forms-item name="password" required>
						<uni-easyinput 
							v-model="loginForm.password" 
							type="password"
							placeholder="请输入密码"
							prefixIcon="locked"
							:clearable="true"
						></uni-easyinput>
					</uni-forms-item>
					
					<view class="login-options">
						<label class="checkbox-wrapper">
							<checkbox :checked="rememberMe" @change="onRememberMeChange" />
							<text class="checkbox-text">记住我</text>
						</label>
						<text class="forgot-password" @click="handleForgotPassword">忘记密码？</text>
					</view>
					
					<button 
						class="login-btn" 
						@click="handleTraditionalLogin"
						:loading="traditionalLoading"
						:disabled="traditionalLoading"
					>
						{{ traditionalLoading ? '登录中...' : '登录' }}
					</button>
				</uni-forms>
			</view>
			
			<!-- 切换登录方式 -->
			<view class="toggle-login-type">
				<text 
					class="toggle-text" 
					@click="toggleLoginType"
				>
					{{ showTraditionalLogin ? '收起账号密码登录' : '展开账号密码登录' }}
				</text>
			</view>
			
			<!-- 注册链接 -->
			<view class="register-section">
				<text class="register-text">还没有账号？</text>
				<text class="register-link" @click="handleRegister">立即注册</text>
			</view>
		</view>
		
		<!-- 底部信息 -->
		<view class="login-footer">
			<text class="footer-text">登录即表示同意</text>
			<text class="footer-link" @click="showUserAgreement">《用户协议》</text>
			<text class="footer-text">和</text>
			<text class="footer-link" @click="showPrivacyPolicy">《隐私政策》</text>
		</view>
	</view>
</template>

<script>
import { loginWithWechat, loginWithPassword, getWechatLoginCode, getWechatUserInfo } from '../../api/auth.js';
import { showSuccess, showError, showLoading, hideLoading, isLoggedIn } from '../../utils/storage.js';

export default {
	data() {
		return {
			statusBarHeight: 0,
			showTraditionalLogin: false,
			wechatLoading: false,
			traditionalLoading: false,
			rememberMe: false,

			// 系统配置
			systemConfig: {
				app_logo_url: '/static/logo.png',
				app_title: '课程学习系统',
				app_subtitle: '在线学习，随时随地'
			},
			configLoading: false,

			// 登录表单
			loginForm: {
				credential: '',
				password: ''
			},
			
			// 表单验证规则
			loginRules: {
				credential: {
					rules: [
						{
							required: true,
							errorMessage: '请输入用户名或邮箱'
						}
					]
				},
				password: {
					rules: [
						{
							required: true,
							errorMessage: '请输入密码'
						},
						{
							minLength: 6,
							errorMessage: '密码长度不能少于6位'
						}
					]
				}
			}
		};
	},
	
	onLoad() {
		// 获取系统信息
		this.getSystemInfo();

		// 检查是否已登录
		if (isLoggedIn()) {
			this.redirectToHome();
			return;
		}

		// 加载系统配置
		this.loadSystemConfig();

		// 从缓存中恢复记住的用户名
		this.loadRememberedCredential();

		// 调试：测试网络连接
		this.debugNetworkConnection();

		// 测试修复后的request
		this.testFixedRequest();
	},

	onShow() {
		// 页面显示时重新检查配置（防止从后台管理页面返回时配置已更新）
		if (!this.configLoading) {
			this.loadSystemConfig();
		}
	},
	
	methods: {
		/**
		 * 获取系统信息
		 */
		getSystemInfo() {
			try {
				// 使用新的API获取窗口信息
				if (uni.getWindowInfo) {
					const windowInfo = uni.getWindowInfo();
					this.statusBarHeight = windowInfo.statusBarHeight || 0;
				} else {
					// 降级方案
					const systemInfo = uni.getSystemInfoSync();
					this.statusBarHeight = systemInfo.statusBarHeight || 0;
				}
			} catch (error) {
				console.warn('获取系统信息失败:', error);
				this.statusBarHeight = 0;
			}
		},

		/**
		 * 加载系统配置
		 */
		async loadSystemConfig() {
			if (this.configLoading) return;

			this.configLoading = true;

			try {
				// 先尝试从缓存加载（仅用于快速显示）
				const cachedConfig = uni.getStorageSync('system_config');
				const cacheTime = uni.getStorageSync('system_config_time');
				const now = Date.now();

				// 如果缓存存在且未过期（5分钟内），先使用缓存
				if (cachedConfig && cacheTime && (now - cacheTime < 5 * 60 * 1000)) {
					this.systemConfig = { ...this.systemConfig, ...cachedConfig };
				}

				// 总是从API获取最新配置
				const response = await this.fetchSystemConfig();
				if (response && response.code === 200 && response.data) {
					const newConfig = {
						app_logo_url: response.data.app_logo_url || '/static/logo.png',
						app_logo_url_direct: response.data.app_logo_url_direct || null,
						app_title: response.data.app_title || '课程学习系统',
						app_subtitle: response.data.app_subtitle || '在线学习，随时随地'
					};

					// 检查配置是否有变化
					const configChanged = !cachedConfig ||
						cachedConfig.app_logo_url !== newConfig.app_logo_url ||
						cachedConfig.app_title !== newConfig.app_title ||
						cachedConfig.app_subtitle !== newConfig.app_subtitle;

					if (configChanged) {
						console.log('系统配置已更新:', newConfig);
					}

					this.systemConfig = newConfig;

					// 更新缓存
					uni.setStorageSync('system_config', newConfig);
					uni.setStorageSync('system_config_time', now);
				}
			} catch (error) {
				console.warn('加载系统配置失败:', error);
				// 使用默认配置，不显示错误给用户
			} finally {
				this.configLoading = false;
			}
		},

		/**
		 * 获取系统配置API
		 */
		async fetchSystemConfig() {
			return new Promise((resolve, reject) => {
				uni.request({
					url: 'https://wx.yx420.cn/api/system-settings.php',
					method: 'GET',
					timeout: 5000,
					success: (response) => {
						if (response.statusCode === 200) {
							resolve(response.data);
						} else {
							reject(new Error('API响应错误: ' + response.statusCode));
						}
					},
					fail: (error) => {
						reject(error);
					}
				});
			});
		},

		/**
		 * Logo加载错误处理
		 */
		onLogoError() {
			console.warn('Logo加载失败，尝试备用方案');

			// 如果有直接访问的备用URL，尝试使用
			if (this.systemConfig.app_logo_url_direct &&
				this.systemConfig.app_logo_url !== this.systemConfig.app_logo_url_direct) {
				console.log('尝试直接访问URL:', this.systemConfig.app_logo_url_direct);
				this.systemConfig.app_logo_url = this.systemConfig.app_logo_url_direct;
				return;
			}

			// 如果当前是API URL，尝试提取原始路径
			if (this.systemConfig.app_logo_url.includes('/api/wx-image.php?path=')) {
				try {
					const urlParams = new URLSearchParams(this.systemConfig.app_logo_url.split('?')[1]);
					const originalPath = decodeURIComponent(urlParams.get('path'));
					console.log('尝试原始路径:', originalPath);
					this.systemConfig.app_logo_url = originalPath;
					return;
				} catch (e) {
					console.warn('解析原始路径失败:', e);
				}
			}

			// 最后使用默认Logo
			console.log('使用默认Logo');
			this.systemConfig.app_logo_url = '/static/logo.png';
		},

		/**
		 * 清除系统配置缓存
		 */
		clearConfigCache() {
			uni.removeStorageSync('system_config');
			uni.removeStorageSync('system_config_time');
			console.log('系统配置缓存已清除');
		},

		/**
		 * 强制刷新系统配置
		 */
		async forceRefreshConfig() {
			this.clearConfigCache();
			await this.loadSystemConfig();
		},

		/**
		 * 清除系统配置缓存
		 */
		clearConfigCache() {
			uni.removeStorageSync('system_config');
			uni.removeStorageSync('system_config_time');
			console.log('系统配置缓存已清除');
		},

		/**
		 * 强制刷新系统配置
		 */
		async forceRefreshConfig() {
			this.clearConfigCache();
			await this.loadSystemConfig();
		},

		/**
		 * 检查网络状态
		 */
		checkNetworkStatus() {
			return new Promise((resolve) => {
				uni.getNetworkType({
					success: (res) => {
						resolve(res.networkType);
					},
					fail: () => {
						resolve('unknown');
					}
				});
			});
		},

		/**
		 * 微信登录
		 */
		async handleWechatLogin() {
			if (this.wechatLoading) return;

			this.wechatLoading = true;
			showLoading('微信登录中...');

			try {
				// 首先测试API连接
				console.log('开始测试API连接...');
				await this.testApiConnection();

				// 检查网络连接
				const networkType = await this.checkNetworkStatus();
				if (!networkType || networkType === 'none') {
					throw new Error('网络连接失败，请检查网络设置');
				}

				// 获取微信授权码
				const code = await getWechatLoginCode();

				// 获取用户信息（如果已授权）
				let userInfo = {};
				try {
					userInfo = await getWechatUserInfo();
				} catch (error) {
					console.log('获取微信用户信息失败，使用空对象');
				}

				// 调用登录接口
				const response = await loginWithWechat(code, userInfo);

				hideLoading();

				// 检查是否需要绑定手机号
				if (response.code === 202) {
					console.log('需要绑定手机号:', response.data);
					showSuccess('登录成功，请绑定手机号');

					// 跳转到手机号绑定页面
					setTimeout(() => {
						uni.redirectTo({
							url: `/pages/phone-bind/phone-bind?mode=${response.data.phone_bind_mode || 'direct'}&reason=${response.data.phone_bind_reason || 'required'}`
						});
					}, 1000);
				} else if (response.code === 200) {
					showSuccess('登录成功');

					// 跳转到首页
					this.redirectToHome();
				} else {
					// 其他状态码
					console.warn('未知的响应状态码:', response.code);
					showError(response.message || '登录失败');
				}

			} catch (error) {
				hideLoading();
				console.error('微信登录失败:', error);

				// 改进错误信息显示
				let errorMessage = '微信登录失败';
				if (error.message) {
					if (error.message.includes('网络') || error.message.includes('Network')) {
						errorMessage = '网络连接失败，请检查网络设置';
					} else if (error.message.includes('invalid url')) {
						errorMessage = '服务配置错误，请联系管理员';
					} else if (error.message.includes('600009')) {
						errorMessage = 'API地址配置错误，请检查配置';
					} else {
						errorMessage = error.message;
					}
				}

				showError(errorMessage);
			} finally {
				this.wechatLoading = false;
			}
		},

		/**
		 * 测试API连接
		 */
		async testApiConnection() {
			return new Promise((resolve, reject) => {
				// 先测试简单的test.php
				const testUrl = 'https://wx.yx420.cn/api/test.php';
				console.log('测试连接URL:', testUrl);

				uni.request({
					url: testUrl,
					method: 'GET',
					timeout: 5000,
					success: (response) => {
						console.log('API连接测试成功:', response);
						resolve(response);
					},
					fail: (error) => {
						console.error('API连接测试失败:', error);
						console.error('错误详情:', {
							errMsg: error.errMsg,
							errno: error.errno,
							statusCode: error.statusCode
						});

						// 尝试更详细的错误分析
						let errorDetail = 'API连接失败';
						if (error.errMsg) {
							if (error.errMsg.includes('invalid url')) {
								errorDetail = 'URL格式无效，请检查API地址配置';
							} else if (error.errMsg.includes('timeout')) {
								errorDetail = '连接超时，请检查服务器是否运行';
							} else if (error.errMsg.includes('fail')) {
								errorDetail = '网络请求失败，请检查网络设置和服务器状态';
							}
						}

						reject(new Error(errorDetail + ': ' + JSON.stringify(error)));
					}
				});
			});
		},
		
		/**
		 * 传统登录
		 */
		async handleTraditionalLogin() {
			if (this.traditionalLoading) return;
			
			// 表单验证
			try {
				await this.$refs.loginForm.validate();
			} catch (error) {
				console.log('表单验证失败:', error);
				return;
			}
			
			this.traditionalLoading = true;
			showLoading('登录中...');
			
			try {
				await loginWithPassword(
					this.loginForm.credential,
					this.loginForm.password
				);

				hideLoading();
				showSuccess('登录成功');
				
				// 记住用户名
				if (this.rememberMe) {
					this.saveRememberedCredential();
				} else {
					this.clearRememberedCredential();
				}
				
				// 跳转到首页
				this.redirectToHome();
				
			} catch (error) {
				hideLoading();
				console.error('登录失败:', error);
				showError(error.message || '登录失败');
			} finally {
				this.traditionalLoading = false;
			}
		},
		
		/**
		 * 切换登录方式
		 */
		toggleLoginType() {
			this.showTraditionalLogin = !this.showTraditionalLogin;
		},
		
		/**
		 * 记住我选项变化
		 */
		onRememberMeChange(e) {
			this.rememberMe = e.detail.value.length > 0;
		},
		
		/**
		 * 忘记密码
		 */
		handleForgotPassword() {
			uni.showModal({
				title: '忘记密码',
				content: '请联系管理员重置密码',
				showCancel: false
			});
		},
		
		/**
		 * 注册
		 */
		handleRegister() {
			uni.showModal({
				title: '用户注册',
				content: '请联系管理员开通账号',
				showCancel: false
			});
		},
		
		/**
		 * 显示用户协议
		 */
		showUserAgreement() {
			uni.showModal({
				title: '用户协议',
				content: '这里是用户协议内容...',
				showCancel: false
			});
		},
		
		/**
		 * 显示隐私政策
		 */
		showPrivacyPolicy() {
			uni.showModal({
				title: '隐私政策',
				content: '这里是隐私政策内容...',
				showCancel: false
			});
		},
		
		/**
		 * 跳转到首页
		 */
		redirectToHome() {
			uni.switchTab({
				url: '/pages/index/index'
			});
		},
		
		/**
		 * 保存记住的用户名
		 */
		saveRememberedCredential() {
			uni.setStorageSync('remembered_credential', this.loginForm.credential);
		},
		
		/**
		 * 清除记住的用户名
		 */
		clearRememberedCredential() {
			uni.removeStorageSync('remembered_credential');
		},
		
		/**
		 * 加载记住的用户名
		 */
		loadRememberedCredential() {
			const credential = uni.getStorageSync('remembered_credential');
			if (credential) {
				this.loginForm.credential = credential;
				this.rememberMe = true;
			}
		},

		/**
		 * 调试网络连接
		 */
		debugNetworkConnection() {
			console.log('开始调试网络连接...');

			// 测试1: 简单的uni.request
			uni.request({
				url: 'https://wx.yx420.cn/api/test.php',
				method: 'GET',
				success: (res) => {
					console.log('直接uni.request测试成功:', res);
				},
				fail: (err) => {
					console.error('直接uni.request测试失败:', err);

					// 显示详细错误信息
					uni.showModal({
						title: '网络连接测试',
						content: `连接失败: ${err.errMsg || JSON.stringify(err)}`,
						showCancel: false
					});
				}
			});
		},

		/**
		 * 测试修复后的request
		 */
		async testFixedRequest() {
			console.log('测试修复后的request...');

			try {
				// 导入request
				const request = (await import('../../utils/request.js')).default;

				const response = await request.get('test.php');
				console.log('修复后的request测试成功:', response);
			} catch (error) {
				console.error('修复后的request测试失败:', error);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	flex-direction: column;
}

.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10px);
	
	.navbar-content {
		height: 44px;
		display: flex;
		align-items: center;
		justify-content: center;
		
		.navbar-title {
			color: #fff;
			font-size: 18px;
			font-weight: 500;
		}
	}
}

.login-content {
	flex: 1;
	padding: 120px 40px 40px;
	display: flex;
	flex-direction: column;
}

.login-header {
	text-align: center;
	margin-bottom: 60px;
	
	.logo {
		width: 80px;
		height: 80px;
		margin-bottom: 20px;
	}
	
	.app-title {
		display: block;
		color: #fff;
		font-size: 28px;
		font-weight: bold;
		margin-bottom: 8px;
	}
	
	.app-subtitle {
		display: block;
		color: rgba(255, 255, 255, 0.8);
		font-size: 16px;
	}
}

.wechat-login-section {
	margin-bottom: 30px;
	
	.wechat-login-btn {
		width: 100%;
		height: 50px;
		background: #07c160;
		border-radius: 25px;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;

		.wechat-icon {
			color: #fff;
			margin-right: 8px;
			font-size: 20px;
		}

		.btn-text {
			color: #fff;
			font-size: 16px;
			margin-left: 8px;
		}
	}
}

.divider {
	display: flex;
	align-items: center;
	margin: 30px 0;
	
	.divider-line {
		flex: 1;
		height: 1px;
		background: rgba(255, 255, 255, 0.3);
	}
	
	.divider-text {
		color: rgba(255, 255, 255, 0.8);
		font-size: 14px;
		margin: 0 20px;
	}
}

.traditional-login {
	.login-options {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 20px 0;
		
		.checkbox-wrapper {
			display: flex;
			align-items: center;
			
			.checkbox-text {
				color: rgba(255, 255, 255, 0.8);
				font-size: 14px;
				margin-left: 8px;
			}
		}
		
		.forgot-password {
			color: rgba(255, 255, 255, 0.8);
			font-size: 14px;
		}
	}
	
	.login-btn {
		width: 100%;
		height: 50px;
		background: rgba(255, 255, 255, 0.2);
		border: 1px solid rgba(255, 255, 255, 0.3);
		border-radius: 25px;
		color: #fff;
		font-size: 16px;
		margin-top: 20px;
	}
}

.toggle-login-type {
	text-align: center;
	margin: 30px 0;
	
	.toggle-text {
		color: rgba(255, 255, 255, 0.8);
		font-size: 14px;
		text-decoration: underline;
	}
}

.register-section {
	text-align: center;
	margin-top: 20px;
	
	.register-text {
		color: rgba(255, 255, 255, 0.8);
		font-size: 14px;
	}
	
	.register-link {
		color: #fff;
		font-size: 14px;
		margin-left: 8px;
		text-decoration: underline;
	}
}

.login-footer {
	text-align: center;
	padding: 20px 40px;
	
	.footer-text {
		color: rgba(255, 255, 255, 0.6);
		font-size: 12px;
	}
	
	.footer-link {
		color: rgba(255, 255, 255, 0.8);
		font-size: 12px;
		text-decoration: underline;
	}
}
</style>
