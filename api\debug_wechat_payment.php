<?php
/**
 * 微信支付详细调试脚本
 * 检查具体的错误原因和配置问题
 */

header('Content-Type: text/html; charset=utf-8');
require_once '../includes/db.php';
require_once '../includes/auth.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='utf-8'><title>微信支付调试</title></head><body>";
echo "<h1>微信支付详细调试</h1>";

// 测试微信支付配置
function testWechatPayConfig($conn) {
    echo "<h2>📋 微信支付配置检查</h2>";
    
    // 获取配置
    $stmt = $conn->prepare("
        SELECT setting_key, setting_value 
        FROM settings 
        WHERE setting_key LIKE 'wechat_pay_%' 
        ORDER BY setting_key
    ");
    $stmt->execute();
    $settings_result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    if (empty($settings_result)) {
        echo "<p style='color: red;'>❌ 没有找到微信支付配置</p>";
        return false;
    }
    
    $settings = [];
    foreach ($settings_result as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>配置项</th><th>值</th><th>状态</th></tr>";
    
    // 检查各项配置
    $config_checks = [
        'wechat_pay_enabled' => '是否启用',
        'wechat_pay_app_id' => 'AppID',
        'wechat_pay_mch_id' => '商户号',
        'wechat_pay_api_key' => 'API密钥',
        'wechat_pay_notify_url' => '回调URL'
    ];
    
    $all_good = true;
    foreach ($config_checks as $key => $name) {
        $value = $settings[$key] ?? '';
        $status = '';
        
        if ($key === 'wechat_pay_enabled') {
            $status = ($value === '1') ? '<span style="color: green;">✅ 已启用</span>' : '<span style="color: red;">❌ 未启用</span>';
            if ($value !== '1') $all_good = false;
        } elseif ($key === 'wechat_pay_app_id') {
            if (preg_match('/^wx[a-f0-9]{16}$/', $value)) {
                $status = '<span style="color: green;">✅ 格式正确</span>';
            } else {
                $status = '<span style="color: red;">❌ 格式错误</span>';
                $all_good = false;
            }
        } elseif ($key === 'wechat_pay_mch_id') {
            if (preg_match('/^\d{10}$/', $value)) {
                $status = '<span style="color: green;">✅ 格式正确</span>';
            } else {
                $status = '<span style="color: red;">❌ 格式错误（应为10位数字）</span>';
                $all_good = false;
            }
        } elseif ($key === 'wechat_pay_api_key') {
            if (strlen($value) === 32) {
                $status = '<span style="color: green;">✅ 长度正确</span>';
                $value = str_repeat('*', 32); // 隐藏密钥
            } else {
                $status = '<span style="color: red;">❌ 长度错误（应为32位）</span>';
                $all_good = false;
            }
        } else {
            $status = !empty($value) ? '<span style="color: green;">✅ 已设置</span>' : '<span style="color: red;">❌ 未设置</span>';
            if (empty($value)) $all_good = false;
        }
        
        echo "<tr><td>$name</td><td>$value</td><td>$status</td></tr>";
    }
    
    echo "</table>";
    
    if ($all_good) {
        echo "<p style='color: green;'>✅ 所有配置项都正确</p>";
    } else {
        echo "<p style='color: red;'>❌ 发现配置问题</p>";
    }
    
    return $settings;
}

// 测试微信支付API调用
function testWechatPayAPI($settings) {
    echo "<h2>🔌 微信支付API测试</h2>";
    
    if (empty($settings)) {
        echo "<p style='color: red;'>❌ 配置不完整，跳过API测试</p>";
        return;
    }
    
    // 构造测试参数
    $test_params = [
        'appid' => $settings['wechat_pay_app_id'],
        'mch_id' => $settings['wechat_pay_mch_id'],
        'nonce_str' => generateNonceStr(),
        'body' => '测试订单',
        'out_trade_no' => 'TEST' . date('YmdHis') . rand(1000, 9999),
        'total_fee' => 1, // 1分钱
        'spbill_create_ip' => '127.0.0.1',
        'notify_url' => $settings['wechat_pay_notify_url'] ?: 'https://your-domain.com/api/payment-wechat-notify.php',
        'trade_type' => 'JSAPI',
        'openid' => 'test_openid'
    ];
    
    // 生成签名
    $test_params['sign'] = generateWechatSign($test_params, $settings['wechat_pay_api_key']);
    
    echo "<h3>测试参数:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>参数</th><th>值</th></tr>";
    foreach ($test_params as $key => $value) {
        if ($key === 'sign') {
            $value = substr($value, 0, 10) . '...'; // 只显示前10位
        }
        echo "<tr><td>$key</td><td>$value</td></tr>";
    }
    echo "</table>";
    
    // 调用API
    $xml_data = arrayToXml($test_params);
    
    echo "<h3>发送的XML数据:</h3>";
    echo "<pre>" . htmlspecialchars($xml_data) . "</pre>";
    
    $response = callWechatAPI('https://api.mch.weixin.qq.com/pay/unifiedorder', $xml_data);
    
    if ($response) {
        echo "<h3>微信返回结果:</h3>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        
        $result = xmlToArray($response);
        
        echo "<h3>解析后的结果:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>字段</th><th>值</th></tr>";
        foreach ($result as $key => $value) {
            echo "<tr><td>$key</td><td>$value</td></tr>";
        }
        echo "</table>";
        
        // 分析错误
        if (isset($result['return_code']) && $result['return_code'] === 'SUCCESS') {
            if (isset($result['result_code']) && $result['result_code'] === 'SUCCESS') {
                echo "<p style='color: green;'>✅ API调用成功</p>";
            } else {
                echo "<p style='color: red;'>❌ 业务处理失败: " . ($result['err_code_des'] ?? $result['err_code'] ?? '未知错误') . "</p>";
                
                // 分析具体错误
                if (isset($result['err_code'])) {
                    analyzeWechatError($result['err_code'], $result['err_code_des'] ?? '');
                }
            }
        } else {
            echo "<p style='color: red;'>❌ API调用失败: " . ($result['return_msg'] ?? '未知错误') . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ 无法调用微信支付API</p>";
    }
}

// 分析微信支付错误
function analyzeWechatError($err_code, $err_code_des) {
    echo "<h3>🔍 错误分析</h3>";
    
    $error_solutions = [
        'APPID_NOT_EXIST' => '检查AppID是否正确，确保在微信支付商户平台已添加该AppID',
        'MCHID_NOT_EXIST' => '检查商户号是否正确，确保商户号已通过微信支付商户认证',
        'APPID_MCHID_NOT_MATCH' => '最常见错误！AppID和商户号不匹配，请确保：1. AppID已在商户平台绑定 2. 商户号正确 3. 账号权限正常',
        'LACK_PARAMS' => '缺少必要参数，检查所有必需的参数是否都已提供',
        'OUT_TRADE_NO_USED' => '商户订单号重复，更换一个新的订单号',
        'NOAUTH' => '商户号未开通此接口权限，联系微信支付客服开通',
        'AMOUNT_LIMIT' => '金额超限，检查支付金额是否在允许范围内',
        'NOT_UTF8' => '编码格式错误，确保所有参数都是UTF-8编码',
        'REQUIRE_POST_METHOD' => '请求方式错误，必须使用POST方法',
        'SIGNERROR' => '签名错误，检查签名算法和API密钥是否正确',
        'XML_FORMAT_ERROR' => 'XML格式错误，检查XML数据结构',
        'SYSTEMERROR' => '系统错误，稍后重试或联系微信支付客服'
    ];
    
    if (isset($error_solutions[$err_code])) {
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
        echo "<h4>错误码: $err_code</h4>";
        echo "<p><strong>错误描述:</strong> $err_code_des</p>";
        echo "<p><strong>解决方案:</strong> {$error_solutions[$err_code]}</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
        echo "<h4>错误码: $err_code</h4>";
        echo "<p><strong>错误描述:</strong> $err_code_des</p>";
        echo "<p><strong>建议:</strong> 查看微信支付官方文档或联系微信支付客服</p>";
        echo "</div>";
    }
}

// 辅助函数
function generateNonceStr($length = 32) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $str = '';
    for ($i = 0; $i < $length; $i++) {
        $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
    }
    return $str;
}

function generateWechatSign($params, $key) {
    ksort($params);
    $string = '';
    foreach ($params as $k => $v) {
        if ($k !== 'sign' && $v !== '' && $v !== null) {
            $string .= $k . '=' . $v . '&';
        }
    }
    $string .= 'key=' . $key;
    return strtoupper(md5($string));
}

function arrayToXml($array) {
    $xml = '<xml>';
    foreach ($array as $key => $val) {
        $xml .= '<' . $key . '><![CDATA[' . $val . ']]></' . $key . '>';
    }
    $xml .= '</xml>';
    return $xml;
}

function xmlToArray($xml) {
    return json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
}

function callWechatAPI($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p style='color: red;'>CURL错误: $error</p>";
        return false;
    }
    
    return $response;
}

// 执行测试
try {
    $settings = testWechatPayConfig($conn);
    
    if ($settings && $settings['wechat_pay_enabled'] === '1') {
        testWechatPayAPI($settings);
    }
    
    echo "<div style='background: #e2f3ff; padding: 20px; border: 1px solid #b8daff; margin: 20px 0;'>";
    echo "<h3>💡 常见问题解决方案</h3>";
    echo "<ol>";
    echo "<li><strong>APPID_MCHID_NOT_MATCH</strong>: 登录微信支付商户平台，在产品中心->AppID授权管理中绑定小程序AppID</li>";
    echo "<li><strong>NOAUTH</strong>: 在商户平台开通JSAPI支付权限</li>";
    echo "<li><strong>SIGNERROR</strong>: 检查API密钥是否正确，确保是32位字符串</li>";
    echo "<li><strong>商户号格式错误</strong>: 确保商户号是10位数字</li>";
    echo "<li><strong>AppID格式错误</strong>: 确保AppID以'wx'开头，后跟16位十六进制字符</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 测试失败: " . $e->getMessage() . "</p>";
}

echo "<p><a href='check_wechat_config.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none;'>返回配置检查</a></p>";
echo "</body></html>";
?>