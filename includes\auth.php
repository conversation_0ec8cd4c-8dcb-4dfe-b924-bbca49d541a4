<?php
/**
 * 认证辅助函数
 * 提供简单的认证函数供API使用
 */

require_once __DIR__ . '/../api/auth.php';

/**
 * 验证用户身份
 * @return array|null 用户信息或null
 */
function authenticate_user() {
    try {
        $auth = new AuthAPI();
        return $auth->getCurrentUser();
    } catch (Exception $e) {
        error_log('Authentication error: ' . $e->getMessage());
        return null;
    }
}

/**
 * 要求用户认证
 * @return array 用户信息
 */
function require_auth() {
    try {
        $auth = new AuthAPI();
        return $auth->requireAuth();
    } catch (Exception $e) {
        error_log('Authentication error: ' . $e->getMessage());
        http_response_code(401);
        echo json_encode([
            'code' => 401,
            'message' => '未授权访问',
            'data' => null
        ]);
        exit;
    }
}

/**
 * 获取Bearer Token
 * @return string|null Token或null
 */
function get_bearer_token() {
    try {
        $auth = new AuthAPI();
        return $auth->getBearerToken();
    } catch (Exception $e) {
        error_log('Get token error: ' . $e->getMessage());
        return null;
    }
}

/**
 * 验证JWT Token
 * @param string $token JWT Token
 * @return array|false Token数据或false
 */
function verify_jwt_token($token) {
    try {
        $auth = new AuthAPI();
        return $auth->verifyJWT($token);
    } catch (Exception $e) {
        error_log('Token verification error: ' . $e->getMessage());
        return false;
    }
}

/**
 * 生成JWT Token
 * @param int $user_id 用户ID
 * @param string $type Token类型
 * @return string JWT Token
 */
function generate_jwt_token($user_id, $type = 'access') {
    try {
        $auth = new AuthAPI();
        return $auth->generateJWT($user_id, $type);
    } catch (Exception $e) {
        error_log('Token generation error: ' . $e->getMessage());
        return null;
    }
}

/**
 * 记录登录日志
 * @param int $user_id 用户ID
 * @param string $login_type 登录类型
 * @param string $login_method 登录方法
 * @param string $status 状态
 * @param string $failure_reason 失败原因
 * @return bool 是否成功
 */
function log_login($user_id, $login_type, $login_method, $status, $failure_reason = null) {
    try {
        $auth = new AuthAPI();
        return $auth->logLogin($user_id, $login_type, $login_method, $status, $failure_reason);
    } catch (Exception $e) {
        error_log('Login log error: ' . $e->getMessage());
        return false;
    }
}

/**
 * 更新用户最后登录信息
 * @param int $user_id 用户ID
 * @return bool 是否成功
 */
function update_last_login($user_id) {
    try {
        $auth = new AuthAPI();
        return $auth->updateLastLogin($user_id);
    } catch (Exception $e) {
        error_log('Update last login error: ' . $e->getMessage());
        return false;
    }
}

/**
 * 获取用户信息
 * @param int $user_id 用户ID
 * @return array|null 用户信息或null
 */
function get_user_by_id($user_id) {
    try {
        $auth = new AuthAPI();
        return $auth->getUserById($user_id);
    } catch (Exception $e) {
        error_log('Get user error: ' . $e->getMessage());
        return null;
    }
}

/**
 * 验证密码
 * @param string $password 明文密码
 * @param string $hash 哈希密码
 * @return bool 是否匹配
 */
function verify_password($password, $hash) {
    try {
        $auth = new AuthAPI();
        return $auth->verifyPassword($password, $hash);
    } catch (Exception $e) {
        error_log('Password verification error: ' . $e->getMessage());
        return false;
    }
}

/**
 * 哈希密码
 * @param string $password 明文密码
 * @return string 哈希密码
 */
function hash_password($password) {
    try {
        $auth = new AuthAPI();
        return $auth->hashPassword($password);
    } catch (Exception $e) {
        error_log('Password hashing error: ' . $e->getMessage());
        return null;
    }
}
?>
