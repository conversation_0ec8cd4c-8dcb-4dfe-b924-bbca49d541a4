<?php
/**
 * 获取当前用户信息API
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

$auth = new AuthAPI();

try {
    // 验证用户认证
    $user = $auth->requireAuth();
    
    // 返回用户信息
    $auth->jsonResponse(200, '获取成功', [
        'user' => [
            'id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'username' => $user['username'],
            'nickname' => $user['nickname'],
            'avatar' => $user['avatar'],
            'phone' => $user['phone'],
            'gender' => $user['gender'],
            'birthday' => $user['birthday'],
            'status' => $user['status'],
            'login_type' => $user['login_type'],
            'last_login_at' => $user['last_login_at'],
            'login_count' => $user['login_count'],
            'created_at' => $user['created_at']
        ]
    ]);
    
} catch (Exception $e) {
    error_log('获取用户信息错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}