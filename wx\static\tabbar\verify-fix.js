/**
 * TabBar图标修复验证脚本
 * 验证所有图标文件是否存在且配置正确
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 TabBar图标修复验证\n');

// 检查图标文件
const requiredIcons = [
    'home.png',
    'home-active.png', 
    'course.png',
    'course-active.png',
    'profile.png',
    'profile-active.png'
];

console.log('📁 检查图标文件:');
let allIconsExist = true;

requiredIcons.forEach(iconFile => {
    const iconPath = path.join(__dirname, iconFile);
    if (fs.existsSync(iconPath)) {
        const stats = fs.statSync(iconPath);
        const sizeKB = (stats.size / 1024).toFixed(1);
        console.log(`  ✅ ${iconFile} (${sizeKB}KB)`);
    } else {
        console.log(`  ❌ ${iconFile} - 文件不存在`);
        allIconsExist = false;
    }
});

// 检查pages.json配置
console.log('\n📋 检查pages.json配置:');
const pagesJsonPath = path.join(__dirname, '../../pages.json');

if (fs.existsSync(pagesJsonPath)) {
    try {
        const pagesJson = JSON.parse(fs.readFileSync(pagesJsonPath, 'utf8'));
        
        if (pagesJson.tabBar && pagesJson.tabBar.list) {
            console.log('  ✅ tabBar配置存在');
            
            const expectedPaths = [
                'static/tabbar/home.png',
                'static/tabbar/home-active.png',
                'static/tabbar/course.png', 
                'static/tabbar/course-active.png',
                'static/tabbar/profile.png',
                'static/tabbar/profile-active.png'
            ];
            
            let configCorrect = true;
            pagesJson.tabBar.list.forEach((item, index) => {
                const expectedIcon = expectedPaths[index * 2];
                const expectedActiveIcon = expectedPaths[index * 2 + 1];
                
                if (item.iconPath === expectedIcon && item.selectedIconPath === expectedActiveIcon) {
                    console.log(`  ✅ ${item.text} 图标配置正确`);
                } else {
                    console.log(`  ❌ ${item.text} 图标配置错误`);
                    console.log(`    期望: ${expectedIcon} / ${expectedActiveIcon}`);
                    console.log(`    实际: ${item.iconPath} / ${item.selectedIconPath}`);
                    configCorrect = false;
                }
            });
            
            if (configCorrect) {
                console.log('  ✅ 所有图标路径配置正确');
            }
        } else {
            console.log('  ❌ tabBar配置不存在');
        }
    } catch (error) {
        console.log(`  ❌ pages.json解析错误: ${error.message}`);
    }
} else {
    console.log('  ❌ pages.json文件不存在');
}

// 总结
console.log('\n📊 验证结果:');
if (allIconsExist) {
    console.log('✅ 所有图标文件已就绪');
    console.log('✅ TabBar图标问题已完全修复');
    console.log('\n🚀 下一步操作:');
    console.log('1. 重新编译微信小程序项目');
    console.log('2. 在开发者工具中查看TabBar效果');
    console.log('3. 测试图标切换功能');
} else {
    console.log('❌ 部分图标文件缺失');
    console.log('\n🔧 修复建议:');
    console.log('1. 运行 download-icons.ps1 重新下载图标');
    console.log('2. 或手动下载缺失的图标文件');
}

console.log('\n📱 预期效果:');
console.log('- 首页: 房子图标');
console.log('- 课程: 视频播放图标'); 
console.log('- 我的: 用户头像图标');
console.log('- 选中状态: 蓝色填充');
console.log('- 未选中状态: 灰色线条');

console.log('\n🎨 图标规格:');
console.log('- 尺寸: 81x81px');
console.log('- 格式: PNG');
console.log('- 普通颜色: #7A7E83');
console.log('- 选中颜色: #007bff');
console.log('- 来源: Icons8');
