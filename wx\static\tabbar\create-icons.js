/**
 * TabBar图标下载和配置脚本
 * 提供多种图标解决方案
 */

// 在线图标资源 - 可直接使用的CDN图标
const ONLINE_ICONS = {
  // Icons8 图标 (推荐)
  icons8: {
    home: {
      normal: 'https://img.icons8.com/ios/50/7A7E83/home--v1.png',
      active: 'https://img.icons8.com/ios-filled/50/007bff/home--v1.png'
    },
    course: {
      normal: 'https://img.icons8.com/ios/50/7A7E83/video.png',
      active: 'https://img.icons8.com/ios-filled/50/007bff/video.png'
    },
    profile: {
      normal: 'https://img.icons8.com/ios/50/7A7E83/user.png',
      active: 'https://img.icons8.com/ios-filled/50/007bff/user.png'
    }
  },

  // Feather Icons (备用)
  feather: {
    home: {
      normal: 'https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/icons/home.svg',
      active: 'https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/icons/home.svg'
    },
    course: {
      normal: 'https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/icons/play-circle.svg',
      active: 'https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/icons/play-circle.svg'
    },
    profile: {
      normal: 'https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/icons/user.svg',
      active: 'https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/icons/user.svg'
    }
  }
};

// uni-icons 内置图标名称
const UNI_ICONS = {
  home: 'home',
  course: 'videocam',
  profile: 'person'
};

// 图标下载链接 (可直接访问下载)
const DOWNLOAD_LINKS = {
  // 高质量PNG图标下载链接
  home_normal: 'https://img.icons8.com/ios/81/7A7E83/home--v1.png',
  home_active: 'https://img.icons8.com/ios-filled/81/007bff/home--v1.png',
  course_normal: 'https://img.icons8.com/ios/81/7A7E83/video.png',
  course_active: 'https://img.icons8.com/ios-filled/81/007bff/video.png',
  profile_normal: 'https://img.icons8.com/ios/81/7A7E83/user.png',
  profile_active: 'https://img.icons8.com/ios-filled/81/007bff/user.png'
};

console.log('=== TabBar图标解决方案 ===\n');

console.log('方案1: 在线图标 (推荐)');
console.log('直接在pages.json中使用在线图标URL');
console.log('优点: 无需下载，即时可用');
console.log('缺点: 需要网络连接\n');

console.log('方案2: 下载本地图标');
console.log('请访问以下链接下载图标:');
Object.entries(DOWNLOAD_LINKS).forEach(([name, url]) => {
  console.log(`${name}: ${url}`);
});
console.log('\n');

console.log('方案3: 使用uni-icons内置图标');
console.log('在tabBar中使用iconfont字体图标');
console.log('优点: 体积小，加载快');
console.log('缺点: 样式选择有限\n');

console.log('=== 推荐配置 ===');
console.log('建议使用方案1(在线图标)作为主要方案');
console.log('方案2(本地图标)作为备用方案');
console.log('方案3(uni-icons)作为最终备用方案');
