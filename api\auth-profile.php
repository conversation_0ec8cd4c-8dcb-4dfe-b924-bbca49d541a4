<?php
/**
 * 用户资料更新API
 * 专门处理用户资料的更新操作
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: PUT, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    // 验证用户认证
    $user = $auth->requireAuth();
    
    if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        updateUserProfile($auth, $user);
    } else {
        $auth->jsonResponse(405, '不支持的请求方法');
    }
} catch (Exception $e) {
    error_log('用户资料更新API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 更新用户资料
 */
function updateUserProfile($auth, $user) {
    $conn = $auth->getConn();
    
    $user_id = $user['id'];
    
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $auth->jsonResponse(400, '请求数据格式错误');
    }
    
    // 允许更新的字段
    $allowed_fields = ['name', 'nickname', 'phone', 'gender', 'birthday', 'avatar'];
    $update_fields = [];
    $params = [];
    $param_types = "";

    // 输入验证
    foreach ($allowed_fields as $field) {
        if (isset($input[$field])) {
            $value = $input[$field];

            // 字段特定验证
            switch ($field) {
                case 'name':
                case 'nickname':
                    if (empty(trim($value))) {
                        $auth->jsonResponse(400, $field === 'name' ? '姓名不能为空' : '昵称不能为空');
                    }
                    if (strlen($value) > 50) {
                        $auth->jsonResponse(400, $field === 'name' ? '姓名长度不能超过50个字符' : '昵称长度不能超过50个字符');
                    }
                    break;

                case 'phone':
                    if (!empty($value) && !preg_match('/^1[3-9]\d{9}$/', $value)) {
                        $auth->jsonResponse(400, '手机号格式不正确');
                    }
                    break;

                case 'gender':
                    if (!in_array($value, [0, 1, 2, '0', '1', '2'])) {
                        $auth->jsonResponse(400, '性别值无效');
                    }
                    break;

                case 'birthday':
                    if (!empty($value) && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
                        $auth->jsonResponse(400, '生日格式不正确，请使用YYYY-MM-DD格式');
                    }
                    // 如果是空字符串，转换为NULL
                    if ($value === '') {
                        $value = null;
                    }
                    break;

                case 'avatar':
                    if (!empty($value) && strlen($value) > 500) {
                        $auth->jsonResponse(400, '头像URL长度不能超过500个字符');
                    }
                    break;
            }

            $update_fields[] = "$field = ?";
            $params[] = $value;

            // 根据字段类型设置参数类型
            if ($field === 'gender') {
                $param_types .= "i";
            } elseif ($field === 'birthday' && $value === null) {
                $param_types .= "s"; // NULL值也用字符串类型
            } else {
                $param_types .= "s";
            }
        }
    }

    if (empty($update_fields)) {
        $auth->jsonResponse(400, '没有可更新的字段');
    }
    
    // 特殊处理性别字段
    if (isset($input['gender'])) {
        $gender_index = array_search('gender = ?', $update_fields);
        if ($gender_index !== false) {
            $param_types = substr_replace($param_types, 'i', $gender_index, 1);
            $params[$gender_index] = (int)$input['gender'];
        }
    }
    
    // 添加更新时间
    $update_fields[] = "updated_at = NOW()";
    
    // 构建SQL
    $sql = "UPDATE users SET " . implode(", ", $update_fields) . " WHERE id = ?";
    $params[] = $user_id;
    $param_types .= "i";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($param_types, ...$params);
    
    if ($stmt->execute()) {
        // 检查是否有行被更新
        if ($stmt->affected_rows === 0) {
            $auth->jsonResponse(400, '没有数据被更新，请检查输入的数据是否有变化');
        }

        // 获取更新后的用户信息
        $updated_user = $auth->getUserById($user_id);

        if (!$updated_user) {
            error_log('更新后获取用户信息失败，用户ID: ' . $user_id);
            $auth->jsonResponse(500, '更新成功但获取用户信息失败');
        }

        $auth->jsonResponse(200, '更新用户资料成功', [
            'user' => $updated_user,
            'updated_fields' => array_keys(array_intersect_key($input, array_flip($allowed_fields)))
        ]);
    } else {
        $error_msg = $conn->error;
        error_log('更新用户资料失败: ' . $error_msg . ', SQL: ' . $sql . ', 参数: ' . json_encode($params));

        // 根据错误类型提供更具体的错误信息
        if (strpos($error_msg, 'Duplicate entry') !== false) {
            if (strpos($error_msg, 'phone') !== false) {
                $auth->jsonResponse(400, '该手机号已被其他用户使用');
            } elseif (strpos($error_msg, 'email') !== false) {
                $auth->jsonResponse(400, '该邮箱已被其他用户使用');
            } else {
                $auth->jsonResponse(400, '数据重复，请检查输入信息');
            }
        } elseif (strpos($error_msg, 'Data too long') !== false) {
            $auth->jsonResponse(400, '输入的数据过长，请检查输入内容');
        } else {
            $auth->jsonResponse(500, '更新用户资料失败：' . $error_msg);
        }
    }
}
?>
