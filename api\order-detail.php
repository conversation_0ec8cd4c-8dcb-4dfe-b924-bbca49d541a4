<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db.php';
require_once '../includes/auth.php';

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 验证用户身份
$user = authenticate_user();
if (!$user) {
    http_response_code(401);
    echo json_encode([
        'code' => 401,
        'message' => '未授权访问',
        'data' => null
    ]);
    exit;
}

// 获取订单ID
$order_id = intval($_GET['id'] ?? 0);
if ($order_id <= 0) {
    echo json_encode([
        'code' => 400,
        'message' => '订单ID无效',
        'data' => null
    ]);
    exit;
}

try {
    $user_id = $user['id'];
    
    // 查询订单基本信息
    $stmt = $conn->prepare("
        SELECT 
            o.*,
            u.name as user_name,
            u.email as user_email
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.id
        WHERE o.id = ? AND o.user_id = ?
    ");
    $stmt->bind_param("ii", $order_id, $user_id);
    $stmt->execute();
    $order = $stmt->get_result()->fetch_assoc();
    
    if (!$order) {
        echo json_encode([
            'code' => 404,
            'message' => '订单不存在',
            'data' => null
        ]);
        exit;
    }
    
    // 查询订单商品
    $stmt = $conn->prepare("
        SELECT 
            oi.*,
            c.thumbnail,
            c.cover_image,
            c.status as course_status,
            c.is_on_sale
        FROM order_items oi
        LEFT JOIN courses c ON oi.course_id = c.id
        WHERE oi.order_id = ?
        ORDER BY oi.id
    ");
    $stmt->bind_param("i", $order_id);
    $stmt->execute();
    $items = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // 查询支付记录
    $stmt = $conn->prepare("
        SELECT *
        FROM payments
        WHERE order_id = ?
        ORDER BY created_at DESC
    ");
    $stmt->bind_param("i", $order_id);
    $stmt->execute();
    $payments = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // 查询退款记录
    $stmt = $conn->prepare("
        SELECT 
            r.*,
            a.username as processed_by_name
        FROM refunds r
        LEFT JOIN admins a ON r.processed_by = a.id
        WHERE r.order_id = ?
        ORDER BY r.created_at DESC
    ");
    $stmt->bind_param("i", $order_id);
    $stmt->execute();
    $refunds = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // 格式化订单数据
    $order['total_amount'] = floatval($order['total_amount']);
    $order['discount_amount'] = floatval($order['discount_amount']);
    $order['actual_amount'] = floatval($order['actual_amount']);
    
    // 格式化时间
    $order['created_at'] = date('Y-m-d H:i:s', strtotime($order['created_at']));
    $order['updated_at'] = date('Y-m-d H:i:s', strtotime($order['updated_at']));
    $order['expire_time'] = date('Y-m-d H:i:s', strtotime($order['expire_time']));
    if ($order['payment_time']) {
        $order['payment_time'] = date('Y-m-d H:i:s', strtotime($order['payment_time']));
    }
    
    // 添加状态文本
    $order['status_text'] = get_order_status_text($order['order_status']);
    $order['payment_status_text'] = get_payment_status_text($order['payment_status']);
    
    // 检查是否过期
    $order['is_expired'] = strtotime($order['expire_time']) < time() && $order['order_status'] === 'pending';
    
    // 格式化商品数据
    foreach ($items as &$item) {
        $item['course_price'] = floatval($item['course_price']);
        $item['original_price'] = floatval($item['original_price']);
        $item['subtotal'] = floatval($item['subtotal']);
        $item['created_at'] = date('Y-m-d H:i:s', strtotime($item['created_at']));
    }
    
    // 格式化支付记录
    foreach ($payments as &$payment) {
        $payment['amount'] = floatval($payment['amount']);
        $payment['created_at'] = date('Y-m-d H:i:s', strtotime($payment['created_at']));
        $payment['updated_at'] = date('Y-m-d H:i:s', strtotime($payment['updated_at']));
        if ($payment['paid_at']) {
            $payment['paid_at'] = date('Y-m-d H:i:s', strtotime($payment['paid_at']));
        }
    }
    
    // 格式化退款记录
    foreach ($refunds as &$refund) {
        $refund['refund_amount'] = floatval($refund['refund_amount']);
        $refund['created_at'] = date('Y-m-d H:i:s', strtotime($refund['created_at']));
        $refund['updated_at'] = date('Y-m-d H:i:s', strtotime($refund['updated_at']));
        if ($refund['processed_at']) {
            $refund['processed_at'] = date('Y-m-d H:i:s', strtotime($refund['processed_at']));
        }
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '获取订单详情成功',
        'data' => [
            'order' => $order,
            'items' => $items,
            'payments' => $payments,
            'refunds' => $refunds
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage(),
        'data' => null
    ]);
}

/**
 * 获取订单状态文本
 */
function get_order_status_text($status) {
    $status_map = [
        'pending' => '待支付',
        'paid' => '已支付',
        'cancelled' => '已取消',
        'refunded' => '已退款',
        'expired' => '已过期'
    ];
    return $status_map[$status] ?? '未知状态';
}

/**
 * 获取支付状态文本
 */
function get_payment_status_text($status) {
    $status_map = [
        'unpaid' => '未支付',
        'paid' => '已支付',
        'refunding' => '退款中',
        'refunded' => '已退款',
        'failed' => '支付失败'
    ];
    return $status_map[$status] ?? '未知状态';
}
?>
