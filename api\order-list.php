<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db.php';
require_once '../includes/auth.php';

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 验证用户身份
$user = authenticate_user();
if (!$user) {
    http_response_code(401);
    echo json_encode([
        'code' => 401,
        'message' => '未授权访问',
        'data' => null
    ]);
    exit;
}

try {
    $user_id = $user['id'];
    
    // 获取查询参数
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(50, max(1, intval($_GET['limit'] ?? 10)));
    $status = $_GET['status'] ?? '';
    $offset = ($page - 1) * $limit;
    
    // 构建查询条件
    $where_conditions = ["o.user_id = ?"];
    $params = [$user_id];
    $param_types = "i";
    
    if (!empty($status) && in_array($status, ['pending', 'paid', 'cancelled', 'refunded', 'expired'])) {
        $where_conditions[] = "o.order_status = ?";
        $params[] = $status;
        $param_types .= "s";
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // 查询订单总数
    $count_sql = "SELECT COUNT(*) as total FROM orders o WHERE $where_clause";
    $stmt = $conn->prepare($count_sql);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $total = $stmt->get_result()->fetch_assoc()['total'];
    
    // 查询订单列表
    $sql = "
        SELECT 
            o.id,
            o.order_no,
            o.total_amount,
            o.discount_amount,
            o.actual_amount,
            o.order_status,
            o.payment_status,
            o.payment_method,
            o.payment_time,
            o.expire_time,
            o.created_at,
            COUNT(oi.id) as item_count
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE $where_clause
        GROUP BY o.id
        ORDER BY o.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    $param_types .= "ii";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $orders = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // 获取订单商品详情
    if (!empty($orders)) {
        $order_ids = array_column($orders, 'id');
        $order_ids_str = implode(',', $order_ids);
        
        $items_sql = "
            SELECT 
                oi.*,
                c.thumbnail,
                c.cover_image
            FROM order_items oi
            LEFT JOIN courses c ON oi.course_id = c.id
            WHERE oi.order_id IN ($order_ids_str)
            ORDER BY oi.order_id, oi.id
        ";
        
        $stmt = $conn->prepare($items_sql);
        $stmt->execute();
        $items = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
        
        // 按订单ID分组商品
        $items_by_order = [];
        foreach ($items as $item) {
            $items_by_order[$item['order_id']][] = $item;
        }
        
        // 将商品信息添加到订单中
        foreach ($orders as &$order) {
            $order['items'] = $items_by_order[$order['id']] ?? [];
            
            // 格式化金额
            $order['total_amount'] = floatval($order['total_amount']);
            $order['discount_amount'] = floatval($order['discount_amount']);
            $order['actual_amount'] = floatval($order['actual_amount']);
            
            // 格式化时间
            $order['created_at'] = date('Y-m-d H:i:s', strtotime($order['created_at']));
            $order['expire_time'] = date('Y-m-d H:i:s', strtotime($order['expire_time']));
            if ($order['payment_time']) {
                $order['payment_time'] = date('Y-m-d H:i:s', strtotime($order['payment_time']));
            }
            
            // 添加状态文本
            $order['status_text'] = get_order_status_text($order['order_status']);
            $order['payment_status_text'] = get_payment_status_text($order['payment_status']);
            
            // 检查是否过期
            $order['is_expired'] = strtotime($order['expire_time']) < time() && $order['order_status'] === 'pending';
        }
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '获取订单列表成功',
        'data' => [
            'orders' => $orders,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => intval($total),
                'pages' => ceil($total / $limit)
            ]
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage(),
        'data' => null
    ]);
}

/**
 * 获取订单状态文本
 */
function get_order_status_text($status) {
    $status_map = [
        'pending' => '待支付',
        'paid' => '已支付',
        'cancelled' => '已取消',
        'refunded' => '已退款',
        'expired' => '已过期'
    ];
    return $status_map[$status] ?? '未知状态';
}

/**
 * 获取支付状态文本
 */
function get_payment_status_text($status) {
    $status_map = [
        'unpaid' => '未支付',
        'paid' => '已支付',
        'refunding' => '退款中',
        'refunded' => '已退款',
        'failed' => '支付失败'
    ];
    return $status_map[$status] ?? '未知状态';
}
?>
