<?php
/**
 * 增强版课程详情API
 * 支持JWT认证，获取课程详情和更新观看进度
 * 新增：课程统计、标签处理、课时信息等
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

/**
 * 验证用户课程访问权限
 */
function validateUserAccess($auth, $user_id, $course_id) {
    $conn = $auth->getConn();

    // 检查用户是否有课程权限
    $stmt = $conn->prepare("
        SELECT uc.*, c.is_free, c.price
        FROM user_courses uc
        RIGHT JOIN courses c ON c.id = ?
        LEFT JOIN user_courses uc2 ON uc2.course_id = c.id AND uc2.user_id = ?
        WHERE c.id = ?
    ");
    $stmt->bind_param("iii", $course_id, $user_id, $course_id);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();

    if (!$result) {
        return ['has_access' => false, 'reason' => 'course_not_found'];
    }

    // 免费课程直接允许访问
    if ($result['is_free'] == 1) {
        return ['has_access' => true, 'reason' => 'free_course'];
    }

    // 付费课程需要检查用户权限
    if ($result['user_id'] && $result['status'] === 'active') {
        $is_expired = false;
        if ($result['expire_time'] && strtotime($result['expire_time']) < time()) {
            $is_expired = true;
        }
        return [
            'has_access' => !$is_expired,
            'reason' => $is_expired ? 'expired' : 'purchased',
            'user_info' => $result
        ];
    }

    return ['has_access' => false, 'reason' => 'not_purchased'];
}

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // 获取课程详情
    $course_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    
    if ($course_id <= 0) {
        echo json_encode(['code' => 400, 'message' => '课程ID无效']);
        exit;
    }

    try {
        $conn = $auth->getConn();

        // 先检查用户是否有课程权限
        $user = null;
        $user_has_access = false;
        try {
            $user = $auth->getCurrentUser();
            if ($user) {
                $access_result = validateUserAccess($auth, $user['id'], $course_id);
                $user_has_access = $access_result['has_access'];
            }
        } catch (Exception $e) {
            // 用户未登录，继续执行
        }

        // 构建课程查询条件：如果用户有权限，则忽略课程状态；否则只显示启用的课程
        $status_condition = $user_has_access ? "c.id = ?" : "c.id = ? AND c.status IN ('published', 'active')";

        // 获取课程详情（增强版）
        $stmt = $conn->prepare("
            SELECT c.*,
                   CASE
                       WHEN c.duration IS NOT NULL AND c.duration > 0
                       THEN CONCAT(FLOOR(c.duration / 3600), '小时', FLOOR((c.duration % 3600) / 60), '分钟')
                       ELSE '未知时长'
                   END as duration_formatted,
                   CASE
                       WHEN c.difficulty = 'beginner' THEN '初级'
                       WHEN c.difficulty = 'intermediate' THEN '中级'
                       WHEN c.difficulty = 'advanced' THEN '高级'
                       ELSE '初级'
                   END as difficulty_text,
                   CASE
                       WHEN c.is_free = 1 THEN '免费课程'
                       WHEN c.price > 0 THEN '付费课程'
                       ELSE '免费课程'
                   END as course_type_text,
                   a.name as teacher_name_full
            FROM courses c
            LEFT JOIN admins a ON c.created_by = a.id
            WHERE $status_condition
        ");
        $stmt->bind_param("i", $course_id);
        $stmt->execute();
        $course = $stmt->get_result()->fetch_assoc();

        if (!$course) {
            echo json_encode(['code' => 404, 'message' => '课程不存在或已下架']);
            exit;
        }

        // 处理课程标签
        if (!empty($course['tags'])) {
            $course['tags_array'] = array_map('trim', explode(',', $course['tags']));
        } else {
            $course['tags_array'] = [];
        }

        // 获取课程统计信息
        $stats_stmt = $conn->prepare("
            SELECT 
                COUNT(DISTINCT uc.user_id) as student_count,
                AVG(CASE WHEN uc.watch_progress > 0 THEN uc.watch_progress ELSE NULL END) as avg_progress,
                COUNT(DISTINCT CASE WHEN uc.watch_progress >= 100 THEN uc.user_id END) as completed_count
            FROM user_courses uc 
            WHERE uc.course_id = ? AND uc.status = 'active'
        ");
        $stats_stmt->bind_param("i", $course_id);
        $stats_stmt->execute();
        $stats = $stats_stmt->get_result()->fetch_assoc();
        
        $course['student_count'] = (int)$stats['student_count'];
        $course['avg_progress'] = round((float)$stats['avg_progress'], 1);
        $course['completed_count'] = (int)$stats['completed_count'];

        // 获取课程课时信息
        $lessons_stmt = $conn->prepare("
            SELECT 
                COUNT(*) as total_lessons,
                SUM(CASE WHEN is_free = 1 THEN 1 ELSE 0 END) as free_lessons,
                SUM(duration) as total_duration
            FROM lessons 
            WHERE course_id = ? AND status IN ('published', 'active')
            ORDER BY sort_order ASC
        ");
        $lessons_stmt->bind_param("i", $course_id);
        $lessons_stmt->execute();
        $lesson_stats = $lessons_stmt->get_result()->fetch_assoc();
        
        $course['total_lessons'] = (int)$lesson_stats['total_lessons'];
        $course['free_lessons'] = (int)$lesson_stats['free_lessons'];
        $course['total_duration'] = (int)$lesson_stats['total_duration'];
        $course['total_duration_formatted'] = $lesson_stats['total_duration'] ? 
            gmdate("H:i:s", $lesson_stats['total_duration']) : '0:00:00';

        // 获取课程大纲（前5个课时作为预览）
        $outline_stmt = $conn->prepare("
            SELECT id, title, duration, is_free, sort_order
            FROM lessons 
            WHERE course_id = ? AND status IN ('published', 'active')
            ORDER BY sort_order ASC
            LIMIT 5
        ");
        $outline_stmt->bind_param("i", $course_id);
        $outline_stmt->execute();
        $outline_result = $outline_stmt->get_result();
        $course['outline_preview'] = [];
        while ($lesson = $outline_result->fetch_assoc()) {
            $course['outline_preview'][] = [
                'id' => (int)$lesson['id'],
                'title' => $lesson['title'],
                'duration' => (int)$lesson['duration'],
                'duration_formatted' => $lesson['duration'] ? gmdate("i:s", $lesson['duration']) : '0:00',
                'is_free' => (bool)$lesson['is_free'],
                'sort_order' => (int)$lesson['sort_order']
            ];
        }

        // 检查用户登录状态和访问权限
        $user = $auth->getCurrentUser();
        $user_info = null;
        $has_access = false;

        if ($user) {
            $access_check = validateUserAccess($auth, $user['id'], $course_id);
            $has_access = $access_check['has_access'];
            
            if (isset($access_check['user_info'])) {
                $user_info = [
                    'status' => $access_check['user_info']['status'],
                    'purchase_type' => $access_check['user_info']['purchase_type'] ?? 'unknown',
                    'watch_progress' => (float)($access_check['user_info']['watch_progress'] ?? 0),
                    'watch_count' => (int)($access_check['user_info']['watch_count'] ?? 0),
                    'last_watched_at' => $access_check['user_info']['last_watched_at'],
                    'expire_time' => $access_check['user_info']['expire_time'],
                    'is_expired' => $access_check['user_info']['expire_time'] && 
                                   strtotime($access_check['user_info']['expire_time']) < time()
                ];
            }
        }

        // 格式化价格信息
        $course['price'] = (float)$course['price'];
        $course['original_price'] = (float)$course['original_price'];
        $course['is_free'] = (bool)$course['is_free'];
        $course['is_on_sale'] = $course['status'] === 'published' || $course['status'] === 'active';
        
        // 计算折扣信息
        if ($course['original_price'] > $course['price'] && $course['price'] > 0) {
            $course['discount_percent'] = round((1 - $course['price'] / $course['original_price']) * 100);
            $course['has_discount'] = true;
        } else {
            $course['discount_percent'] = 0;
            $course['has_discount'] = false;
        }

        // 增加课程特色信息
        $course['features'] = [];
        if ($course['total_lessons'] > 0) {
            $course['features'][] = "共{$course['total_lessons']}个课时";
        }
        if ($course['free_lessons'] > 0) {
            $course['features'][] = "{$course['free_lessons']}个免费试看";
        }
        if ($course['student_count'] > 0) {
            $course['features'][] = "{$course['student_count']}人已学习";
        }
        if ($course['difficulty_text']) {
            $course['features'][] = $course['difficulty_text'] . "难度";
        }

        $response = [
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'course' => $course,
                'user_info' => $user_info,
                'has_access' => $has_access,
                'is_logged_in' => $user !== null
            ]
        ];

        echo json_encode($response, JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        echo json_encode([
            'code' => 500,
            'message' => '服务器错误: ' . $e->getMessage()
        ]);
    }

} elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 更新观看进度（保持原有逻辑）
    $user = $auth->requireAuth();
    
    $input = json_decode(file_get_contents('php://input'), true);
    $course_id = isset($input['course_id']) ? (int)$input['course_id'] : 0;
    $watch_time = isset($input['watch_time']) ? (int)$input['watch_time'] : 0;
    $progress_position = isset($input['progress_position']) ? (int)$input['progress_position'] : 0;

    if ($course_id <= 0) {
        echo json_encode(['code' => 400, 'message' => '课程ID无效']);
        exit;
    }

    try {
        $conn = $auth->getConn();
        $user_id = $user['id'];

        // 验证用户访问权限
        $access_check = validateUserAccess($auth, $user_id, $course_id);
        if (!$access_check['has_access']) {
            echo json_encode(['code' => 403, 'message' => '无权限访问此课程']);
            exit;
        }

        // 更新观看进度
        $stmt = $conn->prepare("
            INSERT INTO course_watch_logs (user_id, course_id, watch_time, progress_position, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $stmt->bind_param("iiiiss", $user_id, $course_id, $watch_time, $progress_position, $ip_address, $user_agent);
        $stmt->execute();

        echo json_encode([
            'code' => 200,
            'message' => '进度更新成功',
            'data' => [
                'watch_time' => $watch_time,
                'progress_position' => $progress_position
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'code' => 500,
            'message' => '服务器错误: ' . $e->getMessage()
        ]);
    }

} else {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
}
?>