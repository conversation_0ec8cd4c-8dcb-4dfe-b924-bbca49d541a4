<template>
  <view class="phone-bind-container">
    <!-- 头部说明 -->
    <view class="header">
      <view class="icon">
        <uni-icons type="phone" size="60" color="#007bff"></uni-icons>
      </view>
      <view class="title">绑定手机号</view>
      <view class="subtitle">{{ getSubtitle() }}</view>
    </view>

    <!-- 绑定表单 -->
    <view class="form-container">
      <!-- 手机号输入 -->
      <view class="form-item">
        <view class="label">手机号</view>
        <input 
          class="input" 
          type="number" 
          v-model="phone" 
          placeholder="请输入手机号"
          maxlength="11"
          @input="onPhoneInput"
        />
      </view>

      <!-- 验证码输入（仅在短信验证模式下显示） -->
      <view class="form-item" v-if="bindMode === 'sms'">
        <view class="label">验证码</view>
        <view class="code-input-container">
          <input 
            class="input code-input" 
            type="number" 
            v-model="code" 
            placeholder="请输入验证码"
            maxlength="6"
          />
          <button 
            class="code-btn" 
            :disabled="!canSendCode || codeCountdown > 0"
            @click="sendCode"
          >
            {{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
          </button>
        </view>
      </view>

      <!-- 绑定按钮 -->
      <button 
        class="bind-btn" 
        :disabled="!canBind"
        @click="bindPhone"
        :loading="binding"
      >
        {{ binding ? '绑定中...' : '确认绑定' }}
      </button>

      <!-- 跳过按钮（仅在非强制绑定时显示） -->
      <button 
        class="skip-btn" 
        v-if="canSkip"
        @click="skipBind"
      >
        暂时跳过
      </button>
    </view>

    <!-- 底部说明 -->
    <view class="footer">
      <text class="notice">绑定手机号后，您可以使用手机号登录系统</text>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      phone: '',
      code: '',
      bindMode: 'direct', // direct: 直接绑定, sms: 短信验证
      bindReason: '',
      binding: false,
      codeCountdown: 0,
      countdownTimer: null
    }
  },
  
  computed: {
    canSendCode() {
      return this.phone.length === 11 && /^1[3-9]\d{9}$/.test(this.phone)
    },
    
    canBind() {
      if (this.bindMode === 'direct') {
        return this.canSendCode
      } else {
        return this.canSendCode && this.code.length >= 4
      }
    },
    
    canSkip() {
      return this.bindReason !== 'new_user_required'
    }
  },
  
  onLoad(options) {
    // 获取传递的参数
    this.bindMode = options.mode || 'direct'
    this.bindReason = options.reason || ''
    
    console.log('手机号绑定页面参数:', { mode: this.bindMode, reason: this.bindReason })
  },
  
  onUnload() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
  },
  
  methods: {
    getSubtitle() {
      switch (this.bindReason) {
        case 'new_user_required':
          return '新用户需要绑定手机号才能使用系统'
        case 'user_required':
          return '您的账户需要绑定手机号'
        default:
          return '绑定手机号，享受更好的服务体验'
      }
    },
    
    onPhoneInput(e) {
      // 限制只能输入数字
      this.phone = e.detail.value.replace(/\D/g, '')
    },
    
    async sendCode() {
      if (!this.canSendCode) {
        return
      }
      
      try {
        uni.showLoading({ title: '发送中...' })
        
        const response = await request.request({
          url: '/sms-send.php',
          method: 'POST',
          data: {
            phone: this.phone,
            type: 'bind'
          }
        })
        
        uni.hideLoading()
        
        if (response.code === 200) {
          uni.showToast({
            title: '验证码已发送',
            icon: 'success'
          })
          
          // 开始倒计时
          this.startCountdown()
        } else {
          uni.showToast({
            title: response.message || '发送失败',
            icon: 'none'
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('发送验证码失败:', error)
        uni.showToast({
          title: '发送失败，请重试',
          icon: 'none'
        })
      }
    },
    
    startCountdown() {
      this.codeCountdown = 60
      this.countdownTimer = setInterval(() => {
        this.codeCountdown--
        if (this.codeCountdown <= 0) {
          clearInterval(this.countdownTimer)
          this.countdownTimer = null
        }
      }, 1000)
    },
    
    async bindPhone() {
      if (!this.canBind || this.binding) {
        return
      }
      
      this.binding = true
      
      try {
        const data = {
          action: 'bind',
          phone: this.phone
        }
        
        if (this.bindMode === 'sms') {
          data.code = this.code
        }
        
        const response = await request.request({
          url: '/phone-bind.php',
          method: 'POST',
          data: data
        })
        
        if (response.code === 200) {
          uni.showToast({
            title: '绑定成功',
            icon: 'success'
          })
          
          // 延迟跳转
          setTimeout(() => {
            this.goToHome()
          }, 1500)
        } else {
          uni.showToast({
            title: response.message || '绑定失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('绑定手机号失败:', error)
        uni.showToast({
          title: '绑定失败，请重试',
          icon: 'none'
        })
      } finally {
        this.binding = false
      }
    },
    
    skipBind() {
      uni.showModal({
        title: '确认跳过',
        content: '跳过绑定可能会影响部分功能的使用，确定要跳过吗？',
        success: (res) => {
          if (res.confirm) {
            this.goToHome()
          }
        }
      })
    },
    
    goToHome() {
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }
  }
}
</script>

<style scoped>
.phone-bind-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
  padding-top: 100rpx;
}

.icon {
  margin-bottom: 30rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.form-container {
  background: #fff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  color: #333;
  background: #fafafa;
}

.input:focus {
  border-color: #007bff;
  background: #fff;
}

.code-input-container {
  display: flex;
  gap: 20rpx;
}

.code-input {
  flex: 1;
}

.code-btn {
  width: 200rpx;
  height: 88rpx;
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-btn:disabled {
  background: #ccc;
  color: #999;
}

.bind-btn {
  width: 100%;
  height: 88rpx;
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 40rpx;
}

.bind-btn:disabled {
  background: #ccc;
  color: #999;
}

.skip-btn {
  width: 100%;
  height: 88rpx;
  background: transparent;
  color: #666;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 32rpx;
  margin-top: 20rpx;
}

.footer {
  text-align: center;
  padding: 0 20rpx;
}

.notice {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}
</style>
