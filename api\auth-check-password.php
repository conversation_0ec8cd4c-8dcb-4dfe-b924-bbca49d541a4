<?php
/**
 * 检查用户是否已设置密码API
 * 支持JWT认证，返回用户密码设置状态
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    // 验证用户认证
    $user = $auth->requireAuth();
    
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        $auth->jsonResponse(405, '不支持的请求方法');
    }
    
    checkPasswordStatus($auth, $user);
    
} catch (Exception $e) {
    error_log('检查密码状态API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 检查密码设置状态
 */
function checkPasswordStatus($auth, $user) {
    $conn = $auth->getConn();
    $user_id = $user['id'];
    
    // 获取用户密码状态
    $stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $auth->jsonResponse(404, '用户不存在');
    }
    
    $user_data = $result->fetch_assoc();
    $has_password = !empty($user_data['password']);
    
    $auth->jsonResponse(200, '获取密码状态成功', [
        'user_id' => $user_id,
        'has_password' => $has_password,
        'message' => $has_password ? '用户已设置密码' : '用户未设置密码'
    ]);
}
?>
