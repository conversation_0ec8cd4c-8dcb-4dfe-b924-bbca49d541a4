<?php
/**
 * 轮播图列表API - 简化版本用于测试
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '方法不允许',
        'data' => null,
        'timestamp' => time()
    ]);
    exit;
}

try {
    require_once '../includes/db.php';
    
    // 检查数据库连接
    if (!$conn) {
        throw new Exception('数据库连接失败');
    }
    
    // 检查banners表是否存在
    $check_table = $conn->query("SHOW TABLES LIKE 'banners'");
    if ($check_table->num_rows == 0) {
        // 表不存在，返回空数据
        echo json_encode([
            'code' => 200,
            'message' => '获取成功（表不存在）',
            'data' => [
                'list' => [],
                'total' => 0,
                'limit' => 10,
                'status' => 'active'
            ],
            'timestamp' => time()
        ]);
        exit;
    }
    
    // 参数验证和清理
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
    $status = isset($_GET['status']) ? $_GET['status'] : 'active';
    
    // 参数范围验证
    if ($limit < 1 || $limit > 50) $limit = 10;
    if (!in_array($status, ['active', 'inactive'])) $status = 'active';
    
    // 构建WHERE条件
    $where_conditions = ["b.status = ?"];
    $params = [$status];
    $param_types = "s";

    // 只显示有效期内的轮播图
    $where_conditions[] = "(b.start_time IS NULL OR b.start_time <= NOW())";
    $where_conditions[] = "(b.end_time IS NULL OR b.end_time > NOW())";

    $where_clause = "WHERE " . implode(' AND ', $where_conditions);
    
    // 获取总数
    $count_sql = "SELECT COUNT(*) as total FROM banners b $where_clause";
    $count_stmt = $conn->prepare($count_sql);
    
    if (!$count_stmt) {
        throw new Exception('COUNT SQL预处理失败: ' . $conn->error);
    }

    $count_stmt->bind_param($param_types, ...$params);
    
    if (!$count_stmt->execute()) {
        throw new Exception('COUNT SQL执行失败: ' . $count_stmt->error);
    }

    $total = $count_stmt->get_result()->fetch_assoc()['total'];
    $count_stmt->close();
    
    // 获取数据
    $sql = "SELECT
                b.id, b.title, b.image_url, b.link_type, b.link_value,
                b.start_time, b.end_time, b.status, b.sort_order,
                b.view_count, b.created_at, b.updated_at,
                ad.username as creator_name
            FROM banners b
            LEFT JOIN admins ad ON b.created_by = ad.id
            $where_clause
            ORDER BY b.sort_order ASC, b.created_at DESC
            LIMIT ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('SELECT SQL预处理失败: ' . $conn->error);
    }

    $params[] = $limit;
    $param_types .= "i";

    $stmt->bind_param($param_types, ...$params);

    if (!$stmt->execute()) {
        throw new Exception('SELECT SQL执行失败: ' . $stmt->error);
    }

    $result = $stmt->get_result();
    $banners = [];

    while ($row = $result->fetch_assoc()) {
        $banners[] = [
            'id' => (int)$row['id'],
            'title' => htmlspecialchars($row['title'], ENT_QUOTES, 'UTF-8'),
            'image_url' => $row['image_url'],
            'link_type' => $row['link_type'],
            'link_value' => $row['link_value'],
            'start_time' => $row['start_time'],
            'end_time' => $row['end_time'],
            'status' => $row['status'],
            'sort_order' => (int)$row['sort_order'],
            'view_count' => (int)$row['view_count'],
            'creator_name' => $row['creator_name'] ? htmlspecialchars($row['creator_name'], ENT_QUOTES, 'UTF-8') : null,
            'created_at' => $row['created_at'],
            'updated_at' => $row['updated_at']
        ];
    }

    $stmt->close();

    echo json_encode([
        'code' => 200,
        'message' => '获取成功',
        'data' => [
            'list' => $banners,
            'total' => (int)$total,
            'limit' => $limit,
            'status' => $status
        ],
        'timestamp' => time()
    ]);

} catch (Exception $e) {
    error_log('轮播图列表API错误: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'code' => 500,
        'message' => '服务器内部错误: ' . $e->getMessage(),
        'data' => null,
        'timestamp' => time()
    ]);
}
?>
