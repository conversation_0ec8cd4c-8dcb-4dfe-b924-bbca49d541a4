<?php
/**
 * 账号绑定API
 * 支持微信账号与传统账号的绑定和解绑
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

$auth = new AuthAPI();

// 验证用户登录状态
$user = $auth->getCurrentUser();
if (!$user) {
    $auth->jsonResponse(401, '请先登录');
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    $auth->jsonResponse(400, '无效的请求数据');
}

$action = $input['action'] ?? '';
$bind_type = $input['bind_type'] ?? '';

try {
    switch ($action) {
        case 'bind_password':
            // 绑定密码登录
            $username = trim($input['username'] ?? '');
            $password = $input['password'] ?? '';
            
            if (empty($username) || empty($password)) {
                $auth->jsonResponse(400, '请填写用户名和密码');
            }
            
            if (strlen($password) < 6) {
                $auth->jsonResponse(400, '密码长度不能少于6位');
            }
            
            // 检查用户名是否已被使用
            $check_stmt = $auth->getConn()->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
            $check_stmt->bind_param("si", $username, $user['id']);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $auth->jsonResponse(400, '该用户名已被使用');
            }
            
            // 更新用户信息
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $new_login_type = $user['login_type'] === 'wechat' ? 'both' : 'both';
            
            $stmt = $auth->getConn()->prepare("UPDATE users SET username = ?, password = ?, login_type = ?, updated_at = NOW() WHERE id = ?");
            $stmt->bind_param("sssi", $username, $hashed_password, $new_login_type, $user['id']);
            
            if ($stmt->execute()) {
                $auth->jsonResponse(200, '密码登录绑定成功', [
                    'login_type' => $new_login_type
                ]);
            } else {
                $auth->jsonResponse(500, '绑定失败');
            }
            break;
            
        case 'bind_wechat':
            // 绑定微信登录
            $code = $input['code'] ?? '';
            $user_info = $input['userInfo'] ?? null;
            
            if (empty($code)) {
                $auth->jsonResponse(400, '缺少微信授权码');
            }
            
            // 获取微信配置
            $settings_result = $auth->getConn()->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('wechat_app_id', 'wechat_app_secret')");
            $wechat_config = [];
            while ($row = $settings_result->fetch_assoc()) {
                $wechat_config[$row['setting_key']] = $row['setting_value'];
            }
            
            $app_id = $wechat_config['wechat_app_id'] ?? '';
            $app_secret = $wechat_config['wechat_app_secret'] ?? '';
            
            if (empty($app_id) || empty($app_secret)) {
                $auth->jsonResponse(500, '微信配置未完成');
            }
            
            // 调用微信API获取openid
            $url = "https://api.weixin.qq.com/sns/jscode2session?appid={$app_id}&secret={$app_secret}&js_code={$code}&grant_type=authorization_code";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code !== 200 || !$response) {
                $auth->jsonResponse(500, '微信服务器连接失败');
            }
            
            $wechat_data = json_decode($response, true);
            
            if (isset($wechat_data['errcode'])) {
                $auth->jsonResponse(400, '微信授权失败');
            }
            
            $openid = $wechat_data['openid'] ?? '';
            $session_key = $wechat_data['session_key'] ?? '';
            $unionid = $wechat_data['unionid'] ?? null;
            
            if (empty($openid)) {
                $auth->jsonResponse(500, '获取微信用户标识失败');
            }
            
            // 检查该微信是否已绑定其他账号
            $check_stmt = $auth->getConn()->prepare("SELECT user_id FROM wechat_users WHERE openid = ? AND user_id != ?");
            $check_stmt->bind_param("si", $openid, $user['id']);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $auth->jsonResponse(400, '该微信账号已绑定其他用户');
            }
            
            // 开始事务
            $auth->getConn()->begin_transaction();
            
            try {
                // 删除旧的微信绑定（如果存在）
                $stmt = $auth->getConn()->prepare("DELETE FROM wechat_users WHERE user_id = ?");
                $stmt->bind_param("i", $user['id']);
                $stmt->execute();
                
                // 创建新的微信绑定
                $stmt = $auth->getConn()->prepare("INSERT INTO wechat_users (user_id, openid, unionid, session_key) VALUES (?, ?, ?, ?)");
                $stmt->bind_param("isss", $user['id'], $openid, $unionid, $session_key);
                $stmt->execute();
                
                // 更新用户登录类型
                $new_login_type = $user['login_type'] === 'traditional' ? 'both' : 'both';
                $stmt = $auth->getConn()->prepare("UPDATE users SET login_type = ?, updated_at = NOW() WHERE id = ?");
                $stmt->bind_param("si", $new_login_type, $user['id']);
                $stmt->execute();
                
                // 如果提供了用户信息，更新用户资料
                if ($user_info && is_array($user_info)) {
                    $nickname = $user_info['nickName'] ?? $user['nickname'];
                    $avatar = $user_info['avatarUrl'] ?? $user['avatar'];
                    $gender = isset($user_info['gender']) ? intval($user_info['gender']) : $user['gender'];
                    
                    $stmt = $auth->getConn()->prepare("UPDATE users SET nickname = ?, avatar = ?, gender = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->bind_param("ssii", $nickname, $avatar, $gender, $user['id']);
                    $stmt->execute();
                }
                
                $auth->getConn()->commit();
                
                $auth->jsonResponse(200, '微信账号绑定成功', [
                    'login_type' => $new_login_type
                ]);
                
            } catch (Exception $e) {
                $auth->getConn()->rollback();
                throw $e;
            }
            break;
            
        case 'unbind_password':
            // 解绑密码登录
            if ($user['login_type'] === 'traditional') {
                $auth->jsonResponse(400, '无法解绑，这是您唯一的登录方式');
            }
            
            $stmt = $auth->getConn()->prepare("UPDATE users SET username = NULL, password = NULL, login_type = 'wechat', updated_at = NOW() WHERE id = ?");
            $stmt->bind_param("i", $user['id']);
            
            if ($stmt->execute()) {
                $auth->jsonResponse(200, '密码登录解绑成功');
            } else {
                $auth->jsonResponse(500, '解绑失败');
            }
            break;
            
        case 'unbind_wechat':
            // 解绑微信登录
            if ($user['login_type'] === 'wechat') {
                $auth->jsonResponse(400, '无法解绑，这是您唯一的登录方式');
            }
            
            // 开始事务
            $auth->getConn()->begin_transaction();
            
            try {
                // 删除微信绑定
                $stmt = $auth->getConn()->prepare("DELETE FROM wechat_users WHERE user_id = ?");
                $stmt->bind_param("i", $user['id']);
                $stmt->execute();
                
                // 更新登录类型
                $stmt = $auth->getConn()->prepare("UPDATE users SET login_type = 'traditional', updated_at = NOW() WHERE id = ?");
                $stmt->bind_param("i", $user['id']);
                $stmt->execute();
                
                $auth->getConn()->commit();
                
                $auth->jsonResponse(200, '微信账号解绑成功');
                
            } catch (Exception $e) {
                $auth->getConn()->rollback();
                throw $e;
            }
            break;
            
        case 'get_bind_status':
            // 获取绑定状态
            $wechat_bound = false;
            $password_bound = !empty($user['password']);
            
            // 检查微信绑定状态
            $stmt = $auth->getConn()->prepare("SELECT id FROM wechat_users WHERE user_id = ?");
            $stmt->bind_param("i", $user['id']);
            $stmt->execute();
            $wechat_result = $stmt->get_result();
            $wechat_bound = $wechat_result->num_rows > 0;
            
            $auth->jsonResponse(200, '获取绑定状态成功', [
                'login_type' => $user['login_type'],
                'wechat_bound' => $wechat_bound,
                'password_bound' => $password_bound,
                'can_unbind_wechat' => $wechat_bound && $password_bound,
                'can_unbind_password' => $password_bound && $wechat_bound
            ]);
            break;
            
        default:
            $auth->jsonResponse(400, '无效的操作类型');
    }
    
} catch (Exception $e) {
    error_log('账号绑定错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}
