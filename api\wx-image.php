<?php
/**
 * 小程序图片访问API
 * 专门为小程序提供图片访问服务，解决直接访问的兼容性问题
 */

// 设置CORS头部
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, HEAD, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    die('Method Not Allowed');
}

// 获取图片路径参数
$path = $_GET['path'] ?? '';

if (empty($path)) {
    http_response_code(400);
    die('Missing path parameter');
}

// 安全检查：防止路径遍历攻击
if (strpos($path, '..') !== false) {
    http_response_code(403);
    die('Invalid path');
}

// 支持的图片类型
$allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
$file_extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));

if (!in_array($file_extension, $allowed_extensions)) {
    http_response_code(403);
    die('Invalid file type');
}

// 构建完整的文件路径
$full_path = $_SERVER['DOCUMENT_ROOT'] . $path;

// 检查文件是否存在
if (!file_exists($full_path)) {
    http_response_code(404);
    die('Image not found');
}

// 检查是否为有效的图片文件
$image_info = @getimagesize($full_path);
if ($image_info === false) {
    http_response_code(400);
    die('Invalid image file');
}

// 获取文件信息
$file_size = filesize($full_path);
$file_time = filemtime($full_path);

// 设置适当的Content-Type
$mime_types = [
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'webp' => 'image/webp'
];

$content_type = $mime_types[$file_extension] ?? 'application/octet-stream';

// 设置缓存头
$etag = md5($full_path . $file_time . $file_size);
header('ETag: "' . $etag . '"');
header('Last-Modified: ' . gmdate('D, d M Y H:i:s', $file_time) . ' GMT');
header('Cache-Control: public, max-age=86400'); // 缓存24小时

// 检查客户端缓存
$client_etag = $_SERVER['HTTP_IF_NONE_MATCH'] ?? '';
$client_modified = $_SERVER['HTTP_IF_MODIFIED_SINCE'] ?? '';

if ($client_etag === '"' . $etag . '"' || 
    ($client_modified && strtotime($client_modified) >= $file_time)) {
    http_response_code(304);
    exit;
}

// 设置响应头
header('Content-Type: ' . $content_type);
header('Content-Length: ' . $file_size);
header('Accept-Ranges: bytes');

// 支持范围请求（用于大文件和流媒体）
if (isset($_SERVER['HTTP_RANGE'])) {
    $range = $_SERVER['HTTP_RANGE'];
    if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
        $start = intval($matches[1]);
        $end = $matches[2] ? intval($matches[2]) : $file_size - 1;
        
        if ($start < $file_size && $end < $file_size && $start <= $end) {
            http_response_code(206);
            header('Content-Range: bytes ' . $start . '-' . $end . '/' . $file_size);
            header('Content-Length: ' . ($end - $start + 1));
            
            $file = fopen($full_path, 'rb');
            if ($file) {
                fseek($file, $start);
                echo fread($file, $end - $start + 1);
                fclose($file);
            }
            exit;
        }
    }
}

// 输出完整文件
$file = fopen($full_path, 'rb');
if ($file) {
    // 分块读取，避免内存问题
    while (!feof($file)) {
        echo fread($file, 8192);
        if (connection_aborted()) {
            break;
        }
    }
    fclose($file);
} else {
    http_response_code(500);
    die('Failed to read file');
}
?>
