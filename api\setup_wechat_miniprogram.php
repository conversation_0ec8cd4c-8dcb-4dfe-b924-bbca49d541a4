<?php
/**
 * 微信小程序配置设置
 * 用于配置微信小程序AppID和AppSecret
 */

header('Content-Type: text/html; charset=utf-8');
require_once '../includes/db.php';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $app_id = trim($_POST['app_id'] ?? '');
        $app_secret = trim($_POST['app_secret'] ?? '');
        
        // 验证输入
        $errors = [];
        
        if (empty($app_id)) {
            $errors[] = 'AppID不能为空';
        } elseif (!preg_match('/^wx[a-f0-9]{16}$/', $app_id)) {
            $errors[] = 'AppID格式不正确（应以wx开头，后跟16位十六进制字符）';
        }
        
        if (empty($app_secret)) {
            $errors[] = 'AppSecret不能为空';
        } elseif (strlen($app_secret) < 32) {
            $errors[] = 'AppSecret长度不能少于32位';
        }
        
        if (empty($errors)) {
            // 更新配置
            $configs = [
                'wechat_app_id' => $app_id,
                'wechat_app_secret' => $app_secret
            ];
            
            foreach ($configs as $key => $value) {
                $stmt = $conn->prepare("
                    INSERT INTO settings (setting_key, setting_value, setting_type, description) 
                    VALUES (?, ?, 'string', ?)
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $description = '微信小程序配置: ' . $key;
                $stmt->bind_param("sss", $key, $value, $description);
                $stmt->execute();
            }
            
            $success_message = "微信小程序配置已保存成功！";
        }
    } catch (Exception $e) {
        $errors[] = "保存配置时出错: " . $e->getMessage();
    }
}

// 获取当前配置
$current_config = [];
try {
    $stmt = $conn->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('wechat_app_id', 'wechat_app_secret')");
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $current_config[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    $errors[] = "读取配置时出错: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序配置</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .error { background: #ffebee; color: #c62828; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .success { background: #e8f5e8; color: #2e7d32; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { background: #e3f2fd; color: #1565c0; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>微信小程序配置</h1>
    
    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="error">
            <h3>错误：</h3>
            <ul>
                <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
    
    <?php if (isset($success_message)): ?>
        <div class="success">
            <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>
    
    <div class="info">
        <h3>配置说明</h3>
        <p>要使用微信小程序登录功能，您需要：</p>
        <ol>
            <li>在微信公众平台注册小程序</li>
            <li>获取小程序的AppID和AppSecret</li>
            <li>在小程序后台配置服务器域名</li>
            <li>确保AppID与当前小程序一致</li>
        </ol>
    </div>
    
    <form method="POST">
        <div class="form-group">
            <label for="app_id">微信小程序AppID:</label>
            <input type="text" id="app_id" name="app_id" value="<?php echo htmlspecialchars($current_config['wechat_app_id'] ?? 'wxa936b5c9fa9b1893'); ?>" placeholder="wx1234567890abcdef">
            <small>格式：wx + 16位十六进制字符</small>
        </div>
        
        <div class="form-group">
            <label for="app_secret">微信小程序AppSecret:</label>
            <input type="password" id="app_secret" name="app_secret" value="<?php echo htmlspecialchars($current_config['wechat_app_secret'] ?? ''); ?>" placeholder="32位字符的AppSecret">
            <small>在微信公众平台小程序后台获取，长度至少32位</small>
        </div>
        
        <button type="submit">保存配置</button>
    </form>
    
    <div class="warning">
        <h3>⚠️ 重要提示</h3>
        <p><strong>当前微信登录失败的原因是缺少微信小程序配置。</strong></p>
        <p>您需要：</p>
        <ul>
            <li><strong>获取真实的AppID和AppSecret</strong>：在微信公众平台小程序后台获取</li>
            <li><strong>配置服务器域名</strong>：在小程序后台添加 request 合法域名</li>
            <li><strong>确保AppID匹配</strong>：配置的AppID必须与当前运行的小程序一致</li>
        </ul>
    </div>
    
    <h2>当前配置状态</h2>
    <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr>
            <th>配置项</th>
            <th>状态</th>
            <th>值</th>
        </tr>
        <?php
        $required_configs = [
            'wechat_app_id' => 'AppID',
            'wechat_app_secret' => 'AppSecret'
        ];
        
        foreach ($required_configs as $key => $name) {
            $value = $current_config[$key] ?? '';
            $status = empty($value) ? '❌ 未配置' : '✅ 已配置';
            
            if ($key === 'wechat_app_secret' && !empty($value)) {
                $value = str_repeat('*', strlen($value));
            }
            
            echo "<tr>";
            echo "<td>$name</td>";
            echo "<td>$status</td>";
            echo "<td>" . htmlspecialchars($value) . "</td>";
            echo "</tr>";
        }
        ?>
    </table>
    
    <h2>测试微信登录</h2>
    <div class="info">
        <p>配置完成后，您可以：</p>
        <ol>
            <li>在微信开发者工具中重新编译小程序</li>
            <li>尝试微信登录功能</li>
            <li>检查控制台是否还有错误信息</li>
            <li>确认微信公众平台的配置是否正确</li>
        </ol>
    </div>
    
    <h2>数据库配置更新SQL</h2>
    <div style="background: #f0f0f0; padding: 15px; margin: 10px 0;">
        <p>如果您有微信小程序的配置信息，可以使用以下SQL直接更新：</p>
        <pre>
-- 更新微信小程序AppID
UPDATE settings SET setting_value = '您的AppID' WHERE setting_key = 'wechat_app_id';

-- 更新微信小程序AppSecret  
UPDATE settings SET setting_value = '您的AppSecret' WHERE setting_key = 'wechat_app_secret';

-- 或者插入新配置
INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES
('wechat_app_id', '您的AppID', 'string', '微信小程序AppID'),
('wechat_app_secret', '您的AppSecret', 'string', '微信小程序AppSecret')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);
        </pre>
    </div>
    
    <h2>临时解决方案</h2>
    <div class="warning">
        <p>如果您暂时没有微信小程序的真实配置，可以：</p>
        <ol>
            <li><strong>使用账号密码登录</strong>：在登录页面展开账号密码登录</li>
            <li><strong>申请微信小程序</strong>：在微信公众平台申请小程序账号</li>
            <li><strong>测试其他功能</strong>：先测试课程浏览等不需要登录的功能</li>
        </ol>
    </div>
</body>
</html>
