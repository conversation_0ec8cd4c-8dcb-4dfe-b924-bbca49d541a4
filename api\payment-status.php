<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);

require_once '../includes/db.php';
require_once '../includes/auth.php';

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 验证用户身份
$user = authenticate_user();
if (!$user) {
    http_response_code(401);
    echo json_encode([
        'code' => 401,
        'message' => '未授权访问',
        'data' => null
    ]);
    exit;
}

// 获取参数
$order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
$payment_no = isset($_GET['payment_no']) ? trim($_GET['payment_no']) : '';

if (!$order_id && !$payment_no) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少必需参数',
        'data' => null
    ]);
    exit;
}

try {
    $user_id = $user['id'];
    
    // 构建查询条件
    if ($order_id) {
        // 根据订单ID查询
        $stmt = $conn->prepare("
            SELECT o.*, p.payment_status, p.payment_no, p.transaction_id, p.paid_at
            FROM orders o 
            LEFT JOIN payments p ON o.id = p.order_id 
            WHERE o.id = ? AND o.user_id = ?
            ORDER BY p.created_at DESC
            LIMIT 1
        ");
        $stmt->bind_param("ii", $order_id, $user_id);
    } else {
        // 根据支付流水号查询
        $stmt = $conn->prepare("
            SELECT o.*, p.payment_status, p.payment_no, p.transaction_id, p.paid_at
            FROM payments p 
            LEFT JOIN orders o ON p.order_id = o.id 
            WHERE p.payment_no = ? AND p.user_id = ?
            LIMIT 1
        ");
        $stmt->bind_param("si", $payment_no, $user_id);
    }
    
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    
    if (!$result) {
        echo json_encode([
            'code' => 404,
            'message' => '订单不存在',
            'data' => null
        ]);
        exit;
    }
    
    // 判断支付状态
    $payment_status = 'unpaid';
    $payment_message = '未支付';
    
    if ($result['payment_status'] === 'success' && $result['order_status'] === 'paid') {
        $payment_status = 'success';
        $payment_message = '支付成功';
    } elseif ($result['payment_status'] === 'pending') {
        $payment_status = 'pending';
        $payment_message = '支付处理中';
    } elseif ($result['order_status'] === 'expired') {
        $payment_status = 'expired';
        $payment_message = '订单已过期';
    } elseif ($result['order_status'] === 'cancelled') {
        $payment_status = 'cancelled';
        $payment_message = '订单已取消';
    }
    
    // 检查订单是否过期
    $is_expired = false;
    if ($result['expire_time'] && strtotime($result['expire_time']) < time()) {
        $is_expired = true;
        if ($result['order_status'] === 'pending') {
            // 更新过期状态
            $stmt = $conn->prepare("UPDATE orders SET order_status = 'expired' WHERE id = ?");
            $stmt->bind_param("i", $result['id']);
            $stmt->execute();
            
            $payment_status = 'expired';
            $payment_message = '订单已过期';
        }
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '查询成功',
        'data' => [
            'order_id' => $result['id'],
            'order_no' => $result['order_no'],
            'order_status' => $result['order_status'],
            'payment_status' => $payment_status,
            'payment_message' => $payment_message,
            'payment_no' => $result['payment_no'],
            'transaction_id' => $result['transaction_id'],
            'amount' => $result['actual_amount'],
            'paid_at' => $result['paid_at'],
            'is_expired' => $is_expired,
            'expire_time' => $result['expire_time'],
            'can_pay' => $payment_status === 'unpaid' && !$is_expired,
            'can_retry' => in_array($payment_status, ['unpaid', 'pending']) && !$is_expired
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '查询失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?>
