/**
 * 创建base64格式的图标
 * 这些图标可以直接在小程序中使用
 */

// 创建简单的SVG图标并转换为base64
function createIcon(type, color = '#7A7E83', size = 81) {
    const svgTemplates = {
        home: `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
            <polyline points="9,22 9,12 15,12 15,22"/>
        </svg>`,
        
        course: `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="23 7 16 12 23 17 23 7"/>
            <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
        </svg>`,
        
        profile: `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
            <circle cx="12" cy="7" r="4"/>
        </svg>`
    };
    
    return svgTemplates[type] || '';
}

// 生成所有图标的base64编码
const icons = {
    // 首页图标
    home: {
        normal: 'data:image/svg+xml;base64,' + btoa(createIcon('home', '#7A7E83')),
        active: 'data:image/svg+xml;base64,' + btoa(createIcon('home', '#007bff'))
    },
    
    // 课程图标
    course: {
        normal: 'data:image/svg+xml;base64,' + btoa(createIcon('course', '#7A7E83')),
        active: 'data:image/svg+xml;base64,' + btoa(createIcon('course', '#007bff'))
    },
    
    // 个人图标
    profile: {
        normal: 'data:image/svg+xml;base64,' + btoa(createIcon('profile', '#7A7E83')),
        active: 'data:image/svg+xml;base64,' + btoa(createIcon('profile', '#007bff'))
    }
};

// 输出配置信息
console.log('=== TabBar图标Base64配置 ===');
console.log('');
console.log('在pages.json中使用以下配置：');
console.log('');
console.log(JSON.stringify({
    "tabBar": {
        "color": "#7A7E83",
        "selectedColor": "#007bff",
        "borderStyle": "black",
        "backgroundColor": "#ffffff",
        "list": [
            {
                "pagePath": "pages/index/index",
                "text": "首页",
                "iconPath": icons.home.normal,
                "selectedIconPath": icons.home.active
            },
            {
                "pagePath": "pages/courses/list",
                "text": "课程",
                "iconPath": icons.course.normal,
                "selectedIconPath": icons.course.active
            },
            {
                "pagePath": "pages/profile/profile",
                "text": "我的",
                "iconPath": icons.profile.normal,
                "selectedIconPath": icons.profile.active
            }
        ]
    }
}, null, 2));

// 导出图标数据
if (typeof module !== 'undefined' && module.exports) {
    module.exports = icons;
}
