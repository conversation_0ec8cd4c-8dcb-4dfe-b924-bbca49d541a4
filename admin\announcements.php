<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

// 检查数据库表是否存在
$table_check = $conn->query("SHOW TABLES LIKE 'announcements'");
if (!$table_check || $table_check->num_rows == 0) {
    die('错误：公告表不存在。请先导入 install.sql 数据库文件。');
}

$success_message = '';
$error_message = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_announcement'])) {
        // 添加公告
        $title = trim($_POST['title']);
        $content = trim($_POST['content']);
        $type = $_POST['type'];
        $status = $_POST['status'];
        $priority = (int)$_POST['priority'];
        $is_pinned = isset($_POST['is_pinned']) ? 1 : 0;
        $category_id = !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null;
        $publish_time = !empty($_POST['publish_time']) ? $_POST['publish_time'] : null;
        $expire_time = !empty($_POST['expire_time']) ? $_POST['expire_time'] : null;

        if (empty($title) || empty($content)) {
            $error_message = '标题和内容不能为空';
        } else {
            $sql = "INSERT INTO announcements (title, content, type, status, priority, is_pinned, category_id, publish_time, expire_time, author_id) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            if ($stmt) {
                $author_id = 1; // 默认管理员ID
                $stmt->bind_param("ssssiiissi", $title, $content, $type, $status, $priority, $is_pinned, $category_id, $publish_time, $expire_time, $author_id);
                if ($stmt->execute()) {
                    $success_message = '公告添加成功';
                } else {
                    $error_message = '添加公告失败：' . $conn->error;
                }
            } else {
                $error_message = '准备语句失败：' . $conn->error;
            }
        }
    } elseif (isset($_POST['edit_announcement'])) {
        // 编辑公告
        $id = (int)$_POST['id'];
        $title = trim($_POST['title']);
        $content = trim($_POST['content']);
        $type = $_POST['type'];
        $status = $_POST['status'];
        $priority = (int)$_POST['priority'];
        $is_pinned = isset($_POST['is_pinned']) ? 1 : 0;
        $category_id = !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null;
        $publish_time = !empty($_POST['publish_time']) ? $_POST['publish_time'] : null;
        $expire_time = !empty($_POST['expire_time']) ? $_POST['expire_time'] : null;

        if (empty($title) || empty($content)) {
            $error_message = '标题和内容不能为空';
        } else {
            $sql = "UPDATE announcements SET title=?, content=?, type=?, status=?, priority=?, is_pinned=?, category_id=?, publish_time=?, expire_time=? WHERE id=?";
            $stmt = $conn->prepare($sql);
            if ($stmt) {
                $stmt->bind_param("ssssiiissi", $title, $content, $type, $status, $priority, $is_pinned, $category_id, $publish_time, $expire_time, $id);
                if ($stmt->execute()) {
                    $success_message = '公告更新成功';
                } else {
                    $error_message = '更新公告失败：' . $conn->error;
                }
            } else {
                $error_message = '准备语句失败：' . $conn->error;
            }
        }
    }
}

// 删除公告
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);
    $stmt = $conn->prepare("DELETE FROM announcements WHERE id = ?");
    if ($stmt) {
        $stmt->bind_param("i", $id);
        if ($stmt->execute()) {
            $success_message = '公告删除成功';
        } else {
            $error_message = '删除公告失败：' . $conn->error;
        }
    }
}

// 获取要编辑的公告
$edit_announcement = null;
if (isset($_GET['edit'])) {
    $id = intval($_GET['edit']);
    $stmt = $conn->prepare("SELECT * FROM announcements WHERE id = ?");
    if ($stmt) {
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $edit_announcement = $result->fetch_assoc();
    }
}

// 获取公告列表
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$per_page = 10;
$offset = ($page - 1) * $per_page;

$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$type_filter = $_GET['type'] ?? '';

$where_conditions = [];
$params = [];
$param_types = "";

if (!empty($search)) {
    $where_conditions[] = "(title LIKE ? OR content LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $param_types .= "ss";
}

if (!empty($status_filter)) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
    $param_types .= "s";
}

if (!empty($type_filter)) {
    $where_conditions[] = "type = ?";
    $params[] = $type_filter;
    $param_types .= "s";
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(' AND ', $where_conditions) : "";

// 获取总数
$count_sql = "SELECT COUNT(*) as total FROM announcements $where_clause";
if (!empty($where_conditions)) {
    $count_stmt = $conn->prepare($count_sql);
    if ($count_stmt) {
        $count_stmt->bind_param($param_types, ...$params);
        $count_stmt->execute();
        $total = $count_stmt->get_result()->fetch_assoc()['total'];
    } else {
        $total = 0;
    }
} else {
    $result = $conn->query($count_sql);
    $total = $result ? $result->fetch_assoc()['total'] : 0;
}

// 获取数据
$announcements = [];
if (!empty($where_conditions)) {
    $sql = "SELECT a.*, c.name as category_name, ad.username as author_name 
            FROM announcements a 
            LEFT JOIN announcement_categories c ON a.category_id = c.id
            LEFT JOIN admins ad ON a.author_id = ad.id
            $where_clause
            ORDER BY a.is_pinned DESC, a.priority DESC, a.created_at DESC 
            LIMIT ? OFFSET ?";
    $stmt = $conn->prepare($sql);
    if ($stmt) {
        $limit_params = $params;
        $limit_params[] = $per_page;
        $limit_params[] = $offset;
        $limit_param_types = $param_types . "ii";
        $stmt->bind_param($limit_param_types, ...$limit_params);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $announcements[] = $row;
        }
    }
} else {
    $sql = "SELECT a.*, c.name as category_name, ad.username as author_name 
            FROM announcements a 
            LEFT JOIN announcement_categories c ON a.category_id = c.id
            LEFT JOIN admins ad ON a.author_id = ad.id
            ORDER BY a.is_pinned DESC, a.priority DESC, a.created_at DESC 
            LIMIT ? OFFSET ?";
    $stmt = $conn->prepare($sql);
    if ($stmt) {
        $limit_value = $per_page;
        $offset_value = $offset;
        $stmt->bind_param("ii", $limit_value, $offset_value);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $announcements[] = $row;
        }
    }
}

// 获取分类列表
$categories = [];
$categories_result = $conn->query("SELECT * FROM announcement_categories WHERE is_active = 1 ORDER BY sort_order ASC");
if ($categories_result) {
    while ($row = $categories_result->fetch_assoc()) {
        $categories[] = $row;
    }
}

$total_pages = ceil($total / $per_page);

// 渲染页面头部
render_admin_header('公告管理', 'announcements');
?>

<!-- 显示消息 -->
<?php if ($success_message): ?>
    <?php show_success_message($success_message); ?>
<?php endif; ?>

<?php if ($error_message): ?>
    <?php show_error_message($error_message); ?>
<?php endif; ?>

<!-- 搜索和筛选 -->
<?php render_card_start('搜索和筛选'); ?>
    <?php render_form_start('', 'get', 'searchForm'); ?>
        <div class="admin-form-row">
            <div class="admin-form-col">
                <?php render_form_input('搜索', 'search', 'text', htmlspecialchars($search), false, '搜索标题或内容'); ?>
            </div>
            <div class="admin-form-col">
                <label class="admin-form-label">状态</label>
                <select name="status" class="admin-form-input">
                    <option value="">所有状态</option>
                    <option value="draft" <?= $status_filter === 'draft' ? 'selected' : '' ?>>草稿</option>
                    <option value="published" <?= $status_filter === 'published' ? 'selected' : '' ?>>已发布</option>
                    <option value="archived" <?= $status_filter === 'archived' ? 'selected' : '' ?>>已归档</option>
                </select>
            </div>
            <div class="admin-form-col">
                <label class="admin-form-label">类型</label>
                <select name="type" class="admin-form-input">
                    <option value="">所有类型</option>
                    <option value="notice" <?= $type_filter === 'notice' ? 'selected' : '' ?>>通知</option>
                    <option value="urgent" <?= $type_filter === 'urgent' ? 'selected' : '' ?>>紧急</option>
                    <option value="system" <?= $type_filter === 'system' ? 'selected' : '' ?>>系统</option>
                    <option value="activity" <?= $type_filter === 'activity' ? 'selected' : '' ?>>活动</option>
                </select>
            </div>
        </div>
        <div class="admin-actions">
            <?php render_button('搜索', 'submit', 'admin-btn-primary'); ?>
            <?php render_link_button('重置', 'announcements.php', 'admin-btn-secondary'); ?>
        </div>
    <?php render_form_end(); ?>
<?php render_card_end(); ?>

<!-- 添加/编辑公告表单 -->
<?php render_card_start($edit_announcement ? '编辑公告' : '添加新公告'); ?>
    <?php render_form_start('', 'post', 'announcementForm'); ?>
        <?php if ($edit_announcement): ?>
            <input type="hidden" name="id" value="<?= $edit_announcement['id'] ?>">
        <?php endif; ?>
        
        <div class="admin-form-row">
            <div class="admin-form-col">
                <?php render_form_input('标题', 'title', 'text', $edit_announcement['title'] ?? '', true, '请输入公告标题'); ?>
            </div>
            <div class="admin-form-col">
                <label class="admin-form-label">类型</label>
                <select name="type" class="admin-form-input">
                    <option value="notice" <?= ($edit_announcement['type'] ?? '') === 'notice' ? 'selected' : '' ?>>通知</option>
                    <option value="urgent" <?= ($edit_announcement['type'] ?? '') === 'urgent' ? 'selected' : '' ?>>紧急</option>
                    <option value="system" <?= ($edit_announcement['type'] ?? '') === 'system' ? 'selected' : '' ?>>系统</option>
                    <option value="activity" <?= ($edit_announcement['type'] ?? '') === 'activity' ? 'selected' : '' ?>>活动</option>
                </select>
            </div>
        </div>
        
        <div class="admin-form-group">
            <label class="admin-form-label">内容</label>
            <textarea name="content" class="admin-form-input" rows="8" required placeholder="请输入公告内容"><?= htmlspecialchars($edit_announcement['content'] ?? '') ?></textarea>
        </div>
        
        <div class="admin-form-row">
            <div class="admin-form-col">
                <label class="admin-form-label">状态</label>
                <select name="status" class="admin-form-input">
                    <option value="draft" <?= ($edit_announcement['status'] ?? '') === 'draft' ? 'selected' : '' ?>>草稿</option>
                    <option value="published" <?= ($edit_announcement['status'] ?? '') === 'published' ? 'selected' : '' ?>>已发布</option>
                    <option value="archived" <?= ($edit_announcement['status'] ?? '') === 'archived' ? 'selected' : '' ?>>已归档</option>
                </select>
            </div>
            <div class="admin-form-col">
                <label class="admin-form-label">优先级</label>
                <select name="priority" class="admin-form-input">
                    <option value="0" <?= ($edit_announcement['priority'] ?? 0) == 0 ? 'selected' : '' ?>>普通</option>
                    <option value="1" <?= ($edit_announcement['priority'] ?? 0) == 1 ? 'selected' : '' ?>>重要</option>
                    <option value="2" <?= ($edit_announcement['priority'] ?? 0) == 2 ? 'selected' : '' ?>>紧急</option>
                </select>
            </div>
            <div class="admin-form-col">
                <label class="admin-form-label">分类</label>
                <select name="category_id" class="admin-form-input">
                    <option value="">无分类</option>
                    <?php foreach ($categories as $category): ?>
                    <option value="<?= $category['id'] ?>" <?= ($edit_announcement['category_id'] ?? '') == $category['id'] ? 'selected' : '' ?>><?= htmlspecialchars($category['name']) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        
        <div class="admin-form-row">
            <div class="admin-form-col">
                <?php render_form_input('发布时间', 'publish_time', 'datetime-local', $edit_announcement['publish_time'] ?? '', false); ?>
            </div>
            <div class="admin-form-col">
                <?php render_form_input('过期时间', 'expire_time', 'datetime-local', $edit_announcement['expire_time'] ?? '', false); ?>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label">
                        <input type="checkbox" name="is_pinned" value="1" <?= ($edit_announcement['is_pinned'] ?? 0) ? 'checked' : '' ?>>
                        置顶显示
                    </label>
                </div>
            </div>
        </div>

        <div class="admin-actions">
            <?php if ($edit_announcement): ?>
                <?php render_button('更新公告', 'submit', 'admin-btn-primary'); ?>
                <?php render_link_button('取消编辑', 'announcements.php', 'admin-btn-secondary'); ?>
                <input type="hidden" name="edit_announcement" value="1">
            <?php else: ?>
                <?php render_button('添加公告', 'submit', 'admin-btn-primary'); ?>
                <input type="hidden" name="add_announcement" value="1">
            <?php endif; ?>
        </div>
    <?php render_form_end(); ?>
<?php render_card_end(); ?>

<!-- 公告列表 -->
<?php render_card_start('公告列表 (共 ' . $total . ' 个公告)'); ?>
    <?php if (!empty($announcements)): ?>
        <?php render_table_start(['ID', '标题', '类型', '状态', '优先级', '置顶', '分类', '作者', '查看数', '创建时间', '操作']); ?>
            <?php foreach ($announcements as $announcement): ?>
            <tr>
                <td><?= $announcement['id'] ?></td>
                <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    <?= htmlspecialchars($announcement['title']) ?>
                </td>
                <td>
                    <?php
                    $type_names = [
                        'notice' => '通知',
                        'urgent' => '紧急',
                        'system' => '系统',
                        'activity' => '活动'
                    ];
                    $type_colors = [
                        'notice' => 'info',
                        'urgent' => 'danger',
                        'system' => 'warning',
                        'activity' => 'success'
                    ];
                    render_badge($type_names[$announcement['type']], $type_colors[$announcement['type']]);
                    ?>
                </td>
                <td>
                    <?php
                    $status_names = [
                        'draft' => '草稿',
                        'published' => '已发布',
                        'archived' => '已归档'
                    ];
                    $status_colors = [
                        'draft' => 'warning',
                        'published' => 'success',
                        'archived' => 'info'
                    ];
                    render_badge($status_names[$announcement['status']], $status_colors[$announcement['status']]);
                    ?>
                </td>
                <td><?= $announcement['priority'] ?></td>
                <td><?= $announcement['is_pinned'] ? '<i class="fas fa-thumbtack" style="color: #e74c3c;"></i>' : '' ?></td>
                <td><?= htmlspecialchars($announcement['category_name'] ?? '') ?></td>
                <td><?= htmlspecialchars($announcement['author_name'] ?? '') ?></td>
                <td><?= $announcement['view_count'] ?></td>
                <td><?= date('Y-m-d H:i', strtotime($announcement['created_at'])) ?></td>
                <td>
                    <div class="admin-actions">
                        <?php render_link_button('编辑', 'announcements.php?edit=' . $announcement['id'], 'admin-btn-secondary admin-btn-sm'); ?>
                        <?php render_link_button('删除', 'announcements.php?delete=' . $announcement['id'], 'admin-btn-danger admin-btn-sm', 'return confirmDelete("确定要删除公告 \"' . htmlspecialchars($announcement['title']) . '\" 吗？")'); ?>
                    </div>
                </td>
            </tr>
            <?php endforeach; ?>
        <?php render_table_end(); ?>

        <!-- 分页 -->
        <?php if ($total_pages > 1): ?>
        <div style="margin-top: 20px; text-align: center;">
            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <?php
                $page_url = 'announcements.php?page=' . $i;
                if (!empty($search)) $page_url .= '&search=' . urlencode($search);
                if (!empty($status_filter)) $page_url .= '&status=' . urlencode($status_filter);
                if (!empty($type_filter)) $page_url .= '&type=' . urlencode($type_filter);
                ?>
                <?php if ($i == $page): ?>
                    <span class="admin-btn admin-btn-primary admin-btn-sm" style="margin: 0 2px;"><?= $i ?></span>
                <?php else: ?>
                    <?php render_link_button($i, $page_url, 'admin-btn-secondary admin-btn-sm', '', 'margin: 0 2px;'); ?>
                <?php endif; ?>
            <?php endfor; ?>
        </div>
        <?php endif; ?>
    <?php else: ?>
        <div style="text-align: center; padding: 40px; color: #666;">
            <i class="fas fa-bullhorn" style="font-size: 48px; margin-bottom: 15px; opacity: 0.3;"></i>
            <p>暂无公告数据</p>
        </div>
    <?php endif; ?>
<?php render_card_end(); ?>

<?php render_admin_footer(); ?>