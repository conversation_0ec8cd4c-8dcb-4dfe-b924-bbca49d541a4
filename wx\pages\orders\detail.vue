<template>
	<view class="order-detail-container">
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<uni-load-more status="loading"></uni-load-more>
		</view>
		
		<!-- 订单详情 -->
		<view class="order-detail" v-else-if="orderDetail">
			<!-- 订单状态 -->
			<view class="status-section">
				<view class="status-icon">
					<simple-icon 
						:type="getStatusIcon(orderDetail.order.order_status)" 
						size="40" 
						:color="getStatusIconColor(orderDetail.order.order_status)"
					></simple-icon>
				</view>
				<view class="status-info">
					<text class="status-text">{{ orderDetail.order.status_text }}</text>
					<text class="status-desc">{{ getStatusDesc(orderDetail.order.order_status) }}</text>
				</view>
			</view>
			
			<!-- 订单信息 -->
			<view class="info-section">
				<view class="section-title">订单信息</view>
				<view class="info-list">
					<view class="info-item">
						<text class="label">订单号</text>
						<text class="value">{{ orderDetail.order.order_no }}</text>
					</view>
					<view class="info-item">
						<text class="label">创建时间</text>
						<text class="value">{{ formatTime(orderDetail.order.created_at) }}</text>
					</view>
					<view class="info-item" v-if="orderDetail.order.payment_time">
						<text class="label">支付时间</text>
						<text class="value">{{ formatTime(orderDetail.order.payment_time) }}</text>
					</view>
					<view class="info-item" v-if="orderDetail.order.payment_method">
						<text class="label">支付方式</text>
						<text class="value">{{ getPaymentMethodText(orderDetail.order.payment_method) }}</text>
					</view>
					<view class="info-item" v-if="orderDetail.order.order_status === 'pending'">
						<text class="label">过期时间</text>
						<text class="value" :class="{ expired: orderDetail.order.is_expired }">
							{{ formatTime(orderDetail.order.expire_time) }}
						</text>
					</view>
				</view>
			</view>
			
			<!-- 商品信息 -->
			<view class="items-section">
				<view class="section-title">商品信息</view>
				<view class="items-list">
					<view class="item" v-for="item in orderDetail.items" :key="item.id">
						<image 
							class="item-image" 
							:src="item.thumbnail || item.cover_image || '/static/images/default-course.png'"
							mode="aspectFill"
						></image>
						<view class="item-info">
							<text class="item-title">{{ item.course_title }}</text>
							<view class="item-price">
								<text class="current-price">¥{{ item.course_price }}</text>
								<text class="original-price" v-if="item.original_price > item.course_price">
									¥{{ item.original_price }}
								</text>
							</view>
						</view>
						<view class="item-quantity">
							<text>x{{ item.quantity }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 金额信息 -->
			<view class="amount-section">
				<view class="section-title">金额信息</view>
				<view class="amount-list">
					<view class="amount-item">
						<text class="label">商品总额</text>
						<text class="value">¥{{ orderDetail.order.total_amount }}</text>
					</view>
					<view class="amount-item" v-if="orderDetail.order.discount_amount > 0">
						<text class="label">优惠金额</text>
						<text class="value discount">-¥{{ orderDetail.order.discount_amount }}</text>
					</view>
					<view class="amount-item total">
						<text class="label">实付金额</text>
						<text class="value">¥{{ orderDetail.order.actual_amount }}</text>
					</view>
				</view>
			</view>
			
			<!-- 支付记录 -->
			<view class="payments-section" v-if="orderDetail.payments && orderDetail.payments.length > 0">
				<view class="section-title">支付记录</view>
				<view class="payments-list">
					<view class="payment-item" v-for="payment in orderDetail.payments" :key="payment.id">
						<view class="payment-info">
							<text class="payment-method">{{ getPaymentMethodText(payment.payment_method) }}</text>
							<text class="payment-amount">¥{{ payment.amount }}</text>
						</view>
						<view class="payment-status">
							<uni-tag 
								:text="getPaymentStatusText(payment.payment_status)" 
								:type="getPaymentStatusColor(payment.payment_status)"
								size="mini"
							></uni-tag>
						</view>
						<view class="payment-time">
							<text>{{ formatTime(payment.created_at) }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 退款记录 -->
			<view class="refunds-section" v-if="orderDetail.refunds && orderDetail.refunds.length > 0">
				<view class="section-title">退款记录</view>
				<view class="refunds-list">
					<view class="refund-item" v-for="refund in orderDetail.refunds" :key="refund.id">
						<view class="refund-info">
							<text class="refund-amount">¥{{ refund.refund_amount }}</text>
							<text class="refund-reason">{{ refund.refund_reason }}</text>
						</view>
						<view class="refund-status">
							<uni-tag 
								:text="getRefundStatusText(refund.refund_status)" 
								:type="getRefundStatusColor(refund.refund_status)"
								size="mini"
							></uni-tag>
						</view>
						<view class="refund-time">
							<text>{{ formatTime(refund.created_at) }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view class="error-state" v-else-if="error">
			<simple-icon type="error" size="60" color="#ff6b6b"></simple-icon>
			<text class="error-text">{{ error }}</text>
			<button class="retry-btn" @click="loadOrderDetail">重试</button>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions" v-if="orderDetail">
			<button 
				class="action-btn primary" 
				v-if="orderDetail.order.order_status === 'pending' && !orderDetail.order.is_expired"
				@click="payOrder"
			>
				立即支付
			</button>
			<button 
				class="action-btn secondary" 
				v-if="orderDetail.order.order_status === 'pending'"
				@click="cancelOrder"
			>
				取消订单
			</button>
			<button 
				class="action-btn primary" 
				v-if="orderDetail.order.order_status === 'paid'"
				@click="goToStudy"
			>
				去学习
			</button>
		</view>
	</view>
</template>

<script>
	import { isLoggedIn, formatDate, showSuccess, showError, showConfirm } from '../../utils/storage.js';
	import { authMixin } from '../../utils/auth.js';
	import { getOrderDetail, cancelOrder, wechatPrepay } from '../../api/order.js';
	import SimpleIcon from '../../components/simple-icon/simple-icon.vue';

	export default {
		mixins: [authMixin],
		components: {
			SimpleIcon
		},
		
		data() {
			return {
				loading: false,
				orderId: null,
				orderDetail: null,
				error: null
			};
		},
		
		onLoad(options) {
			// 检查登录状态
			if (!isLoggedIn()) {
				uni.reLaunch({
					url: '/pages/login/login'
				});
				return;
			}
			
			if (options.id) {
				this.orderId = parseInt(options.id);
				this.loadOrderDetail();
			} else {
				this.error = '订单ID无效';
			}
		},
		
		onShow() {
			// 刷新订单详情
			if (this.orderId && this.orderDetail) {
				this.loadOrderDetail();
			}
		},
		
		onPullDownRefresh() {
			this.loadOrderDetail().finally(() => {
				uni.stopPullDownRefresh();
			});
		},
		
		methods: {
			/**
			 * 加载订单详情
			 */
			async loadOrderDetail() {
				if (!this.orderId) return;
				
				this.loading = true;
				this.error = null;
				
				try {
					const response = await getOrderDetail(this.orderId);
					
					if (response.code === 200) {
						this.orderDetail = response.data;
					} else {
						this.error = response.message || '加载订单详情失败';
					}
				} catch (error) {
					console.error('加载订单详情失败:', error);
					this.error = '加载订单详情失败';
				} finally {
					this.loading = false;
				}
			},
			
			/**
			 * 支付订单
			 */
			async payOrder() {
				try {
					// 检查订单是否过期
					if (this.orderDetail.order.is_expired) {
						showError('订单已过期');
						return;
					}
					
					// 获取用户openid
					const openid = uni.getStorageSync('user_openid');
					if (!openid) {
						showError('获取用户信息失败，请重新登录');
						return;
					}
					
					uni.showLoading({
						title: '正在创建支付订单...'
					});
					
					const response = await wechatPrepay(this.orderDetail.order.id, openid);
					
					if (response.code === 200) {
						const payParams = response.data.pay_params;
						
						// 调用微信支付
						uni.requestPayment({
							...payParams,
							success: () => {
								showSuccess('支付成功');
								this.loadOrderDetail();
							},
							fail: (err) => {
								console.error('支付失败:', err);
								if (err.errMsg !== 'requestPayment:fail cancel') {
									showError('支付失败');
								}
							}
						});
					} else {
						showError(response.message || '创建支付订单失败');
					}
				} catch (error) {
					console.error('支付失败:', error);
					showError('支付失败');
				} finally {
					uni.hideLoading();
				}
			},
			
			/**
			 * 取消订单
			 */
			async cancelOrder() {
				const confirmed = await showConfirm('确定要取消这个订单吗？');
				if (!confirmed) return;
				
				try {
					uni.showLoading({
						title: '正在取消订单...'
					});
					
					const response = await cancelOrder(this.orderDetail.order.id);
					
					if (response.code === 200) {
						showSuccess('订单已取消');
						this.loadOrderDetail();
					} else {
						showError(response.message || '取消订单失败');
					}
				} catch (error) {
					console.error('取消订单失败:', error);
					showError('取消订单失败');
				} finally {
					uni.hideLoading();
				}
			},
			
			/**
			 * 去学习
			 */
			goToStudy() {
				if (this.orderDetail.items && this.orderDetail.items.length > 0) {
					const courseId = this.orderDetail.items[0].course_id;
					uni.navigateTo({
						url: `/pages/courses/detail?id=${courseId}`
					});
				}
			},
			
			/**
			 * 格式化时间
			 */
			formatTime(time) {
				return formatDate(time, 'YYYY-MM-dd HH:mm:ss');
			},
			
			/**
			 * 获取状态图标
			 */
			getStatusIcon(status) {
				const iconMap = {
					pending: 'clock',
					paid: 'checkmark',
					cancelled: 'close',
					refunded: 'refresh',
					expired: 'time'
				};
				return iconMap[status] || 'help';
			},
			
			/**
			 * 获取状态图标颜色
			 */
			getStatusIconColor(status) {
				const colorMap = {
					pending: '#f39c12',
					paid: '#27ae60',
					cancelled: '#95a5a6',
					refunded: '#3498db',
					expired: '#e74c3c'
				};
				return colorMap[status] || '#95a5a6';
			},
			
			/**
			 * 获取状态描述
			 */
			getStatusDesc(status) {
				const descMap = {
					pending: '等待支付中',
					paid: '支付成功，可以开始学习',
					cancelled: '订单已取消',
					refunded: '订单已退款',
					expired: '订单已过期'
				};
				return descMap[status] || '';
			},
			
			/**
			 * 获取支付方式文本
			 */
			getPaymentMethodText(method) {
				const methodMap = {
					wechat: '微信支付',
					alipay: '支付宝'
				};
				return methodMap[method] || method;
			},
			
			/**
			 * 获取支付状态文本
			 */
			getPaymentStatusText(status) {
				const statusMap = {
					pending: '处理中',
					success: '成功',
					failed: '失败',
					cancelled: '已取消'
				};
				return statusMap[status] || status;
			},
			
			/**
			 * 获取支付状态颜色
			 */
			getPaymentStatusColor(status) {
				const colorMap = {
					pending: 'warning',
					success: 'success',
					failed: 'error',
					cancelled: 'default'
				};
				return colorMap[status] || 'default';
			},
			
			/**
			 * 获取退款状态文本
			 */
			getRefundStatusText(status) {
				const statusMap = {
					pending: '待处理',
					processing: '处理中',
					success: '成功',
					failed: '失败',
					cancelled: '已取消'
				};
				return statusMap[status] || status;
			},
			
			/**
			 * 获取退款状态颜色
			 */
			getRefundStatusColor(status) {
				const colorMap = {
					pending: 'warning',
					processing: 'info',
					success: 'success',
					failed: 'error',
					cancelled: 'default'
				};
				return colorMap[status] || 'default';
			}
		}
	};
</script>

<style lang="scss" scoped>
.order-detail-container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 80px;
}

.loading-container {
	padding: 60px 0;
}

.error-state {
	text-align: center;
	padding: 80px 20px;
	
	.error-text {
		display: block;
		color: #666;
		font-size: 16px;
		margin: 20px 0 30px;
	}
	
	.retry-btn {
		background: #007bff;
		color: #fff;
		border: none;
		border-radius: 25px;
		padding: 12px 30px;
		font-size: 14px;
	}
}

.status-section {
	background: #fff;
	padding: 30px 20px;
	text-align: center;
	margin-bottom: 15px;
	
	.status-icon {
		margin-bottom: 15px;
	}
	
	.status-text {
		display: block;
		font-size: 18px;
		font-weight: 500;
		color: #333;
		margin-bottom: 5px;
	}
	
	.status-desc {
		display: block;
		font-size: 14px;
		color: #666;
	}
}

.info-section,
.items-section,
.amount-section,
.payments-section,
.refunds-section {
	background: #fff;
	margin-bottom: 15px;
	
	.section-title {
		padding: 15px 20px;
		font-size: 16px;
		font-weight: 500;
		color: #333;
		border-bottom: 1px solid #f5f5f5;
	}
}

.info-list {
	padding: 0 20px;
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 0;
	border-bottom: 1px solid #f8f9fa;
	
	&:last-child {
		border-bottom: none;
	}
	
	.label {
		font-size: 14px;
		color: #666;
	}
	
	.value {
		font-size: 14px;
		color: #333;
		
		&.expired {
			color: #e74c3c;
		}
	}
}

.items-list {
	padding: 15px 20px;
}

.item {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
	
	&:last-child {
		margin-bottom: 0;
	}
	
	.item-image {
		width: 60px;
		height: 45px;
		border-radius: 6px;
		margin-right: 12px;
		background: #f5f5f5;
	}
	
	.item-info {
		flex: 1;
		
		.item-title {
			display: block;
			font-size: 14px;
			color: #333;
			margin-bottom: 5px;
			line-height: 1.4;
		}
		
		.item-price {
			display: flex;
			align-items: center;
			gap: 8px;
			
			.current-price {
				font-size: 14px;
				color: #e74c3c;
				font-weight: 500;
			}
			
			.original-price {
				font-size: 12px;
				color: #999;
				text-decoration: line-through;
			}
		}
	}
	
	.item-quantity {
		font-size: 14px;
		color: #666;
	}
}

.amount-list {
	padding: 0 20px;
}

.amount-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 0;
	border-bottom: 1px solid #f8f9fa;
	
	&:last-child {
		border-bottom: none;
	}
	
	&.total {
		font-weight: 500;
		
		.value {
			color: #e74c3c;
			font-size: 16px;
		}
	}
	
	.label {
		font-size: 14px;
		color: #666;
	}
	
	.value {
		font-size: 14px;
		color: #333;
		
		&.discount {
			color: #27ae60;
		}
	}
}

.payments-list,
.refunds-list {
	padding: 15px 20px;
}

.payment-item,
.refund-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 0;
	border-bottom: 1px solid #f8f9fa;
	
	&:last-child {
		border-bottom: none;
	}
}

.payment-info,
.refund-info {
	flex: 1;
	
	.payment-method,
	.refund-amount {
		display: block;
		font-size: 14px;
		color: #333;
		margin-bottom: 5px;
	}
	
	.payment-amount {
		display: block;
		font-size: 14px;
		color: #e74c3c;
		font-weight: 500;
	}
	
	.refund-reason {
		display: block;
		font-size: 12px;
		color: #666;
	}
}

.payment-time,
.refund-time {
	font-size: 12px;
	color: #999;
	margin-left: 10px;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 15px 20px;
	border-top: 1px solid #eee;
	display: flex;
	gap: 15px;
	
	.action-btn {
		flex: 1;
		height: 44px;
		border-radius: 22px;
		font-size: 16px;
		border: none;
		
		&.primary {
			background: #007bff;
			color: #fff;
		}
		
		&.secondary {
			background: #fff;
			color: #007bff;
			border: 1px solid #007bff;
		}
	}
}
</style>
