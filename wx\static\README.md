# 静态资源说明

## 目录结构

```
static/
├── tabbar/           # 底部导航栏图标
│   ├── home.png      # 首页图标（未选中）
│   ├── home-active.png # 首页图标（选中）
│   ├── course.png    # 课程图标（未选中）
│   ├── course-active.png # 课程图标（选中）
│   ├── notice.png    # 公告图标（未选中）
│   ├── notice-active.png # 公告图标（选中）
│   ├── profile.png   # 个人图标（未选中）
│   └── profile-active.png # 个人图标（选中）
├── logo.png          # 应用Logo
├── default-avatar.png # 默认头像
├── default-course.png # 默认课程封面
└── README.md         # 说明文件
```

## 图标规格

### 底部导航栏图标
- 尺寸：81px × 81px
- 格式：PNG
- 背景：透明
- 颜色：未选中状态使用灰色，选中状态使用主题色

### Logo
- 尺寸：200px × 200px
- 格式：PNG
- 背景：透明或白色

### 头像
- 尺寸：200px × 200px
- 格式：PNG
- 背景：透明或白色

### 课程封面
- 尺寸：300px × 200px
- 格式：PNG/JPG
- 背景：可以有背景色

## 注意事项

1. 所有图标都应该使用高清图片，支持不同设备的显示需求
2. 图标设计应该简洁明了，符合小程序设计规范
3. 颜色搭配应该与应用整体风格保持一致
4. 建议使用矢量图标，然后导出为PNG格式

## 替换说明

当前使用的是占位图标，实际开发中需要：

1. 设计师提供符合品牌风格的图标
2. 按照上述规格要求制作图标
3. 替换对应的文件
4. 测试在不同设备上的显示效果

## 图标来源建议

- 可以使用 iconfont、Feather Icons、Material Icons 等图标库
- 也可以使用 Figma、Sketch 等设计工具自行设计
- 注意版权问题，确保图标可以商用
