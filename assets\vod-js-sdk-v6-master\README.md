# vod-js-sdk-v6

Web SDK for Tencent Cloud Video Service

Document: https://cloud.tencent.com/document/product/266

## Install

`npm install vod-js-sdk-v6`

For browser, there is a umd bundle: `dist/vod-js-sdk-v6.js`

We use `Promise` in the source code. You should imoprt Promise polyfill when target legacy browsers.

## Demo

demo https://tencentyun.github.io/vod-js-sdk-v6/

demo source code https://github.com/tencentyun/vod-js-sdk-v6/blob/master/docs/index.html

`import` usage demo: https://github.com/tencentyun/vod-js-sdk-v6/tree/master/docs/import-demo

troubleshooting https://github.com/tencentyun/vod-js-sdk-v6/wiki/%E5%B8%B8%E8%A7%81%E6%95%85%E9%9A%9C%E6%8E%92%E6%9F%A5

## Usage

* NOTICE: Document may be outdated, but demo and source code are always latest*

See: https://cloud.tencent.com/document/product/266/9239

## Contributing

1. `git clone` this project
2. `npm install`
3. `npm run test`
4. add more task cases