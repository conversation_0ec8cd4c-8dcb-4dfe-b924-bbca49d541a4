<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

/**
 * 系统状态检查页面
 * 检查微信小程序登录系统的各项配置和功能状态
 */

// 检查数据库表是否存在
function checkDatabaseTables($conn) {
    $required_tables = [
        'users' => '用户表',
        'wechat_users' => '微信用户表',
        'user_tokens' => 'JWT令牌表',
        'user_login_logs' => '登录日志表',
        'settings' => '系统设置表'
    ];
    
    $results = [];
    foreach ($required_tables as $table => $description) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        $results[$table] = [
            'name' => $description,
            'exists' => $result->num_rows > 0
        ];
    }
    
    return $results;
}

// 检查用户表字段
function checkUserTableFields($conn) {
    $required_fields = [
        'username' => '用户名字段',
        'password' => '密码字段',
        'login_type' => '登录类型字段',
        'status' => '用户状态字段',
        'last_login_at' => '最后登录时间字段'
    ];
    
    $results = [];
    $table_info = $conn->query("DESCRIBE users");
    $existing_fields = [];
    
    while ($row = $table_info->fetch_assoc()) {
        $existing_fields[] = $row['Field'];
    }
    
    foreach ($required_fields as $field => $description) {
        $results[$field] = [
            'name' => $description,
            'exists' => in_array($field, $existing_fields)
        ];
    }
    
    return $results;
}

// 检查系统配置
function checkSystemSettings($conn) {
    $required_settings = [
        'wechat_app_id' => '微信小程序AppID',
        'wechat_app_secret' => '微信小程序AppSecret',
        'jwt_secret_key' => 'JWT密钥',
        'jwt_access_token_expire' => '访问令牌过期时间',
        'jwt_refresh_token_expire' => '刷新令牌过期时间'
    ];
    
    $result = $conn->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('" . implode("','", array_keys($required_settings)) . "')");
    $existing_settings = [];
    
    while ($row = $result->fetch_assoc()) {
        $existing_settings[$row['setting_key']] = $row['setting_value'];
    }
    
    $results = [];
    foreach ($required_settings as $key => $description) {
        $value = $existing_settings[$key] ?? '';
        $results[$key] = [
            'name' => $description,
            'configured' => !empty($value),
            'value' => $key === 'wechat_app_secret' ? (empty($value) ? '' : '已配置') : $value
        ];
    }
    
    return $results;
}

// 检查API文件
function checkApiFiles() {
    $required_files = [
        '../api/auth.php' => '认证基础类',
        '../api/auth-login.php' => '传统登录接口',
        '../api/auth-register.php' => '用户注册接口',
        '../api/auth-wechat.php' => '微信登录接口',
        '../api/auth-me.php' => '用户信息接口'
    ];
    
    $results = [];
    foreach ($required_files as $file => $description) {
        $results[$file] = [
            'name' => $description,
            'exists' => file_exists($file)
        ];
    }
    
    return $results;
}

// 检查微信小程序文件
function checkWechatFiles() {
    $required_files = [
        '../wx/pages/login/login.vue' => '登录页面',
        '../wx/api/user.js' => '用户API服务',
        '../wx/utils/api.js' => 'API工具类'
    ];
    
    $results = [];
    foreach ($required_files as $file => $description) {
        $results[$file] = [
            'name' => $description,
            'exists' => file_exists($file)
        ];
    }
    
    return $results;
}

// 执行检查
$db_tables = checkDatabaseTables($conn);
$user_fields = checkUserTableFields($conn);
$system_settings = checkSystemSettings($conn);
$api_files = checkApiFiles();
$wechat_files = checkWechatFiles();

// 计算总体状态
$total_checks = 0;
$passed_checks = 0;

foreach ([$db_tables, $user_fields, $api_files, $wechat_files] as $checks) {
    foreach ($checks as $check) {
        $total_checks++;
        if ($check['exists']) $passed_checks++;
    }
}

foreach ($system_settings as $check) {
    $total_checks++;
    if ($check['configured']) $passed_checks++;
}

$completion_rate = round(($passed_checks / $total_checks) * 100);

// 渲染页面头部
render_admin_header('系统状态检查', 'system_status');
?>

<!-- 系统状态概览 -->
<?php render_card_start('系统状态概览'); ?>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
        <div class="status-card <?php echo $completion_rate >= 90 ? 'status-success' : ($completion_rate >= 70 ? 'status-warning' : 'status-error'); ?>">
            <div class="status-icon">
                <i class="fas <?php echo $completion_rate >= 90 ? 'fa-check-circle' : ($completion_rate >= 70 ? 'fa-exclamation-triangle' : 'fa-times-circle'); ?>"></i>
            </div>
            <div class="status-info">
                <div class="status-title">系统完整性</div>
                <div class="status-value"><?php echo $completion_rate; ?>%</div>
                <div class="status-desc"><?php echo $passed_checks; ?>/<?php echo $total_checks; ?> 项检查通过</div>
            </div>
        </div>
        
        <div class="status-card status-info">
            <div class="status-icon">
                <i class="fas fa-database"></i>
            </div>
            <div class="status-info">
                <div class="status-title">数据库状态</div>
                <div class="status-value"><?php echo array_sum(array_column($db_tables, 'exists')); ?>/<?php echo count($db_tables); ?></div>
                <div class="status-desc">数据表完整性</div>
            </div>
        </div>
        
        <div class="status-card status-info">
            <div class="status-icon">
                <i class="fas fa-cog"></i>
            </div>
            <div class="status-info">
                <div class="status-title">配置状态</div>
                <div class="status-value"><?php echo array_sum(array_column($system_settings, 'configured')); ?>/<?php echo count($system_settings); ?></div>
                <div class="status-desc">系统配置完整性</div>
            </div>
        </div>
        
        <div class="status-card status-info">
            <div class="status-icon">
                <i class="fas fa-code"></i>
            </div>
            <div class="status-info">
                <div class="status-title">文件状态</div>
                <div class="status-value"><?php echo array_sum(array_column($api_files, 'exists')) + array_sum(array_column($wechat_files, 'exists')); ?>/<?php echo count($api_files) + count($wechat_files); ?></div>
                <div class="status-desc">核心文件完整性</div>
            </div>
        </div>
    </div>
    
    <?php if ($completion_rate >= 90): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <strong>系统状态良好！</strong> 微信小程序登录系统已正确安装和配置。
        </div>
    <?php elseif ($completion_rate >= 70): ?>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>系统基本可用，</strong> 但还有一些配置项需要完善。
        </div>
    <?php else: ?>
        <div class="alert alert-error">
            <i class="fas fa-times-circle"></i>
            <strong>系统配置不完整！</strong> 请按照下方提示完成必要的配置。
        </div>
    <?php endif; ?>
<?php render_card_end(); ?>

<!-- 数据库表检查 -->
<?php render_card_start('数据库表检查'); ?>
    <div class="check-list">
        <?php foreach ($db_tables as $table => $info): ?>
            <div class="check-item <?php echo $info['exists'] ? 'check-success' : 'check-error'; ?>">
                <div class="check-icon">
                    <i class="fas <?php echo $info['exists'] ? 'fa-check' : 'fa-times'; ?>"></i>
                </div>
                <div class="check-content">
                    <div class="check-title"><?php echo $info['name']; ?> (<?php echo $table; ?>)</div>
                    <div class="check-status"><?php echo $info['exists'] ? '已创建' : '未创建'; ?></div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <?php if (array_sum(array_column($db_tables, 'exists')) < count($db_tables)): ?>
        <div class="alert alert-info" style="margin-top: 15px;">
            <i class="fas fa-info-circle"></i>
            请运行 <a href="../install/install_auth_system.php" target="_blank">数据库安装脚本</a> 来创建缺失的数据表。
        </div>
    <?php endif; ?>
<?php render_card_end(); ?>

<!-- 用户表字段检查 -->
<?php render_card_start('用户表字段检查'); ?>
    <div class="check-list">
        <?php foreach ($user_fields as $field => $info): ?>
            <div class="check-item <?php echo $info['exists'] ? 'check-success' : 'check-error'; ?>">
                <div class="check-icon">
                    <i class="fas <?php echo $info['exists'] ? 'fa-check' : 'fa-times'; ?>"></i>
                </div>
                <div class="check-content">
                    <div class="check-title"><?php echo $info['name']; ?> (<?php echo $field; ?>)</div>
                    <div class="check-status"><?php echo $info['exists'] ? '已添加' : '未添加'; ?></div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php render_card_end(); ?>

<!-- 系统配置检查 -->
<?php render_card_start('系统配置检查'); ?>
    <div class="check-list">
        <?php foreach ($system_settings as $key => $info): ?>
            <div class="check-item <?php echo $info['configured'] ? 'check-success' : 'check-error'; ?>">
                <div class="check-icon">
                    <i class="fas <?php echo $info['configured'] ? 'fa-check' : 'fa-times'; ?>"></i>
                </div>
                <div class="check-content">
                    <div class="check-title"><?php echo $info['name']; ?></div>
                    <div class="check-status">
                        <?php if ($info['configured']): ?>
                            已配置: <?php echo $info['value']; ?>
                        <?php else: ?>
                            未配置
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <?php if (array_sum(array_column($system_settings, 'configured')) < count($system_settings)): ?>
        <div class="alert alert-info" style="margin-top: 15px;">
            <i class="fas fa-info-circle"></i>
            请在 <a href="settings.php">系统设置</a> 页面完成微信小程序配置。
        </div>
    <?php endif; ?>
<?php render_card_end(); ?>

<!-- API文件检查 -->
<?php render_card_start('API文件检查'); ?>
    <div class="check-list">
        <?php foreach ($api_files as $file => $info): ?>
            <div class="check-item <?php echo $info['exists'] ? 'check-success' : 'check-error'; ?>">
                <div class="check-icon">
                    <i class="fas <?php echo $info['exists'] ? 'fa-check' : 'fa-times'; ?>"></i>
                </div>
                <div class="check-content">
                    <div class="check-title"><?php echo $info['name']; ?></div>
                    <div class="check-status">
                        <?php echo $info['exists'] ? '文件存在' : '文件缺失'; ?>
                        <code style="margin-left: 10px; font-size: 11px; opacity: 0.7;"><?php echo $file; ?></code>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php render_card_end(); ?>

<!-- 微信小程序文件检查 -->
<?php render_card_start('微信小程序文件检查'); ?>
    <div class="check-list">
        <?php foreach ($wechat_files as $file => $info): ?>
            <div class="check-item <?php echo $info['exists'] ? 'check-success' : 'check-error'; ?>">
                <div class="check-icon">
                    <i class="fas <?php echo $info['exists'] ? 'fa-check' : 'fa-times'; ?>"></i>
                </div>
                <div class="check-content">
                    <div class="check-title"><?php echo $info['name']; ?></div>
                    <div class="check-status">
                        <?php echo $info['exists'] ? '文件存在' : '文件缺失'; ?>
                        <code style="margin-left: 10px; font-size: 11px; opacity: 0.7;"><?php echo $file; ?></code>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php render_card_end(); ?>

<!-- 快速操作 -->
<?php render_card_start('快速操作'); ?>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
        <div class="quick-action">
            <h4><i class="fas fa-database"></i> 数据库管理</h4>
            <p>安装或更新数据库表结构</p>
            <a href="../install/install_auth_system.php" target="_blank" class="admin-btn admin-btn-primary admin-btn-sm">
                运行安装脚本
            </a>
        </div>

        <div class="quick-action">
            <h4><i class="fas fa-cog"></i> 系统配置</h4>
            <p>配置微信小程序和JWT设置</p>
            <a href="settings.php" class="admin-btn admin-btn-info admin-btn-sm">
                打开设置页面
            </a>
        </div>

        <div class="quick-action">
            <h4><i class="fas fa-users"></i> 用户管理</h4>
            <p>管理用户账户和认证状态</p>
            <a href="user_auth.php" class="admin-btn admin-btn-success admin-btn-sm">
                用户认证管理
            </a>
        </div>

        <div class="quick-action">
            <h4><i class="fas fa-mobile-alt"></i> 小程序测试</h4>
            <p>测试微信小程序登录功能</p>
            <button class="admin-btn admin-btn-warning admin-btn-sm" onclick="showTestInfo()">
                查看测试说明
            </button>
        </div>
    </div>
<?php render_card_end(); ?>

<script>
function showTestInfo() {
    alert('微信小程序测试说明：\n\n1. 确保已在系统设置中配置微信AppID和AppSecret\n2. 在微信开发者工具中导入wx目录\n3. 修改wx/utils/api.js中的API地址\n4. 在开发者工具中测试登录功能\n5. 检查后台用户认证管理页面是否有新用户');
}
</script>

<style>
.status-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    border-left: 4px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.status-card.status-success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.status-card.status-warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.status-card.status-error {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.status-card.status-info {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.status-icon {
    font-size: 32px;
    opacity: 0.8;
}

.status-success .status-icon { color: #155724; }
.status-warning .status-icon { color: #856404; }
.status-error .status-icon { color: #721c24; }
.status-info .status-icon { color: #0c5460; }

.status-info .status-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
    opacity: 0.8;
}

.status-info .status-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 2px;
}

.status-info .status-desc {
    font-size: 12px;
    opacity: 0.7;
}

.check-list {
    display: grid;
    gap: 12px;
}

.check-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.check-item.check-success {
    background: #d4edda;
    border-color: #c3e6cb;
}

.check-item.check-error {
    background: #f8d7da;
    border-color: #f5c6cb;
}

.check-icon {
    width: 24px;
    text-align: center;
}

.check-success .check-icon { color: #155724; }
.check-error .check-icon { color: #721c24; }

.check-content .check-title {
    font-weight: 600;
    margin-bottom: 2px;
}

.check-content .check-status {
    font-size: 12px;
    opacity: 0.8;
}

.alert {
    padding: 15px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.quick-action {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.quick-action:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.quick-action h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 16px;
}

.quick-action h4 i {
    margin-right: 8px;
    color: #667eea;
}

.quick-action p {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}
</style>

<?php render_admin_footer(); ?>
