# TabBar图标下载脚本
# 使用PowerShell下载图标文件

Write-Host "🚀 开始下载TabBar图标..." -ForegroundColor Green

# 创建下载目录
$iconDir = "."
if (!(Test-Path $iconDir)) {
    New-Item -ItemType Directory -Path $iconDir -Force
}

# 图标下载列表
$icons = @(
    @{
        Name = "home.png"
        Url = "https://img.icons8.com/ios/81/7A7E83/home--v1.png"
        Description = "首页图标(普通)"
    },
    @{
        Name = "home-active.png"
        Url = "https://img.icons8.com/ios-filled/81/007bff/home--v1.png"
        Description = "首页图标(选中)"
    },
    @{
        Name = "course.png"
        Url = "https://img.icons8.com/ios/81/7A7E83/video.png"
        Description = "课程图标(普通)"
    },
    @{
        Name = "course-active.png"
        Url = "https://img.icons8.com/ios-filled/81/007bff/video.png"
        Description = "课程图标(选中)"
    },
    @{
        Name = "profile.png"
        Url = "https://img.icons8.com/ios/81/7A7E83/user.png"
        Description = "个人图标(普通)"
    },
    @{
        Name = "profile-active.png"
        Url = "https://img.icons8.com/ios-filled/81/007bff/user.png"
        Description = "个人图标(选中)"
    }
)

# 下载函数
function Download-Icon {
    param(
        [string]$Url,
        [string]$FileName,
        [string]$Description
    )
    
    try {
        Write-Host "📥 下载 $Description..." -ForegroundColor Yellow
        $filePath = Join-Path $iconDir $FileName
        
        # 使用Invoke-WebRequest下载文件
        Invoke-WebRequest -Uri $Url -OutFile $filePath -UseBasicParsing
        
        if (Test-Path $filePath) {
            $fileSize = (Get-Item $filePath).Length
            Write-Host "✅ $Description 下载成功 ($fileSize bytes)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description 下载失败" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ $Description 下载出错: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 开始下载
$successCount = 0
$totalCount = $icons.Count

foreach ($icon in $icons) {
    if (Download-Icon -Url $icon.Url -FileName $icon.Name -Description $icon.Description) {
        $successCount++
    }
    Start-Sleep -Milliseconds 500  # 避免请求过快
}

# 显示结果
Write-Host "`n📊 下载完成统计:" -ForegroundColor Cyan
Write-Host "成功: $successCount/$totalCount" -ForegroundColor Green
Write-Host "失败: $($totalCount - $successCount)/$totalCount" -ForegroundColor Red

if ($successCount -eq $totalCount) {
    Write-Host "`n🎉 所有图标下载成功！" -ForegroundColor Green
    Write-Host "📱 现在可以重新编译小程序项目了" -ForegroundColor Yellow
} else {
    Write-Host "`n⚠️  部分图标下载失败，请检查网络连接" -ForegroundColor Yellow
    Write-Host "💡 可以手动访问以下链接下载:" -ForegroundColor Cyan
    
    foreach ($icon in $icons) {
        $filePath = Join-Path $iconDir $icon.Name
        if (!(Test-Path $filePath)) {
            Write-Host "   $($icon.Name): $($icon.Url)" -ForegroundColor White
        }
    }
}

Write-Host "`n📁 图标文件位置: $((Get-Location).Path)" -ForegroundColor Cyan
Write-Host "🔄 下次运行: .\download-icons.ps1" -ForegroundColor Cyan

# 检查文件大小
Write-Host "`n📋 文件检查:" -ForegroundColor Cyan
Get-ChildItem -Path $iconDir -Filter "*.png" | ForEach-Object {
    $size = [math]::Round($_.Length / 1KB, 2)
    Write-Host "   $($_.Name): ${size}KB" -ForegroundColor White
}
