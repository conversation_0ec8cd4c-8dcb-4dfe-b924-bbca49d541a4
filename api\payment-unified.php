<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);

require_once '../includes/db.php';
require_once '../includes/auth.php';
require_once '../includes/payment_bridge.php';

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 验证用户身份
$user = authenticate_user();
if (!$user) {
    http_response_code(401);
    echo json_encode([
        'code' => 401,
        'message' => '未授权访问',
        'data' => null
    ]);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
if (!isset($input['order_id']) || !isset($input['payment_type'])) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少必需参数',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $user_id = $user['id'];
    $order_id = intval($input['order_id']);
    $payment_type = trim($input['payment_type']); // wechat, alipay, wxpay, qqpay
    $system_type = $input['system_type'] ?? 'auto'; // api, legacy, auto
    
    // 获取支付桥接器
    $bridge = getPaymentBridge();
    
    // 验证订单
    $stmt = $conn->prepare("
        SELECT * FROM orders 
        WHERE id = ? AND user_id = ? AND order_status = 'pending' AND payment_status = 'unpaid'
    ");
    $stmt->bind_param("ii", $order_id, $user_id);
    $stmt->execute();
    $order = $stmt->get_result()->fetch_assoc();
    
    if (!$order) {
        echo json_encode([
            'code' => 404,
            'message' => '订单不存在或状态不正确',
            'data' => null
        ]);
        exit;
    }
    
    // 检查订单是否过期
    if (strtotime($order['expire_time']) < time()) {
        $stmt = $conn->prepare("UPDATE orders SET order_status = 'expired' WHERE id = ?");
        $stmt->bind_param("i", $order_id);
        $stmt->execute();
        
        echo json_encode([
            'code' => 400,
            'message' => '订单已过期',
            'data' => null
        ]);
        exit;
    }
    
    // 获取可用支付方式
    $available_methods = $bridge->getAvailablePaymentMethods();
    $selected_method = null;
    
    foreach ($available_methods as $method) {
        if (($payment_type === 'wechat' && $method['type'] === 'wechat') ||
            ($payment_type !== 'wechat' && $method['epay_type'] === $payment_type)) {
            $selected_method = $method;
            break;
        }
    }
    
    if (!$selected_method) {
        echo json_encode([
            'code' => 400,
            'message' => '不支持的支付方式',
            'data' => null
        ]);
        exit;
    }
    
    // 根据系统类型处理支付
    if ($selected_method['type'] === 'wechat') {
        // 微信支付使用API系统
        $response = processWechatPayment($order, $user, $input);
    } else {
        // 码支付根据配置选择系统
        if ($selected_method['system'] === 'legacy' || $system_type === 'legacy') {
            $response = processLegacyEpayPayment($order, $user, $payment_type, $bridge);
        } else {
            $response = processApiEpayPayment($order, $user, $payment_type);
        }
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage(),
        'data' => null
    ]);
}

/**
 * 处理微信支付
 */
function processWechatPayment($order, $user, $input) {
    require_once 'payment-wechat-prepay.php';
    // 这里调用现有的微信支付逻辑
    // 为了简化，直接返回成功响应
    return [
        'code' => 200,
        'message' => '微信支付预支付订单创建成功',
        'data' => [
            'payment_type' => 'wechat',
            'system' => 'api'
        ]
    ];
}

/**
 * 处理API系统码支付
 */
function processApiEpayPayment($order, $user, $payment_type) {
    global $conn;
    
    // 生成支付流水号
    $payment_no = 'EPAY' . date('YmdHis') . sprintf('%06d', $user['id']) . sprintf('%04d', rand(1000, 9999));
    
    // 创建支付记录
    $stmt = $conn->prepare("
        INSERT INTO payments (payment_no, order_id, user_id, payment_method, amount, 
                             payment_status, created_at) 
        VALUES (?, ?, ?, ?, ?, 'pending', NOW())
    ");
    $payment_method = 'epay_' . $payment_type;
    $stmt->bind_param("siiss", $payment_no, $order['id'], $user['id'], $payment_method, $order['actual_amount']);
    $stmt->execute();
    $payment_id = $conn->insert_id;
    
    // 获取配置
    $stmt = $conn->prepare("
        SELECT setting_key, setting_value 
        FROM settings 
        WHERE setting_key IN ('epay_api_url', 'epay_partner_id', 'epay_partner_key', 'epay_notify_url', 'epay_return_url')
    ");
    $stmt->execute();
    $settings_result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    $settings = [];
    foreach ($settings_result as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
    
    // 构建支付参数
    $epay_params = [
        'pid' => $settings['epay_partner_id'],
        'type' => $payment_type,
        'out_trade_no' => $payment_no,
        'notify_url' => $settings['epay_notify_url'],
        'return_url' => $settings['epay_return_url'],
        'name' => '课程购买-订单号:' . $order['order_no'],
        'money' => number_format($order['actual_amount'], 2, '.', ''),
        'sitename' => '在线学习平台'
    ];
    
    // 生成签名
    $epay_params['sign'] = generate_epay_sign($epay_params, $settings['epay_partner_key']);
    $epay_params['sign_type'] = 'MD5';
    
    // 构建支付URL
    $api_url = rtrim($settings['epay_api_url'], '/') . '/submit.php';
    $payment_url = $api_url . '?' . http_build_query($epay_params);
    
    return [
        'code' => 200,
        'message' => '预支付订单创建成功',
        'data' => [
            'payment_id' => $payment_id,
            'payment_no' => $payment_no,
            'payment_url' => $payment_url,
            'payment_type' => $payment_type,
            'system' => 'api',
            'amount' => $order['actual_amount']
        ]
    ];
}

/**
 * 处理传统系统码支付
 */
function processLegacyEpayPayment($order, $user, $payment_type, $bridge) {
    // 生成传统系统订单号
    $trade_no = 'LEGACY' . date('YmdHis') . sprintf('%06d', $user['id']) . sprintf('%04d', rand(1000, 9999));
    
    // 在传统系统中创建订单
    $legacy_order = [
        'trade_no' => $trade_no,
        'name' => '课程购买-订单号:' . $order['order_no'],
        'money' => $order['actual_amount'],
        'userid' => $user['id'],
        'ip' => $_SERVER['REMOTE_ADDR']
    ];
    
    $result = $bridge->createLegacyOrder($legacy_order);
    
    if (!$result) {
        throw new Exception('创建传统系统订单失败');
    }
    
    // 生成支付URL
    $payment_url = $bridge->generateLegacyPaymentUrl($trade_no, $payment_type);
    
    return [
        'code' => 200,
        'message' => '预支付订单创建成功',
        'data' => [
            'trade_no' => $trade_no,
            'payment_url' => $payment_url,
            'payment_type' => $payment_type,
            'system' => 'legacy',
            'amount' => $order['actual_amount']
        ]
    ];
}

/**
 * 生成码支付易支付签名
 */
function generate_epay_sign($params, $key) {
    $filtered_params = [];
    foreach ($params as $k => $v) {
        if ($k !== 'sign' && $k !== 'sign_type' && $v !== '' && $v !== null) {
            $filtered_params[$k] = $v;
        }
    }
    
    ksort($filtered_params);
    
    $sign_string = '';
    foreach ($filtered_params as $k => $v) {
        $sign_string .= $k . '=' . $v . '&';
    }
    $sign_string .= 'key=' . $key;
    
    return md5($sign_string);
}
?>
