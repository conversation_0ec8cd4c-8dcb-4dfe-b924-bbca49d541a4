<?php
/**
 * 课时详情API
 * 获取指定课时的详细信息
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db.php';
require_once '../includes/lesson_db_setup.php';

// 确保数据库表结构正确
$setup_result = setupLessonTables($conn);
if (!$setup_result['success']) {
    echo json_encode([
        'success' => false,
        'message' => '数据库初始化失败',
        'error' => implode(', ', $setup_result['messages'])
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 获取参数
    $lesson_id = isset($_GET['lesson_id']) ? intval($_GET['lesson_id']) : 0;
    $course_id = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;
    
    // 验证参数
    if ($lesson_id <= 0) {
        echo json_encode([
            'success' => false,
            'message' => '课时ID无效'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建查询条件
    $where_conditions = ["l.id = ?"];
    $params = [$lesson_id];
    $param_types = "i";
    
    if ($course_id > 0) {
        $where_conditions[] = "l.course_id = ?";
        $params[] = $course_id;
        $param_types .= "i";
    }
    
    // 获取课时详情
    $sql = "SELECT
                l.id,
                l.course_id,
                l.title,
                l.description,
                l.video_url,
                l.vod_file_id,
                l.vod_video_url,
                l.video_type,
                l.thumbnail,
                l.duration,
                l.sort_order,
                l.status,
                l.is_free,
                l.created_by,
                l.created_at,
                l.updated_at,
                c.title as course_title,
                c.status as course_status,
                c.is_free as course_is_free
            FROM lessons l
            LEFT JOIN courses c ON l.course_id = c.id
            WHERE " . implode(" AND ", $where_conditions);
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    $lesson = $result->fetch_assoc();
    
    if (!$lesson) {
        echo json_encode([
            'success' => false,
            'message' => '课时不存在'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查课时状态
    if ($lesson['status'] != 1) {
        echo json_encode([
            'success' => false,
            'message' => '课时已禁用'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 获取同课程的其他课时（用于导航）
    $siblings_sql = "SELECT id, title, sort_order, is_free, duration 
                     FROM lessons 
                     WHERE course_id = ? AND status = 1 AND id != ?
                     ORDER BY sort_order ASC, id ASC";
    $siblings_stmt = $conn->prepare($siblings_sql);
    $siblings_stmt->bind_param("ii", $lesson['course_id'], $lesson['id']);
    $siblings_stmt->execute();
    $siblings_result = $siblings_stmt->get_result();
    
    $siblings = [];
    $current_index = 0;
    $prev_lesson = null;
    $next_lesson = null;
    
    while ($sibling = $siblings_result->fetch_assoc()) {
        $siblings[] = [
            'id' => intval($sibling['id']),
            'title' => $sibling['title'],
            'sort_order' => intval($sibling['sort_order']),
            'is_free' => intval($sibling['is_free']),
            'duration' => intval($sibling['duration']),
            'duration_formatted' => $sibling['duration'] ? gmdate("H:i:s", $sibling['duration']) : null
        ];
    }
    
    // 找到当前课时在列表中的位置，确定上一课时和下一课时
    $all_lessons_sql = "SELECT id, title, sort_order 
                        FROM lessons 
                        WHERE course_id = ? AND status = 1
                        ORDER BY sort_order ASC, id ASC";
    $all_lessons_stmt = $conn->prepare($all_lessons_sql);
    $all_lessons_stmt->bind_param("i", $lesson['course_id']);
    $all_lessons_stmt->execute();
    $all_lessons_result = $all_lessons_stmt->get_result();
    
    $all_lessons = [];
    while ($row = $all_lessons_result->fetch_assoc()) {
        $all_lessons[] = $row;
    }
    
    for ($i = 0; $i < count($all_lessons); $i++) {
        if ($all_lessons[$i]['id'] == $lesson['id']) {
            $current_index = $i;
            if ($i > 0) {
                $prev_lesson = [
                    'id' => intval($all_lessons[$i-1]['id']),
                    'title' => $all_lessons[$i-1]['title'],
                    'sort_order' => intval($all_lessons[$i-1]['sort_order'])
                ];
            }
            if ($i < count($all_lessons) - 1) {
                $next_lesson = [
                    'id' => intval($all_lessons[$i+1]['id']),
                    'title' => $all_lessons[$i+1]['title'],
                    'sort_order' => intval($all_lessons[$i+1]['sort_order'])
                ];
            }
            break;
        }
    }
    
    // 如果是腾讯云点播视频，只获取M3U8格式的播放URL
    if ($lesson['video_type'] === 'vod' && !empty($lesson['vod_file_id'])) {
        // 获取m3u8格式的播放URL
        $vod_urls = getVodPlayUrls($lesson['vod_file_id']);
        if ($vod_urls && !empty($vod_urls['m3u8'])) {
            // 只使用m3u8格式
            $lesson['vod_video_url'] = $vod_urls['m3u8'];
            $lesson['vod_video_url_m3u8'] = $vod_urls['m3u8'];

            // 更新数据库中的播放URL
            $update_stmt = $conn->prepare("UPDATE lessons SET vod_video_url = ? WHERE id = ?");
            $update_stmt->bind_param("si", $lesson['vod_video_url'], $lesson_id);
            $update_stmt->execute();
        } else {
            // 如果没有m3u8格式，尝试从MP4 URL生成M3U8 URL
            if (!empty($lesson['vod_video_url']) && strpos($lesson['vod_video_url'], '.mp4') !== false) {
                $generated_m3u8 = generateM3u8Url($lesson['vod_video_url'], $lesson['vod_file_id']);
                if ($generated_m3u8) {
                    $lesson['vod_video_url'] = $generated_m3u8;
                    $lesson['vod_video_url_m3u8'] = $generated_m3u8;

                    // 更新数据库
                    $update_stmt = $conn->prepare("UPDATE lessons SET vod_video_url = ? WHERE id = ?");
                    $update_stmt->bind_param("si", $lesson['vod_video_url'], $lesson_id);
                    $update_stmt->execute();
                }
            }
        }

        // 清空MP4相关字段，确保只使用M3U8
        $lesson['vod_video_url_mp4'] = '';
        $lesson['video_url'] = '';
    }

    // 处理缩略图URL - 确保返回完整的网络地址（修复微信小程序video组件poster属性问题）
    $thumbnail_url = convertToFullUrl($lesson['thumbnail']);

    // 格式化返回数据
    $lesson_data = [
        'id' => intval($lesson['id']),
        'course_id' => intval($lesson['course_id']),
        'title' => $lesson['title'],
        'description' => $lesson['description'],
        'video_url' => $lesson['video_url'],
        'vod_file_id' => $lesson['vod_file_id'],
        'vod_video_url' => $lesson['vod_video_url'],
        'vod_video_url_m3u8' => $lesson['vod_video_url_m3u8'] ?? '',
        'vod_video_url_mp4' => $lesson['vod_video_url_mp4'] ?? '',
        'video_type' => $lesson['video_type'] ?? 'url',
        'thumbnail' => $thumbnail_url,  // 使用处理后的完整网络地址
        'duration' => intval($lesson['duration']),
        'duration_formatted' => $lesson['duration'] ? gmdate("H:i:s", $lesson['duration']) : null,
        'sort_order' => intval($lesson['sort_order']),
        'status' => intval($lesson['status']),
        'is_free' => intval($lesson['is_free']),
        'created_by' => intval($lesson['created_by']),
        'created_at' => $lesson['created_at'],
        'updated_at' => $lesson['updated_at'],
        'course' => [
            'id' => intval($lesson['course_id']),
            'title' => $lesson['course_title'],
            'status' => $lesson['course_status'],
            'is_free' => intval($lesson['course_is_free'])
        ],
        'navigation' => [
            'current_index' => $current_index + 1,
            'total_lessons' => count($all_lessons),
            'prev_lesson' => $prev_lesson,
            'next_lesson' => $next_lesson
        ],
        'siblings' => $siblings
    ];
    
    // 返回结果
    echo json_encode([
        'success' => true,
        'data' => $lesson_data,
        'message' => '获取课时详情成功'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '服务器错误',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 获取腾讯云VOD的播放URL（支持m3u8和mp4格式）
 */
function getVodPlayUrls($file_id) {
    require_once '../includes/vod_config.php';

    if (empty($file_id)) {
        return null;
    }

    // 获取配置
    $config = VodConfig::getConfig();

    // 构建请求参数
    $params = [
        'Action' => 'DescribeMediaInfos',
        'Version' => '2018-07-17',
        'Region' => 'ap-beijing',
        'FileIds.0' => $file_id,
        'Timestamp' => time(),
        'Nonce' => rand(10000, 99999),
        'SecretId' => $config['secret_id']
    ];

    // 构建请求字符串
    ksort($params);
    $request_string = '';
    foreach ($params as $key => $value) {
        $request_string .= $key . '=' . $value . '&';
    }
    $request_string = rtrim($request_string, '&');

    // 构建签名原文字符串
    $sign_str = "GET" . "vod.tencentcloudapi.com" . "/?" . $request_string;

    // 生成签名
    $signature = base64_encode(hash_hmac('sha1', $sign_str, $config['secret_key'], true));

    // 添加签名到请求参数
    $params['Signature'] = $signature;

    // 构建请求URL
    $request_url = 'https://vod.tencentcloudapi.com/?' . http_build_query($params);

    // 发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $request_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code != 200) {
        return null;
    }

    $result = json_decode($response, true);

    if (isset($result['Response']['Error'])) {
        return null;
    }

    $media_info = $result['Response']['MediaInfoSet'][0] ?? null;
    if (!$media_info) {
        return null;
    }

    $urls = [
        'm3u8' => '',
        'mp4' => ''
    ];

    // 获取自适应流URL（m3u8格式）
    $adaptive_streaming_info = $media_info['AdaptiveDynamicStreamingInfo'] ?? [];
    if (isset($adaptive_streaming_info['AdaptiveDynamicStreamingSet']) && is_array($adaptive_streaming_info['AdaptiveDynamicStreamingSet'])) {
        $adaptive_streams = $adaptive_streaming_info['AdaptiveDynamicStreamingSet'];
        if (!empty($adaptive_streams)) {
            $urls['m3u8'] = $adaptive_streams[0]['Url'] ?? '';
        }
    }

    // 获取转码后的URL（mp4格式）
    $transcoding_info = $media_info['TranscodeInfo'] ?? [];
    if (isset($transcoding_info['TranscodeSet']) && is_array($transcoding_info['TranscodeSet'])) {
        $streams = $transcoding_info['TranscodeSet'];

        // 按清晰度优先级排序：720p > 480p > 其他
        usort($streams, function($a, $b) {
            $height_a = $a['Height'] ?? 0;
            $height_b = $b['Height'] ?? 0;

            // 优先级：720p > 480p > 其他
            $priority_a = ($height_a == 720) ? 3 : (($height_a == 480) ? 2 : 1);
            $priority_b = ($height_b == 720) ? 3 : (($height_b == 480) ? 2 : 1);

            return $priority_b - $priority_a;
        });

        if (!empty($streams)) {
            $urls['mp4'] = $streams[0]['Url'] ?? '';
        }
    }

    // 如果没有找到任何URL，返回null
    if (empty($urls['m3u8']) && empty($urls['mp4'])) {
        return null;
    }

    return $urls;
}

/**
 * 从MP4 URL生成M3U8 URL
 */
function generateM3u8Url($mp4_url, $file_id) {
    if (empty($mp4_url) || empty($file_id)) {
        return null;
    }

    // 腾讯云VOD的M3U8 URL生成规则
    // 方法1: 直接替换扩展名
    $m3u8_url = str_replace('.mp4', '.m3u8', $mp4_url);

    // 方法2: 使用腾讯云标准的自适应流URL格式
    // 格式: https://domain/vodtranscq1234567890/fileId/playlist.m3u8
    $url_parts = parse_url($mp4_url);
    if ($url_parts && isset($url_parts['host'], $url_parts['path'])) {
        $path_parts = explode('/', trim($url_parts['path'], '/'));
        if (count($path_parts) >= 3) {
            $domain = $url_parts['scheme'] . '://' . $url_parts['host'];
            $app_id_part = $path_parts[0]; // 如: e20d94advodcq1325256586
            $file_id_part = $path_parts[1]; // 如: fabd267b5145403692208013494

            // 尝试多种可能的M3U8 URL格式
            $possible_urls = [
                // 标准自适应流格式
                "$domain/$app_id_part/$file_id_part/playlist.m3u8",
                // 带转码模板的格式
                "$domain/$app_id_part/$file_id_part/adp.10.m3u8",
                "$domain/$app_id_part/$file_id_part/adp.20.m3u8",
                // 直接替换扩展名
                $m3u8_url
            ];

            // 返回第一个可能的URL（后续可以添加URL验证）
            return $possible_urls[0];
        }
    }

    // 如果解析失败，返回简单替换的结果
    return $m3u8_url;
}

/**
 * 验证M3U8 URL是否可访问
 */
function validateM3u8Url($url) {
    if (empty($url)) {
        return false;
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return $http_code === 200;
}
?>
