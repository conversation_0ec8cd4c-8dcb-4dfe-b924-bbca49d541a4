<?php
/**
 * 检查微信支付配置
 */

header('Content-Type: text/html; charset=utf-8');
require_once '../includes/db.php';

echo "<h1>微信支付配置检查</h1>";

try {
    // 检查微信支付配置
    $stmt = $conn->prepare("
        SELECT setting_key, setting_value 
        FROM settings 
        WHERE setting_key LIKE 'wechat_pay_%' 
        ORDER BY setting_key
    ");
    $stmt->execute();
    $settings = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    echo "<h2>当前配置</h2>";
    if (!empty($settings)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>配置项</th><th>值</th><th>状态</th></tr>";
        
        $config_status = [];
        foreach ($settings as $setting) {
            $key = $setting['setting_key'];
            $value = $setting['setting_value'];
            $status = '';
            
            if (empty($value)) {
                $status = '<span style="color: red;">❌ 未配置</span>';
                $config_status[$key] = false;
            } else {
                $status = '<span style="color: green;">✅ 已配置</span>';
                $config_status[$key] = true;
                
                // 隐藏敏感信息
                if (strpos($key, 'api_key') !== false) {
                    $value = str_repeat('*', min(strlen($value), 20));
                }
            }
            
            echo "<tr>";
            echo "<td>$key</td>";
            echo "<td>$value</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 分析配置状态
        echo "<h2>配置分析</h2>";
        $required_configs = ['wechat_pay_app_id', 'wechat_pay_mch_id', 'wechat_pay_api_key'];
        $missing_configs = [];
        
        foreach ($required_configs as $config) {
            if (!isset($config_status[$config]) || !$config_status[$config]) {
                $missing_configs[] = $config;
            }
        }
        
        if (empty($missing_configs)) {
            echo "<p style='color: green;'>✅ 所有必需配置都已设置</p>";
        } else {
            echo "<p style='color: red;'>❌ 缺少以下必需配置：</p>";
            echo "<ul>";
            foreach ($missing_configs as $config) {
                echo "<li>$config</li>";
            }
            echo "</ul>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ 没有找到微信支付配置</p>";
    }
    
    // 提供配置建议
    echo "<h2>配置建议</h2>";
    echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #007cba;'>";
    echo "<h3>要解决 'appid和mch_id不匹配' 错误，请：</h3>";
    echo "<ol>";
    echo "<li><strong>确保AppID正确</strong>：当前使用的是 'wxa936b5c9fa9b1893'，请确认这是您的微信小程序AppID</li>";
    echo "<li><strong>获取正确的商户号</strong>：需要在微信支付商户平台获取真实的商户号</li>";
    echo "<li><strong>获取API密钥</strong>：在微信支付商户平台设置API密钥</li>";
    echo "<li><strong>确保AppID和商户号匹配</strong>：AppID必须与商户号绑定</li>";
    echo "</ol>";
    echo "</div>";
    
    // 提供临时解决方案
    echo "<h2>临时解决方案</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107;'>";
    echo "<p><strong>如果您暂时没有微信支付商户号，可以：</strong></p>";
    echo "<ol>";
    echo "<li>先测试订单创建功能（已经正常工作）</li>";
    echo "<li>在支付环节显示提示信息，告知用户支付功能正在开发中</li>";
    echo "<li>或者直接将订单标记为已支付（仅用于测试）</li>";
    echo "</ol>";
    echo "</div>";
    
    // 提供快速修复SQL
    echo "<h2>配置更新SQL</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>";
    echo "<p>如果您有真实的微信支付配置，请执行以下SQL：</p>";
    echo "<pre>";
    echo "-- 插入或更新微信支付配置\n";
    echo "INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES\n";
    echo "('wechat_pay_enabled', '1', 'boolean', '是否启用微信支付'),\n";
    echo "('wechat_pay_app_id', '您的微信小程序AppID', 'string', '微信小程序AppID'),\n";
    echo "('wechat_pay_mch_id', '您的微信支付商户号', 'string', '微信支付商户号'),\n";
    echo "('wechat_pay_api_key', '您的微信支付API密钥', 'string', '微信支付API密钥'),\n";
    echo "('wechat_pay_notify_url', 'https://您的域名/api/payment-wechat-notify.php', 'string', '微信支付回调URL')\n";
    echo "ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);\n";
    echo "</pre>";
    echo "</div>";
    
    // 检查是否可以禁用微信支付
    echo "<h2>快速操作</h2>";
    if (isset($_GET['action'])) {
        if ($_GET['action'] === 'disable_payment') {
            $stmt = $conn->prepare("
                INSERT INTO settings (setting_key, setting_value, setting_type, description) 
                VALUES ('wechat_pay_enabled', '0', 'boolean', '是否启用微信支付')
                ON DUPLICATE KEY UPDATE setting_value = '0'
            ");
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✅ 已禁用微信支付</p>";
            } else {
                echo "<p style='color: red;'>❌ 禁用失败</p>";
            }
        } elseif ($_GET['action'] === 'enable_payment') {
            $stmt = $conn->prepare("
                INSERT INTO settings (setting_key, setting_value, setting_type, description) 
                VALUES ('wechat_pay_enabled', '1', 'boolean', '是否启用微信支付')
                ON DUPLICATE KEY UPDATE setting_value = '1'
            ");
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✅ 已启用微信支付</p>";
            } else {
                echo "<p style='color: red;'>❌ 启用失败</p>";
            }
        }
    }
    
    echo "<p>";
    echo "<a href='?action=disable_payment' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; margin-right: 10px;'>禁用微信支付</a>";
    echo "<a href='?action=enable_payment' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none;'>启用微信支付</a>";
    echo "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 检查过程中出现错误: " . $e->getMessage() . "</p>";
}
?>
