<template>
	<view class="page-container" :class="containerClass">
		<!-- 自定义导航栏 -->
		<view 
			class="custom-navbar" 
			v-if="showNavbar"
			:style="navbarStyle"
		>
			<view class="navbar-content">
				<!-- 左侧内容 -->
				<view class="navbar-left">
					<view 
						class="navbar-back" 
						v-if="showBack"
						@click="handleBack"
					>
						<uni-icons type="back" size="20" color="#fff"></uni-icons>
					</view>
					<slot name="navbar-left"></slot>
				</view>
				
				<!-- 中间标题 -->
				<view class="navbar-center">
					<text class="navbar-title" v-if="title">{{ title }}</text>
					<slot name="navbar-center"></slot>
				</view>
				
				<!-- 右侧内容 -->
				<view class="navbar-right">
					<slot name="navbar-right"></slot>
				</view>
			</view>
		</view>
		
		<!-- 页面内容 -->
		<view class="page-content" :style="contentStyle">
			<!-- 加载状态 -->
			<loading 
				:show="loading" 
				:text="loadingText"
				v-if="showLoading"
			></loading>
			
			<!-- 错误状态 -->
			<error 
				:show="!!error && !loading" 
				:title="errorTitle"
				:message="error"
				:show-retry="showRetry"
				@retry="handleRetry"
				v-if="showError"
			></error>
			
			<!-- 空状态 -->
			<empty 
				:show="isEmpty && !loading && !error"
				:icon-type="emptyIcon"
				:title="emptyTitle"
				:description="emptyDescription"
				:action-text="emptyActionText"
				@action="handleEmptyAction"
				v-if="showEmpty"
			></empty>
			
			<!-- 正常内容 -->
			<view class="content-wrapper" v-if="!loading && !error && !isEmpty">
				<slot></slot>
			</view>
		</view>
		
		<!-- 底部内容 -->
		<view class="page-footer" v-if="$slots.footer">
			<slot name="footer"></slot>
		</view>
	</view>
</template>

<script>
	import Loading from '../loading/loading.vue';
	import Error from '../error/error.vue';
	import Empty from '../empty/empty.vue';
	
	export default {
		name: 'PageContainer',
		components: {
			Loading,
			Error,
			Empty
		},
		props: {
			// 页面标题
			title: {
				type: String,
				default: ''
			},
			// 是否显示导航栏
			showNavbar: {
				type: Boolean,
				default: false
			},
			// 是否显示返回按钮
			showBack: {
				type: Boolean,
				default: true
			},
			// 导航栏背景色
			navbarBg: {
				type: String,
				default: '#007bff'
			},
			// 页面背景色
			pageBg: {
				type: String,
				default: '#f8f9fa'
			},
			// 加载状态
			loading: {
				type: Boolean,
				default: false
			},
			// 加载文本
			loadingText: {
				type: String,
				default: '加载中...'
			},
			// 是否显示加载组件
			showLoading: {
				type: Boolean,
				default: true
			},
			// 错误信息
			error: {
				type: String,
				default: ''
			},
			// 错误标题
			errorTitle: {
				type: String,
				default: '加载失败'
			},
			// 是否显示重试按钮
			showRetry: {
				type: Boolean,
				default: true
			},
			// 是否显示错误组件
			showError: {
				type: Boolean,
				default: true
			},
			// 是否为空状态
			isEmpty: {
				type: Boolean,
				default: false
			},
			// 空状态图标
			emptyIcon: {
				type: String,
				default: 'info'
			},
			// 空状态标题
			emptyTitle: {
				type: String,
				default: '暂无数据'
			},
			// 空状态描述
			emptyDescription: {
				type: String,
				default: ''
			},
			// 空状态操作按钮文本
			emptyActionText: {
				type: String,
				default: ''
			},
			// 是否显示空状态组件
			showEmpty: {
				type: Boolean,
				default: true
			}
		},
		
		computed: {
			/**
			 * 容器样式类
			 */
			containerClass() {
				return {
					'has-navbar': this.showNavbar
				};
			},
			
			/**
			 * 导航栏样式
			 */
			navbarStyle() {
				return {
					background: this.navbarBg,
					paddingTop: uni.getSystemInfoSync().statusBarHeight + 'px'
				};
			},
			
			/**
			 * 内容区域样式
			 */
			contentStyle() {
				return {
					background: this.pageBg
				};
			}
		},
		
		methods: {
			/**
			 * 处理返回
			 */
			handleBack() {
				this.$emit('back');
				if (!this.$listeners.back) {
					uni.navigateBack();
				}
			},
			
			/**
			 * 处理重试
			 */
			handleRetry() {
				this.$emit('retry');
			},
			
			/**
			 * 处理空状态操作
			 */
			handleEmptyAction() {
				this.$emit('empty-action');
			}
		}
	};
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	
	&.has-navbar {
		.page-content {
			padding-top: 44px;
		}
	}
}

.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	
	.navbar-content {
		height: 44px;
		display: flex;
		align-items: center;
		padding: 0 15px;
		
		.navbar-left {
			display: flex;
			align-items: center;
			min-width: 60px;
			
			.navbar-back {
				padding: 8px;
				margin-left: -8px;
			}
		}
		
		.navbar-center {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.navbar-title {
				color: #fff;
				font-size: 18px;
				font-weight: 500;
				text-align: center;
			}
		}
		
		.navbar-right {
			display: flex;
			align-items: center;
			min-width: 60px;
			justify-content: flex-end;
		}
	}
}

.page-content {
	flex: 1;
	position: relative;
}

.content-wrapper {
	min-height: 100%;
}

.page-footer {
	background: #fff;
	border-top: 1px solid #f0f0f0;
}
</style>
