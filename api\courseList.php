<?php
/**
 * 课程列表API
 * 支持JWT认证，获取用户可访问的课程列表
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

$auth = new AuthAPI();

try {
    // 获取请求参数
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;
    $recommended = isset($_GET['recommended']) ? (int)$_GET['recommended'] : 0;
    $popular = isset($_GET['popular']) ? (int)$_GET['popular'] : 0;
    $latest = isset($_GET['latest']) ? (int)$_GET['latest'] : 0;

    // 限制每页数量
    if ($limit > 50) $limit = 50;
    if ($limit < 1) $limit = 10;
    if ($page < 1) $page = 1;

    $offset = ($page - 1) * $limit;

    // 如果提供了user_id，验证用户认证并获取用户课程
    if ($user_id > 0) {
        $user = $auth->requireAuth();
        if ($user['id'] != $user_id) {
            $auth->jsonResponse(403, '无权访问其他用户的课程');
        }
        getUserCourses($auth, $user_id, $page, $limit, $offset, $search);
    } else {
        // 获取公开课程列表（不需要认证）
        getPublicCourses($auth, $page, $limit, $offset, $search, $recommended, $popular, $latest);
    }

} catch (Exception $e) {
    error_log('课程列表API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 获取用户有权限的课程列表
 */

function getUserCourses($auth, $user_id, $page, $limit, $offset, $search) {
    $conn = $auth->getConn();
    
    $where_conditions = [
        // 移除课程状态检查，因为用户已购买的课程即使下架也应该可以访问
        "uc.user_id = ?",
        "uc.status = 'active'",
        "(uc.expires_at IS NULL OR uc.expires_at > NOW())"
    ];
    $params = [$user_id];
    $param_types = "i";
    
    if (!empty($search)) {
        $where_conditions[] = "(c.title LIKE ? OR c.description LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $param_types .= "ss";
    }
    
    $where_clause = "WHERE " . implode(' AND ', $where_conditions);
    
    // 获取总数
    $count_sql = "SELECT COUNT(*) as total 
                  FROM user_courses uc 
                  LEFT JOIN courses c ON uc.course_id = c.id 
                  $where_clause";
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param($param_types, ...$params);
    $count_stmt->execute();
    $total = $count_stmt->get_result()->fetch_assoc()['total'];
    
    // 获取用户课程数据
    $sql = "SELECT c.id, c.title, c.description, c.video_url, c.thumbnail, 
                   c.duration, c.sort_order, c.created_at,
                   uc.assigned_at, uc.expires_at, uc.last_watched_at, 
                   uc.watch_progress, uc.watch_count
            FROM user_courses uc 
            LEFT JOIN courses c ON uc.course_id = c.id 
            $where_clause 
            ORDER BY c.sort_order ASC, uc.assigned_at DESC 
            LIMIT ? OFFSET ?";
    
    $stmt = $conn->prepare($sql);
    $params[] = $limit;
    $params[] = $offset;
    $param_types .= "ii";
    
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $courses = [];
    while ($row = $result->fetch_assoc()) {
        // 格式化时长
        if ($row['duration']) {
            $row['duration_formatted'] = gmdate("H:i:s", $row['duration']);
        } else {
            $row['duration_formatted'] = null;
        }
        
        // 添加用户相关信息
        $row['user_info'] = [
            'assigned_at' => $row['assigned_at'],
            'expires_at' => $row['expires_at'],
            'last_watched_at' => $row['last_watched_at'],
            'watch_progress' => floatval($row['watch_progress']),
            'watch_count' => intval($row['watch_count'])
        ];
        
        // 移除重复字段
        unset($row['assigned_at'], $row['expires_at'], $row['last_watched_at'], 
              $row['watch_progress'], $row['watch_count']);
        
        $courses[] = $row;
    }
    
    $response_data = [
        'list' => $courses,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $total,
            'pages' => ceil($total / $limit)
        ]
    ];
    
    $auth->jsonResponse(200, '获取成功', $response_data);
}

/**
 * 获取公开课程列表（支持用户购买状态检查）
 */
function getPublicCourses($auth, $page, $limit, $offset, $search, $recommended, $popular, $latest) {
    $conn = $auth->getConn();

    // 尝试获取当前用户信息（可选认证）
    $current_user = null;
    try {
        $current_user = $auth->getCurrentUser(); // 不强制要求认证
    } catch (Exception $e) {
        // 用户未登录，继续执行
    }

    $where_conditions = ["c.status = 'active'"];
    $params = [];
    $param_types = "";

    if (!empty($search)) {
        $where_conditions[] = "(c.title LIKE ? OR c.description LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $param_types .= "ss";
    }

    $where_clause = "WHERE " . implode(' AND ', $where_conditions);

    // 排序条件
    $order_clause = "ORDER BY ";
    if ($recommended) {
        $order_clause .= "COALESCE(c.is_recommend, c.is_recommended, 0) DESC, ";
    }
    if ($popular) {
        $order_clause .= "COALESCE(c.view_count, 0) DESC, ";
    }
    if ($latest) {
        $order_clause .= "c.created_at DESC, ";
    }
    $order_clause .= "c.sort_order ASC, c.id DESC";

    // 获取总数
    $count_sql = "SELECT COUNT(*) as total FROM courses c $where_clause";
    if (!empty($params)) {
        $count_stmt = $conn->prepare($count_sql);
        $count_stmt->bind_param($param_types, ...$params);
        $count_stmt->execute();
        $total = $count_stmt->get_result()->fetch_assoc()['total'];
    } else {
        $result = $conn->query($count_sql);
        $total = $result->fetch_assoc()['total'];
    }

    // 获取课程数据（包含用户购买状态）
    $user_join = "";
    $user_fields = "";
    if ($current_user) {
        $user_join = "LEFT JOIN user_courses uc ON c.id = uc.course_id AND uc.user_id = {$current_user['id']} AND uc.status = 'active'";
        $user_fields = ",
                       CASE
                           WHEN uc.id IS NOT NULL THEN 1
                           ELSE 0
                       END as is_purchased,
                       uc.assigned_at as purchase_date,
                       uc.expires_at as expires_at,
                       CASE
                           WHEN c.is_free = 1 THEN 'free'
                           WHEN uc.id IS NOT NULL THEN 'purchased'
                           ELSE 'not_purchased'
                       END as purchase_status";
    } else {
        $user_fields = ",
                       0 as is_purchased,
                       NULL as purchase_date,
                       NULL as expires_at,
                       CASE
                           WHEN c.is_free = 1 THEN 'free'
                           ELSE 'not_purchased'
                       END as purchase_status";
    }

    $sql = "SELECT c.id, c.title, c.description,
                   COALESCE(c.cover_image, c.thumbnail) as cover_image,
                   c.thumbnail,
                   c.duration, c.sort_order, c.created_at,
                   COALESCE(c.view_count, 0) as view_count,
                   COALESCE(c.is_recommended, 0) as is_recommended,
                   COALESCE(c.is_recommend, 0) as is_recommend,
                   COALESCE(c.price, 0) as price,
                   c.original_price,
                   COALESCE(c.is_free, 1) as is_free,
                   COALESCE(c.teacher_name, '专业讲师') as teacher_name,
                   c.subtitle,
                   COALESCE(c.rating, 0) as rating,
                   COALESCE(c.student_count, 0) as student_count,
                   COALESCE((SELECT COUNT(*) FROM lessons WHERE course_id = c.id AND status = 1), 0) as lesson_count
                   $user_fields
            FROM courses c
            $user_join
            $where_clause
            $order_clause
            LIMIT ? OFFSET ?";

    $params[] = $limit;
    $params[] = $offset;
    $param_types .= "ii";

    $stmt = $conn->prepare($sql);
    if (!empty($param_types)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    $courses = [];
    while ($row = $result->fetch_assoc()) {
        // 格式化时长
        if ($row['duration']) {
            $row['duration_formatted'] = gmdate("H:i:s", $row['duration']);
        } else {
            $row['duration_formatted'] = null;
        }

        // 格式化价格
        $row['price'] = $row['price'] ? floatval($row['price']) : 0;
        $row['original_price'] = $row['original_price'] ? floatval($row['original_price']) : null;
        $row['is_free'] = (bool)$row['is_free'];
        $row['is_recommended'] = (bool)$row['is_recommended'];

        // 格式化课时数
        $row['lesson_count'] = intval($row['lesson_count']);

        // 格式化评分
        $row['rating'] = $row['rating'] ? floatval($row['rating']) : 0;

        // 格式化购买状态
        $row['is_purchased'] = (bool)($row['is_purchased'] ?? false);
        $row['purchase_status'] = $row['purchase_status'] ?? 'not_purchased';

        // 添加用户友好的状态标签
        switch ($row['purchase_status']) {
            case 'free':
                $row['status_label'] = '免费';
                $row['status_color'] = 'success';
                $row['action_text'] = '开始学习';
                $row['show_price'] = false;
                break;
            case 'purchased':
                $row['status_label'] = null; // 已购买课程不显示状态标签
                $row['status_color'] = null;
                $row['action_text'] = '开始学习';
                $row['show_price'] = false; // 已购买课程不显示价格
                break;
            case 'not_purchased':
                $row['status_label'] = '¥' . $row['price'];
                $row['status_color'] = 'warning';
                $row['action_text'] = '立即购买';
                $row['show_price'] = true;
                break;
        }

        // 添加缩略图字段兼容性
        if (!isset($row['thumbnail']) && isset($row['cover_image'])) {
            $row['thumbnail'] = $row['cover_image'];
        }

        // 移除敏感信息
        unset($row['video_url']);

        $courses[] = $row;
    }

    $response_data = [
        'list' => $courses,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $total,
            'pages' => ceil($total / $limit)
        ]
    ];

    $auth->jsonResponse(200, '获取成功', $response_data);
}
