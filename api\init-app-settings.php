<?php
/**
 * 初始化应用外观设置
 * 添加logo、标题、副标题等配置项到settings表
 */

require_once '../includes/db.php';

header('Content-Type: text/html; charset=utf-8');

echo "<h1>初始化应用外观设置</h1>";

try {
    // 定义默认的应用外观设置
    $app_settings = [
        [
            'setting_key' => 'app_logo_url',
            'setting_value' => '/static/logo.png',
            'setting_type' => 'string',
            'description' => '应用Logo图片URL',
            'group_name' => 'appearance'
        ],
        [
            'setting_key' => 'app_title',
            'setting_value' => '课程学习系统',
            'setting_type' => 'string',
            'description' => '应用主标题',
            'group_name' => 'appearance'
        ],
        [
            'setting_key' => 'app_subtitle',
            'setting_value' => '在线学习，随时随地',
            'setting_type' => 'string',
            'description' => '应用副标题',
            'group_name' => 'appearance'
        ],
        [
            'setting_key' => 'logo_upload_path',
            'setting_value' => '/uploads/logos/',
            'setting_type' => 'string',
            'description' => 'Logo上传路径',
            'group_name' => 'appearance'
        ],
        [
            'setting_key' => 'max_logo_size',
            'setting_value' => '2048',
            'setting_type' => 'number',
            'description' => 'Logo文件最大大小（KB）',
            'group_name' => 'appearance'
        ],
        [
            'setting_key' => 'allowed_logo_types',
            'setting_value' => 'jpg,jpeg,png,gif',
            'setting_type' => 'string',
            'description' => '允许的Logo文件类型',
            'group_name' => 'appearance'
        ]
    ];

    $success_count = 0;
    $skip_count = 0;

    foreach ($app_settings as $setting) {
        // 检查设置是否已存在
        $check_stmt = $conn->prepare("SELECT id FROM settings WHERE setting_key = ?");
        $check_stmt->bind_param("s", $setting['setting_key']);
        $check_stmt->execute();
        $result = $check_stmt->get_result();

        if ($result->num_rows > 0) {
            echo "<p style='color: orange;'>⚠️ 设置 '{$setting['setting_key']}' 已存在，跳过</p>";
            $skip_count++;
        } else {
            // 插入新设置
            $insert_stmt = $conn->prepare("
                INSERT INTO settings (setting_key, setting_value, setting_type, description, group_name) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $insert_stmt->bind_param(
                "sssss", 
                $setting['setting_key'], 
                $setting['setting_value'], 
                $setting['setting_type'], 
                $setting['description'], 
                $setting['group_name']
            );

            if ($insert_stmt->execute()) {
                echo "<p style='color: green;'>✅ 成功添加设置: {$setting['setting_key']} = {$setting['setting_value']}</p>";
                $success_count++;
            } else {
                echo "<p style='color: red;'>❌ 添加设置失败: {$setting['setting_key']} - " . $conn->error . "</p>";
            }
        }
    }

    echo "<hr>";
    echo "<h2>初始化完成</h2>";
    echo "<p>成功添加: {$success_count} 个设置</p>";
    echo "<p>跳过已存在: {$skip_count} 个设置</p>";

    // 创建uploads/logos目录（如果不存在）
    $upload_dir = $_SERVER['DOCUMENT_ROOT'] . '/uploads/logos';
    if (!is_dir($upload_dir)) {
        if (mkdir($upload_dir, 0755, true)) {
            echo "<p style='color: green;'>✅ 成功创建上传目录: /uploads/logos</p>";
        } else {
            echo "<p style='color: red;'>❌ 创建上传目录失败: /uploads/logos</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ 上传目录已存在: /uploads/logos</p>";
    }

    // 显示当前所有外观设置
    echo "<h3>当前外观设置：</h3>";
    $settings_stmt = $conn->prepare("SELECT setting_key, setting_value, description FROM settings WHERE group_name = 'appearance' ORDER BY setting_key");
    $settings_stmt->execute();
    $settings_result = $settings_stmt->get_result();

    if ($settings_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>设置键</th><th>设置值</th><th>描述</th></tr>";
        while ($row = $settings_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['setting_key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['setting_value']) . "</td>";
            echo "<td>" . htmlspecialchars($row['description']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>没有找到外观设置</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 初始化失败: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../admin/settings.php'>返回系统设置</a></p>";
?>
