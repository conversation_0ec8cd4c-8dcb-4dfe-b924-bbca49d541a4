<?php
/**
 * API连接测试文件
 * 用于测试微信小程序与API服务器的连接
 */

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 获取请求方法
$method = $_SERVER['REQUEST_METHOD'];

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

// 返回测试响应
$response = [
    'code' => 200,
    'message' => 'API连接测试成功',
    'data' => [
        'server_time' => date('Y-m-d H:i:s'),
        'method' => $method,
        'input' => $input,
        'server_info' => [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
        ]
    ]
];

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
