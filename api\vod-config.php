<?php
/**
 * 腾讯云点播配置检查API
 * 用于检查VOD配置状态和环境要求
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/vod_config.php';

try {
    // 验证管理员权限
    session_start();
    if (!isset($_SESSION['admin_id'])) {
        echo json_encode([
            'success' => false,
            'message' => '需要管理员权限'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $action = isset($_GET['action']) ? $_GET['action'] : 'check';
    
    switch ($action) {
        case 'check':
            // 检查配置状态
            $configValidation = VodConfig::validateConfig();
            $environment = checkVodEnvironment();
            $config = VodConfig::getConfig();
            
            // 隐藏敏感信息
            $safeConfig = $config;
            $safeConfig['secret_id'] = $config['secret_id'] === 'YOUR_SECRET_ID' ? '未配置' : '已配置';
            $safeConfig['secret_key'] = $config['secret_key'] === 'YOUR_SECRET_KEY' ? '未配置' : '已配置';
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'config_valid' => $configValidation['valid'],
                    'config_errors' => $configValidation['errors'],
                    'config' => $safeConfig,
                    'environment' => $environment
                ],
                'message' => '配置检查完成'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'test':
            // 测试签名生成
            require_once '../includes/vod_signature.php';
            
            $testResult = VodSignature::generateUploadSignature();
            
            echo json_encode([
                'success' => $testResult['success'],
                'data' => [
                    'signature_test' => $testResult['success'],
                    'test_message' => $testResult['message']
                ],
                'message' => $testResult['success'] ? '签名测试成功' : '签名测试失败'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'info':
            // 获取基本信息
            $config = VodConfig::getConfig();
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'allowed_video_types' => $config['allowed_video_types'],
                    'allowed_image_types' => $config['allowed_image_types'],
                    'max_video_size' => $config['max_video_size'],
                    'max_image_size' => $config['max_image_size'],
                    'max_video_size_mb' => round($config['max_video_size'] / 1024 / 1024, 2),
                    'max_image_size_mb' => round($config['max_image_size'] / 1024 / 1024, 2),
                    'signature_expire_time' => $config['signature_expire_time'],
                    'upload_region' => $config['upload_region'],
                    'storage_region' => $config['storage_region']
                ],
                'message' => '配置信息获取成功'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => '无效的操作'
            ], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '服务器错误',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
