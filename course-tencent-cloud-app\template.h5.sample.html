<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="author" content="深圳酷瓜软件有限公司" />
        <meta name="keywords" content="开源网课系统，开源网校系统，开源网络教育平台，开源在线教育平台" />
        <meta name="description" content="酷瓜云课堂，依托腾讯云基础服务架构，使用C扩展框架Phalcon开发，GPL-2.0开源协议发布！" />
        <title>
            <%= htmlWebpackPlugin.options.title %>
        </title>
        <script>
            var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS
                .supports(
                    'top: constant(a)'))
            document.write(
                '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
                (coverSupport ? ', viewport-fit=cover' : '') + '" />')
        </script>
        <link rel="shortcut icon" href="/static/favicon.ico" />
        <link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
    </head>
    <body>
        <noscript>
            <strong>Please enable JavaScript to continue.</strong>
        </noscript>
        <div id="app"></div>
    </body>
</html>