<?php
/**
 * 腾讯云点播(VOD)配置文件
 * 用于管理腾讯云点播服务的相关配置
 */

// 腾讯云点播配置
class VodConfig {
    // 腾讯云API密钥配置
    const SECRET_ID = 'AKIDoV3Qguxsb7RaVEk6HF1ayCG8nETfniRx';  // 请替换为您的SecretId
    const SECRET_KEY = 'Mp9GEzYtKk1IVdeerunUyFQRTP9gz4zi'; // 请替换为您的SecretKey
    
    // 点播应用配置
    const SUB_APP_ID = 0; // 子应用ID，0表示主应用
    
    // 上传配置
    const UPLOAD_REGION = 'ap-chongqing'; // 上传地域
    const STORAGE_REGION = 'ap-chongqing'; // 存储地域
    
    // 视频处理配置
    const PROCEDURE = 'wx'; // 任务流模板名称，留空表示不进行任务流处理
    
    // 分类配置
    const CLASS_ID = 0; // 分类ID，0表示其他分类
    
    // 过期时间配置（秒）
    const SIGNATURE_EXPIRE_TIME = 3600; // 签名过期时间，1小时
    
    // 允许的文件类型
    const ALLOWED_VIDEO_TYPES = [
        'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', 'm4v', '3gp'
    ];
    
    // 允许的图片类型（封面）
    const ALLOWED_IMAGE_TYPES = [
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'
    ];
    
    // 文件大小限制（字节）
    const MAX_VIDEO_SIZE = 5 * 1024 * 1024 * 1024; // 5GB
    const MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
    
    /**
     * 获取配置信息
     */
    public static function getConfig() {
        return [
            'secret_id' => self::SECRET_ID,
            'secret_key' => self::SECRET_KEY,
            'sub_app_id' => self::SUB_APP_ID,
            'upload_region' => self::UPLOAD_REGION,
            'storage_region' => self::STORAGE_REGION,
            'procedure' => self::PROCEDURE,
            'class_id' => self::CLASS_ID,
            'signature_expire_time' => self::SIGNATURE_EXPIRE_TIME,
            'allowed_video_types' => self::ALLOWED_VIDEO_TYPES,
            'allowed_image_types' => self::ALLOWED_IMAGE_TYPES,
            'max_video_size' => self::MAX_VIDEO_SIZE,
            'max_image_size' => self::MAX_IMAGE_SIZE
        ];
    }
    
    /**
     * 验证配置是否完整
     */
    public static function validateConfig() {
        $errors = [];
        
        if (self::SECRET_ID === 'YOUR_SECRET_ID' || empty(self::SECRET_ID)) {
            $errors[] = 'SECRET_ID未配置';
        }
        
        if (self::SECRET_KEY === 'YOUR_SECRET_KEY' || empty(self::SECRET_KEY)) {
            $errors[] = 'SECRET_KEY未配置';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * 检查文件类型是否允许
     */
    public static function isAllowedVideoType($extension) {
        return in_array(strtolower($extension), self::ALLOWED_VIDEO_TYPES);
    }
    
    /**
     * 检查图片类型是否允许
     */
    public static function isAllowedImageType($extension) {
        return in_array(strtolower($extension), self::ALLOWED_IMAGE_TYPES);
    }
    
    /**
     * 检查文件大小是否允许
     */
    public static function isAllowedVideoSize($size) {
        return $size <= self::MAX_VIDEO_SIZE;
    }
    
    /**
     * 检查图片大小是否允许
     */
    public static function isAllowedImageSize($size) {
        return $size <= self::MAX_IMAGE_SIZE;
    }
}

/**
 * 环境配置检查
 */
function checkVodEnvironment() {
    $requirements = [];
    
    // 检查PHP版本
    $requirements['php_version'] = [
        'required' => '7.0.0',
        'current' => PHP_VERSION,
        'status' => version_compare(PHP_VERSION, '7.0.0', '>=')
    ];
    
    // 检查必需的PHP扩展
    $required_extensions = ['curl', 'json', 'openssl'];
    foreach ($required_extensions as $ext) {
        $requirements['extensions'][$ext] = [
            'required' => true,
            'status' => extension_loaded($ext)
        ];
    }
    
    // 检查函数可用性
    $required_functions = ['hash_hmac', 'base64_encode', 'json_encode'];
    foreach ($required_functions as $func) {
        $requirements['functions'][$func] = [
            'required' => true,
            'status' => function_exists($func)
        ];
    }
    
    return $requirements;
}
?>
