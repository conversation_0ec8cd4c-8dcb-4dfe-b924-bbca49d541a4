<?php
/**
 * 检查和更新管理员表结构
 * 确保admins表包含所有必要的字段
 */

require_once '../includes/db.php';

echo "<h2>管理员表结构检查</h2>";

// 检查admins表是否存在
$table_check = $conn->query("SHOW TABLES LIKE 'admins'");
if ($table_check->num_rows == 0) {
    echo "<p style='color: red;'>❌ admins表不存在，正在创建...</p>";
    
    $create_table_sql = "
    CREATE TABLE `admins` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL COMMENT '管理员用户名',
        `password` varchar(255) NOT NULL COMMENT '管理员密码',
        `email` varchar(100) DEFAULT NULL COMMENT '管理员邮箱',
        `name` varchar(100) DEFAULT NULL COMMENT '管理员姓名',
        `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active启用 inactive禁用',
        `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `username` (`username`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表'";
    
    if ($conn->query($create_table_sql)) {
        echo "<p style='color: green;'>✅ admins表创建成功</p>";
        
        // 插入默认管理员
        $default_password = password_hash('123456', PASSWORD_DEFAULT);
        $insert_admin_sql = "INSERT INTO admins (username, password, name, status) VALUES ('admin', '$default_password', '系统管理员', 'active')";
        if ($conn->query($insert_admin_sql)) {
            echo "<p style='color: green;'>✅ 默认管理员账户创建成功 (用户名: admin, 密码: 123456)</p>";
        } else {
            echo "<p style='color: red;'>❌ 默认管理员账户创建失败: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ admins表创建失败: " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color: green;'>✅ admins表存在</p>";
    
    // 检查表结构
    $columns_result = $conn->query("SHOW COLUMNS FROM admins");
    $existing_columns = [];
    while ($row = $columns_result->fetch_assoc()) {
        $existing_columns[] = $row['Field'];
    }
    
    // 需要的字段
    $required_columns = [
        'id' => "int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY",
        'username' => "varchar(50) NOT NULL COMMENT '管理员用户名'",
        'password' => "varchar(255) NOT NULL COMMENT '管理员密码'",
        'email' => "varchar(100) DEFAULT NULL COMMENT '管理员邮箱'",
        'name' => "varchar(100) DEFAULT NULL COMMENT '管理员姓名'",
        'status' => "enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active启用 inactive禁用'",
        'last_login_at' => "timestamp NULL DEFAULT NULL COMMENT '最后登录时间'",
        'created_at' => "timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'",
        'updated_at' => "timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'"
    ];
    
    // 检查缺失的字段
    $missing_columns = [];
    foreach ($required_columns as $column => $definition) {
        if (!in_array($column, $existing_columns)) {
            $missing_columns[$column] = $definition;
        }
    }
    
    if (!empty($missing_columns)) {
        echo "<p style='color: orange;'>⚠️ 发现缺失字段，正在添加...</p>";
        
        foreach ($missing_columns as $column => $definition) {
            $alter_sql = "ALTER TABLE admins ADD COLUMN `$column` $definition";
            if ($conn->query($alter_sql)) {
                echo "<p style='color: green;'>✅ 添加字段 '$column' 成功</p>";
            } else {
                echo "<p style='color: red;'>❌ 添加字段 '$column' 失败: " . $conn->error . "</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✅ 所有必要字段都存在</p>";
    }
    
    // 检查username字段的唯一索引
    $index_result = $conn->query("SHOW INDEX FROM admins WHERE Key_name = 'username'");
    if ($index_result->num_rows == 0) {
        echo "<p style='color: orange;'>⚠️ username字段缺少唯一索引，正在添加...</p>";
        $add_index_sql = "ALTER TABLE admins ADD UNIQUE KEY `username` (`username`)";
        if ($conn->query($add_index_sql)) {
            echo "<p style='color: green;'>✅ username唯一索引添加成功</p>";
        } else {
            echo "<p style='color: red;'>❌ username唯一索引添加失败: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ username唯一索引存在</p>";
    }
}

// 显示当前管理员列表
echo "<h3>当前管理员列表</h3>";
$admins_result = $conn->query("SELECT id, username, email, name, status, last_login_at, created_at FROM admins ORDER BY created_at DESC");

if ($admins_result->num_rows > 0) {
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f5f5f5;'>";
    echo "<th>ID</th><th>用户名</th><th>邮箱</th><th>姓名</th><th>状态</th><th>最后登录</th><th>创建时间</th>";
    echo "</tr>";
    
    while ($row = $admins_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['username']) . "</td>";
        echo "<td>" . ($row['email'] ? htmlspecialchars($row['email']) : '未设置') . "</td>";
        echo "<td>" . ($row['name'] ? htmlspecialchars($row['name']) : '未设置') . "</td>";
        echo "<td>" . ($row['status'] === 'active' ? '启用' : '禁用') . "</td>";
        echo "<td>" . ($row['last_login_at'] ? $row['last_login_at'] : '从未登录') . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>暂无管理员数据</p>";
}

echo "<br><p><a href='admins.php'>返回管理员管理页面</a></p>";
?>
