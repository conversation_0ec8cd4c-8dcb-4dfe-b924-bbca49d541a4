<?php
/**
 * 腾讯云点播转码状态检查和更新API
 * 用于检查视频转码状态并自动更新数据库中的播放URL
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/vod_config.php';
require_once '../includes/db.php';

try {
    // 获取参数
    $file_id = isset($_REQUEST['file_id']) ? trim($_REQUEST['file_id']) : '';
    $lesson_id = isset($_REQUEST['lesson_id']) ? intval($_REQUEST['lesson_id']) : 0;
    $update_db = isset($_REQUEST['update_db']) ? $_REQUEST['update_db'] === '1' : true;
    
    // 验证参数
    if (empty($file_id) && $lesson_id <= 0) {
        echo json_encode([
            'success' => false,
            'message' => '请提供fileId或lessonId参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 如果提供了lesson_id，从数据库获取file_id
    if ($lesson_id > 0 && empty($file_id)) {
        $lesson_query = "SELECT vod_file_id, vod_video_url FROM lessons WHERE id = ?";
        $lesson_stmt = $conn->prepare($lesson_query);
        $lesson_stmt->bind_param("i", $lesson_id);
        $lesson_stmt->execute();
        $lesson_result = $lesson_stmt->get_result();
        $lesson = $lesson_result->fetch_assoc();
        
        if (!$lesson || empty($lesson['vod_file_id'])) {
            echo json_encode([
                'success' => false,
                'message' => '课时不存在或未使用腾讯云点播'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        $file_id = $lesson['vod_file_id'];
        $current_video_url = $lesson['vod_video_url'];
    }
    
    // 验证配置
    $configValidation = VodConfig::validateConfig();
    if (!$configValidation['valid']) {
        echo json_encode([
            'success' => false,
            'message' => '腾讯云点播配置错误: ' . implode(', ', $configValidation['errors'])
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $config = VodConfig::getConfig();
    
    // 构建请求参数
    $params = [
        'Action' => 'DescribeMediaInfos',
        'Version' => '2018-07-17',
        'Region' => $config['upload_region'],
        'FileIds.0' => $file_id,
        'Timestamp' => time(),
        'Nonce' => rand(10000, 99999),
        'SecretId' => $config['secret_id']
    ];
    
    // 如果有子应用ID，添加到参数中
    if ($config['sub_app_id'] > 0) {
        $params['SubAppId'] = $config['sub_app_id'];
    }
    
    // 按键名排序
    ksort($params);
    
    // 构建请求字符串
    $request_string = '';
    foreach ($params as $key => $value) {
        $request_string .= $key . '=' . $value . '&';
    }
    $request_string = rtrim($request_string, '&');
    
    // 构建签名原文字符串
    $sign_str = "GET" . "vod.tencentcloudapi.com" . "/?" . $request_string;
    
    // 生成签名
    $signature = base64_encode(hash_hmac('sha1', $sign_str, $config['secret_key'], true));
    
    // 添加签名到请求参数
    $params['Signature'] = $signature;
    
    // 构建请求URL
    $request_url = 'https://vod.tencentcloudapi.com/?' . http_build_query($params);
    
    // 发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $request_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code != 200) {
        echo json_encode([
            'success' => false,
            'message' => '请求腾讯云API失败，HTTP状态码: ' . $http_code
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (isset($result['Response']['Error'])) {
        echo json_encode([
            'success' => false,
            'message' => '腾讯云API错误: ' . $result['Response']['Error']['Message'],
            'error_code' => $result['Response']['Error']['Code']
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理视频信息
    $media_info = $result['Response']['MediaInfoSet'][0] ?? null;
    
    if (!$media_info) {
        echo json_encode([
            'success' => false,
            'message' => '未找到视频信息'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 提取基本信息
    $basic_info = $media_info['BasicInfo'] ?? [];
    $transcoding_info = $media_info['TranscodeInfo'] ?? [];
    $adaptive_streaming_info = $media_info['AdaptiveDynamicStreamingInfo'] ?? [];
    
    // 查找最佳播放URL
    $best_play_url = '';
    $video_duration = $basic_info['Duration'] ?? 0;
    
    // 优先使用转码后的URL
    if (isset($transcoding_info['TranscodeSet']) && is_array($transcoding_info['TranscodeSet'])) {
        // 按清晰度优先级排序：720p > 480p > 其他
        $streams = $transcoding_info['TranscodeSet'];
        usort($streams, function($a, $b) {
            $height_a = $a['Height'] ?? 0;
            $height_b = $b['Height'] ?? 0;
            
            // 优先级：720p > 480p > 其他
            $priority_a = ($height_a == 720) ? 3 : (($height_a == 480) ? 2 : 1);
            $priority_b = ($height_b == 720) ? 3 : (($height_b == 480) ? 2 : 1);
            
            return $priority_b - $priority_a;
        });
        
        if (!empty($streams)) {
            $best_play_url = $streams[0]['Url'] ?? '';
        }
    }
    
    // 如果没有转码URL，使用自适应流URL
    if (empty($best_play_url) && isset($adaptive_streaming_info['AdaptiveDynamicStreamingSet']) && is_array($adaptive_streaming_info['AdaptiveDynamicStreamingSet'])) {
        $adaptive_streams = $adaptive_streaming_info['AdaptiveDynamicStreamingSet'];
        if (!empty($adaptive_streams)) {
            $best_play_url = $adaptive_streams[0]['Url'] ?? '';
        }
    }
    
    // 如果需要更新数据库且找到了播放URL
    $updated = false;
    if ($update_db && !empty($best_play_url) && $lesson_id > 0) {
        // 检查是否需要更新
        if (empty($current_video_url) || $current_video_url !== $best_play_url) {
            $update_sql = "UPDATE lessons SET vod_video_url = ?, duration = ? WHERE id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("sii", $best_play_url, $video_duration, $lesson_id);
            
            if ($update_stmt->execute()) {
                $updated = true;
                error_log("VOD播放URL已更新 - Lesson ID: {$lesson_id}, URL: {$best_play_url}");
            } else {
                error_log("VOD播放URL更新失败 - Lesson ID: {$lesson_id}, Error: " . $conn->error);
            }
        }
    }
    
    // 格式化返回数据
    $video_data = [
        'file_id' => $basic_info['FileId'] ?? '',
        'name' => $basic_info['Name'] ?? '',
        'duration' => $video_duration,
        'duration_formatted' => formatDuration($video_duration),
        'size' => $basic_info['Size'] ?? 0,
        'size_formatted' => formatSize($basic_info['Size'] ?? 0),
        'status' => $basic_info['Status'] ?? '',
        'status_text' => getStatusText($basic_info['Status'] ?? ''),
        'create_time' => $basic_info['CreateTime'] ?? '',
        'update_time' => $basic_info['UpdateTime'] ?? '',
        'thumbnail_url' => $basic_info['CoverUrl'] ?? '',
        'best_play_url' => $best_play_url,
        'transcoding' => [
            'status' => $transcoding_info['Status'] ?? '',
            'status_text' => getTranscodingStatusText($transcoding_info['Status'] ?? ''),
            'completed' => ($transcoding_info['Status'] ?? '') === 'SUCCESS',
            'streams_count' => count($transcoding_info['TranscodeSet'] ?? [])
        ],
        'updated' => $updated
    ];
    
    // 返回结果
    echo json_encode([
        'success' => true,
        'data' => $video_data,
        'message' => $updated ? '视频信息已更新' : '获取视频信息成功'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '服务器错误',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 格式化时长
 */
function formatDuration($seconds) {
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $secs = $seconds % 60;
    
    if ($hours > 0) {
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $secs);
    } else {
        return sprintf('%02d:%02d', $minutes, $secs);
    }
}

/**
 * 格式化文件大小
 */
function formatSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = 0;
    while ($bytes >= 1024 && $i < count($units) - 1) {
        $bytes /= 1024;
        $i++;
    }
    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * 获取视频状态文本
 */
function getStatusText($status) {
    $status_map = [
        'NORMAL' => '正常',
        'PROCESSING' => '处理中',
        'FAILED' => '处理失败',
        'DELETED' => '已删除'
    ];
    
    return $status_map[$status] ?? $status;
}

/**
 * 获取转码状态文本
 */
function getTranscodingStatusText($status) {
    $status_map = [
        'PROCESSING' => '转码中',
        'SUCCESS' => '转码成功',
        'FAILED' => '转码失败'
    ];
    
    return $status_map[$status] ?? $status;
}
?>
