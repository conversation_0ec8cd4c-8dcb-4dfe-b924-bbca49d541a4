<?php
/**
 * 初始化支付相关数据表
 * 如果表不存在则自动创建
 */

require_once 'db.php';

/**
 * 创建支付相关数据表
 */
function init_payment_tables() {
    global $conn;
    
    try {
        // 检查并创建订单表
        $create_orders_table = "
        CREATE TABLE IF NOT EXISTS `orders` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `order_no` varchar(50) NOT NULL COMMENT '订单号',
            `user_id` int(11) NOT NULL COMMENT '用户ID',
            `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
            `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
            `actual_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实付金额',
            `order_status` enum('pending','paid','cancelled','refunded','expired') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
            `payment_status` enum('unpaid','paid','refunding','refunded','failed') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态',
            `payment_method` varchar(20) DEFAULT NULL COMMENT '支付方式',
            `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
            `expire_time` datetime NOT NULL COMMENT '过期时间',
            `remark` text COMMENT '订单备注',
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `order_no` (`order_no`),
            KEY `user_id` (`user_id`),
            KEY `order_status` (`order_status`),
            KEY `payment_status` (`payment_status`),
            KEY `created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
        ";
        
        $conn->query($create_orders_table);
        
        // 检查并创建订单商品表
        $create_order_items_table = "
        CREATE TABLE IF NOT EXISTS `order_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `order_id` int(11) NOT NULL COMMENT '订单ID',
            `course_id` int(11) NOT NULL COMMENT '课程ID',
            `course_title` varchar(255) NOT NULL COMMENT '课程标题',
            `course_price` decimal(10,2) NOT NULL COMMENT '课程价格',
            `original_price` decimal(10,2) NOT NULL COMMENT '课程原价',
            `quantity` int(11) NOT NULL DEFAULT '1' COMMENT '数量',
            `subtotal` decimal(10,2) NOT NULL COMMENT '小计',
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `order_id` (`order_id`),
            KEY `course_id` (`course_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单商品表';
        ";
        
        $conn->query($create_order_items_table);
        
        // 检查并创建支付记录表
        $create_payments_table = "
        CREATE TABLE IF NOT EXISTS `payments` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `payment_no` varchar(50) NOT NULL COMMENT '支付流水号',
            `order_id` int(11) NOT NULL COMMENT '订单ID',
            `user_id` int(11) NOT NULL COMMENT '用户ID',
            `payment_method` varchar(20) NOT NULL COMMENT '支付方式',
            `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
            `payment_status` enum('pending','success','failed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '支付状态',
            `transaction_id` varchar(100) DEFAULT NULL COMMENT '第三方交易号',
            `prepay_id` varchar(100) DEFAULT NULL COMMENT '预支付ID',
            `callback_data` text COMMENT '回调数据',
            `paid_at` datetime DEFAULT NULL COMMENT '支付完成时间',
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `payment_no` (`payment_no`),
            KEY `order_id` (`order_id`),
            KEY `user_id` (`user_id`),
            KEY `payment_status` (`payment_status`),
            KEY `transaction_id` (`transaction_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付记录表';
        ";
        
        $conn->query($create_payments_table);
        
        // 检查并创建退款记录表
        $create_refunds_table = "
        CREATE TABLE IF NOT EXISTS `refunds` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `refund_no` varchar(50) NOT NULL COMMENT '退款单号',
            `order_id` int(11) NOT NULL COMMENT '订单ID',
            `payment_id` int(11) NOT NULL COMMENT '支付记录ID',
            `user_id` int(11) NOT NULL COMMENT '用户ID',
            `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
            `refund_reason` varchar(500) NOT NULL COMMENT '退款原因',
            `refund_status` enum('pending','processing','success','failed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '退款状态',
            `processed_by` int(11) DEFAULT NULL COMMENT '处理人ID',
            `processed_at` datetime DEFAULT NULL COMMENT '处理时间',
            `refund_transaction_id` varchar(100) DEFAULT NULL COMMENT '退款交易号',
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `refund_no` (`refund_no`),
            KEY `order_id` (`order_id`),
            KEY `payment_id` (`payment_id`),
            KEY `user_id` (`user_id`),
            KEY `refund_status` (`refund_status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款记录表';
        ";
        
        $conn->query($create_refunds_table);
        
        // 添加支付相关的系统设置
        $payment_settings = [
            'wechat_pay_enabled' => '0',
            'wechat_pay_app_id' => 'wxa936b5c9fa9b1893',
            'wechat_pay_mch_id' => '',
            'wechat_pay_api_key' => '',
            'wechat_pay_notify_url' => '',
            'order_expire_minutes' => '30',
            'auto_cancel_unpaid_orders' => '1',
            'refund_enabled' => '1'
        ];
        
        foreach ($payment_settings as $key => $value) {
            $stmt = $conn->prepare("
                INSERT IGNORE INTO settings (setting_key, setting_value, created_at) 
                VALUES (?, ?, NOW())
            ");
            $stmt->bind_param("ss", $key, $value);
            $stmt->execute();
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("初始化支付表失败: " . $e->getMessage());
        return false;
    }
}

// 如果直接访问此文件，则执行初始化
if (basename($_SERVER['PHP_SELF']) === 'init_payment_tables.php') {
    if (init_payment_tables()) {
        echo "支付相关数据表初始化成功！\n";
    } else {
        echo "支付相关数据表初始化失败！\n";
    }
}
?>
