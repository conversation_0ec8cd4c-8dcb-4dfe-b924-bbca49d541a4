<?php
/**
 * 课时列表API
 * 获取指定课程的课时列表
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db.php';
require_once '../includes/lesson_db_setup.php';

// 确保数据库表结构正确
$setup_result = setupLessonTables($conn);
if (!$setup_result['success']) {
    echo json_encode([
        'success' => false,
        'message' => '数据库初始化失败',
        'error' => implode(', ', $setup_result['messages'])
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 获取参数
    $course_id = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    // 验证课程ID
    if ($course_id <= 0) {
        echo json_encode([
            'success' => false,
            'message' => '课程ID无效'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查课程是否存在
    $course_check_sql = "SELECT id, title, status FROM courses WHERE id = ?";
    $course_check_stmt = $conn->prepare($course_check_sql);
    $course_check_stmt->bind_param("i", $course_id);
    $course_check_stmt->execute();
    $course_result = $course_check_stmt->get_result();
    $course = $course_result->fetch_assoc();
    
    if (!$course) {
        echo json_encode([
            'success' => false,
            'message' => '课程不存在'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建查询条件
    $where_conditions = ["course_id = ?"];
    $params = [$course_id];
    $param_types = "i";
    
    // 只显示启用的课时（对于前端用户）
    $show_all = isset($_GET['show_all']) && $_GET['show_all'] === '1';
    if (!$show_all) {
        $where_conditions[] = "status = 1";
    }
    
    // 获取总数
    $count_sql = "SELECT COUNT(*) as total FROM lessons WHERE " . implode(" AND ", $where_conditions);
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param($param_types, ...$params);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $total = $count_result->fetch_assoc()['total'];
    
    // 获取课时列表
    $sql = "SELECT
                id,
                title,
                description,
                video_url,
                vod_file_id,
                vod_video_url,
                video_type,
                thumbnail,
                duration,
                sort_order,
                status,
                is_free,
                created_at,
                updated_at
            FROM lessons
            WHERE " . implode(" AND ", $where_conditions) . "
            ORDER BY sort_order ASC, id ASC
            LIMIT ? OFFSET ?";
    
    $stmt = $conn->prepare($sql);
    $param_types .= "ii";
    $params[] = $limit;
    $params[] = $offset;
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $lessons = [];
    while ($row = $result->fetch_assoc()) {
        // 处理缩略图URL - 确保返回完整的网络地址（修复微信小程序video组件poster属性问题）
        $thumbnail_url = convertToFullUrl($row['thumbnail']);

        // 格式化数据
        $lesson = [
            'id' => intval($row['id']),
            'title' => $row['title'],
            'description' => $row['description'],
            'video_url' => $row['video_url'],
            'vod_file_id' => $row['vod_file_id'],
            'vod_video_url' => $row['vod_video_url'],
            'video_type' => $row['video_type'] ?? 'url',
            'thumbnail' => $thumbnail_url,  // 使用处理后的完整网络地址
            'duration' => intval($row['duration']),
            'duration_formatted' => $row['duration'] ? gmdate("H:i:s", $row['duration']) : null,
            'sort_order' => intval($row['sort_order']),
            'status' => intval($row['status']),
            'is_free' => intval($row['is_free']),
            'created_at' => $row['created_at'],
            'updated_at' => $row['updated_at']
        ];

        $lessons[] = $lesson;
    }
    
    // 计算分页信息
    $total_pages = ceil($total / $limit);
    $has_next = $page < $total_pages;
    $has_prev = $page > 1;
    
    // 返回结果
    echo json_encode([
        'success' => true,
        'data' => [
            'course' => [
                'id' => intval($course['id']),
                'title' => $course['title'],
                'status' => $course['status']
            ],
            'lessons' => $lessons,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $total_pages,
                'total_items' => intval($total),
                'items_per_page' => $limit,
                'has_next' => $has_next,
                'has_prev' => $has_prev
            ],
            'statistics' => [
                'total_lessons' => intval($total),
                'total_duration' => array_sum(array_column($lessons, 'duration')),
                'free_lessons' => count(array_filter($lessons, function($lesson) {
                    return $lesson['is_free'] === 1;
                })),
                'paid_lessons' => count(array_filter($lessons, function($lesson) {
                    return $lesson['is_free'] === 0;
                }))
            ]
        ],
        'message' => '获取课时列表成功'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '服务器错误',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
