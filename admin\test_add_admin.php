<?php
session_start();
require_once '../includes/db.php';

// 模拟登录状态（仅用于测试）
if (!isset($_SESSION['loggedin'])) {
    $_SESSION['loggedin'] = true;
    $_SESSION['admin_id'] = 1;
    $_SESSION['username'] = 'admin';
}

$success_message = '';
$error_message = '';

// Add admin (复制自admins.php的逻辑)
if (isset($_POST['add_admin'])) {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $email = trim($_POST['email']);
    $name = trim($_POST['name']);

    echo "<h3>调试信息：</h3>";
    echo "<p>用户名: " . htmlspecialchars($username) . "</p>";
    echo "<p>密码长度: " . strlen($password) . "</p>";
    echo "<p>邮箱: " . htmlspecialchars($email) . "</p>";
    echo "<p>姓名: " . htmlspecialchars($name) . "</p>";

    if (empty($username) || empty($password) || empty($confirm_password)) {
        $error_message = '请填写所有必填字段';
    } elseif ($password !== $confirm_password) {
        $error_message = '两次输入的密码不一致';
    } elseif (strlen($password) < 6) {
        $error_message = '密码长度至少6位';
    } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = '邮箱格式不正确';
    } else {
        // 检查用户名是否已存在
        $check_stmt = $conn->prepare("SELECT id FROM admins WHERE username = ?");
        if ($check_stmt) {
            $check_stmt->bind_param("s", $username);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                $error_message = '该用户名已存在';
            } else {
                $password_hash = password_hash($password, PASSWORD_DEFAULT);
                
                // 处理空值
                $email = empty($email) ? null : $email;
                $name = empty($name) ? null : $name;
                
                $stmt = $conn->prepare("INSERT INTO admins (username, password, email, name, status) VALUES (?, ?, ?, ?, 'active')");
                if ($stmt) {
                    $stmt->bind_param("ssss", $username, $password_hash, $email, $name);
                    if ($stmt->execute()) {
                        $success_message = '管理员添加成功！ID: ' . $conn->insert_id;
                        // 清空表单数据
                        $_POST = array();
                    } else {
                        $error_message = '添加管理员失败：' . $stmt->error;
                    }
                    $stmt->close();
                } else {
                    $error_message = '数据库准备语句失败：' . $conn->error;
                }
            }
            $check_stmt->close();
        } else {
            $error_message = '数据库查询失败：' . $conn->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试添加管理员功能</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 300px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .debug { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
        th { background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>测试添加管理员功能</h1>
    
    <?php if ($success_message): ?>
        <div class="success"><?php echo $success_message; ?></div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="error"><?php echo $error_message; ?></div>
    <?php endif; ?>
    
    <form method="post">
        <div class="form-group">
            <label for="username">用户名 *</label>
            <input type="text" name="username" id="username" required placeholder="请输入管理员用户名">
        </div>
        
        <div class="form-group">
            <label for="email">邮箱</label>
            <input type="email" name="email" id="email" placeholder="请输入邮箱地址（可选）">
        </div>
        
        <div class="form-group">
            <label for="name">姓名</label>
            <input type="text" name="name" id="name" placeholder="请输入真实姓名（可选）">
        </div>
        
        <div class="form-group">
            <label for="password">密码 *</label>
            <input type="password" name="password" id="password" required placeholder="请输入密码（至少6位）">
        </div>
        
        <div class="form-group">
            <label for="confirm_password">确认密码 *</label>
            <input type="password" name="confirm_password" id="confirm_password" required placeholder="请再次输入密码">
        </div>
        
        <button type="submit" name="add_admin">添加管理员</button>
    </form>
    
    <h2>当前管理员列表</h2>
    <?php
    $result = $conn->query("SELECT id, username, email, name, status, created_at FROM admins ORDER BY created_at DESC");
    if ($result && $result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>用户名</th><th>邮箱</th><th>姓名</th><th>状态</th><th>创建时间</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['username']) . "</td>";
            echo "<td>" . ($row['email'] ? htmlspecialchars($row['email']) : '未设置') . "</td>";
            echo "<td>" . ($row['name'] ? htmlspecialchars($row['name']) : '未设置') . "</td>";
            echo "<td>" . ($row['status'] === 'active' ? '启用' : '禁用') . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>暂无管理员数据</p>";
    }
    ?>
    
    <p><a href="admins.php">返回管理员管理页面</a></p>
    <p><a href="check_admins_db.php">检查数据库表结构</a></p>
</body>
</html>
