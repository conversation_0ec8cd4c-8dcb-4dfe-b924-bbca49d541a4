<?php
session_start();
require_once 'includes/admin_template.php';
require_once '../includes/db.php';

$success_message = '';
$error_message = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $updated_count = 0;

    // 处理logo上传
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
        $upload_result = handleLogoUpload($_FILES['logo'], $conn);
        if ($upload_result['success']) {
            $success_message = $upload_result['message'];
            $updated_count++;
        } else {
            $error_message = $upload_result['message'];
        }
    }

    if (isset($_POST['settings']) && is_array($_POST['settings'])) {
        // 检查是否有用户功能控制相关的设置更新
        $user_features_keys = ['allow_password_change', 'allow_profile_edit', 'password_change_notice'];
        $has_user_features_update = false;

        foreach ($user_features_keys as $key) {
            if (isset($_POST['settings'][$key])) {
                $has_user_features_update = true;
                break;
            }
        }

        foreach ($_POST['settings'] as $key => $value) {
            $value = trim($value);
            $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
            $stmt->bind_param("ss", $key, $value);
            if ($stmt->execute()) {
                $updated_count++;
            }
        }

        // 如果更新了用户功能控制设置，记录更新时间
        if ($has_user_features_update && $updated_count > 0) {
            $current_time = date('Y-m-d H:i:s');
            $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES ('user_features_update_time', ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
            $stmt->bind_param("s", $current_time);
            $stmt->execute();
        }

        if ($updated_count > 0 && empty($error_message)) {
            $success_message = '设置保存成功';
        } elseif ($updated_count === 0 && empty($error_message)) {
            $error_message = '保存设置失败';
        }
    }
}

/**
 * 处理Logo上传
 */
function handleLogoUpload($file, $conn) {
    // 验证文件类型
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    $file_type = strtolower($file['type']);
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if (!in_array($file_type, $allowed_types) && !in_array('image/' . $file_extension, $allowed_types)) {
        return ['success' => false, 'message' => '不支持的文件类型，只允许JPG、PNG、GIF格式'];
    }

    // 验证文件大小（2MB）
    if ($file['size'] > 2 * 1024 * 1024) {
        return ['success' => false, 'message' => '文件大小不能超过2MB'];
    }

    // 验证是否为有效图片
    $image_info = getimagesize($file['tmp_name']);
    if ($image_info === false) {
        return ['success' => false, 'message' => '无效的图片文件'];
    }

    // 创建上传目录
    $upload_dir = $_SERVER['DOCUMENT_ROOT'] . '/uploads/logos/';
    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            return ['success' => false, 'message' => '创建上传目录失败'];
        }
    }

    // 生成安全的文件名
    $filename = 'logo_' . date('YmdHis') . '_' . uniqid() . '.' . $file_extension;
    $file_path = $upload_dir . $filename;
    $url_path = '/uploads/logos/' . $filename;

    // 移动上传文件
    if (!move_uploaded_file($file['tmp_name'], $file_path)) {
        return ['success' => false, 'message' => '保存文件失败'];
    }

    // 删除旧的logo文件
    try {
        $old_logo_stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'app_logo_url'");
        $old_logo_stmt->execute();
        $old_logo_result = $old_logo_stmt->get_result();

        if ($old_logo_result->num_rows > 0) {
            $old_logo = $old_logo_result->fetch_assoc()['setting_value'];
            if (strpos($old_logo, '/uploads/logos/') === 0) {
                $old_file = $_SERVER['DOCUMENT_ROOT'] . $old_logo;
                if (file_exists($old_file)) {
                    unlink($old_file);
                }
            }
        }
    } catch (Exception $e) {
        // 忽略删除旧文件的错误
    }

    // 更新数据库
    $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES ('app_logo_url', ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
    $stmt->bind_param("s", $url_path);

    if (!$stmt->execute()) {
        // 如果数据库更新失败，删除已上传的文件
        unlink($file_path);
        return ['success' => false, 'message' => '更新数据库失败'];
    }

    return ['success' => true, 'message' => 'Logo上传成功'];
}

// 获取所有设置
$result = $conn->query("SELECT * FROM settings ORDER BY setting_key");
$settings = [];
while ($row = $result->fetch_assoc()) {
    $settings[$row['setting_key']] = $row['setting_value'];
}

// 如果没有设置，创建默认设置
if (empty($settings)) {
    $default_settings = [
        'website_title' => '我的网站',
        'website_description' => '网站描述',
        'admin_email' => '<EMAIL>',
        'maintenance_mode' => '0',
        'max_upload_size' => '10',
        'timezone' => 'Asia/Shanghai',
        'admin_panel_title' => '管理中心',
        'admin_panel_subtitle' => 'Admin Panel'
    ];

    foreach ($default_settings as $key => $value) {
        $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        $stmt->bind_param("ss", $key, $value);
        $stmt->execute();
        $settings[$key] = $value;
    }
}

// 渲染页面头部
render_admin_header('系统设置', 'settings');
?>

<!-- 显示消息 -->
<?php if ($success_message): ?>
    <?php show_success_message($success_message); ?>
<?php endif; ?>

<?php if ($error_message): ?>
    <?php show_error_message($error_message); ?>
<?php endif; ?>

<!-- 基本设置 -->
<?php render_card_start('基本设置'); ?>
    <?php render_form_start('', 'post', 'settingsForm'); ?>
        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="website_title">网站标题</label>
                    <input class="admin-form-input" type="text" name="settings[website_title]" id="website_title"
                           value="<?php echo htmlspecialchars($settings['website_title'] ?? ''); ?>"
                           placeholder="请输入网站标题">
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="admin_email">管理员邮箱</label>
                    <input class="admin-form-input" type="email" name="settings[admin_email]" id="admin_email"
                           value="<?php echo htmlspecialchars($settings['admin_email'] ?? ''); ?>"
                           placeholder="请输入管理员邮箱">
                </div>
            </div>
        </div>

        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="admin_panel_title">管理面板主标题</label>
                    <input class="admin-form-input" type="text" name="settings[admin_panel_title]" id="admin_panel_title"
                           value="<?php echo htmlspecialchars($settings['admin_panel_title'] ?? '管理中心'); ?>"
                           placeholder="请输入管理面板主标题">
                    <small style="color: #666;">显示在后台侧边栏顶部的主标题</small>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="admin_panel_subtitle">管理面板副标题</label>
                    <input class="admin-form-input" type="text" name="settings[admin_panel_subtitle]" id="admin_panel_subtitle"
                           value="<?php echo htmlspecialchars($settings['admin_panel_subtitle'] ?? 'Admin Panel'); ?>"
                           placeholder="请输入管理面板副标题">
                    <small style="color: #666;">显示在后台侧边栏顶部的副标题</small>
                </div>
            </div>
        </div>

        <div class="admin-form-group">
            <label class="admin-form-label" for="website_description">网站描述</label>
            <textarea class="admin-form-input" name="settings[website_description]" id="website_description"
                      rows="3" placeholder="请输入网站描述"><?php echo htmlspecialchars($settings['website_description'] ?? ''); ?></textarea>
        </div>

        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="timezone">时区设置</label>
                    <select class="admin-form-input" name="settings[timezone]" id="timezone">
                        <option value="Asia/Shanghai" <?php echo ($settings['timezone'] ?? '') === 'Asia/Shanghai' ? 'selected' : ''; ?>>Asia/Shanghai</option>
                        <option value="UTC" <?php echo ($settings['timezone'] ?? '') === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                        <option value="America/New_York" <?php echo ($settings['timezone'] ?? '') === 'America/New_York' ? 'selected' : ''; ?>>America/New_York</option>
                    </select>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="max_upload_size">最大上传大小 (MB)</label>
                    <input class="admin-form-input" type="number" name="settings[max_upload_size]" id="max_upload_size"
                           value="<?php echo htmlspecialchars($settings['max_upload_size'] ?? '10'); ?>"
                           min="1" max="100">
                </div>
            </div>
        </div>

        <div class="admin-actions">
            <?php render_button('保存设置', 'submit', 'admin-btn-primary'); ?>
            <?php render_button('重置', 'button', 'admin-btn-secondary', 'resetForm()'); ?>
        </div>
    <?php render_form_end(); ?>
<?php render_card_end(); ?>

<!-- 系统外观设置 -->
<?php render_card_start('系统外观设置'); ?>
    <form class="admin-form" method="post" id="appearanceForm" enctype="multipart/form-data">
        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="app_title">应用主标题</label>
                    <input class="admin-form-input" type="text" name="settings[app_title]" id="app_title"
                           value="<?php echo htmlspecialchars($settings['app_title'] ?? '课程学习系统'); ?>"
                           placeholder="请输入应用主标题">
                    <small style="color: #666;">显示在登录页面的主标题</small>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="app_subtitle">应用副标题</label>
                    <input class="admin-form-input" type="text" name="settings[app_subtitle]" id="app_subtitle"
                           value="<?php echo htmlspecialchars($settings['app_subtitle'] ?? '在线学习，随时随地'); ?>"
                           placeholder="请输入应用副标题">
                    <small style="color: #666;">显示在登录页面的副标题</small>
                </div>
            </div>
        </div>

        <div class="admin-form-group">
            <label class="admin-form-label" for="logo_upload">应用Logo</label>
            <div style="display: flex; align-items: flex-start; gap: 20px;">
                <div style="flex: 1;">
                    <input class="admin-form-input" type="file" name="logo" id="logo_upload"
                           accept="image/jpeg,image/jpg,image/png,image/gif">
                    <small style="color: #666;">支持JPG、PNG、GIF格式，建议尺寸80x80像素，最大2MB</small>
                </div>
                <div style="flex-shrink: 0;">
                    <div id="logo_preview" style="width: 80px; height: 80px; border: 1px solid #ddd; border-radius: 4px; overflow: hidden; background: #f5f5f5; display: flex; align-items: center; justify-content: center;">
                        <?php if (!empty($settings['app_logo_url'])): ?>
                            <img src="<?php echo htmlspecialchars($settings['app_logo_url']); ?>"
                                 style="max-width: 100%; max-height: 100%; object-fit: contain;"
                                 alt="当前Logo">
                        <?php else: ?>
                            <span style="color: #999; font-size: 12px;">暂无Logo</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="admin-form-group">
            <label class="admin-form-label">预览效果</label>
            <div id="appearance_preview" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px; border-radius: 8px; text-align: center; color: white;">
                <div id="preview_logo" style="margin-bottom: 20px;">
                    <?php if (!empty($settings['app_logo_url'])): ?>
                        <img src="<?php echo htmlspecialchars($settings['app_logo_url']); ?>"
                             style="width: 60px; height: 60px; object-fit: contain;"
                             alt="Logo预览">
                    <?php else: ?>
                        <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 4px; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 12px;">Logo</div>
                    <?php endif; ?>
                </div>
                <div id="preview_title" style="font-size: 24px; font-weight: bold; margin-bottom: 8px;">
                    <?php echo htmlspecialchars($settings['app_title'] ?? '课程学习系统'); ?>
                </div>
                <div id="preview_subtitle" style="font-size: 14px; opacity: 0.8;">
                    <?php echo htmlspecialchars($settings['app_subtitle'] ?? '在线学习，随时随地'); ?>
                </div>
            </div>
        </div>

        <div class="admin-actions">
            <?php render_button('保存外观设置', 'submit', 'admin-btn-primary'); ?>
            <?php render_button('重置为默认', 'button', 'admin-btn-secondary', 'resetAppearance()'); ?>
        </div>
    </form>
<?php render_card_end(); ?>

<!-- 手机号绑定配置 -->
<?php render_card_start('手机号绑定配置'); ?>
    <?php render_form_start('', 'post', 'phoneBindForm'); ?>
        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="phone_bind_required">是否启用手机号绑定</label>
                    <select class="admin-form-input" name="settings[phone_bind_required]" id="phone_bind_required">
                        <option value="0" <?php echo ($settings['phone_bind_required'] ?? '0') === '0' ? 'selected' : ''; ?>>关闭</option>
                        <option value="1" <?php echo ($settings['phone_bind_required'] ?? '0') === '1' ? 'selected' : ''; ?>>开启</option>
                    </select>
                    <small style="color: #666;">开启后，用户需要绑定手机号才能正常使用</small>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="phone_bind_mode">手机号绑定模式</label>
                    <select class="admin-form-input" name="settings[phone_bind_mode]" id="phone_bind_mode">
                        <option value="direct" <?php echo ($settings['phone_bind_mode'] ?? 'direct') === 'direct' ? 'selected' : ''; ?>>直接绑定</option>
                        <option value="sms" <?php echo ($settings['phone_bind_mode'] ?? 'direct') === 'sms' ? 'selected' : ''; ?>>短信验证</option>
                    </select>
                    <small style="color: #666;">直接绑定：管理员可直接设置用户手机号；短信验证：需要用户输入验证码</small>
                </div>
            </div>
        </div>

        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="phone_bind_force_new_user">新用户强制绑定</label>
                    <select class="admin-form-input" name="settings[phone_bind_force_new_user]" id="phone_bind_force_new_user">
                        <option value="0" <?php echo ($settings['phone_bind_force_new_user'] ?? '0') === '0' ? 'selected' : ''; ?>>否</option>
                        <option value="1" <?php echo ($settings['phone_bind_force_new_user'] ?? '0') === '1' ? 'selected' : ''; ?>>是</option>
                    </select>
                    <small style="color: #666;">新注册用户是否必须绑定手机号</small>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="phone_bind_allow_change">允许用户修改手机号</label>
                    <select class="admin-form-input" name="settings[phone_bind_allow_change]" id="phone_bind_allow_change">
                        <option value="0" <?php echo ($settings['phone_bind_allow_change'] ?? '1') === '0' ? 'selected' : ''; ?>>否</option>
                        <option value="1" <?php echo ($settings['phone_bind_allow_change'] ?? '1') === '1' ? 'selected' : ''; ?>>是</option>
                    </select>
                    <small style="color: #666;">用户是否可以自行修改已绑定的手机号</small>
                </div>
            </div>
        </div>

        <div class="admin-actions">
            <?php render_button('保存设置', 'submit', 'admin-btn-primary'); ?>
        </div>
    <?php render_form_end(); ?>
<?php render_card_end(); ?>

<!-- 微信小程序配置 -->
<?php render_card_start('微信小程序配置'); ?>
    <?php render_form_start('', 'post', 'wechatForm'); ?>
        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="wechat_app_id">微信小程序 AppID</label>
                    <input class="admin-form-input" type="text" name="settings[wechat_app_id]" id="wechat_app_id"
                           value="<?php echo htmlspecialchars($settings['wechat_app_id'] ?? ''); ?>"
                           placeholder="请输入微信小程序AppID">
                    <small style="color: #666;">在微信公众平台获取小程序的AppID</small>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="wechat_app_secret">微信小程序 AppSecret</label>
                    <input class="admin-form-input" type="password" name="settings[wechat_app_secret]" id="wechat_app_secret"
                           value="<?php echo htmlspecialchars($settings['wechat_app_secret'] ?? ''); ?>"
                           placeholder="请输入微信小程序AppSecret">
                    <small style="color: #666;">在微信公众平台获取小程序的AppSecret</small>
                </div>
            </div>
        </div>

        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="jwt_access_token_expire">访问令牌有效期 (秒)</label>
                    <input class="admin-form-input" type="number" name="settings[jwt_access_token_expire]" id="jwt_access_token_expire"
                           value="<?php echo htmlspecialchars($settings['jwt_access_token_expire'] ?? '7200'); ?>"
                           min="300" max="86400">
                    <small style="color: #666;">默认2小时(7200秒)，建议不超过24小时</small>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="jwt_refresh_token_expire">刷新令牌有效期 (秒)</label>
                    <input class="admin-form-input" type="number" name="settings[jwt_refresh_token_expire]" id="jwt_refresh_token_expire"
                           value="<?php echo htmlspecialchars($settings['jwt_refresh_token_expire'] ?? '604800'); ?>"
                           min="3600" max="2592000">
                    <small style="color: #666;">默认7天(604800秒)，建议不超过30天</small>
                </div>
            </div>
        </div>



        <div class="admin-actions">
            <?php render_button('保存微信配置', 'submit', 'admin-btn-primary'); ?>
            <?php render_button('测试微信连接', 'button', 'admin-btn-info', 'testWechatConnection()'); ?>
        </div>
    <?php render_form_end(); ?>
<?php render_card_end(); ?>

<!-- 支付配置 -->
<?php render_card_start('支付配置'); ?>
    <?php render_form_start('', 'post', 'paymentForm'); ?>
        <div class="admin-form-group">
            <label class="admin-form-label">
                <input type="checkbox" name="settings[wechat_pay_enabled]" value="1"
                       <?php echo ($settings['wechat_pay_enabled'] ?? '0') === '1' ? 'checked' : ''; ?>>
                启用微信支付
            </label>
            <small style="color: #666;">开启后用户可以使用微信支付购买课程</small>
        </div>

        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="wechat_pay_app_id">微信小程序AppID</label>
                    <input class="admin-form-input" type="text" name="settings[wechat_pay_app_id]" id="wechat_pay_app_id"
                           value="<?php echo htmlspecialchars($settings['wechat_pay_app_id'] ?? ''); ?>"
                           placeholder="请输入微信小程序AppID">
                    <small style="color: #666;">用于微信支付的小程序AppID</small>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="wechat_pay_mch_id">微信支付商户号</label>
                    <input class="admin-form-input" type="text" name="settings[wechat_pay_mch_id]" id="wechat_pay_mch_id"
                           value="<?php echo htmlspecialchars($settings['wechat_pay_mch_id'] ?? ''); ?>"
                           placeholder="请输入微信支付商户号">
                    <small style="color: #666;">在微信支付商户平台获取</small>
                </div>
            </div>
        </div>

        <div class="admin-form-group">
            <label class="admin-form-label" for="wechat_pay_api_key">微信支付API密钥</label>
            <input class="admin-form-input" type="password" name="settings[wechat_pay_api_key]" id="wechat_pay_api_key"
                   value="<?php echo htmlspecialchars($settings['wechat_pay_api_key'] ?? ''); ?>"
                   placeholder="请输入微信支付API密钥">
            <small style="color: #666;">在微信支付商户平台设置的API密钥，32位字符</small>
        </div>

        <div class="admin-form-group">
            <label class="admin-form-label" for="wechat_pay_notify_url">支付回调地址</label>
            <input class="admin-form-input" type="url" name="settings[wechat_pay_notify_url]" id="wechat_pay_notify_url"
                   value="<?php echo htmlspecialchars($settings['wechat_pay_notify_url'] ?? ''); ?>"
                   placeholder="https://your-domain.com/api/payment-wechat-notify.php">
            <small style="color: #666;">微信支付成功后的回调地址，必须是HTTPS</small>
        </div>

        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="order_expire_minutes">订单过期时间 (分钟)</label>
                    <input class="admin-form-input" type="number" name="settings[order_expire_minutes]" id="order_expire_minutes"
                           value="<?php echo htmlspecialchars($settings['order_expire_minutes'] ?? '30'); ?>"
                           min="5" max="1440">
                    <small style="color: #666;">订单创建后多长时间自动过期，默认30分钟</small>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label">
                        <input type="checkbox" name="settings[auto_cancel_unpaid_orders]" value="1"
                               <?php echo ($settings['auto_cancel_unpaid_orders'] ?? '1') === '1' ? 'checked' : ''; ?>>
                        自动取消未支付订单
                    </label>
                    <small style="color: #666;">过期后自动将未支付订单标记为已取消</small>
                </div>
            </div>
        </div>

        <div class="admin-form-group">
            <label class="admin-form-label">
                <input type="checkbox" name="settings[refund_enabled]" value="1"
                       <?php echo ($settings['refund_enabled'] ?? '1') === '1' ? 'checked' : ''; ?>>
                启用退款功能
            </label>
            <small style="color: #666;">允许管理员处理用户退款申请</small>
        </div>

        <div class="admin-actions">
            <?php render_button('保存支付配置', 'submit', 'admin-btn-primary'); ?>
            <?php render_button('测试微信支付', 'button', 'admin-btn-info', 'testWechatPayment()'); ?>
        </div>
    <?php render_form_end(); ?>
<?php render_card_end(); ?>

<!-- 码支付易支付配置 -->
<?php render_card_start('码支付易支付配置'); ?>
    <?php render_form_start('', 'post', 'epayForm'); ?>
        <div class="admin-form-group">
            <label class="admin-form-label">
                <input type="checkbox" name="settings[epay_enabled]" value="1"
                       <?php echo ($settings['epay_enabled'] ?? '0') === '1' ? 'checked' : ''; ?>>
                启用码支付易支付
            </label>
            <small style="color: #666;">开启后用户可以使用支付宝、QQ钱包等方式支付</small>
        </div>

        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="epay_api_url">码支付API地址</label>
                    <input class="admin-form-input" type="url" name="settings[epay_api_url]" id="epay_api_url"
                           value="<?php echo htmlspecialchars($settings['epay_api_url'] ?? ''); ?>"
                           placeholder="https://api.example.com">
                    <small style="color: #666;">码支付易支付的API接口地址</small>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="epay_partner_id">商户PID</label>
                    <input class="admin-form-input" type="text" name="settings[epay_partner_id]" id="epay_partner_id"
                           value="<?php echo htmlspecialchars($settings['epay_partner_id'] ?? ''); ?>"
                           placeholder="请输入商户PID">
                    <small style="color: #666;">在码支付易支付商户后台获取</small>
                </div>
            </div>
        </div>

        <div class="admin-form-group">
            <label class="admin-form-label" for="epay_partner_key">商户密钥</label>
            <input class="admin-form-input" type="password" name="settings[epay_partner_key]" id="epay_partner_key"
                   value="<?php echo htmlspecialchars($settings['epay_partner_key'] ?? ''); ?>"
                   placeholder="请输入商户密钥">
            <small style="color: #666;">在码支付易支付商户后台设置的密钥，请妥善保管</small>
        </div>

        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="epay_notify_url">异步回调地址</label>
                    <input class="admin-form-input" type="url" name="settings[epay_notify_url]" id="epay_notify_url"
                           value="<?php echo htmlspecialchars($settings['epay_notify_url'] ?? 'https://wx.yx420.cn/api/payment-epay-notify.php'); ?>"
                           placeholder="http://your-domain.com/api/payment-epay-notify.php">
                    <small style="color: #666;">支付成功后的异步通知地址</small>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="epay_return_url">同步返回地址</label>
                    <input class="admin-form-input" type="url" name="settings[epay_return_url]" id="epay_return_url"
                           value="<?php echo htmlspecialchars($settings['epay_return_url'] ?? 'https://wx.yx420.cn/api/payment-epay-return.php'); ?>"
                           placeholder="http://your-domain.com/api/payment-epay-return.php">
                    <small style="color: #666;">支付完成后的页面跳转地址</small>
                </div>
            </div>
        </div>

        <div class="admin-form-group">
            <label class="admin-form-label" for="epay_supported_methods">支持的支付方式</label>
            <div style="display: flex; gap: 20px; margin-top: 10px;">
                <label style="display: flex; align-items: center; gap: 5px;">
                    <input type="checkbox" name="epay_methods[]" value="alipay"
                           <?php echo strpos($settings['epay_supported_methods'] ?? 'alipay,wxpay,qqpay', 'alipay') !== false ? 'checked' : ''; ?>>
                    支付宝
                </label>
                <label style="display: flex; align-items: center; gap: 5px;">
                    <input type="checkbox" name="epay_methods[]" value="wxpay"
                           <?php echo strpos($settings['epay_supported_methods'] ?? 'alipay,wxpay,qqpay', 'wxpay') !== false ? 'checked' : ''; ?>>
                    微信扫码
                </label>
                <label style="display: flex; align-items: center; gap: 5px;">
                    <input type="checkbox" name="epay_methods[]" value="qqpay"
                           <?php echo strpos($settings['epay_supported_methods'] ?? 'alipay,wxpay,qqpay', 'qqpay') !== false ? 'checked' : ''; ?>>
                    QQ钱包
                </label>
            </div>
            <small style="color: #666;">选择要启用的支付方式</small>
        </div>

        <div class="admin-actions">
            <?php render_button('保存码支付配置', 'submit', 'admin-btn-primary'); ?>
            <?php render_button('测试码支付连接', 'button', 'admin-btn-info', 'testEpayConnection()'); ?>
            <?php render_button('初始化数据库', 'button', 'admin-btn-warning', 'initEpayDatabase()'); ?>
        </div>
    <?php render_form_end(); ?>
<?php render_card_end(); ?>

<!-- 用户功能控制 -->
<?php render_card_start('用户功能控制'); ?>
    <?php render_form_start('', 'post', 'userFeaturesForm'); ?>
        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="allow_password_change">允许用户修改密码</label>
                    <select class="admin-form-input" name="settings[allow_password_change]" id="allow_password_change">
                        <option value="1" <?php echo ($settings['allow_password_change'] ?? '1') === '1' ? 'selected' : ''; ?>>允许</option>
                        <option value="0" <?php echo ($settings['allow_password_change'] ?? '1') === '0' ? 'selected' : ''; ?>>禁止</option>
                    </select>
                    <small style="color: #666;">控制小程序中是否显示"修改密码"功能选项</small>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="allow_profile_edit">允许用户编辑个人信息</label>
                    <select class="admin-form-input" name="settings[allow_profile_edit]" id="allow_profile_edit">
                        <option value="1" <?php echo ($settings['allow_profile_edit'] ?? '1') === '1' ? 'selected' : ''; ?>>允许</option>
                        <option value="0" <?php echo ($settings['allow_profile_edit'] ?? '1') === '0' ? 'selected' : ''; ?>>禁止</option>
                    </select>
                    <small style="color: #666;">控制小程序中是否显示"个人信息"编辑功能</small>
                </div>
            </div>
        </div>

        <div class="admin-form-row">
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="password_change_notice">修改密码功能说明</label>
                    <textarea class="admin-form-input" name="settings[password_change_notice]" id="password_change_notice"
                              rows="3" placeholder="当禁止修改密码时显示给用户的说明文字"><?php echo htmlspecialchars($settings['password_change_notice'] ?? '管理员已禁用密码修改功能，如需修改密码请联系客服。'); ?></textarea>
                    <small style="color: #666;">当禁止修改密码时，在小程序中显示的提示信息</small>
                </div>
            </div>
            <div class="admin-form-col">
                <div class="admin-form-group">
                    <label class="admin-form-label" for="user_features_update_time">功能控制更新时间</label>
                    <input class="admin-form-input" type="text" readonly
                           value="<?php echo $settings['user_features_update_time'] ?? '未设置'; ?>">
                    <small style="color: #666;">最后一次修改用户功能控制设置的时间</small>
                </div>
            </div>
        </div>

        <div class="admin-actions">
            <?php render_button('保存用户功能设置', 'submit', 'admin-btn-primary'); ?>
        </div>
    <?php render_form_end(); ?>
<?php render_card_end(); ?>

<!-- 系统维护 -->
<?php render_card_start('系统维护'); ?>
    <div class="admin-form-group">
        <label class="admin-form-label">
            <input type="checkbox" name="settings[maintenance_mode]" value="1"
                   <?php echo ($settings['maintenance_mode'] ?? '0') === '1' ? 'checked' : ''; ?>
                   onchange="toggleMaintenance(this)">
            维护模式
        </label>
        <p style="color: #666; font-size: 14px; margin-top: 5px;">
            启用维护模式后，普通用户将无法访问网站，只有管理员可以正常使用
        </p>
    </div>

    <div style="margin-top: 20px;">
        <?php render_button('清理缓存', 'button', 'admin-btn-warning', 'clearCache()'); ?>
        <?php render_button('备份数据库', 'button', 'admin-btn-info', 'backupDatabase()'); ?>
    </div>
<?php render_card_end(); ?>

<!-- 系统信息 -->
<?php render_card_start('系统信息'); ?>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
        <div>
            <h4 style="margin: 0 0 10px 0; color: #2c3e50;">服务器信息</h4>
            <p><strong>PHP版本:</strong> <?php echo PHP_VERSION; ?></p>
            <p><strong>服务器软件:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
            <p><strong>操作系统:</strong> <?php echo PHP_OS; ?></p>
        </div>
        <div>
            <h4 style="margin: 0 0 10px 0; color: #2c3e50;">数据库信息</h4>
            <p><strong>MySQL版本:</strong> <?php echo $conn->server_info; ?></p>
            <p><strong>数据库名:</strong> <?php echo $conn->get_server_info(); ?></p>
        </div>
    </div>
<?php render_card_end(); ?>

<script>
function resetForm() {
    if (confirm('确定要重置所有设置吗？')) {
        document.getElementById('settingsForm').reset();
    }
}

function toggleMaintenance(checkbox) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';

    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'settings[maintenance_mode]';
    input.value = checkbox.checked ? '1' : '0';

    form.appendChild(input);
    document.body.appendChild(form);
    form.submit();
}

function clearCache() {
    if (confirm('确定要清理缓存吗？')) {
        alert('缓存清理功能待实现');
    }
}

function backupDatabase() {
    if (confirm('确定要备份数据库吗？')) {
        alert('数据库备份功能待实现');
    }
}

function testWechatConnection() {
    const appId = document.getElementById('wechat_app_id').value;
    const appSecret = document.getElementById('wechat_app_secret').value;

    if (!appId || !appSecret) {
        alert('请先填写微信小程序的AppID和AppSecret');
        return;
    }

    // 这里可以添加测试微信API连接的逻辑
    alert('微信连接测试功能开发中...\n\n请确保：\n1. AppID和AppSecret正确\n2. 服务器可以访问微信API\n3. 小程序已发布或在开发者工具中测试');
}

function testWechatPayment() {
    const appId = document.getElementById('wechat_pay_app_id').value;
    const mchId = document.getElementById('wechat_pay_mch_id').value;
    const apiKey = document.getElementById('wechat_pay_api_key').value;
    const notifyUrl = document.getElementById('wechat_pay_notify_url').value;

    if (!appId || !mchId || !apiKey) {
        alert('请先填写微信支付的基本配置信息');
        return;
    }

    if (!notifyUrl) {
        alert('请填写支付回调地址');
        return;
    }

    if (!notifyUrl.startsWith('https://')) {
        alert('支付回调地址必须使用HTTPS协议');
        return;
    }

    alert('微信支付配置检查通过！\n\n请确保：\n1. 商户号已开通并审核通过\n2. API密钥设置正确\n3. 回调地址可以正常访问\n4. 服务器支持HTTPS\n\n建议先在测试环境进行小额支付测试。');
}

function testEpayConnection() {
    const apiUrl = document.getElementById('epay_api_url').value;
    const partnerId = document.getElementById('epay_partner_id').value;
    const partnerKey = document.getElementById('epay_partner_key').value;

    if (!apiUrl || !partnerId || !partnerKey) {
        alert('请先填写码支付易支付的基本配置信息');
        return;
    }

    if (!apiUrl.startsWith('http://') && !apiUrl.startsWith('https://')) {
        alert('API地址格式不正确，请以http://或https://开头');
        return;
    }

    // 发送测试请求
    fetch('../api/test-epay-connection.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            api_url: apiUrl,
            partner_id: partnerId,
            partner_key: partnerKey
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('码支付连接测试成功！\n\n' + data.message);
        } else {
            alert('码支付连接测试失败：\n\n' + data.message);
        }
    })
    .catch(error => {
        alert('测试请求失败：' + error.message);
    });
}

function initEpayDatabase() {
    if (!confirm('确定要初始化码支付数据库配置吗？\n\n这将创建必要的数据库表和配置项。')) {
        return;
    }

    window.open('../api/setup_epay_payment.php', '_blank');
}

// 处理支付方式复选框
document.addEventListener('DOMContentLoaded', function() {
    const epayForm = document.getElementById('epayForm');
    if (epayForm) {
        epayForm.addEventListener('submit', function(e) {
            // 收集选中的支付方式
            const checkedMethods = [];
            const methodCheckboxes = document.querySelectorAll('input[name="epay_methods[]"]:checked');
            methodCheckboxes.forEach(checkbox => {
                checkedMethods.push(checkbox.value);
            });

            // 创建隐藏字段存储支付方式
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'settings[epay_supported_methods]';
            hiddenInput.value = checkedMethods.join(',');
            epayForm.appendChild(hiddenInput);
        });
    }

    // 外观设置相关功能
    initAppearanceSettings();
});

// 初始化外观设置功能
function initAppearanceSettings() {
    // 监听标题输入变化
    const titleInput = document.getElementById('app_title');
    const subtitleInput = document.getElementById('app_subtitle');
    const logoUpload = document.getElementById('logo_upload');

    if (titleInput) {
        titleInput.addEventListener('input', updatePreview);
    }
    if (subtitleInput) {
        subtitleInput.addEventListener('input', updatePreview);
    }
    if (logoUpload) {
        logoUpload.addEventListener('change', handleLogoUpload);
    }
}

// 更新预览
function updatePreview() {
    const title = document.getElementById('app_title').value || '课程学习系统';
    const subtitle = document.getElementById('app_subtitle').value || '在线学习，随时随地';

    document.getElementById('preview_title').textContent = title;
    document.getElementById('preview_subtitle').textContent = subtitle;
}

// 处理Logo上传预览
function handleLogoUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
        alert('只支持JPG、PNG、GIF格式的图片');
        event.target.value = '';
        return;
    }

    // 验证文件大小（2MB）
    if (file.size > 2 * 1024 * 1024) {
        alert('文件大小不能超过2MB');
        event.target.value = '';
        return;
    }

    // 预览图片
    const reader = new FileReader();
    reader.onload = function(e) {
        const previewImg = '<img src="' + e.target.result + '" style="max-width: 100%; max-height: 100%; object-fit: contain;" alt="Logo预览">';
        document.getElementById('logo_preview').innerHTML = previewImg;

        // 更新预览区域的logo
        const previewLogo = '<img src="' + e.target.result + '" style="width: 60px; height: 60px; object-fit: contain;" alt="Logo预览">';
        document.getElementById('preview_logo').innerHTML = previewLogo;
    };
    reader.readAsDataURL(file);
}

// 重置外观设置
function resetAppearance() {
    if (!confirm('确定要重置为默认外观设置吗？')) {
        return;
    }

    // 重置表单值
    document.getElementById('app_title').value = '课程学习系统';
    document.getElementById('app_subtitle').value = '在线学习，随时随地';
    document.getElementById('logo_upload').value = '';

    // 重置预览
    document.getElementById('preview_title').textContent = '课程学习系统';
    document.getElementById('preview_subtitle').textContent = '在线学习，随时随地';

    // 重置logo预览
    const defaultLogo = '<div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 4px; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 12px;">Logo</div>';
    document.getElementById('preview_logo').innerHTML = defaultLogo;
    document.getElementById('logo_preview').innerHTML = '<span style="color: #999; font-size: 12px;">暂无Logo</span>';
}
</script>

<?php render_admin_footer(); ?>
