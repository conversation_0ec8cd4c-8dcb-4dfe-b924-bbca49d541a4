<?php
/**
 * 码支付易支付同步返回处理
 */

header('Content-Type: text/html; charset=utf-8');

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);

require_once '../includes/db.php';

// 记录返回日志
function log_return($message, $data = null) {
    $log_file = '../logs/epay_return_' . date('Y-m-d') . '.log';
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $log_content = date('Y-m-d H:i:s') . ' - ' . $message;
    if ($data) {
        $log_content .= ' - Data: ' . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    $log_content .= "\n";
    
    file_put_contents($log_file, $log_content, FILE_APPEND | LOCK_EX);
}

try {
    // 记录原始返回数据
    $return_data = $_GET;
    log_return('收到码支付易支付同步返回', $return_data);
    
    // 验证必需参数
    $required_params = ['pid', 'trade_no', 'out_trade_no', 'type', 'name', 'money', 'trade_status', 'sign'];
    $missing_params = [];
    
    foreach ($required_params as $param) {
        if (!isset($return_data[$param]) || $return_data[$param] === '') {
            $missing_params[] = $param;
        }
    }
    
    if (!empty($missing_params)) {
        log_return('缺少必需参数', $missing_params);
        show_result('参数错误', '支付返回参数不完整，请联系客服处理。', 'error');
        exit;
    }
    
    $pid = $return_data['pid'];
    $trade_no = $return_data['trade_no']; // 第三方交易号
    $out_trade_no = $return_data['out_trade_no']; // 我们的支付流水号
    $type = $return_data['type'];
    $name = $return_data['name'];
    $money = $return_data['money'];
    $trade_status = $return_data['trade_status'];
    $sign = $return_data['sign'];
    
    // 获取码支付易支付配置
    $stmt = $conn->prepare("
        SELECT setting_key, setting_value 
        FROM settings 
        WHERE setting_key IN ('epay_partner_id', 'epay_partner_key')
    ");
    $stmt->execute();
    $settings_result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    $settings = [];
    foreach ($settings_result as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
    
    // 验证商户ID
    if ($pid !== $settings['epay_partner_id']) {
        log_return('商户ID不匹配', ['received' => $pid, 'expected' => $settings['epay_partner_id']]);
        show_result('验证失败', '商户信息验证失败，请联系客服处理。', 'error');
        exit;
    }
    
    // 验证签名
    $verify_sign = generate_epay_sign($return_data, $settings['epay_partner_key']);
    if ($sign !== $verify_sign) {
        log_return('签名验证失败', ['received' => $sign, 'calculated' => $verify_sign]);
        show_result('验证失败', '签名验证失败，请联系客服处理。', 'error');
        exit;
    }
    
    // 查找支付记录
    $stmt = $conn->prepare("
        SELECT p.*, o.order_no, o.user_id as order_user_id 
        FROM payments p 
        LEFT JOIN orders o ON p.order_id = o.id 
        WHERE p.payment_no = ?
    ");
    $stmt->bind_param("s", $out_trade_no);
    $stmt->execute();
    $payment = $stmt->get_result()->fetch_assoc();
    
    if (!$payment) {
        log_return('支付记录不存在', ['payment_no' => $out_trade_no]);
        show_result('订单不存在', '未找到相关支付记录，请联系客服处理。', 'error');
        exit;
    }
    
    // 验证金额
    if (abs(floatval($money) - floatval($payment['amount'])) > 0.01) {
        log_return('金额不匹配', [
            'return_amount' => $money,
            'order_amount' => $payment['amount']
        ]);
        show_result('金额异常', '支付金额与订单金额不匹配，请联系客服处理。', 'error');
        exit;
    }
    
    // 处理支付结果
    if ($trade_status === 'TRADE_SUCCESS') {
        log_return('支付成功返回', [
            'payment_no' => $out_trade_no,
            'trade_no' => $trade_no,
            'amount' => $money
        ]);
        
        show_result('支付成功', '恭喜您，支付已完成！正在跳转到课程页面...', 'success', $payment['order_id']);
        
    } else {
        log_return('支付状态异常', ['trade_status' => $trade_status]);
        show_result('支付失败', '支付未完成，请重新尝试或联系客服。', 'warning');
    }
    
} catch (Exception $e) {
    log_return('返回处理异常: ' . $e->getMessage());
    show_result('系统错误', '处理支付返回时发生错误，请联系客服处理。', 'error');
}

/**
 * 显示支付结果页面
 */
function show_result($title, $message, $type = 'info', $order_id = null) {
    $icon = '';
    $color = '';
    $redirect_url = '';
    
    switch ($type) {
        case 'success':
            $icon = '✅';
            $color = '#4CAF50';
            $redirect_url = $order_id ? "/wx/pages/orders/detail?id={$order_id}" : "/wx/pages/orders/list";
            break;
        case 'error':
            $icon = '❌';
            $color = '#F44336';
            $redirect_url = "/wx/pages/orders/list";
            break;
        case 'warning':
            $icon = '⚠️';
            $color = '#FF9800';
            $redirect_url = "/wx/pages/orders/list";
            break;
        default:
            $icon = 'ℹ️';
            $color = '#2196F3';
            $redirect_url = "/wx/pages/index/index";
    }
    
    echo "<!DOCTYPE html>";
    echo "<html><head>";
    echo "<meta charset='utf-8'>";
    echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
    echo "<title>{$title}</title>";
    echo "<style>";
    echo "body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }";
    echo ".container { max-width: 400px; margin: 50px auto; background: white; border-radius: 10px; padding: 30px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
    echo ".icon { font-size: 48px; margin-bottom: 20px; }";
    echo ".title { font-size: 24px; font-weight: bold; color: {$color}; margin-bottom: 15px; }";
    echo ".message { font-size: 16px; color: #666; margin-bottom: 30px; line-height: 1.5; }";
    echo ".btn { display: inline-block; padding: 12px 24px; background: {$color}; color: white; text-decoration: none; border-radius: 5px; font-size: 16px; }";
    echo ".btn:hover { opacity: 0.8; }";
    echo "</style>";
    echo "</head><body>";
    
    echo "<div class='container'>";
    echo "<div class='icon'>{$icon}</div>";
    echo "<div class='title'>{$title}</div>";
    echo "<div class='message'>{$message}</div>";
    
    if ($type === 'success') {
        echo "<a href='#' class='btn' onclick='redirectToApp()'>返回应用</a>";
        echo "<script>";
        echo "function redirectToApp() {";
        echo "  if (window.wx && window.wx.miniProgram) {";
        echo "    wx.miniProgram.navigateTo({ url: '{$redirect_url}' });";
        echo "  } else {";
        echo "    window.location.href = '{$redirect_url}';";
        echo "  }";
        echo "}";
        echo "setTimeout(redirectToApp, 3000);";
        echo "</script>";
    } else {
        echo "<a href='#' class='btn' onclick='goBack()'>返回</a>";
        echo "<script>";
        echo "function goBack() {";
        echo "  if (window.wx && window.wx.miniProgram) {";
        echo "    wx.miniProgram.navigateBack();";
        echo "  } else {";
        echo "    history.back();";
        echo "  }";
        echo "}";
        echo "</script>";
    }
    
    echo "</div>";
    echo "</body></html>";
}

/**
 * 生成码支付易支付签名
 */
function generate_epay_sign($params, $key) {
    // 过滤空值和签名参数
    $filtered_params = [];
    foreach ($params as $k => $v) {
        if ($k !== 'sign' && $k !== 'sign_type' && $v !== '' && $v !== null) {
            $filtered_params[$k] = $v;
        }
    }
    
    // 按键名排序
    ksort($filtered_params);
    
    // 构建签名字符串
    $sign_string = '';
    foreach ($filtered_params as $k => $v) {
        $sign_string .= $k . '=' . $v . '&';
    }
    $sign_string .= 'key=' . $key;
    
    return md5($sign_string);
}
?>
