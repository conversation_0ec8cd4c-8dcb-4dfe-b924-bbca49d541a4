<?php
/**
 * 检查管理员表结构并修复
 */
require_once '../includes/db.php';

echo "<h2>管理员表检查和修复</h2>";

// 检查admins表是否存在
$table_check = $conn->query("SHOW TABLES LIKE 'admins'");
if ($table_check->num_rows == 0) {
    echo "<p style='color: red;'>❌ admins表不存在，正在创建...</p>";
    
    $create_table_sql = "
    CREATE TABLE `admins` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL COMMENT '管理员用户名',
        `password` varchar(255) NOT NULL COMMENT '管理员密码',
        `email` varchar(100) DEFAULT NULL COMMENT '管理员邮箱',
        `name` varchar(100) DEFAULT NULL COMMENT '管理员姓名',
        `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active启用 inactive禁用',
        `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `username` (`username`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表'";
    
    if ($conn->query($create_table_sql)) {
        echo "<p style='color: green;'>✅ admins表创建成功</p>";
        
        // 插入默认管理员
        $default_password = password_hash('123456', PASSWORD_DEFAULT);
        $insert_admin_sql = "INSERT INTO admins (username, password, name, status) VALUES ('admin', ?, '系统管理员', 'active')";
        $stmt = $conn->prepare($insert_admin_sql);
        $stmt->bind_param("s", $default_password);
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ 默认管理员账户创建成功 (用户名: admin, 密码: 123456)</p>";
        } else {
            echo "<p style='color: red;'>❌ 默认管理员账户创建失败: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ admins表创建失败: " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color: green;'>✅ admins表存在</p>";
    
    // 检查表结构
    $columns_result = $conn->query("SHOW COLUMNS FROM admins");
    $existing_columns = [];
    while ($row = $columns_result->fetch_assoc()) {
        $existing_columns[$row['Field']] = $row;
    }
    
    // 需要添加的字段
    $required_fields = [
        'email' => "VARCHAR(100) DEFAULT NULL COMMENT '管理员邮箱'",
        'name' => "VARCHAR(100) DEFAULT NULL COMMENT '管理员姓名'",
        'status' => "ENUM('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active启用 inactive禁用'",
        'last_login_at' => "TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间'",
        'updated_at' => "TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'"
    ];
    
    foreach ($required_fields as $field => $definition) {
        if (!isset($existing_columns[$field])) {
            echo "<p style='color: orange;'>⚠️ 缺少字段 '$field'，正在添加...</p>";
            $alter_sql = "ALTER TABLE admins ADD COLUMN `$field` $definition";
            if ($conn->query($alter_sql)) {
                echo "<p style='color: green;'>✅ 字段 '$field' 添加成功</p>";
            } else {
                echo "<p style='color: red;'>❌ 字段 '$field' 添加失败: " . $conn->error . "</p>";
            }
        } else {
            echo "<p style='color: green;'>✅ 字段 '$field' 存在</p>";
        }
    }
    
    // 检查username唯一索引
    $index_result = $conn->query("SHOW INDEX FROM admins WHERE Key_name = 'username'");
    if ($index_result->num_rows == 0) {
        echo "<p style='color: orange;'>⚠️ username字段缺少唯一索引，正在添加...</p>";
        $add_index_sql = "ALTER TABLE admins ADD UNIQUE KEY `username` (`username`)";
        if ($conn->query($add_index_sql)) {
            echo "<p style='color: green;'>✅ username唯一索引添加成功</p>";
        } else {
            echo "<p style='color: red;'>❌ username唯一索引添加失败: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ username唯一索引存在</p>";
    }
}

// 测试添加管理员功能
echo "<h3>测试添加管理员功能</h3>";
echo "<form method='post' style='border: 1px solid #ccc; padding: 20px; margin: 20px 0;'>";
echo "<h4>添加测试管理员</h4>";
echo "<p><label>用户名: <input type='text' name='test_username' value='test_admin' required></label></p>";
echo "<p><label>密码: <input type='password' name='test_password' value='123456' required></label></p>";
echo "<p><label>邮箱: <input type='email' name='test_email' value='<EMAIL>'></label></p>";
echo "<p><label>姓名: <input type='text' name='test_name' value='测试管理员'></label></p>";
echo "<p><button type='submit' name='test_add_admin'>添加测试管理员</button></p>";
echo "</form>";

if (isset($_POST['test_add_admin'])) {
    $test_username = trim($_POST['test_username']);
    $test_password = $_POST['test_password'];
    $test_email = trim($_POST['test_email']);
    $test_name = trim($_POST['test_name']);
    
    if (!empty($test_username) && !empty($test_password)) {
        // 检查用户名是否已存在
        $check_stmt = $conn->prepare("SELECT id FROM admins WHERE username = ?");
        $check_stmt->bind_param("s", $test_username);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            echo "<p style='color: red;'>❌ 用户名 '$test_username' 已存在</p>";
        } else {
            $password_hash = password_hash($test_password, PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO admins (username, password, email, name, status) VALUES (?, ?, ?, ?, 'active')");
            $stmt->bind_param("ssss", $test_username, $password_hash, $test_email, $test_name);
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✅ 测试管理员添加成功！</p>";
            } else {
                echo "<p style='color: red;'>❌ 测试管理员添加失败: " . $conn->error . "</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>❌ 请填写用户名和密码</p>";
    }
}

// 显示当前管理员列表
echo "<h3>当前管理员列表</h3>";
$admins_result = $conn->query("SELECT id, username, email, name, status, created_at FROM admins ORDER BY created_at DESC");

if ($admins_result && $admins_result->num_rows > 0) {
    echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background-color: #f5f5f5;'>";
    echo "<th>ID</th><th>用户名</th><th>邮箱</th><th>姓名</th><th>状态</th><th>创建时间</th>";
    echo "</tr>";
    
    while ($row = $admins_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['username']) . "</td>";
        echo "<td>" . ($row['email'] ? htmlspecialchars($row['email']) : '未设置') . "</td>";
        echo "<td>" . ($row['name'] ? htmlspecialchars($row['name']) : '未设置') . "</td>";
        echo "<td>" . ($row['status'] === 'active' ? '启用' : '禁用') . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>暂无管理员数据</p>";
}

echo "<br><p><a href='admins.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>返回管理员管理页面</a></p>";
?>
