<?php
/**
 * 公告阅读历史API
 * 支持JWT认证，获取用户的公告阅读历史记录
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    // 验证用户认证
    $user = $auth->requireAuth();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        getReadHistory($auth, $user);
    } else {
        $auth->jsonResponse(405, '不支持的请求方法');
    }
} catch (Exception $e) {
    error_log('获取阅读历史API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 获取用户阅读历史
 */
function getReadHistory($auth, $user) {
    $conn = $auth->getConn();
    
    $user_id = $user['id'];
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
    $date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
    $type = isset($_GET['type']) ? $_GET['type'] : '';
    
    // 验证分页参数
    if ($page < 1) $page = 1;
    if ($limit < 1 || $limit > 100) $limit = 10;
    
    $offset = ($page - 1) * $limit;
    
    // 构建查询条件
    $where_conditions = ["ar.user_id = ?"];
    $params = [$user_id];
    $param_types = "i";
    
    // 日期范围过滤
    if (!empty($date_from)) {
        $where_conditions[] = "ar.read_time >= ?";
        $params[] = $date_from . ' 00:00:00';
        $param_types .= "s";
    }
    
    if (!empty($date_to)) {
        $where_conditions[] = "ar.read_time <= ?";
        $params[] = $date_to . ' 23:59:59';
        $param_types .= "s";
    }
    
    // 公告类型过滤
    if (!empty($type)) {
        $where_conditions[] = "a.type = ?";
        $params[] = $type;
        $param_types .= "s";
    }
    
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
    
    // 获取总数
    $count_sql = "SELECT COUNT(*) as total 
                  FROM announcement_reads ar 
                  INNER JOIN announcements a ON ar.announcement_id = a.id 
                  $where_clause";
    
    $count_stmt = $conn->prepare($count_sql);
    if (!empty($params)) {
        $count_stmt->bind_param($param_types, ...$params);
    }
    $count_stmt->execute();
    $total = $count_stmt->get_result()->fetch_assoc()['total'];
    
    // 获取数据
    $sql = "SELECT 
                ar.id as read_id,
                ar.read_time,
                a.id as announcement_id,
                a.title,
                a.content,
                a.type,
                a.priority,
                a.is_pinned,
                a.category_id,
                a.publish_time,
                a.view_count,
                c.name as category_name,
                ad.username as author_name
            FROM announcement_reads ar
            INNER JOIN announcements a ON ar.announcement_id = a.id
            LEFT JOIN announcement_categories c ON a.category_id = c.id
            LEFT JOIN admins ad ON a.author_id = ad.id
            $where_clause
            ORDER BY ar.read_time DESC 
            LIMIT ? OFFSET ?";
    
    // 添加分页参数
    $params[] = $limit;
    $params[] = $offset;
    $param_types .= "ii";
    
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $history = [];
    while ($row = $result->fetch_assoc()) {
        // 处理内容长度
        $content_preview = mb_strlen($row['content']) > 150 
            ? mb_substr($row['content'], 0, 150) . '...' 
            : $row['content'];
        
        // 格式化时间
        $read_time = date('Y-m-d H:i:s', strtotime($row['read_time']));
        $publish_time = $row['publish_time'] ? date('Y-m-d H:i:s', strtotime($row['publish_time'])) : null;
        
        $history[] = [
            'read_id' => (int)$row['read_id'],
            'read_time' => $read_time,
            'announcement' => [
                'id' => (int)$row['announcement_id'],
                'title' => $row['title'],
                'content_preview' => $content_preview,
                'type' => $row['type'],
                'priority' => (int)$row['priority'],
                'is_pinned' => (bool)$row['is_pinned'],
                'category_id' => $row['category_id'] ? (int)$row['category_id'] : null,
                'category_name' => $row['category_name'],
                'publish_time' => $publish_time,
                'view_count' => (int)$row['view_count'],
                'author_name' => $row['author_name']
            ]
        ];
    }
    
    // 获取统计信息
    $stats = getReadStatistics($conn, $user_id, $date_from, $date_to);
    
    // 计算分页信息
    $total_pages = ceil($total / $limit);
    $has_next = $page < $total_pages;
    $has_prev = $page > 1;
    
    $auth->jsonResponse(200, '获取阅读历史成功', [
        'history' => $history,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => (int)$total,
            'total_pages' => $total_pages,
            'has_next' => $has_next,
            'has_prev' => $has_prev
        ],
        'filters' => [
            'date_from' => $date_from,
            'date_to' => $date_to,
            'type' => $type
        ],
        'statistics' => $stats
    ]);
}

/**
 * 获取阅读统计信息
 */
function getReadStatistics($conn, $user_id, $date_from = '', $date_to = '') {
    $stats = [
        'total_read' => 0,
        'by_type' => [],
        'by_date' => []
    ];
    
    try {
        // 构建日期条件
        $date_condition = "";
        $params = [$user_id];
        $param_types = "i";
        
        if (!empty($date_from)) {
            $date_condition .= " AND ar.read_time >= ?";
            $params[] = $date_from . ' 00:00:00';
            $param_types .= "s";
        }
        
        if (!empty($date_to)) {
            $date_condition .= " AND ar.read_time <= ?";
            $params[] = $date_to . ' 23:59:59';
            $param_types .= "s";
        }
        
        // 按类型统计
        $type_sql = "SELECT a.type, COUNT(*) as count 
                     FROM announcement_reads ar 
                     INNER JOIN announcements a ON ar.announcement_id = a.id 
                     WHERE ar.user_id = ? $date_condition 
                     GROUP BY a.type";
        
        $type_stmt = $conn->prepare($type_sql);
        $type_stmt->bind_param($param_types, ...$params);
        $type_stmt->execute();
        $type_result = $type_stmt->get_result();
        
        while ($row = $type_result->fetch_assoc()) {
            $stats['by_type'][$row['type']] = (int)$row['count'];
            $stats['total_read'] += (int)$row['count'];
        }
        
        // 按日期统计（最近7天）
        $date_sql = "SELECT DATE(ar.read_time) as read_date, COUNT(*) as count 
                     FROM announcement_reads ar 
                     WHERE ar.user_id = ? 
                     AND ar.read_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                     GROUP BY DATE(ar.read_time) 
                     ORDER BY read_date DESC";
        
        $date_stmt = $conn->prepare($date_sql);
        $date_stmt->bind_param("i", $user_id);
        $date_stmt->execute();
        $date_result = $date_stmt->get_result();
        
        while ($row = $date_result->fetch_assoc()) {
            $stats['by_date'][$row['read_date']] = (int)$row['count'];
        }
        
    } catch (Exception $e) {
        error_log('获取阅读统计信息时出错: ' . $e->getMessage());
    }
    
    return $stats;
}
?>
