<?php
/**
 * 文件上传API
 * 支持JWT认证，处理用户头像等文件上传
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    // 验证用户认证
    $user = $auth->requireAuth();
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        $auth->jsonResponse(405, '不支持的请求方法');
    }
    
    uploadFile($auth, $user);
    
} catch (Exception $e) {
    error_log('文件上传API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 处理文件上传
 */
function uploadFile($auth, $user) {
    // 检查是否有文件上传
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        $auth->jsonResponse(400, '没有上传文件或上传失败');
    }
    
    $file = $_FILES['file'];
    $upload_type = $_POST['type'] ?? 'avatar'; // 上传类型：avatar, document等
    
    // 验证文件类型
    $allowed_types = [
        'avatar' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        'course_thumbnail' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        'document' => ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
    ];
    
    if (!isset($allowed_types[$upload_type])) {
        $auth->jsonResponse(400, '不支持的上传类型');
    }
    
    if (!in_array($file['type'], $allowed_types[$upload_type])) {
        $auth->jsonResponse(400, '不支持的文件格式');
    }
    
    // 验证文件大小（头像限制2MB，课程缩略图限制5MB，文档限制10MB）
    $max_sizes = [
        'avatar' => 2 * 1024 * 1024, // 2MB
        'course_thumbnail' => 5 * 1024 * 1024, // 5MB
        'document' => 10 * 1024 * 1024 // 10MB
    ];
    
    if ($file['size'] > $max_sizes[$upload_type]) {
        $max_size_mb = $max_sizes[$upload_type] / (1024 * 1024);
        $auth->jsonResponse(400, "文件大小不能超过{$max_size_mb}MB");
    }
    
    // 创建上传目录
    $upload_dir = dirname(__DIR__) . '/uploads/' . $upload_type . '/' . date('Y/m');
    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            $auth->jsonResponse(500, '创建上传目录失败');
        }
    }
    
    // 生成文件名
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $file_name = $user['id'] . '_' . time() . '_' . uniqid() . '.' . $file_extension;
    $file_path = $upload_dir . '/' . $file_name;
    
    // 移动上传文件
    if (!move_uploaded_file($file['tmp_name'], $file_path)) {
        $auth->jsonResponse(500, '文件保存失败');
    }
    
    // 生成访问URL
    $file_url = '/uploads/' . $upload_type . '/' . date('Y/m') . '/' . $file_name;
    
    // 如果是头像上传，更新用户头像字段
    if ($upload_type === 'avatar') {
        $conn = $auth->getConn();
        $stmt = $conn->prepare("UPDATE users SET avatar = ?, updated_at = NOW() WHERE id = ?");
        $stmt->bind_param("si", $file_url, $user['id']);
        
        if (!$stmt->execute()) {
            // 如果数据库更新失败，删除已上传的文件
            unlink($file_path);
            $auth->jsonResponse(500, '更新用户头像失败');
        }
    }
    
    // 记录上传日志
    logUpload($auth->getConn(), $user['id'], $upload_type, $file_name, $file['size'], $file_url);
    
    $auth->jsonResponse(200, '文件上传成功', [
        'file_name' => $file_name,
        'file_url' => $file_url,
        'file_size' => $file['size'],
        'upload_type' => $upload_type,
        'uploaded_at' => date('Y-m-d H:i:s')
    ]);
}

/**
 * 记录上传日志
 */
function logUpload($conn, $user_id, $upload_type, $file_name, $file_size, $file_url) {
    try {
        // 检查是否存在上传日志表
        $table_check = $conn->query("SHOW TABLES LIKE 'upload_logs'");
        if (!$table_check || $table_check->num_rows == 0) {
            // 创建上传日志表
            $create_table_sql = "
                CREATE TABLE upload_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    upload_type VARCHAR(50) NOT NULL,
                    file_name VARCHAR(255) NOT NULL,
                    file_size INT NOT NULL,
                    file_url VARCHAR(500) NOT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_user_id (user_id),
                    INDEX idx_upload_type (upload_type),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            $conn->query($create_table_sql);
        }
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $stmt = $conn->prepare("INSERT INTO upload_logs (user_id, upload_type, file_name, file_size, file_url, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("issssss", $user_id, $upload_type, $file_name, $file_size, $file_url, $ip_address, $user_agent);
        $stmt->execute();
        
    } catch (Exception $e) {
        error_log('记录上传日志失败: ' . $e->getMessage());
    }
}
?>
