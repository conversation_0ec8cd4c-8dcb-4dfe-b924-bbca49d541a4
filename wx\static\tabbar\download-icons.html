<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TabBar图标下载工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007bff;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: #fafafa;
        }
        .icon-preview {
            width: 50px;
            height: 50px;
            margin: 0 auto 10px;
            display: block;
        }
        .icon-name {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .download-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 2px;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .batch-download {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 0;
            width: 100%;
        }
        .batch-download:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 TabBar图标下载工具</h1>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>点击下方的下载按钮，下载对应的图标文件</li>
                <li>将下载的图标文件重命名并放置到 <code>static/tabbar/</code> 目录</li>
                <li>使用 <code>pages-local-icons.json</code> 替换 <code>pages.json</code></li>
                <li>重新编译项目即可看到图标效果</li>
            </ol>
        </div>

        <button class="batch-download" onclick="downloadAllIcons()">
            🚀 一键下载所有图标
        </button>

        <div class="icon-grid">
            <!-- 首页图标 -->
            <div class="icon-item">
                <img class="icon-preview" src="https://img.icons8.com/ios/50/7A7E83/home--v1.png" alt="首页图标">
                <div class="icon-name">首页图标</div>
                <a href="https://img.icons8.com/ios/81/7A7E83/home--v1.png" class="download-btn" download="home.png">下载普通</a>
                <a href="https://img.icons8.com/ios-filled/81/007bff/home--v1.png" class="download-btn" download="home-active.png">下载选中</a>
            </div>

            <!-- 课程图标 -->
            <div class="icon-item">
                <img class="icon-preview" src="https://img.icons8.com/ios/50/7A7E83/video.png" alt="课程图标">
                <div class="icon-name">课程图标</div>
                <a href="https://img.icons8.com/ios/81/7A7E83/video.png" class="download-btn" download="course.png">下载普通</a>
                <a href="https://img.icons8.com/ios-filled/81/007bff/video.png" class="download-btn" download="course-active.png">下载选中</a>
            </div>

            <!-- 个人图标 -->
            <div class="icon-item">
                <img class="icon-preview" src="https://img.icons8.com/ios/50/7A7E83/user.png" alt="个人图标">
                <div class="icon-name">个人图标</div>
                <a href="https://img.icons8.com/ios/81/7A7E83/user.png" class="download-btn" download="profile.png">下载普通</a>
                <a href="https://img.icons8.com/ios-filled/81/007bff/user.png" class="download-btn" download="profile-active.png">下载选中</a>
            </div>
        </div>

        <div class="instructions">
            <h3>🔄 备用方案</h3>
            <p><strong>方案1:</strong> 当前使用的在线图标（已配置在pages.json中）</p>
            <p><strong>方案2:</strong> 下载本地图标（使用此页面下载）</p>
            <p><strong>方案3:</strong> 使用uni-icons字体图标（见custom-tabbar组件）</p>
        </div>
    </div>

    <script>
        function downloadAllIcons() {
            const icons = [
                { url: 'https://img.icons8.com/ios/81/7A7E83/home--v1.png', name: 'home.png' },
                { url: 'https://img.icons8.com/ios-filled/81/007bff/home--v1.png', name: 'home-active.png' },
                { url: 'https://img.icons8.com/ios/81/7A7E83/video.png', name: 'course.png' },
                { url: 'https://img.icons8.com/ios-filled/81/007bff/video.png', name: 'course-active.png' },
                { url: 'https://img.icons8.com/ios/81/7A7E83/user.png', name: 'profile.png' },
                { url: 'https://img.icons8.com/ios-filled/81/007bff/user.png', name: 'profile-active.png' }
            ];

            icons.forEach((icon, index) => {
                setTimeout(() => {
                    const link = document.createElement('a');
                    link.href = icon.url;
                    link.download = icon.name;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }, index * 500); // 延迟下载避免浏览器限制
            });

            alert('开始下载所有图标文件，请检查浏览器下载文件夹！');
        }
    </script>
</body>
</html>
