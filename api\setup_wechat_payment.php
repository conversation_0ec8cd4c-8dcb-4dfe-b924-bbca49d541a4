<?php
/**
 * 微信支付配置设置页面
 * 用于配置微信支付参数
 */

header('Content-Type: text/html; charset=utf-8');
require_once '../includes/db.php';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $app_id = trim($_POST['app_id'] ?? '');
        $mch_id = trim($_POST['mch_id'] ?? '');
        $api_key = trim($_POST['api_key'] ?? '');
        $enabled = isset($_POST['enabled']) ? '1' : '0';
        
        // 验证输入
        $errors = [];
        
        if (empty($app_id)) {
            $errors[] = 'AppID不能为空';
        } elseif (!preg_match('/^wx[a-f0-9]{16}$/', $app_id)) {
            $errors[] = 'AppID格式不正确（应以wx开头，后跟16位十六进制字符）';
        }
        
        if (empty($mch_id)) {
            $errors[] = '商户号不能为空';
        } elseif (!preg_match('/^\d{10}$/', $mch_id)) {
            $errors[] = '商户号格式不正确（应为10位数字）';
        }
        
        if (empty($api_key)) {
            $errors[] = 'API密钥不能为空';
        } elseif (strlen($api_key) < 32) {
            $errors[] = 'API密钥长度不能少于32位';
        }
        
        if (empty($errors)) {
            // 更新配置
            $configs = [
                'wechat_pay_enabled' => $enabled,
                'wechat_pay_app_id' => $app_id,
                'wechat_pay_mch_id' => $mch_id,
                'wechat_pay_api_key' => $api_key,
                'wechat_pay_notify_url' => 'https://wx.yx420.cn/api/payment-wechat-notify.php'
            ];
            
            foreach ($configs as $key => $value) {
                $stmt = $conn->prepare("
                    INSERT INTO settings (setting_key, setting_value, setting_type, description) 
                    VALUES (?, ?, 'string', ?)
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $description = '微信支付配置: ' . $key;
                $stmt->bind_param("sss", $key, $value, $description);
                $stmt->execute();
            }
            
            $success_message = "微信支付配置已保存成功！";
        }
    } catch (Exception $e) {
        $errors[] = "保存配置时出错: " . $e->getMessage();
    }
}

// 获取当前配置
$current_config = [];
try {
    $stmt = $conn->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'wechat_pay_%'");
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $current_config[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    $errors[] = "读取配置时出错: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信支付配置</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .error { background: #ffebee; color: #c62828; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .success { background: #e8f5e8; color: #2e7d32; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { background: #e3f2fd; color: #1565c0; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .checkbox-group { display: flex; align-items: center; }
        .checkbox-group input { width: auto; margin-right: 10px; }
    </style>
</head>
<body>
    <h1>微信支付配置</h1>
    
    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="error">
            <h3>错误：</h3>
            <ul>
                <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
    
    <?php if (isset($success_message)): ?>
        <div class="success">
            <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>
    
    <div class="info">
        <h3>配置说明</h3>
        <p>要使用微信支付功能，您需要：</p>
        <ol>
            <li>在微信公众平台注册小程序并获取AppID</li>
            <li>在微信支付商户平台申请商户号</li>
            <li>在商户平台设置API密钥</li>
            <li>确保AppID与商户号已正确绑定</li>
        </ol>
    </div>
    
    <form method="POST">
        <div class="form-group checkbox-group">
            <input type="checkbox" id="enabled" name="enabled" <?php echo ($current_config['wechat_pay_enabled'] ?? '0') === '1' ? 'checked' : ''; ?>>
            <label for="enabled">启用微信支付</label>
        </div>
        
        <div class="form-group">
            <label for="app_id">微信小程序AppID:</label>
            <input type="text" id="app_id" name="app_id" value="<?php echo htmlspecialchars($current_config['wechat_pay_app_id'] ?? 'wxa936b5c9fa9b1893'); ?>" placeholder="wx1234567890abcdef">
            <small>格式：wx + 16位十六进制字符</small>
        </div>
        
        <div class="form-group">
            <label for="mch_id">微信支付商户号:</label>
            <input type="text" id="mch_id" name="mch_id" value="<?php echo htmlspecialchars($current_config['wechat_pay_mch_id'] ?? ''); ?>" placeholder="1234567890">
            <small>格式：10位数字</small>
        </div>
        
        <div class="form-group">
            <label for="api_key">微信支付API密钥:</label>
            <input type="password" id="api_key" name="api_key" value="<?php echo htmlspecialchars($current_config['wechat_pay_api_key'] ?? ''); ?>" placeholder="32位字符的API密钥">
            <small>在微信支付商户平台设置，长度至少32位</small>
        </div>
        
        <button type="submit">保存配置</button>
    </form>
    
    <div class="warning">
        <h3>⚠️ 重要提示</h3>
        <p><strong>如果您没有真实的微信支付商户号，暂时无法使用微信支付功能。</strong></p>
        <p>您可以：</p>
        <ul>
            <li>先测试其他功能（订单创建等已正常工作）</li>
            <li>申请微信支付商户号后再配置</li>
            <li>在开发阶段暂时禁用支付功能</li>
        </ul>
    </div>
    
    <h2>当前配置状态</h2>
    <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr>
            <th>配置项</th>
            <th>状态</th>
            <th>值</th>
        </tr>
        <?php
        $required_configs = [
            'wechat_pay_enabled' => '是否启用',
            'wechat_pay_app_id' => 'AppID',
            'wechat_pay_mch_id' => '商户号',
            'wechat_pay_api_key' => 'API密钥'
        ];
        
        foreach ($required_configs as $key => $name) {
            $value = $current_config[$key] ?? '';
            $status = empty($value) ? '❌ 未配置' : '✅ 已配置';
            
            if ($key === 'wechat_pay_api_key' && !empty($value)) {
                $value = str_repeat('*', strlen($value));
            }
            
            echo "<tr>";
            echo "<td>$name</td>";
            echo "<td>$status</td>";
            echo "<td>" . htmlspecialchars($value) . "</td>";
            echo "</tr>";
        }
        ?>
    </table>
    
    <h2>测试建议</h2>
    <div class="info">
        <p>配置完成后，您可以：</p>
        <ol>
            <li>在微信开发者工具中测试小程序</li>
            <li>尝试购买课程，查看是否还有配置错误</li>
            <li>检查微信支付商户平台的日志</li>
            <li>确认AppID和商户号的绑定关系</li>
        </ol>
    </div>
</body>
</html>
