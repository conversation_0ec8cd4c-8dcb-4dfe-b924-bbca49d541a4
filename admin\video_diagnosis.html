<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频播放诊断工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.warning {
            background: #ffc107;
            color: #333;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .lesson-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .lesson-card {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            background: #f9f9f9;
        }
        .lesson-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .lesson-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-ok {
            background: #d4edda;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>视频播放诊断工具</h1>
            <p>检测和修复小程序课程视频播放问题</p>
        </div>
        
        <div class="content">
            <!-- 快速修复 -->
            <div class="section">
                <h3>🚀 快速修复</h3>
                <p>一键修复常见的视频播放问题</p>
                <button class="btn success" onclick="fixVideoData()">修复视频数据</button>
                <button class="btn" onclick="checkAllLessons()">检查所有课时</button>
                <button class="btn warning" onclick="testVideoUrls()">测试视频URL</button>
                <div id="quickFixResult" class="result" style="display: none;"></div>
            </div>

            <!-- 课时状态检查 -->
            <div class="section">
                <h3>📋 课时状态检查</h3>
                <p>检查所有课时的视频配置状态</p>
                <button class="btn" onclick="loadLessonStatus()">加载课时状态</button>
                <div class="loading" id="lessonLoading">
                    <div class="spinner"></div>
                    <p>正在加载课时数据...</p>
                </div>
                <div id="lessonList" class="lesson-list"></div>
            </div>

            <!-- VOD配置检查 -->
            <div class="section">
                <h3>☁️ 腾讯云VOD配置检查</h3>
                <p>检查腾讯云点播服务配置</p>
                <button class="btn" onclick="checkVodConfig()">检查VOD配置</button>
                <button class="btn" onclick="testVodApi()">测试VOD API</button>
                <div id="vodResult" class="result" style="display: none;"></div>
            </div>

            <!-- 小程序兼容性检查 -->
            <div class="section">
                <h3>📱 小程序兼容性检查</h3>
                <p>检查视频格式和URL的小程序兼容性</p>
                <button class="btn" onclick="checkCompatibility()">检查兼容性</button>
                <div id="compatibilityResult" class="result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        // 修复视频数据
        async function fixVideoData() {
            const resultDiv = document.getElementById('quickFixResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在修复视频数据...';

            try {
                const response = await fetch('fix_video_data.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `修复完成！\n\n${result.message}\n\n修复项目：\n${result.fixes.join('\n')}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `修复失败：${result.message}\n\n${result.errors ? result.errors.join('\n') : ''}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败：${error.message}`;
            }
        }

        // 检查所有课时
        async function checkAllLessons() {
            const resultDiv = document.getElementById('quickFixResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在检查所有课时...';

            try {
                const response = await fetch('../api/lessonList.php?course_id=0&show_all=1');
                const result = await response.json();
                
                if (result.success) {
                    const lessons = result.data.lessons || [];
                    let report = `课时检查报告\n\n总课时数：${lessons.length}\n\n`;
                    
                    let okCount = 0;
                    let errorCount = 0;
                    
                    lessons.forEach(lesson => {
                        const hasVideo = lesson.video_url || (lesson.video_type === 'vod' && lesson.vod_file_id);
                        if (hasVideo) {
                            okCount++;
                            report += `✅ 课时${lesson.id}：${lesson.title} - 配置正常\n`;
                        } else {
                            errorCount++;
                            report += `❌ 课时${lesson.id}：${lesson.title} - 缺少视频配置\n`;
                        }
                    });
                    
                    report += `\n总结：${okCount}个正常，${errorCount}个有问题`;
                    
                    resultDiv.className = errorCount > 0 ? 'result error' : 'result success';
                    resultDiv.textContent = report;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `检查失败：${result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败：${error.message}`;
            }
        }

        // 加载课时状态
        async function loadLessonStatus() {
            const loadingDiv = document.getElementById('lessonLoading');
            const listDiv = document.getElementById('lessonList');
            
            loadingDiv.style.display = 'block';
            listDiv.innerHTML = '';

            try {
                const response = await fetch('../api/lessonList.php?course_id=0&show_all=1');
                const result = await response.json();
                
                if (result.success) {
                    const lessons = result.data.lessons || [];
                    
                    lessons.forEach(lesson => {
                        const card = createLessonCard(lesson);
                        listDiv.appendChild(card);
                    });
                } else {
                    listDiv.innerHTML = `<p class="result error">加载失败：${result.message}</p>`;
                }
            } catch (error) {
                listDiv.innerHTML = `<p class="result error">请求失败：${error.message}</p>`;
            } finally {
                loadingDiv.style.display = 'none';
            }
        }

        // 创建课时卡片
        function createLessonCard(lesson) {
            const card = document.createElement('div');
            card.className = 'lesson-card';
            
            const hasVideo = lesson.video_url || (lesson.video_type === 'vod' && lesson.vod_file_id);
            const status = hasVideo ? 'ok' : 'error';
            const statusText = hasVideo ? '配置正常' : '缺少视频';
            
            card.innerHTML = `
                <h4>${lesson.title}</h4>
                <div class="lesson-info">课时ID: ${lesson.id} | 课程ID: ${lesson.course_id}</div>
                <div class="lesson-info">类型: ${lesson.video_type || 'url'}</div>
                <div class="lesson-info">状态: <span class="status-badge status-${status}">${statusText}</span></div>
                ${lesson.video_url ? `<div class="lesson-info">视频URL: ${lesson.video_url.substring(0, 50)}...</div>` : ''}
                ${lesson.vod_file_id ? `<div class="lesson-info">VOD文件ID: ${lesson.vod_file_id}</div>` : ''}
            `;
            
            return card;
        }

        // 检查VOD配置
        async function checkVodConfig() {
            const resultDiv = document.getElementById('vodResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在检查VOD配置...';

            try {
                const response = await fetch('../api/vod-config.php');
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `VOD配置检查通过\n\n配置信息：\n${JSON.stringify(result.data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `VOD配置检查失败：${result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败：${error.message}`;
            }
        }

        // 测试VOD API
        async function testVodApi() {
            const resultDiv = document.getElementById('vodResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试VOD API...';

            try {
                // 使用第一个VOD课时的file_id进行测试
                const response = await fetch('../api/vod-status.php?file_id=5145403692137145316');
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `VOD API测试成功\n\n返回数据：\n${JSON.stringify(result.data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `VOD API测试失败：${result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败：${error.message}`;
            }
        }

        // 检查兼容性
        function checkCompatibility() {
            const resultDiv = document.getElementById('compatibilityResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            
            let report = '小程序视频兼容性检查\n\n';
            report += '支持的视频格式：\n';
            report += '✅ MP4 (推荐)\n';
            report += '✅ M3U8 (HLS流)\n';
            report += '❌ AVI, WMV, FLV (不支持)\n\n';
            
            report += '视频URL要求：\n';
            report += '✅ 必须是HTTPS协议\n';
            report += '✅ 支持跨域访问\n';
            report += '✅ 文件大小建议小于100MB\n\n';
            
            report += '常见问题：\n';
            report += '• 视频黑屏：检查URL是否有效\n';
            report += '• 无法播放：检查视频格式和编码\n';
            report += '• 加载缓慢：检查网络和CDN配置\n';
            
            resultDiv.className = 'result info';
            resultDiv.textContent = report;
        }

        // 测试视频URL
        async function testVideoUrls() {
            const resultDiv = document.getElementById('quickFixResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试视频URL可访问性...';

            try {
                const response = await fetch('../api/lessonList.php?course_id=0&show_all=1');
                const result = await response.json();
                
                if (result.success) {
                    const lessons = result.data.lessons || [];
                    let report = '视频URL测试报告\n\n';
                    
                    for (const lesson of lessons) {
                        const videoUrl = lesson.video_url || lesson.vod_video_url;
                        if (videoUrl) {
                            try {
                                const testResponse = await fetch(videoUrl, { method: 'HEAD' });
                                const status = testResponse.ok ? '✅ 可访问' : '❌ 不可访问';
                                report += `课时${lesson.id}: ${status} (${testResponse.status})\n`;
                            } catch (error) {
                                report += `课时${lesson.id}: ❌ 网络错误\n`;
                            }
                        } else {
                            report += `课时${lesson.id}: ⚠️ 无视频URL\n`;
                        }
                    }
                    
                    resultDiv.className = 'result info';
                    resultDiv.textContent = report;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `获取课时列表失败：${result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败：${error.message}`;
            }
        }

        // 页面加载时自动加载课时状态
        window.addEventListener('load', function() {
            loadLessonStatus();
        });
    </script>
</body>
</html>
