<?php
require_once '../includes/db.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

function sendResponse($code, $message, $data = null) {
    $response = [
        'code' => $code,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ];
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getCategories();
        break;
    default:
        sendResponse(405, '不支持的请求方法');
}

function getCategories() {
    global $conn;
    
    $sql = "SELECT id, name, description, sort_order 
            FROM announcement_categories 
            WHERE is_active = 1 
            ORDER BY sort_order ASC, id ASC";
    
    $result = $conn->query($sql);
    $categories = [];
    
    while ($row = $result->fetch_assoc()) {
        $categories[] = $row;
    }
    
    sendResponse(200, '获取成功', $categories);
}
?>