<?php
/**
 * 静态文件访问处理器
 * 处理uploads目录下的文件访问，解决500错误问题
 */

// 设置CORS头部
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 允许GET和HEAD请求
if (!in_array($_SERVER['REQUEST_METHOD'], ['GET', 'HEAD'])) {
    http_response_code(405);
    exit('Method Not Allowed');
}

// 获取请求的文件路径
$request_path = $_GET['path'] ?? '';

if (empty($request_path)) {
    http_response_code(400);
    exit('Missing file path');
}

// 安全检查：防止目录遍历攻击
if (strpos($request_path, '..') !== false || strpos($request_path, '\\') !== false) {
    http_response_code(403);
    exit('Forbidden');
}

// 构建实际文件路径
$base_dir = dirname(__DIR__);
$file_path = $base_dir . '/' . ltrim($request_path, '/');

// 检查文件是否存在
if (!file_exists($file_path) || !is_file($file_path)) {
    // 如果是头像文件不存在，返回默认头像
    if (strpos($request_path, '/uploads/avatar/') === 0) {
        serveDefaultAvatar();
        exit;
    }
    
    http_response_code(404);
    exit('File not found');
}

// 检查文件是否在允许的目录内
$real_file_path = realpath($file_path);
$real_base_dir = realpath($base_dir);

if (strpos($real_file_path, $real_base_dir) !== 0) {
    http_response_code(403);
    exit('Forbidden');
}

// 获取文件信息
$file_size = filesize($file_path);
$file_time = filemtime($file_path);

// 设置缓存头部
$etag = md5($file_path . $file_time . $file_size);
header('ETag: "' . $etag . '"');
header('Last-Modified: ' . gmdate('D, d M Y H:i:s', $file_time) . ' GMT');
header('Cache-Control: public, max-age=86400'); // 缓存1天

// 检查客户端缓存
$client_etag = $_SERVER['HTTP_IF_NONE_MATCH'] ?? '';
$client_modified = $_SERVER['HTTP_IF_MODIFIED_SINCE'] ?? '';

if ($client_etag === '"' . $etag . '"' || 
    ($client_modified && strtotime($client_modified) >= $file_time)) {
    http_response_code(304);
    exit;
}

// 根据文件扩展名设置Content-Type
$file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
$content_types = [
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'webp' => 'image/webp',
    'pdf' => 'application/pdf',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];

$content_type = $content_types[$file_extension] ?? 'application/octet-stream';
header('Content-Type: ' . $content_type);
header('Content-Length: ' . $file_size);

// 对于HEAD请求，只返回头部信息，不输出文件内容
if ($_SERVER['REQUEST_METHOD'] === 'HEAD') {
    exit;
}

// 输出文件内容
readfile($file_path);

/**
 * 提供默认头像
 */
function serveDefaultAvatar() {
    // 创建一个简单的默认头像SVG
    $svg = '<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <circle cx="50" cy="50" r="50" fill="#f0f0f0"/>
    <circle cx="50" cy="35" r="15" fill="#ccc"/>
    <path d="M20 80 Q20 65 35 65 L65 65 Q80 65 80 80 L80 100 L20 100 Z" fill="#ccc"/>
</svg>';

    header('Content-Type: image/svg+xml');
    header('Content-Length: ' . strlen($svg));
    header('Cache-Control: public, max-age=86400');

    // 对于HEAD请求，只返回头部信息
    if ($_SERVER['REQUEST_METHOD'] === 'HEAD') {
        return;
    }

    echo $svg;
}
?>
