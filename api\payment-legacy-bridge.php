<?php
/**
 * 传统系统支付回调桥接器
 * 用于处理传统系统的支付回调并同步到新系统
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);

require_once '../includes/db.php';
require_once '../includes/payment_bridge.php';

// 记录回调日志
function log_bridge_callback($message, $data = null) {
    $log_file = '../logs/legacy_bridge_' . date('Y-m-d') . '.log';
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $log_content = date('Y-m-d H:i:s') . ' - ' . $message;
    if ($data) {
        $log_content .= ' - Data: ' . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    $log_content .= "\n";
    
    file_put_contents($log_file, $log_content, FILE_APPEND | LOCK_EX);
}

try {
    // 获取支付桥接器
    $bridge = getPaymentBridge();
    
    // 获取传统系统订单号
    $trade_no = $_GET['trade_no'] ?? $_POST['trade_no'] ?? '';
    $status = $_GET['status'] ?? $_POST['status'] ?? '';
    
    if (empty($trade_no)) {
        log_bridge_callback('缺少订单号参数');
        echo 'fail';
        exit;
    }
    
    log_bridge_callback('收到传统系统支付回调', [
        'trade_no' => $trade_no,
        'status' => $status,
        'get_data' => $_GET,
        'post_data' => $_POST
    ]);
    
    // 获取传统系统订单状态
    $legacy_order = $bridge->getLegacyOrderStatus($trade_no);
    
    if (!$legacy_order) {
        log_bridge_callback('传统系统订单不存在', ['trade_no' => $trade_no]);
        echo 'fail';
        exit;
    }
    
    // 检查是否是LEGACY开头的订单号（表示是从新系统创建的）
    if (strpos($trade_no, 'LEGACY') === 0) {
        // 这是从新系统创建的订单，需要同步回新系统
        if ($legacy_order['status'] == 1) {
            // 支付成功，同步到新系统
            syncLegacyPaymentToNewSystem($trade_no, $legacy_order, $bridge);
        }
    }
    
    log_bridge_callback('传统系统支付回调处理完成', [
        'trade_no' => $trade_no,
        'legacy_status' => $legacy_order['status']
    ]);
    
    echo 'success';
    
} catch (Exception $e) {
    log_bridge_callback('传统系统支付回调处理异常: ' . $e->getMessage());
    echo 'fail';
}

/**
 * 同步传统系统支付结果到新系统
 */
function syncLegacyPaymentToNewSystem($trade_no, $legacy_order, $bridge) {
    global $conn;
    
    try {
        // 从订单号中提取用户ID（LEGACY + 时间戳 + 用户ID + 随机数）
        $user_id_part = substr($trade_no, 20, 6); // 提取用户ID部分
        $user_id = intval($user_id_part);
        
        if ($user_id <= 0) {
            log_bridge_callback('无法从订单号提取用户ID', ['trade_no' => $trade_no]);
            return false;
        }
        
        // 查找对应的新系统订单（根据用户ID和金额匹配）
        $stmt = $conn->prepare("
            SELECT o.* FROM orders o 
            WHERE o.user_id = ? 
            AND o.actual_amount = ? 
            AND o.order_status = 'pending' 
            AND o.payment_status = 'unpaid'
            ORDER BY o.created_at DESC 
            LIMIT 1
        ");
        $stmt->bind_param("is", $user_id, $legacy_order['money']);
        $stmt->execute();
        $new_order = $stmt->get_result()->fetch_assoc();
        
        if (!$new_order) {
            log_bridge_callback('未找到对应的新系统订单', [
                'user_id' => $user_id,
                'amount' => $legacy_order['money']
            ]);
            return false;
        }
        
        // 开始事务
        $conn->begin_transaction();
        
        try {
            // 创建支付记录
            $stmt = $conn->prepare("
                INSERT INTO payments (payment_no, order_id, user_id, payment_method, amount, 
                                     payment_status, transaction_id, paid_at, created_at) 
                VALUES (?, ?, ?, 'legacy_epay', ?, 'success', ?, NOW(), NOW())
            ");
            $stmt->bind_param("siiss", $trade_no, $new_order['id'], $user_id, $legacy_order['money'], $trade_no);
            $stmt->execute();
            
            // 更新订单状态
            $stmt = $conn->prepare("
                UPDATE orders 
                SET order_status = 'paid', 
                    payment_status = 'paid', 
                    payment_method = 'legacy_epay', 
                    payment_time = NOW(),
                    updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->bind_param("i", $new_order['id']);
            $stmt->execute();
            
            // 处理课程购买逻辑
            process_course_purchase($conn, $new_order['id'], $user_id);
            
            // 提交事务
            $conn->commit();
            
            log_bridge_callback('成功同步传统系统支付到新系统', [
                'trade_no' => $trade_no,
                'new_order_id' => $new_order['id'],
                'user_id' => $user_id,
                'amount' => $legacy_order['money']
            ]);
            
            return true;
            
        } catch (Exception $e) {
            // 回滚事务
            $conn->rollback();
            log_bridge_callback('同步支付到新系统失败: ' . $e->getMessage());
            return false;
        }
        
    } catch (Exception $e) {
        log_bridge_callback('同步支付到新系统异常: ' . $e->getMessage());
        return false;
    }
}

/**
 * 处理课程购买逻辑
 */
function process_course_purchase($conn, $order_id, $user_id) {
    // 获取订单中的课程
    $stmt = $conn->prepare("
        SELECT course_id, quantity 
        FROM order_items 
        WHERE order_id = ?
    ");
    $stmt->bind_param("i", $order_id);
    $stmt->execute();
    $order_items = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    foreach ($order_items as $item) {
        // 检查用户是否已经拥有该课程
        $stmt = $conn->prepare("
            SELECT id FROM user_courses 
            WHERE user_id = ? AND course_id = ?
        ");
        $stmt->bind_param("ii", $user_id, $item['course_id']);
        $stmt->execute();
        $exists = $stmt->get_result()->num_rows > 0;
        
        if (!$exists) {
            // 添加用户课程记录
            $stmt = $conn->prepare("
                INSERT INTO user_courses (user_id, course_id, purchase_time, status) 
                VALUES (?, ?, NOW(), 'active')
            ");
            $stmt->bind_param("ii", $user_id, $item['course_id']);
            $stmt->execute();
        }
    }
}
?>
