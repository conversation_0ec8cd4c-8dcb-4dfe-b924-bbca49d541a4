!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.TcVod=t():e.TcVod=t()}(window,function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){"use strict";n.r(t);var r=n(54),o=n.n(r),i=n(1),a=n.n(i),s=n(28),c=n.n(s);function u(){return a.a.post("https://demo.vod2.myqcloud.com/ugc-upload/",JSON.stringify({Action:"GetUgcUploadSign"})).then(function(e){return e.data.data.sign})}function l(e,t){return a.a.post("https://demo.vod2.myqcloud.com/ugc-upload/",JSON.stringify({Action:"GetAntiLeechUrl",Url:e})).then(function(e){return e.data.data.url})}new o.a({el:"#main-area",data:{uploaderInfos:[],vcExampleVideoName:"",vcExampleCoverName:"",cExampleFileId:""},created:function(){this.tcVod=new c.a({getSignature:u})},methods:{vExampleAdd:function(){this.$refs.vExampleFile.click()},vExampleUpload:function(){var e=this,t=this.$refs.vExampleFile.files[0],n=this.tcVod.upload({mediaFile:t});n.on("media_progress",function(e){r.progress=e.percent}),n.on("media_upload",function(e){r.isVideoUploadSuccess=!0}),console.log(n,"uploader");var r={videoInfo:n.videoInfo,isVideoUploadSuccess:!1,isVideoUploadCancel:!1,progress:0,fileId:"",videoUrl:"",cancel:function(){r.isVideoUploadCancel=!0,n.cancel()}};this.uploaderInfos.push(r),n.done().then(function(e){return console.log("doneResult",e),r.fileId=e.fileId,l(e.video.url)}).then(function(t){r.videoUrl=t,e.$refs.vExample.reset()})},setVcExampleVideoName:function(){this.vcExampleVideoName=this.$refs.vcExampleVideo.files[0].name},setVcExampleCoverName:function(){this.vcExampleCoverName=this.$refs.vcExampleCover.files[0].name},vcExampleAddVideo:function(){this.$refs.vcExampleVideo.click()},vcExampleAddCover:function(){this.$refs.vcExampleCover.click()},vcExampleAddUpload:function(){var e=this,t=this.$refs.vcExampleVideo.files[0],n=this.$refs.vcExampleCover.files[0],r=this.tcVod.upload({mediaFile:t,coverFile:n});r.on("media_progress",function(e){o.progress=e.percent}),r.on("media_upload",function(e){o.isVideoUploadSuccess=!0}),r.on("cover_progress",function(e){o.coverProgress=e.percent}),r.on("cover_upload",function(e){o.isCoverUploadSuccess=!0}),console.log(r,"uploader");var o={videoInfo:r.videoInfo,coverInfo:r.coverInfo,isVideoUploadSuccess:!1,isVideoUploadCancel:!1,isCoverUploadSuccess:!1,progress:0,coverProgress:0,fileId:"",videoUrl:"",coverUrl:"",cancel:function(){o.isVideoUploadCancel=!0,r.cancel()}};this.uploaderInfos.push(o),r.done().then(function(e){return console.log("doneResult",e),o.fileId=e.fileId,o.coverUrl=e.cover.url,l(e.video.url)}).then(function(t){o.videoUrl=t,e.$refs.vcExample.reset(),e.vcExampleVideoName="",e.vcExampleCoverName=""})},cExampleAddCover:function(){this.$refs.cExampleCover.click()},cExampleUpload:function(){var e=this,t=this.$refs.cExampleCover.files[0],n=this.tcVod.upload({fileId:this.cExampleFileId,coverFile:t});n.on("cover_progress",function(e){r.coverProgress=e.percent}),n.on("cover_upload",function(e){r.isCoverUploadSuccess=!0}),console.log(n,"uploader");var r={coverInfo:n.coverInfo,isCoverUploadSuccess:!1,coverProgress:0,coverUrl:"",cancel:function(){n.cancel()}};this.uploaderInfos.push(r),n.done().then(function(t){console.log("doneResult",t),r.coverUrl=t.cover.url,e.$refs.cExample.reset()})}}})},function(e,t,n){e.exports=n(2)},function(e,t,n){"use strict";var r=n(3),o=n(4),i=n(6),a=n(7);function s(e){var t=new i(e),n=o(i.prototype.request,t);return r.extend(n,i.prototype,t),r.extend(n,t),n}var c=s(a);c.Axios=i,c.create=function(e){return s(r.merge(a,e))},c.Cancel=n(25),c.CancelToken=n(26),c.isCancel=n(22),c.all=function(e){return Promise.all(e)},c.spread=n(27),e.exports=c,e.exports.default=c},function(e,t,n){"use strict";var r=n(4),o=n(5),i=Object.prototype.toString;function a(e){return"[object Array]"===i.call(e)}function s(e){return null!==e&&"object"==typeof e}function c(e){return"[object Function]"===i.call(e)}function u(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),a(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}e.exports={isArray:a,isArrayBuffer:function(e){return"[object ArrayBuffer]"===i.call(e)},isBuffer:o,isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:s,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===i.call(e)},isFile:function(e){return"[object File]"===i.call(e)},isBlob:function(e){return"[object Blob]"===i.call(e)},isFunction:c,isStream:function(e){return s(e)&&c(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:u,merge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]=n}for(var r=0,o=arguments.length;r<o;r++)u(arguments[r],n);return t},extend:function(e,t,n){return u(t,function(t,o){e[o]=n&&"function"==typeof t?r(t,n):t}),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}}},function(e,t,n){"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}},function(e,t){function n(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
e.exports=function(e){return null!=e&&(n(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&n(e.slice(0,0))}(e)||!!e._isBuffer)}},function(e,t,n){"use strict";var r=n(7),o=n(3),i=n(19),a=n(20);function s(e){this.defaults=e,this.interceptors={request:new i,response:new i}}s.prototype.request=function(e){"string"==typeof e&&(e=o.merge({url:arguments[0]},arguments[1])),(e=o.merge(r,{method:"get"},this.defaults,e)).method=e.method.toLowerCase();var t=[a,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach(function(e){t.unshift(e.fulfilled,e.rejected)}),this.interceptors.response.forEach(function(e){t.push(e.fulfilled,e.rejected)});t.length;)n=n.then(t.shift(),t.shift());return n},o.forEach(["delete","get","head","options"],function(e){s.prototype[e]=function(t,n){return this.request(o.merge(n||{},{method:e,url:t}))}}),o.forEach(["post","put","patch"],function(e){s.prototype[e]=function(t,n,r){return this.request(o.merge(r||{},{method:e,url:t,data:n}))}}),e.exports=s},function(e,t,n){"use strict";(function(t){var r=n(3),o=n(9),i={"Content-Type":"application/x-www-form-urlencoded"};function a(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var s,c={adapter:("undefined"!=typeof XMLHttpRequest?s=n(10):void 0!==t&&(s=n(10)),s),transformRequest:[function(e,t){return o(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(a(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)?(a(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300}};c.headers={common:{Accept:"application/json, text/plain, */*"}},r.forEach(["delete","get","head"],function(e){c.headers[e]={}}),r.forEach(["post","put","patch"],function(e){c.headers[e]=r.merge(i)}),e.exports=c}).call(this,n(8))},function(e,t){var n,r,o=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(e){if(n===setTimeout)return setTimeout(e,0);if((n===i||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:i}catch(e){n=i}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(e){r=a}}();var c,u=[],l=!1,d=-1;function f(){l&&c&&(l=!1,c.length?u=c.concat(u):d=-1,u.length&&p())}function p(){if(!l){var e=s(f);l=!0;for(var t=u.length;t;){for(c=u,u=[];++d<t;)c&&c[d].run();d=-1,t=u.length}c=null,l=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function v(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];u.push(new h(e,t)),1!==u.length||l||s(p)},h.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=v,o.addListener=v,o.once=v,o.off=v,o.removeListener=v,o.removeAllListeners=v,o.emit=v,o.prependListener=v,o.prependOnceListener=v,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},function(e,t,n){"use strict";var r=n(3);e.exports=function(e,t){r.forEach(e,function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])})}},function(e,t,n){"use strict";var r=n(3),o=n(11),i=n(14),a=n(15),s=n(16),c=n(12),u="undefined"!=typeof window&&window.btoa&&window.btoa.bind(window)||n(17);e.exports=function(e){return new Promise(function(t,l){var d=e.data,f=e.headers;r.isFormData(d)&&delete f["Content-Type"];var p=new XMLHttpRequest,h="onreadystatechange",v=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in p||s(e.url)||(p=new window.XDomainRequest,h="onload",v=!0,p.onprogress=function(){},p.ontimeout=function(){}),e.auth){var m=e.auth.username||"",g=e.auth.password||"";f.Authorization="Basic "+u(m+":"+g)}if(p.open(e.method.toUpperCase(),i(e.url,e.params,e.paramsSerializer),!0),p.timeout=e.timeout,p[h]=function(){if(p&&(4===p.readyState||v)&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in p?a(p.getAllResponseHeaders()):null,r={data:e.responseType&&"text"!==e.responseType?p.response:p.responseText,status:1223===p.status?204:p.status,statusText:1223===p.status?"No Content":p.statusText,headers:n,config:e,request:p};o(t,l,r),p=null}},p.onerror=function(){l(c("Network Error",e,null,p)),p=null},p.ontimeout=function(){l(c("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",p)),p=null},r.isStandardBrowserEnv()){var y=n(18),b=(e.withCredentials||s(e.url))&&e.xsrfCookieName?y.read(e.xsrfCookieName):void 0;b&&(f[e.xsrfHeaderName]=b)}if("setRequestHeader"in p&&r.forEach(f,function(e,t){void 0===d&&"content-type"===t.toLowerCase()?delete f[t]:p.setRequestHeader(t,e)}),e.withCredentials&&(p.withCredentials=!0),e.responseType)try{p.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&p.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&p.upload&&p.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then(function(e){p&&(p.abort(),l(e),p=null)}),void 0===d&&(d=null),p.send(d)})}},function(e,t,n){"use strict";var r=n(12);e.exports=function(e,t,n){var o=n.config.validateStatus;n.status&&o&&!o(n.status)?t(r("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},function(e,t,n){"use strict";var r=n(13);e.exports=function(e,t,n,o,i){var a=new Error(e);return r(a,t,n,o,i)}},function(e,t,n){"use strict";e.exports=function(e,t,n,r,o){return e.config=t,n&&(e.code=n),e.request=r,e.response=o,e}},function(e,t,n){"use strict";var r=n(3);function o(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var i;if(n)i=n(t);else if(r.isURLSearchParams(t))i=t.toString();else{var a=[];r.forEach(t,function(e,t){null!=e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),a.push(o(t)+"="+o(e))}))}),i=a.join("&")}return i&&(e+=(-1===e.indexOf("?")?"?":"&")+i),e}},function(e,t,n){"use strict";var r=n(3),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,i,a={};return e?(r.forEach(e.split("\n"),function(e){if(i=e.indexOf(":"),t=r.trim(e.substr(0,i)).toLowerCase(),n=r.trim(e.substr(i+1)),t){if(a[t]&&o.indexOf(t)>=0)return;a[t]="set-cookie"===t?(a[t]?a[t]:[]).concat([n]):a[t]?a[t]+", "+n:n}}),a):a}},function(e,t,n){"use strict";var r=n(3);e.exports=r.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function o(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=o(window.location.href),function(t){var n=r.isString(t)?o(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0}},function(e,t,n){"use strict";var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function o(){this.message="String contains an invalid character"}o.prototype=new Error,o.prototype.code=5,o.prototype.name="InvalidCharacterError",e.exports=function(e){for(var t,n,i=String(e),a="",s=0,c=r;i.charAt(0|s)||(c="=",s%1);a+=c.charAt(63&t>>8-s%1*8)){if((n=i.charCodeAt(s+=.75))>255)throw new o;t=t<<8|n}return a}},function(e,t,n){"use strict";var r=n(3);e.exports=r.isStandardBrowserEnv()?{write:function(e,t,n,o,i,a){var s=[];s.push(e+"="+encodeURIComponent(t)),r.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),r.isString(o)&&s.push("path="+o),r.isString(i)&&s.push("domain="+i),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},function(e,t,n){"use strict";var r=n(3);function o(){this.handlers=[]}o.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){r.forEach(this.handlers,function(t){null!==t&&e(t)})},e.exports=o},function(e,t,n){"use strict";var r=n(3),o=n(21),i=n(22),a=n(7),s=n(23),c=n(24);function u(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return u(e),e.baseURL&&!s(e.url)&&(e.url=c(e.baseURL,e.url)),e.headers=e.headers||{},e.data=o(e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),r.forEach(["delete","get","head","post","put","patch","common"],function(t){delete e.headers[t]}),(e.adapter||a.adapter)(e).then(function(t){return u(e),t.data=o(t.data,t.headers,e.transformResponse),t},function(t){return i(t)||(u(e),t&&t.response&&(t.response.data=o(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)})}},function(e,t,n){"use strict";var r=n(3);e.exports=function(e,t,n){return r.forEach(n,function(n){e=n(e,t)}),e}},function(e,t,n){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},function(e,t,n){"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},function(e,t,n){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},function(e,t,n){"use strict";function r(e){this.message=e}r.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},r.prototype.__CANCEL__=!0,e.exports=r},function(e,t,n){"use strict";var r=n(25);function o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(e){t=e});var n=this;e(function(e){n.reason||(n.reason=new r(e),t(n.reason))})}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.source=function(){var e;return{token:new o(function(t){e=t}),cancel:e}},e.exports=o},function(e,t,n){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},function(e,t,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0});var o=n(29),i=function(){function e(e){this.getSignature=e.getSignature}return e.prototype.upload=function(e){e=r({getSignature:this.getSignature},e);var t=new o.default(e);return t.start(),t},e}();t.default=i},function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},a=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(a,s)}c((r=r.apply(e,t||[])).next())})},s=this&&this.__generator||function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}};Object.defineProperty(t,"__esModule",{value:!0});var c,u=n(30),l=n(33),d=n(52),f=n(1),p=n(53);!function(e){e.video_progress="video_progress",e.media_progress="media_progress",e.video_upload="video_upload",e.media_upload="media_upload",e.cover_progress="cover_progress",e.cover_upload="cover_upload"}(c=t.UploaderEvent||(t.UploaderEvent={}));var h=function(e){function t(t){var n=e.call(this)||this;return n.sessionName="",n.applyRequestTimeout=5e3,n.applyRequestRetryCount=3,n.commitRequestTimeout=5e3,n.commitRequestRetryCount=3,n.retryDelay=1e3,n.validateInitParams(t),n.videoFile=t.mediaFile||t.videoFile,n.getSignature=t.getSignature,n.videoName=t.mediaName||t.videoName,n.coverFile=t.coverFile,n.fileId=t.fileId,n.genFileInfo(),n}return o(t,e),t.prototype.setStorage=function(e,t){if(e){var n="webugc_"+u(e);try{localStorage.setItem(n,t)}catch(e){}}},t.prototype.getStorage=function(e){if(e){var t="webugc_"+u(e),n=null;try{n=localStorage.getItem(t)}catch(e){}return n}},t.prototype.delStorage=function(e){if(e){var t="webugc_"+u(e);try{localStorage.removeItem(t)}catch(e){}}},t.prototype.validateInitParams=function(e){if(!p.default.isFunction(e.getSignature))throw new Error("getSignature must be a function");if(e.videoFile&&!p.default.isFile(e.videoFile))throw new Error("videoFile must be a File")},t.prototype.genFileInfo=function(){var e=this.videoFile;if(e){var t=e.name.lastIndexOf("."),n="";if(this.videoName){if(!p.default.isString(this.videoName))throw new Error("mediaName must be a string");if(/[:*?<>\"\\\/|]/g.test(this.videoName))throw new Error('Cant use these chars in filename: \\ / : * ? " < > |');n=this.videoName}else n=e.name.substring(0,t);this.videoInfo={name:n,type:e.name.substring(t+1).toLowerCase(),size:e.size},this.sessionName+=e.name+"_"+e.size+";"}var r=this.coverFile;if(r){var o=r.name,i=o.lastIndexOf(".");this.coverInfo={name:o.substring(0,i),type:o.substring(i+1).toLowerCase(),size:r.size},this.sessionName+=r.name+"_"+r.size+";"}},t.prototype.applyUploadUGC=function(e,t){return void 0===t&&(t=0),a(this,void 0,void 0,function(){function n(n){return a(this,void 0,void 0,function(){return s(this,function(o){switch(o.label){case 0:if(r.delStorage(r.sessionName),r.applyRequestRetryCount==t){if(n)throw n;throw new Error("apply upload failed")}return[4,p.default.delay(r.retryDelay)];case 1:return o.sent(),[2,r.applyUploadUGC(e,t+1)]}})})}var r,o,i,c,u,l,d,h,v;return s(this,function(t){switch(t.label){case 0:if(r=this,i=this.videoInfo,c=this.coverInfo,u=this.getStorage(this.sessionName))o={signature:e,vodSessionKey:u};else if(i)o={signature:e,videoName:i.name,videoType:i.type,videoSize:i.size},c&&(o.coverName=c.name,o.coverType=c.type,o.coverSize=c.size);else{if(!this.fileId||!c)throw"Wrong params, please check and try again";o={signature:e,fileId:this.fileId,coverName:c.name,coverType:c.type,coverSize:c.size}}t.label=1;case 1:return t.trys.push([1,3,,4]),[4,f.default.post("https://vod2.qcloud.com/v3/index.php?Action=ApplyUploadUGC",o,{timeout:this.applyRequestTimeout,withCredentials:!1})];case 2:return l=t.sent(),[3,4];case 3:return t.sent(),[2,n()];case 4:return 0==(d=l.data).code?(h=d.data.vodSessionKey,this.setStorage(this.sessionName,h),[2,d.data]):((v=new Error(d.message)).code=d.code,[2,n(v)])}})})},t.prototype.uploadToCos=function(e){return a(this,void 0,void 0,function(){var t,n,r,o,u,d,f;return s(this,function(h){switch(h.label){case 0:return t=this,n={bucket:e.storageBucket+"-"+e.storageAppId,region:e.storageRegionV5},r=new l({getAuthorization:function(e,n){return a(this,void 0,void 0,function(){var e,r;return s(this,function(o){switch(o.label){case 0:return[4,t.getSignature()];case 1:return e=o.sent(),[4,t.applyUploadUGC(e)];case 2:return r=o.sent(),n({TmpSecretId:r.tempCertificate.secretId,TmpSecretKey:r.tempCertificate.secretKey,XCosSecurityToken:r.tempCertificate.token,ExpiredTime:r.tempCertificate.expiredTime}),[2]}})})}}),this.cos=r,o=[],this.videoFile&&(u=i({},n,{file:this.videoFile,key:e.video.storagePath,onProgress:function(e){t.emit(c.video_progress,e),t.emit(c.media_progress,e)},onUpload:function(e){t.emit(c.video_upload,e),t.emit(c.media_upload,e)},TaskReady:function(e){t.taskId=e}}),o.push(u)),this.coverFile&&(d=i({},n,{file:this.coverFile,key:e.cover.storagePath,onProgress:function(e){t.emit(c.cover_progress,e)},onUpload:function(e){t.emit(c.cover_upload,e)},TaskReady:p.default.noop}),o.push(d)),f=o.map(function(e){return new Promise(function(n,o){r.sliceUploadFile({Bucket:e.bucket,Region:e.region,Key:e.key,Body:e.file,TaskReady:e.TaskReady,onProgress:e.onProgress},function(r,i){if(!r)return e.onUpload(i),n();t.delStorage(t.sessionName),o(r)})})}),[4,Promise.all(f)];case 1:return[2,h.sent()]}})})},t.prototype.commitUploadUGC=function(e,t,n){return void 0===n&&(n=0),a(this,void 0,void 0,function(){function r(){return a(this,void 0,void 0,function(){return s(this,function(r){switch(r.label){case 0:if(o.commitRequestRetryCount==n)throw new Error("commit upload failed");return[4,p.default.delay(o.retryDelay)];case 1:return r.sent(),[2,o.commitUploadUGC(e,t,n+1)]}})})}var o,i,c;return s(this,function(n){switch(n.label){case 0:o=this,this.delStorage(this.sessionName),n.label=1;case 1:return n.trys.push([1,3,,4]),[4,f.default.post("https://vod2.qcloud.com/v3/index.php?Action=CommitUploadUGC",{signature:e,vodSessionKey:t},{timeout:this.commitRequestTimeout,withCredentials:!1})];case 2:return i=n.sent(),[3,4];case 3:return n.sent(),[2,r()];case 4:return 0==(c=i.data).code?[2,c.data]:[2,r()]}})})},t.prototype.start=function(){this.donePromise=this._start()},t.prototype._start=function(){return a(this,void 0,void 0,function(){var e,t,n;return s(this,function(r){switch(r.label){case 0:return[4,this.getSignature()];case 1:return e=r.sent(),[4,this.applyUploadUGC(e)];case 2:return t=r.sent(),[4,this.uploadToCos(t)];case 3:return r.sent(),[4,this.getSignature()];case 4:return n=r.sent(),[4,this.commitUploadUGC(n,t.vodSessionKey)];case 5:return[2,r.sent()]}})})},t.prototype.done=function(){return this.donePromise},t.prototype.cancel=function(){this.cos.cancelTask(this.taskId)},t}(d.EventEmitter);t.default=h},function(module,exports,__webpack_require__){(function(process,global){var __WEBPACK_AMD_DEFINE_RESULT__;
/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.6.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2017
 * @license MIT
 */
/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.6.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2017
 * @license MIT
 */
!function(){"use strict";var root="object"==typeof window?window:{},NODE_JS=!root.JS_SHA1_NO_NODE_JS&&"object"==typeof process&&process.versions&&process.versions.node;NODE_JS&&(root=global);var COMMON_JS=!root.JS_SHA1_NO_COMMON_JS&&"object"==typeof module&&module.exports,AMD=__webpack_require__(32),HEX_CHARS="0123456789abcdef".split(""),EXTRA=[-**********,8388608,32768,128],SHIFT=[24,16,8,0],OUTPUT_TYPES=["hex","array","digest","arrayBuffer"],blocks=[],createOutputMethod=function(e){return function(t){return new Sha1(!0).update(t)[e]()}},createMethod=function(){var e=createOutputMethod("hex");NODE_JS&&(e=nodeWrap(e)),e.create=function(){return new Sha1},e.update=function(t){return e.create().update(t)};for(var t=0;t<OUTPUT_TYPES.length;++t){var n=OUTPUT_TYPES[t];e[n]=createOutputMethod(n)}return e},nodeWrap=function(method){var crypto=eval("require('crypto')"),Buffer=eval("require('buffer').Buffer"),nodeMethod=function(e){if("string"==typeof e)return crypto.createHash("sha1").update(e,"utf8").digest("hex");if(e.constructor===ArrayBuffer)e=new Uint8Array(e);else if(void 0===e.length)return method(e);return crypto.createHash("sha1").update(new Buffer(e)).digest("hex")};return nodeMethod};function Sha1(e){e?(blocks[0]=blocks[16]=blocks[1]=blocks[2]=blocks[3]=blocks[4]=blocks[5]=blocks[6]=blocks[7]=blocks[8]=blocks[9]=blocks[10]=blocks[11]=blocks[12]=blocks[13]=blocks[14]=blocks[15]=0,this.blocks=blocks):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}Sha1.prototype.update=function(e){if(!this.finalized){var t="string"!=typeof e;t&&e.constructor===root.ArrayBuffer&&(e=new Uint8Array(e));for(var n,r,o=0,i=e.length||0,a=this.blocks;o<i;){if(this.hashed&&(this.hashed=!1,a[0]=this.block,a[16]=a[1]=a[2]=a[3]=a[4]=a[5]=a[6]=a[7]=a[8]=a[9]=a[10]=a[11]=a[12]=a[13]=a[14]=a[15]=0),t)for(r=this.start;o<i&&r<64;++o)a[r>>2]|=e[o]<<SHIFT[3&r++];else for(r=this.start;o<i&&r<64;++o)(n=e.charCodeAt(o))<128?a[r>>2]|=n<<SHIFT[3&r++]:n<2048?(a[r>>2]|=(192|n>>6)<<SHIFT[3&r++],a[r>>2]|=(128|63&n)<<SHIFT[3&r++]):n<55296||n>=57344?(a[r>>2]|=(224|n>>12)<<SHIFT[3&r++],a[r>>2]|=(128|n>>6&63)<<SHIFT[3&r++],a[r>>2]|=(128|63&n)<<SHIFT[3&r++]):(n=65536+((1023&n)<<10|1023&e.charCodeAt(++o)),a[r>>2]|=(240|n>>18)<<SHIFT[3&r++],a[r>>2]|=(128|n>>12&63)<<SHIFT[3&r++],a[r>>2]|=(128|n>>6&63)<<SHIFT[3&r++],a[r>>2]|=(128|63&n)<<SHIFT[3&r++]);this.lastByteIndex=r,this.bytes+=r-this.start,r>=64?(this.block=a[16],this.start=r-64,this.hash(),this.hashed=!0):this.start=r}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},Sha1.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[16]=this.block,e[t>>2]|=EXTRA[3&t],this.block=e[16],t>=56&&(this.hashed||this.hash(),e[0]=this.block,e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.hBytes<<3|this.bytes>>>29,e[15]=this.bytes<<3,this.hash()}},Sha1.prototype.hash=function(){var e,t,n=this.h0,r=this.h1,o=this.h2,i=this.h3,a=this.h4,s=this.blocks;for(e=16;e<80;++e)t=s[e-3]^s[e-8]^s[e-14]^s[e-16],s[e]=t<<1|t>>>31;for(e=0;e<20;e+=5)n=(t=(r=(t=(o=(t=(i=(t=(a=(t=n<<5|n>>>27)+(r&o|~r&i)+a+1518500249+s[e]<<0)<<5|a>>>27)+(n&(r=r<<30|r>>>2)|~n&o)+i+1518500249+s[e+1]<<0)<<5|i>>>27)+(a&(n=n<<30|n>>>2)|~a&r)+o+1518500249+s[e+2]<<0)<<5|o>>>27)+(i&(a=a<<30|a>>>2)|~i&n)+r+1518500249+s[e+3]<<0)<<5|r>>>27)+(o&(i=i<<30|i>>>2)|~o&a)+n+1518500249+s[e+4]<<0,o=o<<30|o>>>2;for(;e<40;e+=5)n=(t=(r=(t=(o=(t=(i=(t=(a=(t=n<<5|n>>>27)+(r^o^i)+a+1859775393+s[e]<<0)<<5|a>>>27)+(n^(r=r<<30|r>>>2)^o)+i+1859775393+s[e+1]<<0)<<5|i>>>27)+(a^(n=n<<30|n>>>2)^r)+o+1859775393+s[e+2]<<0)<<5|o>>>27)+(i^(a=a<<30|a>>>2)^n)+r+1859775393+s[e+3]<<0)<<5|r>>>27)+(o^(i=i<<30|i>>>2)^a)+n+1859775393+s[e+4]<<0,o=o<<30|o>>>2;for(;e<60;e+=5)n=(t=(r=(t=(o=(t=(i=(t=(a=(t=n<<5|n>>>27)+(r&o|r&i|o&i)+a-1894007588+s[e]<<0)<<5|a>>>27)+(n&(r=r<<30|r>>>2)|n&o|r&o)+i-1894007588+s[e+1]<<0)<<5|i>>>27)+(a&(n=n<<30|n>>>2)|a&r|n&r)+o-1894007588+s[e+2]<<0)<<5|o>>>27)+(i&(a=a<<30|a>>>2)|i&n|a&n)+r-1894007588+s[e+3]<<0)<<5|r>>>27)+(o&(i=i<<30|i>>>2)|o&a|i&a)+n-1894007588+s[e+4]<<0,o=o<<30|o>>>2;for(;e<80;e+=5)n=(t=(r=(t=(o=(t=(i=(t=(a=(t=n<<5|n>>>27)+(r^o^i)+a-899497514+s[e]<<0)<<5|a>>>27)+(n^(r=r<<30|r>>>2)^o)+i-899497514+s[e+1]<<0)<<5|i>>>27)+(a^(n=n<<30|n>>>2)^r)+o-899497514+s[e+2]<<0)<<5|o>>>27)+(i^(a=a<<30|a>>>2)^n)+r-899497514+s[e+3]<<0)<<5|r>>>27)+(o^(i=i<<30|i>>>2)^a)+n-899497514+s[e+4]<<0,o=o<<30|o>>>2;this.h0=this.h0+n<<0,this.h1=this.h1+r<<0,this.h2=this.h2+o<<0,this.h3=this.h3+i<<0,this.h4=this.h4+a<<0},Sha1.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,r=this.h3,o=this.h4;return HEX_CHARS[e>>28&15]+HEX_CHARS[e>>24&15]+HEX_CHARS[e>>20&15]+HEX_CHARS[e>>16&15]+HEX_CHARS[e>>12&15]+HEX_CHARS[e>>8&15]+HEX_CHARS[e>>4&15]+HEX_CHARS[15&e]+HEX_CHARS[t>>28&15]+HEX_CHARS[t>>24&15]+HEX_CHARS[t>>20&15]+HEX_CHARS[t>>16&15]+HEX_CHARS[t>>12&15]+HEX_CHARS[t>>8&15]+HEX_CHARS[t>>4&15]+HEX_CHARS[15&t]+HEX_CHARS[n>>28&15]+HEX_CHARS[n>>24&15]+HEX_CHARS[n>>20&15]+HEX_CHARS[n>>16&15]+HEX_CHARS[n>>12&15]+HEX_CHARS[n>>8&15]+HEX_CHARS[n>>4&15]+HEX_CHARS[15&n]+HEX_CHARS[r>>28&15]+HEX_CHARS[r>>24&15]+HEX_CHARS[r>>20&15]+HEX_CHARS[r>>16&15]+HEX_CHARS[r>>12&15]+HEX_CHARS[r>>8&15]+HEX_CHARS[r>>4&15]+HEX_CHARS[15&r]+HEX_CHARS[o>>28&15]+HEX_CHARS[o>>24&15]+HEX_CHARS[o>>20&15]+HEX_CHARS[o>>16&15]+HEX_CHARS[o>>12&15]+HEX_CHARS[o>>8&15]+HEX_CHARS[o>>4&15]+HEX_CHARS[15&o]},Sha1.prototype.toString=Sha1.prototype.hex,Sha1.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,r=this.h3,o=this.h4;return[e>>24&255,e>>16&255,e>>8&255,255&e,t>>24&255,t>>16&255,t>>8&255,255&t,n>>24&255,n>>16&255,n>>8&255,255&n,r>>24&255,r>>16&255,r>>8&255,255&r,o>>24&255,o>>16&255,o>>8&255,255&o]},Sha1.prototype.array=Sha1.prototype.digest,Sha1.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(20),t=new DataView(e);return t.setUint32(0,this.h0),t.setUint32(4,this.h1),t.setUint32(8,this.h2),t.setUint32(12,this.h3),t.setUint32(16,this.h4),e};var exports=createMethod();COMMON_JS?module.exports=exports:(root.sha1=exports,AMD&&(__WEBPACK_AMD_DEFINE_RESULT__=function(){return exports}.call(exports,__webpack_require__,exports,module),void 0===__WEBPACK_AMD_DEFINE_RESULT__||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)))}()}).call(this,__webpack_require__(8),__webpack_require__(31))},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t){(function(t){e.exports=t}).call(this,{})},function(e,t,n){var r=n(34);e.exports=r},function(e,t,n){"use strict";var r=n(35),o=n(43),i=n(44),a=n(45),s=n(50),c={AppId:"",SecretId:"",SecretKey:"",XCosSecurityToken:"",FileParallelLimit:3,ChunkParallelLimit:3,ChunkRetryTimes:3,ChunkSize:1048576,SliceSize:1048576,CopyChunkParallelLimit:20,CopyChunkSize:10485760,CopySliceSize:10485760,MaxPartNumber:1e4,ProgressInterval:1e3,UploadQueueSize:1e4,Domain:"",ServiceDomain:"",Protocol:"",CompatibilityMode:!1,ForcePathStyle:!1,UseRawKey:!1,Timeout:0,CorrectClockSkew:!0,SystemClockOffset:0,UploadCheckContentMd5:!1,UploadIdCacheLimit:50},u=function(e){this.options=r.extend(r.clone(c),e||{}),this.options.FileParallelLimit=Math.max(1,this.options.FileParallelLimit),this.options.ChunkParallelLimit=Math.max(1,this.options.ChunkParallelLimit),this.options.ChunkRetryTimes=Math.max(0,this.options.ChunkRetryTimes),this.options.ChunkSize=Math.max(1048576,this.options.ChunkSize),this.options.CopyChunkParallelLimit=Math.max(1,this.options.CopyChunkParallelLimit),this.options.CopyChunkSize=Math.max(1048576,this.options.CopyChunkSize),this.options.CopySliceSize=Math.max(0,this.options.CopySliceSize),this.options.MaxPartNumber=Math.max(1024,Math.min(1e4,this.options.MaxPartNumber)),this.options.Timeout=Math.max(0,this.options.Timeout),this.options.AppId&&console.warn('warning: AppId has been deprecated, Please put it at the end of parameter Bucket(E.g: "test-1250000000").'),o.init(this),i.init(this)};a.init(u,i),s.init(u,i),u.getAuthorization=r.getAuth,u.version="0.5.8",e.exports=u},function(e,t,n){"use strict";(function(t){var r=n(36),o=n(37),i=n(38),a=n(42);function s(e){return encodeURIComponent(e).replace(/!/g,"%21").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}var c=function(){},u=function(e){var t={};for(var n in e)e.hasOwnProperty(n)&&void 0!==e[n]&&null!==e[n]&&(t[n]=e[n]);return t};function l(e){return h(e,function(e){return"object"==typeof e?l(e):e})}function d(e,t){return p(t,function(n,r){e[r]=t[r]}),e}function f(e){return e instanceof Array}function p(e,t){for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)}function h(e,t){var n=f(e)?[]:{};for(var r in e)e.hasOwnProperty(r)&&(n[r]=t(e[r],r));return n}var v=function(e,t){if(t=d({},t),"getAuth"!==e&&"getV4Auth"!==e&&"getObjectUrl"!==e){var n=t.Headers||{};if(t&&"object"==typeof t){!function(){for(var e in t)t.hasOwnProperty(e)&&e.indexOf("x-cos-")>-1&&(n[e]=t[e])}();g.each({"x-cos-mfa":"MFA","Content-MD5":"ContentMD5","Content-Length":"ContentLength","Content-Type":"ContentType",Expect:"Expect",Expires:"Expires","Cache-Control":"CacheControl","Content-Disposition":"ContentDisposition","Content-Encoding":"ContentEncoding",Range:"Range","If-Modified-Since":"IfModifiedSince","If-Unmodified-Since":"IfUnmodifiedSince","If-Match":"IfMatch","If-None-Match":"IfNoneMatch","x-cos-copy-source":"CopySource","x-cos-copy-source-Range":"CopySourceRange","x-cos-metadata-directive":"MetadataDirective","x-cos-copy-source-If-Modified-Since":"CopySourceIfModifiedSince","x-cos-copy-source-If-Unmodified-Since":"CopySourceIfUnmodifiedSince","x-cos-copy-source-If-Match":"CopySourceIfMatch","x-cos-copy-source-If-None-Match":"CopySourceIfNoneMatch","x-cos-acl":"ACL","x-cos-grant-read":"GrantRead","x-cos-grant-write":"GrantWrite","x-cos-grant-full-control":"GrantFullControl","x-cos-grant-read-acp":"GrantReadAcp","x-cos-grant-write-acp":"GrantWriteAcp","x-cos-storage-class":"StorageClass","x-cos-server-side-encryption-customer-algorithm":"SSECustomerAlgorithm","x-cos-server-side-encryption-customer-key":"SSECustomerKey","x-cos-server-side-encryption-customer-key-MD5":"SSECustomerKeyMD5","x-cos-server-side-encryption":"ServerSideEncryption","x-cos-server-side-encryption-cos-kms-key-id":"SSEKMSKeyId","x-cos-server-side-encryption-context":"SSEContext"},function(e,r){void 0!==t[e]&&(n[r]=t[e])}),t.Headers=u(n)}}return t},m=function(e){return Date.now()+(e||0)},g={noop:c,formatParams:v,apiWrapper:function(e,t){return function(n,r){"function"==typeof n&&(r=n,n={}),n=v(e,n);var o=function(e){return e&&e.headers&&(e.headers["x-cos-version-id"]&&(e.VersionId=e.headers["x-cos-version-id"]),e.headers["x-cos-delete-marker"]&&(e.DeleteMarker=e.headers["x-cos-delete-marker"])),e},i=function(e,t){r&&r(o(e),o(t))};if("getService"!==e&&"abortUploadTask"!==e){var a;if(a=function(e,t){var n=t.Bucket,r=t.Region,o=t.Key;if(e.indexOf("Bucket")>-1||"deleteMultipleObject"===e||"multipartList"===e||"listObjectVersions"===e){if(!n)return"Bucket";if(!r)return"Region"}else if(e.indexOf("Object")>-1||e.indexOf("multipart")>-1||"sliceUploadFile"===e||"abortUploadTask"===e){if(!n)return"Bucket";if(!r)return"Region";if(!o)return"Key"}return!1}(e,n))return void i({error:"missing param "+a});if(n.Region){if(n.Region.indexOf("cos.")>-1)return void i({error:'param Region should not be start with "cos."'});if(!/^([a-z\d-]+)$/.test(n.Region))return void i({error:"Region format error."});this.options.CompatibilityMode||-1!==n.Region.indexOf("-")||"yfb"===n.Region||"default"===n.Region||console.warn("warning: param Region format error, find help here: https://cloud.tencent.com/document/product/436/6224")}if(n.Bucket){if(!/^([a-z\d-]+)-(\d+)$/.test(n.Bucket))if(n.AppId)n.Bucket=n.Bucket+"-"+n.AppId;else{if(!this.options.AppId)return void i({error:'Bucket should format as "test-1250000000".'});n.Bucket=n.Bucket+"-"+this.options.AppId}n.AppId&&(console.warn('warning: AppId has been deprecated, Please put it at the end of parameter Bucket(E.g Bucket:"test-1250000000" ).'),delete n.AppId)}!this.options.UseRawKey&&n.Key&&"/"===n.Key.substr(0,1)&&(n.Key=n.Key.substr(1))}var s=t.call(this,n,i);if("getAuth"===e||"getObjectUrl"===e)return s}},xml2json:i,json2xml:a,md5:r,clearKey:u,getFileMd5:function(e,t){!function(e,t){var n,r=new FileReader;FileReader.prototype.readAsBinaryString?(n=FileReader.prototype.readAsBinaryString,r.onload=function(){t(this.result)}):FileReader.prototype.readAsArrayBuffer?n=function(e){var n="",r=new FileReader;r.onload=function(e){for(var o=new Uint8Array(r.result),i=o.byteLength,a=0;a<i;a++)n+=String.fromCharCode(o[a]);t(n)},r.readAsArrayBuffer(e)}:console.error("FileReader not support readAsBinaryString"),n.call(r,e)}(e,function(e){var n=r(e,!0);t(null,n)})},binaryBase64:function(e){var t,n,r,o="";for(t=0,n=e.length/2;t<n;t++)r=parseInt(e[2*t]+e[2*t+1],16),o+=String.fromCharCode(r);return btoa(o)},extend:d,isArray:f,isInArray:function(e,t){for(var n=!1,r=0;r<e.length;r++)if(t===e[r]){n=!0;break}return n},each:p,map:h,filter:function(e,t){var n=f(e),r=n?[]:{};for(var o in e)e.hasOwnProperty(o)&&t(e[o],o)&&(n?r.push(e[o]):r[o]=e[o]);return r},clone:l,uuid:function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},camSafeUrlEncode:s,throttleOnProgress:function(e,t){var n,r,o=this,i=0,a=0,s=Date.now();function c(){if(r=0,t&&"function"==typeof t){n=Date.now();var o,c=Math.max(0,Math.round((a-i)/((n-s)/1e3)*100)/100);o=0===a&&0===e?1:Math.round(a/e*100)/100||0,s=n,i=a;try{t({loaded:a,total:e,speed:c,percent:o})}catch(e){}}}return function(t,n){if(t&&(a=t.loaded,e=t.total),n)clearTimeout(r),c();else{if(r)return;r=setTimeout(c,o.options.ProgressInterval)}}},getFileSize:function(e,t,n){var r;"string"==typeof t.Body&&(t.Body=new Blob([t.Body],{type:"text/plain"})),t.Body&&(t.Body instanceof Blob||"[object File]"===t.Body.toString()||"[object Blob]"===t.Body.toString())?(r=t.Body.size,t.ContentLength=r,n(null,r)):n({error:"params body format error, Only allow File|Blob|String."})},getSkewTime:m,getAuth:function(e){var t,n=(e=e||{}).SecretId,r=e.SecretKey,i=(e.method||e.Method||"get").toLowerCase(),a=l(e.Query||e.params||{}),c=l(e.Headers||e.headers||{}),u=e.Key||"";if(e.UseRawKey?t=e.Pathname||e.pathname||"/"+u:0!==(t=e.Pathname||e.pathname||u).indexOf("/")&&(t="/"+t),!n)return console.error("missing param SecretId");if(!r)return console.error("missing param SecretKey");var d=function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t.sort(function(e,t){return(e=e.toLowerCase())===(t=t.toLowerCase())?0:e>t?1:-1})},f=function(e){var t,n,r,o=[],i=d(e);for(t=0;t<i.length;t++)r=void 0===e[n=i[t]]||null===e[n]?"":""+e[n],n=s(n=n.toLowerCase()),r=s(r)||"",o.push(n+"="+r);return o.join("&")},p=Math.round(m(e.SystemClockOffset)/1e3)-1,h=p,v=e.Expires||e.expires,g=n,y=p+";"+(h+=void 0===v?900:1*v||0),b=p+";"+h,C=d(c).join(";").toLowerCase(),_=d(a).join(";").toLowerCase(),w=o.HmacSHA1(b,r).toString(),x=[i,t,f(a),f(c),""].join("\n"),S=["sha1",y,o.SHA1(x).toString(),""].join("\n");return["q-sign-algorithm=sha1","q-ak="+g,"q-sign-time="+y,"q-key-time="+b,"q-header-list="+C,"q-url-param-list="+_,"q-signature="+o.HmacSHA1(S,w).toString()].join("&")},isBrowser:!0};g.localStorage=t.localStorage;var y,b,C,_,w,x=(w=function(e,t){e=e.split("."),t=t.split(".");for(var n=0;n<t.length;n++)if(e[n]!==t[n])return parseInt(e[n])>parseInt(t[n])?1:-1;return 0},y=navigator.userAgent,b=(y.match(/Chrome\/([.\d]+)/)||[])[1],C=(y.match(/QBCore\/([.\d]+)/)||[])[1],_=(y.match(/QQBrowser\/([.\d]+)/)||[])[1],b&&w(b,"53.0.2785.116")<0&&C&&w(C,"3.53.991.400")<0&&_&&w(_,"9.0.2524.400")<=0||!1);g.fileSlice=function(e,t,n,r,o){var i;if(e.slice?i=e.slice(t,n):e.mozSlice?i=e.mozSlice(t,n):e.webkitSlice&&(i=e.webkitSlice(t,n)),r&&x){var a=new FileReader;a.onload=function(e){i=null,o(new Blob([a.result]))},a.readAsArrayBuffer(i)}else o(i)},g.getFileUUID=function(e,t){return e.name&&e.size&&e.lastModifiedDate&&t?g.md5([e.name,e.size,e.lastModifiedDate,t].join("::")):null},g.getBodyMd5=function(e,n,r){r=r||c,e?"string"==typeof n?r(g.md5(n,!0)):n instanceof t.Blob?g.getFileMd5(n,function(e,t){r(t)}):r():r()},e.exports=g}).call(this,n(31))},function(e,t){function n(e,t){var n=e[0],r=e[1],c=e[2],u=e[3];n=o(n,r,c,u,t[0],7,-680876936),u=o(u,n,r,c,t[1],12,-389564586),c=o(c,u,n,r,t[2],17,606105819),r=o(r,c,u,n,t[3],22,-1044525330),n=o(n,r,c,u,t[4],7,-176418897),u=o(u,n,r,c,t[5],12,1200080426),c=o(c,u,n,r,t[6],17,-1473231341),r=o(r,c,u,n,t[7],22,-45705983),n=o(n,r,c,u,t[8],7,1770035416),u=o(u,n,r,c,t[9],12,-1958414417),c=o(c,u,n,r,t[10],17,-42063),r=o(r,c,u,n,t[11],22,-1990404162),n=o(n,r,c,u,t[12],7,1804603682),u=o(u,n,r,c,t[13],12,-40341101),c=o(c,u,n,r,t[14],17,-1502002290),n=i(n,r=o(r,c,u,n,t[15],22,1236535329),c,u,t[1],5,-165796510),u=i(u,n,r,c,t[6],9,-1069501632),c=i(c,u,n,r,t[11],14,643717713),r=i(r,c,u,n,t[0],20,-373897302),n=i(n,r,c,u,t[5],5,-701558691),u=i(u,n,r,c,t[10],9,38016083),c=i(c,u,n,r,t[15],14,-660478335),r=i(r,c,u,n,t[4],20,-405537848),n=i(n,r,c,u,t[9],5,568446438),u=i(u,n,r,c,t[14],9,-1019803690),c=i(c,u,n,r,t[3],14,-187363961),r=i(r,c,u,n,t[8],20,1163531501),n=i(n,r,c,u,t[13],5,-1444681467),u=i(u,n,r,c,t[2],9,-51403784),c=i(c,u,n,r,t[7],14,1735328473),n=a(n,r=i(r,c,u,n,t[12],20,-1926607734),c,u,t[5],4,-378558),u=a(u,n,r,c,t[8],11,-2022574463),c=a(c,u,n,r,t[11],16,1839030562),r=a(r,c,u,n,t[14],23,-35309556),n=a(n,r,c,u,t[1],4,-1530992060),u=a(u,n,r,c,t[4],11,1272893353),c=a(c,u,n,r,t[7],16,-155497632),r=a(r,c,u,n,t[10],23,-1094730640),n=a(n,r,c,u,t[13],4,681279174),u=a(u,n,r,c,t[0],11,-358537222),c=a(c,u,n,r,t[3],16,-722521979),r=a(r,c,u,n,t[6],23,76029189),n=a(n,r,c,u,t[9],4,-640364487),u=a(u,n,r,c,t[12],11,-421815835),c=a(c,u,n,r,t[15],16,530742520),n=s(n,r=a(r,c,u,n,t[2],23,-995338651),c,u,t[0],6,-198630844),u=s(u,n,r,c,t[7],10,1126891415),c=s(c,u,n,r,t[14],15,-1416354905),r=s(r,c,u,n,t[5],21,-57434055),n=s(n,r,c,u,t[12],6,1700485571),u=s(u,n,r,c,t[3],10,-1894986606),c=s(c,u,n,r,t[10],15,-1051523),r=s(r,c,u,n,t[1],21,-2054922799),n=s(n,r,c,u,t[8],6,1873313359),u=s(u,n,r,c,t[15],10,-30611744),c=s(c,u,n,r,t[6],15,-1560198380),r=s(r,c,u,n,t[13],21,1309151649),n=s(n,r,c,u,t[4],6,-145523070),u=s(u,n,r,c,t[11],10,-1120210379),c=s(c,u,n,r,t[2],15,718787259),r=s(r,c,u,n,t[9],21,-343485551),e[0]=f(n,e[0]),e[1]=f(r,e[1]),e[2]=f(c,e[2]),e[3]=f(u,e[3])}function r(e,t,n,r,o,i){return t=f(f(t,e),f(r,i)),f(t<<o|t>>>32-o,n)}function o(e,t,n,o,i,a,s){return r(t&n|~t&o,e,t,i,a,s)}function i(e,t,n,o,i,a,s){return r(t&o|n&~o,e,t,i,a,s)}function a(e,t,n,o,i,a,s){return r(t^n^o,e,t,i,a,s)}function s(e,t,n,o,i,a,s){return r(n^(t|~o),e,t,i,a,s)}function c(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return n}var u="0123456789abcdef".split("");function l(e){for(var t="",n=0;n<4;n++)t+=u[e>>8*n+4&15]+u[e>>8*n&15];return t}function d(e,t){return t||(e=function(e){e=e.replace(/\r\n/g,"\n");for(var t="",n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?t+=String.fromCharCode(r):r>127&&r<2048?(t+=String.fromCharCode(r>>6|192),t+=String.fromCharCode(63&r|128)):(t+=String.fromCharCode(r>>12|224),t+=String.fromCharCode(r>>6&63|128),t+=String.fromCharCode(63&r|128))}return t}(e)),function(e){for(var t=0;t<e.length;t++)e[t]=l(e[t]);return e.join("")}(function(e){var t,r=e.length,o=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=e.length;t+=64)n(o,c(e.substring(t-64,t)));e=e.substring(t-64);var i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<e.length;t++)i[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(i[t>>2]|=128<<(t%4<<3),t>55)for(n(o,i),t=0;t<16;t++)i[t]=0;return i[14]=8*r,n(o,i),o}(e))}var f=function(e,t){return e+t&4294967295};"5d41402abc4b2a76b9719d911017c592"!=d("hello")&&(f=function(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}),e.exports=d},function(e,t,n){var r,o,i,a,s,c,u,l=l||function(e,t){var n={},r=n.lib={},o=function(){},i=r.Base={extend:function(e){o.prototype=this;var t=new o;return e&&t.mixIn(e),t.hasOwnProperty("init")||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},a=r.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||c).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes;if(e=e.sigBytes,this.clamp(),r%4)for(var o=0;o<e;o++)t[r+o>>>2]|=(n[o>>>2]>>>24-o%4*8&255)<<24-(r+o)%4*8;else if(65535<n.length)for(o=0;o<e;o+=4)t[r+o>>>2]=n[o>>>2];else t.push.apply(t,n);return this.sigBytes+=e,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n=[],r=0;r<t;r+=4)n.push(4294967296*e.random()|0);return new a.init(n,t)}}),s=n.enc={},c=s.Hex={stringify:function(e){var t=e.words;e=e.sigBytes;for(var n=[],r=0;r<e;r++){var o=t[r>>>2]>>>24-r%4*8&255;n.push((o>>>4).toString(16)),n.push((15&o).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new a.init(n,t/2)}},u=s.Latin1={stringify:function(e){var t=e.words;e=e.sigBytes;for(var n=[],r=0;r<e;r++)n.push(String.fromCharCode(t[r>>>2]>>>24-r%4*8&255));return n.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new a.init(n,t)}},l=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(u.stringify(e)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function(e){return u.parse(unescape(encodeURIComponent(e)))}},d=r.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new a.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,r=n.words,o=n.sigBytes,i=this.blockSize,s=o/(4*i);if(t=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*i,o=e.min(4*t,o),t){for(var c=0;c<t;c+=i)this._doProcessBlock(r,c);c=r.splice(0,t),n.sigBytes-=o}return new a.init(c,o)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});r.Hasher=d.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new f.HMAC.init(e,n).finalize(t)}}});var f=n.algo={};return n}(Math);o=(s=(r=l).lib).WordArray,i=s.Hasher,a=[],s=r.algo.SHA1=i.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],o=n[1],i=n[2],s=n[3],c=n[4],u=0;80>u;u++){if(16>u)a[u]=0|e[t+u];else{var l=a[u-3]^a[u-8]^a[u-14]^a[u-16];a[u]=l<<1|l>>>31}l=(r<<5|r>>>27)+c+a[u],l=20>u?l+(1518500249+(o&i|~o&s)):40>u?l+(1859775393+(o^i^s)):60>u?l+((o&i|o&s|i&s)-1894007588):l+((o^i^s)-899497514),c=s,s=i,i=o<<30|o>>>2,o=r,r=l}n[0]=n[0]+r|0,n[1]=n[1]+o|0,n[2]=n[2]+i|0,n[3]=n[3]+s|0,n[4]=n[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=Math.floor(n/4294967296),t[15+(r+64>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}}),r.SHA1=i._createHelper(s),r.HmacSHA1=i._createHmacHelper(s),function(){var e=l,t=e.enc.Utf8;e.algo.HMAC=e.lib.Base.extend({init:function(e,n){e=this._hasher=new e.init,"string"==typeof n&&(n=t.parse(n));var r=e.blockSize,o=4*r;n.sigBytes>o&&(n=e.finalize(n)),n.clamp();for(var i=this._oKey=n.clone(),a=this._iKey=n.clone(),s=i.words,c=a.words,u=0;u<r;u++)s[u]^=1549556828,c[u]^=909522486;i.sigBytes=a.sigBytes=o,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher;return e=t.finalize(e),t.reset(),t.finalize(this._oKey.clone().concat(e))}})}(),u=(c=l).lib.WordArray,c.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var o=[],i=0;i<n;i+=3)for(var a=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<n;s++)o.push(r.charAt(a>>>6*(3-s)&63));var c=r.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e){var t=e.length,n=this._map,r=n.charAt(64);if(r){var o=e.indexOf(r);-1!=o&&(t=o)}for(var i=[],a=0,s=0;s<t;s++)if(s%4){var c=n.indexOf(e.charAt(s-1))<<s%4*2,l=n.indexOf(e.charAt(s))>>>6-s%4*2;i[a>>>2]|=(c|l)<<24-a%4*8,a++}return u.create(i,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},e.exports=l},function(e,t,n){var r=n(39).DOMParser,o=function(){this.version="1.3.5";var e={mergeCDATA:!0,normalize:!0,stripElemPrefix:!0},t=new RegExp(/(?!xmlns)^.*:/);new RegExp(/^\s+|\s+$/g);return this.grokType=function(e){return/^\s*$/.test(e)?null:/^(?:true|false)$/i.test(e)?"true"===e.toLowerCase():isFinite(e)?parseFloat(e):e},this.parseString=function(e,t){if(e){var n=this.stringToXML(e);return n.getElementsByTagName("parsererror").length?null:this.parseXML(n,t)}return null},this.parseXML=function(n,r){for(var i in r)e[i]=r[i];var a={},s=0,c="";if(n.childNodes.length)for(var u,l,d,f=0;f<n.childNodes.length;f++)4===(u=n.childNodes.item(f)).nodeType?e.mergeCDATA&&(c+=u.nodeValue):3===u.nodeType?c+=u.nodeValue:1===u.nodeType&&(0===s&&(a={}),l=e.stripElemPrefix?u.nodeName.replace(t,""):u.nodeName,d=o.parseXML(u),a.hasOwnProperty(l)?(a[l].constructor!==Array&&(a[l]=[a[l]]),a[l].push(d)):(a[l]=d,s++));return Object.keys(a).length||(a=c||""),a},this.xmlToString=function(e){try{return e.xml?e.xml:(new XMLSerializer).serializeToString(e)}catch(e){return null}},this.stringToXML=function(e){try{var t=null;return window.DOMParser?t=(new r).parseFromString(e,"text/xml"):((t=new ActiveXObject("Microsoft.XMLDOM")).async=!1,t.loadXML(e),t)}catch(e){return null}},this}.call({});e.exports=function(e){return o.parseString(e)}},function(e,t,n){function r(e){this.options=e||{locator:{}}}function o(){this.cdata=!1}function i(e,t){t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber}function a(e){if(e)return"\n@"+(e.systemId||"")+"#[line:"+e.lineNumber+",col:"+e.columnNumber+"]"}function s(e,t,n){return"string"==typeof e?e.substr(t,n):e.length>=t+n||t?new java.lang.String(e,t,n)+"":e}function c(e,t){e.currentElement?e.currentElement.appendChild(t):e.doc.appendChild(t)}r.prototype.parseFromString=function(e,t){var n=this.options,r=new u,i=n.domBuilder||new o,s=n.errorHandler,c=n.locator,l=n.xmlns||{},d={lt:"<",gt:">",amp:"&",quot:'"',apos:"'"};return c&&i.setDocumentLocator(c),r.errorHandler=function(e,t,n){if(!e){if(t instanceof o)return t;e=t}var r={},i=e instanceof Function;function s(t){var o=e[t];!o&&i&&(o=2==e.length?function(n){e(t,n)}:e),r[t]=o&&function(e){o("[xmldom "+t+"]\t"+e+a(n))}||function(){}}return n=n||{},s("warning"),s("error"),s("fatalError"),r}(s,i,c),r.domBuilder=n.domBuilder||i,/\/x?html?$/.test(t)&&(d.nbsp=" ",d.copy="©",l[""]="http://www.w3.org/1999/xhtml"),l.xml=l.xml||"http://www.w3.org/XML/1998/namespace",e?r.parse(e,l,d):r.errorHandler.error("invalid doc source"),i.doc},o.prototype={startDocument:function(){this.doc=(new l).createDocument(null,null,null),this.locator&&(this.doc.documentURI=this.locator.systemId)},startElement:function(e,t,n,r){var o=this.doc,a=o.createElementNS(e,n||t),s=r.length;c(this,a),this.currentElement=a,this.locator&&i(this.locator,a);for(var u=0;u<s;u++){e=r.getURI(u);var l=r.getValue(u),d=(n=r.getQName(u),o.createAttributeNS(e,n));this.locator&&i(r.getLocator(u),d),d.value=d.nodeValue=l,a.setAttributeNode(d)}},endElement:function(e,t,n){var r=this.currentElement;r.tagName;this.currentElement=r.parentNode},startPrefixMapping:function(e,t){},endPrefixMapping:function(e){},processingInstruction:function(e,t){var n=this.doc.createProcessingInstruction(e,t);this.locator&&i(this.locator,n),c(this,n)},ignorableWhitespace:function(e,t,n){},characters:function(e,t,n){if(e=s.apply(this,arguments)){if(this.cdata)var r=this.doc.createCDATASection(e);else r=this.doc.createTextNode(e);this.currentElement?this.currentElement.appendChild(r):/^\s*$/.test(e)&&this.doc.appendChild(r),this.locator&&i(this.locator,r)}},skippedEntity:function(e){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(e){(this.locator=e)&&(e.lineNumber=0)},comment:function(e,t,n){e=s.apply(this,arguments);var r=this.doc.createComment(e);this.locator&&i(this.locator,r),c(this,r)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(e,t,n){var r=this.doc.implementation;if(r&&r.createDocumentType){var o=r.createDocumentType(e,t,n);this.locator&&i(this.locator,o),c(this,o)}},warning:function(e){console.warn("[xmldom warning]\t"+e,a(this.locator))},error:function(e){console.error("[xmldom error]\t"+e,a(this.locator))},fatalError:function(e){throw console.error("[xmldom fatalError]\t"+e,a(this.locator)),e}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,function(e){o.prototype[e]=function(){return null}});var u=n(40).XMLReader,l=t.DOMImplementation=n(41).DOMImplementation;t.XMLSerializer=n(41).XMLSerializer,t.DOMParser=r},function(e,t){var n=/[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,r=new RegExp("[\\-\\.0-9"+n.source.slice(1,-1)+"\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"),o=new RegExp("^"+n.source+r.source+"*(?::"+n.source+r.source+"*)?$"),i=0,a=1,s=2,c=3,u=4,l=5,d=6,f=7;function p(){}function h(e,t){return t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber,t}function v(e,t,n,r,o,p){for(var h,v=++t,m=i;;){var g=e.charAt(v);switch(g){case"=":if(m===a)h=e.slice(t,v),m=c;else{if(m!==s)throw new Error("attribute equal must after attrName");m=c}break;case"'":case'"':if(m===c||m===a){if(m===a&&(p.warning('attribute value must after "="'),h=e.slice(t,v)),t=v+1,!((v=e.indexOf(g,t))>0))throw new Error("attribute value no end '"+g+"' match");y=e.slice(t,v).replace(/&#?\w+;/g,o),n.add(h,y,t-1),m=l}else{if(m!=u)throw new Error('attribute value must after "="');y=e.slice(t,v).replace(/&#?\w+;/g,o),n.add(h,y,t),p.warning('attribute "'+h+'" missed start quot('+g+")!!"),t=v+1,m=l}break;case"/":switch(m){case i:n.setTagName(e.slice(t,v));case l:case d:case f:m=f,n.closed=!0;case u:case a:case s:break;default:throw new Error("attribute invalid close char('/')")}break;case"":return p.error("unexpected end of input"),m==i&&n.setTagName(e.slice(t,v)),v;case">":switch(m){case i:n.setTagName(e.slice(t,v));case l:case d:case f:break;case u:case a:"/"===(y=e.slice(t,v)).slice(-1)&&(n.closed=!0,y=y.slice(0,-1));case s:m===s&&(y=h),m==u?(p.warning('attribute "'+y+'" missed quot(")!!'),n.add(h,y.replace(/&#?\w+;/g,o),t)):("http://www.w3.org/1999/xhtml"===r[""]&&y.match(/^(?:disabled|checked|selected)$/i)||p.warning('attribute "'+y+'" missed value!! "'+y+'" instead!!'),n.add(y,y,t));break;case c:throw new Error("attribute value missed!!")}return v;case"":g=" ";default:if(g<=" ")switch(m){case i:n.setTagName(e.slice(t,v)),m=d;break;case a:h=e.slice(t,v),m=s;break;case u:var y=e.slice(t,v).replace(/&#?\w+;/g,o);p.warning('attribute "'+y+'" missed quot(")!!'),n.add(h,y,t);case l:m=d}else switch(m){case s:n.tagName;"http://www.w3.org/1999/xhtml"===r[""]&&h.match(/^(?:disabled|checked|selected)$/i)||p.warning('attribute "'+h+'" missed value!! "'+h+'" instead2!!'),n.add(h,h,t),t=v,m=a;break;case l:p.warning('attribute space is required"'+h+'"!!');case d:m=a,t=v;break;case c:m=u,t=v;break;case f:throw new Error("elements closed character '/' and '>' must be connected to")}}v++}}function m(e,t,n){for(var r=e.tagName,o=null,i=e.length;i--;){var a=e[i],s=a.qName,c=a.value;if((f=s.indexOf(":"))>0)var u=a.prefix=s.slice(0,f),l=s.slice(f+1),d="xmlns"===u&&l;else l=s,u=null,d="xmlns"===s&&"";a.localName=l,!1!==d&&(null==o&&(o={},b(n,n={})),n[d]=o[d]=c,a.uri="http://www.w3.org/2000/xmlns/",t.startPrefixMapping(d,c))}for(i=e.length;i--;){(u=(a=e[i]).prefix)&&("xml"===u&&(a.uri="http://www.w3.org/XML/1998/namespace"),"xmlns"!==u&&(a.uri=n[u||""]))}var f;(f=r.indexOf(":"))>0?(u=e.prefix=r.slice(0,f),l=e.localName=r.slice(f+1)):(u=null,l=e.localName=r);var p=e.uri=n[u||""];if(t.startElement(p,l,r,e),!e.closed)return e.currentNSMap=n,e.localNSMap=o,!0;if(t.endElement(p,l,r),o)for(u in o)t.endPrefixMapping(u)}function g(e,t,n,r,o){if(/^(?:script|textarea)$/i.test(n)){var i=e.indexOf("</"+n+">",t),a=e.substring(t+1,i);if(/[&<]/.test(a))return/^script$/i.test(n)?(o.characters(a,0,a.length),i):(a=a.replace(/&#?\w+;/g,r),o.characters(a,0,a.length),i)}return t+1}function y(e,t,n,r){var o=r[n];return null==o&&((o=e.lastIndexOf("</"+n+">"))<t&&(o=e.lastIndexOf("</"+n)),r[n]=o),o<t}function b(e,t){for(var n in e)t[n]=e[n]}function C(e,t,n,r){switch(e.charAt(t+2)){case"-":return"-"===e.charAt(t+3)?(o=e.indexOf("--\x3e",t+4))>t?(n.comment(e,t+4,o-t-4),o+3):(r.error("Unclosed comment"),-1):-1;default:if("CDATA["==e.substr(t+3,6)){var o=e.indexOf("]]>",t+9);return n.startCDATA(),n.characters(e,t+9,o-t-9),n.endCDATA(),o+3}var i=function(e,t){var n,r=[],o=/'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;o.lastIndex=t,o.exec(e);for(;n=o.exec(e);)if(r.push(n),n[1])return r}(e,t),a=i.length;if(a>1&&/!doctype/i.test(i[0][0])){var s=i[1][0],c=a>3&&/^public$/i.test(i[2][0])&&i[3][0],u=a>4&&i[4][0],l=i[a-1];return n.startDTD(s,c&&c.replace(/^(['"])(.*?)\1$/,"$2"),u&&u.replace(/^(['"])(.*?)\1$/,"$2")),n.endDTD(),l.index+l[0].length}}return-1}function _(e,t,n){var r=e.indexOf("?>",t);if(r){var o=e.substring(t,r).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);if(o){o[0].length;return n.processingInstruction(o[1],o[2]),r+2}return-1}return-1}function w(e){}function x(e,t){return e.__proto__=t,e}p.prototype={parse:function(e,t,n){var r=this.domBuilder;r.startDocument(),b(t,t={}),function(e,t,n,r,o){function i(e){var t=e.slice(1,-1);return t in n?n[t]:"#"===t.charAt(0)?function(e){if(e>65535){var t=55296+((e-=65536)>>10),n=56320+(1023&e);return String.fromCharCode(t,n)}return String.fromCharCode(e)}(parseInt(t.substr(1).replace("x","0x"))):(o.error("entity not found:"+e),e)}function a(t){if(t>b){var n=e.substring(b,t).replace(/&#?\w+;/g,i);d&&s(b),r.characters(n,0,t-b),b=t}}function s(t,n){for(;t>=u&&(n=l.exec(e));)c=n.index,u=c+n[0].length,d.lineNumber++;d.columnNumber=t-c+1}var c=0,u=0,l=/.*(?:\r\n?|\n)|.*$/g,d=r.locator,f=[{currentNSMap:t}],p={},b=0;for(;;){try{var x=e.indexOf("<",b);if(x<0){if(!e.substr(b).match(/^\s*$/)){var S=r.doc,k=S.createTextNode(e.substr(b));S.appendChild(k),r.currentElement=k}return}switch(x>b&&a(x),e.charAt(x+1)){case"/":var T=e.indexOf(">",x+3),A=e.substring(x+2,T),E=f.pop();T<0?(A=e.substring(x+2).replace(/[\s<].*/,""),o.error("end tag name: "+A+" is not complete:"+E.tagName),T=x+1+A.length):A.match(/\s</)&&(A=A.replace(/[\s<].*/,""),o.error("end tag name: "+A+" maybe not complete"),T=x+1+A.length);var R=E.localNSMap,O=E.tagName==A,N=O||E.tagName&&E.tagName.toLowerCase()==A.toLowerCase();if(N){if(r.endElement(E.uri,E.localName,A),R)for(var I in R)r.endPrefixMapping(I);O||o.fatalError("end tag name: "+A+" is not match the current start tagName:"+E.tagName)}else f.push(E);T++;break;case"?":d&&s(x),T=_(e,x,r);break;case"!":d&&s(x),T=C(e,x,r,o);break;default:d&&s(x);var P=new w,D=f[f.length-1].currentNSMap,T=v(e,x,P,D,i,o),j=P.length;if(!P.closed&&y(e,T,P.tagName,p)&&(P.closed=!0,n.nbsp||o.warning("unclosed xml attribute")),d&&j){for(var B=h(d,{}),M=0;M<j;M++){var L=P[M];s(L.offset),L.locator=h(d,{})}r.locator=B,m(P,r,D)&&f.push(P),r.locator=d}else m(P,r,D)&&f.push(P);"http://www.w3.org/1999/xhtml"!==P.uri||P.closed?T++:T=g(e,T,P.tagName,i,r)}}catch(e){o.error("element parse error: "+e),T=-1}T>b?b=T:a(Math.max(x,b)+1)}}(e,t,n,r,this.errorHandler),r.endDocument()}},w.prototype={setTagName:function(e){if(!o.test(e))throw new Error("invalid tagName:"+e);this.tagName=e},add:function(e,t,n){if(!o.test(e))throw new Error("invalid attribute:"+e);this[this.length++]={qName:e,value:t,offset:n}},length:0,getLocalName:function(e){return this[e].localName},getLocator:function(e){return this[e].locator},getQName:function(e){return this[e].qName},getURI:function(e){return this[e].uri},getValue:function(e){return this[e].value}},x({},x.prototype)instanceof x||(x=function(e,t){function n(){}for(t in n.prototype=t,n=new n,e)n[t]=e[t];return n}),t.XMLReader=p},function(e,t){function n(e,t){for(var n in e)t[n]=e[n]}function r(e,t){var r=e.prototype;if(Object.create){var o=Object.create(t.prototype);r.__proto__=o}if(!(r instanceof t)){function i(){}i.prototype=t.prototype,n(r,i=new i),e.prototype=r=i}r.constructor!=e&&("function"!=typeof e&&console.error("unknow Class:"+e),r.constructor=e)}var o="http://www.w3.org/1999/xhtml",i={},a=i.ELEMENT_NODE=1,s=i.ATTRIBUTE_NODE=2,c=i.TEXT_NODE=3,u=i.CDATA_SECTION_NODE=4,l=i.ENTITY_REFERENCE_NODE=5,d=i.ENTITY_NODE=6,f=i.PROCESSING_INSTRUCTION_NODE=7,p=i.COMMENT_NODE=8,h=i.DOCUMENT_NODE=9,v=i.DOCUMENT_TYPE_NODE=10,m=i.DOCUMENT_FRAGMENT_NODE=11,g=i.NOTATION_NODE=12,y={},b={},C=(y.INDEX_SIZE_ERR=(b[1]="Index size error",1),y.DOMSTRING_SIZE_ERR=(b[2]="DOMString size error",2),y.HIERARCHY_REQUEST_ERR=(b[3]="Hierarchy request error",3)),_=(y.WRONG_DOCUMENT_ERR=(b[4]="Wrong document",4),y.INVALID_CHARACTER_ERR=(b[5]="Invalid character",5),y.NO_DATA_ALLOWED_ERR=(b[6]="No data allowed",6),y.NO_MODIFICATION_ALLOWED_ERR=(b[7]="No modification allowed",7),y.NOT_FOUND_ERR=(b[8]="Not found",8)),w=(y.NOT_SUPPORTED_ERR=(b[9]="Not supported",9),y.INUSE_ATTRIBUTE_ERR=(b[10]="Attribute in use",10));y.INVALID_STATE_ERR=(b[11]="Invalid state",11),y.SYNTAX_ERR=(b[12]="Syntax error",12),y.INVALID_MODIFICATION_ERR=(b[13]="Invalid modification",13),y.NAMESPACE_ERR=(b[14]="Invalid namespace",14),y.INVALID_ACCESS_ERR=(b[15]="Invalid access",15);function x(e,t){if(t instanceof Error)var n=t;else n=this,Error.call(this,b[e]),this.message=b[e],Error.captureStackTrace&&Error.captureStackTrace(this,x);return n.code=e,t&&(this.message=this.message+": "+t),n}function S(){}function k(e,t){this._node=e,this._refresh=t,T(this)}function T(e){var t=e._node._inc||e._node.ownerDocument._inc;if(e._inc!=t){var r=e._refresh(e._node);ne(e,"length",r.length),n(r,e),e._inc=t}}function A(){}function E(e,t){for(var n=e.length;n--;)if(e[n]===t)return n}function R(e,t,n,r){if(r?t[E(t,r)]=n:t[t.length++]=n,e){n.ownerElement=e;var o=e.ownerDocument;o&&(r&&B(o,e,r),function(e,t,n){e&&e._inc++,"http://www.w3.org/2000/xmlns/"==n.namespaceURI&&(t._nsMap[n.prefix?n.localName:""]=n.value)}(o,e,n))}}function O(e,t,n){var r=E(t,n);if(!(r>=0))throw x(_,new Error(e.tagName+"@"+n));for(var o=t.length-1;r<o;)t[r]=t[++r];if(t.length=o,e){var i=e.ownerDocument;i&&(B(i,e,n),n.ownerElement=null)}}function N(e){if(this._features={},e)for(var t in e)this._features=e[t]}function I(){}function P(e){return("<"==e?"&lt;":">"==e&&"&gt;")||"&"==e&&"&amp;"||'"'==e&&"&quot;"||"&#"+e.charCodeAt()+";"}function D(e,t){if(t(e))return!0;if(e=e.firstChild)do{if(D(e,t))return!0}while(e=e.nextSibling)}function j(){}function B(e,t,n,r){e&&e._inc++,"http://www.w3.org/2000/xmlns/"==n.namespaceURI&&delete t._nsMap[n.prefix?n.localName:""]}function M(e,t,n){if(e&&e._inc){e._inc++;var r=t.childNodes;if(n)r[r.length++]=n;else{for(var o=t.firstChild,i=0;o;)r[i++]=o,o=o.nextSibling;r.length=i}}}function L(e,t){var n=t.previousSibling,r=t.nextSibling;return n?n.nextSibling=r:e.firstChild=r,r?r.previousSibling=n:e.lastChild=n,M(e.ownerDocument,e),t}function $(e,t,n){var r=t.parentNode;if(r&&r.removeChild(t),t.nodeType===m){var o=t.firstChild;if(null==o)return t;var i=t.lastChild}else o=i=t;var a=n?n.previousSibling:e.lastChild;o.previousSibling=a,i.nextSibling=n,a?a.nextSibling=o:e.firstChild=o,null==n?e.lastChild=i:n.previousSibling=i;do{o.parentNode=e}while(o!==i&&(o=o.nextSibling));return M(e.ownerDocument||e,e),t.nodeType==m&&(t.firstChild=t.lastChild=null),t}function U(){this._nsMap={}}function F(){}function H(){}function K(){}function z(){}function q(){}function V(){}function X(){}function G(){}function W(){}function J(){}function Y(){}function Q(){}function Z(e,t){var n=[],r=9==this.nodeType?this.documentElement:this,o=r.prefix,i=r.namespaceURI;if(i&&null==o&&null==(o=r.lookupPrefix(i)))var a=[{namespace:i,prefix:null}];return te(this,n,e,t,a),n.join("")}function ee(e,t,n){var r=e.prefix||"",o=e.namespaceURI;if(!r&&!o)return!1;if("xml"===r&&"http://www.w3.org/XML/1998/namespace"===o||"http://www.w3.org/2000/xmlns/"==o)return!1;for(var i=n.length;i--;){var a=n[i];if(a.prefix==r)return a.namespace!=o}return!0}function te(e,t,n,r,i){if(r){if(!(e=r(e)))return;if("string"==typeof e)return void t.push(e)}switch(e.nodeType){case a:i||(i=[]);i.length;var d=e.attributes,g=d.length,y=e.firstChild,b=e.tagName;n=o===e.namespaceURI||n,t.push("<",b);for(var C=0;C<g;C++){"xmlns"==(_=d.item(C)).prefix?i.push({prefix:_.localName,namespace:_.value}):"xmlns"==_.nodeName&&i.push({prefix:"",namespace:_.value})}for(C=0;C<g;C++){var _;if(ee(_=d.item(C),0,i)){var w=_.prefix||"",x=_.namespaceURI,S=w?" xmlns:"+w:" xmlns";t.push(S,'="',x,'"'),i.push({prefix:w,namespace:x})}te(_,t,n,r,i)}if(ee(e,0,i)){w=e.prefix||"",x=e.namespaceURI,S=w?" xmlns:"+w:" xmlns";t.push(S,'="',x,'"'),i.push({prefix:w,namespace:x})}if(y||n&&!/^(?:meta|link|img|br|hr|input)$/i.test(b)){if(t.push(">"),n&&/^script$/i.test(b))for(;y;)y.data?t.push(y.data):te(y,t,n,r,i),y=y.nextSibling;else for(;y;)te(y,t,n,r,i),y=y.nextSibling;t.push("</",b,">")}else t.push("/>");return;case h:case m:for(y=e.firstChild;y;)te(y,t,n,r,i),y=y.nextSibling;return;case s:return t.push(" ",e.name,'="',e.value.replace(/[<&"]/g,P),'"');case c:return t.push(e.data.replace(/[<&]/g,P));case u:return t.push("<![CDATA[",e.data,"]]>");case p:return t.push("\x3c!--",e.data,"--\x3e");case v:var k=e.publicId,T=e.systemId;if(t.push("<!DOCTYPE ",e.name),k)t.push(' PUBLIC "',k),T&&"."!=T&&t.push('" "',T),t.push('">');else if(T&&"."!=T)t.push(' SYSTEM "',T,'">');else{var A=e.internalSubset;A&&t.push(" [",A,"]"),t.push(">")}return;case f:return t.push("<?",e.target," ",e.data,"?>");case l:return t.push("&",e.nodeName,";");default:t.push("??",e.nodeName)}}function ne(e,t,n){e[t]=n}x.prototype=Error.prototype,n(y,x),S.prototype={length:0,item:function(e){return this[e]||null},toString:function(e,t){for(var n=[],r=0;r<this.length;r++)te(this[r],n,e,t);return n.join("")}},k.prototype.item=function(e){return T(this),this[e]},r(k,S),A.prototype={length:0,item:S.prototype.item,getNamedItem:function(e){for(var t=this.length;t--;){var n=this[t];if(n.nodeName==e)return n}},setNamedItem:function(e){var t=e.ownerElement;if(t&&t!=this._ownerElement)throw new x(w);var n=this.getNamedItem(e.nodeName);return R(this._ownerElement,this,e,n),n},setNamedItemNS:function(e){var t,n=e.ownerElement;if(n&&n!=this._ownerElement)throw new x(w);return t=this.getNamedItemNS(e.namespaceURI,e.localName),R(this._ownerElement,this,e,t),t},removeNamedItem:function(e){var t=this.getNamedItem(e);return O(this._ownerElement,this,t),t},removeNamedItemNS:function(e,t){var n=this.getNamedItemNS(e,t);return O(this._ownerElement,this,n),n},getNamedItemNS:function(e,t){for(var n=this.length;n--;){var r=this[n];if(r.localName==t&&r.namespaceURI==e)return r}return null}},N.prototype={hasFeature:function(e,t){var n=this._features[e.toLowerCase()];return!(!n||t&&!(t in n))},createDocument:function(e,t,n){var r=new j;if(r.implementation=this,r.childNodes=new S,r.doctype=n,n&&r.appendChild(n),t){var o=r.createElementNS(e,t);r.appendChild(o)}return r},createDocumentType:function(e,t,n){var r=new V;return r.name=e,r.nodeName=e,r.publicId=t,r.systemId=n,r}},I.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,attributes:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(e,t){return $(this,e,t)},replaceChild:function(e,t){this.insertBefore(e,t),t&&this.removeChild(t)},removeChild:function(e){return L(this,e)},appendChild:function(e){return this.insertBefore(e,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(e){return function e(t,n,r){var o=new n.constructor;for(var i in n){var c=n[i];"object"!=typeof c&&c!=o[i]&&(o[i]=c)}n.childNodes&&(o.childNodes=new S);o.ownerDocument=t;switch(o.nodeType){case a:var u=n.attributes,l=o.attributes=new A,d=u.length;l._ownerElement=o;for(var f=0;f<d;f++)o.setAttributeNode(e(t,u.item(f),!0));break;case s:r=!0}if(r)for(var p=n.firstChild;p;)o.appendChild(e(t,p,r)),p=p.nextSibling;return o}(this.ownerDocument||this,this,e)},normalize:function(){for(var e=this.firstChild;e;){var t=e.nextSibling;t&&t.nodeType==c&&e.nodeType==c?(this.removeChild(t),e.appendData(t.data)):(e.normalize(),e=t)}},isSupported:function(e,t){return this.ownerDocument.implementation.hasFeature(e,t)},hasAttributes:function(){return this.attributes.length>0},lookupPrefix:function(e){for(var t=this;t;){var n=t._nsMap;if(n)for(var r in n)if(n[r]==e)return r;t=t.nodeType==s?t.ownerDocument:t.parentNode}return null},lookupNamespaceURI:function(e){for(var t=this;t;){var n=t._nsMap;if(n&&e in n)return n[e];t=t.nodeType==s?t.ownerDocument:t.parentNode}return null},isDefaultNamespace:function(e){return null==this.lookupPrefix(e)}},n(i,I),n(i,I.prototype),j.prototype={nodeName:"#document",nodeType:h,doctype:null,documentElement:null,_inc:1,insertBefore:function(e,t){if(e.nodeType==m){for(var n=e.firstChild;n;){var r=n.nextSibling;this.insertBefore(n,t),n=r}return e}return null==this.documentElement&&e.nodeType==a&&(this.documentElement=e),$(this,e,t),e.ownerDocument=this,e},removeChild:function(e){return this.documentElement==e&&(this.documentElement=null),L(this,e)},importNode:function(e,t){return function e(t,n,r){var o;switch(n.nodeType){case a:(o=n.cloneNode(!1)).ownerDocument=t;case m:break;case s:r=!0}o||(o=n.cloneNode(!1));o.ownerDocument=t;o.parentNode=null;if(r)for(var i=n.firstChild;i;)o.appendChild(e(t,i,r)),i=i.nextSibling;return o}(this,e,t)},getElementById:function(e){var t=null;return D(this.documentElement,function(n){if(n.nodeType==a&&n.getAttribute("id")==e)return t=n,!0}),t},createElement:function(e){var t=new U;return t.ownerDocument=this,t.nodeName=e,t.tagName=e,t.childNodes=new S,(t.attributes=new A)._ownerElement=t,t},createDocumentFragment:function(){var e=new J;return e.ownerDocument=this,e.childNodes=new S,e},createTextNode:function(e){var t=new K;return t.ownerDocument=this,t.appendData(e),t},createComment:function(e){var t=new z;return t.ownerDocument=this,t.appendData(e),t},createCDATASection:function(e){var t=new q;return t.ownerDocument=this,t.appendData(e),t},createProcessingInstruction:function(e,t){var n=new Y;return n.ownerDocument=this,n.tagName=n.target=e,n.nodeValue=n.data=t,n},createAttribute:function(e){var t=new F;return t.ownerDocument=this,t.name=e,t.nodeName=e,t.localName=e,t.specified=!0,t},createEntityReference:function(e){var t=new W;return t.ownerDocument=this,t.nodeName=e,t},createElementNS:function(e,t){var n=new U,r=t.split(":"),o=n.attributes=new A;return n.childNodes=new S,n.ownerDocument=this,n.nodeName=t,n.tagName=t,n.namespaceURI=e,2==r.length?(n.prefix=r[0],n.localName=r[1]):n.localName=t,o._ownerElement=n,n},createAttributeNS:function(e,t){var n=new F,r=t.split(":");return n.ownerDocument=this,n.nodeName=t,n.name=t,n.namespaceURI=e,n.specified=!0,2==r.length?(n.prefix=r[0],n.localName=r[1]):n.localName=t,n}},r(j,I),U.prototype={nodeType:a,hasAttribute:function(e){return null!=this.getAttributeNode(e)},getAttribute:function(e){var t=this.getAttributeNode(e);return t&&t.value||""},getAttributeNode:function(e){return this.attributes.getNamedItem(e)},setAttribute:function(e,t){var n=this.ownerDocument.createAttribute(e);n.value=n.nodeValue=""+t,this.setAttributeNode(n)},removeAttribute:function(e){var t=this.getAttributeNode(e);t&&this.removeAttributeNode(t)},appendChild:function(e){return e.nodeType===m?this.insertBefore(e,null):function(e,t){var n=t.parentNode;if(n){var r=e.lastChild;n.removeChild(t),r=e.lastChild}return r=e.lastChild,t.parentNode=e,t.previousSibling=r,t.nextSibling=null,r?r.nextSibling=t:e.firstChild=t,e.lastChild=t,M(e.ownerDocument,e,t),t}(this,e)},setAttributeNode:function(e){return this.attributes.setNamedItem(e)},setAttributeNodeNS:function(e){return this.attributes.setNamedItemNS(e)},removeAttributeNode:function(e){return this.attributes.removeNamedItem(e.nodeName)},removeAttributeNS:function(e,t){var n=this.getAttributeNodeNS(e,t);n&&this.removeAttributeNode(n)},hasAttributeNS:function(e,t){return null!=this.getAttributeNodeNS(e,t)},getAttributeNS:function(e,t){var n=this.getAttributeNodeNS(e,t);return n&&n.value||""},setAttributeNS:function(e,t,n){var r=this.ownerDocument.createAttributeNS(e,t);r.value=r.nodeValue=""+n,this.setAttributeNode(r)},getAttributeNodeNS:function(e,t){return this.attributes.getNamedItemNS(e,t)},getElementsByTagName:function(e){return new k(this,function(t){var n=[];return D(t,function(r){r===t||r.nodeType!=a||"*"!==e&&r.tagName!=e||n.push(r)}),n})},getElementsByTagNameNS:function(e,t){return new k(this,function(n){var r=[];return D(n,function(o){o===n||o.nodeType!==a||"*"!==e&&o.namespaceURI!==e||"*"!==t&&o.localName!=t||r.push(o)}),r})}},j.prototype.getElementsByTagName=U.prototype.getElementsByTagName,j.prototype.getElementsByTagNameNS=U.prototype.getElementsByTagNameNS,r(U,I),F.prototype.nodeType=s,r(F,I),H.prototype={data:"",substringData:function(e,t){return this.data.substring(e,e+t)},appendData:function(e){e=this.data+e,this.nodeValue=this.data=e,this.length=e.length},insertData:function(e,t){this.replaceData(e,0,t)},appendChild:function(e){throw new Error(b[C])},deleteData:function(e,t){this.replaceData(e,t,"")},replaceData:function(e,t,n){n=this.data.substring(0,e)+n+this.data.substring(e+t),this.nodeValue=this.data=n,this.length=n.length}},r(H,I),K.prototype={nodeName:"#text",nodeType:c,splitText:function(e){var t=this.data,n=t.substring(e);t=t.substring(0,e),this.data=this.nodeValue=t,this.length=t.length;var r=this.ownerDocument.createTextNode(n);return this.parentNode&&this.parentNode.insertBefore(r,this.nextSibling),r}},r(K,H),z.prototype={nodeName:"#comment",nodeType:p},r(z,H),q.prototype={nodeName:"#cdata-section",nodeType:u},r(q,H),V.prototype.nodeType=v,r(V,I),X.prototype.nodeType=g,r(X,I),G.prototype.nodeType=d,r(G,I),W.prototype.nodeType=l,r(W,I),J.prototype.nodeName="#document-fragment",J.prototype.nodeType=m,r(J,I),Y.prototype.nodeType=f,r(Y,I),Q.prototype.serializeToString=function(e,t,n){return Z.call(e,t,n)},I.prototype.toString=Z;try{if(Object.defineProperty){Object.defineProperty(k.prototype,"length",{get:function(){return T(this),this.$$length}}),Object.defineProperty(I.prototype,"textContent",{get:function(){return function e(t){switch(t.nodeType){case a:case m:var n=[];for(t=t.firstChild;t;)7!==t.nodeType&&8!==t.nodeType&&n.push(e(t)),t=t.nextSibling;return n.join("");default:return t.nodeValue}}(this)},set:function(e){switch(this.nodeType){case a:case m:for(;this.firstChild;)this.removeChild(this.firstChild);(e||String(e))&&this.appendChild(this.ownerDocument.createTextNode(e));break;default:this.data=e,this.value=e,this.nodeValue=e}}}),ne=function(e,t,n){e["$$"+t]=n}}}catch(e){}t.DOMImplementation=N,t.XMLSerializer=Q},function(e,t){var n=new RegExp("^([^a-zA-Z_À-ÖØ-öø-ÿͰ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿿、-퟿豈-﷏ﷰ-�])|^((x|X)(m|M)(l|L))|([^a-zA-Z_À-ÖØ-öø-ÿͰ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿿、-퟿豈-﷏ﷰ-�-.0-9·̀-ͯ‿⁀])","g"),r=/[^\x09\x0A\x0D\x20-\xFF\x85\xA0-\uD7FF\uE000-\uFDCF\uFDE0-\uFFFD]/gm,o=function(e){var t=[];if(e instanceof Object)for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t},i=function(e,t){var i=function(e,r,o,i,a){var s=void 0!==t.indent?t.indent:"\t",c=t.prettyPrint?"\n"+new Array(i).join(s):"";t.removeIllegalNameCharacters&&(e=e.replace(n,"_"));var u=[c,"<",e,o||""];return r&&r.length>0?(u.push(">"),u.push(r),a&&u.push(c),u.push("</"),u.push(e),u.push(">")):u.push("/>"),u.join("")};return function e(n,a,s){var c=typeof n;switch((Array.isArray?Array.isArray(n):n instanceof Array)?c="array":n instanceof Date&&(c="date"),c){case"array":var u=[];return n.map(function(t){u.push(e(t,1,s+1))}),t.prettyPrint&&u.push("\n"),u.join("");case"date":return n.toJSON?n.toJSON():n+"";case"object":var l=[];for(var d in n)if(n.hasOwnProperty(d))if(n[d]instanceof Array)for(var f=0;f<n[d].length;f++)n[d].hasOwnProperty(f)&&l.push(i(d,e(n[d][f],0,s+1),null,s+1,o(n[d][f]).length));else l.push(i(d,e(n[d],0,s+1),null,s+1));return t.prettyPrint&&l.length>0&&l.push("\n"),l.join("");case"function":return n();default:return t.escape?(""+n).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&apos;").replace(/"/g,"&quot;").replace(r,""):""+n}}(e,0,0)},a=function(e){var t=['<?xml version="1.0" encoding="UTF-8"'];return e&&t.push(' standalone="yes"'),t.push("?>"),t.join("")};e.exports=function(e,t){if(t||(t={xmlHeader:{standalone:!0},prettyPrint:!0,indent:"  ",escape:!0}),"string"==typeof e)try{e=JSON.parse(e.toString())}catch(e){return!1}var n="",r="";return t&&("object"==typeof t?(t.xmlHeader&&(n=a(!!t.xmlHeader.standalone)),void 0!==t.docType&&(r="<!DOCTYPE "+t.docType+">")):n=a()),[n,(t=t||{}).prettyPrint&&r?"\n":"",r,i(e,t)].join("").replace(/\n{2,}/g,"\n").replace(/\s+$/g,"")}},function(e,t){var n=function(e){var t={},n=function(e){return!t[e]&&(t[e]=[]),t[e]};e.on=function(e,t){"task-list-update"===e&&console.warn('warning: Event "'+e+'" has been deprecated. Please use "list-update" instead.'),n(e).push(t)},e.off=function(e,t){for(var r=n(e),o=r.length-1;o>=0;o--)t===r[o]&&r.splice(o,1)},e.emit=function(e,t){for(var r=n(e).map(function(e){return e}),o=0;o<r.length;o++)r[o](t)}};e.exports.init=n,e.exports.EventProxy=function(){n(this)}},function(e,t,n){var r=n(35),o={};e.exports.transferToTaskMethod=function(e,t){o[t]=e[t],e[t]=function(e,n){e.SkipTask?o[t].call(this,e,n):this._addTask(t,e,n)}},e.exports.init=function(e){var t=[],n={},i=0,a=0,s=function(e){var t={id:e.id,Bucket:e.Bucket,Region:e.Region,Key:e.Key,FilePath:e.FilePath,state:e.state,loaded:e.loaded,size:e.size,speed:e.speed,percent:e.percent,hashPercent:e.hashPercent,error:e.error};return e.FilePath&&(t.FilePath=e.FilePath),t},c=function(){e.emit("task-list-update",{list:r.map(t,s)}),e.emit("list-update",{list:r.map(t,s)})},u=function(){var n;if(t.length>e.options.UploadQueueSize)for(n=0;n<t.length&&t.length>e.options.UploadQueueSize&&n<a;n++)t[n]&&"waiting"===t[n].state||(t.splice(n,1),a--)},l=function(){if(a<t.length&&i<e.options.FileParallelLimit){var n=t[a];if("waiting"===n.state){i++,n.state="checking",!n.params.UploadData&&(n.params.UploadData={});var s=r.formatParams(n.api,n.params);o[n.api].call(e,s,function(t,r){e._isRunningTask(n.id)&&("checking"!==n.state&&"uploading"!==n.state||(n.state=t?"error":"success",t&&(n.error=t),i--,c(),l(e),n.callback&&n.callback(t,r),"success"===n.state&&(n.params&&(delete n.params.UploadData,delete n.params.Body,delete n.params),delete n.callback)),u())}),c()}a++,l(e)}},d=function(t,r){var o=n[t];if(o){var a=o&&"waiting"===o.state,s=o&&("checking"===o.state||"uploading"===o.state);if("canceled"===r&&"canceled"!==o.state||"paused"===r&&a||"paused"===r&&s){if("paused"===r&&o.params.Body&&"function"==typeof o.params.Body.pipe)return void console.error("stream not support pause");o.state=r,e.emit("inner-kill-task",{TaskId:t,toState:r}),c(),s&&(i--,l(e)),"canceled"===r&&(o.params&&(delete o.params.UploadData,delete o.params.Body,delete o.params),delete o.callback)}u()}};e._addTasks=function(t){r.each(t,function(t){e._addTask(t.api,t.params,t.callback,!0)}),c()},e._addTask=function(o,i,a,s){i=r.formatParams(o,i);var d=r.uuid();i.TaskId=d,i.TaskReady&&i.TaskReady(d);var f={params:i,callback:a,api:o,index:t.length,id:d,Bucket:i.Bucket,Region:i.Region,Key:i.Key,FilePath:i.FilePath||"",state:"waiting",loaded:0,size:0,speed:0,percent:0,hashPercent:0,error:null},p=i.onHashProgress;i.onHashProgress=function(t){e._isRunningTask(f.id)&&(f.hashPercent=t.percent,p&&p(t),c())};var h=i.onProgress;return i.onProgress=function(t){e._isRunningTask(f.id)&&("checking"===f.state&&(f.state="uploading"),f.loaded=t.loaded,f.speed=t.speed,f.percent=t.percent,h&&h(t),c())},r.getFileSize(o,i,function(r,o){r?a(r):(n[d]=f,t.push(f),f.size=o,!s&&c(),l(e),u())}),d},e._isRunningTask=function(e){var t=n[e];return!(!t||"checking"!==t.state&&"uploading"!==t.state)},e.getTaskList=function(){return r.map(t,s)},e.cancelTask=function(e){d(e,"canceled")},e.pauseTask=function(e){d(e,"paused")},e.restartTask=function(e){var t=n[e];!t||"paused"!==t.state&&"error"!==t.state||(t.state="waiting",c(),a=Math.min(a,t.index),l())}}},function(e,t,n){var r=n(46),o=n(35);function i(e){var t={GrantFullControl:[],GrantWrite:[],GrantRead:[],GrantReadAcp:[],GrantWriteAcp:[],ACL:""},n={FULL_CONTROL:"GrantFullControl",WRITE:"GrantWrite",READ:"GrantRead",READ_ACP:"GrantReadAcp",WRITE_ACP:"GrantWriteAcp"},r=e.AccessControlList.Grant;r&&(r=o.isArray(r)?r:[r]);var i={READ:0,WRITE:0,FULL_CONTROL:0};return r.length&&o.each(r,function(r){"qcs::cam::anyone:anyone"===r.Grantee.ID||"http://cam.qcloud.com/groups/global/AllUsers"===r.Grantee.URI?i[r.Permission]=1:r.Grantee.ID!==e.Owner.ID&&t[n[r.Permission]].push('id="'+r.Grantee.ID+'"')}),i.FULL_CONTROL||i.WRITE&&i.READ?t.ACL="public-read-write":i.READ?t.ACL="public-read":t.ACL="private",o.each(n,function(e){t[e]=a(t[e].join(","))}),t}function a(e){var t,n,r=e.split(","),o={};for(t=0;t<r.length;)o[n=r[t].trim()]?r.splice(t,1):(o[n]=!0,r[t]=n,t++);return r.join(",")}function s(e){var t=e.bucket,n=t.substr(0,t.lastIndexOf("-")),r=t.substr(t.lastIndexOf("-")+1),i=e.domain,a=e.region,s=e.object,c=e.protocol||(o.isBrowser&&"http:"===location.protocol?"http:":"https:");i||(i=["cn-south","cn-south-2","cn-north","cn-east","cn-southwest","sg"].indexOf(a)>-1?"{Region}.myqcloud.com":"cos.{Region}.myqcloud.com",e.ForcePathStyle||(i="{Bucket}."+i)),i=(i=i.replace(/\{\{AppId\}\}/gi,r).replace(/\{\{Bucket\}\}/gi,n).replace(/\{\{Region\}\}/gi,a).replace(/\{\{.*?\}\}/gi,"")).replace(/\{AppId\}/gi,r).replace(/\{BucketName\}/gi,n).replace(/\{Bucket\}/gi,t).replace(/\{Region\}/gi,a).replace(/\{.*?\}/gi,""),/^[a-zA-Z]+:\/\//.test(i)||(i=c+"//"+i),"/"===i.slice(-1)&&(i=i.slice(0,-1));var u=i;return e.ForcePathStyle&&(u+="/"+t),u+="/",s&&(u+=o.camSafeUrlEncode(s).replace(/%2F/g,"/")),e.isLocation&&(u=u.replace(/^https?:\/\//,"")),u}function c(e,t){var n=o.clone(e.Headers);delete n["Content-Type"],delete n["Cache-Control"],o.each(n,function(e,t){""===e&&delete n[t]});var r=function(e){var n=!1,r=e.Authorization;if(r)if(r.indexOf(" ")>-1)n=!1;else if(r.indexOf("q-sign-algorithm=")>-1&&r.indexOf("q-ak=")>-1&&r.indexOf("q-sign-time=")>-1&&r.indexOf("q-key-time=")>-1&&r.indexOf("q-url-param-list=")>-1)n=!0;else try{(r=atob(r)).indexOf("a=")>-1&&r.indexOf("k=")>-1&&r.indexOf("t=")>-1&&r.indexOf("r=")>-1&&r.indexOf("b=")>-1&&(n=!0)}catch(e){}n?t&&t(null,e):t&&t("authorization error")},i=this,a=e.Bucket||"",s=e.Region||"",c=e.Key||"";i.options.ForcePathStyle&&a&&(c=a+"/"+c);var u="/"+c,l={},d=e.Scope;if(!d){var f=e.Action||"",p=e.ResourceKey||e.Key||"";d=e.Scope||[{action:f,bucket:a,region:s,prefix:p}]}var h=o.md5(JSON.stringify(d));i._StsCache=i._StsCache||[],function(){var e,t;for(e=i._StsCache.length-1;e>=0;e--){t=i._StsCache[e];var n=Math.round(o.getSkewTime(i.options.SystemClockOffset)/1e3)+30;if(t.StartTime&&n<t.StartTime||n>=t.ExpiredTime)i._StsCache.splice(e,1);else if(!t.ScopeLimit||t.ScopeLimit&&t.ScopeKey===h){l=t;break}}}();var v,m=function(){var t={Authorization:o.getAuth({SecretId:l.TmpSecretId,SecretKey:l.TmpSecretKey,Method:e.Method,Pathname:u,Query:e.Query,Headers:n,UseRawKey:i.options.UseRawKey,SystemClockOffset:i.options.SystemClockOffset}),XCosSecurityToken:l.XCosSecurityToken||"",Token:l.Token||"",ClientIP:l.ClientIP||"",ClientUA:l.ClientUA||""};r(t)};if(l.ExpiredTime&&l.ExpiredTime-o.getSkewTime(i.options.SystemClockOffset)/1e3>60)m();else if(i.options.getAuthorization)i.options.getAuthorization.call(i,{Bucket:a,Region:s,Method:e.Method,Key:c,Pathname:u,Query:e.Query,Headers:n,Scope:d},function(e){"string"==typeof e&&(e={Authorization:e}),e.TmpSecretId&&e.TmpSecretKey&&e.XCosSecurityToken&&e.ExpiredTime?((l=e||{}).Scope=d,l.ScopeKey=h,i._StsCache.push(l),m()):r(e)});else{if(!i.options.getSTS)return v={Authorization:o.getAuth({SecretId:e.SecretId||i.options.SecretId,SecretKey:e.SecretKey||i.options.SecretKey,Method:e.Method,Pathname:u,Query:e.Query,Headers:n,Expires:e.Expires,UseRawKey:i.options.UseRawKey,SystemClockOffset:i.options.SystemClockOffset}),XCosSecurityToken:i.options.XCosSecurityToken},r(v),v;i.options.getSTS.call(i,{Bucket:a,Region:s},function(e){(l=e||{}).Scope=d,l.ScopeKey=h,l.TmpSecretId=l.SecretId,l.TmpSecretKey=l.SecretKey,i._StsCache.push(l),m()})}return""}function u(e,t){var n=this;!e.headers&&(e.headers={}),!e.qs&&(e.qs={}),e.VersionId&&(e.qs.versionId=e.VersionId),e.qs=o.clearKey(e.qs),e.headers&&(e.headers=o.clearKey(e.headers)),e.qs&&(e.qs=o.clearKey(e.qs));var i=o.clone(e.qs);e.action&&(i[e.action]="");var a=function(u){var l=n.options.SystemClockOffset;c.call(n,{Bucket:e.Bucket||"",Region:e.Region||"",Method:e.method,Key:e.Key,Query:i,Headers:e.headers,Action:e.Action,ResourceKey:e.ResourceKey,Scope:e.Scope},function(i,c){e.AuthData=c,function(e,t){var n=this,i=e.TaskId;if(i&&!n._isRunningTask(i))return;var a=e.Bucket,c=e.Region,u=e.Key,l=e.method||"GET",d=e.url,f=e.body,p=e.json,h=e.rawBody;d=d||s({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:a,region:c,object:u}),e.action&&(d=d+"?"+e.action);var v={method:l,url:d,headers:e.headers,qs:e.qs,body:f,json:p};if(v.headers.Authorization=e.AuthData.Authorization,e.AuthData.Token&&(v.headers.token=e.AuthData.Token),e.AuthData.ClientIP&&(v.headers.clientIP=e.AuthData.ClientIP),e.AuthData.ClientUA&&(v.headers.clientUA=e.AuthData.ClientUA),e.AuthData.XCosSecurityToken&&(v.headers["x-cos-security-token"]=e.AuthData.XCosSecurityToken),v.headers&&(v.headers=o.clearKey(v.headers)),v=o.clearKey(v),e.onProgress&&"function"==typeof e.onProgress){var m=f&&(f.size||f.length)||0;v.onProgress=function(t){if(!i||n._isRunningTask(i)){var r=t?t.loaded:0;e.onProgress({loaded:r,total:m})}}}this.options.Timeout&&(v.timeout=this.options.Timeout);n.emit("before-send",v);var g=r(v,function(e,r,a){if("abort"!==e){var s,c=function(e,a){if(i&&n.off("inner-kill-task",y),!s){s=!0;var c={};r&&r.statusCode&&(c.statusCode=r.statusCode),r&&r.headers&&(c.headers=r.headers),e?(e=o.extend(e||{},c),t(e,null)):(a=o.extend(a||{},c),t(null,a)),g=null}};if(e)c({error:e});else{var u;try{u=o.xml2json(a)||{}}catch(e){u=a||{}}var l=r.statusCode,d=2===Math.floor(l/100);d?(h&&((u={}).body=a),u.Error?c({error:u.Error}):c(null,u)):c({error:u.Error||u})}}}),y=function(e){e.TaskId===i&&(g&&g.abort&&g.abort(),n.off("inner-kill-task",y))};i&&n.on("inner-kill-task",y)}.call(n,e,function(r,i){r&&u<2&&(l!==n.options.SystemClockOffset||function(e){var t=!1,n=!1,r=e.headers&&(e.headers.date||e.headers.Date)||"";try{var i=e.error.Code,a=e.error.Message;("RequestTimeTooSkewed"===i||"AccessDenied"===i&&"Request has expired"===a)&&(n=!0)}catch(e){}if(e)if(n&&r){var s=Date.parse(r);this.options.CorrectClockSkew&&Math.abs(o.getSkewTime(this.options.SystemClockOffset)-s)>=3e4&&(console.error("error: Local time is too skewed."),this.options.SystemClockOffset=s-Date.now(),t=!0)}else 5===Math.round(e.statusCode/100)&&(t=!0);return t}.call(n,r))?(e.headers&&(delete e.headers.Authorization,delete e.headers.token,delete e.headers.clientIP,delete e.headers.clientUA,delete e.headers["x-cos-security-token"]),a(u+1)):t(r,i)})})};a(0)}var l={getBucket:function(e,t){var n={};n.prefix=e.Prefix||"",n.delimiter=e.Delimiter,n.marker=e.Marker,n["max-keys"]=e.MaxKeys,n["encoding-type"]=e.EncodingType,u.call(this,{Action:"name/cos:GetBucket",ResourceKey:n.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:n},function(e,n){if(e)return t(e);var r=n.ListBucketResult||{},i=r.Contents||[],a=r.CommonPrefixes||[];i=o.isArray(i)?i:[i],a=o.isArray(a)?a:[a];var s=o.clone(r);o.extend(s,{Contents:i,CommonPrefixes:a,statusCode:n.statusCode,headers:n.headers}),t(null,s)})},headBucket:function(e,t){u.call(this,{Action:"name/cos:HeadBucket",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"HEAD"},function(e,n){t(e,n)})},deleteBucket:function(e,t){u.call(this,{Action:"name/cos:DeleteBucket",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"DELETE"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketAcl:function(e,t){u.call(this,{Action:"name/cos:GetBucketACL",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"acl"},function(e,n){if(e)return t(e);var r=n.AccessControlPolicy||{},a=r.Owner||{},s=r.AccessControlList.Grant||[];s=o.isArray(s)?s:[s];var c=i(r);n.headers&&n.headers["x-cos-acl"]&&(c.ACL=n.headers["x-cos-acl"]),c=o.extend(c,{Owner:a,Grants:s,statusCode:n.statusCode,headers:n.headers}),t(null,c)})},putBucketAcl:function(e,t){var n=e.Headers,r="";if(e.AccessControlPolicy){var i=o.clone(e.AccessControlPolicy||{}),s=i.Grants||i.Grant;s=o.isArray(s)?s:[s],delete i.Grant,delete i.Grants,i.AccessControlList={Grant:s},r=o.json2xml({AccessControlPolicy:i}),n["Content-Type"]="application/xml",n["Content-MD5"]=o.binaryBase64(o.md5(r))}o.each(n,function(e,t){0===t.indexOf("x-cos-grant-")&&(n[t]=a(n[t]))}),u.call(this,{Action:"name/cos:PutBucketACL",method:"PUT",Bucket:e.Bucket,Region:e.Region,headers:n,action:"acl",body:r},function(e,n){if(e)return t(e);t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketCors:function(e,t){u.call(this,{Action:"name/cos:GetBucketCORS",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors"},function(e,n){if(e)if(404===e.statusCode&&e.error&&"NoSuchCORSConfiguration"===e.error.Code){var r={CORSRules:[],statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else t(e);else{var i=n.CORSConfiguration||{},a=i.CORSRules||i.CORSRule||[];a=o.clone(o.isArray(a)?a:[a]),o.each(a,function(e){o.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],function(t,n){var r=t+"s",i=e[r]||e[t]||[];delete e[t],e[r]=o.isArray(i)?i:[i]})}),t(null,{CORSRules:a,statusCode:n.statusCode,headers:n.headers})}})},putBucketCors:function(e,t){var n=(e.CORSConfiguration||{}).CORSRules||e.CORSRules||[];n=o.clone(o.isArray(n)?n:[n]),o.each(n,function(e){o.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],function(t,n){var r=t+"s",i=e[r]||e[t]||[];delete e[r],e[t]=o.isArray(i)?i:[i]})});var r=o.json2xml({CORSConfiguration:{CORSRule:n}}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=o.binaryBase64(o.md5(r)),u.call(this,{Action:"name/cos:PutBucketCORS",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:r,action:"cors",headers:i},function(e,n){if(e)return t(e);t(null,{statusCode:n.statusCode,headers:n.headers})})},deleteBucketCors:function(e,t){u.call(this,{Action:"name/cos:DeleteBucketCORS",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode||e.statusCode,headers:n.headers})})},getBucketLocation:function(e,t){u.call(this,{Action:"name/cos:GetBucketLocation",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"location"},function(e,n){if(e)return t(e);t(null,n)})},putBucketTagging:function(e,t){var n=e.Tagging||{},r=n.TagSet||n.Tags||e.Tags||[];r=o.clone(o.isArray(r)?r:[r]);var i=o.json2xml({Tagging:{TagSet:{Tag:r}}}),a=e.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=o.binaryBase64(o.md5(i)),u.call(this,{Action:"name/cos:PutBucketTagging",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:i,action:"tagging",headers:a},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketTagging:function(e,t){u.call(this,{Action:"name/cos:GetBucketTagging",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging"},function(e,n){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"NoSuchTagSet"!==e.error.Code)t(e);else{var r={Tags:[],statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else{var i=[];try{i=n.Tagging.TagSet.Tag||[]}catch(e){}i=o.clone(o.isArray(i)?i:[i]),t(null,{Tags:i,statusCode:n.statusCode,headers:n.headers})}})},deleteBucketTagging:function(e,t){u.call(this,{Action:"name/cos:DeleteBucketTagging",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketPolicy:function(e,t){u.call(this,{Action:"name/cos:GetBucketPolicy",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy",rawBody:!0},function(e,n){if(e)return e.statusCode&&403===e.statusCode?t({ErrorStatus:"Access Denied"}):e.statusCode&&405===e.statusCode?t({ErrorStatus:"Method Not Allowed"}):e.statusCode&&404===e.statusCode?t({ErrorStatus:"Policy Not Found"}):t(e);var r={};try{r=JSON.parse(n.body)}catch(e){}t(null,{Policy:r,statusCode:n.statusCode,headers:n.headers})})},putBucketPolicy:function(e,t){var n=e.Policy,r=n;try{"string"==typeof n?n=JSON.parse(r):r=JSON.stringify(n)}catch(e){t({error:"Policy format error"})}var i=e.Headers;i["Content-Type"]="application/json",i["Content-MD5"]=o.binaryBase64(o.md5(r)),u.call(this,{Action:"name/cos:PutBucketPolicy",method:"PUT",Bucket:e.Bucket,Region:e.Region,action:"policy",body:o.isBrowser?r:n,headers:i,json:!0},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},deleteBucketPolicy:function(e,t){u.call(this,{Action:"name/cos:DeleteBucketPolicy",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode||e.statusCode,headers:n.headers})})},getBucketLifecycle:function(e,t){u.call(this,{Action:"name/cos:GetBucketLifecycle",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle"},function(e,n){if(e)if(404===e.statusCode&&e.error&&"NoSuchLifecycleConfiguration"===e.error.Code){var r={Rules:[],statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else t(e);else{var i=[];try{i=n.LifecycleConfiguration.Rule||[]}catch(e){}i=o.clone(o.isArray(i)?i:[i]),t(null,{Rules:i,statusCode:n.statusCode,headers:n.headers})}})},putBucketLifecycle:function(e,t){var n=(e.LifecycleConfiguration||{}).Rules||[];n=o.clone(n);var r=o.json2xml({LifecycleConfiguration:{Rule:n}}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=o.binaryBase64(o.md5(r)),u.call(this,{Action:"name/cos:PutBucketLifecycle",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:r,action:"lifecycle",headers:i},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},deleteBucketLifecycle:function(e,t){u.call(this,{Action:"name/cos:DeleteBucketLifecycle",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},putBucketVersioning:function(e,t){if(e.VersioningConfiguration){var n=e.VersioningConfiguration||{},r=o.json2xml({VersioningConfiguration:n}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=o.binaryBase64(o.md5(r)),u.call(this,{Action:"name/cos:PutBucketVersioning",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:r,action:"versioning",headers:i},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})}else t({error:"missing param VersioningConfiguration"})},getBucketVersioning:function(e,t){u.call(this,{Action:"name/cos:GetBucketVersioning",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"versioning"},function(e,n){e||!n.VersioningConfiguration&&(n.VersioningConfiguration={}),t(e,n)})},putBucketReplication:function(e,t){var n=o.clone(e.ReplicationConfiguration),r=o.json2xml({ReplicationConfiguration:n});r=(r=r.replace(/<(\/?)Rules>/gi,"<$1Rule>")).replace(/<(\/?)Tags>/gi,"<$1Tag>");var i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=o.binaryBase64(o.md5(r)),u.call(this,{Action:"name/cos:PutBucketReplication",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:r,action:"replication",headers:i},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketReplication:function(e,t){u.call(this,{Action:"name/cos:GetBucketReplication",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication"},function(e,n){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"ReplicationConfigurationnotFoundError"!==e.error.Code)t(e);else{var r={ReplicationConfiguration:{Rules:[]},statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else e||!n.ReplicationConfiguration&&(n.ReplicationConfiguration={}),n.ReplicationConfiguration.Rule&&(n.ReplicationConfiguration.Rules=n.ReplicationConfiguration.Rule,delete n.ReplicationConfiguration.Rule),t(e,n)})},deleteBucketReplication:function(e,t){u.call(this,{Action:"name/cos:DeleteBucketReplication",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},getObject:function(e,t){var n={};n["response-content-type"]=e.ResponseContentType,n["response-content-language"]=e.ResponseContentLanguage,n["response-expires"]=e.ResponseExpires,n["response-cache-control"]=e.ResponseCacheControl,n["response-content-disposition"]=e.ResponseContentDisposition,n["response-content-encoding"]=e.ResponseContentEncoding,u.call(this,{Action:"name/cos:GetObject",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers,qs:n,rawBody:!0},function(n,r){if(n){var i=n.statusCode;return e.Headers["If-Modified-Since"]&&i&&304===i?t(null,{NotModified:!0}):t(n)}var a={};a.Body=r.body,r.headers&&r.headers.etag&&(a.ETag=r.headers&&r.headers.etag),o.extend(a,{statusCode:r.statusCode,headers:r.headers}),t(null,a)})},headObject:function(e,t){u.call(this,{Action:"name/cos:HeadObject",method:"HEAD",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers},function(n,r){if(n){var o=n.statusCode;return e.Headers["If-Modified-Since"]&&o&&304===o?t(null,{NotModified:!0,statusCode:o}):t(n)}r.headers&&r.headers.etag&&(r.ETag=r.headers&&r.headers.etag),t(null,r)})},listObjectVersions:function(e,t){var n={};n.prefix=e.Prefix||"",n.delimiter=e.Delimiter,n["key-marker"]=e.KeyMarker,n["version-id-marker"]=e.VersionIdMarker,n["max-keys"]=e.MaxKeys,n["encoding-type"]=e.EncodingType,u.call(this,{Action:"name/cos:GetBucketObjectVersions",ResourceKey:n.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:n,action:"versions"},function(e,n){if(e)return t(e);var r=n.ListVersionsResult||{},i=r.DeleteMarker||[];i=o.isArray(i)?i:[i];var a=r.Version||[];a=o.isArray(a)?a:[a];var s=o.clone(r);delete s.DeleteMarker,delete s.Version,o.extend(s,{DeleteMarkers:i,Versions:a,statusCode:n.statusCode,headers:n.headers}),t(null,s)})},putObject:function(e,t){var n=this,r=e.ContentLength,i=o.throttleOnProgress.call(n,r,e.onProgress),a=e.Headers;!a["Cache-Control"]&&(a["Cache-Control"]="");var c=a["Content-Type"]||e.Body&&e.Body.type;!a["Content-Type"]&&c&&(a["Content-Type"]=c),o.getBodyMd5(n.options.UploadCheckContentMd5,e.Body,function(a){a&&(e.Headers["Content-MD5"]=o.binaryBase64(a)),void 0!==e.ContentLength&&(e.Headers["Content-Length"]=e.ContentLength),u.call(n,{Action:"name/cos:PutObject",TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,body:e.Body,onProgress:i},function(o,a){if(o)return i(null,!0),t(o);if(i({loaded:r,total:r},!0),a&&a.headers&&a.headers.etag){var c=s({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key});return c=c.substr(c.indexOf("://")+3),t(null,{Location:c,ETag:a.headers.etag,statusCode:a.statusCode,headers:a.headers})}t(null,a)})})},deleteObject:function(e,t){u.call(this,{Action:"name/cos:DeleteObject",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,VersionId:e.VersionId},function(e,n){if(e){var r=e.statusCode;return r&&204===r?t(null,{statusCode:r}):r&&404===r?t(null,{BucketNotFound:!0,statusCode:r}):t(e)}t(null,{statusCode:n.statusCode,headers:n.headers})})},getObjectAcl:function(e,t){u.call(this,{Action:"name/cos:GetObjectACL",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"acl"},function(e,n){if(e)return t(e);var r=n.AccessControlPolicy||{},a=r.Owner||{},s=r.AccessControlList&&r.AccessControlList.Grant||[];s=o.isArray(s)?s:[s];var c=i(r);n.headers&&n.headers["x-cos-acl"]&&(c.ACL=n.headers["x-cos-acl"]),c=o.extend(c,{Owner:a,Grants:s,statusCode:n.statusCode,headers:n.headers}),t(null,c)})},putObjectAcl:function(e,t){var n=e.Headers,r="";if(e.AccessControlPolicy){var i=o.clone(e.AccessControlPolicy||{}),s=i.Grants||i.Grant;s=o.isArray(s)?s:[s],delete i.Grant,delete i.Grants,i.AccessControlList={Grant:s},r=o.json2xml({AccessControlPolicy:i}),n["Content-Type"]="application/xml",n["Content-MD5"]=o.binaryBase64(o.md5(r))}o.each(n,function(e,t){0===t.indexOf("x-cos-grant-")&&(n[t]=a(n[t]))}),u.call(this,{Action:"name/cos:PutObjectACL",method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"acl",headers:n,body:r},function(e,n){if(e)return t(e);t(null,{statusCode:n.statusCode,headers:n.headers})})},optionsObject:function(e,t){var n=e.Headers;n.Origin=e.Origin,n["Access-Control-Request-Method"]=e.AccessControlRequestMethod,n["Access-Control-Request-Headers"]=e.AccessControlRequestHeaders,u.call(this,{Action:"name/cos:OptionsObject",method:"OPTIONS",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:n},function(e,n){if(e)return e.statusCode&&403===e.statusCode?t(null,{OptionsForbidden:!0,statusCode:e.statusCode}):t(e);var r=n.headers||{};t(null,{AccessControlAllowOrigin:r["access-control-allow-origin"],AccessControlAllowMethods:r["access-control-allow-methods"],AccessControlAllowHeaders:r["access-control-allow-headers"],AccessControlExposeHeaders:r["access-control-expose-headers"],AccessControlMaxAge:r["access-control-max-age"],statusCode:n.statusCode,headers:n.headers})})},putObjectCopy:function(e,t){var n=(e.CopySource||"").match(/^([^.]+-\d+)\.cos(v6)?\.([^.]+)\.[^\/]+\/(.+)$/);if(n){var r=n[1],i=n[3],a=decodeURIComponent(n[4]);u.call(this,{Scope:[{action:"name/cos:GetObject",bucket:r,region:i,prefix:a},{action:"name/cos:PutObject",bucket:e.Bucket,region:e.Region,prefix:e.Key}],method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers},function(e,n){if(e)return t(e);var r=o.clone(n.CopyObjectResult||{});o.extend(r,{statusCode:n.statusCode,headers:n.headers}),t(null,r)})}else t({error:"CopySource format error"})},deleteMultipleObject:function(e,t){var n=e.Objects||[],r=e.Quiet;n=o.isArray(n)?n:[n];var i=o.json2xml({Delete:{Object:n,Quiet:r||!1}}),a=e.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=o.binaryBase64(o.md5(i));var s=o.map(n,function(t){return{action:"name/cos:DeleteObject",bucket:e.Bucket,region:e.Region,prefix:t.Key}});u.call(this,{Scope:s,method:"POST",Bucket:e.Bucket,Region:e.Region,body:i,action:"delete",headers:a},function(e,n){if(e)return t(e);var r=n.DeleteResult||{},i=r.Deleted||[],a=r.Error||[];i=o.isArray(i)?i:[i],a=o.isArray(a)?a:[a];var s=o.clone(r);o.extend(s,{Error:a,Deleted:i,statusCode:n.statusCode,headers:n.headers}),t(null,s)})},restoreObject:function(e,t){var n=e.Headers;if(e.RestoreRequest){var r=e.RestoreRequest||{},i=o.json2xml({RestoreRequest:r});n["Content-Type"]="application/xml",n["Content-MD5"]=o.binaryBase64(o.md5(i)),u.call(this,{Action:"name/cos:RestoreObject",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,body:i,action:"restore",headers:n},function(e,n){t(e,n)})}else t({error:"missing param RestoreRequest"})},uploadPartCopy:function(e,t){var n=(e.CopySource||"").match(/^([^.]+-\d+)\.cos(v6)?\.([^.]+)\.[^\/]+\/(.+)$/);if(n){var r=n[1],i=n[3],a=decodeURIComponent(n[4]);u.call(this,{Scope:[{action:"name/cos:GetObject",bucket:r,region:i,prefix:a},{action:"name/cos:PutObject",bucket:e.Bucket,region:e.Region,prefix:e.Key}],method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,qs:{partNumber:e.PartNumber,uploadId:e.UploadId},headers:e.Headers},function(e,n){if(e)return t(e);var r=o.clone(n.CopyPartResult||{});o.extend(r,{statusCode:n.statusCode,headers:n.headers}),t(null,r)})}else t({error:"CopySource format error"})},multipartInit:function(e,t){var n,r=e.Headers,i=(navigator.userAgent||"").match(/ TBS\/(\d{6}) /);"http:"===location.protocol&&i&&i[1].length<=6&&i[1]<"044429"&&(n=o.json2xml({}),r["Content-MD5"]=o.binaryBase64(o.md5(n)),r["Content-Type"]||(r["Content-Type"]=e.Body&&e.Body.type||"application/octet-stream")),!r["Cache-Control"]&&(r["Cache-Control"]=""),u.call(this,{Action:"name/cos:InitiateMultipartUpload",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"uploads",headers:e.Headers,body:n},function(e,n){return e?t(e):(n=o.clone(n||{}))&&n.InitiateMultipartUploadResult?t(null,o.extend(n.InitiateMultipartUploadResult,{statusCode:n.statusCode,headers:n.headers})):void t(null,n)})},multipartUpload:function(e,t){var n=this;o.getFileSize("multipartUpload",e,function(){o.getBodyMd5(n.options.UploadCheckContentMd5,e.Body,function(r){r&&(e.Headers["Content-MD5"]=o.binaryBase64(r)),u.call(n,{Action:"name/cos:UploadPart",TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{partNumber:e.PartNumber,uploadId:e.UploadId},headers:e.Headers,onProgress:e.onProgress,body:e.Body||null},function(e,n){if(e)return t(e);n.headers=n.headers||{},t(null,{ETag:n.headers.etag||"",statusCode:n.statusCode,headers:n.headers})})})})},multipartComplete:function(e,t){for(var n=this,r=e.UploadId,i=e.Parts,a=0,c=i.length;a<c;a++)0!==i[a].ETag.indexOf('"')&&(i[a].ETag='"'+i[a].ETag+'"');var l=o.json2xml({CompleteMultipartUpload:{Part:i}}),d=e.Headers;d["Content-Type"]="application/xml",d["Content-MD5"]=o.binaryBase64(o.md5(l)),u.call(this,{Action:"name/cos:CompleteMultipartUpload",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{uploadId:r},body:l,headers:d},function(r,i){if(r)return t(r);var a=s({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key,isLocation:!0}),c=i.CompleteMultipartUploadResult||{},u=o.extend(c,{Location:a,statusCode:i.statusCode,headers:i.headers});t(null,u)})},multipartList:function(e,t){var n={};n.delimiter=e.Delimiter,n["encoding-type"]=e.EncodingType,n.prefix=e.Prefix||"",n["max-uploads"]=e.MaxUploads,n["key-marker"]=e.KeyMarker,n["upload-id-marker"]=e.UploadIdMarker,n=o.clearKey(n),u.call(this,{Action:"name/cos:ListMultipartUploads",ResourceKey:n.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:n,action:"uploads"},function(e,n){if(e)return t(e);if(n&&n.ListMultipartUploadsResult){var r=n.ListMultipartUploadsResult.Upload||[],i=n.ListMultipartUploadsResult.CommonPrefixes||[];i=o.isArray(i)?i:[i],r=o.isArray(r)?r:[r],n.ListMultipartUploadsResult.Upload=r,n.ListMultipartUploadsResult.CommonPrefixes=i}var a=o.clone(n.ListMultipartUploadsResult||{});o.extend(a,{statusCode:n.statusCode,headers:n.headers}),t(null,a)})},multipartListPart:function(e,t){var n={};n.uploadId=e.UploadId,n["encoding-type"]=e.EncodingType,n["max-parts"]=e.MaxParts,n["part-number-marker"]=e.PartNumberMarker,u.call(this,{Action:"name/cos:ListParts",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:n},function(e,n){if(e)return t(e);var r=n.ListPartsResult||{},i=r.Part||[];i=o.isArray(i)?i:[i],r.Part=i;var a=o.clone(r);o.extend(a,{statusCode:n.statusCode,headers:n.headers}),t(null,a)})},multipartAbort:function(e,t){var n={};n.uploadId=e.UploadId,u.call(this,{Action:"name/cos:AbortMultipartUpload",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:n},function(e,n){if(e)return t(e);t(null,{statusCode:n.statusCode,headers:n.headers})})},getObjectUrl:function(e,t){var n=s({ForcePathStyle:this.options.ForcePathStyle,protocol:e.Protocol||this.options.Protocol,domain:this.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key});if(void 0!==e.Sign&&!e.Sign)return t(null,{Url:n}),n;var r=c.call(this,{Action:"PUT"===(e.Method||"").toUpperCase()?"name/cos:PutObject":"name/cos:GetObject",Bucket:e.Bucket||"",Region:e.Region||"",Method:e.Method||"get",Key:e.Key,Expires:e.Expires},function(e,r){if(t)if(e)t(e);else{var o=n;o+="?"+(r.Authorization.indexOf("q-signature")>-1?r.Authorization:"sign="+encodeURIComponent(r.Authorization)),r.XCosSecurityToken&&(o+="&x-cos-security-token="+r.XCosSecurityToken),r.ClientIP&&(o+="&clientIP="+r.ClientIP),r.ClientUA&&(o+="&clientUA="+r.ClientUA),r.Token&&(o+="&token="+r.Token),setTimeout(function(){t(null,{Url:o})})}});return r?n+"?"+r.Authorization+(r.XCosSecurityToken?"&x-cos-security-token="+r.XCosSecurityToken:""):n},getAuth:function(e){return o.getAuth({SecretId:e.SecretId||this.options.SecretId||"",SecretKey:e.SecretKey||this.options.SecretKey||"",Method:e.Method,Key:e.Key,Query:e.Query,Headers:e.Headers,Expires:e.Expires,UseRawKey:this.options.UseRawKey,SystemClockOffset:this.options.SystemClockOffset})}};e.exports.init=function(e,t){t.transferToTaskMethod(l,"putObject"),o.each(l,function(t,n){e.prototype[n]=o.apiWrapper(n,t)})}},function(e,t,n){var r=n(47),o=function(){var e=[],t=e.slice,n=e.concat,r=e.push,o=e.indexOf,i={},a=i.toString,s=i.hasOwnProperty,c={},u="1.11.1 -css,-css/addGetHookIf,-css/curCSS,-css/defaultDisplay,-css/hiddenVisibleSelectors,-css/support,-css/swap,-css/var/cssExpand,-css/var/isHidden,-css/var/rmargin,-css/var/rnumnonpx,-effects,-effects/Tween,-effects/animatedSelector,-effects/support,-dimensions,-offset,-deprecated,-event-alias,-wrap",l=function(e,t){return new l.fn.init(e,t)},d=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,f=/^-ms-/,p=/-([\da-z])/gi,h=function(e,t){return t.toUpperCase()};function v(e){var t=e.length,n=l.type(e);return"function"!==n&&!l.isWindow(e)&&(!(1!==e.nodeType||!t)||("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e))}l.fn=l.prototype={jquery:u,constructor:l,selector:"",length:0,toArray:function(){return t.call(this)},get:function(e){return null!=e?e<0?this[e+this.length]:this[e]:t.call(this)},pushStack:function(e){var t=l.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e,t){return l.each(this,e,t)},map:function(e){return this.pushStack(l.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(t.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:r,sort:e.sort,splice:e.splice},l.extend=l.fn.extend=function(){var e,t,n,r,o,i,a=arguments[0]||{},s=1,c=arguments.length,u=!1;for("boolean"==typeof a&&(u=a,a=arguments[s]||{},s++),"object"==typeof a||l.isFunction(a)||(a={}),s===c&&(a=this,s--);s<c;s++)if(null!=(o=arguments[s]))for(r in o)e=a[r],a!==(n=o[r])&&(u&&n&&(l.isPlainObject(n)||(t=l.isArray(n)))?(t?(t=!1,i=e&&l.isArray(e)?e:[]):i=e&&l.isPlainObject(e)?e:{},a[r]=l.extend(u,i,n)):void 0!==n&&(a[r]=n));return a},l.extend({expando:"jQuery"+(u+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===l.type(e)},isArray:Array.isArray||function(e){return"array"===l.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){return!l.isArray(e)&&e-parseFloat(e)>=0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},isPlainObject:function(e){var t;if(!e||"object"!==l.type(e)||e.nodeType||l.isWindow(e))return!1;try{if(e.constructor&&!s.call(e,"constructor")&&!s.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}if(c.ownLast)for(t in e)return s.call(e,t);for(t in e);return void 0===t||s.call(e,t)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?i[a.call(e)]||"object":typeof e},globalEval:function(e){e&&l.trim(e)&&(window.execScript||function(e){window.eval.call(window,e)})(e)},camelCase:function(e){return e.replace(f,"ms-").replace(p,h)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,n){var r=0,o=e.length,i=v(e);if(n){if(i)for(;r<o&&!1!==t.apply(e[r],n);r++);else for(r in e)if(!1===t.apply(e[r],n))break}else if(i)for(;r<o&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},trim:function(e){return null==e?"":(e+"").replace(d,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(v(Object(e))?l.merge(n,"string"==typeof e?[e]:e):r.call(n,e)),n},inArray:function(e,t,n){var r;if(t){if(o)return o.call(t,e,n);for(r=t.length,n=n?n<0?Math.max(0,r+n):n:0;n<r;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){for(var n=+t.length,r=0,o=e.length;r<n;)e[o++]=t[r++];if(n!=n)for(;void 0!==t[r];)e[o++]=t[r++];return e.length=o,e},grep:function(e,t,n){for(var r=[],o=0,i=e.length,a=!n;o<i;o++)!t(e[o],o)!==a&&r.push(e[o]);return r},map:function(e,t,r){var o,i=0,a=e.length,s=[];if(v(e))for(;i<a;i++)null!=(o=t(e[i],i,r))&&s.push(o);else for(i in e)null!=(o=t(e[i],i,r))&&s.push(o);return n.apply([],s)},guid:1,proxy:function(e,n){var r,o,i;if("string"==typeof n&&(i=e[n],n=e,e=i),l.isFunction(e))return r=t.call(arguments,2),(o=function(){return e.apply(n||this,r.concat(t.call(arguments)))}).guid=e.guid=e.guid||l.guid++,o},now:function(){return+new Date},support:c}),l.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){i["[object "+t+"]"]=t.toLowerCase()});var m,g=window.document,y=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;(l.fn.init=function(e,t){var n,r;if(!e)return this;if("string"==typeof e){if(!(n="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:y.exec(e))||!n[1]&&t)return!t||t.jquery?(t||m).find(e):this.constructor(t).find(e);if(n[1]){if(t=t instanceof l?t[0]:t,l.merge(this,l.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:g,!0)),rsingleTag.test(n[1])&&l.isPlainObject(t))for(n in t)l.isFunction(this[n])?this[n](t[n]):this.attr(n,t[n]);return this}if((r=g.getElementById(n[2]))&&r.parentNode){if(r.id!==n[2])return m.find(e);this.length=1,this[0]=r}return this.context=g,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):l.isFunction(e)?void 0!==m.ready?m.ready(e):e(l):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),l.makeArray(e,this))}).prototype=l.fn,m=l(g);var b,C=/\S+/g,_={};function w(){g.addEventListener?(g.removeEventListener("DOMContentLoaded",x,!1),window.removeEventListener("load",x,!1)):(g.detachEvent("onreadystatechange",x),window.detachEvent("onload",x))}function x(){(g.addEventListener||"load"===event.type||"complete"===g.readyState)&&(w(),l.ready())}l.Callbacks=function(e){e="string"==typeof e?_[e]||function(e){var t=_[e]={};return l.each(e.match(C)||[],function(e,n){t[n]=!0}),t}(e):l.extend({},e);var t,n,r,o,i,a,s=[],c=!e.once&&[],u=function(l){for(n=e.memory&&l,r=!0,i=a||0,a=0,o=s.length,t=!0;s&&i<o;i++)if(!1===s[i].apply(l[0],l[1])&&e.stopOnFalse){n=!1;break}t=!1,s&&(c?c.length&&u(c.shift()):n?s=[]:d.disable())},d={add:function(){if(s){var r=s.length;!function t(n){l.each(n,function(n,r){var o=l.type(r);"function"===o?e.unique&&d.has(r)||s.push(r):r&&r.length&&"string"!==o&&t(r)})}(arguments),t?o=s.length:n&&(a=r,u(n))}return this},remove:function(){return s&&l.each(arguments,function(e,n){for(var r;(r=l.inArray(n,s,r))>-1;)s.splice(r,1),t&&(r<=o&&o--,r<=i&&i--)}),this},has:function(e){return e?l.inArray(e,s)>-1:!(!s||!s.length)},empty:function(){return s=[],o=0,this},disable:function(){return s=c=n=void 0,this},disabled:function(){return!s},lock:function(){return c=void 0,n||d.disable(),this},locked:function(){return!c},fireWith:function(e,n){return!s||r&&!c||(n=[e,(n=n||[]).slice?n.slice():n],t?c.push(n):u(n)),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!r}};return d},l.extend({Deferred:function(e){var t=[["resolve","done",l.Callbacks("once memory"),"resolved"],["reject","fail",l.Callbacks("once memory"),"rejected"],["notify","progress",l.Callbacks("memory")]],n="pending",r={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},then:function(){var e=arguments;return l.Deferred(function(n){l.each(t,function(t,i){var a=l.isFunction(e[t])&&e[t];o[i[1]](function(){var e=a&&a.apply(this,arguments);e&&l.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[i[0]+"With"](this===r?n.promise():this,a?[e]:arguments)})}),e=null}).promise()},promise:function(e){return null!=e?l.extend(e,r):r}},o={};return r.pipe=r.then,l.each(t,function(e,i){var a=i[2],s=i[3];r[i[1]]=a.add,s&&a.add(function(){n=s},t[1^e][2].disable,t[2][2].lock),o[i[0]]=function(){return o[i[0]+"With"](this===o?r:this,arguments),this},o[i[0]+"With"]=a.fireWith}),r.promise(o),e&&e.call(o,o),o},when:function(e){var n,r,o,i=0,a=t.call(arguments),s=a.length,c=1!==s||e&&l.isFunction(e.promise)?s:0,u=1===c?e:l.Deferred(),d=function(e,r,o){return function(i){r[e]=this,o[e]=arguments.length>1?t.call(arguments):i,o===n?u.notifyWith(r,o):--c||u.resolveWith(r,o)}};if(s>1)for(n=new Array(s),r=new Array(s),o=new Array(s);i<s;i++)a[i]&&l.isFunction(a[i].promise)?a[i].promise().done(d(i,o,a)).fail(u.reject).progress(d(i,r,n)):--c;return c||u.resolveWith(o,a),u.promise()}}),l.fn.ready=function(e){return l.ready.promise().done(e),this},l.extend({isReady:!1,readyWait:1,holdReady:function(e){e?l.readyWait++:l.ready(!0)},ready:function(e){if(!0===e?!--l.readyWait:!l.isReady){if(!g.body)return setTimeout(l.ready);l.isReady=!0,!0!==e&&--l.readyWait>0||(b.resolveWith(g,[l]),l.fn.triggerHandler&&(l(g).triggerHandler("ready"),l(g).off("ready")))}}}),l.ready.promise=function(e){if(!b)if(b=l.Deferred(),"complete"===g.readyState)setTimeout(l.ready);else if(g.addEventListener)g.addEventListener("DOMContentLoaded",x,!1),window.addEventListener("load",x,!1);else{g.attachEvent("onreadystatechange",x),window.attachEvent("onload",x);var t=!1;try{t=null==window.frameElement&&g.documentElement}catch(e){}t&&t.doScroll&&function e(){if(!l.isReady){try{t.doScroll("left")}catch(t){return setTimeout(e,50)}w(),l.ready()}}()}return b.promise(e)};var S;for(S in l(c))break;c.ownLast="0"!==S,c.inlineBlockNeedsLayout=!1,l(function(){var e,t,n,r;(n=g.getElementsByTagName("body")[0])&&n.style&&(t=g.createElement("div"),(r=g.createElement("div")).style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(r).appendChild(t),void 0!==t.style.zoom&&(t.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",c.inlineBlockNeedsLayout=e=3===t.offsetWidth,e&&(n.style.zoom=1)),n.removeChild(r))}),function(){var e=g.createElement("div");if(null==c.deleteExpando){c.deleteExpando=!0;try{delete e.test}catch(e){c.deleteExpando=!1}}e=null}(),l.acceptData=function(e){var t=l.noData[(e.nodeName+" ").toLowerCase()],n=+e.nodeType||1;return(1===n||9===n)&&(!t||!0!==t&&e.getAttribute("classid")===t)};var k=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,T=/([A-Z])/g;function A(e,t,n){if(void 0===n&&1===e.nodeType){var r="data-"+t.replace(T,"-$1").toLowerCase();if("string"==typeof(n=e.getAttribute(r))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:k.test(n)?l.parseJSON(n):n)}catch(e){}l.data(e,t,n)}else n=void 0}return n}function E(e){var t;for(t in e)if(("data"!==t||!l.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}function R(t,n,r,o){if(l.acceptData(t)){var i,a,s=l.expando,c=t.nodeType,u=c?l.cache:t,d=c?t[s]:t[s]&&s;if(d&&u[d]&&(o||u[d].data)||void 0!==r||"string"!=typeof n)return d||(d=c?t[s]=e.pop()||l.guid++:s),u[d]||(u[d]=c?{}:{toJSON:l.noop}),"object"!=typeof n&&"function"!=typeof n||(o?u[d]=l.extend(u[d],n):u[d].data=l.extend(u[d].data,n)),a=u[d],o||(a.data||(a.data={}),a=a.data),void 0!==r&&(a[l.camelCase(n)]=r),"string"==typeof n?null==(i=a[n])&&(i=a[l.camelCase(n)]):i=a,i}}function O(e,t,n){if(l.acceptData(e)){var r,o,i=e.nodeType,a=i?l.cache:e,s=i?e[l.expando]:l.expando;if(a[s]){if(t&&(r=n?a[s]:a[s].data)){o=(t=l.isArray(t)?t.concat(l.map(t,l.camelCase)):t in r?[t]:(t=l.camelCase(t))in r?[t]:t.split(" ")).length;for(;o--;)delete r[t[o]];if(n?!E(r):!l.isEmptyObject(r))return}(n||(delete a[s].data,E(a[s])))&&(i?l.cleanData([e],!0):c.deleteExpando||a!=a.window?delete a[s]:a[s]=null)}}}l.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-************"},hasData:function(e){return!!(e=e.nodeType?l.cache[e[l.expando]]:e[l.expando])&&!E(e)},data:function(e,t,n){return R(e,t,n)},removeData:function(e,t){return O(e,t)},_data:function(e,t,n){return R(e,t,n,!0)},_removeData:function(e,t){return O(e,t,!0)}}),l.fn.extend({data:function(e,t){var n,r,o,i=this[0],a=i&&i.attributes;if(void 0===e){if(this.length&&(o=l.data(i),1===i.nodeType&&!l._data(i,"parsedAttrs"))){for(n=a.length;n--;)a[n]&&0===(r=a[n].name).indexOf("data-")&&A(i,r=l.camelCase(r.slice(5)),o[r]);l._data(i,"parsedAttrs",!0)}return o}return"object"==typeof e?this.each(function(){l.data(this,e)}):arguments.length>1?this.each(function(){l.data(this,e,t)}):i?A(i,e,l.data(i,e)):void 0},removeData:function(e){return this.each(function(){l.removeData(this,e)})}}),l.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=l._data(e,t),n&&(!r||l.isArray(n)?r=l._data(e,t,l.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=l.queue(e,t),r=n.length,o=n.shift(),i=l._queueHooks(e,t);"inprogress"===o&&(o=n.shift(),r--),o&&("fx"===t&&n.unshift("inprogress"),delete i.stop,o.call(e,function(){l.dequeue(e,t)},i)),!r&&i&&i.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return l._data(e,n)||l._data(e,n,{empty:l.Callbacks("once memory").add(function(){l._removeData(e,t+"queue"),l._removeData(e,n)})})}}),l.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?l.queue(this[0],e):void 0===t?this:this.each(function(){var n=l.queue(this,e,t);l._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&l.dequeue(this,e)})},dequeue:function(e){return this.each(function(){l.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,o=l.Deferred(),i=this,a=this.length,s=function(){--r||o.resolveWith(i,[i])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(n=l._data(i[a],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(s));return s(),o.promise(t)}}),l.event={global:{},add:function(e,t,n,r,o){var i,a,s,c,u,d,f,p,h,v,m,g=l._data(e);if(g){for(n.handler&&(n=(c=n).handler,o=c.selector),n.guid||(n.guid=l.guid++),(a=g.events)||(a=g.events={}),(d=g.handle)||((d=g.handle=function(e){return void 0===l||e&&l.event.triggered===e.type?void 0:l.event.dispatch.apply(d.elem,arguments)}).elem=e),s=(t=(t||"").match(C)||[""]).length;s--;)h=m=(i=j.exec(t[s])||[])[1],v=(i[2]||"").split(".").sort(),h&&(u=l.event.special[h]||{},h=(o?u.delegateType:u.bindType)||h,u=l.event.special[h]||{},f=l.extend({type:h,origType:m,data:r,handler:n,guid:n.guid,selector:o,needsContext:o&&l.expr.match.needsContext.test(o),namespace:v.join(".")},c),(p=a[h])||((p=a[h]=[]).delegateCount=0,u.setup&&!1!==u.setup.call(e,r,v,d)||(e.addEventListener?e.addEventListener(h,d,!1):e.attachEvent&&e.attachEvent("on"+h,d))),u.add&&(u.add.call(e,f),f.handler.guid||(f.handler.guid=n.guid)),o?p.splice(p.delegateCount++,0,f):p.push(f),l.event.global[h]=!0);e=null}},remove:function(e,t,n,r,o){var i,a,s,c,u,d,f,p,h,v,m,g=l.hasData(e)&&l._data(e);if(g&&(d=g.events)){for(u=(t=(t||"").match(C)||[""]).length;u--;)if(h=m=(s=j.exec(t[u])||[])[1],v=(s[2]||"").split(".").sort(),h){for(f=l.event.special[h]||{},p=d[h=(r?f.delegateType:f.bindType)||h]||[],s=s[2]&&new RegExp("(^|\\.)"+v.join("\\.(?:.*\\.|)")+"(\\.|$)"),c=i=p.length;i--;)a=p[i],!o&&m!==a.origType||n&&n.guid!==a.guid||s&&!s.test(a.namespace)||r&&r!==a.selector&&("**"!==r||!a.selector)||(p.splice(i,1),a.selector&&p.delegateCount--,f.remove&&f.remove.call(e,a));c&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,v,g.handle)||l.removeEvent(e,h,g.handle),delete d[h])}else for(h in d)l.event.remove(e,h+t[u],n,r,!0);l.isEmptyObject(d)&&(delete g.handle,l._removeData(e,"events"))}},trigger:function(e,t,n,r){var o,i,a,c,u,d,f,p=[n||g],h=s.call(e,"type")?e.type:e,v=s.call(e,"namespace")?e.namespace.split("."):[];if(a=d=n=n||g,3!==n.nodeType&&8!==n.nodeType&&!D.test(h+l.event.triggered)&&(h.indexOf(".")>=0&&(v=h.split("."),h=v.shift(),v.sort()),i=h.indexOf(":")<0&&"on"+h,(e=e[l.expando]?e:new l.Event(h,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=v.join("."),e.namespace_re=e.namespace?new RegExp("(^|\\.)"+v.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:l.makeArray(t,[e]),u=l.event.special[h]||{},r||!u.trigger||!1!==u.trigger.apply(n,t))){if(!r&&!u.noBubble&&!l.isWindow(n)){for(c=u.delegateType||h,D.test(c+h)||(a=a.parentNode);a;a=a.parentNode)p.push(a),d=a;d===(n.ownerDocument||g)&&p.push(d.defaultView||d.parentWindow||window)}for(f=0;(a=p[f++])&&!e.isPropagationStopped();)e.type=f>1?c:u.bindType||h,(o=(l._data(a,"events")||{})[e.type]&&l._data(a,"handle"))&&o.apply(a,t),(o=i&&a[i])&&o.apply&&l.acceptData(a)&&(e.result=o.apply(a,t),!1===e.result&&e.preventDefault());if(e.type=h,!r&&!e.isDefaultPrevented()&&(!u._default||!1===u._default.apply(p.pop(),t))&&l.acceptData(n)&&i&&n[h]&&!l.isWindow(n)){(d=n[i])&&(n[i]=null),l.event.triggered=h;try{n[h]()}catch(e){}l.event.triggered=void 0,d&&(n[i]=d)}return e.result}},dispatch:function(e){e=l.event.fix(e);var n,r,o,i,a,s,c=t.call(arguments),u=(l._data(this,"events")||{})[e.type]||[],d=l.event.special[e.type]||{};if(c[0]=e,e.delegateTarget=this,!d.preDispatch||!1!==d.preDispatch.call(this,e)){for(s=l.event.handlers.call(this,e,u),n=0;(i=s[n++])&&!e.isPropagationStopped();)for(e.currentTarget=i.elem,a=0;(o=i.handlers[a++])&&!e.isImmediatePropagationStopped();)e.namespace_re&&!e.namespace_re.test(o.namespace)||(e.handleObj=o,e.data=o.data,void 0!==(r=((l.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,c))&&!1===(e.result=r)&&(e.preventDefault(),e.stopPropagation()));return d.postDispatch&&d.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,r,o,i,a=[],s=t.delegateCount,c=e.target;if(s&&c.nodeType&&(!e.button||"click"!==e.type))for(;c!=this;c=c.parentNode||this)if(1===c.nodeType&&(!0!==c.disabled||"click"!==e.type)){for(o=[],i=0;i<s;i++)void 0===o[n=(r=t[i]).selector+" "]&&(o[n]=r.needsContext?l(n,this).index(c)>=0:l.find(n,this,null,[c]).length),o[n]&&o.push(r);o.length&&a.push({elem:c,handlers:o})}return s<t.length&&a.push({elem:this,handlers:t.slice(s)}),a},fix:function(e){if(e[l.expando])return e;var t,n,r,o=e.type,i=e,a=this.fixHooks[o];for(a||(this.fixHooks[o]=a=P.test(o)?this.mouseHooks:I.test(o)?this.keyHooks:{}),r=a.props?this.props.concat(a.props):this.props,e=new l.Event(i),t=r.length;t--;)e[n=r[t]]=i[n];return e.target||(e.target=i.srcElement||g),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,a.filter?a.filter(e,i):e},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,r,o,i=t.button,a=t.fromElement;return null==e.pageX&&null!=t.clientX&&(o=(r=e.target.ownerDocument||g).documentElement,n=r.body,e.pageX=t.clientX+(o&&o.scrollLeft||n&&n.scrollLeft||0)-(o&&o.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(o&&o.scrollTop||n&&n.scrollTop||0)-(o&&o.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&a&&(e.relatedTarget=a===e.target?t.toElement:a),e.which||void 0===i||(e.which=1&i?1:2&i?3:4&i?2:0),e}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==safeActiveElement()&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){if(this===safeActiveElement()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if(l.nodeName(this,"input")&&"checkbox"===this.type&&this.click)return this.click(),!1},_default:function(e){return l.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,r){var o=l.extend(new l.Event,n,{type:e,isSimulated:!0,originalEvent:{}});r?l.event.trigger(o,null,t):l.event.dispatch.call(t,o),o.isDefaultPrevented()&&n.preventDefault()}},l.removeEvent=g.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)}:function(e,t,n){var r="on"+t;e.detachEvent&&(void 0===e[r]&&(e[r]=null),e.detachEvent(r,n))},l.Event=function(e,t){if(!(this instanceof l.Event))return new l.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?B:M):this.type=e,t&&l.extend(this,t),this.timeStamp=e&&e.timeStamp||l.now(),this[l.expando]=!0};var N=/^(?:input|select|textarea)$/i,I=/^key/,P=/^(?:mouse|pointer|contextmenu)|click/,D=/^(?:focusinfocus|focusoutblur)$/,j=/^([^.]*)(?:\.(.+)|)$/;function B(){return!0}function M(){return!1}l.Event.prototype={isDefaultPrevented:M,isPropagationStopped:M,isImmediatePropagationStopped:M,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=B,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=B,e&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=B,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},c.submitBubbles||(l.event.special.submit={setup:function(){if(l.nodeName(this,"form"))return!1;l.event.add(this,"click._submit keypress._submit",function(e){var t=e.target,n=l.nodeName(t,"input")||l.nodeName(t,"button")?t.form:void 0;n&&!l._data(n,"submitBubbles")&&(l.event.add(n,"submit._submit",function(e){e._submit_bubble=!0}),l._data(n,"submitBubbles",!0))})},postDispatch:function(e){e._submit_bubble&&(delete e._submit_bubble,this.parentNode&&!e.isTrigger&&l.event.simulate("submit",this.parentNode,e,!0))},teardown:function(){if(l.nodeName(this,"form"))return!1;l.event.remove(this,"._submit")}}),c.changeBubbles||(l.event.special.change={setup:function(){if(N.test(this.nodeName))return"checkbox"!==this.type&&"radio"!==this.type||(l.event.add(this,"propertychange._change",function(e){"checked"===e.originalEvent.propertyName&&(this._just_changed=!0)}),l.event.add(this,"click._change",function(e){this._just_changed&&!e.isTrigger&&(this._just_changed=!1),l.event.simulate("change",this,e,!0)})),!1;l.event.add(this,"beforeactivate._change",function(e){var t=e.target;N.test(t.nodeName)&&!l._data(t,"changeBubbles")&&(l.event.add(t,"change._change",function(e){!this.parentNode||e.isSimulated||e.isTrigger||l.event.simulate("change",this.parentNode,e,!0)}),l._data(t,"changeBubbles",!0))})},handle:function(e){var t=e.target;if(this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type)return e.handleObj.handler.apply(this,arguments)},teardown:function(){return l.event.remove(this,"._change"),!N.test(this.nodeName)}}),c.focusinBubbles||l.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){l.event.simulate(t,e.target,l.event.fix(e),!0)};l.event.special[t]={setup:function(){var r=this.ownerDocument||this,o=l._data(r,t);o||r.addEventListener(e,n,!0),l._data(r,t,(o||0)+1)},teardown:function(){var r=this.ownerDocument||this,o=l._data(r,t)-1;o?l._data(r,t,o):(r.removeEventListener(e,n,!0),l._removeData(r,t))}}}),l.fn.extend({on:function(e,t,n,r,o){var i,a;if("object"==typeof e){for(i in"string"!=typeof t&&(n=n||t,t=void 0),e)this.on(i,t,n,e[i],o);return this}if(null==n&&null==r?(r=t,n=t=void 0):null==r&&("string"==typeof t?(r=n,n=void 0):(r=n,n=t,t=void 0)),!1===r)r=M;else if(!r)return this;return 1===o&&(a=r,(r=function(e){return l().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=l.guid++)),this.each(function(){l.event.add(this,e,r,n,t)})},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,t,n){var r,o;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,l(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(o in e)this.off(o,t,e[o]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=M),this.each(function(){l.event.remove(this,e,n,t)})},trigger:function(e,t){return this.each(function(){l.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return l.event.trigger(e,t,n,!0)}}),l.fn.delay=function(e,t){return e=l.fx&&l.fx.speeds[e]||e,t=t||"fx",this.queue(t,function(t,n){var r=setTimeout(t,e);n.stop=function(){clearTimeout(r)}})};var L=l.now(),$=/\?/,U=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;l.parseJSON=function(e){if(window.JSON&&window.JSON.parse)return window.JSON.parse(e+"");var t,n=null,r=l.trim(e+"");return r&&!l.trim(r.replace(U,function(e,r,o,i){return t&&r&&(n=0),0===n?e:(t=o||r,n+=!i-!o,"")}))?Function("return "+r)():l.error("Invalid JSON: "+e)},l.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{window.DOMParser?t=(new DOMParser).parseFromString(e,"text/xml"):((t=new ActiveXObject("Microsoft.XMLDOM")).async="false",t.loadXML(e))}catch(e){t=void 0}return t&&t.documentElement&&!t.getElementsByTagName("parsererror").length||l.error("Invalid XML: "+e),t};var F,H,K=/#.*$/,z=/([?&])_=[^&]*/,q=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,V=/^(?:GET|HEAD)$/,X=/^\/\//,G=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,W={},J={},Y="*/".concat("*");try{H=location.href}catch(e){(H=g.createElement("a")).href="",H=H.href}function Q(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,o=0,i=t.toLowerCase().match(C)||[];if(l.isFunction(n))for(;r=i[o++];)"+"===r.charAt(0)?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function Z(e,t,n,r){var o={},i=e===J;function a(s){var c;return o[s]=!0,l.each(e[s]||[],function(e,s){var u=s(t,n,r);return"string"!=typeof u||i||o[u]?i?!(c=u):void 0:(t.dataTypes.unshift(u),a(u),!1)}),c}return a(t.dataTypes[0])||!o["*"]&&a("*")}function ee(e,t){var n,r,o=l.ajaxSettings.flatOptions||{};for(r in t)void 0!==t[r]&&((o[r]?e:n||(n={}))[r]=t[r]);return n&&l.extend(!0,e,n),e}F=G.exec(H.toLowerCase())||[],l.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:H,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(F[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Y,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":l.parseJSON,"text xml":l.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?ee(ee(e,l.ajaxSettings),t):ee(l.ajaxSettings,e)},ajaxPrefilter:Q(W),ajaxTransport:Q(J),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var n,r,o,i,a,s,c,u,d=l.ajaxSetup({},t),f=d.context||d,p=d.context&&(f.nodeType||f.jquery)?l(f):l.event,h=l.Deferred(),v=l.Callbacks("once memory"),m=d.statusCode||{},g={},y={},b=0,_="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(2===b){if(!u)for(u={};t=q.exec(i);)u[t[1].toLowerCase()]=t[2];t=u[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===b?i:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return b||(e=y[n]=y[n]||e,g[e]=t),this},overrideMimeType:function(e){return b||(d.mimeType=e),this},statusCode:function(e){var t;if(e)if(b<2)for(t in e)m[t]=[m[t],e[t]];else w.always(e[w.status]);return this},abort:function(e){var t=e||_;return c&&c.abort(t),x(0,t),this}};if(h.promise(w).complete=v.add,w.success=w.done,w.error=w.fail,d.url=((e||d.url||H)+"").replace(K,"").replace(X,F[1]+"//"),d.type=t.method||t.type||d.method||d.type,d.dataTypes=l.trim(d.dataType||"*").toLowerCase().match(C)||[""],null==d.crossDomain&&(n=G.exec(d.url.toLowerCase()),d.crossDomain=!(!n||n[1]===F[1]&&n[2]===F[2]&&(n[3]||("http:"===n[1]?"80":"443"))===(F[3]||("http:"===F[1]?"80":"443")))),d.data&&d.processData&&"string"!=typeof d.data&&(d.data=l.param(d.data,d.traditional)),Z(W,d,t,w),2===b)return w;for(r in(s=d.global)&&0==l.active++&&l.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!V.test(d.type),o=d.url,d.hasContent||(d.data&&(o=d.url+=($.test(o)?"&":"?")+d.data,delete d.data),!1===d.cache&&(d.url=z.test(o)?o.replace(z,"$1_="+L++):o+($.test(o)?"&":"?")+"_="+L++)),d.ifModified&&(l.lastModified[o]&&w.setRequestHeader("If-Modified-Since",l.lastModified[o]),l.etag[o]&&w.setRequestHeader("If-None-Match",l.etag[o])),(d.data&&d.hasContent&&!1!==d.contentType||t.contentType)&&w.setRequestHeader("Content-Type",d.contentType),d.headers)w.setRequestHeader(r,d.headers[r]);if(d.beforeSend&&(!1===d.beforeSend.call(f,w,d)||2===b))return w.abort();for(r in _="abort",{success:1,error:1,complete:1})w[r](d[r]);if(c=Z(J,d,t,w)){w.readyState=1,s&&p.trigger("ajaxSend",[w,d]),d.async&&d.timeout>0&&(a=setTimeout(function(){w.abort("timeout")},d.timeout));try{b=1,c.send(g,x)}catch(e){if(!(b<2))throw e;x(-1,e)}}else x(-1,"No Transport");function x(e,t,n,r){var u,g,y,C,_,x=t;2!==b&&(b=2,a&&clearTimeout(a),c=void 0,i=r||"",w.readyState=e>0?4:0,u=e>=200&&e<300||304===e,n&&(C=function(e,t,n){for(var r,o,i,a,s=e.contents,c=e.dataTypes;"*"===c[0];)c.shift(),void 0===o&&(o=e.mimeType||t.getResponseHeader("Content-Type"));if(o)for(a in s)if(s[a]&&s[a].test(o)){c.unshift(a);break}if(c[0]in n)i=c[0];else{for(a in n){if(!c[0]||e.converters[a+" "+c[0]]){i=a;break}r||(r=a)}i=i||r}if(i)return i!==c[0]&&c.unshift(i),n[i]}(d,w,n)),C=function(e,t,n,r){var o,i,a,s,c,u={},l=e.dataTypes.slice();if(l[1])for(a in e.converters)u[a.toLowerCase()]=e.converters[a];for(i=l.shift();i;)if(e.responseFields[i]&&(n[e.responseFields[i]]=t),!c&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),c=i,i=l.shift())if("*"===i)i=c;else if("*"!==c&&c!==i){if(!(a=u[c+" "+i]||u["* "+i]))for(o in u)if((s=o.split(" "))[1]===i&&(a=u[c+" "+s[0]]||u["* "+s[0]])){!0===a?a=u[o]:!0!==u[o]&&(i=s[0],l.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+c+" to "+i}}}return{state:"success",data:t}}(d,C,w,u),u?(d.ifModified&&((_=w.getResponseHeader("Last-Modified"))&&(l.lastModified[o]=_),(_=w.getResponseHeader("etag"))&&(l.etag[o]=_)),204===e||"HEAD"===d.type?x="nocontent":304===e?x="notmodified":(x=C.state,g=C.data,u=!(y=C.error))):(y=x,!e&&x||(x="error",e<0&&(e=0))),w.status=e,w.statusText=(t||x)+"",u?h.resolveWith(f,[g,x,w]):h.rejectWith(f,[w,x,y]),w.statusCode(m),m=void 0,s&&p.trigger(u?"ajaxSuccess":"ajaxError",[w,d,u?g:y]),v.fireWith(f,[w,x]),s&&(p.trigger("ajaxComplete",[w,d]),--l.active||l.event.trigger("ajaxStop")))}return w},getJSON:function(e,t,n){return l.get(e,t,n,"json")},getScript:function(e,t){return l.get(e,void 0,t,"script")}}),l.each(["get","post"],function(e,t){l[t]=function(e,n,r,o){return l.isFunction(n)&&(o=o||r,r=n,n=void 0),l.ajax({url:e,type:t,dataType:o,data:n,success:r})}}),l.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){l.fn[t]=function(e){return this.on(t,e)}}),l._evalUrl=function(e){return l.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,throws:!0})};var te=/%20/g,ne=/\[\]$/,re=/\r?\n/g,oe=/^(?:submit|button|image|reset|file)$/i,ie=/^(?:input|select|textarea|keygen)/i;function ae(e,t,n,r){var o;if(l.isArray(t))l.each(t,function(t,o){n||ne.test(e)?r(e,o):ae(e+"["+("object"==typeof o?t:"")+"]",o,n,r)});else if(n||"object"!==l.type(t))r(e,t);else for(o in t)ae(e+"["+o+"]",t[o],n,r)}l.param=function(e,t){var n,r=[],o=function(e,t){t=l.isFunction(t)?t():null==t?"":t,r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=l.ajaxSettings&&l.ajaxSettings.traditional),l.isArray(e)||e.jquery&&!l.isPlainObject(e))l.each(e,function(){o(this.name,this.value)});else for(n in e)ae(n,e[n],t,o);return r.join("&").replace(te,"+")},l.fn.extend({serialize:function(){return l.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=l.prop(this,"elements");return e?l.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!l(this).is(":disabled")&&ie.test(this.nodeName)&&!oe.test(e)&&(this.checked||!rcheckableType.test(e))}).map(function(e,t){var n=l(this).val();return null==n?null:l.isArray(n)?l.map(n,function(e){return{name:t.name,value:e.replace(re,"\r\n")}}):{name:t.name,value:n.replace(re,"\r\n")}}).get()}}),l.ajaxSettings.xhr=void 0!==window.ActiveXObject?function(){return!this.isLocal&&/^(get|post|head|put|delete|options)$/i.test(this.type)&&le()||function(){try{return new window.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}()}:le;var se=0,ce={},ue=l.ajaxSettings.xhr();function le(){try{return new window.XMLHttpRequest}catch(e){}}window.ActiveXObject&&l(window).on("unload",function(){for(var e in ce)ce[e](void 0,!0)}),c.cors=!!ue&&"withCredentials"in ue,(ue=c.ajax=!!ue)&&l.ajaxTransport(function(e){var t;if(!e.crossDomain||c.cors)return{send:function(n,r){var o,i=e.xhr(),a=++se;if(i.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(o in e.xhrFields)i[o]=e.xhrFields[o];for(o in e.mimeType&&i.overrideMimeType&&i.overrideMimeType(e.mimeType),e.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest"),n)void 0!==n[o]&&i.setRequestHeader(o,n[o]+"");i.upload&&e.progress&&(i.upload.onprogress=e.progress),i.send(e.hasContent&&(e.body||e.data)||null),t=function(n,o){var s,c,u;if(t&&(o||4===i.readyState))if(delete ce[a],t=void 0,i.onreadystatechange=l.noop,o)4!==i.readyState&&i.abort();else{u={},s=i.status,"string"==typeof i.responseText&&(u.text=i.responseText);try{c=i.statusText}catch(e){c=""}s||!e.isLocal||e.crossDomain?1223===s&&(s=204):s=u.text?200:404}u&&r(s,c,u,i.getAllResponseHeaders())},e.async?4===i.readyState?setTimeout(t):i.onreadystatechange=ce[a]=t:t()},abort:function(){t&&t(void 0,!0)}}}),l.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return l.globalEval(e),e}}}),l.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),l.ajaxTransport("script",function(e){if(e.crossDomain){var t,n=g.head||l("head")[0]||g.documentElement;return{send:function(r,o){(t=g.createElement("script")).async=!0,e.scriptCharset&&(t.charset=e.scriptCharset),t.src=e.url,t.onload=t.onreadystatechange=function(e,n){(n||!t.readyState||/loaded|complete/.test(t.readyState))&&(t.onload=t.onreadystatechange=null,t.parentNode&&t.parentNode.removeChild(t),t=null,n||o(200,"success"))},n.insertBefore(t,n.firstChild)},abort:function(){t&&t.onload(void 0,!0)}}}});var de=[],fe=/(=)\?(?=&|$)|\?\?/;return l.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=de.pop()||l.expando+"_"+L++;return this[e]=!0,e}}),l.ajaxPrefilter("json jsonp",function(e,t,n){var r,o,i,a=!1!==e.jsonp&&(fe.test(e.url)?"url":"string"==typeof e.data&&!(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&fe.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=l.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(fe,"$1"+r):!1!==e.jsonp&&(e.url+=($.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return i||l.error(r+" was not called"),i[0]},e.dataTypes[0]="json",o=window[r],window[r]=function(){i=arguments},n.always(function(){window[r]=o,e[r]&&(e.jsonpCallback=t.jsonpCallback,de.push(r)),i&&l.isFunction(o)&&o(i[0]),i=o=void 0}),"script"}),l.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||g;var r=rsingleTag.exec(e),o=!n&&[];return r?[t.createElement(r[1])]:(r=l.buildFragment([e],t,o),o&&o.length&&l(o).remove(),l.merge([],r.childNodes))},l}();e.exports=function(e,t){if((e=o.extend(!0,{headers:{},qs:{}},e)).type=e.method,delete e.method,e.onProgress&&(e.progress=e.onProgress,delete e.onProgress),e.qs){var n=r.stringify(e.qs);n&&(e.url+=(-1===e.url.indexOf("?")?"?":"&")+n),delete e.qs}if(e.json&&(e.data=e.body,delete e.json,delete e.body,!e.headers&&(e.headers={}),e.headers["Content-Type"]="application/json"),e.body&&(e.body instanceof Blob||"[object File]"===e.body.toString()||"[object Blob]"===e.body.toString()||(e.data=e.body,delete e.body)),e.headers){var i=e.headers;delete e.headers,e.beforeSend=function(e){for(var t in i)i.hasOwnProperty(t)&&"content-length"!==t.toLowerCase()&&"user-agent"!==t.toLowerCase()&&"origin"!==t.toLowerCase()&&"host"!==t.toLowerCase()&&e.setRequestHeader(t,i[t])}}var a=function(e){var t={};return e.getAllResponseHeaders().trim().split("\n").forEach(function(e){if(e){var n=e.indexOf(":"),r=e.substr(0,n).trim().toLowerCase(),o=e.substr(n+1).trim();t[r]=o}}),{statusCode:e.status,statusMessage:e.statusText,headers:t}};return e.success=function(e,n,r){t(null,a(r),e)},e.error=function(e){e.responseText?t(null,a(e),e.responseText):t(e.statusText,a(e),e.responseText)},e.dataType="text",o.ajax(e)}},function(e,t,n){"use strict";t.decode=t.parse=n(48),t.encode=t.stringify=n(49)},function(e,t,n){"use strict";function r(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.exports=function(e,t,n,i){t=t||"&",n=n||"=";var a={};if("string"!=typeof e||0===e.length)return a;var s=/\+/g;e=e.split(t);var c=1e3;i&&"number"==typeof i.maxKeys&&(c=i.maxKeys);var u=e.length;c>0&&u>c&&(u=c);for(var l=0;l<u;++l){var d,f,p,h,v=e[l].replace(s,"%20"),m=v.indexOf(n);m>=0?(d=v.substr(0,m),f=v.substr(m+1)):(d=v,f=""),p=decodeURIComponent(d),h=decodeURIComponent(f),r(a,p)?o(a[p])?a[p].push(h):a[p]=[a[p],h]:a[p]=h}return a};var o=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},function(e,t,n){"use strict";var r=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};e.exports=function(e,t,n,s){return t=t||"&",n=n||"=",null===e&&(e=void 0),"object"==typeof e?i(a(e),function(a){var s=encodeURIComponent(r(a))+n;return o(e[a])?i(e[a],function(e){return s+encodeURIComponent(r(e))}).join(t):s+encodeURIComponent(r(e[a]))}).join(t):s?encodeURIComponent(r(s))+n+encodeURIComponent(r(e)):""};var o=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function i(e,t){if(e.map)return e.map(t);for(var n=[],r=0;r<e.length;r++)n.push(t(e[r],r));return n}var a=Object.keys||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t}},function(e,t,n){var r,o=n(51),i=n(43).EventProxy,a=n(35);var s={},c="cos_sdk_upload_cache";function u(){var e=this.options.UploadIdCacheLimit;if(!r){if(e)try{r=JSON.parse(a.localStorage.getItem(c))||[]}catch(e){}r||(r=[])}}function l(e){u.call(this),delete s[e];for(var t=r.length-1;t>=0;t--)r[t][1]===e&&r.splice(t,1);var n=this.options.UploadIdCacheLimit;r.length>n&&r.splice(n),n&&setTimeout(function(){try{r.length?a.localStorage.setItem(c,JSON.stringify(r)):a.localStorage.removeItem(c)}catch(e){}})}function d(e){u.call(this);for(var t=[],n=0;n<r.length;n++)r[n][0]===e&&t.push(r[n][1]);return t.length?t:null}function f(e,t){var n=this,r=[],o={Bucket:e.Bucket,Region:e.Region,Prefix:e.Key},i=function(){n.multipartList(o,function(e,n){if(e)return t(e);r.push.apply(r,n.Upload||[]),"true"==n.IsTruncated?(o.KeyMarker=n.NextKeyMarker,o.UploadIdMarker=n.NextUploadIdMarker,i()):t(null,{UploadList:r})})};i()}function p(e,t){var n=this,r=[],o={Bucket:e.Bucket,Region:e.Region,Key:e.Key,UploadId:e.UploadId},i=function(){n.multipartListPart(o,function(e,n){if(e)return t(e);r.push.apply(r,n.Part||[]),"true"==n.IsTruncated?(o.PartNumberMarker=n.NextPartNumberMarker,i()):t(null,{PartList:r})})};i()}var h={sliceUploadFile:function(e,t){var n,h,v=this,m=new i,g=e.TaskId,y=e.Bucket,b=e.Region,C=e.Key,_=e.Body,w=e.ChunkSize||e.SliceSize||v.options.ChunkSize,x=e.AsyncLimit,S=e.StorageClass||"Standard",k=e.ServerSideEncryption,T=e.onHashProgress;m.on("error",function(e){if(v._isRunningTask(g))return t(e)}),m.on("upload_complete",function(e){t(null,e)}),m.on("upload_slice_complete",function(e){(function(e,t){var n=e.Bucket,r=e.Region,i=e.Key,a=e.UploadId,s=e.SliceList,c=this,u=this.options.ChunkRetryTimes+1,l=s.map(function(e){return{PartNumber:e.PartNumber,ETag:e.ETag}});o.retry(u,function(e){c.multipartComplete({Bucket:n,Region:r,Key:i,UploadId:a,Parts:l},e)},function(e,n){t(e,n)})}).call(v,{Bucket:y,Region:b,Key:C,UploadId:e.UploadId,SliceList:e.SliceList},function(t,r){if(v._isRunningTask(g)){if(delete s[e.UploadId],t)return h(null,!0),m.emit("error",t);h({loaded:n,total:n},!0),l.call(v,e.UploadId),m.emit("upload_complete",r)}})}),m.on("get_upload_data_finish",function(t){var i=a.getFileUUID(_,e.ChunkSize);i&&function(e,t,n){u.call(this);for(var o=r.length-1;o>=0;o--)r[o][0]===e&&r[o][1]===t&&r.splice(o,1);r.unshift([e,t]);var i=this.options.UploadIdCacheLimit;r.length>i&&r.splice(i),i&&setTimeout(function(){try{a.localStorage.setItem(c,JSON.stringify(r))}catch(e){}})}.call(v,i,t.UploadId),s[t.UploadId]=!0,g&&v.on("inner-kill-task",function(e){e.TaskId===g&&"canceled"===e.toState&&delete s[t.UploadId]}),function(e,t){var n=this,r=e.TaskId,i=e.Bucket,s=e.Region,c=e.Key,u=e.UploadData,l=e.FileSize,d=e.SliceSize,f=Math.min(e.AsyncLimit||n.options.ChunkParallelLimit||1,256),p=e.Body,h=Math.ceil(l/d),v=0,m=e.ServerSideEncryption,g=a.filter(u.PartList,function(e){return e.Uploaded&&(v+=e.PartNumber>=h&&l%d||d),!e.Uploaded}),y=e.onProgress;o.eachLimit(g,f,function(e,t){if(n._isRunningTask(r)){var f=e.PartNumber,h=Math.min(l,e.PartNumber*d)-(e.PartNumber-1)*d,g=0;(function(e,t){var n=this,r=e.TaskId,i=e.Bucket,s=e.Region,c=e.Key,u=e.FileSize,l=e.Body,d=1*e.PartNumber,f=e.SliceSize,p=e.ServerSideEncryption,h=e.UploadData,v=n.options.ChunkRetryTimes+1,m=f*(d-1),g=f,y=m+f;y>u&&(g=(y=u)-m);var b=h.PartList[d-1];o.retry(v,function(t){n._isRunningTask(r)&&a.fileSlice(l,m,y,!0,function(o){n.multipartUpload({TaskId:r,Bucket:i,Region:s,Key:c,ContentLength:g,PartNumber:d,UploadId:h.UploadId,ServerSideEncryption:p,Body:o,onProgress:e.onProgress},function(e,o){if(n._isRunningTask(r))return e?t(e):(b.Uploaded=!0,t(null,o))})})},function(e,o){if(n._isRunningTask(r))return t(e,o)})}).call(n,{TaskId:r,Bucket:i,Region:s,Key:c,SliceSize:d,FileSize:l,PartNumber:f,ServerSideEncryption:m,Body:p,UploadData:u,onProgress:function(e){v+=e.loaded-g,g=e.loaded,y({loaded:v,total:l})}},function(o,i){n._isRunningTask(r)&&(!a.isBrowser||o||i.ETag||(o='get ETag error, please add "ETag" to CORS ExposeHeader setting.'),o?v-=g:(v+=h-g,e.ETag=i.ETag),t(o||null,i))})}},function(e){if(n._isRunningTask(r))return e?t(e):void t(null,{UploadId:u.UploadId,SliceList:u.PartList})})}.call(v,{TaskId:g,Bucket:y,Region:b,Key:C,Body:_,FileSize:n,SliceSize:w,AsyncLimit:x,ServerSideEncryption:k,UploadData:t,onProgress:h},function(e,t){if(v._isRunningTask(g))return e?(h(null,!0),m.emit("error",e)):void m.emit("upload_slice_complete",t)})}),m.on("get_file_size_finish",function(){if(h=a.throttleOnProgress.call(v,n,e.onProgress),e.UploadData.UploadId)m.emit("get_upload_data_finish",e.UploadData);else{var t=a.extend({TaskId:g,Bucket:y,Region:b,Key:C,Headers:e.Headers,StorageClass:S,Body:_,FileSize:n,SliceSize:w,onHashProgress:T},e);(function(e,t){var n=e.TaskId,r=e.Bucket,c=e.Region,u=e.Key,h=e.StorageClass,v=this,m={},g=e.FileSize,y=e.SliceSize,b=Math.ceil(g/y),C=0,_=a.throttleOnProgress.call(v,g,e.onHashProgress),w=function(t,n){var r=t.length;if(0===r)return n(null,!0);if(r>b)return n(null,!1);if(r>1){var o=Math.max(t[0].Size,t[1].Size);if(o!==y)return n(null,!1)}var i=function(o){if(o<r){var s=t[o];!function(t,n){var r=y*(t-1),o=Math.min(r+y,g),i=o-r;m[t]?n(null,{PartNumber:t,ETag:m[t],Size:i}):a.fileSlice(e.Body,r,o,!1,function(e){a.getFileMd5(e,function(e,r){if(e)return n(e);var o='"'+r+'"';m[t]=o,C+=i,n(e,{PartNumber:t,ETag:o,Size:i}),_({loaded:C,total:g})})})}(s.PartNumber,function(e,t){t&&t.ETag===s.ETag&&t.Size===s.Size?i(o+1):n(null,!1)})}else n(null,!0)};i(0)},x=new i;x.on("error",function(e){if(v._isRunningTask(n))return t(e)}),x.on("upload_id_ready",function(e){var n={},r=[];a.each(e.PartList,function(e){n[e.PartNumber]=e});for(var o=1;o<=b;o++){var i=n[o];i?(i.PartNumber=o,i.Uploaded=!0):i={PartNumber:o,ETag:null,Uploaded:!1},r.push(i)}e.PartList=r,t(null,e)}),x.on("no_available_upload_id",function(){if(v._isRunningTask(n)){var o=a.extend({Bucket:r,Region:c,Key:u,Headers:a.clone(e.Headers),StorageClass:h,Body:e.Body},e),i=e.Headers["Content-Type"]||e.Body&&e.Body.type;i&&(o.Headers["Content-Type"]=i),v.multipartInit(o,function(e,r){if(v._isRunningTask(n)){if(e)return x.emit("error",e);var o=r.UploadId;if(!o)return t({Message:"no upload id"});x.emit("upload_id_ready",{UploadId:o,PartList:[]})}})}}),x.on("has_upload_id",function(e){e=e.reverse(),o.eachLimit(e,1,function(e,t){v._isRunningTask(n)&&(s[e]?t():p.call(v,{Bucket:r,Region:c,Key:u,UploadId:e},function(r,o){if(v._isRunningTask(n)){if(r)return l.call(v,e),x.emit("error",r);var i=o.PartList;i.forEach(function(e){e.PartNumber*=1,e.Size*=1,e.ETag=e.ETag||""}),w(i,function(r,o){if(v._isRunningTask(n))return r?x.emit("error",r):void(o?t({UploadId:e,PartList:i}):t())})}}))},function(e){v._isRunningTask(n)&&(_(null,!0),e&&e.UploadId?x.emit("upload_id_ready",e):x.emit("no_available_upload_id"))})}),x.on("seek_local_avail_upload_id",function(t){var o,i=a.getFileUUID(e.Body,e.ChunkSize);if(i&&(o=d.call(v,i))){var f=function(e){if(e>=o.length)x.emit("has_upload_id",t);else{var i=o[e];if(!a.isInArray(t,i))return l.call(v,i),void f(e+1);s[i]?f(e+1):p.call(v,{Bucket:r,Region:c,Key:u,UploadId:i},function(t,r){v._isRunningTask(n)&&(t?(l.call(v,i),f(e+1)):x.emit("upload_id_ready",{UploadId:i,PartList:r.PartList}))})}};f(0)}else x.emit("has_upload_id",t)}),x.on("get_remote_upload_id_list",function(t){f.call(v,{Bucket:r,Region:c,Key:u},function(t,r){if(v._isRunningTask(n)){if(t)return x.emit("error",t);var o=a.filter(r.UploadList,function(e){return e.Key===u&&(!h||e.StorageClass.toUpperCase()===h.toUpperCase())}).reverse().map(function(e){return e.UploadId||e.UploadID});if(o.length)x.emit("seek_local_avail_upload_id",o);else{var i,s=a.getFileUUID(e.Body,e.ChunkSize);s&&(i=d.call(v,s))&&a.each(i,function(e){l.call(v,e)}),x.emit("no_available_upload_id")}}})}),x.emit("get_remote_upload_id_list")}).call(v,t,function(t,n){if(v._isRunningTask(g)){if(t)return m.emit("error",t);e.UploadData.UploadId=n.UploadId,e.UploadData.PartList=n.PartList,m.emit("get_upload_data_finish",e.UploadData)}})}}),n=e.ContentLength,delete e.ContentLength,!e.Headers&&(e.Headers={}),a.each(e.Headers,function(t,n){"content-length"===n.toLowerCase()&&delete e.Headers[n]}),function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],r=1048576,o=0;o<t.length&&!(n/(r=1024*t[o]*1024)<=v.options.MaxPartNumber);o++);e.ChunkSize=e.SliceSize=w=Math.max(w,r)}(),0===n?(e.Body="",e.ContentLength=0,e.SkipTask=!0,v.putObject(e,function(e,n){if(e)return t(e);t(null,n)})):m.emit("get_file_size_finish")},abortUploadTask:function(e,t){var n=e.Bucket,r=e.Region,a=e.Key,s=e.UploadId,c=e.Level||"task",u=e.AsyncLimit,l=this,d=new i;if(d.on("error",function(e){return t(e)}),d.on("get_abort_array",function(i){(function(e,t){var n=e.Bucket,r=e.Region,i=e.Key,a=e.AbortArray,s=e.AsyncLimit||1,c=this,u=0,l=new Array(a.length);o.eachLimit(a,s,function(t,o){var a=u;if(i&&i!=t.Key)return o(null,{KeyNotMatch:!0});var s=t.UploadId||t.UploadID;c.multipartAbort({Bucket:n,Region:r,Key:t.Key,Headers:e.Headers,UploadId:s},function(e,i){var c={Bucket:n,Region:r,Key:t.Key,UploadId:s};l[a]={error:e,task:c},o(null)}),u++},function(e){if(e)return t(e);for(var n=[],r=[],o=0,i=l.length;o<i;o++){var a=l[o];a.task&&(a.error?r.push(a.task):n.push(a.task))}return t(null,{successList:n,errorList:r})})}).call(l,{Bucket:n,Region:r,Key:a,Headers:e.Headers,AsyncLimit:u,AbortArray:i},function(e,n){if(e)return t(e);t(null,n)})}),"bucket"===c)f.call(l,{Bucket:n,Region:r},function(e,n){if(e)return t(e);d.emit("get_abort_array",n.UploadList||[])});else if("file"===c){if(!a)return t({error:"abort_upload_task_no_key"});f.call(l,{Bucket:n,Region:r,Key:a},function(e,n){if(e)return t(e);d.emit("get_abort_array",n.UploadList||[])})}else{if("task"!==c)return t({error:"abort_unknown_level"});if(!s)return t({error:"abort_upload_task_no_id"});if(!a)return t({error:"abort_upload_task_no_key"});d.emit("get_abort_array",[{Key:a,UploadId:s}])}},uploadFiles:function(e,t){var n=void 0===e.SliceSize?this.options.SliceSize:e.SliceSize,r=0,o=0,i=a.throttleOnProgress.call(this,o,e.onProgress),s=e.files.length,c=e.onFileFinish,u=Array(s),l=function(e,n,r){i(null,!0),c&&c(e,n,r),u[r.Index]={options:r,error:e,data:n},--s<=0&&t&&t(null,{files:u})},d=[];a.each(e.files,function(e,t){!function(){var s=e.Body,c=s.size||s.length||0,u={Index:t,TaskId:""};r+=c,a.each(e,function(e,t){"object"!=typeof e&&"function"!=typeof e&&(u[t]=e)});var f=e.TaskReady;e.TaskReady=function(e){u.TaskId=e,f&&f(e)};var p=0,h=e.onProgress;e.onProgress=function(e){o=o-p+e.loaded,p=e.loaded,h&&h(e),i({loaded:o,total:r})};var v=e.onFileFinish,m=c>=n?"sliceUploadFile":"putObject";d.push({api:m,params:e,callback:function(e,t){v&&v(e,t),l&&l(e,t,u)}})}()}),this._addTasks(d)},sliceCopyFile:function(e,t){var n=new i,r=this,s=e.Bucket,c=e.Region,u=e.Key,l=e.CopySource,d=l.match(/^([^.]+-\d+)\.cos(v6)?\.([^.]+)\.[^\/]+\/(.+)$/);if(d){var f=d[1],p=d[3],h=decodeURIComponent(d[4]),v=void 0===e.SliceSize?r.options.CopySliceSize:e.SliceSize;v=Math.max(0,Math.min(v,5368709120));var m,g,y=e.ChunkSize||this.options.CopyChunkSize,b=this.options.CopyChunkParallelLimit,C=0;n.on("copy_slice_complete",function(e){r.multipartComplete({Bucket:s,Region:c,Key:u,UploadId:e.UploadId,Parts:e.PartList},function(e,n){if(e)return g(null,!0),t(e);g({loaded:m,total:m},!0),t(null,n)})}),n.on("get_copy_data_finish",function(e){o.eachLimit(e.PartList,b,function(t,n){var i=t.PartNumber,a=t.CopySourceRange,d=t.end-t.start,f=0;(function(e,t){var n=e.TaskId,r=e.Bucket,i=e.Region,a=e.Key,s=e.CopySource,c=e.UploadId,u=1*e.PartNumber,l=e.CopySourceRange,d=this.options.ChunkRetryTimes+1,f=this;o.retry(d,function(t){f.uploadPartCopy({TaskId:n,Bucket:r,Region:i,Key:a,CopySource:s,UploadId:c,PartNumber:u,CopySourceRange:l,onProgress:e.onProgress},function(e,n){t(e||null,n)})},function(e,n){return t(e,n)})}).call(r,{Bucket:s,Region:c,Key:u,CopySource:l,UploadId:e.UploadId,PartNumber:i,CopySourceRange:a,onProgress:function(e){C+=e.loaded-f,f=e.loaded,g({loaded:C,total:m})}},function(e,r){if(e)return n(e);g({loaded:C,total:m}),C+=d-f,t.ETag=r.ETag,n(e||null,r)})},function(r){if(r)return g(null,!0),t(r);n.emit("copy_slice_complete",e)})}),n.on("get_file_size_finish",function(o){var i;!function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],n=1048576,o=0;o<t.length&&!(m/(n=1024*t[o]*1024)<=r.options.MaxPartNumber);o++);e.ChunkSize=y=Math.max(y,n);for(var i=Math.ceil(m/y),a=[],s=1;s<=i;s++){var c=(s-1)*y,u=s*y<m?s*y-1:m-1,l={PartNumber:s,start:c,end:u,CopySourceRange:"bytes="+c+"-"+u};a.push(l)}e.PartList=a}(),(i="Replaced"===e.Headers["x-cos-metadata-directive"]?e.Headers:o)["x-cos-storage-class"]=e.Headers["x-cos-storage-class"]||o["x-cos-storage-class"],i=a.clearKey(i),r.multipartInit({Bucket:s,Region:c,Key:u,Headers:i},function(r,o){if(r)return t(r);e.UploadId=o.UploadId,n.emit("get_copy_data_finish",e)})}),r.headObject({Bucket:f,Region:p,Key:h},function(o,i){if(o)o.statusCode&&404===o.statusCode?t({ErrorStatus:h+" Not Exist"}):t(o);else if(void 0!==(m=e.FileSize=i.headers["content-length"])&&m)if(g=a.throttleOnProgress.call(r,m,e.onProgress),m<=v)e.Headers["x-cos-metadata-directive"]||(e.Headers["x-cos-metadata-directive"]="Copy"),r.putObjectCopy(e,function(e,n){if(e)return g(null,!0),t(e);g({loaded:m,total:m},!0),t(e,n)});else{var s=i.headers,c={"Cache-Control":s["cache-control"],"Content-Disposition":s["content-disposition"],"Content-Encoding":s["content-encoding"],"Content-Type":s["content-type"],Expires:s.expires,"x-cos-storage-class":s["x-cos-storage-class"]};a.each(s,function(e,t){0===t.indexOf("x-cos-meta-")&&t.length>"x-cos-meta-".length&&(c[t]=e)}),n.emit("get_file_size_finish",c)}else t({error:'get Content-Length error, please add "Content-Length" to CORS ExposeHeader setting.'})})}else t({error:"CopySource format error"})}};e.exports.init=function(e,t){t.transferToTaskMethod(h,"sliceUploadFile"),a.each(h,function(t,n){e.prototype[n]=a.apiWrapper(n,t)})}},function(e,t){var n={eachLimit:function(e,t,n,r){if(r=r||function(){},!e.length||t<=0)return r();var o=0,i=0,a=0;!function s(){if(o>=e.length)return r();for(;a<t&&i<e.length;)a+=1,n(e[(i+=1)-1],function(t){t?(r(t),r=function(){}):(a-=1,(o+=1)>=e.length?r():s())})}()},retry:function(e,t,n){var r=function(o){t(function(t,i){t&&o<e?r(o+1):n(t,i)})};e<1?n():r(1)}};e.exports=n},function(e,t,n){"use strict";var r,o="object"==typeof Reflect?Reflect:null,i=o&&"function"==typeof o.apply?o.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};r=o&&"function"==typeof o.ownKeys?o.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var a=Number.isNaN||function(e){return e!=e};function s(){s.init.call(this)}e.exports=s,s.EventEmitter=s,s.prototype._events=void 0,s.prototype._eventsCount=0,s.prototype._maxListeners=void 0;var c=10;function u(e){return void 0===e._maxListeners?s.defaultMaxListeners:e._maxListeners}function l(e,t,n,r){var o,i,a,s;if("function"!=typeof n)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof n);if(void 0===(i=e._events)?(i=e._events=Object.create(null),e._eventsCount=0):(void 0!==i.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),i=e._events),a=i[t]),void 0===a)a=i[t]=n,++e._eventsCount;else if("function"==typeof a?a=i[t]=r?[n,a]:[a,n]:r?a.unshift(n):a.push(n),(o=u(e))>0&&a.length>o&&!a.warned){a.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=t,c.count=a.length,s=c,console&&console.warn&&console.warn(s)}return e}function d(e,t,n){var r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},o=function(){for(var e=[],t=0;t<arguments.length;t++)e.push(arguments[t]);this.fired||(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,i(this.listener,this.target,e))}.bind(r);return o.listener=n,r.wrapFn=o,o}function f(e,t,n){var r=e._events;if(void 0===r)return[];var o=r[t];return void 0===o?[]:"function"==typeof o?n?[o.listener||o]:[o]:n?function(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(o):h(o,o.length)}function p(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function h(e,t){for(var n=new Array(t),r=0;r<t;++r)n[r]=e[r];return n}Object.defineProperty(s,"defaultMaxListeners",{enumerable:!0,get:function(){return c},set:function(e){if("number"!=typeof e||e<0||a(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");c=e}}),s.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},s.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||a(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},s.prototype.getMaxListeners=function(){return u(this)},s.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var r="error"===e,o=this._events;if(void 0!==o)r=r&&void 0===o.error;else if(!r)return!1;if(r){var a;if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var s=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw s.context=a,s}var c=o[e];if(void 0===c)return!1;if("function"==typeof c)i(c,this,t);else{var u=c.length,l=h(c,u);for(n=0;n<u;++n)i(l[n],this,t)}return!0},s.prototype.addListener=function(e,t){return l(this,e,t,!1)},s.prototype.on=s.prototype.addListener,s.prototype.prependListener=function(e,t){return l(this,e,t,!0)},s.prototype.once=function(e,t){if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t);return this.on(e,d(this,e,t)),this},s.prototype.prependOnceListener=function(e,t){if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t);return this.prependListener(e,d(this,e,t)),this},s.prototype.removeListener=function(e,t){var n,r,o,i,a;if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t);if(void 0===(r=this._events))return this;if(void 0===(n=r[e]))return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(o=-1,i=n.length-1;i>=0;i--)if(n[i]===t||n[i].listener===t){a=n[i].listener,o=i;break}if(o<0)return this;0===o?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,o),1===n.length&&(r[e]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",e,a||t)}return this},s.prototype.off=s.prototype.removeListener,s.prototype.removeAllListeners=function(e){var t,n,r;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var o,i=Object.keys(n);for(r=0;r<i.length;++r)"removeListener"!==(o=i[r])&&this.removeAllListeners(o);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(r=t.length-1;r>=0;r--)this.removeListener(e,t[r]);return this},s.prototype.listeners=function(e){return f(this,e,!0)},s.prototype.rawListeners=function(e){return f(this,e,!1)},s.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):p.call(e,t)},s.prototype.listenerCount=p,s.prototype.eventNames=function(){return this._eventsCount>0?r(this._events):[]}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={isFile:function(e){return"[object File]"==Object.prototype.toString.call(e)},isFunction:function(e){return"function"==typeof e},isString:function(e){return"string"==typeof e},noop:function(){},delay:function(e){return new Promise(function(t){setTimeout(function(){t()},e)})}}},function(e,t,n){(function(t,n){
/*!
 * Vue.js v2.6.10
 * (c) 2014-2019 Evan You
 * Released under the MIT License.
 */var r;r=function(){"use strict";var e=Object.freeze({});function r(e){return null==e}function o(e){return null!=e}function i(e){return!0===e}function a(e){return"string"==typeof e||"number"==typeof e||"symbol"==typeof e||"boolean"==typeof e}function s(e){return null!==e&&"object"==typeof e}var c=Object.prototype.toString;function u(e){return c.call(e).slice(8,-1)}function l(e){return"[object Object]"===c.call(e)}function d(e){return"[object RegExp]"===c.call(e)}function f(e){var t=parseFloat(String(e));return t>=0&&Math.floor(t)===t&&isFinite(e)}function p(e){return o(e)&&"function"==typeof e.then&&"function"==typeof e.catch}function h(e){return null==e?"":Array.isArray(e)||l(e)&&e.toString===c?JSON.stringify(e,null,2):String(e)}function v(e){var t=parseFloat(e);return isNaN(t)?e:t}function m(e,t){for(var n=Object.create(null),r=e.split(","),o=0;o<r.length;o++)n[r[o]]=!0;return t?function(e){return n[e.toLowerCase()]}:function(e){return n[e]}}var g=m("slot,component",!0),y=m("key,ref,slot,slot-scope,is");function b(e,t){if(e.length){var n=e.indexOf(t);if(n>-1)return e.splice(n,1)}}var C=Object.prototype.hasOwnProperty;function _(e,t){return C.call(e,t)}function w(e){var t=Object.create(null);return function(n){return t[n]||(t[n]=e(n))}}var x=/-(\w)/g,S=w(function(e){return e.replace(x,function(e,t){return t?t.toUpperCase():""})}),k=w(function(e){return e.charAt(0).toUpperCase()+e.slice(1)}),T=/\B([A-Z])/g,A=w(function(e){return e.replace(T,"-$1").toLowerCase()}),E=Function.prototype.bind?function(e,t){return e.bind(t)}:function(e,t){function n(n){var r=arguments.length;return r?r>1?e.apply(t,arguments):e.call(t,n):e.call(t)}return n._length=e.length,n};function R(e,t){t=t||0;for(var n=e.length-t,r=new Array(n);n--;)r[n]=e[n+t];return r}function O(e,t){for(var n in t)e[n]=t[n];return e}function N(e){for(var t={},n=0;n<e.length;n++)e[n]&&O(t,e[n]);return t}function I(e,t,n){}var P=function(e,t,n){return!1},D=function(e){return e};function j(e,t){if(e===t)return!0;var n=s(e),r=s(t);if(!n||!r)return!n&&!r&&String(e)===String(t);try{var o=Array.isArray(e),i=Array.isArray(t);if(o&&i)return e.length===t.length&&e.every(function(e,n){return j(e,t[n])});if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();if(o||i)return!1;var a=Object.keys(e),c=Object.keys(t);return a.length===c.length&&a.every(function(n){return j(e[n],t[n])})}catch(e){return!1}}function B(e,t){for(var n=0;n<e.length;n++)if(j(e[n],t))return n;return-1}function M(e){var t=!1;return function(){t||(t=!0,e.apply(this,arguments))}}var L="data-server-rendered",$=["component","directive","filter"],U=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch"],F={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!0,devtools:!0,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:P,isReservedAttr:P,isUnknownElement:P,getTagNamespace:I,parsePlatformTagName:D,mustUseProp:P,async:!0,_lifecycleHooks:U},H=/a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function K(e){var t=(e+"").charCodeAt(0);return 36===t||95===t}function z(e,t,n,r){Object.defineProperty(e,t,{value:n,enumerable:!!r,writable:!0,configurable:!0})}var q,V=new RegExp("[^"+H.source+".$_\\d]"),X="__proto__"in{},G="undefined"!=typeof window,W="undefined"!=typeof WXEnvironment&&!!WXEnvironment.platform,J=W&&WXEnvironment.platform.toLowerCase(),Y=G&&window.navigator.userAgent.toLowerCase(),Q=Y&&/msie|trident/.test(Y),Z=Y&&Y.indexOf("msie 9.0")>0,ee=Y&&Y.indexOf("edge/")>0,te=(Y&&Y.indexOf("android"),Y&&/iphone|ipad|ipod|ios/.test(Y)||"ios"===J),ne=(Y&&/chrome\/\d+/.test(Y),Y&&/phantomjs/.test(Y),Y&&Y.match(/firefox\/(\d+)/)),re={}.watch,oe=!1;if(G)try{var ie={};Object.defineProperty(ie,"passive",{get:function(){oe=!0}}),window.addEventListener("test-passive",null,ie)}catch(e){}var ae=function(){return void 0===q&&(q=!G&&!W&&void 0!==t&&(t.process&&"server"===t.process.env.VUE_ENV)),q},se=G&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function ce(e){return"function"==typeof e&&/native code/.test(e.toString())}var ue,le="undefined"!=typeof Symbol&&ce(Symbol)&&"undefined"!=typeof Reflect&&ce(Reflect.ownKeys);ue="undefined"!=typeof Set&&ce(Set)?Set:function(){function e(){this.set=Object.create(null)}return e.prototype.has=function(e){return!0===this.set[e]},e.prototype.add=function(e){this.set[e]=!0},e.prototype.clear=function(){this.set=Object.create(null)},e}();var de=I,fe=I,pe=I,he=I,ve="undefined"!=typeof console,me=/(?:^|[-_])(\w)/g;de=function(e,t){var n=t?pe(t):"";F.warnHandler?F.warnHandler.call(null,e,t,n):ve&&!F.silent&&console.error("[Vue warn]: "+e+n)},fe=function(e,t){ve&&!F.silent&&console.warn("[Vue tip]: "+e+(t?pe(t):""))},he=function(e,t){if(e.$root===e)return"<Root>";var n="function"==typeof e&&null!=e.cid?e.options:e._isVue?e.$options||e.constructor.options:e,r=n.name||n._componentTag,o=n.__file;if(!r&&o){var i=o.match(/([^\/\\]+)\.vue$/);r=i&&i[1]}return(r?"<"+r.replace(me,function(e){return e.toUpperCase()}).replace(/[-_]/g,"")+">":"<Anonymous>")+(o&&!1!==t?" at "+o:"")},pe=function(e){if(e._isVue&&e.$parent){for(var t=[],n=0;e;){if(t.length>0){var r=t[t.length-1];if(r.constructor===e.constructor){n++,e=e.$parent;continue}n>0&&(t[t.length-1]=[r,n],n=0)}t.push(e),e=e.$parent}return"\n\nfound in\n\n"+t.map(function(e,t){return""+(0===t?"---\x3e ":function(e,t){for(var n="";t;)t%2==1&&(n+=e),t>1&&(e+=e),t>>=1;return n}(" ",5+2*t))+(Array.isArray(e)?he(e[0])+"... ("+e[1]+" recursive calls)":he(e))}).join("\n")}return"\n\n(found in "+he(e)+")"};var ge=0,ye=function(){this.id=ge++,this.subs=[]};ye.prototype.addSub=function(e){this.subs.push(e)},ye.prototype.removeSub=function(e){b(this.subs,e)},ye.prototype.depend=function(){ye.target&&ye.target.addDep(this)},ye.prototype.notify=function(){var e=this.subs.slice();F.async||e.sort(function(e,t){return e.id-t.id});for(var t=0,n=e.length;t<n;t++)e[t].update()},ye.target=null;var be=[];function Ce(e){be.push(e),ye.target=e}function _e(){be.pop(),ye.target=be[be.length-1]}var we=function(e,t,n,r,o,i,a,s){this.tag=e,this.data=t,this.children=n,this.text=r,this.elm=o,this.ns=void 0,this.context=i,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=t&&t.key,this.componentOptions=a,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=s,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1},xe={child:{configurable:!0}};xe.child.get=function(){return this.componentInstance},Object.defineProperties(we.prototype,xe);var Se=function(e){void 0===e&&(e="");var t=new we;return t.text=e,t.isComment=!0,t};function ke(e){return new we(void 0,void 0,void 0,String(e))}function Te(e){var t=new we(e.tag,e.data,e.children&&e.children.slice(),e.text,e.elm,e.context,e.componentOptions,e.asyncFactory);return t.ns=e.ns,t.isStatic=e.isStatic,t.key=e.key,t.isComment=e.isComment,t.fnContext=e.fnContext,t.fnOptions=e.fnOptions,t.fnScopeId=e.fnScopeId,t.asyncMeta=e.asyncMeta,t.isCloned=!0,t}var Ae=Array.prototype,Ee=Object.create(Ae);["push","pop","shift","unshift","splice","sort","reverse"].forEach(function(e){var t=Ae[e];z(Ee,e,function(){for(var n=[],r=arguments.length;r--;)n[r]=arguments[r];var o,i=t.apply(this,n),a=this.__ob__;switch(e){case"push":case"unshift":o=n;break;case"splice":o=n.slice(2)}return o&&a.observeArray(o),a.dep.notify(),i})});var Re=Object.getOwnPropertyNames(Ee),Oe=!0;function Ne(e){Oe=e}var Ie=function(e){var t;this.value=e,this.dep=new ye,this.vmCount=0,z(e,"__ob__",this),Array.isArray(e)?(X?(t=Ee,e.__proto__=t):function(e,t,n){for(var r=0,o=n.length;r<o;r++){var i=n[r];z(e,i,t[i])}}(e,Ee,Re),this.observeArray(e)):this.walk(e)};function Pe(e,t){var n;if(s(e)&&!(e instanceof we))return _(e,"__ob__")&&e.__ob__ instanceof Ie?n=e.__ob__:Oe&&!ae()&&(Array.isArray(e)||l(e))&&Object.isExtensible(e)&&!e._isVue&&(n=new Ie(e)),t&&n&&n.vmCount++,n}function De(e,t,n,r,o){var i=new ye,a=Object.getOwnPropertyDescriptor(e,t);if(!a||!1!==a.configurable){var s=a&&a.get,c=a&&a.set;s&&!c||2!==arguments.length||(n=e[t]);var u=!o&&Pe(n);Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){var t=s?s.call(e):n;return ye.target&&(i.depend(),u&&(u.dep.depend(),Array.isArray(t)&&function e(t){for(var n=void 0,r=0,o=t.length;r<o;r++)(n=t[r])&&n.__ob__&&n.__ob__.dep.depend(),Array.isArray(n)&&e(n)}(t))),t},set:function(t){var a=s?s.call(e):n;t===a||t!=t&&a!=a||(r&&r(),s&&!c||(c?c.call(e,t):n=t,u=!o&&Pe(t),i.notify()))}})}}function je(e,t,n){if((r(e)||a(e))&&de("Cannot set reactive property on undefined, null, or primitive value: "+e),Array.isArray(e)&&f(t))return e.length=Math.max(e.length,t),e.splice(t,1,n),n;if(t in e&&!(t in Object.prototype))return e[t]=n,n;var o=e.__ob__;return e._isVue||o&&o.vmCount?(de("Avoid adding reactive properties to a Vue instance or its root $data at runtime - declare it upfront in the data option."),n):o?(De(o.value,t,n),o.dep.notify(),n):(e[t]=n,n)}function Be(e,t){if((r(e)||a(e))&&de("Cannot delete reactive property on undefined, null, or primitive value: "+e),Array.isArray(e)&&f(t))e.splice(t,1);else{var n=e.__ob__;e._isVue||n&&n.vmCount?de("Avoid deleting properties on a Vue instance or its root $data - just set it to null."):_(e,t)&&(delete e[t],n&&n.dep.notify())}}Ie.prototype.walk=function(e){for(var t=Object.keys(e),n=0;n<t.length;n++)De(e,t[n])},Ie.prototype.observeArray=function(e){for(var t=0,n=e.length;t<n;t++)Pe(e[t])};var Me=F.optionMergeStrategies;function Le(e,t){if(!t)return e;for(var n,r,o,i=le?Reflect.ownKeys(t):Object.keys(t),a=0;a<i.length;a++)"__ob__"!==(n=i[a])&&(r=e[n],o=t[n],_(e,n)?r!==o&&l(r)&&l(o)&&Le(r,o):je(e,n,o));return e}function $e(e,t,n){return n?function(){var r="function"==typeof t?t.call(n,n):t,o="function"==typeof e?e.call(n,n):e;return r?Le(r,o):o}:t?e?function(){return Le("function"==typeof t?t.call(this,this):t,"function"==typeof e?e.call(this,this):e)}:t:e}function Ue(e,t){var n=t?e?e.concat(t):Array.isArray(t)?t:[t]:e;return n?function(e){for(var t=[],n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}function Fe(e,t,n,r){var o=Object.create(e||null);return t?(ze(r,t,n),O(o,t)):o}Me.el=Me.propsData=function(e,t,n,r){return n||de('option "'+r+'" can only be used during instance creation with the `new` keyword.'),He(e,t)},Me.data=function(e,t,n){return n?$e(e,t,n):t&&"function"!=typeof t?(de('The "data" option should be a function that returns a per-instance value in component definitions.',n),e):$e(e,t)},U.forEach(function(e){Me[e]=Ue}),$.forEach(function(e){Me[e+"s"]=Fe}),Me.watch=function(e,t,n,r){if(e===re&&(e=void 0),t===re&&(t=void 0),!t)return Object.create(e||null);if(ze(r,t,n),!e)return t;var o={};for(var i in O(o,e),t){var a=o[i],s=t[i];a&&!Array.isArray(a)&&(a=[a]),o[i]=a?a.concat(s):Array.isArray(s)?s:[s]}return o},Me.props=Me.methods=Me.inject=Me.computed=function(e,t,n,r){if(t&&ze(r,t,n),!e)return t;var o=Object.create(null);return O(o,e),t&&O(o,t),o},Me.provide=$e;var He=function(e,t){return void 0===t?e:t};function Ke(e){new RegExp("^[a-zA-Z][\\-\\.0-9_"+H.source+"]*$").test(e)||de('Invalid component name: "'+e+'". Component names should conform to valid custom element name in html5 specification.'),(g(e)||F.isReservedTag(e))&&de("Do not use built-in or reserved HTML elements as component id: "+e)}function ze(e,t,n){l(t)||de('Invalid value for option "'+e+'": expected an Object, but got '+u(t)+".",n)}function qe(e,t,n){if(function(e){for(var t in e.components)Ke(t)}(t),"function"==typeof t&&(t=t.options),function(e,t){var n=e.props;if(n){var r,o,i={};if(Array.isArray(n))for(r=n.length;r--;)"string"==typeof(o=n[r])?i[S(o)]={type:null}:de("props must be strings when using array syntax.");else if(l(n))for(var a in n)o=n[a],i[S(a)]=l(o)?o:{type:o};else de('Invalid value for option "props": expected an Array or an Object, but got '+u(n)+".",t);e.props=i}}(t,n),function(e,t){var n=e.inject;if(n){var r=e.inject={};if(Array.isArray(n))for(var o=0;o<n.length;o++)r[n[o]]={from:n[o]};else if(l(n))for(var i in n){var a=n[i];r[i]=l(a)?O({from:i},a):{from:a}}else de('Invalid value for option "inject": expected an Array or an Object, but got '+u(n)+".",t)}}(t,n),function(e){var t=e.directives;if(t)for(var n in t){var r=t[n];"function"==typeof r&&(t[n]={bind:r,update:r})}}(t),!t._base&&(t.extends&&(e=qe(e,t.extends,n)),t.mixins))for(var r=0,o=t.mixins.length;r<o;r++)e=qe(e,t.mixins[r],n);var i,a={};for(i in e)s(i);for(i in t)_(e,i)||s(i);function s(r){var o=Me[r]||He;a[r]=o(e[r],t[r],n,r)}return a}function Ve(e,t,n,r){if("string"==typeof n){var o=e[t];if(_(o,n))return o[n];var i=S(n);if(_(o,i))return o[i];var a=k(i);if(_(o,a))return o[a];var s=o[n]||o[i]||o[a];return r&&!s&&de("Failed to resolve "+t.slice(0,-1)+": "+n,e),s}}function Xe(e,t,n,r){var o=t[e],i=!_(n,e),a=n[e],c=Qe(Boolean,o.type);if(c>-1)if(i&&!_(o,"default"))a=!1;else if(""===a||a===A(e)){var l=Qe(String,o.type);(l<0||c<l)&&(a=!0)}if(void 0===a){a=function(e,t,n){if(!_(t,"default"))return;var r=t.default;s(r)&&de('Invalid default value for prop "'+n+'": Props with type Object/Array must use a factory function to return the default value.',e);if(e&&e.$options.propsData&&void 0===e.$options.propsData[n]&&void 0!==e._props[n])return e._props[n];return"function"==typeof r&&"Function"!==Je(t.type)?r.call(e):r}(r,o,e);var d=Oe;Ne(!0),Pe(a),Ne(d)}return function(e,t,n,r,o){if(e.required&&o)return void de('Missing required prop: "'+t+'"',r);if(null==n&&!e.required)return;var i=e.type,a=!i||!0===i,s=[];if(i){Array.isArray(i)||(i=[i]);for(var c=0;c<i.length&&!a;c++){var l=We(n,i[c]);s.push(l.expectedType||""),a=l.valid}}if(!a)return void de(function(e,t,n){var r='Invalid prop: type check failed for prop "'+e+'". Expected '+n.map(k).join(", "),o=n[0],i=u(t),a=Ze(t,o),s=Ze(t,i);1===n.length&&et(o)&&!function(){var e=[],t=arguments.length;for(;t--;)e[t]=arguments[t];return e.some(function(e){return"boolean"===e.toLowerCase()})}(o,i)&&(r+=" with value "+a);r+=", got "+i+" ",et(i)&&(r+="with value "+s+".");return r}(t,n,s),r);var d=e.validator;d&&(d(n)||de('Invalid prop: custom validator check failed for prop "'+t+'".',r))}(o,e,a,r,i),a}var Ge=/^(String|Number|Boolean|Function|Symbol)$/;function We(e,t){var n,r=Je(t);if(Ge.test(r)){var o=typeof e;(n=o===r.toLowerCase())||"object"!==o||(n=e instanceof t)}else n="Object"===r?l(e):"Array"===r?Array.isArray(e):e instanceof t;return{valid:n,expectedType:r}}function Je(e){var t=e&&e.toString().match(/^\s*function (\w+)/);return t?t[1]:""}function Ye(e,t){return Je(e)===Je(t)}function Qe(e,t){if(!Array.isArray(t))return Ye(t,e)?0:-1;for(var n=0,r=t.length;n<r;n++)if(Ye(t[n],e))return n;return-1}function Ze(e,t){return"String"===t?'"'+e+'"':"Number"===t?""+Number(e):""+e}function et(e){return["string","number","boolean"].some(function(t){return e.toLowerCase()===t})}function tt(e,t,n){Ce();try{if(t)for(var r=t;r=r.$parent;){var o=r.$options.errorCaptured;if(o)for(var i=0;i<o.length;i++)try{if(!1===o[i].call(r,e,t,n))return}catch(e){rt(e,r,"errorCaptured hook")}}rt(e,t,n)}finally{_e()}}function nt(e,t,n,r,o){var i;try{(i=n?e.apply(t,n):e.call(t))&&!i._isVue&&p(i)&&!i._handled&&(i.catch(function(e){return tt(e,r,o+" (Promise/async)")}),i._handled=!0)}catch(e){tt(e,r,o)}return i}function rt(e,t,n){if(F.errorHandler)try{return F.errorHandler.call(null,e,t,n)}catch(t){t!==e&&ot(t,null,"config.errorHandler")}ot(e,t,n)}function ot(e,t,n){if(de("Error in "+n+': "'+e.toString()+'"',t),!G&&!W||"undefined"==typeof console)throw e;console.error(e)}var it,at,st,ct=!1,ut=[],lt=!1;function dt(){lt=!1;var e=ut.slice(0);ut.length=0;for(var t=0;t<e.length;t++)e[t]()}if("undefined"!=typeof Promise&&ce(Promise)){var ft=Promise.resolve();it=function(){ft.then(dt),te&&setTimeout(I)},ct=!0}else if(Q||"undefined"==typeof MutationObserver||!ce(MutationObserver)&&"[object MutationObserverConstructor]"!==MutationObserver.toString())it=void 0!==n&&ce(n)?function(){n(dt)}:function(){setTimeout(dt,0)};else{var pt=1,ht=new MutationObserver(dt),vt=document.createTextNode(String(pt));ht.observe(vt,{characterData:!0}),it=function(){pt=(pt+1)%2,vt.data=String(pt)},ct=!0}function mt(e,t){var n;if(ut.push(function(){if(e)try{e.call(t)}catch(e){tt(e,t,"nextTick")}else n&&n(t)}),lt||(lt=!0,it()),!e&&"undefined"!=typeof Promise)return new Promise(function(e){n=e})}var gt,yt=G&&window.performance;yt&&yt.mark&&yt.measure&&yt.clearMarks&&yt.clearMeasures&&(at=function(e){return yt.mark(e)},st=function(e,t,n){yt.measure(e,t,n),yt.clearMarks(t),yt.clearMarks(n)});var bt=m("Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,require"),Ct=function(e,t){de('Property or method "'+t+'" is not defined on the instance but referenced during render. Make sure that this property is reactive, either in the data option, or for class-based components, by initializing the property. See: https://vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.',e)},_t=function(e,t){de('Property "'+t+'" must be accessed with "$data.'+t+'" because properties starting with "$" or "_" are not proxied in the Vue instance to prevent conflicts with Vue internalsSee: https://vuejs.org/v2/api/#data',e)},wt="undefined"!=typeof Proxy&&ce(Proxy);if(wt){var xt=m("stop,prevent,self,ctrl,shift,alt,meta,exact");F.keyCodes=new Proxy(F.keyCodes,{set:function(e,t,n){return xt(t)?(de("Avoid overwriting built-in modifier in config.keyCodes: ."+t),!1):(e[t]=n,!0)}})}var St={has:function(e,t){var n=t in e,r=bt(t)||"string"==typeof t&&"_"===t.charAt(0)&&!(t in e.$data);return n||r||(t in e.$data?_t(e,t):Ct(e,t)),n||!r}},kt={get:function(e,t){return"string"!=typeof t||t in e||(t in e.$data?_t(e,t):Ct(e,t)),e[t]}};gt=function(e){if(wt){var t=e.$options,n=t.render&&t.render._withStripped?kt:St;e._renderProxy=new Proxy(e,n)}else e._renderProxy=e};var Tt=new ue;function At(e){!function e(t,n){var r,o;var i=Array.isArray(t);if(!i&&!s(t)||Object.isFrozen(t)||t instanceof we)return;if(t.__ob__){var a=t.__ob__.dep.id;if(n.has(a))return;n.add(a)}if(i)for(r=t.length;r--;)e(t[r],n);else for(o=Object.keys(t),r=o.length;r--;)e(t[o[r]],n)}(e,Tt),Tt.clear()}var Et=w(function(e){var t="&"===e.charAt(0),n="~"===(e=t?e.slice(1):e).charAt(0),r="!"===(e=n?e.slice(1):e).charAt(0);return{name:e=r?e.slice(1):e,once:n,capture:r,passive:t}});function Rt(e,t){function n(){var e=arguments,r=n.fns;if(!Array.isArray(r))return nt(r,null,arguments,t,"v-on handler");for(var o=r.slice(),i=0;i<o.length;i++)nt(o[i],null,e,t,"v-on handler")}return n.fns=e,n}function Ot(e,t,n,o,a,s){var c,u,l,d;for(c in e)u=e[c],l=t[c],d=Et(c),r(u)?de('Invalid handler for event "'+d.name+'": got '+String(u),s):r(l)?(r(u.fns)&&(u=e[c]=Rt(u,s)),i(d.once)&&(u=e[c]=a(d.name,u,d.capture)),n(d.name,u,d.capture,d.passive,d.params)):u!==l&&(l.fns=u,e[c]=l);for(c in t)r(e[c])&&o((d=Et(c)).name,t[c],d.capture)}function Nt(e,t,n){var a;e instanceof we&&(e=e.data.hook||(e.data.hook={}));var s=e[t];function c(){n.apply(this,arguments),b(a.fns,c)}r(s)?a=Rt([c]):o(s.fns)&&i(s.merged)?(a=s).fns.push(c):a=Rt([s,c]),a.merged=!0,e[t]=a}function It(e,t,n,r,i){if(o(t)){if(_(t,n))return e[n]=t[n],i||delete t[n],!0;if(_(t,r))return e[n]=t[r],i||delete t[r],!0}return!1}function Pt(e){return a(e)?[ke(e)]:Array.isArray(e)?function e(t,n){var s=[];var c,u,l,d;for(c=0;c<t.length;c++)r(u=t[c])||"boolean"==typeof u||(l=s.length-1,d=s[l],Array.isArray(u)?u.length>0&&(Dt((u=e(u,(n||"")+"_"+c))[0])&&Dt(d)&&(s[l]=ke(d.text+u[0].text),u.shift()),s.push.apply(s,u)):a(u)?Dt(d)?s[l]=ke(d.text+u):""!==u&&s.push(ke(u)):Dt(u)&&Dt(d)?s[l]=ke(d.text+u.text):(i(t._isVList)&&o(u.tag)&&r(u.key)&&o(n)&&(u.key="__vlist"+n+"_"+c+"__"),s.push(u)));return s}(e):void 0}function Dt(e){return o(e)&&o(e.text)&&!1===e.isComment}function jt(e,t){if(e){for(var n=Object.create(null),r=le?Reflect.ownKeys(e):Object.keys(e),o=0;o<r.length;o++){var i=r[o];if("__ob__"!==i){for(var a=e[i].from,s=t;s;){if(s._provided&&_(s._provided,a)){n[i]=s._provided[a];break}s=s.$parent}if(!s)if("default"in e[i]){var c=e[i].default;n[i]="function"==typeof c?c.call(t):c}else de('Injection "'+i+'" not found',t)}}return n}}function Bt(e,t){if(!e||!e.length)return{};for(var n={},r=0,o=e.length;r<o;r++){var i=e[r],a=i.data;if(a&&a.attrs&&a.attrs.slot&&delete a.attrs.slot,i.context!==t&&i.fnContext!==t||!a||null==a.slot)(n.default||(n.default=[])).push(i);else{var s=a.slot,c=n[s]||(n[s]=[]);"template"===i.tag?c.push.apply(c,i.children||[]):c.push(i)}}for(var u in n)n[u].every(Mt)&&delete n[u];return n}function Mt(e){return e.isComment&&!e.asyncFactory||" "===e.text}function Lt(t,n,r){var o,i=Object.keys(n).length>0,a=t?!!t.$stable:!i,s=t&&t.$key;if(t){if(t._normalized)return t._normalized;if(a&&r&&r!==e&&s===r.$key&&!i&&!r.$hasNormal)return r;for(var c in o={},t)t[c]&&"$"!==c[0]&&(o[c]=$t(n,c,t[c]))}else o={};for(var u in n)u in o||(o[u]=Ut(n,u));return t&&Object.isExtensible(t)&&(t._normalized=o),z(o,"$stable",a),z(o,"$key",s),z(o,"$hasNormal",i),o}function $t(e,t,n){var r=function(){var e=arguments.length?n.apply(null,arguments):n({});return(e=e&&"object"==typeof e&&!Array.isArray(e)?[e]:Pt(e))&&(0===e.length||1===e.length&&e[0].isComment)?void 0:e};return n.proxy&&Object.defineProperty(e,t,{get:r,enumerable:!0,configurable:!0}),r}function Ut(e,t){return function(){return e[t]}}function Ft(e,t){var n,r,i,a,c;if(Array.isArray(e)||"string"==typeof e)for(n=new Array(e.length),r=0,i=e.length;r<i;r++)n[r]=t(e[r],r);else if("number"==typeof e)for(n=new Array(e),r=0;r<e;r++)n[r]=t(r+1,r);else if(s(e))if(le&&e[Symbol.iterator]){n=[];for(var u=e[Symbol.iterator](),l=u.next();!l.done;)n.push(t(l.value,n.length)),l=u.next()}else for(a=Object.keys(e),n=new Array(a.length),r=0,i=a.length;r<i;r++)c=a[r],n[r]=t(e[c],c,r);return o(n)||(n=[]),n._isVList=!0,n}function Ht(e,t,n,r){var o,i=this.$scopedSlots[e];i?(n=n||{},r&&(s(r)||de("slot v-bind without argument expects an Object",this),n=O(O({},r),n)),o=i(n)||t):o=this.$slots[e]||t;var a=n&&n.slot;return a?this.$createElement("template",{slot:a},o):o}function Kt(e){return Ve(this.$options,"filters",e,!0)||D}function zt(e,t){return Array.isArray(e)?-1===e.indexOf(t):e!==t}function qt(e,t,n,r,o){var i=F.keyCodes[t]||n;return o&&r&&!F.keyCodes[t]?zt(o,r):i?zt(i,e):r?A(r)!==t:void 0}function Vt(e,t,n,r,o){if(n)if(s(n)){var i;Array.isArray(n)&&(n=N(n));var a=function(a){if("class"===a||"style"===a||y(a))i=e;else{var s=e.attrs&&e.attrs.type;i=r||F.mustUseProp(t,s,a)?e.domProps||(e.domProps={}):e.attrs||(e.attrs={})}var c=S(a),u=A(a);c in i||u in i||(i[a]=n[a],o&&((e.on||(e.on={}))["update:"+a]=function(e){n[a]=e}))};for(var c in n)a(c)}else de("v-bind without argument expects an Object or Array value",this);return e}function Xt(e,t){var n=this._staticTrees||(this._staticTrees=[]),r=n[e];return r&&!t?r:(Wt(r=n[e]=this.$options.staticRenderFns[e].call(this._renderProxy,null,this),"__static__"+e,!1),r)}function Gt(e,t,n){return Wt(e,"__once__"+t+(n?"_"+n:""),!0),e}function Wt(e,t,n){if(Array.isArray(e))for(var r=0;r<e.length;r++)e[r]&&"string"!=typeof e[r]&&Jt(e[r],t+"_"+r,n);else Jt(e,t,n)}function Jt(e,t,n){e.isStatic=!0,e.key=t,e.isOnce=n}function Yt(e,t){if(t)if(l(t)){var n=e.on=e.on?O({},e.on):{};for(var r in t){var o=n[r],i=t[r];n[r]=o?[].concat(o,i):i}}else de("v-on without argument expects an Object value",this);return e}function Qt(e,t,n,r){t=t||{$stable:!n};for(var o=0;o<e.length;o++){var i=e[o];Array.isArray(i)?Qt(i,t,n):i&&(i.proxy&&(i.fn.proxy=!0),t[i.key]=i.fn)}return r&&(t.$key=r),t}function Zt(e,t){for(var n=0;n<t.length;n+=2){var r=t[n];"string"==typeof r&&r?e[t[n]]=t[n+1]:""!==r&&null!==r&&de("Invalid value for dynamic directive argument (expected string or null): "+r,this)}return e}function en(e,t){return"string"==typeof e?t+e:e}function tn(e){e._o=Gt,e._n=v,e._s=h,e._l=Ft,e._t=Ht,e._q=j,e._i=B,e._m=Xt,e._f=Kt,e._k=qt,e._b=Vt,e._v=ke,e._e=Se,e._u=Qt,e._g=Yt,e._d=Zt,e._p=en}function nn(t,n,r,o,a){var s,c=this,u=a.options;_(o,"_uid")?(s=Object.create(o))._original=o:(s=o,o=o._original);var l=i(u._compiled),d=!l;this.data=t,this.props=n,this.children=r,this.parent=o,this.listeners=t.on||e,this.injections=jt(u.inject,o),this.slots=function(){return c.$slots||Lt(t.scopedSlots,c.$slots=Bt(r,o)),c.$slots},Object.defineProperty(this,"scopedSlots",{enumerable:!0,get:function(){return Lt(t.scopedSlots,this.slots())}}),l&&(this.$options=u,this.$slots=this.slots(),this.$scopedSlots=Lt(t.scopedSlots,this.$slots)),u._scopeId?this._c=function(e,t,n,r){var i=fn(s,e,t,n,r,d);return i&&!Array.isArray(i)&&(i.fnScopeId=u._scopeId,i.fnContext=o),i}:this._c=function(e,t,n,r){return fn(s,e,t,n,r,d)}}function rn(e,t,n,r,o){var i=Te(e);return i.fnContext=n,i.fnOptions=r,(i.devtoolsMeta=i.devtoolsMeta||{}).renderContext=o,t.slot&&((i.data||(i.data={})).slot=t.slot),i}function on(e,t){for(var n in t)e[S(n)]=t[n]}tn(nn.prototype);var an={init:function(e,t){if(e.componentInstance&&!e.componentInstance._isDestroyed&&e.data.keepAlive){var n=e;an.prepatch(n,n)}else{(e.componentInstance=function(e,t){var n={_isComponent:!0,_parentVnode:e,parent:t},r=e.data.inlineTemplate;o(r)&&(n.render=r.render,n.staticRenderFns=r.staticRenderFns);return new e.componentOptions.Ctor(n)}(e,wn)).$mount(t?e.elm:void 0,t)}},prepatch:function(t,n){var r=n.componentOptions;!function(t,n,r,o,i){xn=!0;var a=o.data.scopedSlots,s=t.$scopedSlots,c=!!(a&&!a.$stable||s!==e&&!s.$stable||a&&t.$scopedSlots.$key!==a.$key),u=!!(i||t.$options._renderChildren||c);t.$options._parentVnode=o,t.$vnode=o,t._vnode&&(t._vnode.parent=o);if(t.$options._renderChildren=i,t.$attrs=o.data.attrs||e,t.$listeners=r||e,n&&t.$options.props){Ne(!1);for(var l=t._props,d=t.$options._propKeys||[],f=0;f<d.length;f++){var p=d[f],h=t.$options.props;l[p]=Xe(p,h,n,t)}Ne(!0),t.$options.propsData=n}r=r||e;var v=t.$options._parentListeners;t.$options._parentListeners=r,_n(t,r,v),u&&(t.$slots=Bt(i,o.context),t.$forceUpdate());xn=!1}(n.componentInstance=t.componentInstance,r.propsData,r.listeners,n,r.children)},insert:function(e){var t,n=e.context,r=e.componentInstance;r._isMounted||(r._isMounted=!0,An(r,"mounted")),e.data.keepAlive&&(n._isMounted?((t=r)._inactive=!1,On.push(t)):Tn(r,!0))},destroy:function(e){var t=e.componentInstance;t._isDestroyed||(e.data.keepAlive?function e(t,n){if(n&&(t._directInactive=!0,kn(t)))return;if(!t._inactive){t._inactive=!0;for(var r=0;r<t.$children.length;r++)e(t.$children[r]);An(t,"deactivated")}}(t,!0):t.$destroy())}},sn=Object.keys(an);function cn(t,n,a,c,u){if(!r(t)){var l=a.$options._base;if(s(t)&&(t=l.extend(t)),"function"==typeof t){var d;if(r(t.cid)&&void 0===(t=function(e,t){if(i(e.error)&&o(e.errorComp))return e.errorComp;if(o(e.resolved))return e.resolved;var n=hn;n&&o(e.owners)&&-1===e.owners.indexOf(n)&&e.owners.push(n);if(i(e.loading)&&o(e.loadingComp))return e.loadingComp;if(n&&!o(e.owners)){var a=e.owners=[n],c=!0,u=null,l=null;n.$on("hook:destroyed",function(){return b(a,n)});var d=function(e){for(var t=0,n=a.length;t<n;t++)a[t].$forceUpdate();e&&(a.length=0,null!==u&&(clearTimeout(u),u=null),null!==l&&(clearTimeout(l),l=null))},f=M(function(n){e.resolved=vn(n,t),c?a.length=0:d(!0)}),h=M(function(t){de("Failed to resolve async component: "+String(e)+(t?"\nReason: "+t:"")),o(e.errorComp)&&(e.error=!0,d(!0))}),v=e(f,h);return s(v)&&(p(v)?r(e.resolved)&&v.then(f,h):p(v.component)&&(v.component.then(f,h),o(v.error)&&(e.errorComp=vn(v.error,t)),o(v.loading)&&(e.loadingComp=vn(v.loading,t),0===v.delay?e.loading=!0:u=setTimeout(function(){u=null,r(e.resolved)&&r(e.error)&&(e.loading=!0,d(!1))},v.delay||200)),o(v.timeout)&&(l=setTimeout(function(){l=null,r(e.resolved)&&h("timeout ("+v.timeout+"ms)")},v.timeout)))),c=!1,e.loading?e.loadingComp:e.resolved}}(d=t,l)))return function(e,t,n,r,o){var i=Se();return i.asyncFactory=e,i.asyncMeta={data:t,context:n,children:r,tag:o},i}(d,n,a,c,u);n=n||{},Yn(t),o(n.model)&&function(e,t){var n=e.model&&e.model.prop||"value",r=e.model&&e.model.event||"input";(t.attrs||(t.attrs={}))[n]=t.model.value;var i=t.on||(t.on={}),a=i[r],s=t.model.callback;o(a)?(Array.isArray(a)?-1===a.indexOf(s):a!==s)&&(i[r]=[s].concat(a)):i[r]=s}(t.options,n);var f=function(e,t,n){var i=t.options.props;if(!r(i)){var a={},s=e.attrs,c=e.props;if(o(s)||o(c))for(var u in i){var l=A(u),d=u.toLowerCase();u!==d&&s&&_(s,d)&&fe('Prop "'+d+'" is passed to component '+he(n||t)+', but the declared prop name is "'+u+'". Note that HTML attributes are case-insensitive and camelCased props need to use their kebab-case equivalents when using in-DOM templates. You should probably use "'+l+'" instead of "'+u+'".'),It(a,c,u,l,!0)||It(a,s,u,l,!1)}return a}}(n,t,u);if(i(t.options.functional))return function(t,n,r,i,a){var s=t.options,c={},u=s.props;if(o(u))for(var l in u)c[l]=Xe(l,u,n||e);else o(r.attrs)&&on(c,r.attrs),o(r.props)&&on(c,r.props);var d=new nn(r,c,a,i,t),f=s.render.call(null,d._c,d);if(f instanceof we)return rn(f,r,d.parent,s,d);if(Array.isArray(f)){for(var p=Pt(f)||[],h=new Array(p.length),v=0;v<p.length;v++)h[v]=rn(p[v],r,d.parent,s,d);return h}}(t,f,n,a,c);var h=n.on;if(n.on=n.nativeOn,i(t.options.abstract)){var v=n.slot;n={},v&&(n.slot=v)}!function(e){for(var t=e.hook||(e.hook={}),n=0;n<sn.length;n++){var r=sn[n],o=t[r],i=an[r];o===i||o&&o._merged||(t[r]=o?un(i,o):i)}}(n);var m=t.options.name||u;return new we("vue-component-"+t.cid+(m?"-"+m:""),n,void 0,void 0,void 0,a,{Ctor:t,propsData:f,listeners:h,tag:u,children:c},d)}de("Invalid Component definition: "+String(t),a)}}function un(e,t){var n=function(n,r){e(n,r),t(n,r)};return n._merged=!0,n}var ln=1,dn=2;function fn(e,t,n,c,u,l){return(Array.isArray(n)||a(n))&&(u=c,c=n,n=void 0),i(l)&&(u=dn),function(e,t,n,c,u){if(o(n)&&o(n.__ob__))return de("Avoid using observed data object as vnode data: "+JSON.stringify(n)+"\nAlways create fresh vnode data objects in each render!",e),Se();o(n)&&o(n.is)&&(t=n.is);if(!t)return Se();o(n)&&o(n.key)&&!a(n.key)&&de("Avoid using non-primitive value as key, use string/number value instead.",e);Array.isArray(c)&&"function"==typeof c[0]&&((n=n||{}).scopedSlots={default:c[0]},c.length=0);u===dn?c=Pt(c):u===ln&&(c=function(e){for(var t=0;t<e.length;t++)if(Array.isArray(e[t]))return Array.prototype.concat.apply([],e);return e}(c));var l,d;if("string"==typeof t){var f;d=e.$vnode&&e.$vnode.ns||F.getTagNamespace(t),l=F.isReservedTag(t)?new we(F.parsePlatformTagName(t),n,c,void 0,void 0,e):n&&n.pre||!o(f=Ve(e.$options,"components",t))?new we(t,n,c,void 0,void 0,e):cn(f,n,e,c,t)}else l=cn(t,n,e,c);return Array.isArray(l)?l:o(l)?(o(d)&&function e(t,n,a){t.ns=n;"foreignObject"===t.tag&&(n=void 0,a=!0);if(o(t.children))for(var s=0,c=t.children.length;s<c;s++){var u=t.children[s];o(u.tag)&&(r(u.ns)||i(a)&&"svg"!==u.tag)&&e(u,n,a)}}(l,d),o(n)&&function(e){s(e.style)&&At(e.style);s(e.class)&&At(e.class)}(n),l):Se()}(e,t,n,c,u)}var pn,hn=null;function vn(e,t){return(e.__esModule||le&&"Module"===e[Symbol.toStringTag])&&(e=e.default),s(e)?t.extend(e):e}function mn(e){return e.isComment&&e.asyncFactory}function gn(e){if(Array.isArray(e))for(var t=0;t<e.length;t++){var n=e[t];if(o(n)&&(o(n.componentOptions)||mn(n)))return n}}function yn(e,t){pn.$on(e,t)}function bn(e,t){pn.$off(e,t)}function Cn(e,t){var n=pn;return function r(){null!==t.apply(null,arguments)&&n.$off(e,r)}}function _n(e,t,n){pn=e,Ot(t,n||{},yn,bn,Cn,e),pn=void 0}var wn=null,xn=!1;function Sn(e){var t=wn;return wn=e,function(){wn=t}}function kn(e){for(;e&&(e=e.$parent);)if(e._inactive)return!0;return!1}function Tn(e,t){if(t){if(e._directInactive=!1,kn(e))return}else if(e._directInactive)return;if(e._inactive||null===e._inactive){e._inactive=!1;for(var n=0;n<e.$children.length;n++)Tn(e.$children[n]);An(e,"activated")}}function An(e,t){Ce();var n=e.$options[t],r=t+" hook";if(n)for(var o=0,i=n.length;o<i;o++)nt(n[o],e,null,e,r);e._hasHookEvent&&e.$emit("hook:"+t),_e()}var En=100,Rn=[],On=[],Nn={},In={},Pn=!1,Dn=!1,jn=0,Bn=0,Mn=Date.now;if(G&&!Q){var Ln=window.performance;Ln&&"function"==typeof Ln.now&&Mn()>document.createEvent("Event").timeStamp&&(Mn=function(){return Ln.now()})}function $n(){var e,t;for(Bn=Mn(),Dn=!0,Rn.sort(function(e,t){return e.id-t.id}),jn=0;jn<Rn.length;jn++)if((e=Rn[jn]).before&&e.before(),t=e.id,Nn[t]=null,e.run(),null!=Nn[t]&&(In[t]=(In[t]||0)+1,In[t]>En)){de("You may have an infinite update loop "+(e.user?'in watcher with expression "'+e.expression+'"':"in a component render function."),e.vm);break}var n=On.slice(),r=Rn.slice();jn=Rn.length=On.length=0,Nn={},In={},Pn=Dn=!1,function(e){for(var t=0;t<e.length;t++)e[t]._inactive=!0,Tn(e[t],!0)}(n),function(e){var t=e.length;for(;t--;){var n=e[t],r=n.vm;r._watcher===n&&r._isMounted&&!r._isDestroyed&&An(r,"updated")}}(r),se&&F.devtools&&se.emit("flush")}var Un=0,Fn=function(e,t,n,r,o){this.vm=e,o&&(e._watcher=this),e._watchers.push(this),r?(this.deep=!!r.deep,this.user=!!r.user,this.lazy=!!r.lazy,this.sync=!!r.sync,this.before=r.before):this.deep=this.user=this.lazy=this.sync=!1,this.cb=n,this.id=++Un,this.active=!0,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new ue,this.newDepIds=new ue,this.expression=t.toString(),"function"==typeof t?this.getter=t:(this.getter=function(e){if(!V.test(e)){var t=e.split(".");return function(e){for(var n=0;n<t.length;n++){if(!e)return;e=e[t[n]]}return e}}}(t),this.getter||(this.getter=I,de('Failed watching path: "'+t+'" Watcher only accepts simple dot-delimited paths. For full control, use a function instead.',e))),this.value=this.lazy?void 0:this.get()};Fn.prototype.get=function(){var e;Ce(this);var t=this.vm;try{e=this.getter.call(t,t)}catch(e){if(!this.user)throw e;tt(e,t,'getter for watcher "'+this.expression+'"')}finally{this.deep&&At(e),_e(),this.cleanupDeps()}return e},Fn.prototype.addDep=function(e){var t=e.id;this.newDepIds.has(t)||(this.newDepIds.add(t),this.newDeps.push(e),this.depIds.has(t)||e.addSub(this))},Fn.prototype.cleanupDeps=function(){for(var e=this.deps.length;e--;){var t=this.deps[e];this.newDepIds.has(t.id)||t.removeSub(this)}var n=this.depIds;this.depIds=this.newDepIds,this.newDepIds=n,this.newDepIds.clear(),n=this.deps,this.deps=this.newDeps,this.newDeps=n,this.newDeps.length=0},Fn.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():function(e){var t=e.id;if(null==Nn[t]){if(Nn[t]=!0,Dn){for(var n=Rn.length-1;n>jn&&Rn[n].id>e.id;)n--;Rn.splice(n+1,0,e)}else Rn.push(e);if(!Pn){if(Pn=!0,!F.async)return void $n();mt($n)}}}(this)},Fn.prototype.run=function(){if(this.active){var e=this.get();if(e!==this.value||s(e)||this.deep){var t=this.value;if(this.value=e,this.user)try{this.cb.call(this.vm,e,t)}catch(e){tt(e,this.vm,'callback for watcher "'+this.expression+'"')}else this.cb.call(this.vm,e,t)}}},Fn.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},Fn.prototype.depend=function(){for(var e=this.deps.length;e--;)this.deps[e].depend()},Fn.prototype.teardown=function(){if(this.active){this.vm._isBeingDestroyed||b(this.vm._watchers,this);for(var e=this.deps.length;e--;)this.deps[e].removeSub(this);this.active=!1}};var Hn={enumerable:!0,configurable:!0,get:I,set:I};function Kn(e,t,n){Hn.get=function(){return this[t][n]},Hn.set=function(e){this[t][n]=e},Object.defineProperty(e,n,Hn)}function zn(e){e._watchers=[];var t=e.$options;t.props&&function(e,t){var n=e.$options.propsData||{},r=e._props={},o=e.$options._propKeys=[],i=!e.$parent;i||Ne(!1);var a=function(a){o.push(a);var s=Xe(a,t,n,e),c=A(a);(y(c)||F.isReservedAttr(c))&&de('"'+c+'" is a reserved attribute and cannot be used as component prop.',e),De(r,a,s,function(){i||xn||de("Avoid mutating a prop directly since the value will be overwritten whenever the parent component re-renders. Instead, use a data or computed property based on the prop's value. Prop being mutated: \""+a+'"',e)}),a in e||Kn(e,"_props",a)};for(var s in t)a(s);Ne(!0)}(e,t.props),t.methods&&function(e,t){var n=e.$options.props;for(var r in t)"function"!=typeof t[r]&&de('Method "'+r+'" has type "'+typeof t[r]+'" in the component definition. Did you reference the function correctly?',e),n&&_(n,r)&&de('Method "'+r+'" has already been defined as a prop.',e),r in e&&K(r)&&de('Method "'+r+'" conflicts with an existing Vue instance method. Avoid defining component methods that start with _ or $.'),e[r]="function"!=typeof t[r]?I:E(t[r],e)}(e,t.methods),t.data?function(e){var t=e.$options.data;l(t=e._data="function"==typeof t?function(e,t){Ce();try{return e.call(t,t)}catch(e){return tt(e,t,"data()"),{}}finally{_e()}}(t,e):t||{})||(t={},de("data functions should return an object:\nhttps://vuejs.org/v2/guide/components.html#data-Must-Be-a-Function",e));var n=Object.keys(t),r=e.$options.props,o=e.$options.methods,i=n.length;for(;i--;){var a=n[i];o&&_(o,a)&&de('Method "'+a+'" has already been defined as a data property.',e),r&&_(r,a)?de('The data property "'+a+'" is already declared as a prop. Use prop default value instead.',e):K(a)||Kn(e,"_data",a)}Pe(t,!0)}(e):Pe(e._data={},!0),t.computed&&function(e,t){var n=e._computedWatchers=Object.create(null),r=ae();for(var o in t){var i=t[o],a="function"==typeof i?i:i.get;null==a&&de('Getter is missing for computed property "'+o+'".',e),r||(n[o]=new Fn(e,a||I,I,qn)),o in e?o in e.$data?de('The computed property "'+o+'" is already defined in data.',e):e.$options.props&&o in e.$options.props&&de('The computed property "'+o+'" is already defined as a prop.',e):Vn(e,o,i)}}(e,t.computed),t.watch&&t.watch!==re&&function(e,t){for(var n in t){var r=t[n];if(Array.isArray(r))for(var o=0;o<r.length;o++)Wn(e,n,r[o]);else Wn(e,n,r)}}(e,t.watch)}var qn={lazy:!0};function Vn(e,t,n){var r=!ae();"function"==typeof n?(Hn.get=r?Xn(t):Gn(n),Hn.set=I):(Hn.get=n.get?r&&!1!==n.cache?Xn(t):Gn(n.get):I,Hn.set=n.set||I),Hn.set===I&&(Hn.set=function(){de('Computed property "'+t+'" was assigned to but it has no setter.',this)}),Object.defineProperty(e,t,Hn)}function Xn(e){return function(){var t=this._computedWatchers&&this._computedWatchers[e];if(t)return t.dirty&&t.evaluate(),ye.target&&t.depend(),t.value}}function Gn(e){return function(){return e.call(this,this)}}function Wn(e,t,n,r){return l(n)&&(r=n,n=n.handler),"string"==typeof n&&(n=e[n]),e.$watch(t,n,r)}var Jn=0;function Yn(e){var t=e.options;if(e.super){var n=Yn(e.super);if(n!==e.superOptions){e.superOptions=n;var r=function(e){var t,n=e.options,r=e.sealedOptions;for(var o in n)n[o]!==r[o]&&(t||(t={}),t[o]=n[o]);return t}(e);r&&O(e.extendOptions,r),(t=e.options=qe(n,e.extendOptions)).name&&(t.components[t.name]=e)}}return t}function Qn(e){this instanceof Qn||de("Vue is a constructor and should be called with the `new` keyword"),this._init(e)}function Zn(e){e.cid=0;var t=1;e.extend=function(e){e=e||{};var n=this,r=n.cid,o=e._Ctor||(e._Ctor={});if(o[r])return o[r];var i=e.name||n.options.name;i&&Ke(i);var a=function(e){this._init(e)};return(a.prototype=Object.create(n.prototype)).constructor=a,a.cid=t++,a.options=qe(n.options,e),a.super=n,a.options.props&&function(e){var t=e.options.props;for(var n in t)Kn(e.prototype,"_props",n)}(a),a.options.computed&&function(e){var t=e.options.computed;for(var n in t)Vn(e.prototype,n,t[n])}(a),a.extend=n.extend,a.mixin=n.mixin,a.use=n.use,$.forEach(function(e){a[e]=n[e]}),i&&(a.options.components[i]=a),a.superOptions=n.options,a.extendOptions=e,a.sealedOptions=O({},a.options),o[r]=a,a}}function er(e){return e&&(e.Ctor.options.name||e.tag)}function tr(e,t){return Array.isArray(e)?e.indexOf(t)>-1:"string"==typeof e?e.split(",").indexOf(t)>-1:!!d(e)&&e.test(t)}function nr(e,t){var n=e.cache,r=e.keys,o=e._vnode;for(var i in n){var a=n[i];if(a){var s=er(a.componentOptions);s&&!t(s)&&rr(n,i,r,o)}}}function rr(e,t,n,r){var o=e[t];!o||r&&o.tag===r.tag||o.componentInstance.$destroy(),e[t]=null,b(n,t)}!function(t){t.prototype._init=function(t){var n,r,o=this;o._uid=Jn++,F.performance&&at&&(n="vue-perf-start:"+o._uid,r="vue-perf-end:"+o._uid,at(n)),o._isVue=!0,t&&t._isComponent?function(e,t){var n=e.$options=Object.create(e.constructor.options),r=t._parentVnode;n.parent=t.parent,n._parentVnode=r;var o=r.componentOptions;n.propsData=o.propsData,n._parentListeners=o.listeners,n._renderChildren=o.children,n._componentTag=o.tag,t.render&&(n.render=t.render,n.staticRenderFns=t.staticRenderFns)}(o,t):o.$options=qe(Yn(o.constructor),t||{},o),gt(o),o._self=o,function(e){var t=e.$options,n=t.parent;if(n&&!t.abstract){for(;n.$options.abstract&&n.$parent;)n=n.$parent;n.$children.push(e)}e.$parent=n,e.$root=n?n.$root:e,e.$children=[],e.$refs={},e._watcher=null,e._inactive=null,e._directInactive=!1,e._isMounted=!1,e._isDestroyed=!1,e._isBeingDestroyed=!1}(o),function(e){e._events=Object.create(null),e._hasHookEvent=!1;var t=e.$options._parentListeners;t&&_n(e,t)}(o),function(t){t._vnode=null,t._staticTrees=null;var n=t.$options,r=t.$vnode=n._parentVnode,o=r&&r.context;t.$slots=Bt(n._renderChildren,o),t.$scopedSlots=e,t._c=function(e,n,r,o){return fn(t,e,n,r,o,!1)},t.$createElement=function(e,n,r,o){return fn(t,e,n,r,o,!0)};var i=r&&r.data;De(t,"$attrs",i&&i.attrs||e,function(){!xn&&de("$attrs is readonly.",t)},!0),De(t,"$listeners",n._parentListeners||e,function(){!xn&&de("$listeners is readonly.",t)},!0)}(o),An(o,"beforeCreate"),function(e){var t=jt(e.$options.inject,e);t&&(Ne(!1),Object.keys(t).forEach(function(n){De(e,n,t[n],function(){de('Avoid mutating an injected value directly since the changes will be overwritten whenever the provided component re-renders. injection being mutated: "'+n+'"',e)})}),Ne(!0))}(o),zn(o),function(e){var t=e.$options.provide;t&&(e._provided="function"==typeof t?t.call(e):t)}(o),An(o,"created"),F.performance&&at&&(o._name=he(o,!1),at(r),st("vue "+o._name+" init",n,r)),o.$options.el&&o.$mount(o.$options.el)}}(Qn),function(e){var t={get:function(){return this._data}},n={get:function(){return this._props}};t.set=function(){de("Avoid replacing instance root $data. Use nested data properties instead.",this)},n.set=function(){de("$props is readonly.",this)},Object.defineProperty(e.prototype,"$data",t),Object.defineProperty(e.prototype,"$props",n),e.prototype.$set=je,e.prototype.$delete=Be,e.prototype.$watch=function(e,t,n){if(l(t))return Wn(this,e,t,n);(n=n||{}).user=!0;var r=new Fn(this,e,t,n);if(n.immediate)try{t.call(this,r.value)}catch(e){tt(e,this,'callback for immediate watcher "'+r.expression+'"')}return function(){r.teardown()}}}(Qn),function(e){var t=/^hook:/;e.prototype.$on=function(e,n){var r=this;if(Array.isArray(e))for(var o=0,i=e.length;o<i;o++)r.$on(e[o],n);else(r._events[e]||(r._events[e]=[])).push(n),t.test(e)&&(r._hasHookEvent=!0);return r},e.prototype.$once=function(e,t){var n=this;function r(){n.$off(e,r),t.apply(n,arguments)}return r.fn=t,n.$on(e,r),n},e.prototype.$off=function(e,t){var n=this;if(!arguments.length)return n._events=Object.create(null),n;if(Array.isArray(e)){for(var r=0,o=e.length;r<o;r++)n.$off(e[r],t);return n}var i,a=n._events[e];if(!a)return n;if(!t)return n._events[e]=null,n;for(var s=a.length;s--;)if((i=a[s])===t||i.fn===t){a.splice(s,1);break}return n},e.prototype.$emit=function(e){var t=this,n=e.toLowerCase();n!==e&&t._events[n]&&fe('Event "'+n+'" is emitted in component '+he(t)+' but the handler is registered for "'+e+'". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "'+A(e)+'" instead of "'+e+'".');var r=t._events[e];if(r){r=r.length>1?R(r):r;for(var o=R(arguments,1),i='event handler for "'+e+'"',a=0,s=r.length;a<s;a++)nt(r[a],t,o,t,i)}return t}}(Qn),function(e){e.prototype._update=function(e,t){var n=this,r=n.$el,o=n._vnode,i=Sn(n);n._vnode=e,n.$el=o?n.__patch__(o,e):n.__patch__(n.$el,e,t,!1),i(),r&&(r.__vue__=null),n.$el&&(n.$el.__vue__=n),n.$vnode&&n.$parent&&n.$vnode===n.$parent._vnode&&(n.$parent.$el=n.$el)},e.prototype.$forceUpdate=function(){this._watcher&&this._watcher.update()},e.prototype.$destroy=function(){var e=this;if(!e._isBeingDestroyed){An(e,"beforeDestroy"),e._isBeingDestroyed=!0;var t=e.$parent;!t||t._isBeingDestroyed||e.$options.abstract||b(t.$children,e),e._watcher&&e._watcher.teardown();for(var n=e._watchers.length;n--;)e._watchers[n].teardown();e._data.__ob__&&e._data.__ob__.vmCount--,e._isDestroyed=!0,e.__patch__(e._vnode,null),An(e,"destroyed"),e.$off(),e.$el&&(e.$el.__vue__=null),e.$vnode&&(e.$vnode.parent=null)}}}(Qn),function(e){tn(e.prototype),e.prototype.$nextTick=function(e){return mt(e,this)},e.prototype._render=function(){var e,t=this,n=t.$options,r=n.render,o=n._parentVnode;o&&(t.$scopedSlots=Lt(o.data.scopedSlots,t.$slots,t.$scopedSlots)),t.$vnode=o;try{hn=t,e=r.call(t._renderProxy,t.$createElement)}catch(n){if(tt(n,t,"render"),t.$options.renderError)try{e=t.$options.renderError.call(t._renderProxy,t.$createElement,n)}catch(n){tt(n,t,"renderError"),e=t._vnode}else e=t._vnode}finally{hn=null}return Array.isArray(e)&&1===e.length&&(e=e[0]),e instanceof we||(Array.isArray(e)&&de("Multiple root nodes returned from render function. Render function should return a single root node.",t),e=Se()),e.parent=o,e}}(Qn);var or=[String,RegExp,Array],ir={KeepAlive:{name:"keep-alive",abstract:!0,props:{include:or,exclude:or,max:[String,Number]},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){for(var e in this.cache)rr(this.cache,e,this.keys)},mounted:function(){var e=this;this.$watch("include",function(t){nr(e,function(e){return tr(t,e)})}),this.$watch("exclude",function(t){nr(e,function(e){return!tr(t,e)})})},render:function(){var e=this.$slots.default,t=gn(e),n=t&&t.componentOptions;if(n){var r=er(n),o=this.include,i=this.exclude;if(o&&(!r||!tr(o,r))||i&&r&&tr(i,r))return t;var a=this.cache,s=this.keys,c=null==t.key?n.Ctor.cid+(n.tag?"::"+n.tag:""):t.key;a[c]?(t.componentInstance=a[c].componentInstance,b(s,c),s.push(c)):(a[c]=t,s.push(c),this.max&&s.length>parseInt(this.max)&&rr(a,s[0],s,this._vnode)),t.data.keepAlive=!0}return t||e&&e[0]}}};!function(e){var t={get:function(){return F},set:function(){de("Do not replace the Vue.config object, set individual fields instead.")}};Object.defineProperty(e,"config",t),e.util={warn:de,extend:O,mergeOptions:qe,defineReactive:De},e.set=je,e.delete=Be,e.nextTick=mt,e.observable=function(e){return Pe(e),e},e.options=Object.create(null),$.forEach(function(t){e.options[t+"s"]=Object.create(null)}),e.options._base=e,O(e.options.components,ir),function(e){e.use=function(e){var t=this._installedPlugins||(this._installedPlugins=[]);if(t.indexOf(e)>-1)return this;var n=R(arguments,1);return n.unshift(this),"function"==typeof e.install?e.install.apply(e,n):"function"==typeof e&&e.apply(null,n),t.push(e),this}}(e),function(e){e.mixin=function(e){return this.options=qe(this.options,e),this}}(e),Zn(e),function(e){$.forEach(function(t){e[t]=function(e,n){return n?("component"===t&&Ke(e),"component"===t&&l(n)&&(n.name=n.name||e,n=this.options._base.extend(n)),"directive"===t&&"function"==typeof n&&(n={bind:n,update:n}),this.options[t+"s"][e]=n,n):this.options[t+"s"][e]}})}(e)}(Qn),Object.defineProperty(Qn.prototype,"$isServer",{get:ae}),Object.defineProperty(Qn.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(Qn,"FunctionalRenderContext",{value:nn}),Qn.version="2.6.10";var ar=m("style,class"),sr=m("input,textarea,option,select,progress"),cr=function(e,t,n){return"value"===n&&sr(e)&&"button"!==t||"selected"===n&&"option"===e||"checked"===n&&"input"===e||"muted"===n&&"video"===e},ur=m("contenteditable,draggable,spellcheck"),lr=m("events,caret,typing,plaintext-only"),dr=function(e,t){return mr(t)||"false"===t?"false":"contenteditable"===e&&lr(t)?t:"true"},fr=m("allowfullscreen,async,autofocus,autoplay,checked,compact,controls,declare,default,defaultchecked,defaultmuted,defaultselected,defer,disabled,enabled,formnovalidate,hidden,indeterminate,inert,ismap,itemscope,loop,multiple,muted,nohref,noresize,noshade,novalidate,nowrap,open,pauseonexit,readonly,required,reversed,scoped,seamless,selected,sortable,translate,truespeed,typemustmatch,visible"),pr="http://www.w3.org/1999/xlink",hr=function(e){return":"===e.charAt(5)&&"xlink"===e.slice(0,5)},vr=function(e){return hr(e)?e.slice(6,e.length):""},mr=function(e){return null==e||!1===e};function gr(e){for(var t=e.data,n=e,r=e;o(r.componentInstance);)(r=r.componentInstance._vnode)&&r.data&&(t=yr(r.data,t));for(;o(n=n.parent);)n&&n.data&&(t=yr(t,n.data));return function(e,t){if(o(e)||o(t))return br(e,Cr(t));return""}(t.staticClass,t.class)}function yr(e,t){return{staticClass:br(e.staticClass,t.staticClass),class:o(e.class)?[e.class,t.class]:t.class}}function br(e,t){return e?t?e+" "+t:e:t||""}function Cr(e){return Array.isArray(e)?function(e){for(var t,n="",r=0,i=e.length;r<i;r++)o(t=Cr(e[r]))&&""!==t&&(n&&(n+=" "),n+=t);return n}(e):s(e)?function(e){var t="";for(var n in e)e[n]&&(t&&(t+=" "),t+=n);return t}(e):"string"==typeof e?e:""}var _r={svg:"http://www.w3.org/2000/svg",math:"http://www.w3.org/1998/Math/MathML"},wr=m("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,menuitem,summary,content,element,shadow,template,blockquote,iframe,tfoot"),xr=m("svg,animate,circle,clippath,cursor,defs,desc,ellipse,filter,font-face,foreignObject,g,glyph,image,line,marker,mask,missing-glyph,path,pattern,polygon,polyline,rect,switch,symbol,text,textpath,tspan,use,view",!0),Sr=function(e){return wr(e)||xr(e)};function kr(e){return xr(e)?"svg":"math"===e?"math":void 0}var Tr=Object.create(null),Ar=m("text,number,password,search,email,tel,url");function Er(e){if("string"==typeof e){var t=document.querySelector(e);return t||(de("Cannot find element: "+e),document.createElement("div"))}return e}var Rr=Object.freeze({createElement:function(e,t){var n=document.createElement(e);return"select"!==e?n:(t.data&&t.data.attrs&&void 0!==t.data.attrs.multiple&&n.setAttribute("multiple","multiple"),n)},createElementNS:function(e,t){return document.createElementNS(_r[e],t)},createTextNode:function(e){return document.createTextNode(e)},createComment:function(e){return document.createComment(e)},insertBefore:function(e,t,n){e.insertBefore(t,n)},removeChild:function(e,t){e.removeChild(t)},appendChild:function(e,t){e.appendChild(t)},parentNode:function(e){return e.parentNode},nextSibling:function(e){return e.nextSibling},tagName:function(e){return e.tagName},setTextContent:function(e,t){e.textContent=t},setStyleScope:function(e,t){e.setAttribute(t,"")}}),Or={create:function(e,t){Nr(t)},update:function(e,t){e.data.ref!==t.data.ref&&(Nr(e,!0),Nr(t))},destroy:function(e){Nr(e,!0)}};function Nr(e,t){var n=e.data.ref;if(o(n)){var r=e.context,i=e.componentInstance||e.elm,a=r.$refs;t?Array.isArray(a[n])?b(a[n],i):a[n]===i&&(a[n]=void 0):e.data.refInFor?Array.isArray(a[n])?a[n].indexOf(i)<0&&a[n].push(i):a[n]=[i]:a[n]=i}}var Ir=new we("",{},[]),Pr=["create","activate","update","remove","destroy"];function Dr(e,t){return e.key===t.key&&(e.tag===t.tag&&e.isComment===t.isComment&&o(e.data)===o(t.data)&&function(e,t){if("input"!==e.tag)return!0;var n,r=o(n=e.data)&&o(n=n.attrs)&&n.type,i=o(n=t.data)&&o(n=n.attrs)&&n.type;return r===i||Ar(r)&&Ar(i)}(e,t)||i(e.isAsyncPlaceholder)&&e.asyncFactory===t.asyncFactory&&r(t.asyncFactory.error))}function jr(e,t,n){var r,i,a={};for(r=t;r<=n;++r)o(i=e[r].key)&&(a[i]=r);return a}var Br={create:Mr,update:Mr,destroy:function(e){Mr(e,Ir)}};function Mr(e,t){(e.data.directives||t.data.directives)&&function(e,t){var n,r,o,i=e===Ir,a=t===Ir,s=$r(e.data.directives,e.context),c=$r(t.data.directives,t.context),u=[],l=[];for(n in c)r=s[n],o=c[n],r?(o.oldValue=r.value,o.oldArg=r.arg,Fr(o,"update",t,e),o.def&&o.def.componentUpdated&&l.push(o)):(Fr(o,"bind",t,e),o.def&&o.def.inserted&&u.push(o));if(u.length){var d=function(){for(var n=0;n<u.length;n++)Fr(u[n],"inserted",t,e)};i?Nt(t,"insert",d):d()}l.length&&Nt(t,"postpatch",function(){for(var n=0;n<l.length;n++)Fr(l[n],"componentUpdated",t,e)});if(!i)for(n in s)c[n]||Fr(s[n],"unbind",e,e,a)}(e,t)}var Lr=Object.create(null);function $r(e,t){var n,r,o=Object.create(null);if(!e)return o;for(n=0;n<e.length;n++)(r=e[n]).modifiers||(r.modifiers=Lr),o[Ur(r)]=r,r.def=Ve(t.$options,"directives",r.name,!0);return o}function Ur(e){return e.rawName||e.name+"."+Object.keys(e.modifiers||{}).join(".")}function Fr(e,t,n,r,o){var i=e.def&&e.def[t];if(i)try{i(n.elm,e,n,r,o)}catch(r){tt(r,n.context,"directive "+e.name+" "+t+" hook")}}var Hr=[Or,Br];function Kr(e,t){var n=t.componentOptions;if(!(o(n)&&!1===n.Ctor.options.inheritAttrs||r(e.data.attrs)&&r(t.data.attrs))){var i,a,s=t.elm,c=e.data.attrs||{},u=t.data.attrs||{};for(i in o(u.__ob__)&&(u=t.data.attrs=O({},u)),u)a=u[i],c[i]!==a&&zr(s,i,a);for(i in(Q||ee)&&u.value!==c.value&&zr(s,"value",u.value),c)r(u[i])&&(hr(i)?s.removeAttributeNS(pr,vr(i)):ur(i)||s.removeAttribute(i))}}function zr(e,t,n){e.tagName.indexOf("-")>-1?qr(e,t,n):fr(t)?mr(n)?e.removeAttribute(t):(n="allowfullscreen"===t&&"EMBED"===e.tagName?"true":t,e.setAttribute(t,n)):ur(t)?e.setAttribute(t,dr(t,n)):hr(t)?mr(n)?e.removeAttributeNS(pr,vr(t)):e.setAttributeNS(pr,t,n):qr(e,t,n)}function qr(e,t,n){if(mr(n))e.removeAttribute(t);else{if(Q&&!Z&&"TEXTAREA"===e.tagName&&"placeholder"===t&&""!==n&&!e.__ieph){var r=function(t){t.stopImmediatePropagation(),e.removeEventListener("input",r)};e.addEventListener("input",r),e.__ieph=!0}e.setAttribute(t,n)}}var Vr={create:Kr,update:Kr};function Xr(e,t){var n=t.elm,i=t.data,a=e.data;if(!(r(i.staticClass)&&r(i.class)&&(r(a)||r(a.staticClass)&&r(a.class)))){var s=gr(t),c=n._transitionClasses;o(c)&&(s=br(s,Cr(c))),s!==n._prevClass&&(n.setAttribute("class",s),n._prevClass=s)}}var Gr,Wr,Jr,Yr,Qr,Zr,eo,to={create:Xr,update:Xr},no=/[\w).+\-_$\]]/;function ro(e){var t,n,r,o,i,a=!1,s=!1,c=!1,u=!1,l=0,d=0,f=0,p=0;for(r=0;r<e.length;r++)if(n=t,t=e.charCodeAt(r),a)39===t&&92!==n&&(a=!1);else if(s)34===t&&92!==n&&(s=!1);else if(c)96===t&&92!==n&&(c=!1);else if(u)47===t&&92!==n&&(u=!1);else if(124!==t||124===e.charCodeAt(r+1)||124===e.charCodeAt(r-1)||l||d||f){switch(t){case 34:s=!0;break;case 39:a=!0;break;case 96:c=!0;break;case 40:f++;break;case 41:f--;break;case 91:d++;break;case 93:d--;break;case 123:l++;break;case 125:l--}if(47===t){for(var h=r-1,v=void 0;h>=0&&" "===(v=e.charAt(h));h--);v&&no.test(v)||(u=!0)}}else void 0===o?(p=r+1,o=e.slice(0,r).trim()):m();function m(){(i||(i=[])).push(e.slice(p,r).trim()),p=r+1}if(void 0===o?o=e.slice(0,r).trim():0!==p&&m(),i)for(r=0;r<i.length;r++)o=oo(o,i[r]);return o}function oo(e,t){var n=t.indexOf("(");if(n<0)return'_f("'+t+'")('+e+")";var r=t.slice(0,n),o=t.slice(n+1);return'_f("'+r+'")('+e+(")"!==o?","+o:o)}function io(e,t){console.error("[Vue compiler]: "+e)}function ao(e,t){return e?e.map(function(e){return e[t]}).filter(function(e){return e}):[]}function so(e,t,n,r,o){(e.props||(e.props=[])).push(yo({name:t,value:n,dynamic:o},r)),e.plain=!1}function co(e,t,n,r,o){(o?e.dynamicAttrs||(e.dynamicAttrs=[]):e.attrs||(e.attrs=[])).push(yo({name:t,value:n,dynamic:o},r)),e.plain=!1}function uo(e,t,n,r){e.attrsMap[t]=n,e.attrsList.push(yo({name:t,value:n},r))}function lo(e,t,n,r,o,i,a,s){(e.directives||(e.directives=[])).push(yo({name:t,rawName:n,value:r,arg:o,isDynamicArg:i,modifiers:a},s)),e.plain=!1}function fo(e,t,n){return n?"_p("+t+',"'+e+'")':e+t}function po(t,n,r,o,i,a,s,c){var u;o=o||e,a&&o.prevent&&o.passive&&a("passive and prevent can't be used together. Passive handler can't prevent default event.",s),o.right?c?n="("+n+")==='click'?'contextmenu':("+n+")":"click"===n&&(n="contextmenu",delete o.right):o.middle&&(c?n="("+n+")==='click'?'mouseup':("+n+")":"click"===n&&(n="mouseup")),o.capture&&(delete o.capture,n=fo("!",n,c)),o.once&&(delete o.once,n=fo("~",n,c)),o.passive&&(delete o.passive,n=fo("&",n,c)),o.native?(delete o.native,u=t.nativeEvents||(t.nativeEvents={})):u=t.events||(t.events={});var l=yo({value:r.trim(),dynamic:c},s);o!==e&&(l.modifiers=o);var d=u[n];Array.isArray(d)?i?d.unshift(l):d.push(l):u[n]=d?i?[l,d]:[d,l]:l,t.plain=!1}function ho(e,t){return e.rawAttrsMap[":"+t]||e.rawAttrsMap["v-bind:"+t]||e.rawAttrsMap[t]}function vo(e,t,n){var r=mo(e,":"+t)||mo(e,"v-bind:"+t);if(null!=r)return ro(r);if(!1!==n){var o=mo(e,t);if(null!=o)return JSON.stringify(o)}}function mo(e,t,n){var r;if(null!=(r=e.attrsMap[t]))for(var o=e.attrsList,i=0,a=o.length;i<a;i++)if(o[i].name===t){o.splice(i,1);break}return n&&delete e.attrsMap[t],r}function go(e,t){for(var n=e.attrsList,r=0,o=n.length;r<o;r++){var i=n[r];if(t.test(i.name))return n.splice(r,1),i}}function yo(e,t){return t&&(null!=t.start&&(e.start=t.start),null!=t.end&&(e.end=t.end)),e}function bo(e,t,n){var r=n||{},o=r.number,i="$$v";r.trim&&(i="(typeof $$v === 'string'? $$v.trim(): $$v)"),o&&(i="_n("+i+")");var a=Co(t,i);e.model={value:"("+t+")",expression:JSON.stringify(t),callback:"function ($$v) {"+a+"}"}}function Co(e,t){var n=function(e){if(e=e.trim(),Gr=e.length,e.indexOf("[")<0||e.lastIndexOf("]")<Gr-1)return(Yr=e.lastIndexOf("."))>-1?{exp:e.slice(0,Yr),key:'"'+e.slice(Yr+1)+'"'}:{exp:e,key:null};Wr=e,Yr=Qr=Zr=0;for(;!wo();)xo(Jr=_o())?ko(Jr):91===Jr&&So(Jr);return{exp:e.slice(0,Qr),key:e.slice(Qr+1,Zr)}}(e);return null===n.key?e+"="+t:"$set("+n.exp+", "+n.key+", "+t+")"}function _o(){return Wr.charCodeAt(++Yr)}function wo(){return Yr>=Gr}function xo(e){return 34===e||39===e}function So(e){var t=1;for(Qr=Yr;!wo();)if(xo(e=_o()))ko(e);else if(91===e&&t++,93===e&&t--,0===t){Zr=Yr;break}}function ko(e){for(var t=e;!wo()&&(e=_o())!==t;);}var To,Ao="__r",Eo="__c";function Ro(e,t,n){var r=To;return function o(){null!==t.apply(null,arguments)&&Io(e,o,n,r)}}var Oo=ct&&!(ne&&Number(ne[1])<=53);function No(e,t,n,r){if(Oo){var o=Bn,i=t;t=i._wrapper=function(e){if(e.target===e.currentTarget||e.timeStamp>=o||e.timeStamp<=0||e.target.ownerDocument!==document)return i.apply(this,arguments)}}To.addEventListener(e,t,oe?{capture:n,passive:r}:n)}function Io(e,t,n,r){(r||To).removeEventListener(e,t._wrapper||t,n)}function Po(e,t){if(!r(e.data.on)||!r(t.data.on)){var n=t.data.on||{},i=e.data.on||{};To=t.elm,function(e){if(o(e[Ao])){var t=Q?"change":"input";e[t]=[].concat(e[Ao],e[t]||[]),delete e[Ao]}o(e[Eo])&&(e.change=[].concat(e[Eo],e.change||[]),delete e[Eo])}(n),Ot(n,i,No,Io,Ro,t.context),To=void 0}}var Do,jo={create:Po,update:Po};function Bo(e,t){if(!r(e.data.domProps)||!r(t.data.domProps)){var n,i,a=t.elm,s=e.data.domProps||{},c=t.data.domProps||{};for(n in o(c.__ob__)&&(c=t.data.domProps=O({},c)),s)n in c||(a[n]="");for(n in c){if(i=c[n],"textContent"===n||"innerHTML"===n){if(t.children&&(t.children.length=0),i===s[n])continue;1===a.childNodes.length&&a.removeChild(a.childNodes[0])}if("value"===n&&"PROGRESS"!==a.tagName){a._value=i;var u=r(i)?"":String(i);Mo(a,u)&&(a.value=u)}else if("innerHTML"===n&&xr(a.tagName)&&r(a.innerHTML)){(Do=Do||document.createElement("div")).innerHTML="<svg>"+i+"</svg>";for(var l=Do.firstChild;a.firstChild;)a.removeChild(a.firstChild);for(;l.firstChild;)a.appendChild(l.firstChild)}else if(i!==s[n])try{a[n]=i}catch(e){}}}}function Mo(e,t){return!e.composing&&("OPTION"===e.tagName||function(e,t){var n=!0;try{n=document.activeElement!==e}catch(e){}return n&&e.value!==t}(e,t)||function(e,t){var n=e.value,r=e._vModifiers;if(o(r)){if(r.number)return v(n)!==v(t);if(r.trim)return n.trim()!==t.trim()}return n!==t}(e,t))}var Lo={create:Bo,update:Bo},$o=w(function(e){var t={},n=/:(.+)/;return e.split(/;(?![^(]*\))/g).forEach(function(e){if(e){var r=e.split(n);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t});function Uo(e){var t=Fo(e.style);return e.staticStyle?O(e.staticStyle,t):t}function Fo(e){return Array.isArray(e)?N(e):"string"==typeof e?$o(e):e}var Ho,Ko=/^--/,zo=/\s*!important$/,qo=function(e,t,n){if(Ko.test(t))e.style.setProperty(t,n);else if(zo.test(n))e.style.setProperty(A(t),n.replace(zo,""),"important");else{var r=Xo(t);if(Array.isArray(n))for(var o=0,i=n.length;o<i;o++)e.style[r]=n[o];else e.style[r]=n}},Vo=["Webkit","Moz","ms"],Xo=w(function(e){if(Ho=Ho||document.createElement("div").style,"filter"!==(e=S(e))&&e in Ho)return e;for(var t=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<Vo.length;n++){var r=Vo[n]+t;if(r in Ho)return r}});function Go(e,t){var n=t.data,i=e.data;if(!(r(n.staticStyle)&&r(n.style)&&r(i.staticStyle)&&r(i.style))){var a,s,c=t.elm,u=i.staticStyle,l=i.normalizedStyle||i.style||{},d=u||l,f=Fo(t.data.style)||{};t.data.normalizedStyle=o(f.__ob__)?O({},f):f;var p=function(e,t){var n,r={};if(t)for(var o=e;o.componentInstance;)(o=o.componentInstance._vnode)&&o.data&&(n=Uo(o.data))&&O(r,n);(n=Uo(e.data))&&O(r,n);for(var i=e;i=i.parent;)i.data&&(n=Uo(i.data))&&O(r,n);return r}(t,!0);for(s in d)r(p[s])&&qo(c,s,"");for(s in p)(a=p[s])!==d[s]&&qo(c,s,null==a?"":a)}}var Wo={create:Go,update:Go},Jo=/\s+/;function Yo(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(Jo).forEach(function(t){return e.classList.add(t)}):e.classList.add(t);else{var n=" "+(e.getAttribute("class")||"")+" ";n.indexOf(" "+t+" ")<0&&e.setAttribute("class",(n+t).trim())}}function Qo(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(Jo).forEach(function(t){return e.classList.remove(t)}):e.classList.remove(t),e.classList.length||e.removeAttribute("class");else{for(var n=" "+(e.getAttribute("class")||"")+" ",r=" "+t+" ";n.indexOf(r)>=0;)n=n.replace(r," ");(n=n.trim())?e.setAttribute("class",n):e.removeAttribute("class")}}function Zo(e){if(e){if("object"==typeof e){var t={};return!1!==e.css&&O(t,ei(e.name||"v")),O(t,e),t}return"string"==typeof e?ei(e):void 0}}var ei=w(function(e){return{enterClass:e+"-enter",enterToClass:e+"-enter-to",enterActiveClass:e+"-enter-active",leaveClass:e+"-leave",leaveToClass:e+"-leave-to",leaveActiveClass:e+"-leave-active"}}),ti=G&&!Z,ni="transition",ri="animation",oi="transition",ii="transitionend",ai="animation",si="animationend";ti&&(void 0===window.ontransitionend&&void 0!==window.onwebkittransitionend&&(oi="WebkitTransition",ii="webkitTransitionEnd"),void 0===window.onanimationend&&void 0!==window.onwebkitanimationend&&(ai="WebkitAnimation",si="webkitAnimationEnd"));var ci=G?window.requestAnimationFrame?window.requestAnimationFrame.bind(window):setTimeout:function(e){return e()};function ui(e){ci(function(){ci(e)})}function li(e,t){var n=e._transitionClasses||(e._transitionClasses=[]);n.indexOf(t)<0&&(n.push(t),Yo(e,t))}function di(e,t){e._transitionClasses&&b(e._transitionClasses,t),Qo(e,t)}function fi(e,t,n){var r=hi(e,t),o=r.type,i=r.timeout,a=r.propCount;if(!o)return n();var s=o===ni?ii:si,c=0,u=function(){e.removeEventListener(s,l),n()},l=function(t){t.target===e&&++c>=a&&u()};setTimeout(function(){c<a&&u()},i+1),e.addEventListener(s,l)}var pi=/\b(transform|all)(,|$)/;function hi(e,t){var n,r=window.getComputedStyle(e),o=(r[oi+"Delay"]||"").split(", "),i=(r[oi+"Duration"]||"").split(", "),a=vi(o,i),s=(r[ai+"Delay"]||"").split(", "),c=(r[ai+"Duration"]||"").split(", "),u=vi(s,c),l=0,d=0;return t===ni?a>0&&(n=ni,l=a,d=i.length):t===ri?u>0&&(n=ri,l=u,d=c.length):d=(n=(l=Math.max(a,u))>0?a>u?ni:ri:null)?n===ni?i.length:c.length:0,{type:n,timeout:l,propCount:d,hasTransform:n===ni&&pi.test(r[oi+"Property"])}}function vi(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max.apply(null,t.map(function(t,n){return mi(t)+mi(e[n])}))}function mi(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}function gi(e,t){var n=e.elm;o(n._leaveCb)&&(n._leaveCb.cancelled=!0,n._leaveCb());var i=Zo(e.data.transition);if(!r(i)&&!o(n._enterCb)&&1===n.nodeType){for(var a=i.css,c=i.type,u=i.enterClass,l=i.enterToClass,d=i.enterActiveClass,f=i.appearClass,p=i.appearToClass,h=i.appearActiveClass,m=i.beforeEnter,g=i.enter,y=i.afterEnter,b=i.enterCancelled,C=i.beforeAppear,_=i.appear,w=i.afterAppear,x=i.appearCancelled,S=i.duration,k=wn,T=wn.$vnode;T&&T.parent;)k=T.context,T=T.parent;var A=!k._isMounted||!e.isRootInsert;if(!A||_||""===_){var E=A&&f?f:u,R=A&&h?h:d,O=A&&p?p:l,N=A&&C||m,I=A&&"function"==typeof _?_:g,P=A&&w||y,D=A&&x||b,j=v(s(S)?S.enter:S);null!=j&&bi(j,"enter",e);var B=!1!==a&&!Z,L=_i(I),$=n._enterCb=M(function(){B&&(di(n,O),di(n,R)),$.cancelled?(B&&di(n,E),D&&D(n)):P&&P(n),n._enterCb=null});e.data.show||Nt(e,"insert",function(){var t=n.parentNode,r=t&&t._pending&&t._pending[e.key];r&&r.tag===e.tag&&r.elm._leaveCb&&r.elm._leaveCb(),I&&I(n,$)}),N&&N(n),B&&(li(n,E),li(n,R),ui(function(){di(n,E),$.cancelled||(li(n,O),L||(Ci(j)?setTimeout($,j):fi(n,c,$)))})),e.data.show&&(t&&t(),I&&I(n,$)),B||L||$()}}}function yi(e,t){var n=e.elm;o(n._enterCb)&&(n._enterCb.cancelled=!0,n._enterCb());var i=Zo(e.data.transition);if(r(i)||1!==n.nodeType)return t();if(!o(n._leaveCb)){var a=i.css,c=i.type,u=i.leaveClass,l=i.leaveToClass,d=i.leaveActiveClass,f=i.beforeLeave,p=i.leave,h=i.afterLeave,m=i.leaveCancelled,g=i.delayLeave,y=i.duration,b=!1!==a&&!Z,C=_i(p),_=v(s(y)?y.leave:y);o(_)&&bi(_,"leave",e);var w=n._leaveCb=M(function(){n.parentNode&&n.parentNode._pending&&(n.parentNode._pending[e.key]=null),b&&(di(n,l),di(n,d)),w.cancelled?(b&&di(n,u),m&&m(n)):(t(),h&&h(n)),n._leaveCb=null});g?g(x):x()}function x(){w.cancelled||(!e.data.show&&n.parentNode&&((n.parentNode._pending||(n.parentNode._pending={}))[e.key]=e),f&&f(n),b&&(li(n,u),li(n,d),ui(function(){di(n,u),w.cancelled||(li(n,l),C||(Ci(_)?setTimeout(w,_):fi(n,c,w)))})),p&&p(n,w),b||C||w())}}function bi(e,t,n){"number"!=typeof e?de("<transition> explicit "+t+" duration is not a valid number - got "+JSON.stringify(e)+".",n.context):isNaN(e)&&de("<transition> explicit "+t+" duration is NaN - the duration expression might be incorrect.",n.context)}function Ci(e){return"number"==typeof e&&!isNaN(e)}function _i(e){if(r(e))return!1;var t=e.fns;return o(t)?_i(Array.isArray(t)?t[0]:t):(e._length||e.length)>1}function wi(e,t){!0!==t.data.show&&gi(t)}var xi=function(e){var t,n,s={},c=e.modules,u=e.nodeOps;for(t=0;t<Pr.length;++t)for(s[Pr[t]]=[],n=0;n<c.length;++n)o(c[n][Pr[t]])&&s[Pr[t]].push(c[n][Pr[t]]);function l(e){var t=u.parentNode(e);o(t)&&u.removeChild(t,e)}function f(e,t){return!t&&!e.ns&&!(F.ignoredElements.length&&F.ignoredElements.some(function(t){return d(t)?t.test(e.tag):t===e.tag}))&&F.isUnknownElement(e.tag)}var p=0;function h(e,t,n,r,a,c,l){if(o(e.elm)&&o(c)&&(e=c[l]=Te(e)),e.isRootInsert=!a,!function(e,t,n,r){var a=e.data;if(o(a)){var c=o(e.componentInstance)&&a.keepAlive;if(o(a=a.hook)&&o(a=a.init)&&a(e,!1),o(e.componentInstance))return v(e,t),g(n,e.elm,r),i(c)&&function(e,t,n,r){for(var i,a=e;a.componentInstance;)if(a=a.componentInstance._vnode,o(i=a.data)&&o(i=i.transition)){for(i=0;i<s.activate.length;++i)s.activate[i](Ir,a);t.push(a);break}g(n,e.elm,r)}(e,t,n,r),!0}}(e,t,n,r)){var d=e.data,h=e.children,m=e.tag;o(m)?(d&&d.pre&&p++,f(e,p)&&de("Unknown custom element: <"+m+'> - did you register the component correctly? For recursive components, make sure to provide the "name" option.',e.context),e.elm=e.ns?u.createElementNS(e.ns,m):u.createElement(m,e),_(e),y(e,h,t),o(d)&&C(e,t),g(n,e.elm,r),d&&d.pre&&p--):i(e.isComment)?(e.elm=u.createComment(e.text),g(n,e.elm,r)):(e.elm=u.createTextNode(e.text),g(n,e.elm,r))}}function v(e,t){o(e.data.pendingInsert)&&(t.push.apply(t,e.data.pendingInsert),e.data.pendingInsert=null),e.elm=e.componentInstance.$el,b(e)?(C(e,t),_(e)):(Nr(e),t.push(e))}function g(e,t,n){o(e)&&(o(n)?u.parentNode(n)===e&&u.insertBefore(e,t,n):u.appendChild(e,t))}function y(e,t,n){if(Array.isArray(t)){T(t);for(var r=0;r<t.length;++r)h(t[r],n,e.elm,null,!0,t,r)}else a(e.text)&&u.appendChild(e.elm,u.createTextNode(String(e.text)))}function b(e){for(;e.componentInstance;)e=e.componentInstance._vnode;return o(e.tag)}function C(e,n){for(var r=0;r<s.create.length;++r)s.create[r](Ir,e);o(t=e.data.hook)&&(o(t.create)&&t.create(Ir,e),o(t.insert)&&n.push(e))}function _(e){var t;if(o(t=e.fnScopeId))u.setStyleScope(e.elm,t);else for(var n=e;n;)o(t=n.context)&&o(t=t.$options._scopeId)&&u.setStyleScope(e.elm,t),n=n.parent;o(t=wn)&&t!==e.context&&t!==e.fnContext&&o(t=t.$options._scopeId)&&u.setStyleScope(e.elm,t)}function w(e,t,n,r,o,i){for(;r<=o;++r)h(n[r],i,e,t,!1,n,r)}function x(e){var t,n,r=e.data;if(o(r))for(o(t=r.hook)&&o(t=t.destroy)&&t(e),t=0;t<s.destroy.length;++t)s.destroy[t](e);if(o(t=e.children))for(n=0;n<e.children.length;++n)x(e.children[n])}function S(e,t,n,r){for(;n<=r;++n){var i=t[n];o(i)&&(o(i.tag)?(k(i),x(i)):l(i.elm))}}function k(e,t){if(o(t)||o(e.data)){var n,r=s.remove.length+1;for(o(t)?t.listeners+=r:t=function(e,t){function n(){0==--n.listeners&&l(e)}return n.listeners=t,n}(e.elm,r),o(n=e.componentInstance)&&o(n=n._vnode)&&o(n.data)&&k(n,t),n=0;n<s.remove.length;++n)s.remove[n](e,t);o(n=e.data.hook)&&o(n=n.remove)?n(e,t):t()}else l(e.elm)}function T(e){for(var t={},n=0;n<e.length;n++){var r=e[n],i=r.key;o(i)&&(t[i]?de("Duplicate keys detected: '"+i+"'. This may cause an update error.",r.context):t[i]=!0)}}function A(e,t,n,r){for(var i=n;i<r;i++){var a=t[i];if(o(a)&&Dr(e,a))return i}}function E(e,t,n,a,c,l){if(e!==t){o(t.elm)&&o(a)&&(t=a[c]=Te(t));var d=t.elm=e.elm;if(i(e.isAsyncPlaceholder))o(t.asyncFactory.resolved)?I(e.elm,t,n):t.isAsyncPlaceholder=!0;else if(i(t.isStatic)&&i(e.isStatic)&&t.key===e.key&&(i(t.isCloned)||i(t.isOnce)))t.componentInstance=e.componentInstance;else{var f,p=t.data;o(p)&&o(f=p.hook)&&o(f=f.prepatch)&&f(e,t);var v=e.children,m=t.children;if(o(p)&&b(t)){for(f=0;f<s.update.length;++f)s.update[f](e,t);o(f=p.hook)&&o(f=f.update)&&f(e,t)}r(t.text)?o(v)&&o(m)?v!==m&&function(e,t,n,i,a){var s,c,l,d=0,f=0,p=t.length-1,v=t[0],m=t[p],g=n.length-1,y=n[0],b=n[g],C=!a;for(T(n);d<=p&&f<=g;)r(v)?v=t[++d]:r(m)?m=t[--p]:Dr(v,y)?(E(v,y,i,n,f),v=t[++d],y=n[++f]):Dr(m,b)?(E(m,b,i,n,g),m=t[--p],b=n[--g]):Dr(v,b)?(E(v,b,i,n,g),C&&u.insertBefore(e,v.elm,u.nextSibling(m.elm)),v=t[++d],b=n[--g]):Dr(m,y)?(E(m,y,i,n,f),C&&u.insertBefore(e,m.elm,v.elm),m=t[--p],y=n[++f]):(r(s)&&(s=jr(t,d,p)),r(c=o(y.key)?s[y.key]:A(y,t,d,p))?h(y,i,e,v.elm,!1,n,f):Dr(l=t[c],y)?(E(l,y,i,n,f),t[c]=void 0,C&&u.insertBefore(e,l.elm,v.elm)):h(y,i,e,v.elm,!1,n,f),y=n[++f]);d>p?w(e,r(n[g+1])?null:n[g+1].elm,n,f,g,i):f>g&&S(0,t,d,p)}(d,v,m,n,l):o(m)?(T(m),o(e.text)&&u.setTextContent(d,""),w(d,null,m,0,m.length-1,n)):o(v)?S(0,v,0,v.length-1):o(e.text)&&u.setTextContent(d,""):e.text!==t.text&&u.setTextContent(d,t.text),o(p)&&o(f=p.hook)&&o(f=f.postpatch)&&f(e,t)}}}function R(e,t,n){if(i(n)&&o(e.parent))e.parent.data.pendingInsert=t;else for(var r=0;r<t.length;++r)t[r].data.hook.insert(t[r])}var O=!1,N=m("attrs,class,staticClass,staticStyle,key");function I(e,t,n,r){var a,s=t.tag,c=t.data,u=t.children;if(r=r||c&&c.pre,t.elm=e,i(t.isComment)&&o(t.asyncFactory))return t.isAsyncPlaceholder=!0,!0;if(!function(e,t,n){return o(t.tag)?0===t.tag.indexOf("vue-component")||!f(t,n)&&t.tag.toLowerCase()===(e.tagName&&e.tagName.toLowerCase()):e.nodeType===(t.isComment?8:3)}(e,t,r))return!1;if(o(c)&&(o(a=c.hook)&&o(a=a.init)&&a(t,!0),o(a=t.componentInstance)))return v(t,n),!0;if(o(s)){if(o(u))if(e.hasChildNodes())if(o(a=c)&&o(a=a.domProps)&&o(a=a.innerHTML)){if(a!==e.innerHTML)return"undefined"==typeof console||O||(O=!0,console.warn("Parent: ",e),console.warn("server innerHTML: ",a),console.warn("client innerHTML: ",e.innerHTML)),!1}else{for(var l=!0,d=e.firstChild,p=0;p<u.length;p++){if(!d||!I(d,u[p],n,r)){l=!1;break}d=d.nextSibling}if(!l||d)return"undefined"==typeof console||O||(O=!0,console.warn("Parent: ",e),console.warn("Mismatching childNodes vs. VNodes: ",e.childNodes,u)),!1}else y(t,u,n);if(o(c)){var h=!1;for(var m in c)if(!N(m)){h=!0,C(t,n);break}!h&&c.class&&At(c.class)}}else e.data!==t.text&&(e.data=t.text);return!0}return function(e,t,n,a){if(!r(t)){var c,l=!1,d=[];if(r(e))l=!0,h(t,d);else{var f=o(e.nodeType);if(!f&&Dr(e,t))E(e,t,d,null,null,a);else{if(f){if(1===e.nodeType&&e.hasAttribute(L)&&(e.removeAttribute(L),n=!0),i(n)){if(I(e,t,d))return R(t,d,!0),e;de("The client-side rendered virtual DOM tree is not matching server-rendered content. This is likely caused by incorrect HTML markup, for example nesting block-level elements inside <p>, or missing <tbody>. Bailing hydration and performing full client-side render.")}c=e,e=new we(u.tagName(c).toLowerCase(),{},[],void 0,c)}var p=e.elm,v=u.parentNode(p);if(h(t,d,p._leaveCb?null:v,u.nextSibling(p)),o(t.parent))for(var m=t.parent,g=b(t);m;){for(var y=0;y<s.destroy.length;++y)s.destroy[y](m);if(m.elm=t.elm,g){for(var C=0;C<s.create.length;++C)s.create[C](Ir,m);var _=m.data.hook.insert;if(_.merged)for(var w=1;w<_.fns.length;w++)_.fns[w]()}else Nr(m);m=m.parent}o(v)?S(0,[e],0,0):o(e.tag)&&x(e)}}return R(t,d,l),t.elm}o(e)&&x(e)}}({nodeOps:Rr,modules:[Vr,to,jo,Lo,Wo,G?{create:wi,activate:wi,remove:function(e,t){!0!==e.data.show?yi(e,t):t()}}:{}].concat(Hr)});Z&&document.addEventListener("selectionchange",function(){var e=document.activeElement;e&&e.vmodel&&Ni(e,"input")});var Si={inserted:function(e,t,n,r){"select"===n.tag?(r.elm&&!r.elm._vOptions?Nt(n,"postpatch",function(){Si.componentUpdated(e,t,n)}):ki(e,t,n.context),e._vOptions=[].map.call(e.options,Ei)):("textarea"===n.tag||Ar(e.type))&&(e._vModifiers=t.modifiers,t.modifiers.lazy||(e.addEventListener("compositionstart",Ri),e.addEventListener("compositionend",Oi),e.addEventListener("change",Oi),Z&&(e.vmodel=!0)))},componentUpdated:function(e,t,n){if("select"===n.tag){ki(e,t,n.context);var r=e._vOptions,o=e._vOptions=[].map.call(e.options,Ei);if(o.some(function(e,t){return!j(e,r[t])}))(e.multiple?t.value.some(function(e){return Ai(e,o)}):t.value!==t.oldValue&&Ai(t.value,o))&&Ni(e,"change")}}};function ki(e,t,n){Ti(e,t,n),(Q||ee)&&setTimeout(function(){Ti(e,t,n)},0)}function Ti(e,t,n){var r=t.value,o=e.multiple;if(!o||Array.isArray(r)){for(var i,a,s=0,c=e.options.length;s<c;s++)if(a=e.options[s],o)i=B(r,Ei(a))>-1,a.selected!==i&&(a.selected=i);else if(j(Ei(a),r))return void(e.selectedIndex!==s&&(e.selectedIndex=s));o||(e.selectedIndex=-1)}else de('<select multiple v-model="'+t.expression+'"> expects an Array value for its binding, but got '+Object.prototype.toString.call(r).slice(8,-1),n)}function Ai(e,t){return t.every(function(t){return!j(t,e)})}function Ei(e){return"_value"in e?e._value:e.value}function Ri(e){e.target.composing=!0}function Oi(e){e.target.composing&&(e.target.composing=!1,Ni(e.target,"input"))}function Ni(e,t){var n=document.createEvent("HTMLEvents");n.initEvent(t,!0,!0),e.dispatchEvent(n)}function Ii(e){return!e.componentInstance||e.data&&e.data.transition?e:Ii(e.componentInstance._vnode)}var Pi={model:Si,show:{bind:function(e,t,n){var r=t.value,o=(n=Ii(n)).data&&n.data.transition,i=e.__vOriginalDisplay="none"===e.style.display?"":e.style.display;r&&o?(n.data.show=!0,gi(n,function(){e.style.display=i})):e.style.display=r?i:"none"},update:function(e,t,n){var r=t.value;!r!=!t.oldValue&&((n=Ii(n)).data&&n.data.transition?(n.data.show=!0,r?gi(n,function(){e.style.display=e.__vOriginalDisplay}):yi(n,function(){e.style.display="none"})):e.style.display=r?e.__vOriginalDisplay:"none")},unbind:function(e,t,n,r,o){o||(e.style.display=e.__vOriginalDisplay)}}},Di={name:String,appear:Boolean,css:Boolean,mode:String,type:String,enterClass:String,leaveClass:String,enterToClass:String,leaveToClass:String,enterActiveClass:String,leaveActiveClass:String,appearClass:String,appearActiveClass:String,appearToClass:String,duration:[Number,String,Object]};function ji(e){var t=e&&e.componentOptions;return t&&t.Ctor.options.abstract?ji(gn(t.children)):e}function Bi(e){var t={},n=e.$options;for(var r in n.propsData)t[r]=e[r];var o=n._parentListeners;for(var i in o)t[S(i)]=o[i];return t}function Mi(e,t){if(/\d-keep-alive$/.test(t.tag))return e("keep-alive",{props:t.componentOptions.propsData})}var Li=function(e){return e.tag||mn(e)},$i=function(e){return"show"===e.name},Ui={name:"transition",props:Di,abstract:!0,render:function(e){var t=this,n=this.$slots.default;if(n&&(n=n.filter(Li)).length){n.length>1&&de("<transition> can only be used on a single element. Use <transition-group> for lists.",this.$parent);var r=this.mode;r&&"in-out"!==r&&"out-in"!==r&&de("invalid <transition> mode: "+r,this.$parent);var o=n[0];if(function(e){for(;e=e.parent;)if(e.data.transition)return!0}(this.$vnode))return o;var i=ji(o);if(!i)return o;if(this._leaving)return Mi(e,o);var s="__transition-"+this._uid+"-";i.key=null==i.key?i.isComment?s+"comment":s+i.tag:a(i.key)?0===String(i.key).indexOf(s)?i.key:s+i.key:i.key;var c=(i.data||(i.data={})).transition=Bi(this),u=this._vnode,l=ji(u);if(i.data.directives&&i.data.directives.some($i)&&(i.data.show=!0),l&&l.data&&!function(e,t){return t.key===e.key&&t.tag===e.tag}(i,l)&&!mn(l)&&(!l.componentInstance||!l.componentInstance._vnode.isComment)){var d=l.data.transition=O({},c);if("out-in"===r)return this._leaving=!0,Nt(d,"afterLeave",function(){t._leaving=!1,t.$forceUpdate()}),Mi(e,o);if("in-out"===r){if(mn(i))return u;var f,p=function(){f()};Nt(c,"afterEnter",p),Nt(c,"enterCancelled",p),Nt(d,"delayLeave",function(e){f=e})}}return o}}},Fi=O({tag:String,moveClass:String},Di);function Hi(e){e.elm._moveCb&&e.elm._moveCb(),e.elm._enterCb&&e.elm._enterCb()}function Ki(e){e.data.newPos=e.elm.getBoundingClientRect()}function zi(e){var t=e.data.pos,n=e.data.newPos,r=t.left-n.left,o=t.top-n.top;if(r||o){e.data.moved=!0;var i=e.elm.style;i.transform=i.WebkitTransform="translate("+r+"px,"+o+"px)",i.transitionDuration="0s"}}delete Fi.mode;var qi={Transition:Ui,TransitionGroup:{props:Fi,beforeMount:function(){var e=this,t=this._update;this._update=function(n,r){var o=Sn(e);e.__patch__(e._vnode,e.kept,!1,!0),e._vnode=e.kept,o(),t.call(e,n,r)}},render:function(e){for(var t=this.tag||this.$vnode.data.tag||"span",n=Object.create(null),r=this.prevChildren=this.children,o=this.$slots.default||[],i=this.children=[],a=Bi(this),s=0;s<o.length;s++){var c=o[s];if(c.tag)if(null!=c.key&&0!==String(c.key).indexOf("__vlist"))i.push(c),n[c.key]=c,(c.data||(c.data={})).transition=a;else{var u=c.componentOptions,l=u?u.Ctor.options.name||u.tag||"":c.tag;de("<transition-group> children must be keyed: <"+l+">")}}if(r){for(var d=[],f=[],p=0;p<r.length;p++){var h=r[p];h.data.transition=a,h.data.pos=h.elm.getBoundingClientRect(),n[h.key]?d.push(h):f.push(h)}this.kept=e(t,null,d),this.removed=f}return e(t,null,i)},updated:function(){var e=this.prevChildren,t=this.moveClass||(this.name||"v")+"-move";e.length&&this.hasMove(e[0].elm,t)&&(e.forEach(Hi),e.forEach(Ki),e.forEach(zi),this._reflow=document.body.offsetHeight,e.forEach(function(e){if(e.data.moved){var n=e.elm,r=n.style;li(n,t),r.transform=r.WebkitTransform=r.transitionDuration="",n.addEventListener(ii,n._moveCb=function e(r){r&&r.target!==n||r&&!/transform$/.test(r.propertyName)||(n.removeEventListener(ii,e),n._moveCb=null,di(n,t))})}}))},methods:{hasMove:function(e,t){if(!ti)return!1;if(this._hasMove)return this._hasMove;var n=e.cloneNode();e._transitionClasses&&e._transitionClasses.forEach(function(e){Qo(n,e)}),Yo(n,t),n.style.display="none",this.$el.appendChild(n);var r=hi(n);return this.$el.removeChild(n),this._hasMove=r.hasTransform}}}};Qn.config.mustUseProp=cr,Qn.config.isReservedTag=Sr,Qn.config.isReservedAttr=ar,Qn.config.getTagNamespace=kr,Qn.config.isUnknownElement=function(e){if(!G)return!0;if(Sr(e))return!1;if(e=e.toLowerCase(),null!=Tr[e])return Tr[e];var t=document.createElement(e);return e.indexOf("-")>-1?Tr[e]=t.constructor===window.HTMLUnknownElement||t.constructor===window.HTMLElement:Tr[e]=/HTMLUnknownElement/.test(t.toString())},O(Qn.options.directives,Pi),O(Qn.options.components,qi),Qn.prototype.__patch__=G?xi:I,Qn.prototype.$mount=function(e,t){return function(e,t,n){var r;return e.$el=t,e.$options.render||(e.$options.render=Se,e.$options.template&&"#"!==e.$options.template.charAt(0)||e.$options.el||t?de("You are using the runtime-only build of Vue where the template compiler is not available. Either pre-compile the templates into render functions, or use the compiler-included build.",e):de("Failed to mount component: template or render function not defined.",e)),An(e,"beforeMount"),r=F.performance&&at?function(){var t=e._name,r=e._uid,o="vue-perf-start:"+r,i="vue-perf-end:"+r;at(o);var a=e._render();at(i),st("vue "+t+" render",o,i),at(o),e._update(a,n),at(i),st("vue "+t+" patch",o,i)}:function(){e._update(e._render(),n)},new Fn(e,r,I,{before:function(){e._isMounted&&!e._isDestroyed&&An(e,"beforeUpdate")}},!0),n=!1,null==e.$vnode&&(e._isMounted=!0,An(e,"mounted")),e}(this,e=e&&G?Er(e):void 0,t)},G&&setTimeout(function(){F.devtools&&(se?se.emit("init",Qn):console[console.info?"info":"log"]("Download the Vue Devtools extension for a better development experience:\nhttps://github.com/vuejs/vue-devtools")),!1!==F.productionTip&&"undefined"!=typeof console&&console[console.info?"info":"log"]("You are running Vue in development mode.\nMake sure to turn on production mode when deploying for production.\nSee more tips at https://vuejs.org/guide/deployment.html")},0);var Vi=/\{\{((?:.|\r?\n)+?)\}\}/g,Xi=/[-.*+?^${}()|[\]\/\\]/g,Gi=w(function(e){var t=e[0].replace(Xi,"\\$&"),n=e[1].replace(Xi,"\\$&");return new RegExp(t+"((?:.|\\n)+?)"+n,"g")});function Wi(e,t){var n=t?Gi(t):Vi;if(n.test(e)){for(var r,o,i,a=[],s=[],c=n.lastIndex=0;r=n.exec(e);){(o=r.index)>c&&(s.push(i=e.slice(c,o)),a.push(JSON.stringify(i)));var u=ro(r[1].trim());a.push("_s("+u+")"),s.push({"@binding":u}),c=o+r[0].length}return c<e.length&&(s.push(i=e.slice(c)),a.push(JSON.stringify(i))),{expression:a.join("+"),tokens:s}}}var Ji,Yi={staticKeys:["staticClass"],transformNode:function(e,t){var n=t.warn||io,r=mo(e,"class");r&&Wi(r,t.delimiters)&&n('class="'+r+'": Interpolation inside attributes has been removed. Use v-bind or the colon shorthand instead. For example, instead of <div class="{{ val }}">, use <div :class="val">.',e.rawAttrsMap.class),r&&(e.staticClass=JSON.stringify(r));var o=vo(e,"class",!1);o&&(e.classBinding=o)},genData:function(e){var t="";return e.staticClass&&(t+="staticClass:"+e.staticClass+","),e.classBinding&&(t+="class:"+e.classBinding+","),t}},Qi={staticKeys:["staticStyle"],transformNode:function(e,t){var n=t.warn||io,r=mo(e,"style");r&&(Wi(r,t.delimiters)&&n('style="'+r+'": Interpolation inside attributes has been removed. Use v-bind or the colon shorthand instead. For example, instead of <div style="{{ val }}">, use <div :style="val">.',e.rawAttrsMap.style),e.staticStyle=JSON.stringify($o(r)));var o=vo(e,"style",!1);o&&(e.styleBinding=o)},genData:function(e){var t="";return e.staticStyle&&(t+="staticStyle:"+e.staticStyle+","),e.styleBinding&&(t+="style:("+e.styleBinding+"),"),t}},Zi=function(e){return(Ji=Ji||document.createElement("div")).innerHTML=e,Ji.textContent},ea=m("area,base,br,col,embed,frame,hr,img,input,isindex,keygen,link,meta,param,source,track,wbr"),ta=m("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr,source"),na=m("address,article,aside,base,blockquote,body,caption,col,colgroup,dd,details,dialog,div,dl,dt,fieldset,figcaption,figure,footer,form,h1,h2,h3,h4,h5,h6,head,header,hgroup,hr,html,legend,li,menuitem,meta,optgroup,option,param,rp,rt,source,style,summary,tbody,td,tfoot,th,thead,title,tr,track"),ra=/^\s*([^\s"'<>\/=]+)(?:\s*(=)\s*(?:"([^"]*)"+|'([^']*)'+|([^\s"'=<>`]+)))?/,oa=/^\s*((?:v-[\w-]+:|@|:|#)\[[^=]+\][^\s"'<>\/=]*)(?:\s*(=)\s*(?:"([^"]*)"+|'([^']*)'+|([^\s"'=<>`]+)))?/,ia="[a-zA-Z_][\\-\\.0-9_a-zA-Z"+H.source+"]*",aa="((?:"+ia+"\\:)?"+ia+")",sa=new RegExp("^<"+aa),ca=/^\s*(\/?)>/,ua=new RegExp("^<\\/"+aa+"[^>]*>"),la=/^<!DOCTYPE [^>]+>/i,da=/^<!\--/,fa=/^<!\[/,pa=m("script,style,textarea",!0),ha={},va={"&lt;":"<","&gt;":">","&quot;":'"',"&amp;":"&","&#10;":"\n","&#9;":"\t","&#39;":"'"},ma=/&(?:lt|gt|quot|amp|#39);/g,ga=/&(?:lt|gt|quot|amp|#39|#10|#9);/g,ya=m("pre,textarea",!0),ba=function(e,t){return e&&ya(e)&&"\n"===t[0]};function Ca(e,t){var n=t?ga:ma;return e.replace(n,function(e){return va[e]})}var _a,wa,xa,Sa,ka,Ta,Aa,Ea,Ra,Oa=/^@|^v-on:/,Na=/^v-|^@|^:/,Ia=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,Pa=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,Da=/^\(|\)$/g,ja=/^\[.*\]$/,Ba=/:(.*)$/,Ma=/^:|^\.|^v-bind:/,La=/\.[^.\]]+(?=[^\]]*$)/g,$a=/^v-slot(:|$)|^#/,Ua=/[\r\n]/,Fa=/\s+/g,Ha=/[\s"'<>\/=]/,Ka=w(Zi),za="_empty_";function qa(e,t,n){return{type:1,tag:e,attrsList:t,attrsMap:Qa(t),rawAttrsMap:{},parent:n,children:[]}}function Va(e,t){_a=t.warn||io,Ta=t.isPreTag||P,Aa=t.mustUseProp||P,Ea=t.getTagNamespace||P;var n=t.isReservedTag||P;Ra=function(e){return!!e.component||!n(e.tag)},xa=ao(t.modules,"transformNode"),Sa=ao(t.modules,"preTransformNode"),ka=ao(t.modules,"postTransformNode"),wa=t.delimiters;var r,o,i=[],a=!1!==t.preserveWhitespace,s=t.whitespace,c=!1,u=!1,l=!1;function d(e,t){l||(l=!0,_a(e,t))}function f(e){if(p(e),c||e.processed||(e=Xa(e,t)),i.length||e===r||(r.if&&(e.elseif||e.else)?(h(e),Wa(r,{exp:e.elseif,block:e})):d("Component template should contain exactly one root element. If you are using v-if on multiple elements, use v-else-if to chain them instead.",{start:e.start})),o&&!e.forbidden)if(e.elseif||e.else)a=e,(s=function(e){var t=e.length;for(;t--;){if(1===e[t].type)return e[t];" "!==e[t].text&&_a('text "'+e[t].text.trim()+'" between v-if and v-else(-if) will be ignored.',e[t]),e.pop()}}(o.children))&&s.if?Wa(s,{exp:a.elseif,block:a}):_a("v-"+(a.elseif?'else-if="'+a.elseif+'"':"else")+" used on element <"+a.tag+"> without corresponding v-if.",a.rawAttrsMap[a.elseif?"v-else-if":"v-else"]);else{if(e.slotScope){var n=e.slotTarget||'"default"';(o.scopedSlots||(o.scopedSlots={}))[n]=e}o.children.push(e),e.parent=o}var a,s;e.children=e.children.filter(function(e){return!e.slotScope}),p(e),e.pre&&(c=!1),Ta(e.tag)&&(u=!1);for(var l=0;l<ka.length;l++)ka[l](e,t)}function p(e){if(!u)for(var t;(t=e.children[e.children.length-1])&&3===t.type&&" "===t.text;)e.children.pop()}function h(e){"slot"!==e.tag&&"template"!==e.tag||d("Cannot use <"+e.tag+"> as component root element because it may contain multiple nodes.",{start:e.start}),e.attrsMap.hasOwnProperty("v-for")&&d("Cannot use v-for on stateful component root element because it renders multiple elements.",e.rawAttrsMap["v-for"])}return function(e,t){for(var n,r,o=[],i=t.expectHTML,a=t.isUnaryTag||P,s=t.canBeLeftOpenTag||P,c=0;e;){if(n=e,r&&pa(r)){var u=0,l=r.toLowerCase(),d=ha[l]||(ha[l]=new RegExp("([\\s\\S]*?)(</"+l+"[^>]*>)","i")),f=e.replace(d,function(e,n,r){return u=r.length,pa(l)||"noscript"===l||(n=n.replace(/<!\--([\s\S]*?)-->/g,"$1").replace(/<!\[CDATA\[([\s\S]*?)]]>/g,"$1")),ba(l,n)&&(n=n.slice(1)),t.chars&&t.chars(n),""});c+=e.length-f.length,e=f,T(l,c-u,c)}else{var p=e.indexOf("<");if(0===p){if(da.test(e)){var h=e.indexOf("--\x3e");if(h>=0){t.shouldKeepComment&&t.comment(e.substring(4,h),c,c+h+3),x(h+3);continue}}if(fa.test(e)){var v=e.indexOf("]>");if(v>=0){x(v+2);continue}}var m=e.match(la);if(m){x(m[0].length);continue}var g=e.match(ua);if(g){var y=c;x(g[0].length),T(g[1],y,c);continue}var b=S();if(b){k(b),ba(b.tagName,e)&&x(1);continue}}var C=void 0,_=void 0,w=void 0;if(p>=0){for(_=e.slice(p);!(ua.test(_)||sa.test(_)||da.test(_)||fa.test(_)||(w=_.indexOf("<",1))<0);)p+=w,_=e.slice(p);C=e.substring(0,p)}p<0&&(C=e),C&&x(C.length),t.chars&&C&&t.chars(C,c-C.length,c)}if(e===n){t.chars&&t.chars(e),!o.length&&t.warn&&t.warn('Mal-formatted tag at end of template: "'+e+'"',{start:c+e.length});break}}function x(t){c+=t,e=e.substring(t)}function S(){var t=e.match(sa);if(t){var n,r,o={tagName:t[1],attrs:[],start:c};for(x(t[0].length);!(n=e.match(ca))&&(r=e.match(oa)||e.match(ra));)r.start=c,x(r[0].length),r.end=c,o.attrs.push(r);if(n)return o.unarySlash=n[1],x(n[0].length),o.end=c,o}}function k(e){var n=e.tagName,c=e.unarySlash;i&&("p"===r&&na(n)&&T(r),s(n)&&r===n&&T(n));for(var u=a(n)||!!c,l=e.attrs.length,d=new Array(l),f=0;f<l;f++){var p=e.attrs[f],h=p[3]||p[4]||p[5]||"",v="a"===n&&"href"===p[1]?t.shouldDecodeNewlinesForHref:t.shouldDecodeNewlines;d[f]={name:p[1],value:Ca(h,v)},t.outputSourceRange&&(d[f].start=p.start+p[0].match(/^\s*/).length,d[f].end=p.end)}u||(o.push({tag:n,lowerCasedTag:n.toLowerCase(),attrs:d,start:e.start,end:e.end}),r=n),t.start&&t.start(n,d,u,e.start,e.end)}function T(e,n,i){var a,s;if(null==n&&(n=c),null==i&&(i=c),e)for(s=e.toLowerCase(),a=o.length-1;a>=0&&o[a].lowerCasedTag!==s;a--);else a=0;if(a>=0){for(var u=o.length-1;u>=a;u--)(u>a||!e&&t.warn)&&t.warn("tag <"+o[u].tag+"> has no matching end tag.",{start:o[u].start,end:o[u].end}),t.end&&t.end(o[u].tag,n,i);o.length=a,r=a&&o[a-1].tag}else"br"===s?t.start&&t.start(e,[],!0,n,i):"p"===s&&(t.start&&t.start(e,[],!1,n,i),t.end&&t.end(e,n,i))}T()}(e,{warn:_a,expectHTML:t.expectHTML,isUnaryTag:t.isUnaryTag,canBeLeftOpenTag:t.canBeLeftOpenTag,shouldDecodeNewlines:t.shouldDecodeNewlines,shouldDecodeNewlinesForHref:t.shouldDecodeNewlinesForHref,shouldKeepComment:t.comments,outputSourceRange:t.outputSourceRange,start:function(e,n,a,s,l){var d=o&&o.ns||Ea(e);Q&&"svg"===d&&(n=function(e){for(var t=[],n=0;n<e.length;n++){var r=e[n];Za.test(r.name)||(r.name=r.name.replace(es,""),t.push(r))}return t}(n));var p,v=qa(e,n,o);d&&(v.ns=d),t.outputSourceRange&&(v.start=s,v.end=l,v.rawAttrsMap=v.attrsList.reduce(function(e,t){return e[t.name]=t,e},{})),n.forEach(function(e){Ha.test(e.name)&&_a("Invalid dynamic argument expression: attribute names cannot contain spaces, quotes, <, >, / or =.",{start:e.start+e.name.indexOf("["),end:e.start+e.name.length})}),"style"!==(p=v).tag&&("script"!==p.tag||p.attrsMap.type&&"text/javascript"!==p.attrsMap.type)||ae()||(v.forbidden=!0,_a("Templates should only be responsible for mapping the state to the UI. Avoid placing tags with side-effects in your templates, such as <"+e+">, as they will not be parsed.",{start:v.start}));for(var m=0;m<Sa.length;m++)v=Sa[m](v,t)||v;c||(!function(e){null!=mo(e,"v-pre")&&(e.pre=!0)}(v),v.pre&&(c=!0)),Ta(v.tag)&&(u=!0),c?function(e){var t=e.attrsList,n=t.length;if(n)for(var r=e.attrs=new Array(n),o=0;o<n;o++)r[o]={name:t[o].name,value:JSON.stringify(t[o].value)},null!=t[o].start&&(r[o].start=t[o].start,r[o].end=t[o].end);else e.pre||(e.plain=!0)}(v):v.processed||(Ga(v),function(e){var t=mo(e,"v-if");if(t)e.if=t,Wa(e,{exp:t,block:e});else{null!=mo(e,"v-else")&&(e.else=!0);var n=mo(e,"v-else-if");n&&(e.elseif=n)}}(v),function(e){null!=mo(e,"v-once")&&(e.once=!0)}(v)),r||h(r=v),a?f(v):(o=v,i.push(v))},end:function(e,n,r){var a=i[i.length-1];i.length-=1,o=i[i.length-1],t.outputSourceRange&&(a.end=r),f(a)},chars:function(n,r,i){if(o){if(!Q||"textarea"!==o.tag||o.attrsMap.placeholder!==n){var l,f,p,h=o.children;if(n=u||n.trim()?"script"===(l=o).tag||"style"===l.tag?n:Ka(n):h.length?s?"condense"===s&&Ua.test(n)?"":" ":a?" ":"":"")u||"condense"!==s||(n=n.replace(Fa," ")),!c&&" "!==n&&(f=Wi(n,wa))?p={type:2,expression:f.expression,tokens:f.tokens,text:n}:" "===n&&h.length&&" "===h[h.length-1].text||(p={type:3,text:n}),p&&(t.outputSourceRange&&(p.start=r,p.end=i),h.push(p))}}else n===e?d("Component template requires a root element, rather than just text.",{start:r}):(n=n.trim())&&d('text "'+n+'" outside root element will be ignored.',{start:r})},comment:function(e,n,r){if(o){var i={type:3,text:e,isComment:!0};t.outputSourceRange&&(i.start=n,i.end=r),o.children.push(i)}}}),r}function Xa(e,t){var n,r;!function(e){var t=vo(e,"key");if(t){if("template"===e.tag&&_a("<template> cannot be keyed. Place the key on real elements instead.",ho(e,"key")),e.for){var n=e.iterator2||e.iterator1,r=e.parent;n&&n===t&&r&&"transition-group"===r.tag&&_a("Do not use v-for index as key on <transition-group> children, this is the same as not using keys.",ho(e,"key"),!0)}e.key=t}}(e),e.plain=!e.key&&!e.scopedSlots&&!e.attrsList.length,(r=vo(n=e,"ref"))&&(n.ref=r,n.refInFor=function(e){for(var t=e;t;){if(void 0!==t.for)return!0;t=t.parent}return!1}(n)),function(e){var t;"template"===e.tag?((t=mo(e,"scope"))&&_a('the "scope" attribute for scoped slots have been deprecated and replaced by "slot-scope" since 2.5. The new "slot-scope" attribute can also be used on plain elements in addition to <template> to denote scoped slots.',e.rawAttrsMap.scope,!0),e.slotScope=t||mo(e,"slot-scope")):(t=mo(e,"slot-scope"))&&(e.attrsMap["v-for"]&&_a("Ambiguous combined usage of slot-scope and v-for on <"+e.tag+"> (v-for takes higher priority). Use a wrapper <template> for the scoped slot to make it clearer.",e.rawAttrsMap["slot-scope"],!0),e.slotScope=t);var n=vo(e,"slot");n&&(e.slotTarget='""'===n?'"default"':n,e.slotTargetDynamic=!(!e.attrsMap[":slot"]&&!e.attrsMap["v-bind:slot"]),"template"===e.tag||e.slotScope||co(e,"slot",n,ho(e,"slot")));if("template"===e.tag){var r=go(e,$a);if(r){(e.slotTarget||e.slotScope)&&_a("Unexpected mixed usage of different slot syntaxes.",e),e.parent&&!Ra(e.parent)&&_a("<template v-slot> can only appear at the root level inside the receiving the component",e);var o=Ja(r),i=o.name,a=o.dynamic;e.slotTarget=i,e.slotTargetDynamic=a,e.slotScope=r.value||za}}else{var s=go(e,$a);if(s){Ra(e)||_a("v-slot can only be used on components or <template>.",s),(e.slotScope||e.slotTarget)&&_a("Unexpected mixed usage of different slot syntaxes.",e),e.scopedSlots&&_a("To avoid scope ambiguity, the default slot should also use <template> syntax when there are other named slots.",s);var c=e.scopedSlots||(e.scopedSlots={}),u=Ja(s),l=u.name,d=u.dynamic,f=c[l]=qa("template",[],e);f.slotTarget=l,f.slotTargetDynamic=d,f.children=e.children.filter(function(e){if(!e.slotScope)return e.parent=f,!0}),f.slotScope=s.value||za,e.children=[],e.plain=!1}}}(e),function(e){"slot"===e.tag&&(e.slotName=vo(e,"name"),e.key&&_a("`key` does not work on <slot> because slots are abstract outlets and can possibly expand into multiple elements. Use the key on a wrapping element instead.",ho(e,"key")))}(e),function(e){var t;(t=vo(e,"is"))&&(e.component=t);null!=mo(e,"inline-template")&&(e.inlineTemplate=!0)}(e);for(var o=0;o<xa.length;o++)e=xa[o](e,t)||e;return function(e){var t,n,r,o,i,a,s,c,u=e.attrsList;for(t=0,n=u.length;t<n;t++)if(r=o=u[t].name,i=u[t].value,Na.test(r))if(e.hasBindings=!0,(a=Ya(r.replace(Na,"")))&&(r=r.replace(La,"")),Ma.test(r))r=r.replace(Ma,""),i=ro(i),(c=ja.test(r))&&(r=r.slice(1,-1)),0===i.trim().length&&_a('The value for a v-bind expression cannot be empty. Found in "v-bind:'+r+'"'),a&&(a.prop&&!c&&"innerHtml"===(r=S(r))&&(r="innerHTML"),a.camel&&!c&&(r=S(r)),a.sync&&(s=Co(i,"$event"),c?po(e,'"update:"+('+r+")",s,null,!1,_a,u[t],!0):(po(e,"update:"+S(r),s,null,!1,_a,u[t]),A(r)!==S(r)&&po(e,"update:"+A(r),s,null,!1,_a,u[t])))),a&&a.prop||!e.component&&Aa(e.tag,e.attrsMap.type,r)?so(e,r,i,u[t],c):co(e,r,i,u[t],c);else if(Oa.test(r))r=r.replace(Oa,""),(c=ja.test(r))&&(r=r.slice(1,-1)),po(e,r,i,a,!1,_a,u[t],c);else{var l=(r=r.replace(Na,"")).match(Ba),d=l&&l[1];c=!1,d&&(r=r.slice(0,-(d.length+1)),ja.test(d)&&(d=d.slice(1,-1),c=!0)),lo(e,r,o,i,d,c,a,u[t]),"model"===r&&ts(e,i)}else{var f=Wi(i,wa);f&&_a(r+'="'+i+'": Interpolation inside attributes has been removed. Use v-bind or the colon shorthand instead. For example, instead of <div id="{{ val }}">, use <div :id="val">.',u[t]),co(e,r,JSON.stringify(i),u[t]),!e.component&&"muted"===r&&Aa(e.tag,e.attrsMap.type,r)&&so(e,r,"true",u[t])}}(e),e}function Ga(e){var t;if(t=mo(e,"v-for")){var n=function(e){var t=e.match(Ia);if(!t)return;var n={};n.for=t[2].trim();var r=t[1].trim().replace(Da,""),o=r.match(Pa);o?(n.alias=r.replace(Pa,"").trim(),n.iterator1=o[1].trim(),o[2]&&(n.iterator2=o[2].trim())):n.alias=r;return n}(t);n?O(e,n):_a("Invalid v-for expression: "+t,e.rawAttrsMap["v-for"])}}function Wa(e,t){e.ifConditions||(e.ifConditions=[]),e.ifConditions.push(t)}function Ja(e){var t=e.name.replace($a,"");return t||("#"!==e.name[0]?t="default":_a("v-slot shorthand syntax requires a slot name.",e)),ja.test(t)?{name:t.slice(1,-1),dynamic:!0}:{name:'"'+t+'"',dynamic:!1}}function Ya(e){var t=e.match(La);if(t){var n={};return t.forEach(function(e){n[e.slice(1)]=!0}),n}}function Qa(e){for(var t={},n=0,r=e.length;n<r;n++)!t[e[n].name]||Q||ee||_a("duplicate attribute: "+e[n].name,e[n]),t[e[n].name]=e[n].value;return t}var Za=/^xmlns:NS\d+/,es=/^NS\d+:/;function ts(e,t){for(var n=e;n;)n.for&&n.alias===t&&_a("<"+e.tag+' v-model="'+t+'">: You are binding v-model directly to a v-for iteration alias. This will not be able to modify the v-for source array because writing to the alias is like modifying a function local variable. Consider using an array of objects and use v-model on an object property instead.',e.rawAttrsMap["v-model"]),n=n.parent}function ns(e){return qa(e.tag,e.attrsList.slice(),e.parent)}var rs,os,is=[Yi,Qi,{preTransformNode:function(e,t){if("input"===e.tag){var n,r=e.attrsMap;if(!r["v-model"])return;if((r[":type"]||r["v-bind:type"])&&(n=vo(e,"type")),r.type||n||!r["v-bind"]||(n="("+r["v-bind"]+").type"),n){var o=mo(e,"v-if",!0),i=o?"&&("+o+")":"",a=null!=mo(e,"v-else",!0),s=mo(e,"v-else-if",!0),c=ns(e);Ga(c),uo(c,"type","checkbox"),Xa(c,t),c.processed=!0,c.if="("+n+")==='checkbox'"+i,Wa(c,{exp:c.if,block:c});var u=ns(e);mo(u,"v-for",!0),uo(u,"type","radio"),Xa(u,t),Wa(c,{exp:"("+n+")==='radio'"+i,block:u});var l=ns(e);return mo(l,"v-for",!0),uo(l,":type",n),Xa(l,t),Wa(c,{exp:o,block:l}),a?c.else=!0:s&&(c.elseif=s),c}}}}],as={expectHTML:!0,modules:is,directives:{model:function(e,t,n){eo=n;var r=t.value,o=t.modifiers,i=e.tag,a=e.attrsMap.type;if("input"===i&&"file"===a&&eo("<"+e.tag+' v-model="'+r+'" type="file">:\nFile inputs are read only. Use a v-on:change listener instead.',e.rawAttrsMap["v-model"]),e.component)return bo(e,r,o),!1;if("select"===i)!function(e,t,n){var r='var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = "_value" in o ? o._value : o.value;return '+(n&&n.number?"_n(val)":"val")+"});";r=r+" "+Co(t,"$event.target.multiple ? $$selectedVal : $$selectedVal[0]"),po(e,"change",r,null,!0)}(e,r,o);else if("input"===i&&"checkbox"===a)!function(e,t,n){var r=n&&n.number,o=vo(e,"value")||"null",i=vo(e,"true-value")||"true",a=vo(e,"false-value")||"false";so(e,"checked","Array.isArray("+t+")?_i("+t+","+o+")>-1"+("true"===i?":("+t+")":":_q("+t+","+i+")")),po(e,"change","var $$a="+t+",$$el=$event.target,$$c=$$el.checked?("+i+"):("+a+");if(Array.isArray($$a)){var $$v="+(r?"_n("+o+")":o)+",$$i=_i($$a,$$v);if($$el.checked){$$i<0&&("+Co(t,"$$a.concat([$$v])")+")}else{$$i>-1&&("+Co(t,"$$a.slice(0,$$i).concat($$a.slice($$i+1))")+")}}else{"+Co(t,"$$c")+"}",null,!0)}(e,r,o);else if("input"===i&&"radio"===a)!function(e,t,n){var r=n&&n.number,o=vo(e,"value")||"null";so(e,"checked","_q("+t+","+(o=r?"_n("+o+")":o)+")"),po(e,"change",Co(t,o),null,!0)}(e,r,o);else if("input"===i||"textarea"===i)!function(e,t,n){var r=e.attrsMap.type,o=e.attrsMap["v-bind:value"]||e.attrsMap[":value"],i=e.attrsMap["v-bind:type"]||e.attrsMap[":type"];if(o&&!i){var a=e.attrsMap["v-bind:value"]?"v-bind:value":":value";eo(a+'="'+o+'" conflicts with v-model on the same element because the latter already expands to a value binding internally',e.rawAttrsMap[a])}var s=n||{},c=s.lazy,u=s.number,l=s.trim,d=!c&&"range"!==r,f=c?"change":"range"===r?Ao:"input",p="$event.target.value";l&&(p="$event.target.value.trim()"),u&&(p="_n("+p+")");var h=Co(t,p);d&&(h="if($event.target.composing)return;"+h),so(e,"value","("+t+")"),po(e,f,h,null,!0),(l||u)&&po(e,"blur","$forceUpdate()")}(e,r,o);else{if(!F.isReservedTag(i))return bo(e,r,o),!1;eo("<"+e.tag+' v-model="'+r+"\">: v-model is not supported on this element type. If you are working with contenteditable, it's recommended to wrap a library dedicated for that purpose inside a custom component.",e.rawAttrsMap["v-model"])}return!0},text:function(e,t){t.value&&so(e,"textContent","_s("+t.value+")",t)},html:function(e,t){t.value&&so(e,"innerHTML","_s("+t.value+")",t)}},isPreTag:function(e){return"pre"===e},isUnaryTag:ea,mustUseProp:cr,canBeLeftOpenTag:ta,isReservedTag:Sr,getTagNamespace:kr,staticKeys:function(e){return e.reduce(function(e,t){return e.concat(t.staticKeys||[])},[]).join(",")}(is)},ss=w(function(e){return m("type,tag,attrsList,attrsMap,plain,parent,children,attrs,start,end,rawAttrsMap"+(e?","+e:""))});function cs(e,t){e&&(rs=ss(t.staticKeys||""),os=t.isReservedTag||P,function e(t){t.static=function(e){if(2===e.type)return!1;if(3===e.type)return!0;return!(!e.pre&&(e.hasBindings||e.if||e.for||g(e.tag)||!os(e.tag)||function(e){for(;e.parent;){if("template"!==(e=e.parent).tag)return!1;if(e.for)return!0}return!1}(e)||!Object.keys(e).every(rs)))}(t);if(1===t.type){if(!os(t.tag)&&"slot"!==t.tag&&null==t.attrsMap["inline-template"])return;for(var n=0,r=t.children.length;n<r;n++){var o=t.children[n];e(o),o.static||(t.static=!1)}if(t.ifConditions)for(var i=1,a=t.ifConditions.length;i<a;i++){var s=t.ifConditions[i].block;e(s),s.static||(t.static=!1)}}}(e),function e(t,n){if(1===t.type){if((t.static||t.once)&&(t.staticInFor=n),t.static&&t.children.length&&(1!==t.children.length||3!==t.children[0].type))return void(t.staticRoot=!0);if(t.staticRoot=!1,t.children)for(var r=0,o=t.children.length;r<o;r++)e(t.children[r],n||!!t.for);if(t.ifConditions)for(var i=1,a=t.ifConditions.length;i<a;i++)e(t.ifConditions[i].block,n)}}(e,!1))}var us=/^([\w$_]+|\([^)]*?\))\s*=>|^function\s*(?:[\w$]+)?\s*\(/,ls=/\([^)]*?\);*$/,ds=/^[A-Za-z_$][\w$]*(?:\.[A-Za-z_$][\w$]*|\['[^']*?']|\["[^"]*?"]|\[\d+]|\[[A-Za-z_$][\w$]*])*$/,fs={esc:27,tab:9,enter:13,space:32,up:38,left:37,right:39,down:40,delete:[8,46]},ps={esc:["Esc","Escape"],tab:"Tab",enter:"Enter",space:[" ","Spacebar"],up:["Up","ArrowUp"],left:["Left","ArrowLeft"],right:["Right","ArrowRight"],down:["Down","ArrowDown"],delete:["Backspace","Delete","Del"]},hs=function(e){return"if("+e+")return null;"},vs={stop:"$event.stopPropagation();",prevent:"$event.preventDefault();",self:hs("$event.target !== $event.currentTarget"),ctrl:hs("!$event.ctrlKey"),shift:hs("!$event.shiftKey"),alt:hs("!$event.altKey"),meta:hs("!$event.metaKey"),left:hs("'button' in $event && $event.button !== 0"),middle:hs("'button' in $event && $event.button !== 1"),right:hs("'button' in $event && $event.button !== 2")};function ms(e,t){var n=t?"nativeOn:":"on:",r="",o="";for(var i in e){var a=gs(e[i]);e[i]&&e[i].dynamic?o+=i+","+a+",":r+='"'+i+'":'+a+","}return r="{"+r.slice(0,-1)+"}",o?n+"_d("+r+",["+o.slice(0,-1)+"])":n+r}function gs(e){if(!e)return"function(){}";if(Array.isArray(e))return"["+e.map(function(e){return gs(e)}).join(",")+"]";var t=ds.test(e.value),n=us.test(e.value),r=ds.test(e.value.replace(ls,""));if(e.modifiers){var o="",i="",a=[];for(var s in e.modifiers)if(vs[s])i+=vs[s],fs[s]&&a.push(s);else if("exact"===s){var c=e.modifiers;i+=hs(["ctrl","shift","alt","meta"].filter(function(e){return!c[e]}).map(function(e){return"$event."+e+"Key"}).join("||"))}else a.push(s);return a.length&&(o+=function(e){return"if(!$event.type.indexOf('key')&&"+e.map(ys).join("&&")+")return null;"}(a)),i&&(o+=i),"function($event){"+o+(t?"return "+e.value+"($event)":n?"return ("+e.value+")($event)":r?"return "+e.value:e.value)+"}"}return t||n?e.value:"function($event){"+(r?"return "+e.value:e.value)+"}"}function ys(e){var t=parseInt(e,10);if(t)return"$event.keyCode!=="+t;var n=fs[e],r=ps[e];return"_k($event.keyCode,"+JSON.stringify(e)+","+JSON.stringify(n)+",$event.key,"+JSON.stringify(r)+")"}var bs={on:function(e,t){t.modifiers&&de("v-on without argument does not support modifiers."),e.wrapListeners=function(e){return"_g("+e+","+t.value+")"}},bind:function(e,t){e.wrapData=function(n){return"_b("+n+",'"+e.tag+"',"+t.value+","+(t.modifiers&&t.modifiers.prop?"true":"false")+(t.modifiers&&t.modifiers.sync?",true":"")+")"}},cloak:I},Cs=function(e){this.options=e,this.warn=e.warn||io,this.transforms=ao(e.modules,"transformCode"),this.dataGenFns=ao(e.modules,"genData"),this.directives=O(O({},bs),e.directives);var t=e.isReservedTag||P;this.maybeComponent=function(e){return!!e.component||!t(e.tag)},this.onceId=0,this.staticRenderFns=[],this.pre=!1};function _s(e,t){var n=new Cs(t);return{render:"with(this){return "+(e?ws(e,n):'_c("div")')+"}",staticRenderFns:n.staticRenderFns}}function ws(e,t){if(e.parent&&(e.pre=e.pre||e.parent.pre),e.staticRoot&&!e.staticProcessed)return xs(e,t);if(e.once&&!e.onceProcessed)return Ss(e,t);if(e.for&&!e.forProcessed)return Ts(e,t);if(e.if&&!e.ifProcessed)return ks(e,t);if("template"!==e.tag||e.slotTarget||t.pre){if("slot"===e.tag)return function(e,t){var n=e.slotName||'"default"',r=Os(e,t),o="_t("+n+(r?","+r:""),i=e.attrs||e.dynamicAttrs?Ps((e.attrs||[]).concat(e.dynamicAttrs||[]).map(function(e){return{name:S(e.name),value:e.value,dynamic:e.dynamic}})):null,a=e.attrsMap["v-bind"];!i&&!a||r||(o+=",null");i&&(o+=","+i);a&&(o+=(i?"":",null")+","+a);return o+")"}(e,t);var n;if(e.component)n=function(e,t,n){var r=t.inlineTemplate?null:Os(t,n,!0);return"_c("+e+","+As(t,n)+(r?","+r:"")+")"}(e.component,e,t);else{var r;(!e.plain||e.pre&&t.maybeComponent(e))&&(r=As(e,t));var o=e.inlineTemplate?null:Os(e,t,!0);n="_c('"+e.tag+"'"+(r?","+r:"")+(o?","+o:"")+")"}for(var i=0;i<t.transforms.length;i++)n=t.transforms[i](e,n);return n}return Os(e,t)||"void 0"}function xs(e,t){e.staticProcessed=!0;var n=t.pre;return e.pre&&(t.pre=e.pre),t.staticRenderFns.push("with(this){return "+ws(e,t)+"}"),t.pre=n,"_m("+(t.staticRenderFns.length-1)+(e.staticInFor?",true":"")+")"}function Ss(e,t){if(e.onceProcessed=!0,e.if&&!e.ifProcessed)return ks(e,t);if(e.staticInFor){for(var n="",r=e.parent;r;){if(r.for){n=r.key;break}r=r.parent}return n?"_o("+ws(e,t)+","+t.onceId+++","+n+")":(t.warn("v-once can only be used inside v-for that is keyed. ",e.rawAttrsMap["v-once"]),ws(e,t))}return xs(e,t)}function ks(e,t,n,r){return e.ifProcessed=!0,function e(t,n,r,o){if(!t.length)return o||"_e()";var i=t.shift();return i.exp?"("+i.exp+")?"+a(i.block)+":"+e(t,n,r,o):""+a(i.block);function a(e){return r?r(e,n):e.once?Ss(e,n):ws(e,n)}}(e.ifConditions.slice(),t,n,r)}function Ts(e,t,n,r){var o=e.for,i=e.alias,a=e.iterator1?","+e.iterator1:"",s=e.iterator2?","+e.iterator2:"";return t.maybeComponent(e)&&"slot"!==e.tag&&"template"!==e.tag&&!e.key&&t.warn("<"+e.tag+' v-for="'+i+" in "+o+'">: component lists rendered with v-for should have explicit keys. See https://vuejs.org/guide/list.html#key for more info.',e.rawAttrsMap["v-for"],!0),e.forProcessed=!0,(r||"_l")+"(("+o+"),function("+i+a+s+"){return "+(n||ws)(e,t)+"})"}function As(e,t){var n="{",r=function(e,t){var n=e.directives;if(!n)return;var r,o,i,a,s="directives:[",c=!1;for(r=0,o=n.length;r<o;r++){i=n[r],a=!0;var u=t.directives[i.name];u&&(a=!!u(e,i,t.warn)),a&&(c=!0,s+='{name:"'+i.name+'",rawName:"'+i.rawName+'"'+(i.value?",value:("+i.value+"),expression:"+JSON.stringify(i.value):"")+(i.arg?",arg:"+(i.isDynamicArg?i.arg:'"'+i.arg+'"'):"")+(i.modifiers?",modifiers:"+JSON.stringify(i.modifiers):"")+"},")}if(c)return s.slice(0,-1)+"]"}(e,t);r&&(n+=r+","),e.key&&(n+="key:"+e.key+","),e.ref&&(n+="ref:"+e.ref+","),e.refInFor&&(n+="refInFor:true,"),e.pre&&(n+="pre:true,"),e.component&&(n+='tag:"'+e.tag+'",');for(var o=0;o<t.dataGenFns.length;o++)n+=t.dataGenFns[o](e);if(e.attrs&&(n+="attrs:"+Ps(e.attrs)+","),e.props&&(n+="domProps:"+Ps(e.props)+","),e.events&&(n+=ms(e.events,!1)+","),e.nativeEvents&&(n+=ms(e.nativeEvents,!0)+","),e.slotTarget&&!e.slotScope&&(n+="slot:"+e.slotTarget+","),e.scopedSlots&&(n+=function(e,t,n){var r=e.for||Object.keys(t).some(function(e){var n=t[e];return n.slotTargetDynamic||n.if||n.for||Es(n)}),o=!!e.if;if(!r)for(var i=e.parent;i;){if(i.slotScope&&i.slotScope!==za||i.for){r=!0;break}i.if&&(o=!0),i=i.parent}var a=Object.keys(t).map(function(e){return Rs(t[e],n)}).join(",");return"scopedSlots:_u(["+a+"]"+(r?",null,true":"")+(!r&&o?",null,false,"+function(e){var t=5381,n=e.length;for(;n;)t=33*t^e.charCodeAt(--n);return t>>>0}(a):"")+")"}(e,e.scopedSlots,t)+","),e.model&&(n+="model:{value:"+e.model.value+",callback:"+e.model.callback+",expression:"+e.model.expression+"},"),e.inlineTemplate){var i=function(e,t){var n=e.children[0];1===e.children.length&&1===n.type||t.warn("Inline-template components must have exactly one child element.",{start:e.start});if(n&&1===n.type){var r=_s(n,t.options);return"inlineTemplate:{render:function(){"+r.render+"},staticRenderFns:["+r.staticRenderFns.map(function(e){return"function(){"+e+"}"}).join(",")+"]}"}}(e,t);i&&(n+=i+",")}return n=n.replace(/,$/,"")+"}",e.dynamicAttrs&&(n="_b("+n+',"'+e.tag+'",'+Ps(e.dynamicAttrs)+")"),e.wrapData&&(n=e.wrapData(n)),e.wrapListeners&&(n=e.wrapListeners(n)),n}function Es(e){return 1===e.type&&("slot"===e.tag||e.children.some(Es))}function Rs(e,t){var n=e.attrsMap["slot-scope"];if(e.if&&!e.ifProcessed&&!n)return ks(e,t,Rs,"null");if(e.for&&!e.forProcessed)return Ts(e,t,Rs);var r=e.slotScope===za?"":String(e.slotScope),o="function("+r+"){return "+("template"===e.tag?e.if&&n?"("+e.if+")?"+(Os(e,t)||"undefined")+":undefined":Os(e,t)||"undefined":ws(e,t))+"}",i=r?"":",proxy:true";return"{key:"+(e.slotTarget||'"default"')+",fn:"+o+i+"}"}function Os(e,t,n,r,o){var i=e.children;if(i.length){var a=i[0];if(1===i.length&&a.for&&"template"!==a.tag&&"slot"!==a.tag){var s=n?t.maybeComponent(a)?",1":",0":"";return""+(r||ws)(a,t)+s}var c=n?function(e,t){for(var n=0,r=0;r<e.length;r++){var o=e[r];if(1===o.type){if(Ns(o)||o.ifConditions&&o.ifConditions.some(function(e){return Ns(e.block)})){n=2;break}(t(o)||o.ifConditions&&o.ifConditions.some(function(e){return t(e.block)}))&&(n=1)}}return n}(i,t.maybeComponent):0,u=o||Is;return"["+i.map(function(e){return u(e,t)}).join(",")+"]"+(c?","+c:"")}}function Ns(e){return void 0!==e.for||"template"===e.tag||"slot"===e.tag}function Is(e,t){return 1===e.type?ws(e,t):3===e.type&&e.isComment?(r=e,"_e("+JSON.stringify(r.text)+")"):"_v("+(2===(n=e).type?n.expression:Ds(JSON.stringify(n.text)))+")";var n,r}function Ps(e){for(var t="",n="",r=0;r<e.length;r++){var o=e[r],i=Ds(o.value);o.dynamic?n+=o.name+","+i+",":t+='"'+o.name+'":'+i+","}return t="{"+t.slice(0,-1)+"}",n?"_d("+t+",["+n.slice(0,-1)+"])":t}function Ds(e){return e.replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}var js=new RegExp("\\b"+"do,if,for,let,new,try,var,case,else,with,await,break,catch,class,const,super,throw,while,yield,delete,export,import,return,switch,default,extends,finally,continue,debugger,function,arguments".split(",").join("\\b|\\b")+"\\b"),Bs=new RegExp("\\b"+"delete,typeof,void".split(",").join("\\s*\\([^\\)]*\\)|\\b")+"\\s*\\([^\\)]*\\)"),Ms=/'(?:[^'\\]|\\.)*'|"(?:[^"\\]|\\.)*"|`(?:[^`\\]|\\.)*\$\{|\}(?:[^`\\]|\\.)*`|`(?:[^`\\]|\\.)*`/g;function Ls(e,t){e&&function e(t,n){if(1===t.type){for(var r in t.attrsMap)if(Na.test(r)){var o=t.attrsMap[r];if(o){var i=t.rawAttrsMap[r];"v-for"===r?Us(t,'v-for="'+o+'"',n,i):Oa.test(r)?$s(o,r+'="'+o+'"',n,i):Hs(o,r+'="'+o+'"',n,i)}}if(t.children)for(var a=0;a<t.children.length;a++)e(t.children[a],n)}else 2===t.type&&Hs(t.expression,t.text,n,t)}(e,t)}function $s(e,t,n,r){var o=e.replace(Ms,""),i=o.match(Bs);i&&"$"!==o.charAt(i.index-1)&&n('avoid using JavaScript unary operator as property name: "'+i[0]+'" in expression '+t.trim(),r),Hs(e,t,n,r)}function Us(e,t,n,r){Hs(e.for||"",t,n,r),Fs(e.alias,"v-for alias",t,n,r),Fs(e.iterator1,"v-for iterator",t,n,r),Fs(e.iterator2,"v-for iterator",t,n,r)}function Fs(e,t,n,r,o){if("string"==typeof e)try{new Function("var "+e+"=_")}catch(i){r("invalid "+t+' "'+e+'" in expression: '+n.trim(),o)}}function Hs(e,t,n,r){try{new Function("return "+e)}catch(i){var o=e.replace(Ms,"").match(js);n(o?'avoid using JavaScript keyword as property name: "'+o[0]+'"\n  Raw expression: '+t.trim():"invalid expression: "+i.message+" in\n\n    "+e+"\n\n  Raw expression: "+t.trim()+"\n",r)}}var Ks=2;function zs(e,t){var n="";if(t>0)for(;1&t&&(n+=e),!((t>>>=1)<=0);)e+=e;return n}function qs(e,t){try{return new Function(e)}catch(n){return t.push({err:n,code:e}),I}}function Vs(e){var t=Object.create(null);return function(n,r,o){var i=(r=O({},r)).warn||de;delete r.warn;try{new Function("return 1")}catch(e){e.toString().match(/unsafe-eval|CSP/)&&i("It seems you are using the standalone build of Vue.js in an environment with Content Security Policy that prohibits unsafe-eval. The template compiler cannot work in this environment. Consider relaxing the policy to allow unsafe-eval or pre-compiling your templates into render functions.")}var a=r.delimiters?String(r.delimiters)+n:n;if(t[a])return t[a];var s=e(n,r);s.errors&&s.errors.length&&(r.outputSourceRange?s.errors.forEach(function(e){i("Error compiling template:\n\n"+e.msg+"\n\n"+function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=e.length);for(var r=e.split(/\r?\n/),o=0,i=[],a=0;a<r.length;a++)if((o+=r[a].length+1)>=t){for(var s=a-Ks;s<=a+Ks||n>o;s++)if(!(s<0||s>=r.length)){i.push(""+(s+1)+zs(" ",3-String(s+1).length)+"|  "+r[s]);var c=r[s].length;if(s===a){var u=t-(o-c)+1,l=n>o?c-u:n-t;i.push("   |  "+zs(" ",u)+zs("^",l))}else if(s>a){if(n>o){var d=Math.min(n-o,c);i.push("   |  "+zs("^",d))}o+=c+1}}break}return i.join("\n")}(n,e.start,e.end),o)}):i("Error compiling template:\n\n"+n+"\n\n"+s.errors.map(function(e){return"- "+e}).join("\n")+"\n",o)),s.tips&&s.tips.length&&(r.outputSourceRange?s.tips.forEach(function(e){return fe(e.msg,o)}):s.tips.forEach(function(e){return fe(e,o)}));var c={},u=[];return c.render=qs(s.render,u),c.staticRenderFns=s.staticRenderFns.map(function(e){return qs(e,u)}),s.errors&&s.errors.length||!u.length||i("Failed to generate render function:\n\n"+u.map(function(e){var t=e.err,n=e.code;return t.toString()+" in\n\n"+n+"\n"}).join("\n"),o),t[a]=c}}var Xs,Gs,Ws=(Xs=function(e,t){var n=Va(e.trim(),t);!1!==t.optimize&&cs(n,t);var r=_s(n,t);return{ast:n,render:r.render,staticRenderFns:r.staticRenderFns}},function(e){function t(t,n){var r=Object.create(e),o=[],i=[],a=function(e,t,n){(n?i:o).push(e)};if(n){if(n.outputSourceRange){var s=t.match(/^\s*/)[0].length;a=function(e,t,n){var r={msg:e};t&&(null!=t.start&&(r.start=t.start+s),null!=t.end&&(r.end=t.end+s)),(n?i:o).push(r)}}for(var c in n.modules&&(r.modules=(e.modules||[]).concat(n.modules)),n.directives&&(r.directives=O(Object.create(e.directives||null),n.directives)),n)"modules"!==c&&"directives"!==c&&(r[c]=n[c])}r.warn=a;var u=Xs(t.trim(),r);return Ls(u.ast,a),u.errors=o,u.tips=i,u}return{compile:t,compileToFunctions:Vs(t)}})(as),Js=(Ws.compile,Ws.compileToFunctions);function Ys(e){return(Gs=Gs||document.createElement("div")).innerHTML=e?'<a href="\n"/>':'<div a="\n"/>',Gs.innerHTML.indexOf("&#10;")>0}var Qs=!!G&&Ys(!1),Zs=!!G&&Ys(!0),ec=w(function(e){var t=Er(e);return t&&t.innerHTML}),tc=Qn.prototype.$mount;return Qn.prototype.$mount=function(e,t){if((e=e&&Er(e))===document.body||e===document.documentElement)return de("Do not mount Vue to <html> or <body> - mount to normal elements instead."),this;var n=this.$options;if(!n.render){var r=n.template;if(r)if("string"==typeof r)"#"===r.charAt(0)&&((r=ec(r))||de("Template element not found or is empty: "+n.template,this));else{if(!r.nodeType)return de("invalid template option:"+r,this),this;r=r.innerHTML}else e&&(r=function(e){if(e.outerHTML)return e.outerHTML;var t=document.createElement("div");return t.appendChild(e.cloneNode(!0)),t.innerHTML}(e));if(r){F.performance&&at&&at("compile");var o=Js(r,{outputSourceRange:!0,shouldDecodeNewlines:Qs,shouldDecodeNewlinesForHref:Zs,delimiters:n.delimiters,comments:n.comments},this),i=o.render,a=o.staticRenderFns;n.render=i,n.staticRenderFns=a,F.performance&&at&&(at("compile end"),st("vue "+this._name+" compile","compile","compile end"))}}return tc.call(this,e,t)},Qn.compile=Js,Qn},e.exports=r()}).call(this,n(31),n(55).setImmediate)},function(e,t,n){(function(e){var r=void 0!==e&&e||"undefined"!=typeof self&&self||window,o=Function.prototype.apply;function i(e,t){this._id=e,this._clearFn=t}t.setTimeout=function(){return new i(o.call(setTimeout,r,arguments),clearTimeout)},t.setInterval=function(){return new i(o.call(setInterval,r,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},i.prototype.unref=i.prototype.ref=function(){},i.prototype.close=function(){this._clearFn.call(r,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},n(56),t.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==e&&e.setImmediate||this&&this.setImmediate,t.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==e&&e.clearImmediate||this&&this.clearImmediate}).call(this,n(31))},function(e,t,n){(function(e,t){!function(e,n){"use strict";if(!e.setImmediate){var r,o,i,a,s,c=1,u={},l=!1,d=e.document,f=Object.getPrototypeOf&&Object.getPrototypeOf(e);f=f&&f.setTimeout?f:e,"[object process]"==={}.toString.call(e.process)?r=function(e){t.nextTick(function(){h(e)})}:!function(){if(e.postMessage&&!e.importScripts){var t=!0,n=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=n,t}}()?e.MessageChannel?((i=new MessageChannel).port1.onmessage=function(e){h(e.data)},r=function(e){i.port2.postMessage(e)}):d&&"onreadystatechange"in d.createElement("script")?(o=d.documentElement,r=function(e){var t=d.createElement("script");t.onreadystatechange=function(){h(e),t.onreadystatechange=null,o.removeChild(t),t=null},o.appendChild(t)}):r=function(e){setTimeout(h,0,e)}:(a="setImmediate$"+Math.random()+"$",s=function(t){t.source===e&&"string"==typeof t.data&&0===t.data.indexOf(a)&&h(+t.data.slice(a.length))},e.addEventListener?e.addEventListener("message",s,!1):e.attachEvent("onmessage",s),r=function(t){e.postMessage(a+t,"*")}),f.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),n=0;n<t.length;n++)t[n]=arguments[n+1];var o={callback:e,args:t};return u[c]=o,r(c),c++},f.clearImmediate=p}function p(e){delete u[e]}function h(e){if(l)setTimeout(h,0,e);else{var t=u[e];if(t){l=!0;try{!function(e){var t=e.callback,r=e.args;switch(r.length){case 0:t();break;case 1:t(r[0]);break;case 2:t(r[0],r[1]);break;case 3:t(r[0],r[1],r[2]);break;default:t.apply(n,r)}}(t)}finally{p(e),l=!1}}}}}("undefined"==typeof self?void 0===e?this:e:self)}).call(this,n(31),n(8))}])});
//# sourceMappingURL=bundle.js.map