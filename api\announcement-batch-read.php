<?php
/**
 * 批量标记公告为已读API
 * 支持JWT认证，批量标记多个公告为已读状态
 */

require_once 'auth.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit;
}

$auth = new AuthAPI();

try {
    // 验证用户认证
    $user = $auth->requireAuth();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        batchMarkAsRead($auth, $user);
    } else {
        $auth->jsonResponse(405, '不支持的请求方法');
    }
} catch (Exception $e) {
    error_log('批量标记已读API错误: ' . $e->getMessage());
    $auth->jsonResponse(500, '服务器内部错误');
}

/**
 * 批量标记公告为已读
 */
function batchMarkAsRead($auth, $user) {
    $conn = $auth->getConn();
    
    $user_id = $user['id'];
    
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['announcement_ids'])) {
        $auth->jsonResponse(400, '缺少公告ID列表');
    }
    
    $announcement_ids = $input['announcement_ids'];
    
    if (!is_array($announcement_ids) || empty($announcement_ids)) {
        $auth->jsonResponse(400, '公告ID列表格式错误或为空');
    }
    
    // 验证公告ID
    $valid_ids = [];
    foreach ($announcement_ids as $id) {
        if (is_numeric($id) && $id > 0) {
            $valid_ids[] = (int)$id;
        }
    }
    
    if (empty($valid_ids)) {
        $auth->jsonResponse(400, '没有有效的公告ID');
    }
    
    // 限制批量操作数量
    if (count($valid_ids) > 100) {
        $auth->jsonResponse(400, '一次最多只能标记100个公告');
    }
    
    // 开始事务
    $conn->begin_transaction();
    
    try {
        $success_count = 0;
        $already_read_count = 0;
        $invalid_count = 0;
        $processed_ids = [];
        
        foreach ($valid_ids as $announcement_id) {
            // 检查公告是否存在且已发布
            $check_stmt = $conn->prepare("SELECT id FROM announcements WHERE id = ? AND status = 'published'");
            $check_stmt->bind_param("i", $announcement_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows === 0) {
                $invalid_count++;
                continue;
            }
            
            // 检查是否已经标记为已读
            $read_check_stmt = $conn->prepare("SELECT id FROM announcement_reads WHERE user_id = ? AND announcement_id = ?");
            $read_check_stmt->bind_param("ii", $user_id, $announcement_id);
            $read_check_stmt->execute();
            $read_check_result = $read_check_stmt->get_result();
            
            if ($read_check_result->num_rows > 0) {
                $already_read_count++;
                $processed_ids[] = $announcement_id;
                continue;
            }
            
            // 插入已读记录
            $insert_stmt = $conn->prepare("INSERT INTO announcement_reads (user_id, announcement_id) VALUES (?, ?)");
            $insert_stmt->bind_param("ii", $user_id, $announcement_id);
            
            if ($insert_stmt->execute()) {
                $success_count++;
                $processed_ids[] = $announcement_id;
            }
        }
        
        // 提交事务
        $conn->commit();
        
        // 返回结果
        $auth->jsonResponse(200, '批量标记已读完成', [
            'total_requested' => count($valid_ids),
            'success_count' => $success_count,
            'already_read_count' => $already_read_count,
            'invalid_count' => $invalid_count,
            'processed_ids' => $processed_ids,
            'summary' => [
                'new_reads' => $success_count,
                'already_read' => $already_read_count,
                'invalid_announcements' => $invalid_count
            ]
        ]);
        
    } catch (Exception $e) {
        // 回滚事务
        $conn->rollback();
        error_log('批量标记已读失败: ' . $e->getMessage());
        $auth->jsonResponse(500, '批量标记已读失败');
    }
}
?>
