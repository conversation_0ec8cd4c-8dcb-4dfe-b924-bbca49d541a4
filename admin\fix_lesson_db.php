<?php
/**
 * 课时管理数据库修复脚本
 * 快速修复和创建课时相关的数据库表
 */

require_once '../includes/db.php';

header('Content-Type: application/json; charset=utf-8');

$results = [];
$success = true;

try {
    // 1. 检查并创建lessons表
    $check_lessons = $conn->query("SHOW TABLES LIKE 'lessons'");
    if ($check_lessons->num_rows == 0) {
        $create_lessons = "
            CREATE TABLE `lessons` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `course_id` int(11) NOT NULL COMMENT '课程ID',
                `title` varchar(255) NOT NULL COMMENT '课时标题',
                `description` text COMMENT '课时描述',
                `video_url` varchar(500) DEFAULT NULL COMMENT '视频链接',
                `duration` int(11) DEFAULT NULL COMMENT '时长（秒）',
                `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
                `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用 1启用',
                `is_free` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否免费：0否 1是',
                `thumbnail` varchar(500) DEFAULT NULL COMMENT '课时缩略图',
                `created_by` int(11) NOT NULL DEFAULT '1' COMMENT '创建者ID',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `idx_course_id` (`course_id`),
                KEY `idx_status` (`status`),
                KEY `idx_sort_order` (`sort_order`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课时表'
        ";
        
        if ($conn->query($create_lessons)) {
            $results[] = "✓ lessons表创建成功";
        } else {
            $success = false;
            $results[] = "✗ lessons表创建失败: " . $conn->error;
        }
    } else {
        $results[] = "✓ lessons表已存在";
        
        // 检查并添加缺失的字段
        $columns_to_check = [
            'is_free' => "ALTER TABLE `lessons` ADD COLUMN `is_free` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否免费：0否 1是'",
            'thumbnail' => "ALTER TABLE `lessons` ADD COLUMN `thumbnail` varchar(500) DEFAULT NULL COMMENT '课时缩略图'",
            'created_by' => "ALTER TABLE `lessons` ADD COLUMN `created_by` int(11) NOT NULL DEFAULT '1' COMMENT '创建者ID'"
        ];
        
        foreach ($columns_to_check as $column => $sql) {
            $check_column = $conn->query("SHOW COLUMNS FROM `lessons` LIKE '$column'");
            if ($check_column->num_rows == 0) {
                if ($conn->query($sql)) {
                    $results[] = "✓ lessons表字段 $column 添加成功";
                } else {
                    $results[] = "⚠ lessons表字段 $column 添加失败: " . $conn->error;
                }
            }
        }
    }

    // 2. 检查并创建lesson_watch_logs表
    $check_logs = $conn->query("SHOW TABLES LIKE 'lesson_watch_logs'");
    if ($check_logs->num_rows == 0) {
        $create_logs = "
            CREATE TABLE `lesson_watch_logs` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL COMMENT '用户ID',
                `lesson_id` int(11) NOT NULL COMMENT '课时ID',
                `course_id` int(11) NOT NULL COMMENT '课程ID',
                `watch_time` int(11) NOT NULL DEFAULT '0' COMMENT '观看时长（秒）',
                `progress_position` int(11) NOT NULL DEFAULT '0' COMMENT '观看进度位置（秒）',
                `is_completed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否完成：0否 1是',
                `completion_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '完成率（0-100）',
                `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
                `user_agent` text COMMENT '用户代理',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `idx_user_id` (`user_id`),
                KEY `idx_lesson_id` (`lesson_id`),
                KEY `idx_course_id` (`course_id`),
                UNIQUE KEY `unique_user_lesson` (`user_id`, `lesson_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课时观看记录表'
        ";
        
        if ($conn->query($create_logs)) {
            $results[] = "✓ lesson_watch_logs表创建成功";
        } else {
            $success = false;
            $results[] = "✗ lesson_watch_logs表创建失败: " . $conn->error;
        }
    } else {
        $results[] = "✓ lesson_watch_logs表已存在";
    }

    // 3. 检查并为courses表添加课时统计字段
    $check_courses = $conn->query("SHOW TABLES LIKE 'courses'");
    if ($check_courses->num_rows > 0) {
        $course_columns = [
            'lesson_count' => "ALTER TABLE `courses` ADD COLUMN `lesson_count` int(11) NOT NULL DEFAULT '0' COMMENT '课时数量'",
            'total_duration' => "ALTER TABLE `courses` ADD COLUMN `total_duration` int(11) NOT NULL DEFAULT '0' COMMENT '总时长（秒）'"
        ];
        
        foreach ($course_columns as $column => $sql) {
            $check_column = $conn->query("SHOW COLUMNS FROM `courses` LIKE '$column'");
            if ($check_column->num_rows == 0) {
                if ($conn->query($sql)) {
                    $results[] = "✓ courses表字段 $column 添加成功";
                } else {
                    $results[] = "⚠ courses表字段 $column 添加失败: " . $conn->error;
                }
            }
        }
    }

    // 4. 创建一些示例课时数据（如果lessons表为空）
    $count_lessons = $conn->query("SELECT COUNT(*) as count FROM lessons");
    $lesson_count = $count_lessons->fetch_assoc()['count'];
    
    if ($lesson_count == 0) {
        // 检查是否有课程
        $count_courses = $conn->query("SELECT COUNT(*) as count FROM courses");
        $course_count = $count_courses->fetch_assoc()['count'];
        
        if ($course_count > 0) {
            // 获取第一个课程ID
            $first_course = $conn->query("SELECT id FROM courses LIMIT 1");
            $course_id = $first_course->fetch_assoc()['id'];
            
            $sample_lessons = [
                [
                    'title' => '第1课：基础入门',
                    'description' => '本课程将带您了解基础知识，为后续学习打下坚实基础。',
                    'duration' => 1800,
                    'sort_order' => 1,
                    'is_free' => 1
                ],
                [
                    'title' => '第2课：核心概念',
                    'description' => '深入学习核心概念，掌握重要的理论知识。',
                    'duration' => 2400,
                    'sort_order' => 2,
                    'is_free' => 1
                ],
                [
                    'title' => '第3课：实践应用',
                    'description' => '通过实际案例学习如何应用所学知识。',
                    'duration' => 3000,
                    'sort_order' => 3,
                    'is_free' => 0
                ]
            ];
            
            $created_count = 0;
            foreach ($sample_lessons as $lesson) {
                $insert_sql = "INSERT INTO lessons (course_id, title, description, duration, sort_order, is_free, created_by) VALUES (?, ?, ?, ?, ?, ?, 1)";
                $stmt = $conn->prepare($insert_sql);
                $stmt->bind_param("issiii", $course_id, $lesson['title'], $lesson['description'], $lesson['duration'], $lesson['sort_order'], $lesson['is_free']);
                
                if ($stmt->execute()) {
                    $created_count++;
                }
            }
            
            if ($created_count > 0) {
                $results[] = "✓ 创建了 $created_count 个示例课时";
                
                // 更新课程统计
                $update_stats = "UPDATE courses SET lesson_count = (SELECT COUNT(*) FROM lessons WHERE course_id = courses.id), total_duration = (SELECT COALESCE(SUM(duration), 0) FROM lessons WHERE course_id = courses.id)";
                if ($conn->query($update_stats)) {
                    $results[] = "✓ 课程统计信息更新成功";
                }
            }
        } else {
            $results[] = "⚠ 没有课程数据，无法创建示例课时";
        }
    } else {
        $results[] = "✓ 已有 $lesson_count 个课时数据";
    }

    // 5. 检查系统设置
    $check_settings = $conn->query("SHOW TABLES LIKE 'settings'");
    if ($check_settings->num_rows > 0) {
        $lesson_settings = [
            ['lesson_auto_next', '1', 'boolean', '是否自动播放下一课时'],
            ['lesson_completion_threshold', '90', 'number', '课时完成判定阈值（百分比）'],
            ['lesson_progress_save_interval', '30', 'number', '进度保存间隔（秒）']
        ];
        
        foreach ($lesson_settings as $setting) {
            $check_setting = $conn->prepare("SELECT COUNT(*) as count FROM settings WHERE setting_key = ?");
            $check_setting->bind_param("s", $setting[0]);
            $check_setting->execute();
            $setting_exists = $check_setting->get_result()->fetch_assoc()['count'];
            
            if ($setting_exists == 0) {
                $insert_setting = $conn->prepare("INSERT INTO settings (setting_key, setting_value, setting_type, description, group_name) VALUES (?, ?, ?, ?, 'lesson')");
                $insert_setting->bind_param("ssss", $setting[0], $setting[1], $setting[2], $setting[3]);
                
                if ($insert_setting->execute()) {
                    $results[] = "✓ 系统设置 {$setting[0]} 添加成功";
                }
            }
        }
    }

} catch (Exception $e) {
    $success = false;
    $results[] = "✗ 发生异常: " . $e->getMessage();
}

// 返回结果
echo json_encode([
    'success' => $success,
    'message' => $success ? '课时管理数据库修复完成' : '课时管理数据库修复失败',
    'results' => $results,
    'timestamp' => date('Y-m-d H:i:s')
], JSON_UNESCAPED_UNICODE);
?>
