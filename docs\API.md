# API 接口文档

## 概述

本文档描述了腾讯云VOD课时管理系统的所有API接口，包括课时管理、视频上传、播放等功能。

## 基础信息

- **Base URL**: `http://your-domain.com/api/`
- **请求格式**: JSON / Form Data
- **响应格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

所有API接口都遵循统一的响应格式：

```json
{
    "success": true,
    "message": "操作成功",
    "data": {
        // 具体数据
    }
}
```

### 错误响应示例

```json
{
    "success": false,
    "message": "错误信息",
    "error": "详细错误描述"
}
```

## 腾讯云VOD相关接口

### 1. 获取上传签名

**接口地址**: `GET /vod-signature.php`

**功能描述**: 获取腾讯云点播上传签名，用于前端直接上传视频。

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| lesson_id | int | 否 | 课时ID |
| class_id | int | 否 | 分类ID |
| procedure | string | 否 | 任务流模板名称 |
| auth | int | 否 | 是否需要管理员权限验证，1为需要 |

**响应示例**:

```json
{
    "success": true,
    "message": "签名生成成功",
    "data": {
        "signature": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expire_time": 1640995200,
        "current_time": 1640991600,
        "config": {
            "region": "ap-beijing",
            "allowed_video_types": ["mp4", "avi", "mov"],
            "max_video_size": 5368709120,
            "signature_expire_time": 3600
        }
    }
}
```

### 2. 查询视频状态

**接口地址**: `GET /vod-status.php`

**功能描述**: 查询腾讯云点播视频的转码状态和播放信息。

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file_id | string | 否 | 腾讯云点播文件ID |
| lesson_id | int | 否 | 课时ID（与file_id二选一） |

**响应示例**:

```json
{
    "success": true,
    "message": "获取视频信息成功",
    "data": {
        "file_id": "123456789",
        "name": "课时视频.mp4",
        "duration": 1800,
        "duration_formatted": "30:00",
        "size": 104857600,
        "size_formatted": "100.00 MB",
        "status": "NORMAL",
        "status_text": "正常",
        "create_time": "2024-01-01T10:00:00Z",
        "update_time": "2024-01-01T10:05:00Z",
        "thumbnail_url": "https://example.com/thumbnail.jpg",
        "transcoding": {
            "status": "SUCCESS",
            "status_text": "转码成功",
            "completed": true,
            "streams": [
                {
                    "url": "https://example.com/video_720p.mp4",
                    "definition": 720,
                    "bitrate": 1000,
                    "height": 720,
                    "width": 1280,
                    "size": 52428800,
                    "size_formatted": "50.00 MB",
                    "duration": 1800,
                    "format": "mp4"
                }
            ]
        },
        "playback_urls": [
            {
                "url": "https://example.com/playlist.m3u8",
                "definition": 0,
                "format": "hls",
                "drm_type": ""
            }
        ]
    }
}
```

### 3. VOD配置检查

**接口地址**: `GET /vod-config.php`

**功能描述**: 检查腾讯云点播配置状态和环境要求。

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| action | string | 否 | 操作类型：check/test/info |

**响应示例**:

```json
{
    "success": true,
    "message": "配置检查完成",
    "data": {
        "config_valid": true,
        "config_errors": [],
        "config": {
            "secret_id": "已配置",
            "secret_key": "已配置",
            "sub_app_id": 0,
            "upload_region": "ap-beijing",
            "max_video_size_mb": 5120,
            "signature_expire_time": 3600
        },
        "environment": {
            "php_version": {
                "required": "7.0.0",
                "current": "7.4.0",
                "status": true
            },
            "extensions": {
                "curl": {"required": true, "status": true},
                "json": {"required": true, "status": true},
                "openssl": {"required": true, "status": true}
            }
        }
    }
}
```

## 课时管理接口

### 1. 获取课时列表

**接口地址**: `GET /lessonList.php`

**功能描述**: 获取指定课程的课时列表。

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| course_id | int | 是 | 课程ID |
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认20 |
| show_all | int | 否 | 是否显示所有状态，1为是 |

**响应示例**:

```json
{
    "success": true,
    "message": "获取课时列表成功",
    "data": {
        "lessons": [
            {
                "id": 1,
                "title": "课时标题",
                "description": "课时描述",
                "video_url": "https://example.com/video.mp4",
                "vod_file_id": "123456789",
                "vod_video_url": "https://vod.example.com/video.mp4",
                "video_type": "vod",
                "thumbnail": "https://example.com/thumb.jpg",
                "duration": 1800,
                "duration_formatted": "30:00",
                "sort_order": 1,
                "status": 1,
                "is_free": 1,
                "created_at": "2024-01-01 10:00:00",
                "updated_at": "2024-01-01 10:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_count": 100,
            "per_page": 20
        }
    }
}
```

### 2. 获取课时详情

**接口地址**: `GET /lessonDetail.php`

**功能描述**: 获取指定课时的详细信息。

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| lesson_id | int | 是 | 课时ID |
| course_id | int | 否 | 课程ID |

**响应示例**:

```json
{
    "success": true,
    "message": "获取课时详情成功",
    "data": {
        "id": 1,
        "course_id": 1,
        "title": "课时标题",
        "description": "课时描述",
        "video_url": "https://example.com/video.mp4",
        "vod_file_id": "123456789",
        "vod_video_url": "https://vod.example.com/video.mp4",
        "video_type": "vod",
        "thumbnail": "https://example.com/thumb.jpg",
        "duration": 1800,
        "duration_formatted": "30:00",
        "sort_order": 1,
        "status": 1,
        "is_free": 1,
        "created_by": 1,
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 10:00:00",
        "course": {
            "title": "课程标题",
            "status": 1,
            "is_free": 0
        },
        "navigation": {
            "current_index": 1,
            "total_lessons": 10,
            "prev_lesson": null,
            "next_lesson": {
                "id": 2,
                "title": "下一课时标题"
            }
        }
    }
}
```

### 3. 课时VOD管理

**接口地址**: `/lesson-vod.php`

**功能描述**: 管理课时的腾讯云点播信息。

#### 获取VOD信息

**请求方式**: `GET`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| lesson_id | int | 是 | 课时ID |

#### 更新VOD信息

**请求方式**: `POST`

**请求参数**:

```json
{
    "lesson_id": 1,
    "vod_file_id": "123456789",
    "vod_video_url": "https://vod.example.com/video.mp4",
    "duration": 1800,
    "thumbnail": "https://example.com/thumb.jpg"
}
```

#### 切换视频类型

**请求方式**: `PUT`

**请求参数**:

```json
{
    "lesson_id": 1,
    "video_type": "vod"
}
```

#### 删除VOD信息

**请求方式**: `DELETE`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| lesson_id | int | 是 | 课时ID |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### JavaScript示例

```javascript
// 获取上传签名
async function getUploadSignature(lessonId) {
    try {
        const response = await fetch(`/api/vod-signature.php?lesson_id=${lessonId}`);
        const result = await response.json();
        
        if (result.success) {
            return result.data.signature;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('获取签名失败:', error);
        throw error;
    }
}

// 查询视频状态
async function getVideoStatus(fileId) {
    try {
        const response = await fetch(`/api/vod-status.php?file_id=${fileId}`);
        const result = await response.json();
        
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('查询视频状态失败:', error);
        throw error;
    }
}
```

### PHP示例

```php
// 获取课时列表
function getLessonList($courseId, $page = 1, $limit = 20) {
    $url = "http://your-domain.com/api/lessonList.php";
    $params = http_build_query([
        'course_id' => $courseId,
        'page' => $page,
        'limit' => $limit
    ]);
    
    $response = file_get_contents($url . '?' . $params);
    return json_decode($response, true);
}
```

## 注意事项

1. **权限验证**: 部分接口需要管理员权限，请确保已正确登录
2. **签名有效期**: 上传签名有效期为1小时，过期需重新获取
3. **文件大小限制**: 视频文件最大5GB，图片文件最大10MB
4. **并发限制**: 建议控制并发上传数量，避免超出腾讯云限制
5. **错误处理**: 请妥善处理API返回的错误信息
