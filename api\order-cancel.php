<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db.php';
require_once '../includes/auth.php';

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 验证用户身份
$user = authenticate_user();
if (!$user) {
    http_response_code(401);
    echo json_encode([
        'code' => 401,
        'message' => '未授权访问',
        'data' => null
    ]);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
if (!isset($input['order_id']) || !is_numeric($input['order_id'])) {
    echo json_encode([
        'code' => 400,
        'message' => '订单ID无效',
        'data' => null
    ]);
    exit;
}

try {
    $user_id = $user['id'];
    $order_id = intval($input['order_id']);
    
    // 验证订单
    $stmt = $conn->prepare("
        SELECT * FROM orders 
        WHERE id = ? AND user_id = ? AND order_status = 'pending' AND payment_status = 'unpaid'
    ");
    $stmt->bind_param("ii", $order_id, $user_id);
    $stmt->execute();
    $order = $stmt->get_result()->fetch_assoc();
    
    if (!$order) {
        echo json_encode([
            'code' => 404,
            'message' => '订单不存在或状态不允许取消',
            'data' => null
        ]);
        exit;
    }
    
    // 更新订单状态
    $stmt = $conn->prepare("
        UPDATE orders 
        SET order_status = 'cancelled', updated_at = NOW() 
        WHERE id = ?
    ");
    $stmt->bind_param("i", $order_id);
    $stmt->execute();
    
    if ($stmt->affected_rows > 0) {
        echo json_encode([
            'code' => 200,
            'message' => '订单已取消',
            'data' => [
                'order_id' => $order_id,
                'order_no' => $order['order_no']
            ]
        ]);
    } else {
        echo json_encode([
            'code' => 500,
            'message' => '取消订单失败',
            'data' => null
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage(),
        'data' => null
    ]);
}
?>
