<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成TabBar图标</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007bff;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-generator {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: #fafafa;
        }
        .icon-preview {
            width: 81px;
            height: 81px;
            margin: 0 auto 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }
        .icon-name {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .download-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 TabBar图标生成器</h1>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>点击下方的"生成图标"按钮</li>
                <li>自动下载生成的PNG图标文件</li>
                <li>将图标文件放置到 <code>static/tabbar/</code> 目录</li>
                <li>重新编译项目即可看到图标效果</li>
            </ol>
        </div>

        <div class="icon-generator">
            <!-- 首页图标 -->
            <div class="icon-item">
                <div class="icon-preview" id="home-preview"></div>
                <div class="icon-name">首页图标</div>
                <button class="download-btn" onclick="generateIcon('home', '#7A7E83')">生成普通</button>
                <button class="download-btn" onclick="generateIcon('home', '#007bff')">生成选中</button>
            </div>

            <!-- 课程图标 -->
            <div class="icon-item">
                <div class="icon-preview" id="course-preview"></div>
                <div class="icon-name">课程图标</div>
                <button class="download-btn" onclick="generateIcon('course', '#7A7E83')">生成普通</button>
                <button class="download-btn" onclick="generateIcon('course', '#007bff')">生成选中</button>
            </div>

            <!-- 个人图标 -->
            <div class="icon-item">
                <div class="icon-preview" id="profile-preview"></div>
                <div class="icon-name">个人图标</div>
                <button class="download-btn" onclick="generateIcon('profile', '#7A7E83')">生成普通</button>
                <button class="download-btn" onclick="generateIcon('profile', '#007bff')">生成选中</button>
            </div>
        </div>

        <button class="download-btn" style="width: 100%; padding: 15px; font-size: 16px; margin: 20px 0;" onclick="generateAllIcons()">
            🚀 一键生成所有图标
        </button>
    </div>

    <canvas id="canvas" width="81" height="81"></canvas>

    <script>
        // SVG图标定义
        const iconSVGs = {
            home: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                <polyline points="9,22 9,12 15,12 15,22"/>
            </svg>`,
            course: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polygon points="23 7 16 12 23 17 23 7"/>
                <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
            </svg>`,
            profile: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                <circle cx="12" cy="7" r="4"/>
            </svg>`
        };

        function generateIcon(iconType, color) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, 81, 81);
            
            // 创建SVG元素
            const svg = iconSVGs[iconType].replace('currentColor', color);
            const svgBlob = new Blob([svg], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(svgBlob);
            
            const img = new Image();
            img.onload = function() {
                // 在画布中心绘制图标
                const size = 40; // 图标大小
                const x = (81 - size) / 2;
                const y = (81 - size) / 2;
                
                ctx.drawImage(img, x, y, size, size);
                
                // 转换为PNG并下载
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    const suffix = color === '#007bff' ? '-active' : '';
                    link.download = `${iconType}${suffix}.png`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(link.href);
                }, 'image/png');
                
                URL.revokeObjectURL(url);
            };
            img.src = url;
        }

        function generateAllIcons() {
            const icons = ['home', 'course', 'profile'];
            const colors = [
                { color: '#7A7E83', suffix: '' },
                { color: '#007bff', suffix: '-active' }
            ];
            
            let index = 0;
            function generateNext() {
                if (index >= icons.length * colors.length) {
                    alert('所有图标生成完成！请检查下载文件夹。');
                    return;
                }
                
                const iconIndex = Math.floor(index / colors.length);
                const colorIndex = index % colors.length;
                
                generateIcon(icons[iconIndex], colors[colorIndex].color);
                index++;
                
                // 延迟生成下一个图标
                setTimeout(generateNext, 500);
            }
            
            generateNext();
        }

        // 页面加载时显示预览
        window.onload = function() {
            Object.keys(iconSVGs).forEach(iconType => {
                const preview = document.getElementById(`${iconType}-preview`);
                const svg = iconSVGs[iconType].replace('currentColor', '#7A7E83');
                preview.innerHTML = svg;
                preview.querySelector('svg').style.width = '40px';
                preview.querySelector('svg').style.height = '40px';
            });
        };
    </script>
</body>
</html>
