<template>
	<view class="course-introduction-container">
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<uni-load-more status="loading"></uni-load-more>
		</view>
		
		<!-- 课程介绍内容 -->
		<view class="introduction-content" v-else-if="courseData">
			<!-- 课程基本信息 -->
			<view class="course-basic-info">
				<image 
					class="course-cover" 
					:src="courseData.course_info.thumbnail || courseData.course_info.cover_image || '/static/default-course.png'" 
					mode="aspectFill"
				></image>
				<view class="course-header">
					<text class="course-title">{{ courseData.course_info.title }}</text>
					<text class="course-subtitle" v-if="courseData.course_info.subtitle">{{ courseData.course_info.subtitle }}</text>
					<view class="course-meta">
						<view class="meta-item">
							<uni-icons type="person" size="14" color="#666"></uni-icons>
							<text class="meta-text">{{ courseData.course_introduction.teacher_info.name }}</text>
						</view>
						<view class="meta-item">
							<uni-icons type="star" size="14" color="#ffa500"></uni-icons>
							<text class="meta-text">{{ courseData.course_info.rating || '5.0' }}</text>
						</view>
						<view class="meta-item">
							<uni-icons type="eye" size="14" color="#666"></uni-icons>
							<text class="meta-text">{{ courseData.course_info.view_count || 0 }}人学习</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 课程概述 -->
			<view class="section">
				<view class="section-title">
					<uni-icons type="info" size="18" color="#007bff"></uni-icons>
					<text class="title-text">课程概述</text>
				</view>
				<view class="section-content">
					<text class="overview-text">{{ courseData.course_introduction.overview || '暂无课程介绍' }}</text>
				</view>
			</view>

			<!-- 课程特色 -->
			<view class="section">
				<view class="section-title">
					<uni-icons type="checkmarkempty" size="18" color="#28a745"></uni-icons>
					<text class="title-text">课程特色</text>
				</view>
				<view class="section-content">
					<view class="feature-list">
						<view 
							class="feature-item" 
							v-for="(feature, index) in courseData.course_introduction.course_features" 
							:key="index"
						>
							<uni-icons type="checkmarkempty" size="14" color="#28a745"></uni-icons>
							<text class="feature-text">{{ feature }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 学习目标 -->
			<view class="section">
				<view class="section-title">
					<uni-icons type="flag" size="18" color="#ffc107"></uni-icons>
					<text class="title-text">学习目标</text>
				</view>
				<view class="section-content">
					<view class="objective-list">
						<view 
							class="objective-item" 
							v-for="(objective, index) in courseData.course_introduction.learning_objectives" 
							:key="index"
						>
							<view class="objective-number">{{ index + 1 }}</view>
							<text class="objective-text">{{ objective }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 讲师信息 -->
			<view class="section">
				<view class="section-title">
					<uni-icons type="person" size="18" color="#6f42c1"></uni-icons>
					<text class="title-text">讲师介绍</text>
				</view>
				<view class="section-content">
					<view class="teacher-info">
						<view class="teacher-avatar">
							<uni-icons type="person-filled" size="30" color="#6f42c1"></uni-icons>
						</view>
						<view class="teacher-details">
							<text class="teacher-name">{{ courseData.course_introduction.teacher_info.name }}</text>
							<text class="teacher-title">{{ courseData.course_introduction.teacher_info.title }}</text>
							<text class="teacher-desc">{{ courseData.course_introduction.teacher_info.description }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 课程大纲 -->
			<view class="section">
				<view class="section-title">
					<uni-icons type="list" size="18" color="#dc3545"></uni-icons>
					<text class="title-text">课程大纲</text>
				</view>
				<view class="section-content">
					<view class="outline-list">
						<view 
							class="outline-item" 
							v-for="(lesson, index) in courseData.course_introduction.course_outline" 
							:key="lesson.id"
						>
							<view class="lesson-number">{{ index + 1 }}</view>
							<view class="lesson-info">
								<text class="lesson-title">{{ lesson.title }}</text>
								<text class="lesson-desc" v-if="lesson.description">{{ lesson.description }}</text>
								<view class="lesson-meta">
									<text class="lesson-duration" v-if="lesson.duration_formatted">{{ lesson.duration_formatted }}</text>
									<uni-tag
										v-if="lesson.is_free"
										text="免费"
										type="success"
										size="mini"
									></uni-tag>
									<uni-tag
										v-else
										text="付费"
										type="warning"
										size="mini"
									></uni-tag>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部操作按钮 -->
			<view class="bottom-actions">
				<button class="action-btn secondary" @click="goBack">返回</button>
				<button class="action-btn primary" @click="startLearning">
					{{ courseData.has_access ? '开始学习' : (courseData.course_info.is_free ? '免费学习' : '立即购买') }}
				</button>
			</view>
		</view>

		<!-- 错误状态 -->
		<view class="error-state" v-else-if="error">
			<uni-icons type="info" size="40" color="#ff6b6b"></uni-icons>
			<text class="error-text">{{ error }}</text>
			<button class="retry-btn" @click="loadCourseIntroduction">重试</button>
		</view>
	</view>
</template>

<script>
	import { isLoggedIn, showSuccess, showError } from '../../utils/storage.js';
	import { authMixin } from '../../utils/auth.js';
	import { getCourseEnhancedDetail } from '../../api/course.js';
	
	export default {
		mixins: [authMixin],
		
		data() {
			return {
				loading: false,
				courseId: null,
				courseData: null,
				error: null
			};
		},
		
		onLoad(options) {
			if (options.id) {
				this.courseId = parseInt(options.id);
				this.loadCourseIntroduction();
			} else {
				this.error = '课程ID无效';
			}
		},
		
		methods: {
			/**
			 * 加载课程介绍信息
			 */
			async loadCourseIntroduction() {
				if (!this.courseId) return;
				
				this.loading = true;
				this.error = null;
				
				try {
					const response = await getCourseEnhancedDetail(this.courseId);
					
					if (response && response.code === 200) {
						this.courseData = response.data;
						
						// 设置页面标题
						uni.setNavigationBarTitle({
							title: this.courseData.course_info.title || '课程介绍'
						});
					} else {
						this.error = response?.message || '加载课程介绍失败';
					}
				} catch (error) {
					console.error('加载课程介绍失败:', error);
					this.error = '网络错误，请稍后重试';
				} finally {
					this.loading = false;
				}
			},
			
			/**
			 * 开始学习
			 */
			startLearning() {
				if (this.courseData.has_access || this.courseData.course_info.is_free) {
					// 跳转到课程详情页开始学习
					uni.navigateTo({
						url: `/pages/courses/detail?id=${this.courseId}`
					});
				} else {
					// 跳转到课程详情页进行购买
					uni.navigateTo({
						url: `/pages/courses/detail?id=${this.courseId}`
					});
				}
			},
			
			/**
			 * 返回上一页
			 */
			goBack() {
				uni.navigateBack();
			}
		}
	};
</script>

<style scoped>
.course-introduction-container {
	min-height: 100vh;
	background-color: #f8f9fa;
}

.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 200px;
}

.introduction-content {
	padding-bottom: 80px;
}

.course-basic-info {
	background: #fff;
	margin-bottom: 10px;
}

.course-cover {
	width: 100%;
	height: 200px;
}

.course-header {
	padding: 20px;
}

.course-title {
	font-size: 20px;
	font-weight: bold;
	color: #333;
	line-height: 1.4;
	margin-bottom: 8px;
	display: block;
}

.course-subtitle {
	font-size: 14px;
	color: #666;
	line-height: 1.4;
	margin-bottom: 12px;
	display: block;
}

.course-meta {
	display: flex;
	gap: 16px;
}

.meta-item {
	display: flex;
	align-items: center;
	gap: 4px;
}

.meta-text {
	font-size: 12px;
	color: #666;
}

.section {
	background: #fff;
	margin-bottom: 10px;
	padding: 20px;
}

.section-title {
	display: flex;
	align-items: center;
	gap: 8px;
	margin-bottom: 16px;
}

.title-text {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.section-content {
	line-height: 1.6;
}

.overview-text {
	font-size: 14px;
	color: #666;
	line-height: 1.6;
}

.feature-list {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.feature-item {
	display: flex;
	align-items: center;
	gap: 8px;
}

.feature-text {
	font-size: 14px;
	color: #666;
}

.objective-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.objective-item {
	display: flex;
	align-items: flex-start;
	gap: 12px;
}

.objective-number {
	width: 24px;
	height: 24px;
	background: #ffc107;
	color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	font-weight: bold;
	flex-shrink: 0;
}

.objective-text {
	font-size: 14px;
	color: #666;
	line-height: 1.5;
	flex: 1;
}

.teacher-info {
	display: flex;
	align-items: center;
	gap: 16px;
}

.teacher-avatar {
	width: 60px;
	height: 60px;
	background: #f8f9fa;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.teacher-details {
	flex: 1;
}

.teacher-name {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 4px;
}

.teacher-title {
	font-size: 12px;
	color: #6f42c1;
	display: block;
	margin-bottom: 8px;
}

.teacher-desc {
	font-size: 14px;
	color: #666;
	line-height: 1.4;
}

.outline-list {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.outline-item {
	display: flex;
	align-items: flex-start;
	gap: 12px;
	padding: 12px;
	background: #f8f9fa;
	border-radius: 8px;
}

.lesson-number {
	width: 24px;
	height: 24px;
	background: #dc3545;
	color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	font-weight: bold;
	flex-shrink: 0;
}

.lesson-info {
	flex: 1;
}

.lesson-title {
	font-size: 14px;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 4px;
}

.lesson-desc {
	font-size: 12px;
	color: #666;
	line-height: 1.4;
	display: block;
	margin-bottom: 8px;
}

.lesson-meta {
	display: flex;
	align-items: center;
	gap: 8px;
}

.lesson-duration {
	font-size: 12px;
	color: #999;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 12px 20px;
	border-top: 1px solid #eee;
	display: flex;
	gap: 12px;
	z-index: 100;
}

.action-btn {
	flex: 1;
	height: 44px;
	border-radius: 22px;
	border: none;
	font-size: 16px;
	font-weight: bold;
}

.action-btn.secondary {
	background: #f8f9fa;
	color: #666;
}

.action-btn.primary {
	background: #007bff;
	color: #fff;
}

.error-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 300px;
	gap: 16px;
}

.error-text {
	font-size: 14px;
	color: #666;
}

.retry-btn {
	padding: 8px 16px;
	background: #007bff;
	color: #fff;
	border: none;
	border-radius: 4px;
	font-size: 14px;
}
</style>