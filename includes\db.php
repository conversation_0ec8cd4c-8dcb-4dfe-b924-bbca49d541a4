<?php
$servername = "localhost";
$username = "qq";
$password = "123456";
$dbname = "qq";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
  die("Connection failed: " . $conn->connect_error);
}

// 设置字符集
$conn->set_charset("utf8mb4");

/**
 * 将相对路径转换为完整的网络地址
 * 用于解决微信小程序video组件poster属性只支持网络地址的问题
 * @param string $url 原始URL（可能是相对路径或完整网络地址）
 * @return string 完整的网络地址，如果原始URL为空则返回空字符串
 */
function convertToFullUrl($url) {
    if (empty($url)) {
        return '';
    }

    // 如果已经是完整的网络地址，直接返回
    if (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0) {
        return $url;
    }

    // 如果是相对路径，转换为完整的网络地址
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];

    // 确保URL以/开头
    if (strpos($url, '/') !== 0) {
        $url = '/' . $url;
    }

    return $protocol . '://' . $host . $url;
}

// 自动初始化支付相关表
if (file_exists(__DIR__ . '/init_payment_tables.php')) {
    require_once __DIR__ . '/init_payment_tables.php';
    init_payment_tables();
}
?>
