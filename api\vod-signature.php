<?php
/**
 * 腾讯云点播上传签名API
 * 为前端提供上传签名服务
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/vod_signature.php';
require_once '../includes/auth.php';

try {
    // 验证管理员权限（可选，根据需求调整）
    $auth_required = isset($_GET['auth']) && $_GET['auth'] === '1';
    if ($auth_required) {
        session_start();
        if (!isset($_SESSION['admin_id'])) {
            echo json_encode([
                'success' => false,
                'message' => '需要管理员权限'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }
    
    // 获取请求参数
    $lesson_id = isset($_REQUEST['lesson_id']) ? intval($_REQUEST['lesson_id']) : 0;
    $class_id = isset($_REQUEST['class_id']) ? intval($_REQUEST['class_id']) : 0;
    $procedure = isset($_REQUEST['procedure']) ? trim($_REQUEST['procedure']) : '';
    
    // 构建签名选项
    $options = [];
    
    if ($lesson_id > 0) {
        $options['lesson_id'] = $lesson_id;
    }
    
    if ($class_id > 0) {
        $options['class_id'] = $class_id;
    }
    
    if (!empty($procedure)) {
        $options['procedure'] = $procedure;
    }
    
    // 生成签名
    $result = VodSignature::generateClientUploadSignature($options);
    
    if ($result['success']) {
        // 添加上传配置信息
        $uploadConfig = VodSignature::getUploadConfig();
        $result['data']['config'] = $uploadConfig;
        
        // 记录签名生成日志（可选）
        if (function_exists('error_log')) {
            error_log("VOD签名生成成功 - Lesson ID: {$lesson_id}, Class ID: {$class_id}");
        }
    }
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '服务器错误',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
